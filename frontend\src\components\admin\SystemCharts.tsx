// Sistem İzleme Grafikleri
'use client';

import React, { useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SystemMetrics } from '@/services/systemService';
import { Activity, Cpu, HardDrive, Wifi } from 'lucide-react';

interface SystemChartsProps {
  metrics: SystemMetrics;
  className?: string;
}

export default function SystemCharts({ metrics, className = '' }: SystemChartsProps) {
  const cpuChartRef = useRef<HTMLCanvasElement>(null);
  const memoryChartRef = useRef<HTMLCanvasElement>(null);
  const diskChartRef = useRef<HTMLCanvasElement>(null);
  const networkChartRef = useRef<HTMLCanvasElement>(null);

  // CPU Usage Chart
  useEffect(() => {
    if (cpuChartRef.current) {
      const ctx = cpuChartRef.current.getContext('2d');
      if (ctx) {
        // Clear previous chart
        ctx.clearRect(0, 0, cpuChartRef.current.width, cpuChartRef.current.height);
        
        // Draw CPU usage as a simple progress circle
        drawProgressCircle(ctx, metrics.cpu.usage, 100, '#3B82F6');
      }
    }
  }, [metrics.cpu.usage]);

  // Memory Usage Chart
  useEffect(() => {
    if (memoryChartRef.current) {
      const ctx = memoryChartRef.current.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, memoryChartRef.current.width, memoryChartRef.current.height);
        drawProgressCircle(ctx, metrics.memory.percentage, 100, '#10B981');
      }
    }
  }, [metrics.memory.percentage]);

  // Disk Usage Chart
  useEffect(() => {
    if (diskChartRef.current) {
      const ctx = diskChartRef.current.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, diskChartRef.current.width, diskChartRef.current.height);
        drawProgressCircle(ctx, metrics.disk.percentage, 100, '#F59E0B');
      }
    }
  }, [metrics.disk.percentage]);

  // Network Chart (simple bars)
  useEffect(() => {
    if (networkChartRef.current) {
      const ctx = networkChartRef.current.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, networkChartRef.current.width, networkChartRef.current.height);
        drawNetworkBars(ctx, metrics.network.inbound, metrics.network.outbound);
      }
    }
  }, [metrics.network.inbound, metrics.network.outbound]);

  const drawProgressCircle = (ctx: CanvasRenderingContext2D, value: number, max: number, color: string) => {
    const canvas = ctx.canvas;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 10;
    const percentage = (value / max) * 100;
    const angle = (percentage / 100) * 2 * Math.PI;

    // Background circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#E5E7EB';
    ctx.lineWidth = 8;
    ctx.stroke();

    // Progress arc
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, -Math.PI / 2, -Math.PI / 2 + angle);
    ctx.strokeStyle = color;
    ctx.lineWidth = 8;
    ctx.lineCap = 'round';
    ctx.stroke();

    // Center text
    ctx.fillStyle = '#374151';
    ctx.font = 'bold 16px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(`${value.toFixed(1)}%`, centerX, centerY);
  };

  const drawNetworkBars = (ctx: CanvasRenderingContext2D, inbound: number, outbound: number) => {
    const canvas = ctx.canvas;
    const maxValue = Math.max(inbound, outbound, 100); // Minimum scale of 100
    const barWidth = 40;
    const barSpacing = 20;
    const maxBarHeight = canvas.height - 40;

    // Inbound bar
    const inboundHeight = (inbound / maxValue) * maxBarHeight;
    ctx.fillStyle = '#10B981';
    ctx.fillRect(canvas.width / 2 - barWidth - barSpacing / 2, canvas.height - 20 - inboundHeight, barWidth, inboundHeight);

    // Outbound bar
    const outboundHeight = (outbound / maxValue) * maxBarHeight;
    ctx.fillStyle = '#3B82F6';
    ctx.fillRect(canvas.width / 2 + barSpacing / 2, canvas.height - 20 - outboundHeight, barWidth, outboundHeight);

    // Labels
    ctx.fillStyle = '#6B7280';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'center';
    
    // Inbound label
    ctx.fillText('In', canvas.width / 2 - barWidth / 2 - barSpacing / 2, canvas.height - 5);
    ctx.fillText(`${inbound.toFixed(1)}`, canvas.width / 2 - barWidth / 2 - barSpacing / 2, canvas.height - inboundHeight - 25);
    
    // Outbound label
    ctx.fillText('Out', canvas.width / 2 + barWidth / 2 + barSpacing / 2, canvas.height - 5);
    ctx.fillText(`${outbound.toFixed(1)}`, canvas.width / 2 + barWidth / 2 + barSpacing / 2, canvas.height - outboundHeight - 25);
  };

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {/* CPU Chart */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Cpu className="w-4 h-4" />
            CPU Kullanımı
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center">
            <canvas
              ref={cpuChartRef}
              width={120}
              height={120}
              className="mb-2"
            />
            <div className="text-center">
              <p className="text-xs text-gray-500">{metrics.cpu.cores} çekirdek</p>
              <p className="text-xs text-gray-500">{metrics.cpu.temperature}°C</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Memory Chart */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Activity className="w-4 h-4" />
            Bellek Kullanımı
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center">
            <canvas
              ref={memoryChartRef}
              width={120}
              height={120}
              className="mb-2"
            />
            <div className="text-center">
              <p className="text-xs text-gray-500">
                {metrics.memory.used}GB / {metrics.memory.total}GB
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Disk Chart */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <HardDrive className="w-4 h-4" />
            Disk Kullanımı
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center">
            <canvas
              ref={diskChartRef}
              width={120}
              height={120}
              className="mb-2"
            />
            <div className="text-center">
              <p className="text-xs text-gray-500">
                {metrics.disk.used}GB / {metrics.disk.total}GB
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Network Chart */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Wifi className="w-4 h-4" />
            Ağ Trafiği
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center">
            <canvas
              ref={networkChartRef}
              width={120}
              height={120}
              className="mb-2"
            />
            <div className="text-center">
              <p className="text-xs text-gray-500">MB/s</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
