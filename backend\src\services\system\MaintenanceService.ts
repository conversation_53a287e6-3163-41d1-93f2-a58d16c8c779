// RFC-501: System Maintenance Service
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface MaintenanceTask {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime?: Date;
  endTime?: Date;
  result?: any;
  error?: string;
}

export interface MaintenanceStats {
  cacheSize: number;
  databaseSize: number;
  logSize: number;
  tempFileSize: number;
  lastBackup?: Date;
  lastOptimization?: Date;
  lastCleanup?: Date;
}

export class MaintenanceService {
  private prisma: PrismaClient;
  private redis: Redis;
  private runningTasks: Map<string, MaintenanceTask> = new Map();

  constructor() {
    this.prisma = new PrismaClient();
    this.redis = new Redis(
      parseInt(process.env.REDIS_PORT || '6379'),
      process.env.REDIS_HOST || 'localhost'
    );
  }

  /**
   * Clear Redis cache
   */
  async clearCache(): Promise<MaintenanceTask> {
    const task: MaintenanceTask = {
      id: this.generateTaskId(),
      name: 'Cache Temizleme',
      description: 'Redis cache tamamen temizleniyor',
      status: 'running',
      startTime: new Date()
    };

    this.runningTasks.set(task.id, task);

    try {
      // Get cache info before clearing
      const info = await this.redis.info('memory');
      const usedMemoryBefore = this.extractMemoryUsage(info);

      // Clear all cache
      await this.redis.flushall();

      // Get cache info after clearing
      const infoAfter = await this.redis.info('memory');
      const usedMemoryAfter = this.extractMemoryUsage(infoAfter);

      task.status = 'completed';
      task.endTime = new Date();
      task.result = {
        memoryFreed: usedMemoryBefore - usedMemoryAfter,
        memoryBefore: usedMemoryBefore,
        memoryAfter: usedMemoryAfter
      };

      // Log maintenance activity
      await this.logMaintenanceActivity(task);

    } catch (error) {
      task.status = 'failed';
      task.endTime = new Date();
      task.error = error instanceof Error ? error.message : 'Unknown error';
    }

    this.runningTasks.set(task.id, task);
    return task;
  }

  /**
   * Optimize database
   */
  async optimizeDatabase(): Promise<MaintenanceTask> {
    const task: MaintenanceTask = {
      id: this.generateTaskId(),
      name: 'Veritabanı Optimizasyonu',
      description: 'PostgreSQL veritabanı optimize ediliyor',
      status: 'running',
      startTime: new Date()
    };

    this.runningTasks.set(task.id, task);

    try {
      const results = [];

      // Vacuum analyze all tables
      const tables = await this.prisma.$queryRaw<Array<{ tablename: string }>>`
        SELECT tablename FROM pg_tables WHERE schemaname = 'public'
      `;

      for (const table of tables) {
        try {
          await this.prisma.$executeRawUnsafe(`VACUUM ANALYZE "${table.tablename}"`);
          results.push({ table: table.tablename, status: 'success' });
        } catch (error) {
          results.push({ 
            table: table.tablename, 
            status: 'error', 
            error: error instanceof Error ? error.message : 'Unknown error' 
          });
        }
      }

      // Reindex database
      try {
        await this.prisma.$executeRaw`REINDEX DATABASE CONCURRENTLY`;
        results.push({ operation: 'reindex', status: 'success' });
      } catch (error) {
        results.push({ 
          operation: 'reindex', 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }

      task.status = 'completed';
      task.endTime = new Date();
      task.result = {
        tablesProcessed: tables.length,
        results
      };

      await this.logMaintenanceActivity(task);

    } catch (error) {
      task.status = 'failed';
      task.endTime = new Date();
      task.error = error instanceof Error ? error.message : 'Unknown error';
    }

    this.runningTasks.set(task.id, task);
    return task;
  }

  /**
   * Clean temporary files
   */
  async cleanTempFiles(): Promise<MaintenanceTask> {
    const task: MaintenanceTask = {
      id: this.generateTaskId(),
      name: 'Geçici Dosya Temizliği',
      description: 'Geçici dosyalar ve eski loglar temizleniyor',
      status: 'running',
      startTime: new Date()
    };

    this.runningTasks.set(task.id, task);

    try {
      const results = [];
      let totalSizeFreed = 0;

      // Clean temp directory
      const tempDir = path.join(process.cwd(), 'temp');
      try {
        const tempFiles = await this.cleanDirectory(tempDir, 24); // 24 hours old
        results.push({ directory: 'temp', filesDeleted: tempFiles.count, sizeFreed: tempFiles.size });
        totalSizeFreed += tempFiles.size;
      } catch (error) {
        results.push({ directory: 'temp', error: error instanceof Error ? error.message : 'Unknown error' });
      }

      // Clean uploads directory (old files)
      const uploadsDir = path.join(process.cwd(), 'uploads');
      try {
        const uploadFiles = await this.cleanDirectory(uploadsDir, 30 * 24); // 30 days old
        results.push({ directory: 'uploads', filesDeleted: uploadFiles.count, sizeFreed: uploadFiles.size });
        totalSizeFreed += uploadFiles.size;
      } catch (error) {
        results.push({ directory: 'uploads', error: error instanceof Error ? error.message : 'Unknown error' });
      }

      // Clean old log files
      const logsDir = path.join(process.cwd(), 'logs');
      try {
        const logFiles = await this.cleanDirectory(logsDir, 7 * 24, '.log'); // 7 days old log files
        results.push({ directory: 'logs', filesDeleted: logFiles.count, sizeFreed: logFiles.size });
        totalSizeFreed += logFiles.size;
      } catch (error) {
        results.push({ directory: 'logs', error: error instanceof Error ? error.message : 'Unknown error' });
      }

      task.status = 'completed';
      task.endTime = new Date();
      task.result = {
        totalSizeFreed,
        results
      };

      await this.logMaintenanceActivity(task);

    } catch (error) {
      task.status = 'failed';
      task.endTime = new Date();
      task.error = error instanceof Error ? error.message : 'Unknown error';
    }

    this.runningTasks.set(task.id, task);
    return task;
  }

  /**
   * Create database backup
   */
  async createBackup(): Promise<MaintenanceTask> {
    const task: MaintenanceTask = {
      id: this.generateTaskId(),
      name: 'Veritabanı Yedekleme',
      description: 'PostgreSQL veritabanı yedekleniyor',
      status: 'running',
      startTime: new Date()
    };

    this.runningTasks.set(task.id, task);

    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupDir = path.join(process.cwd(), 'backups');
      const backupFile = path.join(backupDir, `backup-${timestamp}.sql`);

      // Ensure backup directory exists
      await fs.mkdir(backupDir, { recursive: true });

      // Create backup using pg_dump
      const dbUrl = process.env.DATABASE_URL;
      if (!dbUrl) {
        throw new Error('DATABASE_URL not configured');
      }

      const command = `pg_dump "${dbUrl}" > "${backupFile}"`;
      await execAsync(command);

      // Get backup file size
      const stats = await fs.stat(backupFile);

      task.status = 'completed';
      task.endTime = new Date();
      task.result = {
        backupFile,
        fileSize: stats.size,
        timestamp
      };

      await this.logMaintenanceActivity(task);

    } catch (error) {
      task.status = 'failed';
      task.endTime = new Date();
      task.error = error instanceof Error ? error.message : 'Unknown error';
    }

    this.runningTasks.set(task.id, task);
    return task;
  }

  /**
   * Get maintenance statistics
   */
  async getMaintenanceStats(): Promise<MaintenanceStats> {
    const stats: MaintenanceStats = {
      cacheSize: 0,
      databaseSize: 0,
      logSize: 0,
      tempFileSize: 0
    };

    try {
      // Get Redis memory usage
      const redisInfo = await this.redis.info('memory');
      stats.cacheSize = this.extractMemoryUsage(redisInfo);

      // Get database size
      const dbSize = await this.prisma.$queryRaw<Array<{ size: bigint }>>`
        SELECT pg_database_size(current_database()) as size
      `;
      stats.databaseSize = Number(dbSize[0]?.size || 0);

      // Get log directory size
      stats.logSize = await this.getDirectorySize(path.join(process.cwd(), 'logs'));

      // Get temp directory size
      stats.tempFileSize = await this.getDirectorySize(path.join(process.cwd(), 'temp'));

      // Get last maintenance dates from logs
      const lastActivities = await this.getLastMaintenanceActivities();
      stats.lastBackup = lastActivities.backup;
      stats.lastOptimization = lastActivities.optimization;
      stats.lastCleanup = lastActivities.cleanup;

    } catch (error) {
      console.error('Error getting maintenance stats:', error);
    }

    return stats;
  }

  /**
   * Get running maintenance tasks
   */
  getRunningTasks(): MaintenanceTask[] {
    return Array.from(this.runningTasks.values());
  }

  /**
   * Get maintenance task by ID
   */
  getTask(taskId: string): MaintenanceTask | undefined {
    return this.runningTasks.get(taskId);
  }

  /**
   * Private helper methods
   */
  private generateTaskId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private extractMemoryUsage(redisInfo: string): number {
    const match = redisInfo.match(/used_memory:(\d+)/);
    return match ? parseInt(match[1]) : 0;
  }

  private async cleanDirectory(dirPath: string, hoursOld: number, extension?: string): Promise<{ count: number; size: number }> {
    let count = 0;
    let size = 0;

    try {
      const files = await fs.readdir(dirPath);
      const cutoffTime = Date.now() - (hoursOld * 60 * 60 * 1000);

      for (const file of files) {
        if (extension && !file.endsWith(extension)) continue;

        const filePath = path.join(dirPath, file);
        const stats = await fs.stat(filePath);

        if (stats.mtime.getTime() < cutoffTime) {
          size += stats.size;
          await fs.unlink(filePath);
          count++;
        }
      }
    } catch (error) {
      // Directory might not exist
    }

    return { count, size };
  }

  private async getDirectorySize(dirPath: string): Promise<number> {
    let size = 0;

    try {
      const files = await fs.readdir(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = await fs.stat(filePath);
        
        if (stats.isDirectory()) {
          size += await this.getDirectorySize(filePath);
        } else {
          size += stats.size;
        }
      }
    } catch (error) {
      // Directory might not exist
    }

    return size;
  }

  private async logMaintenanceActivity(task: MaintenanceTask): Promise<void> {
    try {
      // Store in Redis for quick access
      await this.redis.lpush('maintenance_history', JSON.stringify({
        ...task,
        timestamp: new Date().toISOString()
      }));
      await this.redis.ltrim('maintenance_history', 0, 99); // Keep last 100 activities
    } catch (error) {
      console.error('Error logging maintenance activity:', error);
    }
  }

  private async getLastMaintenanceActivities(): Promise<{ backup?: Date; optimization?: Date; cleanup?: Date }> {
    try {
      const history = await this.redis.lrange('maintenance_history', 0, 50);
      const activities = history.map(h => JSON.parse(h));

      return {
        backup: activities.find(a => a.name.includes('Yedekleme'))?.endTime ? new Date(activities.find(a => a.name.includes('Yedekleme')).endTime) : undefined,
        optimization: activities.find(a => a.name.includes('Optimizasyon'))?.endTime ? new Date(activities.find(a => a.name.includes('Optimizasyon')).endTime) : undefined,
        cleanup: activities.find(a => a.name.includes('Temizlik'))?.endTime ? new Date(activities.find(a => a.name.includes('Temizlik')).endTime) : undefined
      };
    } catch (error) {
      return {};
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    await this.prisma.$disconnect();
    await this.redis.quit();
  }
}
