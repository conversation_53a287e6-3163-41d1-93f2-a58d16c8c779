import { io, Socket } from 'socket.io-client';
import toast from 'react-hot-toast';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  data?: any;
}

export interface NotificationPreferences {
  emailEnabled: boolean;
  browserEnabled: boolean;
  escrowNotifications: boolean;
  orderNotifications: boolean;
  quoteNotifications: boolean;
  systemNotifications: boolean;
  marketingEmails: boolean;
}

class WebSocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  // Event listeners
  private notificationListeners: ((notification: Notification) => void)[] = [];
  private unreadCountListeners: ((count: number) => void)[] = [];
  private connectionListeners: ((connected: boolean) => void)[] = [];

  /**
   * Initialize WebSocket connection
   */
  connect(token: string): void {
    if (this.socket?.connected) {
      return;
    }

    const serverUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';

    this.socket = io(serverUrl, {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    this.setupEventListeners();
  }

  /**
   * Setup socket event listeners
   */
  private setupEventListeners(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('✅ WebSocket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.notifyConnectionListeners(true);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket disconnected:', reason);
      this.isConnected = false;
      this.notifyConnectionListeners(false);
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.isConnected = false;
      this.notifyConnectionListeners(false);
      this.handleReconnect();
    });

    // Notification events
    this.socket.on('notification', (notification: Notification) => {
      console.log('📢 New notification:', notification);
      this.handleNewNotification(notification);
    });

    this.socket.on('unreadCount', (count: number) => {
      console.log('📊 Unread count:', count);
      this.notifyUnreadCountListeners(count);
    });

    // Preferences events
    this.socket.on('preferencesUpdated', (data: { success: boolean }) => {
      if (data.success) {
        toast.success('Bildirim tercihleri güncellendi');
      }
    });

    // Notifications list response
    this.socket.on('notifications', (data: any) => {
      console.log('📋 Notifications list:', data);
      // This will be handled by the component that requested it
    });
  }

  /**
   * Handle new notification
   */
  private handleNewNotification(notification: Notification): void {
    // Show toast notification
    this.showToastNotification(notification);
    
    // Notify listeners
    this.notifyNotificationListeners(notification);
  }

  /**
   * Show toast notification
   */
  private showToastNotification(notification: Notification): void {
    const toastOptions = {
      duration: notification.priority === 'urgent' ? 10000 : 5000,
      position: 'top-right' as const,
    };

    switch (notification.priority) {
      case 'urgent':
        toast.error(`${notification.title}\n${notification.message}`, toastOptions);
        break;
      case 'high':
        toast(`${notification.title}\n${notification.message}`, {
          ...toastOptions,
          icon: '🔔',
          style: {
            background: '#f59e0b',
            color: 'white',
          },
        });
        break;
      case 'medium':
        toast.success(`${notification.title}\n${notification.message}`, toastOptions);
        break;
      default:
        toast(`${notification.title}\n${notification.message}`, toastOptions);
    }
  }

  /**
   * Handle reconnection
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      toast.error('Bağlantı kurulamadı. Sayfayı yenileyin.');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    setTimeout(() => {
      console.log(`Reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
      this.socket?.connect();
    }, delay);
  }

  /**
   * Disconnect WebSocket
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.notifyConnectionListeners(false);
    }
  }

  /**
   * Mark notification as read
   */
  markAsRead(notificationId: string): void {
    this.socket?.emit('markAsRead', notificationId);
  }

  /**
   * Mark all notifications as read
   */
  markAllAsRead(): void {
    this.socket?.emit('markAllAsRead');
  }

  /**
   * Update notification preferences
   */
  updatePreferences(preferences: NotificationPreferences): void {
    this.socket?.emit('updatePreferences', preferences);
  }

  /**
   * Get notifications
   */
  getNotifications(page: number = 1, limit: number = 20): void {
    this.socket?.emit('getNotifications', { page, limit });
  }

  /**
   * Event listener management
   */
  onNotification(callback: (notification: Notification) => void): () => void {
    this.notificationListeners.push(callback);
    return () => {
      this.notificationListeners = this.notificationListeners.filter(cb => cb !== callback);
    };
  }

  onUnreadCount(callback: (count: number) => void): () => void {
    this.unreadCountListeners.push(callback);
    return () => {
      this.unreadCountListeners = this.unreadCountListeners.filter(cb => cb !== callback);
    };
  }

  onConnection(callback: (connected: boolean) => void): () => void {
    this.connectionListeners.push(callback);
    return () => {
      this.connectionListeners = this.connectionListeners.filter(cb => cb !== callback);
    };
  }

  /**
   * Notify listeners
   */
  private notifyNotificationListeners(notification: Notification): void {
    this.notificationListeners.forEach(callback => callback(notification));
  }

  private notifyUnreadCountListeners(count: number): void {
    this.unreadCountListeners.forEach(callback => callback(count));
  }

  private notifyConnectionListeners(connected: boolean): void {
    this.connectionListeners.forEach(callback => callback(connected));
  }

  /**
   * Get connection status
   */
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  /**
   * Get socket instance
   */
  getSocket(): Socket | null {
    return this.socket;
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
export default websocketService;
