#!/usr/bin/env node

/**
 * Production Database Setup Script
 * Sets up the production database with proper migrations and seed data
 */

const { execSync } = require('child_process');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

// Load production environment
require('dotenv').config({ path: '.env.production' });

console.log('🗄️ Starting Production Database Setup...\n');

// Validate environment variables
const requiredEnvVars = ['DATABASE_URL'];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  process.exit(1);
}

console.log('✅ Environment variables validated\n');

// Step 1: Test database connection
console.log('🔌 Testing database connection...');
const prisma = new PrismaClient();

async function testConnection() {
  try {
    await prisma.$connect();
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ Database connection successful\n');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Please check your DATABASE_URL and ensure PostgreSQL is running');
    process.exit(1);
  }
}

// Step 2: Run database migrations
async function runMigrations() {
  console.log('🔄 Running database migrations...');
  try {
    execSync('npx prisma migrate deploy', { 
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'production' }
    });
    console.log('✅ Database migrations completed\n');
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Step 3: Generate Prisma client
async function generateClient() {
  console.log('🔧 Generating Prisma client...');
  try {
    execSync('npx prisma generate', { stdio: 'inherit' });
    console.log('✅ Prisma client generated\n');
  } catch (error) {
    console.error('❌ Client generation failed:', error.message);
    process.exit(1);
  }
}

// Step 4: Seed production data
async function seedDatabase() {
  console.log('🌱 Seeding production database...');

  try {
    // Create admin user with secure password
    const bcrypt = require('bcrypt');
    const adminPassword = await bcrypt.hash('Admin123!@#', 12);

    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: adminPassword,
        firstName: 'System',
        lastName: 'Administrator',
        userType: 'ADMIN',
        status: 'ACTIVE',
        emailVerified: true,
        profile: {
          create: {
            phone: '+90 XXX XXX XX XX',
            address: 'İstanbul, Türkiye',
            city: 'İstanbul',
            country: 'Türkiye'
          }
        }
      }
    });

    console.log('✅ Admin user created/updated (Password: Admin123!@#)');

    // Create default categories
    const categories = [
      { name: 'Mermer', description: 'Doğal mermer ürünleri', slug: 'mermer' },
      { name: 'Granit', description: 'Doğal granit ürünleri', slug: 'granit' },
      { name: 'Travertin', description: 'Doğal travertin ürünleri', slug: 'travertin' },
      { name: 'Oniks', description: 'Doğal oniks ürünleri', slug: 'oniks' },
      { name: 'Kireçtaşı', description: 'Doğal kireçtaşı ürünleri', slug: 'kirec-tasi' },
      { name: 'Kuvarsit', description: 'Doğal kuvarsit ürünleri', slug: 'kuvarsit' }
    ];

    for (const category of categories) {
      await prisma.category.upsert({
        where: { slug: category.slug },
        update: {},
        create: category
      });
    }

    console.log('✅ Default categories created/updated');

    // Create default surface treatments
    const surfaceTreatments = [
      { name: 'Cilalı', description: 'Parlak yüzey işlemi' },
      { name: 'Mat', description: 'Mat yüzey işlemi' },
      { name: 'Antik', description: 'Antik yüzey işlemi' },
      { name: 'Fırçalı', description: 'Fırçalı yüzey işlemi' },
      { name: 'Alevli', description: 'Alevli yüzey işlemi' },
      { name: 'Sandblast', description: 'Kumlama yüzey işlemi' }
    ];

    for (const treatment of surfaceTreatments) {
      await prisma.surfaceTreatment.upsert({
        where: { name: treatment.name },
        update: {},
        create: treatment
      });
    }

    console.log('✅ Default surface treatments created/updated');

    // Create default packaging options
    const packagingOptions = [
      { name: 'Ahşap Kasa', description: 'Ahşap kasa ambalaj' },
      { name: 'Karton Kutu', description: 'Karton kutu ambalaj' },
      { name: 'Plastik Palet', description: 'Plastik palet ambalaj' },
      { name: 'Özel Ambalaj', description: 'Müşteri talebi özel ambalaj' }
    ];

    for (const packaging of packagingOptions) {
      await prisma.packagingOption.upsert({
        where: { name: packaging.name },
        update: {},
        create: packaging
      });
    }

    console.log('✅ Default packaging options created/updated');

    // Create system settings
    const systemSettings = [
      { key: 'site_name', value: 'Türkiye Doğal Taş Pazaryeri' },
      { key: 'site_description', value: 'Türkiye\'nin en büyük doğal taş pazaryeri' },
      { key: 'contact_email', value: '<EMAIL>' },
      { key: 'contact_phone', value: '+90 XXX XXX XX XX' },
      { key: 'whatsapp_number', value: '+90XXXXXXXXXX' },
      { key: 'commission_rate', value: '5.0' },
      { key: 'currency', value: 'TRY' },
      { key: 'timezone', value: 'Europe/Istanbul' },
      { key: 'maintenance_mode', value: 'false' }
    ];

    for (const setting of systemSettings) {
      await prisma.systemSetting.upsert({
        where: { key: setting.key },
        update: { value: setting.value },
        create: setting
      });
    }

    console.log('✅ System settings created/updated');

    console.log('✅ Database seeding completed\n');
  } catch (error) {
    console.error('❌ Database seeding failed:', error.message);
    throw error;
  }
}

// Step 5: Create database backup
async function createBackup() {
  console.log('💾 Creating initial database backup...');
  
  const backupDir = path.join(process.cwd(), 'backups');
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFile = path.join(backupDir, `production-initial-${timestamp}.sql`);

  try {
    const dbUrl = new URL(process.env.DATABASE_URL);
    const pgDumpCommand = `pg_dump -h ${dbUrl.hostname} -p ${dbUrl.port || 5432} -U ${dbUrl.username} -d ${dbUrl.pathname.slice(1)} > "${backupFile}"`;
    
    execSync(pgDumpCommand, { 
      stdio: 'inherit',
      env: { ...process.env, PGPASSWORD: dbUrl.password }
    });
    
    console.log(`✅ Database backup created: ${backupFile}\n`);
  } catch (error) {
    console.warn('⚠️ Backup creation failed (pg_dump not available):', error.message);
    console.warn('Please ensure PostgreSQL client tools are installed\n');
  }
}

// Main execution
async function main() {
  try {
    await testConnection();
    await runMigrations();
    await generateClient();
    await seedDatabase();
    await createBackup();

    console.log('🎉 Production database setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Update admin password in the database');
    console.log('2. Configure system settings as needed');
    console.log('3. Set up regular backup schedule');
    console.log('4. Monitor database performance');

  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
