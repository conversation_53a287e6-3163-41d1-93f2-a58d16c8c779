'use client'

import * as React from 'react'
import { useStock } from '@/contexts/stock-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Package,
  Search,
  Filter,
  ShoppingCart,
  Heart,
  Eye
} from 'lucide-react'

export default function CustomerStockPage() {
  const { getApprovedStockItems } = useStock()
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedCategory, setSelectedCategory] = React.useState('all')
  const [selectedProducer, setSelectedProducer] = React.useState('all')

  const stockItems = getApprovedStockItems()

  // Get unique categories and producers for filters
  const categories = Array.from(new Set(stockItems.map(item => item.productName)))
  const producers = Array.from(new Set(stockItems.map(item => item.producerName)))

  const filteredStockItems = stockItems.filter(item => {
    const matchesSearch = item.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.producerName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || item.productName === selectedCategory
    const matchesProducer = selectedProducer === 'all' || item.producerName === selectedProducer

    return matchesSearch && matchesCategory && matchesProducer
  })

  const handleRequestQuote = (stockItem: any) => {
    // This would typically open a quote request modal
    console.log('Request quote for stock item:', stockItem)
    alert(`${stockItem.productName} için teklif talebi gönderildi!`)
  }

  const handleAddToFavorites = (stockItem: any) => {
    // This would typically add to favorites
    console.log('Add to favorites:', stockItem)
    alert(`${stockItem.productName} favorilere eklendi!`)
  }

  const handleViewDetails = (stockItem: any) => {
    // This would typically open a detailed view
    console.log('View details:', stockItem)
    alert(`${stockItem.productName} detayları görüntüleniyor...`)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Stok Ürünler</h1>
          <p className="text-gray-600">
            Mevcut stok ürünleri görüntüleyin ve teklif isteyin
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {stockItems.length}
            </div>
            <div className="text-sm text-gray-600">Toplam Stok</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {categories.length}
            </div>
            <div className="text-sm text-gray-600">Ürün Çeşidi</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {producers.length}
            </div>
            <div className="text-sm text-gray-600">Üretici</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-amber-600">
              {stockItems.reduce((total, item) => total + item.metraj, 0).toFixed(1)}
            </div>
            <div className="text-sm text-gray-600">Toplam m²</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filtreler
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Arama
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Ürün veya üretici ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ürün
              </label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Ürün seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Ürünler</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Üretici
              </label>
              <Select value={selectedProducer} onValueChange={setSelectedProducer}>
                <SelectTrigger>
                  <SelectValue placeholder="Üretici seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Üreticiler</SelectItem>
                  {producers.map((producer) => (
                    <SelectItem key={producer} value={producer}>
                      {producer}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stock Items Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredStockItems.map((stockItem) => (
          <Card key={stockItem.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="aspect-video bg-gray-200 relative">
              <img
                src={stockItem.image?.url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'}
                alt={stockItem.productName}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'
                }}
              />
              <div className="absolute top-2 right-2">
                <div className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                  Stokta
                </div>
              </div>
            </div>

            <CardContent className="p-4">
              <div className="space-y-2">
                <h3 className="font-semibold text-lg text-gray-900">{stockItem.productName}</h3>
                <p className="text-sm text-gray-600">{stockItem.producerName}</p>

                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-gray-500">Metraj:</span>
                    <span className="font-medium ml-1">{stockItem.metraj} m²</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Kalınlık:</span>
                    <span className="font-medium ml-1">{stockItem.thickness} cm</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Ebat:</span>
                    <span className="font-medium ml-1">{stockItem.width}x{stockItem.length} cm</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Fiyat:</span>
                    <span className="font-medium ml-1">{stockItem.price} {stockItem.currency}</span>
                  </div>
                </div>

                <div className="flex gap-2 mt-4">
                  <Button
                    size="sm"
                    className="flex-1 bg-stone-600 hover:bg-stone-700"
                    onClick={() => handleRequestQuote(stockItem)}
                  >
                    <ShoppingCart className="w-4 h-4 mr-1" />
                    Teklif İste
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddToFavorites(stockItem)}
                  >
                    <Heart className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetails(stockItem)}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredStockItems.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Stok ürün bulunamadı</h3>
            <p className="text-gray-600">
              Arama kriterlerinize uygun stok ürün bulunmamaktadır.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
