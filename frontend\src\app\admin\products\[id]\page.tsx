'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useProducts } from '@/contexts/products-context'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft,
  Building2,
  MapPin,
  Package,
  Users,
  TrendingUp,
  DollarSign,
  Clock,
  Star,
  Phone,
  Mail,
  Globe,
  Warehouse,
  AlertTriangle,
  CheckCircle,
  Eye,
  BarChart3
} from 'lucide-react'

interface ProductDetailPageProps {
  params: {
    id: string
  }
}

export default function AdminProductDetailPage({ params }: ProductDetailPageProps) {
  const router = useRouter()
  const { products } = useProducts()
  const [selectedTab, setSelectedTab] = React.useState('overview')

  const product = products.find(p => p.id === params.id)

  if (!product) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2"><PERSON>r<PERSON><PERSON> bulunamadı</h3>
          <p className="text-gray-600 mb-4">Aradığınız ürün mevcut değil.</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Geri Dön
          </Button>
        </div>
      </div>
    )
  }

  // Mock data for producers - gerçek uygulamada API'den gelecek
  const mockProducers = [
    {
      id: '1',
      name: 'Afyon Doğal Taş A.Ş.',
      location: 'Afyon',
      rating: 4.8,
      totalOrders: 156,
      onTimeDelivery: 95,
      responseTime: '2 saat',
      stock: 250,
      unit: 'm²',
      lastUpdate: '2 saat önce',
      priceRange: { min: 115, max: 125, currency: 'USD' },
      contact: { phone: '+90 272 123 45 67', email: '<EMAIL>' },
      status: 'active'
    },
    {
      id: '2', 
      name: 'Premium Stone Co.',
      location: 'İstanbul',
      rating: 4.6,
      totalOrders: 89,
      onTimeDelivery: 88,
      responseTime: '4 saat',
      stock: 180,
      unit: 'm²',
      lastUpdate: '1 gün önce',
      priceRange: { min: 120, max: 130, currency: 'USD' },
      contact: { phone: '+90 212 987 65 43', email: '<EMAIL>' },
      status: 'active'
    },
    {
      id: '3',
      name: 'Marmara Mermer Ltd.',
      location: 'Bursa',
      rating: 4.2,
      totalOrders: 67,
      onTimeDelivery: 82,
      responseTime: '6 saat',
      stock: 45,
      unit: 'm²',
      lastUpdate: '3 gün önce',
      priceRange: { min: 110, max: 118, currency: 'USD' },
      contact: { phone: '+90 224 555 12 34', email: '<EMAIL>' },
      status: 'low_stock'
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="secondary" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Aktif</Badge>
      case 'low_stock':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><AlertTriangle className="w-3 h-3 mr-1" />Düşük Stok</Badge>
      case 'out_of_stock':
        return <Badge variant="secondary" className="bg-red-100 text-red-800"><AlertTriangle className="w-3 h-3 mr-1" />Stok Yok</Badge>
      default:
        return null
    }
  }

  const getStockStatus = (stock: number) => {
    if (stock > 100) return { color: 'text-green-600', bg: 'bg-green-100', label: 'Yeterli' }
    if (stock > 50) return { color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'Orta' }
    return { color: 'text-red-600', bg: 'bg-red-100', label: 'Düşük' }
  }

  const totalStock = mockProducers.reduce((sum, producer) => sum + producer.stock, 0)
  const averagePrice = mockProducers.reduce((sum, producer) => sum + (producer.priceRange.min + producer.priceRange.max) / 2, 0) / mockProducers.length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Geri Dön
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
            <p className="text-gray-600 mt-1">Ürün Detayları ve Üretici Yönetimi</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant="outline">{product.category}</Badge>
          <Button variant="outline">
            <BarChart3 className="w-4 h-4 mr-2" />
            İstatistikler
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Üretici</p>
              <p className="text-xl font-bold text-gray-900">{mockProducers.length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Warehouse className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Stok</p>
              <p className="text-xl font-bold text-gray-900">{totalStock} m²</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-amber-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Ortalama Fiyat</p>
              <p className="text-xl font-bold text-gray-900">${averagePrice.toFixed(0)}/m²</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <TrendingUp className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Ortalama Rating</p>
              <p className="text-xl font-bold text-gray-900">
                {(mockProducers.reduce((sum, p) => sum + p.rating, 0) / mockProducers.length).toFixed(1)}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Genel Bakış', icon: Eye },
            { id: 'producers', label: 'Üreticiler', icon: Building2 },
            { id: 'quarry', label: 'Ocak Bilgileri', icon: MapPin },
            { id: 'pricing', label: 'Fiyat Analizi', icon: DollarSign }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === tab.id
                    ? 'border-amber-500 text-amber-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {selectedTab === 'overview' && (
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            {/* Product Image */}
            <div className="xl:col-span-1">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Ürün Görseli</h3>
                <img
                  src={product.image || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'}
                  alt={product.name}
                  className="w-full h-64 object-cover rounded-lg"
                />
              </Card>
            </div>

            {/* Product Info */}
            <div className="xl:col-span-2 space-y-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Ürün Bilgileri</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Kategori</label>
                    <p className="text-gray-900">{product.category}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Durum</label>
                    <Badge variant="outline">{product.status}</Badge>
                  </div>
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-gray-600">Açıklama</label>
                    <p className="text-gray-900">{product.description}</p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}

        {selectedTab === 'producers' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Bu Ürünü Üreten Firmalar ({mockProducers.length})</h3>
              <Button variant="outline" size="sm">
                <TrendingUp className="w-4 h-4 mr-2" />
                Performans Karşılaştır
              </Button>
            </div>

            <div className="grid grid-cols-1 gap-4">
              {mockProducers.map((producer) => {
                const stockStatus = getStockStatus(producer.stock)
                return (
                  <Card key={producer.id} className="p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                      {/* Producer Info */}
                      <div className="lg:col-span-1">
                        <div className="flex items-start gap-3">
                          <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                            <Building2 className="w-6 h-6 text-gray-600" />
                          </div>
                          <div className="flex-1">
                            <button
                              onClick={() => router.push(`/admin/producers/${producer.id}`)}
                              className="text-lg font-semibold text-blue-600 hover:underline text-left"
                            >
                              {producer.name}
                            </button>
                            <p className="text-sm text-gray-600 flex items-center gap-1">
                              <MapPin className="w-3 h-3" />
                              {producer.location}
                            </p>
                            <div className="flex items-center gap-1 mt-1">
                              <Star className="w-4 h-4 text-yellow-500 fill-current" />
                              <span className="text-sm font-medium">{producer.rating}</span>
                              <span className="text-sm text-gray-500">({producer.totalOrders} sipariş)</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Stock Info */}
                      <div className="lg:col-span-1">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Stok Durumu</span>
                            {getStatusBadge(producer.status)}
                          </div>
                          <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${stockStatus.bg}`}></div>
                            <span className="font-semibold">{producer.stock} {producer.unit}</span>
                            <span className={`text-sm ${stockStatus.color}`}>({stockStatus.label})</span>
                          </div>
                          <p className="text-xs text-gray-500">
                            Son güncelleme: {producer.lastUpdate}
                          </p>
                        </div>
                      </div>

                      {/* Performance */}
                      <div className="lg:col-span-1">
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Zamanında Teslimat</span>
                            <span className="font-medium">{producer.onTimeDelivery}%</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Yanıt Süresi</span>
                            <span className="font-medium">{producer.responseTime}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Fiyat Aralığı</span>
                            <span className="font-medium">
                              ${producer.priceRange.min}-{producer.priceRange.max}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="lg:col-span-1">
                        <div className="space-y-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() => router.push(`/admin/producers/${producer.id}`)}
                          >
                            <Eye className="w-4 h-4 mr-2" />
                            Profili Görüntüle
                          </Button>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm" className="flex-1">
                              <Phone className="w-4 h-4" />
                            </Button>
                            <Button variant="outline" size="sm" className="flex-1">
                              <Mail className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                )
              })}
            </div>
          </div>
        )}

        {selectedTab === 'quarry' && product.quarry && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Ocak Bilgileri</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Ocak Adı</label>
                  <p className="text-gray-900 font-medium">{product.quarry.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Ocak Sahibi</label>
                  <p className="text-gray-900">{product.quarry.owner || 'Belirtilmemiş'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Konum</label>
                  <p className="text-gray-900">{product.quarry.location?.city || product.quarry.location}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Adres</label>
                  <p className="text-gray-900">{product.quarry.address || product.quarry.location?.address}</p>
                </div>
              </div>
              
              {product.quarry.stones && (
                <div>
                  <label className="text-sm font-medium text-gray-600 mb-3 block">Bu Ocaktan Çıkan Taşlar</label>
                  <div className="grid grid-cols-2 gap-3">
                    {product.quarry.stones.map((stone: any, index: number) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-3">
                        <img
                          src={stone.image || product.image}
                          alt={stone.name}
                          className="w-full h-16 object-cover rounded mb-2"
                        />
                        <p className="text-sm font-medium text-gray-900">{stone.name}</p>
                        <p className="text-xs text-gray-600">{stone.category}</p>
                        {stone.producerCount && (
                          <p className="text-xs text-blue-600 mt-1">{stone.producerCount} üretici</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </Card>
        )}

        {selectedTab === 'pricing' && (
          <div className="space-y-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Fiyat Karşılaştırması</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4">Üretici</th>
                      <th className="text-left py-3 px-4">Min Fiyat</th>
                      <th className="text-left py-3 px-4">Max Fiyat</th>
                      <th className="text-left py-3 px-4">Ortalama</th>
                      <th className="text-left py-3 px-4">Stok</th>
                      <th className="text-left py-3 px-4">Durum</th>
                    </tr>
                  </thead>
                  <tbody>
                    {mockProducers.map((producer) => (
                      <tr key={producer.id} className="border-b">
                        <td className="py-3 px-4">
                          <button
                            onClick={() => router.push(`/admin/producers/${producer.id}`)}
                            className="text-blue-600 hover:underline font-medium"
                          >
                            {producer.name}
                          </button>
                        </td>
                        <td className="py-3 px-4">${producer.priceRange.min}</td>
                        <td className="py-3 px-4">${producer.priceRange.max}</td>
                        <td className="py-3 px-4">
                          ${((producer.priceRange.min + producer.priceRange.max) / 2).toFixed(0)}
                        </td>
                        <td className="py-3 px-4">{producer.stock} m²</td>
                        <td className="py-3 px-4">{getStatusBadge(producer.status)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
