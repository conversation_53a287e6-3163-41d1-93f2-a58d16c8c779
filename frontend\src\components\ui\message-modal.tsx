"use client"

import * as React from "react"
import { But<PERSON> } from "./button"
import { Textarea } from "./textarea"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./card"
import { MessageSquare, Send, X } from "lucide-react"

interface MessageModalProps {
  isOpen: boolean
  onClose: () => void
  request: any
}

export function MessageModal({ isOpen, onClose, request }: MessageModalProps) {
  const [message, setMessage] = React.useState('')
  const [isLoading, setIsLoading] = React.useState(false)

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!message.trim()) {
      alert('Lütfen mesaj içeriğini girin')
      return
    }

    setIsLoading(true)
    
    try {
      // Here you would send the message to your API
      console.log('Sending message to customer:', {
        requestId: request.id,
        customerEmail: request.customerEmail,
        message: message.trim()
      })
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      alert('<PERSON><PERSON> ba<PERSON><PERSON>!')
      setMessage('')
      onClose()
      
    } catch (error) {
      console.error('Error sending message:', error)
      alert('Mesaj gönderilirken bir hata oluştu. Lütfen tekrar deneyin.')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen || !request) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <MessageSquare className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold text-gray-900">Müşteriye Mesaj Gönder</h2>
              <p className="text-gray-600">
                {request.customerName} - Talep #{request.id}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Request Summary */}
          <Card className="mb-6 bg-gray-50">
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Talep Özeti</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Ürün:</span>
                  <span className="ml-2 font-medium">{request.productName}</span>
                </div>
                <div>
                  <span className="text-gray-500">Miktar:</span>
                  <span className="ml-2 font-medium">{request.quantity} {request.unit}</span>
                </div>
                <div>
                  <span className="text-gray-500">Son Tarih:</span>
                  <span className="ml-2 font-medium">{request.deadline}</span>
                </div>
                <div>
                  <span className="text-gray-500">Hedef Fiyat:</span>
                  <span className="ml-2 font-medium">${request.targetPrice} / {request.unit}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Message Form */}
          <form onSubmit={handleSendMessage} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mesaj İçeriği *
              </label>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="w-full"
                rows={8}
                placeholder="Müşteriye göndermek istediğiniz mesajı buraya yazın..."
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Bu mesaj müşterinin e-posta adresine gönderilecektir.
              </p>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            Alıcı: <span className="font-medium">{request.customerEmail}</span>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              İptal
            </Button>
            <Button 
              onClick={handleSendMessage}
              disabled={!message.trim() || isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Gönderiliyor...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Mesaj Gönder
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
