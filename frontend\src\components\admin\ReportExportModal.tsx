'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Download, 
  FileText, 
  Mail, 
  Share, 
  Calendar,
  Clock,
  Users,
  Settings,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface ReportExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  reportType: string;
  reportData?: any;
}

export default function ReportExportModal({ 
  isOpen, 
  onClose, 
  reportType, 
  reportData 
}: ReportExportModalProps) {
  const [exportFormat, setExportFormat] = useState('pdf');
  const [includeCharts, setIncludeCharts] = useState(true);
  const [includeRawData, setIncludeRawData] = useState(false);
  const [emailRecipients, setEmailRecipients] = useState('');
  const [emailSubject, setEmailSubject] = useState('');
  const [emailMessage, setEmailMessage] = useState('');
  const [scheduleReport, setScheduleReport] = useState(false);
  const [scheduleFrequency, setScheduleFrequency] = useState('weekly');
  const [isExporting, setIsExporting] = useState(false);

  const exportFormats = [
    { value: 'pdf', label: 'PDF Raporu', icon: FileText, description: 'Profesyonel rapor formatı' },
    { value: 'excel', label: 'Excel Dosyası', icon: FileText, description: 'Detaylı veri analizi için' },
    { value: 'csv', label: 'CSV Dosyası', icon: FileText, description: 'Ham veri export' },
    { value: 'powerpoint', label: 'PowerPoint', icon: FileText, description: 'Sunum formatı' }
  ];

  const scheduleOptions = [
    { value: 'daily', label: 'Günlük' },
    { value: 'weekly', label: 'Haftalık' },
    { value: 'monthly', label: 'Aylık' },
    { value: 'quarterly', label: 'Çeyreklik' }
  ];

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Create download link
      const blob = new Blob(['Sample report data'], { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${reportType}-report-${new Date().toISOString().split('T')[0]}.${exportFormat}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleEmailSend = async () => {
    if (!emailRecipients.trim()) return;
    
    setIsExporting(true);
    
    try {
      // Simulate email sending
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Here you would integrate with your email service
      console.log('Sending email to:', emailRecipients);
      console.log('Subject:', emailSubject);
      console.log('Message:', emailMessage);
      
      onClose();
    } catch (error) {
      console.error('Email sending failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleScheduleReport = async () => {
    setIsExporting(true);
    
    try {
      // Simulate scheduling
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      console.log('Scheduling report:', {
        type: reportType,
        frequency: scheduleFrequency,
        format: exportFormat,
        recipients: emailRecipients
      });
      
      onClose();
    } catch (error) {
      console.error('Scheduling failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Rapor Export ve Paylaşım
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Export Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Export Seçenekleri
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Format Selection */}
              <div className="space-y-3">
                <Label>Export Formatı</Label>
                <div className="grid grid-cols-1 gap-2">
                  {exportFormats.map((format) => {
                    const IconComponent = format.icon;
                    return (
                      <div
                        key={format.value}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          exportFormat === format.value
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setExportFormat(format.value)}
                      >
                        <div className="flex items-center gap-3">
                          <IconComponent className="w-5 h-5" />
                          <div>
                            <p className="font-medium">{format.label}</p>
                            <p className="text-sm text-gray-600">{format.description}</p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Content Options */}
              <div className="space-y-3">
                <Label>İçerik Seçenekleri</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeCharts"
                      checked={includeCharts}
                      onCheckedChange={setIncludeCharts}
                    />
                    <Label htmlFor="includeCharts" className="text-sm">
                      Grafikleri dahil et
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeRawData"
                      checked={includeRawData}
                      onCheckedChange={setIncludeRawData}
                    />
                    <Label htmlFor="includeRawData" className="text-sm">
                      Ham verileri dahil et
                    </Label>
                  </div>
                </div>
              </div>

              {/* Export Button */}
              <Button 
                onClick={handleExport} 
                disabled={isExporting}
                className="w-full"
              >
                {isExporting ? (
                  <>
                    <Clock className="w-4 h-4 mr-2 animate-spin" />
                    Export Ediliyor...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-2" />
                    Raporu İndir
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Email & Sharing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="w-5 h-5" />
                Email ve Paylaşım
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Email Recipients */}
              <div className="space-y-2">
                <Label htmlFor="emailRecipients">Alıcılar</Label>
                <Input
                  id="emailRecipients"
                  placeholder="<EMAIL>, <EMAIL>"
                  value={emailRecipients}
                  onChange={(e) => setEmailRecipients(e.target.value)}
                />
              </div>

              {/* Email Subject */}
              <div className="space-y-2">
                <Label htmlFor="emailSubject">Konu</Label>
                <Input
                  id="emailSubject"
                  placeholder="Rapor konusu"
                  value={emailSubject}
                  onChange={(e) => setEmailSubject(e.target.value)}
                />
              </div>

              {/* Email Message */}
              <div className="space-y-2">
                <Label htmlFor="emailMessage">Mesaj</Label>
                <Textarea
                  id="emailMessage"
                  placeholder="Rapor ile ilgili açıklama..."
                  value={emailMessage}
                  onChange={(e) => setEmailMessage(e.target.value)}
                  rows={3}
                />
              </div>

              {/* Send Email Button */}
              <Button 
                onClick={handleEmailSend} 
                disabled={isExporting || !emailRecipients.trim()}
                variant="outline"
                className="w-full"
              >
                {isExporting ? (
                  <>
                    <Clock className="w-4 h-4 mr-2 animate-spin" />
                    Gönderiliyor...
                  </>
                ) : (
                  <>
                    <Mail className="w-4 h-4 mr-2" />
                    Email Gönder
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Scheduled Reports */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Otomatik Raporlama
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="scheduleReport"
                  checked={scheduleReport}
                  onCheckedChange={setScheduleReport}
                />
                <Label htmlFor="scheduleReport">
                  Bu raporu otomatik olarak oluştur ve gönder
                </Label>
              </div>

              {scheduleReport && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div className="space-y-2">
                    <Label>Sıklık</Label>
                    <Select value={scheduleFrequency} onValueChange={setScheduleFrequency}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {scheduleOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>&nbsp;</Label>
                    <Button 
                      onClick={handleScheduleReport}
                      disabled={isExporting}
                      className="w-full"
                    >
                      {isExporting ? (
                        <>
                          <Clock className="w-4 h-4 mr-2 animate-spin" />
                          Zamanlanıyor...
                        </>
                      ) : (
                        <>
                          <Calendar className="w-4 h-4 mr-2" />
                          Zamanla
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
