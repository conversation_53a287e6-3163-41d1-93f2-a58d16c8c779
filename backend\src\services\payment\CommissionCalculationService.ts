// PRD: Platform Commission Calculation Service
import { createClient, RedisClientType } from 'redis';
import { redisManager } from '../redis/RedisManager';

export interface OrderItem {
  id: string;
  productId: string;
  quantity: number;
  unit: 'm2' | 'ton' | 'piece';
  pricePerUnit: number;
  totalPrice: number;
}

export interface OrderData {
  items: OrderItem[];
  unit: 'm2' | 'ton' | 'piece';
  totalQuantity: number;
}

export interface CommissionBreakdown {
  m2Commission: number;
  tonCommission: number;
  totalCommission: number;
  commissionRate: {
    m2Rate: number; // $1 per m2
    tonRate: number; // $10 per ton
  };
  breakdown: {
    m2Quantity: number;
    tonQuantity: number;
    m2Amount: number;
    tonAmount: number;
  };
}

export class CommissionCalculationService {
  private redis: RedisClientType;
  
  // PRD Commission Rates
  private readonly M2_COMMISSION_RATE = 1; // $1 per m2
  private readonly TON_COMMISSION_RATE = 10; // $10 per ton

  constructor() {
    this.redis = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    this.redis.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });
  }

  async connect(): Promise<void> {
    if (!this.redis.isOpen) {
      await this.redis.connect();
    }
  }

  async disconnect(): Promise<void> {
    if (this.redis.isOpen) {
      await this.redis.disconnect();
    }
  }

  // Calculate platform commission based on PRD requirements
  calculateCommission(orderData: OrderData): CommissionBreakdown {
    let m2Quantity = 0;
    let tonQuantity = 0;
    let m2Commission = 0;
    let tonCommission = 0;

    for (const item of orderData.items) {
      if (item.unit === 'm2') {
        m2Quantity += item.quantity;
        m2Commission += item.quantity * this.M2_COMMISSION_RATE;
      } else if (item.unit === 'ton') {
        tonQuantity += item.quantity;
        tonCommission += item.quantity * this.TON_COMMISSION_RATE;
      }
      // No commission for 'piece' items as per PRD
    }

    const totalCommission = m2Commission + tonCommission;

    return {
      m2Commission,
      tonCommission,
      totalCommission,
      commissionRate: {
        m2Rate: this.M2_COMMISSION_RATE,
        tonRate: this.TON_COMMISSION_RATE
      },
      breakdown: {
        m2Quantity,
        tonQuantity,
        m2Amount: m2Commission,
        tonAmount: tonCommission
      }
    };
  }

  // Update order with commission calculation
  async updateOrderWithCommission(orderId: string, orderData: OrderData): Promise<CommissionBreakdown> {
    await this.connect();
    const commission = this.calculateCommission(orderData);

    // Update order in database (would integrate with Prisma)
    await this.updateOrderCommissionInDatabase(orderId, commission);

    // Cache commission data for quick access
    await this.redis.setEx(
      `commission:${orderId}`, 
      30 * 24 * 60 * 60, // 30 days
      JSON.stringify(commission)
    );

    // Update daily commission totals
    await this.updateDailyCommissionTotals(commission);

    return commission;
  }

  // Get commission for an order
  async getOrderCommission(orderId: string): Promise<CommissionBreakdown | null> {
    await this.connect();
    const cached = await this.redis.get(`commission:${orderId}`);
    
    if (cached) {
      return JSON.parse(cached);
    }

    // Fallback to database
    return await this.getOrderCommissionFromDatabase(orderId);
  }

  // Get daily commission totals
  async getDailyCommissionTotals(date: Date = new Date()): Promise<{
    date: string;
    totalCommission: number;
    m2Commission: number;
    tonCommission: number;
    orderCount: number;
  }> {
    await this.connect();
    const dateKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
    const key = `daily_commission:${dateKey}`;
    
    const data = await this.redis.hGetAll(key);
    
    return {
      date: dateKey,
      totalCommission: parseFloat(data.total_commission || '0'),
      m2Commission: parseFloat(data.m2_commission || '0'),
      tonCommission: parseFloat(data.ton_commission || '0'),
      orderCount: parseInt(data.order_count || '0')
    };
  }

  // Get monthly commission totals
  async getMonthlyCommissionTotals(year: number, month: number): Promise<{
    year: number;
    month: number;
    totalCommission: number;
    m2Commission: number;
    tonCommission: number;
    orderCount: number;
    dailyBreakdown: any[];
  }> {
    await this.connect();
    const monthKey = `${year}-${month.toString().padStart(2, '0')}`;
    
    // Get all daily totals for the month
    const keys = await this.redis.keys(`daily_commission:${monthKey}-*`);
    let totalCommission = 0;
    let m2Commission = 0;
    let tonCommission = 0;
    let orderCount = 0;
    const dailyBreakdown = [];

    for (const key of keys) {
      const data = await this.redis.hGetAll(key);
      const dayTotal = parseFloat(data.total_commission || '0');
      const dayM2 = parseFloat(data.m2_commission || '0');
      const dayTon = parseFloat(data.ton_commission || '0');
      const dayOrders = parseInt(data.order_count || '0');

      totalCommission += dayTotal;
      m2Commission += dayM2;
      tonCommission += dayTon;
      orderCount += dayOrders;

      dailyBreakdown.push({
        date: key.split(':')[1],
        totalCommission: dayTotal,
        m2Commission: dayM2,
        tonCommission: dayTon,
        orderCount: dayOrders
      });
    }

    return {
      year,
      month,
      totalCommission,
      m2Commission,
      tonCommission,
      orderCount,
      dailyBreakdown: dailyBreakdown.sort((a, b) => a.date.localeCompare(b.date))
    };
  }

  // Calculate commission preview for bid
  calculateCommissionPreview(items: OrderItem[]): CommissionBreakdown {
    return this.calculateCommission({
      items,
      unit: items[0]?.unit || 'm2',
      totalQuantity: items.reduce((sum, item) => sum + item.quantity, 0)
    });
  }

  // Get commission statistics
  async getCommissionStatistics(): Promise<{
    totalEarnings: number;
    thisMonthEarnings: number;
    lastMonthEarnings: number;
    averageOrderCommission: number;
    topCommissionCategories: any[];
  }> {
    await this.connect();
    
    const now = new Date();
    const thisMonth = await this.getMonthlyCommissionTotals(now.getFullYear(), now.getMonth() + 1);
    const lastMonth = await this.getMonthlyCommissionTotals(
      now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear(),
      now.getMonth() === 0 ? 12 : now.getMonth()
    );

    // Get total earnings (would typically come from database)
    const totalEarnings = await this.getTotalEarningsFromDatabase();

    return {
      totalEarnings,
      thisMonthEarnings: thisMonth.totalCommission,
      lastMonthEarnings: lastMonth.totalCommission,
      averageOrderCommission: thisMonth.orderCount > 0 ? thisMonth.totalCommission / thisMonth.orderCount : 0,
      topCommissionCategories: [
        { category: 'm2', amount: thisMonth.m2Commission },
        { category: 'ton', amount: thisMonth.tonCommission }
      ]
    };
  }

  private async updateDailyCommissionTotals(commission: CommissionBreakdown): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    const key = `daily_commission:${today}`;
    
    // Increment daily totals
    await this.redis.hIncrByFloat(key, 'total_commission', commission.totalCommission);
    await this.redis.hIncrByFloat(key, 'm2_commission', commission.m2Commission);
    await this.redis.hIncrByFloat(key, 'ton_commission', commission.tonCommission);
    await this.redis.hIncrBy(key, 'order_count', 1);
    
    // Set expiration for cleanup (keep for 2 years)
    await redisManager.safeExpire(key, 2 * 365 * 24 * 60 * 60);
  }

  private async updateOrderCommissionInDatabase(orderId: string, commission: CommissionBreakdown): Promise<void> {
    // This would integrate with your Prisma client
    console.log(`Updating order ${orderId} with commission:`, commission);
  }

  private async getOrderCommissionFromDatabase(orderId: string): Promise<CommissionBreakdown | null> {
    // This would integrate with your Prisma client
    return null;
  }

  private async getTotalEarningsFromDatabase(): Promise<number> {
    // This would integrate with your Prisma client
    return 0;
  }
}
