# 🚀 Türkiye Doğal Taş Pazaryeri - Production Deployment Guide

Bu rehber, projeyi production ortamına deploy etmek için gerekli tüm adımları içerir.

## 📋 Ön Gereksinimler

### Sistem Gereksinimleri
- **<PERSON><PERSON><PERSON><PERSON>:** Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM:** Minimum 8GB (16GB önerilen)
- **Disk:** Minimum 100GB SSD
- **CPU:** Minimum 4 core (8 core önerilen)
- **Network:** Statik IP adresi ve domain name

### Yazılım Gereksinimleri
- Docker 24.0+
- Docker Compose 2.0+
- PostgreSQL 15+
- Nginx 1.20+
- Node.js 18+ (development için)

## 🔧 1. Sunucu Hazırlığı

### Docker Kurulumu
```bash
# Ubuntu/Debian için
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Docker Compose kurulumu
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### Nginx Kurulumu
```bash
sudo apt update
sudo apt install nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

## 🔒 2. SSL Sertifikası Kurulumu

### Let's Encrypt ile SSL
```bash
# Certbot kurulumu
sudo apt install certbot python3-certbot-nginx

# SSL sertifikası alma
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Otomatik yenileme
sudo crontab -e
# Şu satırı ekleyin:
0 12 * * * /usr/bin/certbot renew --quiet
```

### Self-Signed Sertifika (Test için)
```bash
cd backend
node scripts/setup-ssl.js --dev
```

## 🗄️ 3. Database Kurulumu

### PostgreSQL Kurulumu
```bash
sudo apt install postgresql postgresql-contrib
sudo systemctl enable postgresql
sudo systemctl start postgresql

# Database oluşturma
sudo -u postgres createdb natural_stone_marketplace_prod
sudo -u postgres createuser --interactive
```

### Database Migration
```bash
cd backend
cp .env.production.example .env.production
# .env.production dosyasını düzenleyin
node scripts/setup-production-db.js
```

## 📝 4. Environment Konfigürasyonu

### Backend Environment (.env.production)
```bash
cd backend
cp .env.production.example .env.production
```

Aşağıdaki değerleri güncelleyin:
- `DATABASE_URL`: PostgreSQL bağlantı string'i
- `JWT_SECRET`: Güvenli JWT secret
- `SMTP_*`: Email konfigürasyonu
- `AWS_*`: S3 konfigürasyonu
- API anahtarları

### Frontend Environment (.env.production)
```bash
cd frontend
cp .env.production.example .env.production
```

Aşağıdaki değerleri güncelleyin:
- `NEXT_PUBLIC_API_URL`: Backend API URL'i
- `NEXTAUTH_URL`: Frontend URL'i
- `NEXTAUTH_SECRET`: NextAuth secret
- External service API keys

### Root Environment (.env.production)
```bash
cp .env.production.example .env.production
```

Docker Compose için gerekli değerleri güncelleyin.

## 🐳 5. Docker Deployment

### Build ve Deploy
```bash
# Production build
./scripts/deploy-production.sh

# Manuel deployment
docker-compose -f docker-compose.production.yml up -d
```

### Servis Durumu Kontrolü
```bash
docker-compose -f docker-compose.production.yml ps
docker-compose -f docker-compose.production.yml logs -f
```

## 🔍 6. Monitoring Kurulumu

### Prometheus ve Grafana
```bash
# Monitoring servisleri başlatma
docker-compose -f docker-compose.production.yml up -d prometheus grafana

# Grafana erişimi
# URL: http://your-server:3001
# Username: admin
# Password: .env.production dosyasındaki GRAFANA_PASSWORD
```

### Log Monitoring
```bash
# Uygulama logları
docker-compose -f docker-compose.production.yml logs -f backend
docker-compose -f docker-compose.production.yml logs -f frontend

# Nginx logları
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## 💾 7. Backup Kurulumu

### Otomatik Backup
```bash
# Backup script'ini çalıştırma
cd backend
node scripts/backup-database.js

# Cron job ekleme
crontab -e
# Şu satırı ekleyin (her gün saat 02:00'da backup):
0 2 * * * cd /path/to/project/backend && node scripts/backup-database.js
```

### S3 Backup (Opsiyonel)
```bash
# AWS CLI kurulumu
sudo apt install awscli
aws configure

# S3 bucket oluşturma
aws s3 mb s3://natural-stone-marketplace-backups
```

## 🔧 8. Performance Optimizasyonu

### Nginx Optimizasyonu
```nginx
# /etc/nginx/sites-available/yourdomain.com
# Dosyayı nginx/nginx.conf içeriği ile güncelleyin
```

### Database Optimizasyonu
```sql
-- PostgreSQL optimizasyonu
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
SELECT pg_reload_conf();
```

## 🚨 9. Güvenlik Konfigürasyonu

### Firewall Kurulumu
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw deny 5432  # PostgreSQL sadece localhost
sudo ufw deny 6379  # Redis sadece localhost
```

### Fail2Ban Kurulumu
```bash
sudo apt install fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

## 📊 10. Health Checks

### Servis Durumu
```bash
# Backend health check
curl -f http://localhost:8000/health

# Frontend health check
curl -f http://localhost:3000

# Database bağlantısı
docker-compose -f docker-compose.production.yml exec postgres pg_isready
```

### Performance Testleri
```bash
# Load testing (Apache Bench)
sudo apt install apache2-utils
ab -n 1000 -c 10 https://yourdomain.com/

# Database performance
docker-compose -f docker-compose.production.yml exec postgres psql -U postgres -d natural_stone_marketplace_prod -c "SELECT * FROM pg_stat_activity;"
```

## 🔄 11. Deployment Workflow

### Güncellemeler
```bash
# 1. Backup oluştur
cd backend && node scripts/backup-database.js

# 2. Yeni kodu çek
git pull origin main

# 3. Build ve deploy
./scripts/deploy-production.sh

# 4. Health check
curl -f https://yourdomain.com/api/health
```

### Rollback
```bash
# Database restore
cd backend && node scripts/restore-database.js

# Container rollback
docker-compose -f docker-compose.production.yml down
docker-compose -f docker-compose.production.yml up -d
```

## 📞 12. Troubleshooting

### Yaygın Sorunlar

**Database bağlantı hatası:**
```bash
# PostgreSQL servisini kontrol et
sudo systemctl status postgresql
# Connection string'i kontrol et
```

**SSL sertifika hatası:**
```bash
# Sertifika durumunu kontrol et
sudo certbot certificates
# Yenileme
sudo certbot renew
```

**Memory yetersizliği:**
```bash
# Memory kullanımını kontrol et
free -h
# Swap ekleme
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### Log Dosyaları
- Application logs: `backend/logs/`
- Nginx logs: `/var/log/nginx/`
- PostgreSQL logs: `/var/log/postgresql/`
- Docker logs: `docker-compose logs`

## 📈 13. Monitoring ve Alerting

### Grafana Dashboards
- System Metrics: CPU, Memory, Disk
- Application Metrics: Response time, Error rate
- Business Metrics: User activity, Orders

### Alert Konfigürasyonu
- Email notifications
- Slack integration
- SMS alerts (critical issues)

## 🎯 14. Go-Live Checklist

- [ ] SSL sertifikaları kuruldu
- [ ] Database migration tamamlandı
- [ ] Environment variables ayarlandı
- [ ] Backup sistemi çalışıyor
- [ ] Monitoring aktif
- [ ] Performance testleri geçti
- [ ] Security scan tamamlandı
- [ ] DNS kayıtları güncellendi
- [ ] Load balancer konfigüre edildi
- [ ] CDN ayarlandı

## 📞 Destek

Deployment sırasında sorun yaşarsanız:
1. Log dosyalarını kontrol edin
2. Health check endpoint'lerini test edin
3. Monitoring dashboard'larını inceleyin
4. Backup'larınızın güncel olduğundan emin olun

---

**🎉 Başarılı deployment sonrası:**
- Frontend: https://yourdomain.com
- Admin Panel: https://yourdomain.com/admin
- API Health: https://yourdomain.com/api/health
- Monitoring: http://your-server:3001
