import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import apiService from '@/services/api';
import { Product, BidRequest, Order, PaginatedResponse } from '@/types';

// Query Keys
export const queryKeys = {
  health: ['health'],
  products: ['products'],
  product: (id: string) => ['products', id],
  bidRequests: ['bidRequests'],
  bidRequest: (id: string) => ['bidRequests', id],
  orders: ['orders'],
  order: (id: string) => ['orders', id],
  user: ['user'],
  profile: ['profile'],
} as const;

// Health Check Hook
export const useHealthCheck = () => {
  return useQuery({
    queryKey: queryKeys.health,
    queryFn: () => apiService.healthCheck(),
    staleTime: 1000 * 60, // 1 minute
    refetchInterval: 1000 * 60 * 5, // 5 minutes
  });
};

// Products Hooks
export const useProducts = (params?: {
  page?: number;
  limit?: number;
  category?: string;
  search?: string;
}) => {
  return useQuery({
    queryKey: [...queryKeys.products, params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (params?.page) searchParams.set('page', params.page.toString());
      if (params?.limit) searchParams.set('limit', params.limit.toString());
      if (params?.category) searchParams.set('category', params.category);
      if (params?.search) searchParams.set('search', params.search);

      const response = await apiService.get<PaginatedResponse<Product>>(
        `/api/products?${searchParams.toString()}`
      );
      return response.data;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

export const useProduct = (id: string) => {
  return useQuery({
    queryKey: queryKeys.product(id),
    queryFn: async () => {
      const response = await apiService.get<Product>(`/api/products/${id}`);
      return response.data;
    },
    enabled: !!id,
    staleTime: 1000 * 60 * 10, // 10 minutes
  });
};

// Bid Requests Hooks
export const useBidRequests = (params?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  return useQuery({
    queryKey: [...queryKeys.bidRequests, params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (params?.page) searchParams.set('page', params.page.toString());
      if (params?.limit) searchParams.set('limit', params.limit.toString());
      if (params?.status) searchParams.set('status', params.status);

      const response = await apiService.get<PaginatedResponse<BidRequest>>(
        `/api/bid-requests?${searchParams.toString()}`
      );
      return response.data;
    },
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
};

export const useBidRequest = (id: string) => {
  return useQuery({
    queryKey: queryKeys.bidRequest(id),
    queryFn: async () => {
      const response = await apiService.get<BidRequest>(`/api/bid-requests/${id}`);
      return response.data;
    },
    enabled: !!id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Orders Hooks
export const useOrders = (params?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  return useQuery({
    queryKey: [...queryKeys.orders, params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (params?.page) searchParams.set('page', params.page.toString());
      if (params?.limit) searchParams.set('limit', params.limit.toString());
      if (params?.status) searchParams.set('status', params.status);

      const response = await apiService.get<PaginatedResponse<Order>>(
        `/api/orders?${searchParams.toString()}`
      );
      return response.data;
    },
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
};

export const useOrder = (id: string) => {
  return useQuery({
    queryKey: queryKeys.order(id),
    queryFn: async () => {
      const response = await apiService.get<Order>(`/api/orders/${id}`);
      return response.data;
    },
    enabled: !!id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Mutation Hooks
export const useCreateBidRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Partial<BidRequest>) => {
      const response = await apiService.post<BidRequest>('/api/bid-requests', data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch bid requests
      queryClient.invalidateQueries({ queryKey: queryKeys.bidRequests });
    },
  });
};

export const useCreateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Partial<Order>) => {
      const response = await apiService.post<Order>('/api/orders', data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: queryKeys.orders });
    },
  });
};

export const useUpdateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Order> }) => {
      const response = await apiService.put<Order>(`/api/orders/${id}`, data);
      return response.data;
    },
    onSuccess: (data) => {
      // Update the specific order in cache
      queryClient.setQueryData(queryKeys.order(data!.id), data);
      // Invalidate orders list
      queryClient.invalidateQueries({ queryKey: queryKeys.orders });
    },
  });
};

// File Upload Hook
export const useFileUpload = () => {
  return useMutation({
    mutationFn: async ({
      file,
      endpoint,
      onProgress,
    }: {
      file: File;
      endpoint: string;
      onProgress?: (progress: number) => void;
    }) => {
      const response = await apiService.upload(endpoint, file, onProgress);
      return response.data;
    },
  });
};
