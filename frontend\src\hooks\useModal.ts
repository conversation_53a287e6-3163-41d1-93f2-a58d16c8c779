'use client';

import { useCallback } from 'react';

export const useModal = () => {
  const showLoginModal = useCallback(() => {
    // Modal açma işlemi - şimdilik alert
    alert('G<PERSON>ş modal\'ı açılacak - henüz implement edilmedi');
  }, []);

  const showCustomerRegisterModal = useCallback(() => {
    // Modal açma işlemi - şimdilik alert
    alert('Müşteri kayıt modal\'ı açılacak - henüz implement edilmedi');
  }, []);

  const showProducerRegisterModal = useCallback(() => {
    // Modal açma işlemi - şimdilik alert
    alert('Üretici kayıt modal\'ı açılacak - henüz implement edilmedi');
  }, []);

  return {
    showLoginModal,
    showCustomerRegisterModal,
    showProducerRegisterModal
  };
};
