# Multi-stage Dockerfile for AI Marketing System Production Deployment

# ============ BUILD STAGE ============
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++ git

# Copy package files
COPY backend/package*.json ./
COPY frontend/package*.json ./frontend/

# Install dependencies
RUN npm ci --only=production && \
    cd frontend && npm ci --only=production

# Copy source code
COPY backend/ ./
COPY frontend/ ./frontend/

# Build backend
RUN npm run build

# Build frontend
RUN cd frontend && npm run build

# ============ PRODUCTION STAGE ============
FROM node:18-alpine AS production

# Install system dependencies
RUN apk add --no-cache \
    postgresql-client \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create app user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Set working directory
WORKDIR /app

# Copy built application
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/package*.json ./
COPY --from=builder --chown=nextjs:nodejs /app/frontend/dist ./frontend/dist

# Copy AI Marketing specific files
COPY --from=builder --chown=nextjs:nodejs /app/src/modules/ai-marketing ./src/modules/ai-marketing

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/uploads && \
    chown -R nextjs:nodejs /app/logs /app/data /app/uploads

# Health check script
COPY --chown=nextjs:nodejs scripts/health-check.sh ./scripts/
RUN chmod +x ./scripts/health-check.sh

# Environment variables
ENV NODE_ENV=production
ENV PORT=3001
ENV FRONTEND_PORT=3000

# Expose ports
EXPOSE 3000 3001

# Switch to non-root user
USER nextjs

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD ./scripts/health-check.sh

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["npm", "start"]
