'use client';

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Container } from "./container"
import { Button } from "./button"
import NotificationBell from "../notifications/NotificationBell"
import { useAuth } from "@/contexts/auth-context"



export interface NavigationProps extends React.HTMLAttributes<HTMLElement> {
  brand?: {
    name: string
    href: string
    logo?: string
  }
  links?: Array<{
    name: string
    href: string
    active?: boolean
    external?: boolean
  }>
  actions?: React.ReactNode
  sticky?: boolean
  transparent?: boolean
}

/**
 * Navigation component with improved layout
 * Left: Logo + Brand Name | Center: Navigation Links | Right: Action Buttons
 */
const Navigation = React.forwardRef<HTMLElement, NavigationProps>(
  ({
    className,
    brand = { name: "Türkiye Doğal Taş Pazarı", href: "/" },
    links = [],
    actions,
    sticky = true,
    transparent = false,
    ...props
  }, ref) => {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false)
    const { user, isAuthenticated } = useAuth()
    const pathname = usePathname()

    // Get current locale from pathname
    const getCurrentLocale = () => {
      const segments = pathname.split('/').filter(Boolean)
      const locales = ['tr', 'en', 'ar', 'zh', 'ru']
      return locales.includes(segments[0]) ? segments[0] : 'tr'
    }

    const currentLocale = getCurrentLocale()

    // Helper function to create locale-aware links
    const createLocaleLink = (href: string) => {
      if (currentLocale === 'tr') {
        return href
      }
      return `/${currentLocale}${href}`
    }

    const toggleMobileMenu = () => {
      setIsMobileMenuOpen(!isMobileMenuOpen)
    }

    return (
      <nav
        ref={ref}
        className={cn(
          "w-full border-b border-stone-200",
          "transition-all duration-300 ease-in-out",
          sticky && "sticky top-0 z-50",
          transparent ? "bg-transparent" : "bg-white",
          !transparent && "backdrop-blur-sm shadow-sm",
          className
        )}
        {...props}
      >
        <Container className="px-4 lg:px-6 xl:px-8">
          <div className="flex items-center h-14">

            {/* Left: Brand Section */}
            <div className="flex items-center gap-2 mr-4">
              <Link
                href={createLocaleLink(brand.href)}
                className={cn(
                  "flex items-center gap-2",
                  "text-base lg:text-lg font-bold",
                  "text-amber-700 hover:text-amber-800",
                  "transition-all duration-200 hover:scale-105"
                )}
              >
                {brand.logo ? (
                  <img
                    src={brand.logo}
                    alt={brand.name}
                    className="h-6 lg:h-7 w-auto"
                  />
                ) : (
                  <span className="text-xl lg:text-2xl">🏛️</span>
                )}
                <span className="hidden sm:block whitespace-nowrap">
                  Türkiye Doğal Taş
                </span>
                <span className="sm:hidden text-sm">
                  Doğal Taş
                </span>
              </Link>
            </div>

            {/* Navigation Links - Left aligned */}
            <div className="hidden lg:flex items-center flex-1">
              <div className="flex items-center gap-4 xl:gap-6">
                {links.map((link) => (
                  <Link
                    key={link.href}
                    href={link.external ? link.href : createLocaleLink(link.href)}
                    target={link.external ? "_blank" : undefined}
                    rel={link.external ? "noopener noreferrer" : undefined}
                    className={cn(
                      "relative px-2 py-1 text-sm font-medium",
                      "transition-all duration-200 ease-in-out",
                      "hover:text-amber-700 hover:scale-105",
                      "focus:outline-none focus:text-amber-700",
                      "whitespace-nowrap",
                      link.active
                        ? "text-amber-700 font-semibold"
                        : "text-stone-700 hover:text-amber-700"
                    )}
                  >
                    {link.name}
                    {link.active && (
                      <span className={cn(
                        "absolute -bottom-1 left-1/2 transform -translate-x-1/2",
                        "w-8 h-0.5 bg-amber-700 rounded-full"
                      )} />
                    )}
                  </Link>
                ))}
              </div>
            </div>

            {/* Right: Action Buttons */}
            <div className="flex items-center gap-2 ml-auto">


              {/* Notifications - Show only for authenticated users */}
              {isAuthenticated && (
                <NotificationBell className="hidden lg:block" />
              )}

              <div className="hidden lg:flex items-center gap-2">
                {actions}
              </div>

              {/* Mobile Menu Button */}
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden text-stone-700 hover:text-amber-700 p-1"
                onClick={toggleMobileMenu}
                aria-label="Toggle mobile menu"
              >
                <svg
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  {isMobileMenuOpen ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  )}
                </svg>
              </Button>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <div className="lg:hidden border-t border-stone-200 py-4">
              <div className="flex flex-col gap-2">
                {links.map((link) => (
                  <Link
                    key={link.href}
                    href={link.external ? link.href : createLocaleLink(link.href)}
                    target={link.external ? "_blank" : undefined}
                    rel={link.external ? "noopener noreferrer" : undefined}
                    className={cn(
                      "px-4 py-3 text-base font-medium rounded-md",
                      "transition-colors duration-200",
                      "hover:bg-amber-50",
                      link.active
                        ? "text-amber-700 bg-amber-50 font-semibold"
                        : "text-stone-700 hover:text-amber-700"
                    )}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {link.name}
                  </Link>
                ))}



                {/* Mobile Notifications */}
                {isAuthenticated && (
                  <div className="px-4 py-2 border-t border-stone-200 mt-2 pt-4">
                    <NotificationBell />
                  </div>
                )}

                {actions && (
                  <div className="px-4 py-2 border-t border-stone-200 mt-2 pt-4">
                    {actions}
                  </div>
                )}
              </div>
            </div>
          )}
        </Container>
      </nav>
    )
  }
)

Navigation.displayName = "Navigation"

export { Navigation }
