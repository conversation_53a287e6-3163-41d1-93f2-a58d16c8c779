'use client';

import React, { useState, useEffect, useCallback, useMemo, Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { 
  OrbitControls, 
  Environment, 
  ContactShadows, 
  Html, 
  useProgress,
  Grid,
  Sky
} from '@react-three/drei';
import * as THREE from 'three';
import { 
  Settings, 
  Maximize2, 
  RotateCw, 
  Eye, 
  EyeOff,
  Lightbulb,
  Grid3X3,
  Layers
} from 'lucide-react';
import ProductRenderer from './ProductRenderer';
import RoomTemplate from '../rooms/RoomTemplate';
import FloorRenderer from './FloorRenderer';
import { GroutSettings, DEFAULT_GROUT_SETTINGS } from '../utils/grout';
import LightingSystem, { LightingConfiguration, LightingPreset, TimeOfDay } from '../lighting/LightingSystem';

// Types
export interface ProductPlacement {
  id: string;
  productId: string;
  position: { x: number; y: number; z: number };
  rotation: number;
  scale: { width: number; height: number };
  area: number;
  pattern: 'standard' | 'chess' | 'herringbone' | 'diagonal' | 'brick' | 'hexagon';
  opacity: number;
  visible: boolean;
  groutSettings: GroutSettings;
}

export interface ShowroomConfiguration {
  room: 'living' | 'kitchen' | 'bathroom' | 'bedroom' | 'outdoor';
  lighting: LightingConfiguration;
  environment: 'studio' | 'apartment' | 'warehouse' | 'forest';
  showGrid: boolean;
  showShadows: boolean;
  showEnvironment: boolean;
  cameraPosition: [number, number, number];
  totalArea: number;
}

export interface ShowroomEngineProps {
  products: ProductPlacement[];
  configuration: ShowroomConfiguration;
  onProductUpdate: (products: ProductPlacement[]) => void;
  onConfigurationChange: (config: Partial<ShowroomConfiguration>) => void;
  className?: string;
  width?: number;
  height?: number;
}

// Loading Component
function LoadingFallback() {
  const { progress } = useProgress();
  
  return (
    <Html center>
      <div className="flex flex-col items-center justify-center p-8 bg-white rounded-lg shadow-lg">
        <div className="w-16 h-16 border-4 border-amber-200 border-t-amber-600 rounded-full animate-spin mb-4"></div>
        <p className="text-stone-700 font-medium">3D Showroom Yükleniyor...</p>
        <p className="text-stone-500 text-sm mt-2">{Math.round(progress)}% tamamlandı</p>
      </div>
    </Html>
  );
}

// Main ShowroomEngine Component
export const ShowroomEngine: React.FC<ShowroomEngineProps> = ({
  products,
  configuration,
  onProductUpdate,
  onConfigurationChange,
  className = '',
  width = 1200,
  height = 700
}) => {
  // State management
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [viewerSettings, setViewerSettings] = useState({
    showWireframe: false,
    showBounds: false,
    showStats: false,
    autoRotate: false
  });

  // Memoized camera position
  const cameraPosition = useMemo(() => 
    configuration.cameraPosition || [8, 6, 8] as [number, number, number], 
    [configuration.cameraPosition]
  );

  // Handle product selection
  const handleProductSelect = useCallback((productId: string) => {
    setSelectedProduct(productId === selectedProduct ? null : productId);
  }, [selectedProduct]);

  // Handle product position update
  const handleProductMove = useCallback((productId: string, newPosition: { x: number; y: number; z: number }) => {
    const updatedProducts = products.map(product => 
      product.id === productId 
        ? { ...product, position: newPosition }
        : product
    );
    onProductUpdate(updatedProducts);
  }, [products, onProductUpdate]);

  // Toggle viewer settings
  const toggleSetting = useCallback((setting: keyof typeof viewerSettings) => {
    setViewerSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  }, []);

  // Handle configuration changes
  const updateConfiguration = useCallback((updates: Partial<ShowroomConfiguration>) => {
    onConfigurationChange(updates);
  }, [onConfigurationChange]);

  return (
    <div className={`relative bg-gray-50 rounded-lg overflow-hidden ${className}`}>
      {/* Top Controls */}
      <div className="absolute top-4 left-4 z-10 flex gap-2">
        <button
          onClick={() => toggleSetting('showWireframe')}
          className={`p-2 rounded-lg backdrop-blur-sm transition-colors ${
            viewerSettings.showWireframe 
              ? 'bg-amber-500 text-white' 
              : 'bg-white/80 text-stone-700 hover:bg-white'
          }`}
          title="Wireframe Görünümü"
        >
          <Grid3X3 size={18} />
        </button>
        
        <button
          onClick={() => updateConfiguration({ showGrid: !configuration.showGrid })}
          className={`p-2 rounded-lg backdrop-blur-sm transition-colors ${
            configuration.showGrid 
              ? 'bg-amber-500 text-white' 
              : 'bg-white/80 text-stone-700 hover:bg-white'
          }`}
          title="Grid Göster/Gizle"
        >
          <Layers size={18} />
        </button>

        <button
          onClick={() => updateConfiguration({ showEnvironment: !configuration.showEnvironment })}
          className={`p-2 rounded-lg backdrop-blur-sm transition-colors ${
            configuration.showEnvironment 
              ? 'bg-amber-500 text-white' 
              : 'bg-white/80 text-stone-700 hover:bg-white'
          }`}
          title="Ortam Göster/Gizle"
        >
          {configuration.showEnvironment ? <Eye size={18} /> : <EyeOff size={18} />}
        </button>

        <button
          onClick={() => toggleSetting('autoRotate')}
          className={`p-2 rounded-lg backdrop-blur-sm transition-colors ${
            viewerSettings.autoRotate 
              ? 'bg-amber-500 text-white' 
              : 'bg-white/80 text-stone-700 hover:bg-white'
          }`}
          title="Otomatik Döndürme"
        >
          <RotateCw size={18} />
        </button>
      </div>

      {/* Right Controls */}
      <div className="absolute top-4 right-4 z-10 flex flex-col gap-2">
        <select
          value={configuration.lighting.preset}
          onChange={(e) => updateConfiguration({
            lighting: {
              ...configuration.lighting,
              preset: e.target.value as LightingPreset
            }
          })}
          className="px-3 py-2 rounded-lg bg-white/80 backdrop-blur-sm text-stone-700 text-sm border-0 focus:ring-2 focus:ring-amber-500"
        >
          <option value="natural">Doğal Işık</option>
          <option value="warm">Sıcak Işık</option>
          <option value="cool">Soğuk Işık</option>
          <option value="dramatic">Dramatik Işık</option>
          <option value="sunset">Gün Batımı</option>
          <option value="studio">Stüdyo</option>
        </select>

        <select
          value={configuration.lighting.timeOfDay}
          onChange={(e) => updateConfiguration({
            lighting: {
              ...configuration.lighting,
              timeOfDay: e.target.value as TimeOfDay
            }
          })}
          className="px-3 py-2 rounded-lg bg-white/80 backdrop-blur-sm text-stone-700 text-sm border-0 focus:ring-2 focus:ring-amber-500"
        >
          <option value="morning">Sabah</option>
          <option value="noon">Öğle</option>
          <option value="afternoon">Öğleden Sonra</option>
          <option value="evening">Akşam</option>
          <option value="night">Gece</option>
        </select>

        <select
          value={configuration.environment}
          onChange={(e) => updateConfiguration({ environment: e.target.value as any })}
          className="px-3 py-2 rounded-lg bg-white/80 backdrop-blur-sm text-stone-700 text-sm border-0 focus:ring-2 focus:ring-amber-500"
        >
          <option value="studio">Stüdyo</option>
          <option value="apartment">Daire</option>
          <option value="warehouse">Depo</option>
          <option value="forest">Doğa</option>
        </select>
      </div>

      {/* 3D Canvas */}
      <Canvas
        style={{ width, height }}
        camera={{ 
          position: cameraPosition, 
          fov: 50,
          near: 0.1,
          far: 1000
        }}
        shadows
        dpr={[1, 2]}
        gl={{ 
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        <Suspense fallback={<LoadingFallback />}>
          {/* Advanced Lighting System */}
          <LightingSystem
            configuration={configuration.lighting}
            roomType={configuration.room}
          />

          {/* Environment */}
          {configuration.showEnvironment && (
            <>
              <Environment preset={configuration.environment} />
              {configuration.environment === 'forest' && <Sky />}
            </>
          )}

          {/* Grid */}
          {configuration.showGrid && (
            <Grid
              args={[20, 20]}
              cellSize={1}
              cellThickness={0.5}
              cellColor="#d1d5db"
              sectionSize={5}
              sectionThickness={1}
              sectionColor="#9ca3af"
              fadeDistance={25}
              fadeStrength={1}
              followCamera={false}
              infiniteGrid={true}
            />
          )}

          {/* Ground Shadows */}
          {configuration.showShadows && (
            <ContactShadows 
              position={[0, -0.01, 0]} 
              opacity={0.3} 
              scale={20} 
              blur={2} 
              far={10} 
            />
          )}

          {/* Camera Controls */}
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            autoRotate={viewerSettings.autoRotate}
            autoRotateSpeed={0.5}
            minDistance={3}
            maxDistance={50}
            minPolarAngle={0}
            maxPolarAngle={Math.PI / 2}
            target={[0, 0, 0]}
          />

          {/* Room Template */}
          <RoomTemplate
            roomType={configuration.room}
            visible={true}
            opacity={0.8}
            showEnvironment={configuration.showEnvironment}
          />

          {/* Floor Tiles */}
          {products.length > 0 && (
            <FloorRenderer
              products={products}
              roomDimensions={{
                width: 10,
                depth: 8
              }}
              showGrid={configuration.showGrid}
            />
          )}

          {/* Products Renderer */}
          <ProductRenderer
            products={products}
            selectedProduct={selectedProduct}
            onProductSelect={handleProductSelect}
            onProductMove={handleProductMove}
            showWireframe={viewerSettings.showWireframe}
          />
          
        </Suspense>
      </Canvas>

      {/* Bottom Status Bar */}
      <div className="absolute bottom-4 left-4 right-4 z-10">
        <div className="bg-white/90 backdrop-blur-sm rounded-lg px-4 py-2 flex items-center justify-between text-sm text-stone-600">
          <div className="flex items-center gap-4">
            <span>Ürün Sayısı: {products.length}</span>
            <span>Toplam Alan: {configuration.totalArea.toFixed(2)} m²</span>
            {selectedProduct && (
              <span className="text-amber-600 font-medium">
                Seçili: {products.find(p => p.id === selectedProduct)?.productId}
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Lightbulb size={16} className="text-amber-500" />
            <span className="capitalize">{configuration.lighting.preset} - {configuration.lighting.timeOfDay}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
