/**
 * Authentication Routes
 * Handles user registration, login, token refresh, and logout
 */

import { Router } from 'express';
import { body } from 'express-validator';
import { validateInput, authLimiter } from '../../middleware/security';
import AuthController from '../../modules/auth/auth.controller';

const router = Router();
const authController = new AuthController();

// Validation rules
const registerValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Şifre en az 8 karakter olmalıdır')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Şifre en az 1 küçük harf, 1 büyük harf ve 1 rakam içermelidir'),
  body('userType')
    .isIn(['customer', 'producer'])
    .withMessage('User type must be either customer or producer'),
  body('companyName')
    .isLength({ min: 2, max: 100 })
    .trim()
    .escape()
    .withMessage('Company name must be between 2 and 100 characters')
];

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

const refreshTokenValidation = [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required')
];

/**
 * @route POST /api/auth/register
 * @desc Register a new user (customer or producer)
 * @access Public
 */
router.post('/register', 
  authLimiter,
  registerValidation,
  validateInput(registerValidation),
  authController.register
);

/**
 * @route POST /api/auth/login
 * @desc Login user and return JWT tokens
 * @access Public
 */
router.post('/login',
  authLimiter,
  loginValidation,
  validateInput(loginValidation),
  authController.login
);

/**
 * @route POST /api/auth/refresh
 * @desc Refresh access token using refresh token
 * @access Public
 */
router.post('/refresh',
  authLimiter,
  refreshTokenValidation,
  validateInput(refreshTokenValidation),
  authController.refreshToken
);

/**
 * @route POST /api/auth/logout
 * @desc Logout user and revoke refresh token
 * @access Public
 */
router.post('/logout',
  authController.logoutUser
);

/**
 * @route POST /api/auth/logout-all
 * @desc Logout user from all devices (revoke all refresh tokens)
 * @access Private
 */
router.post('/logout-all',
  // TODO: Add auth middleware when implemented
  authController.logoutUser
);

export default router;
