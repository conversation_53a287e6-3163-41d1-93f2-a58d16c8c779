const path = require('path');

module.exports = {
  i18n: {
    defaultLocale: 'tr',
    locales: ['tr', 'en', 'ar', 'zh', 'ru'],
    localeDetection: true,
    localePath: path.resolve('./public/locales'),
  },
  
  // Namespace configuration
  ns: [
    'common',
    'navigation',
    'products',
    'auth',
    'dashboard',
    'forms',
    'errors',
    'news',
    'about',
    'contact',
    '3d-viewer'
  ],
  
  defaultNS: 'common',
  
  // Fallback configuration
  fallbackLng: {
    'ar': ['en', 'tr'],
    'zh': ['en', 'tr'],
    'ru': ['en', 'tr'],
    'default': ['tr']
  },
  
  // Debug mode for development
  debug: process.env.NODE_ENV === 'development',
  
  // Interpolation configuration
  interpolation: {
    escapeValue: false, // React already escapes values
  },
  
  // React configuration
  react: {
    useSuspense: false,
  },
  
  // Server-side rendering configuration
  serializeConfig: false,
  
  // Reloading configuration for development
  reloadOnPrerender: process.env.NODE_ENV === 'development',
  
  // Custom detection options
  detection: {
    order: ['path', 'cookie', 'header', 'querystring', 'subdomain'],
    caches: ['cookie'],
    cookieMinutes: 60 * 24 * 30, // 30 days
    cookieDomain: process.env.NODE_ENV === 'production' ? '.yourdomain.com' : 'localhost',
  },
  
  // Backend options for server-side
  backend: {
    loadPath: '/locales/{{lng}}/{{ns}}.json',
  },
  
  // Custom load path for client-side
  load: 'languageOnly',
  
  // Preload languages
  preload: ['tr', 'en'],
  
  // Clean code options
  cleanCode: true,
  
  // Pluralization
  pluralSeparator: '_',
  contextSeparator: '_',
  
  // Custom functions
  saveMissing: process.env.NODE_ENV === 'development',
  saveMissingTo: 'current',
  
  // RTL support for Arabic
  rtl: ['ar'],
  
  // Custom formatting
  returnObjects: true,
  returnEmptyString: false,
  returnNull: false,
  
  // Performance optimizations
  partialBundledLanguages: true,
  
  // Custom resources for dynamic content
  resources: {},
  
  // Keyseparator
  keySeparator: '.',
  nsSeparator: ':',
  
  // Postprocessing
  postProcess: false,
  
  // Append namespace to missing key
  appendNamespaceToMissingKey: true,
  
  // Ignore JSON structure
  ignoreJSONStructure: false,
};
