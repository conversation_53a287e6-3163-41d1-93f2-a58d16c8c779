import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: ["class", '[data-theme="dark"]'],
  theme: {
    extend: {
      // RFC-004 UI/UX Design System Configuration
      fontFamily: {
        primary: ["var(--font-inter)", "Inter", "system-ui", "sans-serif"],
        secondary: ["var(--font-playfair)", "Playfair Display", "Georgia", "serif"],
        mono: ["var(--font-jetbrains)", "JetBrains Mono", "Fira Code", "monospace"],
      },
      colors: {
        // Natural Stone Theme Colors
        primary: {
          stone: "var(--primary-stone)",
          dark: "var(--primary-dark)",
          light: "var(--primary-light)",
        },
        secondary: {
          marble: "var(--secondary-marble)",
          granite: "var(--secondary-granite)",
          travertine: "var(--secondary-travertine)",
        },
        // System Colors
        success: "var(--success)",
        warning: "var(--warning)",
        error: "var(--error)",
        info: "var(--info)",
        // Background and Text
        bg: {
          primary: "var(--bg-primary)",
          secondary: "var(--bg-secondary)",
        },
        text: {
          primary: "var(--text-primary)",
          secondary: "var(--text-secondary)",
        },
        border: {
          primary: "var(--border-primary)",
        },
        // Legacy support
        background: "var(--bg-primary)",
        foreground: "var(--text-primary)",
      },
      spacing: {
        // RFC-004 Spacing Scale
        "0": "var(--space-0)",
        "1": "var(--space-1)",
        "2": "var(--space-2)",
        "3": "var(--space-3)",
        "4": "var(--space-4)",
        "5": "var(--space-5)",
        "6": "var(--space-6)",
        "8": "var(--space-8)",
        "10": "var(--space-10)",
        "12": "var(--space-12)",
        "16": "var(--space-16)",
        "20": "var(--space-20)",
        "24": "var(--space-24)",
        "32": "var(--space-32)",
      },
      borderRadius: {
        // RFC-004 Border Radius Scale
        "none": "var(--radius-none)",
        "sm": "var(--radius-sm)",
        "DEFAULT": "var(--radius)",
        "md": "var(--radius-md)",
        "lg": "var(--radius-lg)",
        "xl": "var(--radius-xl)",
        "2xl": "var(--radius-2xl)",
        "3xl": "var(--radius-3xl)",
        "full": "var(--radius-full)",
      },
      boxShadow: {
        // RFC-004 Shadow Scale
        "sm": "var(--shadow-sm)",
        "DEFAULT": "var(--shadow)",
        "md": "var(--shadow-md)",
        "lg": "var(--shadow-lg)",
        "xl": "var(--shadow-xl)",
        "2xl": "var(--shadow-2xl)",
      },
      fontSize: {
        // RFC-004 Typography Scale
        "xs": "var(--text-xs)",
        "sm": "var(--text-sm)",
        "base": "var(--text-base)",
        "lg": "var(--text-lg)",
        "xl": "var(--text-xl)",
        "2xl": "var(--text-2xl)",
        "3xl": "var(--text-3xl)",
        "4xl": "var(--text-4xl)",
        "5xl": "var(--text-5xl)",
        "6xl": "var(--text-6xl)",
      },
      fontWeight: {
        // RFC-004 Font Weights
        "thin": "var(--font-thin)",
        "light": "var(--font-light)",
        "normal": "var(--font-normal)",
        "medium": "var(--font-medium)",
        "semibold": "var(--font-semibold)",
        "bold": "var(--font-bold)",
        "extrabold": "var(--font-extrabold)",
        "black": "var(--font-black)",
      },
      transitionProperty: {
        // RFC-004 Transitions
        "base": "var(--transition-base)",
        "colors": "var(--transition-colors)",
        "transform": "var(--transition-transform)",
        "opacity": "var(--transition-opacity)",
      },
      animation: {
        // Custom animations for stone textures
        "shimmer": "shimmer 3s ease-in-out infinite",
        "blink": "blink 1s infinite",
      },
      keyframes: {
        shimmer: {
          "0%, 100%": { opacity: "0.5" },
          "50%": { opacity: "1" },
        },
        blink: {
          "0%, 50%": { opacity: "0.5" },
          "51%, 100%": { opacity: "0" },
        },
      },
    },
  },
  plugins: [
    function({ addUtilities }: any) {
      addUtilities({
        '.scrollbar-hide': {
          /* IE and Edge */
          '-ms-overflow-style': 'none',
          /* Firefox */
          'scrollbar-width': 'none',
          /* Safari and Chrome */
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        }
      })
    }
  ],
};

export default config;
