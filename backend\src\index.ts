import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import session from 'express-session';
import rateLimit from 'express-rate-limit';
import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { Server } from 'socket.io';
import { env } from './config/environment';
import logger, { logInfo, logError, logWarn, requestLogger } from './utils/logger';
import {
  securityHeaders,
  requestId,
  sqlInjectionProtection,
  xssProtection,
  requestSizeLimiter,
  apiLimiter,
  authLimiter
} from './middleware/security';
import { apiCSRFProtection, getCSRFToken } from './middleware/csrfProtection';
import { createSessionConfig, sessionManager } from './config/session';
import { securityMonitoring, cleanupThreatData } from './middleware/securityMonitoring';
import {
  performanceMonitor,
  metricsCollector,
  requestSizeMonitor,
  responseSizeMonitor,
  healthCheckWithMetrics
} from './middleware/performance';

// Import API routes
import authRoutes from './routes/api/auth';
import adminAuthRoutes from './routes/api/admin-auth';
import productsRoutes from './routes/api/products';
import ordersRoutes from './routes/api/orders';
import usersRoutes from './routes/api/users';
import adminRoutes from './routes/api/admin';
import biddingRoutes from './routes/api/bidding';
import testAdminRoutes from './routes/test-admin';
import quotesRoutes from './routes/api/quotes';
import systemRoutes from './routes/api/system';
import samplesRoutes from './routes/api/samples';
import paymentsRoutes from './routes/api/payments';
import commissionRoutes from './routes/api/commission';

import uploadRoutes from './routes/api/upload';
// import escrowRoutes from './routes/api/escrow'; // Temporarily disabled
import emailRoutes from './routes/api/email';
import { NotificationService } from './services/NotificationService';
import { NotificationController } from './controllers/NotificationController';
import notificationRoutes, { initializeNotificationRoutes } from './routes/api/notifications';
import whatsappRoutes from './routes/api/whatsapp';
import aiMarketingRoutes from './routes/api/ai-marketing';
import PaymentReminderService from './services/payment-reminder-service';
// import chatbotRoutes from './modules/ai/routes/chatbotRoutes';
import threeDRoutes from './modules/3d/routes';
import adminPaymentRoutes from './modules/admin/payment.routes';

// Load environment variables
dotenv.config();

// Validate environment variables (will throw if invalid)
console.log('🔧 Validating environment variables...');
const NODE_ENV = env.NODE_ENV;
const PORT = env.PORT;

const app = express();
const server = createServer(app);

// Initialize Notification Service
const notificationService = new NotificationService(server);
const notificationController = new NotificationController(notificationService);

// Initialize notification routes with controller
initializeNotificationRoutes(notificationController);

// Production-ready CORS configuration
const allowedOrigins = NODE_ENV === 'production'
  ? (process.env.CORS_ORIGIN?.split(',') || [])
  : [
      "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:3002"
    ];

// Add frontend URL if specified
if (process.env.FRONTEND_URL && !allowedOrigins.includes(process.env.FRONTEND_URL)) {
  allowedOrigins.push(process.env.FRONTEND_URL);
}

// Add admin URL if specified
if (process.env.ADMIN_URL && !allowedOrigins.includes(process.env.ADMIN_URL)) {
  allowedOrigins.push(process.env.ADMIN_URL);
}

const io = new Server(server, {
  cors: {
    origin: allowedOrigins,
    methods: ["GET", "POST"]
  }
});

const prisma = new PrismaClient();

// Security middleware stack
app.use(requestId);
app.use(securityHeaders);
app.use(helmet({
  contentSecurityPolicy: NODE_ENV === 'production' ? {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", ...allowedOrigins]
    }
  } : false,
  crossOriginEmbedderPolicy: NODE_ENV === 'production'
}));

app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: process.env.CORS_CREDENTIALS === 'true',
  maxAge: parseInt(process.env.CORS_MAX_AGE || '86400')
}));

app.use(compression());

// Serve static files from uploads directory
app.use('/uploads', express.static('uploads'));

// Use custom request logger in production, morgan in development
if (NODE_ENV === 'production') {
  app.use(requestLogger);
} else {
  app.use(morgan('dev'));
}

// Request parsing with security
app.use(requestSizeLimiter(NODE_ENV === 'production' ? 5 * 1024 * 1024 : 10 * 1024 * 1024)); // 5MB prod, 10MB dev
app.use(express.json({
  limit: NODE_ENV === 'production' ? '5mb' : '10mb'
}));
app.use(express.urlencoded({
  extended: true,
  limit: NODE_ENV === 'production' ? '5mb' : '10mb'
}));

// Cookie parser for CSRF protection
app.use(cookieParser());

// Initialize session management
async function initializeSession() {
  try {
    const sessionConfig = await createSessionConfig();
    app.use(session(sessionConfig));
    console.log('✅ Session management initialized');
  } catch (error) {
    console.error('❌ Session initialization failed:', error);
    // Continue without sessions
  }
}

// Security monitoring (should be early in middleware chain)
app.use(securityMonitoring);

// Security protection middleware
app.use(sqlInjectionProtection);
app.use(xssProtection);

// CSRF protection for API routes (temporarily disabled for testing)
// app.use('/api', apiCSRFProtection);

// Performance monitoring middleware
app.use(performanceMonitor);
app.use(metricsCollector);
app.use(requestSizeMonitor);
app.use(responseSizeMonitor);

// Production-ready rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes default
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000') / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks in production
    return req.path === '/health' || req.path === '/api/health';
  }
});

// Apply rate limiting in production
if (NODE_ENV === 'production') {
  app.use('/api/', limiter);
  app.use('/api/', apiLimiter);
  app.use('/api/auth/', authLimiter);
}

// Enhanced health check endpoint with performance metrics
app.get('/health', async (req, res) => {
  try {
    const healthData = await healthCheckWithMetrics();
    const statusCode = healthData.status === 'OK' ? 200 : 503;
    res.status(statusCode).json(healthData);
  } catch (error) {
    logError('Health check failed', error as Error);
    res.status(503).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: 'Health check failed'
    });
  }
});

// CSRF token endpoint
app.get('/api/csrf-token', getCSRFToken);

// Security monitoring endpoint (admin only)
app.get('/api/security/stats', (req, res) => {
  // TODO: Add admin authentication middleware
  const { getSecurityStats } = require('./middleware/securityMonitoring');
  const stats = getSecurityStats();
  res.json({
    success: true,
    data: stats
  });
});

// Metrics endpoint for Prometheus
app.get('/metrics', async (req, res) => {
  try {
    const { apiMetrics, memoryMonitor, getOptimizationSuggestions } = await import('./middleware/performance');
    const metrics = apiMetrics.getMetrics();
    const memory = memoryMonitor();
    const suggestions = getOptimizationSuggestions();

    // Prometheus format metrics
    const prometheusMetrics = `
# HELP http_requests_total Total number of HTTP requests
# TYPE http_requests_total counter
http_requests_total ${metrics.totalRequests}

# HELP http_requests_successful_total Total number of successful HTTP requests
# TYPE http_requests_successful_total counter
http_requests_successful_total ${metrics.successfulRequests}

# HELP http_requests_failed_total Total number of failed HTTP requests
# TYPE http_requests_failed_total counter
http_requests_failed_total ${metrics.failedRequests}

# HELP http_request_duration_seconds HTTP request duration in seconds
# TYPE http_request_duration_seconds histogram
http_request_duration_seconds_avg ${metrics.averageResponseTime / 1000}
http_request_duration_seconds_p95 ${metrics.p95ResponseTime / 1000}
http_request_duration_seconds_p99 ${metrics.p99ResponseTime / 1000}

# HELP nodejs_memory_usage_bytes Node.js memory usage in bytes
# TYPE nodejs_memory_usage_bytes gauge
nodejs_memory_usage_bytes{type="heap_used"} ${memory.heapUsed * 1024 * 1024}
nodejs_memory_usage_bytes{type="heap_total"} ${memory.heapTotal * 1024 * 1024}
nodejs_memory_usage_bytes{type="rss"} ${memory.rss * 1024 * 1024}
nodejs_memory_usage_bytes{type="external"} ${memory.external * 1024 * 1024}

# HELP nodejs_uptime_seconds Node.js uptime in seconds
# TYPE nodejs_uptime_seconds gauge
nodejs_uptime_seconds ${process.uptime()}
    `.trim();

    res.set('Content-Type', 'text/plain');
    res.send(prometheusMetrics);
  } catch (error) {
    logError('Metrics endpoint failed', error as Error);
    res.status(500).json({ error: 'Metrics collection failed' });
  }
});

// API Routes
app.get('/api', (req, res) => {
  res.json({
    message: 'Natural Stone Marketplace API - RFC Implementation',
    version: '2.0.0',
    features: [
      'Anonymous Bidding System (RFC-301)',
      'Admin Dashboard (RFC-501)',
      // 'AI Chatbot System (RFC-601)',
      // '3D Product Viewer (RFC-701)',
      'Real-time Notifications',
      'Escrow Payment System',
      'Commission Calculation'
    ],
    documentation: '/api/docs'
  });
});

// Mount API routes
console.log('🔄 Mounting routes...');
app.use('/api/auth', authRoutes);
console.log('✅ Auth routes mounted');
app.use('/api/admin/auth', adminAuthRoutes);
console.log('✅ Admin Auth routes mounted');
app.use('/api/products', productsRoutes);
console.log('✅ Products routes mounted');
app.use('/api/orders', ordersRoutes);
console.log('✅ Orders routes mounted');
app.use('/api/users', usersRoutes);
console.log('✅ Users routes mounted');
app.use('/api/quotes', quotesRoutes);
console.log('✅ Quotes routes mounted');
app.use('/api/admin', adminRoutes);
console.log('✅ Admin routes mounted');
app.use('/api/admin', adminPaymentRoutes);
console.log('✅ Admin payment routes mounted');
app.use('/api/bidding', biddingRoutes);
console.log('✅ Bidding routes mounted');
app.use('/api/system', systemRoutes);
console.log('✅ System routes mounted');
app.use('/api/samples', samplesRoutes);
console.log('✅ Samples routes mounted');
app.use('/api/payments', paymentsRoutes);
console.log('✅ Payments routes mounted');
app.use('/api/commission', commissionRoutes);
console.log('✅ Commission routes mounted');
app.use('/api/notifications', notificationRoutes);
console.log('✅ Notifications routes mounted');
app.use('/api/upload', uploadRoutes);
console.log('✅ Upload routes mounted');
// app.use('/api/escrow', escrowRoutes); // Temporarily disabled
// console.log('✅ Escrow routes mounted');
app.use('/api/email', emailRoutes);
console.log('✅ Email routes mounted');
app.use('/api/whatsapp', whatsappRoutes);
console.log('✅ WhatsApp routes mounted');

app.use('/api/ai-marketing', aiMarketingRoutes);
console.log('✅ AI Marketing routes mounted');
// app.use('/api/chatbot', chatbotRoutes);
app.use('/api/3d', threeDRoutes);
console.log('✅ 3D routes mounted');

// Notification service is already initialized above

// WebSocket is handled by NotificationService

// Enhanced error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  // Log error details
  const errorDetails = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    error: {
      name: err.name,
      message: err.message,
      stack: NODE_ENV === 'development' ? err.stack : undefined
    }
  };

  // Log error using winston logger
  logError('Application Error', err, {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Determine error status code
  const statusCode = err.statusCode || err.status || 500;

  // Send appropriate response
  const response: any = {
    success: false,
    error: NODE_ENV === 'production' ? 'Internal server error' : err.message,
    timestamp: new Date().toISOString()
  };

  // Add request ID for tracking in production
  if (NODE_ENV === 'production') {
    response.requestId = req.headers['x-request-id'] || 'unknown';
  }

  // Add stack trace in development
  if (NODE_ENV === 'development') {
    response.stack = err.stack;
  }

  res.status(statusCode).json(response);
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    availableRoutes: [
      'GET /health',
      'GET /api',
      'POST /api/bidding/requests',
      'GET /api/admin/dashboard/overview',
      'GET /api/quotes/requests',
      'POST /api/quotes/requests',
      'GET /api/quotes',
      'POST /api/quotes/requests/:requestId/quotes',
      'POST /api/samples/request',
      'GET /api/samples/customer/:customerId',
      'GET /api/samples/admin/all',
      'PUT /api/samples/producer/:sampleRequestId/approve',
      'PUT /api/samples/producer/:sampleRequestId/status'
      // 'POST /api/chatbot/start',
      // 'POST /api/chatbot/message'
    ]
  });
});

// Graceful shutdown
process.on('SIGINT', async () => {
  logInfo('Received SIGINT, shutting down gracefully...');
  await prisma.$disconnect();
  io.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logInfo('Received SIGTERM, shutting down gracefully...');
  await prisma.$disconnect();
  io.close();
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('🚨 Uncaught Exception:', error);
  console.error('Stack trace:', error.stack);
  logError('Uncaught Exception', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('🚨 Unhandled Rejection at:', promise);
  console.error('Reason:', reason);
  logError('Unhandled Rejection', new Error(String(reason)), { promise });
  process.exit(1);
});

// Start server
server.listen(PORT, async () => {
  // Initialize session management
  await initializeSession();

  // Start periodic security cleanup (every hour)
  setInterval(() => {
    cleanupThreatData();
    // sessionManager.cleanupExpiredSessions?.(); // Disabled for now
  }, 60 * 60 * 1000);
  const startupInfo = {
    service: 'Natural Stone Marketplace Backend',
    port: PORT,
    environment: NODE_ENV,
    healthCheck: `http://localhost:${PORT}/health`,
    features: [
      'RFC-301: Anonymous Bidding System',
      'RFC-501: Admin Dashboard',
      'RFC-003: Database Design',
      'Real-time Notifications',
      'Escrow Payment System',
      'Commission Calculation',
      'Payment Reminder Service'
    ]
  };

  if (NODE_ENV === 'production') {
    logInfo('🚀 Server Started', startupInfo);
  } else {
    console.log('🚀 Natural Stone Marketplace Backend Started');
    console.log(`📡 Server running on port ${PORT}`);
    console.log(`🔗 WebSocket server active`);
    console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
    console.log(`🌍 Environment: ${NODE_ENV}`);
    console.log('');
    console.log('🎯 Implemented RFC Features:');
    startupInfo.features.forEach(feature => {
      console.log(`  ✅ ${feature}`);
    });
  }

  // Start payment reminder service
  const paymentReminderService = PaymentReminderService.getInstance();
  paymentReminderService.start();

  if (NODE_ENV === 'development') {
    console.log('  ✅ Payment Reminder Service');
  }
});

export { app };
