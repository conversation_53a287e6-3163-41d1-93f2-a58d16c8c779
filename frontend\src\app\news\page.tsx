'use client';


import { Navigation } from "@/components/ui/navigation";
import { Button } from "@/components/ui/button";
import Link from 'next/link';
import { useAuth } from '@/contexts/auth-context';

// Metadata moved to layout.tsx since this is a client component

const navigationLinks = [
  { name: "<PERSON>", href: "/" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/products" },
  { name: "3D Sanal Showroom", href: "/3d-showroom" },
  { name: "<PERSON><PERSON><PERSON>", href: "/news", active: true },
  { name: "Hakkımızda", href: "/about" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/contact" }
];

// Gerçek haberler API'den gelecek - şimdilik boş array
const newsArticles: any[] = [];

const categories = ["Tümü", "Sektör", "Teknoloji", "Pazar", "Üretim", "Etkinlik"];

export default function NewsPage() {
  const { user, isAuthenticated, logout, showLoginModal, showCustomerRegisterModal } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50">
      <Navigation
        brand={{
          name: "Türkiye Doğal Taş Pazarı",
          href: "/"
        }}
        links={navigationLinks}
        actions={
          <div className="flex items-center gap-2">
            {!isAuthenticated ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={showLoginModal}
                >
                  Giriş Yap
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={showCustomerRegisterModal}
                >
                  Kayıt Ol
                </Button>
              </>
            ) : (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 max-w-[200px] truncate">
                  Hoş geldiniz, {user?.company || user?.name}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={logout}
                >
                  Çıkış Yap
                </Button>
              </div>
            )}
          </div>
        }
      />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-stone-900 mb-4">
            Sektör Haberleri
          </h1>
          <p className="text-xl text-stone-600 max-w-3xl mx-auto">
            Doğal taş sektöründen son haberler, trend analizleri ve pazar gelişmelerini takip edin.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-2 mb-8 justify-center">
          {categories.map((category) => (
            <Button
              key={category}
              variant={category === "Tümü" ? "primary" : "outline"}
              size="sm"
              className="mb-2"
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Featured Article */}
        {newsArticles.length > 0 ? (
          <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-12">
            <div className="md:flex">
              <div className="md:w-1/2">
                <div className="h-64 md:h-full bg-gradient-to-br from-stone-200 to-stone-300 flex items-center justify-center">
                  <div className="text-6xl">📰</div>
                </div>
              </div>
              <div className="md:w-1/2 p-8">
                <div className="flex items-center gap-2 mb-3">
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                    {newsArticles[0].category}
                  </span>
                  <span className="text-sm text-gray-500">{newsArticles[0].readTime} okuma</span>
                </div>
                <h2 className="text-2xl font-bold text-stone-900 mb-3">
                  {newsArticles[0].title}
                </h2>
                <p className="text-stone-600 mb-4">
                  {newsArticles[0].excerpt}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    {new Date(newsArticles[0].date).toLocaleDateString('tr-TR')}
                  </span>
                  <Button variant="primary" size="sm">
                    Devamını Oku
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-12">
            <div className="p-8 text-center">
              <div className="text-6xl mb-4">📰</div>
              <h2 className="text-2xl font-bold text-stone-900 mb-3">
                Haberler Yakında
              </h2>
              <p className="text-stone-600">
                Doğal taş sektöründen haberler yakında burada yer alacak.
              </p>
            </div>
          </div>
        )}

        {/* News Grid */}
        {newsArticles.length > 1 ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {newsArticles.slice(1).map((article) => (
              <article key={article.id} className="bg-white rounded-lg shadow-sm border border-stone-200 overflow-hidden hover:shadow-md transition-shadow">
                <div className="h-48 bg-gradient-to-br from-stone-200 to-stone-300 flex items-center justify-center">
                  <div className="text-4xl">📄</div>
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                      {article.category}
                    </span>
                    <span className="text-sm text-gray-500">{article.readTime} okuma</span>
                  </div>
                  <h3 className="text-lg font-semibold text-stone-900 mb-2 line-clamp-2">
                    {article.title}
                  </h3>
                  <p className="text-stone-600 text-sm mb-4 line-clamp-3">
                    {article.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {new Date(article.date).toLocaleDateString('tr-TR')}
                    </span>
                    <Button variant="outline" size="sm">
                      Oku
                    </Button>
                  </div>
                </div>
              </article>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📄</div>
            <h3 className="text-xl font-semibold text-stone-700 mb-2">
              Henüz Haber Yok
            </h3>
            <p className="text-stone-600">
              Yakında sektörden haberler burada yer alacak.
            </p>
          </div>
        )}

        {/* Load More */}
        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            Daha Fazla Haber Yükle
          </Button>
        </div>
      </main>
    </div>
  );
}
