'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  User,
  Building,
  Bell,
  Shield,
  CreditCard,
  Globe,
  Mail,
  Phone,
  MapPin,
  Save,
  Edit,
  Key,
  Trash2,
  Plus,
  Upload,
  CheckCircle,
  AlertTriangle,
  Building2
} from 'lucide-react'

export default function ProducerSettings() {
  const { producer, logout } = useProducerAuth()
  const [isEditing, setIsEditing] = React.useState(false)
  const [formData, setFormData] = React.useState({
    name: producer?.name || '',
    email: producer?.email || '',
    phone: producer?.phone || '',
    companyName: producer?.companyName || '',
    address: producer?.address || '',
    website: '',
    notifications: {
      email: true,
      sms: false,
      push: true
    },
    privacy: {
      profileVisible: true,
      contactVisible: true
    }
  })

  // Ocak yönetimi state'leri
  const [quarries, setQuarries] = React.useState([
    // Mock data - gerçek uygulamada API'den gelecek
    {
      id: '1',
      name: 'Ana Ocak',
      location: 'Afyon Merkez',
      address: 'Afyon Merkez, Ocak Mevkii',
      googleMapsLink: 'https://maps.google.com/?q=38.7569,30.5387',
      ownershipType: 'owner',
      documents: [],
      status: 'pending',
      isActive: true
    }
  ])
  const [showAddQuarry, setShowAddQuarry] = React.useState(false)
  const [newQuarry, setNewQuarry] = React.useState({
    name: '',
    location: '',
    address: '',
    googleMapsLink: '',
    ownershipType: 'owner',
    documents: []
  })

  const handleSave = () => {
    console.log('Saving settings:', formData)
    // Here you would save the settings to your API
    setIsEditing(false)
  }

  const handlePasswordChange = () => {
    console.log('Opening password change modal')
    // Here you would open a password change modal
  }

  const handleAddQuarry = () => {
    if (newQuarry.name && newQuarry.location && newQuarry.address) {
      const quarry = {
        id: Date.now().toString(),
        ...newQuarry,
        status: 'pending',
        isActive: true
      }
      setQuarries(prev => [...prev, quarry])

      // Producer context'ini güncelle - ocak eklendi
      updateProducerQuarryStatus(true)

      setNewQuarry({
        name: '',
        location: '',
        address: '',
        googleMapsLink: '',
        ownershipType: 'owner',
        documents: []
      })
      setShowAddQuarry(false)
      console.log('Ocak eklendi, admin onayı bekleniyor:', quarry)

      // Başarı mesajı göster
      alert('Ocak başarıyla eklendi! Admin onayı sonrası blok satışı yapabileceksiniz.')
    }
  }

  const handleRemoveQuarry = (quarryId: string) => {
    setQuarries(prev => {
      const updatedQuarries = prev.filter(q => q.id !== quarryId)

      // Eğer hiç ocak kalmadıysa hasQuarry'yi false yap
      if (updatedQuarries.length === 0) {
        updateProducerQuarryStatus(false)
      }

      return updatedQuarries
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-4 h-4" />
      case 'pending':
        return <AlertTriangle className="w-4 h-4" />
      case 'rejected':
        return <AlertTriangle className="w-4 h-4" />
      default:
        return <AlertTriangle className="w-4 h-4" />
    }
  }

  const updateProducerQuarryStatus = (hasQuarry: boolean) => {
    try {
      // LocalStorage'daki producer bilgisini güncelle
      const storedProducer = localStorage.getItem('producer')
      if (storedProducer) {
        const producer = JSON.parse(storedProducer)
        producer.hasQuarry = hasQuarry
        localStorage.setItem('producer', JSON.stringify(producer))

        // Cookie'yi de güncelle
        document.cookie = `producer=${encodeURIComponent(JSON.stringify(producer))}; path=/; max-age=${7 * 24 * 60 * 60}`

        console.log('Producer quarry status updated:', hasQuarry)
      }
    } catch (error) {
      console.error('Error updating producer quarry status:', error)
    }
  }

  const handleDeleteAccount = () => {
    if (confirm('Hesabınızı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.')) {
      console.log('Deleting account')
      // Here you would delete the account
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Hesap Ayarları</h1>
          <p className="text-gray-600">
            Hesap bilgilerinizi ve tercihlerinizi yönetin
          </p>
        </div>
        <Button
          onClick={() => setIsEditing(!isEditing)}
          variant={isEditing ? "outline" : "default"}
          className={isEditing ? "" : "bg-amber-600 hover:bg-amber-700"}
        >
          <Edit className="w-4 h-4 mr-2" />
          {isEditing ? 'İptal' : 'Düzenle'}
        </Button>
      </div>

      {/* Profile Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Profil Bilgileri
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ad Soyad
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                E-posta
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Telefon
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Website
              </label>
              <input
                type="url"
                value={formData.website}
                onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                disabled={!isEditing}
                placeholder="https://www.sirketiniz.com"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>
          </div>

          {isEditing && (
            <div className="flex gap-3 pt-4">
              <Button onClick={handleSave} className="bg-amber-600 hover:bg-amber-700">
                <Save className="w-4 h-4 mr-2" />
                Kaydet
              </Button>
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                İptal
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Company Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="w-5 h-5" />
            Şirket Bilgileri
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Şirket Adı
              </label>
              <input
                type="text"
                value={formData.companyName}
                onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Adres
              </label>
              <textarea
                value={formData.address}
                onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                disabled={!isEditing}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="w-5 h-5" />
            Bildirim Ayarları
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.notifications.email}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  notifications: { ...prev.notifications, email: e.target.checked }
                }))}
                disabled={!isEditing}
                className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
              />
              <div>
                <div className="font-medium">E-posta Bildirimleri</div>
                <div className="text-sm text-gray-500">Yeni siparişler ve önemli güncellemeler</div>
              </div>
            </label>

            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.notifications.sms}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  notifications: { ...prev.notifications, sms: e.target.checked }
                }))}
                disabled={!isEditing}
                className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
              />
              <div>
                <div className="font-medium">SMS Bildirimleri</div>
                <div className="text-sm text-gray-500">Acil durumlar ve önemli güncellemeler</div>
              </div>
            </label>

            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.notifications.push}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  notifications: { ...prev.notifications, push: e.target.checked }
                }))}
                disabled={!isEditing}
                className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
              />
              <div>
                <div className="font-medium">Push Bildirimleri</div>
                <div className="text-sm text-gray-500">Tarayıcı bildirimleri</div>
              </div>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Quarry Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5" />
            Ocak Yönetimi
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">
                Blok satışı yapabilmek için ocak sahipliğinizi belgelendirin
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Mevcut durum: {producer?.hasQuarry ? '✅ Blok satışı aktif' : '❌ Blok satışı kapalı'}
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => setShowAddQuarry(true)}
                className="bg-orange-600 hover:bg-orange-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Ocak Ekle
              </Button>
              {/* Test butonu - geliştirme amaçlı */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const currentStatus = producer?.hasQuarry || false
                  updateProducerQuarryStatus(!currentStatus)
                  window.location.reload() // Sayfayı yenile
                }}
                className="text-xs"
              >
                Test: {producer?.hasQuarry ? 'Kapat' : 'Aç'}
              </Button>
            </div>
          </div>

          {/* Existing Quarries */}
          <div className="space-y-3">
            {quarries.map((quarry) => (
              <div key={quarry.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-medium text-gray-900">{quarry.name}</h4>
                      <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${getStatusColor(quarry.status)}`}>
                        {getStatusIcon(quarry.status)}
                        {quarry.status === 'approved' ? 'Onaylandı' :
                         quarry.status === 'pending' ? 'Onay Bekliyor' :
                         quarry.status === 'rejected' ? 'Reddedildi' : 'Bilinmeyen'}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-1">
                      <MapPin className="w-3 h-3 inline mr-1" />
                      {quarry.address}
                    </p>
                    <p className="text-xs text-gray-500">
                      Sahiplik: {quarry.ownershipType === 'owner' ? 'Sahibi' :
                                quarry.ownershipType === 'partner' ? 'Ortak' : 'Kiracı'}
                    </p>
                    {quarry.googleMapsLink && (
                      <a
                        href={quarry.googleMapsLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 hover:underline"
                      >
                        Haritada Görüntüle
                      </a>
                    )}
                  </div>
                  <div className="flex gap-2">
                    {quarry.status === 'approved' && (
                      <div className="text-green-600">
                        <CheckCircle className="w-5 h-5" />
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveQuarry(quarry.id)}
                      className="text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {quarries.length === 0 && (
            <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
              <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz ocak eklenmemiş</h3>
              <p className="text-gray-600 mb-4">
                Blok satışı yapabilmek için önce bir ocak eklemeniz gerekiyor.
              </p>
              <Button
                onClick={() => setShowAddQuarry(true)}
                className="bg-orange-600 hover:bg-orange-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                İlk Ocağınızı Ekleyin
              </Button>
            </div>
          )}

          {/* Add Quarry Form */}
          {showAddQuarry && (
            <div className="border border-orange-200 rounded-lg p-4 bg-orange-50">
              <h4 className="font-medium text-gray-900 mb-4">Yeni Ocak Ekle</h4>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ocak Adı *
                    </label>
                    <input
                      type="text"
                      value={newQuarry.name}
                      onChange={(e) => setNewQuarry(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Örn: Ana Ocak"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Şehir/Bölge *
                    </label>
                    <input
                      type="text"
                      value={newQuarry.location}
                      onChange={(e) => setNewQuarry(prev => ({ ...prev, location: e.target.value }))}
                      placeholder="Örn: Afyon Merkez"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Detaylı Adres *
                  </label>
                  <textarea
                    value={newQuarry.address}
                    onChange={(e) => setNewQuarry(prev => ({ ...prev, address: e.target.value }))}
                    placeholder="Ocağın detaylı adresi..."
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Google Maps Linki
                  </label>
                  <input
                    type="url"
                    value={newQuarry.googleMapsLink}
                    onChange={(e) => setNewQuarry(prev => ({ ...prev, googleMapsLink: e.target.value }))}
                    placeholder="https://maps.google.com/?q=..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Sahiplik Durumu *
                  </label>
                  <select
                    value={newQuarry.ownershipType}
                    onChange={(e) => setNewQuarry(prev => ({ ...prev, ownershipType: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  >
                    <option value="owner">Sahibi</option>
                    <option value="partner">Ortak</option>
                    <option value="renter">Kiracı</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sahiplik Belgeleri
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600 mb-2">
                      Ocak ruhsatı, tapu senedi veya kira sözleşmesi yükleyin
                    </p>
                    <Button variant="outline" size="sm">
                      Dosya Seç
                    </Button>
                  </div>
                </div>

                <div className="flex gap-3 pt-2">
                  <Button
                    onClick={handleAddQuarry}
                    className="bg-orange-600 hover:bg-orange-700"
                  >
                    Ocak Ekle
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowAddQuarry(false)}
                  >
                    İptal
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Güvenlik
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <div className="font-medium">Şifre</div>
              <div className="text-sm text-gray-500">Son değiştirilme: 30 gün önce</div>
            </div>
            <Button variant="outline" onClick={handlePasswordChange}>
              <Key className="w-4 h-4 mr-2" />
              Şifre Değiştir
            </Button>
          </div>

          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <div className="font-medium">İki Faktörlü Doğrulama</div>
              <div className="text-sm text-gray-500">Hesabınızı daha güvenli hale getirin</div>
            </div>
            <Button variant="outline">
              Etkinleştir
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <Trash2 className="w-5 h-5" />
            Tehlikeli Bölge
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
            <div>
              <div className="font-medium text-red-800">Hesabı Sil</div>
              <div className="text-sm text-red-600">Bu işlem geri alınamaz</div>
            </div>
            <Button 
              variant="outline" 
              onClick={handleDeleteAccount}
              className="border-red-300 text-red-600 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Hesabı Sil
            </Button>
          </div>

          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <div className="font-medium">Çıkış Yap</div>
              <div className="text-sm text-gray-500">Tüm cihazlardan çıkış yap</div>
            </div>
            <Button variant="outline" onClick={logout}>
              Çıkış Yap
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
