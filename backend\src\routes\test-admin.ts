// Test Admin Routes
import { Router, Request, Response } from 'express';
import systemRoutes from './api/system';

const router = Router();

// Mount system routes
router.use('/system', systemRoutes);

// Simple test admin dashboard endpoint
router.get('/dashboard/overview', (req: Request, res: Response) => {
  const mockData = {
    kpiCards: [
      {
        id: 'total_users',
        title: 'Toplam Kullanıcı',
        value: 1250,
        previousValue: 1180,
        changePercentage: 5.9,
        trend: 'up',
        format: 'number',
        color: 'blue',
        icon: 'Users',
        drillDownUrl: '/admin/users'
      },
      {
        id: 'active_users',
        title: 'Akt<PERSON> (30g)',
        value: 890,
        format: 'number',
        color: 'green',
        icon: 'UserCheck',
        trend: 'stable'
      },
      {
        id: 'total_orders',
        title: 'Toplam Sipariş',
        value: 456,
        previousValue: 398,
        changePercentage: 14.6,
        trend: 'up',
        format: 'number',
        color: 'blue',
        icon: 'ShoppingCart',
        drillDownUrl: '/admin/orders'
      },
      {
        id: 'total_revenue',
        title: 'Toplam Gelir',
        value: 2450000,
        previousValue: 2180000,
        changePercentage: 12.4,
        trend: 'up',
        format: 'currency',
        color: 'green',
        icon: 'DollarSign',
        drillDownUrl: '/admin/payments'
      },
      {
        id: 'pending_approvals',
        title: 'Bekleyen Onaylar',
        value: 8,
        format: 'number',
        color: 'yellow',
        icon: 'Clock',
        trend: 'stable',
        drillDownUrl: '/admin/approvals'
      },
      {
        id: 'system_uptime',
        title: 'Sistem Çalışma Süresi',
        value: '99.8%',
        format: 'percentage',
        color: 'green',
        icon: 'Activity',
        trend: 'stable'
      }
    ],
    realtimeMetrics: [
      {
        id: 'active_users',
        name: 'Aktif Kullanıcı',
        value: 67,
        unit: 'kullanıcı',
        timestamp: new Date(),
        trend: [45, 52, 48, 61, 55, 67, 67]
      },
      {
        id: 'response_time',
        name: 'Ortalama Yanıt Süresi',
        value: 150,
        unit: 'ms',
        timestamp: new Date(),
        trend: [120, 135, 142, 128, 156, 148, 150]
      },
      {
        id: 'error_rate',
        name: 'Hata Oranı',
        value: 0.1,
        unit: '%',
        timestamp: new Date(),
        trend: [0.2, 0.1, 0.3, 0.1, 0.2, 0.1, 0.1]
      },
      {
        id: 'db_connections',
        name: 'DB Bağlantıları',
        value: 5,
        unit: 'bağlantı',
        timestamp: new Date(),
        trend: [3, 4, 5, 6, 4, 5, 5]
      }
    ],
    alertsPanel: {
      criticalAlerts: [],
      warningAlerts: [
        {
          id: 'pending_approvals_high',
          type: 'warning',
          title: 'Yüksek Sayıda Bekleyen Onay',
          message: '8 üretici başvurusu onay bekliyor',
          timestamp: new Date(),
          acknowledged: false,
          actionUrl: '/admin/approvals'
        }
      ],
      infoAlerts: [
        {
          id: 'new_registrations',
          type: 'info',
          title: 'Yeni Kullanıcı Kayıtları',
          message: 'Son 24 saatte 12 yeni kullanıcı kaydoldu',
          timestamp: new Date(),
          acknowledged: false,
          actionUrl: '/admin/users'
        }
      ],
      totalCount: 2
    },
    quickActions: [
      {
        id: 'approve_producers',
        title: 'Üreticileri Onayla',
        description: 'Bekleyen üretici başvurularını incele ve onayla',
        icon: 'UserCheck',
        url: '/admin/approvals',
        badge: 8
      },
      {
        id: 'process_payments',
        title: 'Ödemeleri İşle',
        description: 'Bekleyen ödemeleri incele ve işle',
        icon: 'CreditCard',
        url: '/admin/payments',
        badge: 3
      },
      {
        id: 'resolve_disputes',
        title: 'Anlaşmazlıkları Çöz',
        description: 'Aktif müşteri anlaşmazlıklarını ele al',
        icon: 'AlertTriangle',
        url: '/admin/disputes',
        badge: 1
      },
      {
        id: 'system_maintenance',
        title: 'Sistem Bakımı',
        description: 'Sistem bakım görevlerini gerçekleştir',
        icon: 'Settings',
        url: '/admin/system',
        badge: 0
      },
      {
        id: 'generate_reports',
        title: 'Rapor Oluştur',
        description: 'İş ve finansal raporlar oluştur',
        icon: 'FileText',
        url: '/admin/reports'
      },
      {
        id: 'broadcast_message',
        title: 'Duyuru Gönder',
        description: 'Tüm kullanıcılara duyuru gönder',
        icon: 'MessageSquare',
        url: '/admin/communications'
      }
    ],
    systemHealth: {
      status: 'healthy',
      uptime: 99.8,
      responseTime: 150,
      errorRate: 0.1,
      activeUsers: 67,
      databaseStatus: 'connected',
      redisStatus: 'connected'
    }
  };

  res.json({
    success: true,
    data: mockData
  });
});

// Test users endpoint
router.get('/users', (req: Request, res: Response) => {
  const mockUsers = {
    users: [
      {
        id: '1',
        email: '<EMAIL>',
        userType: 'PRODUCER',
        status: 'ACTIVE',
        createdAt: '2024-01-15T10:30:00Z',
        lastLoginAt: '2024-01-20T14:22:00Z',
        profile: {
          companyName: 'Marmara Mermer A.Ş.',
          contactPerson: 'Ahmet Yılmaz',
          phone: '+90 ************',
          countryCode: 'TR'
        }
      },
      {
        id: '2',
        email: '<EMAIL>',
        userType: 'CUSTOMER',
        status: 'ACTIVE',
        createdAt: '2024-01-18T09:15:00Z',
        lastLoginAt: '2024-01-21T11:45:00Z',
        profile: {
          companyName: 'İnşaat Firması Ltd.',
          contactPerson: 'Mehmet Demir',
          phone: '+90 ************',
          countryCode: 'TR'
        }
      },
      {
        id: '3',
        email: '<EMAIL>',
        userType: 'PRODUCER',
        status: 'PENDING',
        createdAt: '2024-01-20T16:20:00Z',
        profile: {
          companyName: 'Anadolu Taş San.',
          contactPerson: 'Fatma Özkan',
          phone: '+90 ************',
          countryCode: 'TR'
        }
      }
    ],
    total: 3,
    page: 1,
    totalPages: 1
  };

  res.json({
    success: true,
    data: mockUsers
  });
});

// Test user details endpoint
router.get('/users/:userId', (req: Request, res: Response) => {
  const mockUserDetails = {
    id: req.params.userId,
    email: '<EMAIL>',
    userType: 'PRODUCER',
    status: 'ACTIVE',
    createdAt: '2024-01-15T10:30:00Z',
    lastLoginAt: '2024-01-20T14:22:00Z',
    profile: {
      companyName: 'Marmara Mermer A.Ş.',
      contactPerson: 'Ahmet Yılmaz',
      phone: '+90 ************',
      countryCode: 'TR'
    },
    activityHistory: [
      {
        id: '1',
        action: 'LOGIN',
        timestamp: '2024-01-21T14:22:00Z',
        details: { ipAddress: '*************' }
      },
      {
        id: '2',
        action: 'PRODUCT_CREATED',
        timestamp: '2024-01-20T10:15:00Z',
        details: { productId: 'prod_123' }
      }
    ],
    orderHistory: [
      {
        id: 'order_1',
        status: 'COMPLETED',
        totalAmount: 15000,
        createdAt: '2024-01-18T09:30:00Z',
        productName: 'Beyaz Mermer Blok'
      }
    ],
    paymentHistory: [
      {
        id: 'payment_1',
        amount: 15000,
        status: 'COMPLETED',
        method: 'BANK_TRANSFER',
        createdAt: '2024-01-18T10:00:00Z'
      }
    ],
    riskScore: 15
  };

  res.json({
    success: true,
    data: mockUserDetails
  });
});

export default router;
