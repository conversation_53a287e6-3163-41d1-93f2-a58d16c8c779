'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Clock,
  DollarSign,
  Calendar,
  User,
  Package,
  MessageSquare,
  AlertCircle,
  Play
} from 'lucide-react'
import { MessageModal } from '@/components/ui/message-modal'
import { OrderDetailsModal } from '@/components/ui/order-details-modal'

// Mock data for pending orders
const mockPendingOrders = [
  {
    id: 'ORD-003',
    customerName: 'DEF Yapı',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Granit Blok',
    quantity: 50,
    unit: 'ton',
    orderDate: '2025-06-15',
    deliveryDate: '2025-07-15',
    status: 'pending',
    totalValue: 40000,
    currency: 'USD',
    paymentStatus: 'pending',
    paidAmount: 0,
    specifications: {
      thickness: 'Blok',
      surface: 'Ham',
      packaging: '<PERSON>zel ambalaj',
      delivery: 'Fabrika teslim'
    },
    productionStages: [
      { stage: 'Sipariş Onayı', completed: false, date: null, notes: 'Ödeme bekleniyor' },
      { stage: 'Üretim Başlangıcı', completed: false, date: null, notes: '' },
      { stage: 'Kesim İşlemi', completed: false, date: null, notes: '' },
      { stage: 'Yüzey İşlemi', completed: false, date: null, notes: '' },
      { stage: 'Kalite Kontrol', completed: false, date: null, notes: '' },
      { stage: 'Ambalajlama', completed: false, date: null, notes: '' },
      { stage: 'Sevkiyat', completed: false, date: null, notes: '' }
    ],
    deliveryAddress: 'Afyon, Türkiye',
    notes: 'Anıt projesi için özel işlem gerekli'
  }
]

export default function PendingOrders() {
  const { producer } = useProducerAuth()
  const [selectedOrder, setSelectedOrder] = React.useState<any>(null)
  const [isMessageModalOpen, setIsMessageModalOpen] = React.useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = React.useState(false)

  const handleStartProduction = (orderId: string) => {
    console.log('Starting production for order:', orderId)
    // Here you would update the order status
  }

  const handleSendMessage = (order: any) => {
    setSelectedOrder(order)
    setIsMessageModalOpen(true)
  }

  const handleViewDetails = (order: any) => {
    setSelectedOrder(order)
    setIsDetailsModalOpen(true)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Bekleyen Siparişler</h1>
        <p className="text-gray-600">
          Ödeme bekleyen ve üretime başlanmamış siparişler
        </p>
      </div>

      {/* Pending Orders List */}
      <div className="space-y-4">
        {mockPendingOrders.map((order) => (
          <Card key={order.id} className="overflow-hidden border-yellow-200">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-yellow-600 bg-yellow-100">
                      <Clock className="w-4 h-4" />
                      Bekliyor
                    </div>
                    <span className="text-sm font-medium">#{order.id}</span>
                    <div className="px-2 py-1 rounded-full text-xs font-medium text-red-600 bg-red-100">
                      Ödeme Bekliyor
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <User className="w-4 h-4" />
                        Müşteri
                      </div>
                      <div className="font-medium">{order.customerName}</div>
                      <div className="text-sm text-gray-500">{order.customerEmail}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <Package className="w-4 h-4" />
                        Ürün
                      </div>
                      <div className="font-medium">{order.productName}</div>
                      <div className="text-sm text-gray-500">{order.quantity} {order.unit}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <Calendar className="w-4 h-4" />
                        Tarihler
                      </div>
                      <div className="font-medium">Sipariş: {order.orderDate}</div>
                      <div className="text-sm text-gray-500">Teslimat: {order.deliveryDate}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <DollarSign className="w-4 h-4" />
                        Tutar
                      </div>
                      <div className="font-medium">{order.totalValue.toLocaleString()} {order.currency}</div>
                      <div className="text-sm text-red-500">
                        Ödenmedi
                      </div>
                    </div>
                  </div>

                  {/* Warning Message */}
                  <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="w-4 h-4 text-yellow-600" />
                      <span className="text-sm font-medium text-yellow-800">Ödeme Bekleniyor</span>
                    </div>
                    <p className="text-sm text-yellow-700 mt-1">
                      Müşteri ödemeyi tamamladıktan sonra üretime başlayabilirsiniz.
                    </p>
                  </div>

                  {/* Order Details */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                    <div>
                      <span className="text-gray-500">Kalınlık:</span>
                      <span className="ml-2 font-medium">{order.specifications.thickness}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Yüzey:</span>
                      <span className="ml-2 font-medium">{order.specifications.surface}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Ambalaj:</span>
                      <span className="ml-2 font-medium">{order.specifications.packaging}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Teslimat:</span>
                      <span className="ml-2 font-medium">{order.specifications.delivery}</span>
                    </div>
                  </div>

                  {order.notes && (
                    <div className="mb-4">
                      <p className="text-sm text-gray-600 mb-1">Özel Notlar:</p>
                      <p className="text-sm">{order.notes}</p>
                    </div>
                  )}
                </div>

                <div className="flex flex-col gap-2 ml-4">
                  <Button
                    disabled
                    className="bg-gray-400 cursor-not-allowed"
                    title="Ödeme tamamlanmadan üretim başlatılamaz"
                  >
                    <Play className="w-4 h-4 mr-1" />
                    Üretime Başla
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSendMessage(order)}
                  >
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Müşteriyle İletişim
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetails(order)}
                  >
                    Detayları Gör
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {mockPendingOrders.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Bekleyen sipariş bulunamadı
            </h3>
            <p className="text-gray-600">
              Şu anda bekleyen siparişiniz bulunmuyor.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Message Modal */}
      <MessageModal
        isOpen={isMessageModalOpen}
        onClose={() => setIsMessageModalOpen(false)}
        recipientName={selectedOrder?.customerName || ''}
        recipientType="customer"
        orderId={selectedOrder?.id}
      />

      {/* Order Details Modal */}
      <OrderDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        order={selectedOrder}
      />
    </div>
  )
}
