/**
 * Viewer Service
 * Handles 3D viewer configuration and session management
 */

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

interface Annotation {
  id: string;
  position: { x: number; y: number; z: number };
  title: string;
  type: string;
  visible: boolean;
  description?: string;
}
import {
  ViewerConfiguration,
  ViewerSession,
  ViewerAnalytics,
  ViewerCapabilities,
  CameraState,
  LightingState,
  Annotation as ViewerAnnotation
} from '../types';

export class ViewerService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Get or create viewer configuration for product
   */
  async getViewerConfiguration(productId: string): Promise<ViewerConfiguration> {
    try {
      let config = await this.prisma.viewerConfiguration.findUnique({
        where: { productId },
        include: {
          environmentMap: true
        }
      });

      if (!config) {
        config = await this.createDefaultConfiguration(productId);
      }

      return this.mapPrismaConfiguration(config);
    } catch (error) {
      console.error('Error getting viewer configuration:', error);
      throw error;
    }
  }

  /**
   * Create default viewer configuration
   */
  private async createDefaultConfiguration(productId: string): Promise<any> {
    const defaultConfig = {
      productId,
      cameraPosition: { x: 5, y: 5, z: 5 },
      cameraTarget: { x: 0, y: 0, z: 0 },
      cameraFov: 75,
      ambientLightColor: '#ffffff',
      ambientLightIntensity: 0.4,
      directionalLightColor: '#ffffff',
      directionalLightIntensity: 1.0,
      directionalLightPosition: { x: 10, y: 10, z: 5 },
      backgroundType: 'color',
      backgroundColor: '#f0f0f0',
      enableOrbitControls: true,
      enableZoom: true,
      enablePan: true,
      enableRotate: true,
      autoRotate: false,
      autoRotateSpeed: 2.0,
      enableShadows: true,
      shadowMapSize: 1024,
      enableAntialiasing: true,
      pixelRatio: 1.0,
      enableAnnotations: true,
      annotations: []
    };

    return await this.prisma.viewerConfiguration.create({
      data: defaultConfig
    });
  }

  /**
   * Update viewer configuration
   */
  async updateViewerConfiguration(
    productId: string, 
    updates: Partial<ViewerConfiguration>
  ): Promise<ViewerConfiguration> {
    try {
      const { productId: _, environmentMapId: __, annotations, ...updateData } = updates;
      const config = await this.prisma.viewerConfiguration.update({
        where: { productId },
        data: {
          ...updateData,
          ...(annotations && { annotations: JSON.stringify(annotations) }),
          updatedAt: new Date()
        },
        include: {
          environmentMap: true
        }
      });

      return this.mapPrismaConfiguration(config);
    } catch (error) {
      console.error('Error updating viewer configuration:', error);
      throw error;
    }
  }

  /**
   * Start viewer session
   */
  async startViewerSession(
    productId: string,
    userId?: string,
    deviceInfo?: {
      userAgent?: string;
      deviceType?: 'mobile' | 'tablet' | 'desktop';
      screenResolution?: string;
    }
  ): Promise<ViewerSession> {
    try {
      const sessionId = uuidv4();
      
      const session = await this.prisma.viewerSession.create({
        data: {
          sessionId,
          productId,
          userId,
          userAgent: deviceInfo?.userAgent,
          deviceType: deviceInfo?.deviceType,
          screenResolution: deviceInfo?.screenResolution,
          viewDuration: 0,
          interactionCount: 0,
          zoomCount: 0,
          rotationCount: 0,
          annotationViews: 0
        }
      });

      return this.mapPrismaSession(session);
    } catch (error) {
      console.error('Error starting viewer session:', error);
      throw error;
    }
  }

  /**
   * Update viewer session
   */
  async updateViewerSession(
    sessionId: string,
    updates: {
      viewDuration?: number;
      interactionCount?: number;
      zoomCount?: number;
      rotationCount?: number;
      annotationViews?: number;
      loadTime?: number;
      frameRate?: number;
      memoryUsage?: number;
    }
  ): Promise<void> {
    try {
      await this.prisma.viewerSession.update({
        where: { sessionId },
        data: updates
      });
    } catch (error) {
      console.error('Error updating viewer session:', error);
    }
  }

  /**
   * End viewer session
   */
  async endViewerSession(sessionId: string): Promise<void> {
    try {
      await this.prisma.viewerSession.update({
        where: { sessionId },
        data: {
          endedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error ending viewer session:', error);
    }
  }

  /**
   * Get viewer analytics
   */
  async getViewerAnalytics(
    productId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<ViewerAnalytics> {
    try {
      const whereClause: any = {};
      
      if (productId) {
        whereClause.productId = productId;
      }
      
      if (startDate || endDate) {
        whereClause.startedAt = {};
        if (startDate) whereClause.startedAt.gte = startDate;
        if (endDate) whereClause.startedAt.lte = endDate;
      }

      const sessions = await this.prisma.viewerSession.findMany({
        where: whereClause,
        include: {
          product: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      const totalSessions = sessions.length;
      const averageViewDuration = totalSessions > 0 
        ? sessions.reduce((sum, s) => sum + s.viewDuration, 0) / totalSessions 
        : 0;
      
      const averageInteractions = totalSessions > 0
        ? sessions.reduce((sum, s) => sum + s.interactionCount, 0) / totalSessions
        : 0;

      // Device breakdown
      const deviceBreakdown: Record<string, number> = {};
      sessions.forEach(session => {
        const device = session.deviceType || 'unknown';
        deviceBreakdown[device] = (deviceBreakdown[device] || 0) + 1;
      });

      // Popular products
      const productViews: Record<string, { count: number; totalDuration: number; name: string }> = {};
      sessions.forEach(session => {
        const productId = session.productId;
        if (!productViews[productId]) {
          productViews[productId] = { 
            count: 0, 
            totalDuration: 0, 
            name: session.product?.name || 'Unknown'
          };
        }
        productViews[productId].count++;
        productViews[productId].totalDuration += session.viewDuration;
      });

      const popularProducts = Object.entries(productViews)
        .map(([productId, data]) => ({
          productId,
          viewCount: data.count,
          averageDuration: data.count > 0 ? data.totalDuration / data.count : 0
        }))
        .sort((a, b) => b.viewCount - a.viewCount)
        .slice(0, 10);

      // Performance metrics
      const performanceSessions = sessions.filter(s => s.loadTime && s.frameRate && s.memoryUsage);
      const performanceMetrics = {
        averageLoadTime: performanceSessions.length > 0
          ? performanceSessions.reduce((sum, s) => sum + (s.loadTime || 0), 0) / performanceSessions.length
          : 0,
        averageFrameRate: performanceSessions.length > 0
          ? performanceSessions.reduce((sum, s) => sum + Number(s.frameRate || 0), 0) / performanceSessions.length
          : 0,
        averageMemoryUsage: performanceSessions.length > 0
          ? performanceSessions.reduce((sum, s) => sum + (s.memoryUsage || 0), 0) / performanceSessions.length
          : 0
      };

      return {
        totalSessions,
        averageViewDuration,
        averageInteractions,
        deviceBreakdown,
        popularProducts,
        performanceMetrics
      };
    } catch (error) {
      console.error('Error getting viewer analytics:', error);
      throw error;
    }
  }

  /**
   * Detect viewer capabilities
   */
  detectViewerCapabilities(userAgent: string): ViewerCapabilities {
    // Basic capability detection based on user agent
    // In a real implementation, this would be more sophisticated
    
    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);
    const isWebGL2Supported = true; // Most modern browsers support WebGL2
    
    return {
      webgl: true,
      webgl2: isWebGL2Supported,
      webgpu: false, // WebGPU is still experimental
      maxTextureSize: isMobile ? 2048 : 4096,
      maxVertexAttributes: 16,
      supportedFormats: [
        'GLB',
        'GLTF',
        'JPG',
        'PNG',
        'WEBP'
      ] as any[],
      deviceMemory: isMobile ? 2 : 8, // GB estimate
      hardwareConcurrency: isMobile ? 4 : 8
    };
  }

  /**
   * Save camera state
   */
  async saveCameraState(sessionId: string, cameraState: CameraState): Promise<void> {
    try {
      // For now, we'll store this in session metadata
      // In a more complex implementation, you might have a separate table
      await this.prisma.viewerSession.update({
        where: { sessionId },
        data: {
          // Store camera state in a JSON field if available
          // This is a simplified approach
        }
      });
    } catch (error) {
      console.error('Error saving camera state:', error);
    }
  }

  /**
   * Add annotation to product
   */
  async addAnnotation(
    productId: string,
    annotation: Omit<Annotation, 'id'>
  ): Promise<Annotation> {
    try {
      const config = await this.prisma.viewerConfiguration.findUnique({
        where: { productId }
      });

      if (!config) {
        throw new Error('Viewer configuration not found');
      }

      const annotations = (config.annotations as unknown as Annotation[]) || [];
      const newAnnotation: Annotation = {
        ...annotation,
        id: uuidv4()
      };

      annotations.push(newAnnotation);

      await this.prisma.viewerConfiguration.update({
        where: { productId },
        data: {
          annotations: annotations as any
        }
      });

      return newAnnotation;
    } catch (error) {
      console.error('Error adding annotation:', error);
      throw error;
    }
  }

  /**
   * Update annotation
   */
  async updateAnnotation(
    productId: string,
    annotationId: string,
    updates: Partial<Annotation>
  ): Promise<Annotation> {
    try {
      const config = await this.prisma.viewerConfiguration.findUnique({
        where: { productId }
      });

      if (!config) {
        throw new Error('Viewer configuration not found');
      }

      const annotations = (config.annotations as unknown as Annotation[]) || [];
      const annotationIndex = annotations.findIndex(a => a.id === annotationId);

      if (annotationIndex === -1) {
        throw new Error('Annotation not found');
      }

      annotations[annotationIndex] = { ...annotations[annotationIndex], ...updates };

      await this.prisma.viewerConfiguration.update({
        where: { productId },
        data: {
          annotations: annotations as any
        }
      });

      return annotations[annotationIndex];
    } catch (error) {
      console.error('Error updating annotation:', error);
      throw error;
    }
  }

  /**
   * Delete annotation
   */
  async deleteAnnotation(productId: string, annotationId: string): Promise<void> {
    try {
      const config = await this.prisma.viewerConfiguration.findUnique({
        where: { productId }
      });

      if (!config) {
        throw new Error('Viewer configuration not found');
      }

      const annotations = (config.annotations as unknown as Annotation[]) || [];
      const filteredAnnotations = annotations.filter(a => a.id !== annotationId);

      await this.prisma.viewerConfiguration.update({
        where: { productId },
        data: {
          annotations: filteredAnnotations as any
        }
      });
    } catch (error) {
      console.error('Error deleting annotation:', error);
      throw error;
    }
  }

  /**
   * Map Prisma configuration to domain model
   */
  private mapPrismaConfiguration(config: any): ViewerConfiguration {
    return {
      id: config.id,
      productId: config.productId,
      cameraPosition: config.cameraPosition,
      cameraTarget: config.cameraTarget,
      cameraFov: Number(config.cameraFov),
      ambientLightColor: config.ambientLightColor,
      ambientLightIntensity: Number(config.ambientLightIntensity),
      directionalLightColor: config.directionalLightColor,
      directionalLightIntensity: Number(config.directionalLightIntensity),
      directionalLightPosition: config.directionalLightPosition,
      environmentMapId: config.environmentMapId,
      backgroundType: config.backgroundType as any,
      backgroundColor: config.backgroundColor,
      enableOrbitControls: config.enableOrbitControls,
      enableZoom: config.enableZoom,
      enablePan: config.enablePan,
      enableRotate: config.enableRotate,
      autoRotate: config.autoRotate,
      autoRotateSpeed: Number(config.autoRotateSpeed),
      enableShadows: config.enableShadows,
      shadowMapSize: config.shadowMapSize,
      enableAntialiasing: config.enableAntialiasing,
      pixelRatio: Number(config.pixelRatio),
      enableAnnotations: config.enableAnnotations,
      annotations: typeof config.annotations === 'string'
        ? JSON.parse(config.annotations) as ViewerAnnotation[]
        : config.annotations as ViewerAnnotation[],
      createdAt: config.createdAt,
      updatedAt: config.updatedAt
    };
  }

  /**
   * Map Prisma session to domain model
   */
  private mapPrismaSession(session: any): ViewerSession {
    return {
      id: session.id,
      sessionId: session.sessionId,
      productId: session.productId,
      userId: session.userId,
      viewDuration: session.viewDuration,
      interactionCount: session.interactionCount,
      zoomCount: session.zoomCount,
      rotationCount: session.rotationCount,
      annotationViews: session.annotationViews,
      userAgent: session.userAgent,
      deviceType: session.deviceType,
      screenResolution: session.screenResolution,
      loadTime: session.loadTime,
      frameRate: session.frameRate ? Number(session.frameRate) : undefined,
      memoryUsage: session.memoryUsage,
      startedAt: session.startedAt,
      endedAt: session.endedAt
    };
  }

  /**
   * Cleanup - close database connection
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
