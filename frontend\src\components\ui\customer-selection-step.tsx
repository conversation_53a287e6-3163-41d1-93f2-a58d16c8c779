'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from './card'
import { But<PERSON> } from './button'
import { Badge } from './badge'
import { 
  Search,
  Plus,
  User,
  Building2,
  Mail,
  Phone,
  MapPin,
  Edit,
  Check,
  X
} from 'lucide-react'

// Mock customer data
const mockCustomers = [
  {
    id: 'cust-001',
    companyName: 'İnşaat A.Ş.',
    contactPerson: 'Ah<PERSON> Yılmaz',
    email: '<EMAIL>',
    phone: '+90 ************',
    address: {
      street: 'Atatürk Cad. No:123',
      city: 'İstanbul',
      state: 'İstanbul',
      country: 'Türkiye',
      postalCode: '34000'
    },
    taxNumber: '1234567890',
    totalOrders: 12,
    totalSpent: 245000,
    lastOrderDate: '2025-06-15'
  },
  {
    id: 'cust-002',
    companyName: 'Villa Projeleri A.Ş.',
    contactPerson: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+90 ************',
    address: {
      street: 'Cumhuriyet Mah. Deniz Sok. No:45',
      city: 'Antalya',
      state: 'Antalya',
      country: 'Türkiye',
      postalCode: '07000'
    },
    taxNumber: '0987654321',
    totalOrders: 8,
    totalSpent: 180000,
    lastOrderDate: '2025-06-20'
  },
  {
    id: 'cust-003',
    companyName: 'Dekorasyon Ltd.',
    contactPerson: 'Fatma Kaya',
    email: '<EMAIL>',
    phone: '+90 ************',
    address: {
      street: 'Merkez Mah. Sanat Cad. No:78',
      city: 'Ankara',
      state: 'Ankara',
      country: 'Türkiye',
      postalCode: '06000'
    },
    taxNumber: '5555666677',
    totalOrders: 15,
    totalSpent: 320000,
    lastOrderDate: '2025-06-25'
  }
]

interface CustomerSelectionStepProps {
  formData: any
  setFormData: (data: any) => void
}

export function CustomerSelectionStep({ formData, setFormData }: CustomerSelectionStepProps) {
  const [searchTerm, setSearchTerm] = React.useState('')
  const [showNewCustomerForm, setShowNewCustomerForm] = React.useState(false)
  const [selectedCustomer, setSelectedCustomer] = React.useState(formData.customer)
  const [newCustomerData, setNewCustomerData] = React.useState({
    companyName: '',
    contactPerson: '',
    email: '',
    phone: '',
    address: {
      street: '',
      city: '',
      state: '',
      country: 'Türkiye',
      postalCode: ''
    },
    taxNumber: '',
    notes: ''
  })

  const filteredCustomers = mockCustomers.filter(customer =>
    customer.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSelectCustomer = (customer: any) => {
    setSelectedCustomer(customer)
    setFormData({ ...formData, customer })
    setShowNewCustomerForm(false)
  }

  const handleCreateNewCustomer = () => {
    if (!newCustomerData.companyName || !newCustomerData.contactPerson || !newCustomerData.email) {
      alert('Şirket adı, iletişim kişisi ve e-posta alanları zorunludur.')
      return
    }

    const newCustomer = {
      id: `cust-new-${Date.now()}`,
      ...newCustomerData,
      totalOrders: 0,
      totalSpent: 0,
      lastOrderDate: null
    }

    setSelectedCustomer(newCustomer)
    setFormData({ ...formData, customer: newCustomer })
    setShowNewCustomerForm(false)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Selection Mode Toggle */}
      <div className="flex gap-4">
        <Button
          variant={!showNewCustomerForm ? 'default' : 'outline'}
          onClick={() => setShowNewCustomerForm(false)}
        >
          <Search className="w-4 h-4 mr-2" />
          Mevcut Müşteri Seç
        </Button>
        <Button
          variant={showNewCustomerForm ? 'default' : 'outline'}
          onClick={() => setShowNewCustomerForm(true)}
        >
          <Plus className="w-4 h-4 mr-2" />
          Yeni Müşteri Oluştur
        </Button>
      </div>

      {!showNewCustomerForm ? (
        /* Existing Customer Selection */
        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Müşteri ara (şirket adı, kişi adı, e-posta)..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Customer List */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
            {filteredCustomers.map((customer) => (
              <Card 
                key={customer.id} 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedCustomer?.id === customer.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                }`}
                onClick={() => handleSelectCustomer(customer)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{customer.companyName}</h3>
                      <p className="text-sm text-gray-600">{customer.contactPerson}</p>
                    </div>
                    {selectedCustomer?.id === customer.id && (
                      <Check className="w-5 h-5 text-blue-600" />
                    )}
                  </div>
                  
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      <span>{customer.email}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      <span>{customer.phone}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      <span>{customer.address.city}, {customer.address.country}</span>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>{customer.totalOrders} sipariş</span>
                      <span>{formatCurrency(customer.totalSpent)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredCustomers.length === 0 && (
            <div className="text-center py-8">
              <User className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600">Arama kriterlerinize uygun müşteri bulunamadı.</p>
              <Button 
                variant="outline" 
                className="mt-3"
                onClick={() => setShowNewCustomerForm(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Yeni Müşteri Oluştur
              </Button>
            </div>
          )}
        </div>
      ) : (
        /* New Customer Form */
        <Card>
          <CardHeader>
            <CardTitle>Yeni Müşteri Oluştur</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Şirket Adı *
                </label>
                <input
                  type="text"
                  value={newCustomerData.companyName}
                  onChange={(e) => setNewCustomerData({ ...newCustomerData, companyName: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Şirket adını girin"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  İletişim Kişisi *
                </label>
                <input
                  type="text"
                  value={newCustomerData.contactPerson}
                  onChange={(e) => setNewCustomerData({ ...newCustomerData, contactPerson: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="İletişim kişisi adı"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  E-posta *
                </label>
                <input
                  type="email"
                  value={newCustomerData.email}
                  onChange={(e) => setNewCustomerData({ ...newCustomerData, email: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Telefon
                </label>
                <input
                  type="tel"
                  value={newCustomerData.phone}
                  onChange={(e) => setNewCustomerData({ ...newCustomerData, phone: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="+90 5XX XXX XX XX"
                />
              </div>
            </div>

            {/* Address */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Adres Bilgileri</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Adres
                  </label>
                  <input
                    type="text"
                    value={newCustomerData.address.street}
                    onChange={(e) => setNewCustomerData({ 
                      ...newCustomerData, 
                      address: { ...newCustomerData.address, street: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Sokak, cadde, bina no"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Şehir
                  </label>
                  <input
                    type="text"
                    value={newCustomerData.address.city}
                    onChange={(e) => setNewCustomerData({ 
                      ...newCustomerData, 
                      address: { ...newCustomerData.address, city: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Şehir"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Posta Kodu
                  </label>
                  <input
                    type="text"
                    value={newCustomerData.address.postalCode}
                    onChange={(e) => setNewCustomerData({ 
                      ...newCustomerData, 
                      address: { ...newCustomerData.address, postalCode: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="34000"
                  />
                </div>
              </div>
            </div>

            {/* Additional Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Vergi Numarası
                </label>
                <input
                  type="text"
                  value={newCustomerData.taxNumber}
                  onChange={(e) => setNewCustomerData({ ...newCustomerData, taxNumber: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="1234567890"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notlar
              </label>
              <textarea
                value={newCustomerData.notes}
                onChange={(e) => setNewCustomerData({ ...newCustomerData, notes: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Müşteri hakkında özel notlar..."
              />
            </div>

            <div className="flex gap-3 pt-4">
              <Button onClick={handleCreateNewCustomer}>
                <Check className="w-4 h-4 mr-2" />
                Müşteriyi Oluştur
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setShowNewCustomerForm(false)}
              >
                <X className="w-4 h-4 mr-2" />
                İptal
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Selected Customer Summary */}
      {selectedCustomer && (
        <Card className="bg-green-50 border-green-200">
          <CardHeader>
            <CardTitle className="text-green-800 flex items-center gap-2">
              <Check className="w-5 h-5" />
              Seçilen Müşteri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-semibold text-green-900">{selectedCustomer.companyName}</h3>
                <p className="text-green-700">{selectedCustomer.contactPerson}</p>
                <p className="text-green-600 text-sm">{selectedCustomer.email}</p>
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  setSelectedCustomer(null)
                  setFormData({ ...formData, customer: null })
                }}
              >
                <X className="w-4 h-4 mr-1" />
                Değiştir
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
