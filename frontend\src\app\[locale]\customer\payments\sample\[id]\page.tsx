'use client';

import React from 'react';
import { useParams, useRouter } from 'next/navigation';
import { SampleProvider } from '@/contexts/sample-context';
import CustomerSamplePaymentPage from '@/components/dashboard/pages/CustomerSamplePaymentPage';

export default function CustomerSamplePaymentPageRoute() {
  const params = useParams();
  const router = useRouter();
  const sampleRequestId = params.id as string;

  const handleNavigate = (route: string) => {
    router.push(route);
  };

  return (
    <SampleProvider>
      <CustomerSamplePaymentPage 
        sampleRequestId={sampleRequestId}
        onNavigate={handleNavigate}
      />
    </SampleProvider>
  );
}
