import { useAuthStore } from '@/stores/authStore';
import { LoginForm, RegisterForm } from '@/types';

export const useAuth = () => {
  const {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    refreshToken,
    getCurrentUser,
    clearError,
    setLoading,
  } = useAuthStore();

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,

    // Actions
    login: async (credentials: LoginForm) => {
      try {
        await login(credentials);
      } catch (error) {
        // Error is already handled in store
        throw error;
      }
    },

    register: async (userData: RegisterForm) => {
      try {
        await register(userData);
      } catch (error) {
        // Error is already handled in store
        throw error;
      }
    },

    logout: async () => {
      try {
        await logout();
      } catch (error) {
        // Error is already handled in store
        console.error('Logout error:', error);
      }
    },

    refreshToken: async () => {
      try {
        await refreshToken();
      } catch (error) {
        // Error is already handled in store
        throw error;
      }
    },

    getCurrentUser: async () => {
      try {
        await getCurrentUser();
      } catch (error) {
        // Error is already handled in store
        throw error;
      }
    },

    clearError,
    setLoading,

    // Computed values
    isProducer: user?.userType === 'PRODUCER',
    isCustomer: user?.userType === 'CUSTOMER',
    isAdmin: user?.userType === 'ADMIN',
    isEmailVerified: user?.emailVerified || false,
    isActive: user?.status === 'ACTIVE',
  };
};
