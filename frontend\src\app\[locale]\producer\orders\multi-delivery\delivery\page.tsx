'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Calendar, Truck, Package } from 'lucide-react'
import { DeliveryScheduleCard } from '@/components/ui/delivery-schedule-card'
import { mockMultiDeliveryOrder } from '@/data/mock-multi-delivery'

export default function DeliverySchedulePage() {
  const { producer } = useProducerAuth()
  const [order, setOrder] = React.useState(mockMultiDeliveryOrder)

  const handleScheduleDelivery = (packageId: string, date: string) => {
    console.log('Scheduling delivery:', { packageId, date })
    
    setOrder(prevOrder => ({
      ...prevOrder,
      deliveryPackages: prevOrder.deliveryPackages.map(pkg => {
        if (pkg.id === packageId) {
          return {
            ...pkg,
            deliveryDate: date,
            deliveryStatus: 'ready' as const,
            deliverySchedule: {
              ...pkg.deliverySchedule,
              id: `DS-${packageId}`,
              deliveryPackageId: packageId,
              deliveryMethod: 'delivery' as const,
              scheduledDate: date,
              status: 'scheduled' as const,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          }
        }
        return pkg
      })
    }))

    alert(`Paket teslimatı ${date} tarihine planlandı!`)
  }

  const handleUpdateDelivery = (packageId: string, updates: any) => {
    console.log('Updating delivery:', { packageId, updates })
    
    setOrder(prevOrder => ({
      ...prevOrder,
      deliveryPackages: prevOrder.deliveryPackages.map(pkg => {
        if (pkg.id === packageId) {
          return {
            ...pkg,
            deliveryDate: updates.deliveryDate || pkg.deliveryDate,
            deliveryNotes: updates.notes || pkg.deliveryNotes,
            deliverySchedule: pkg.deliverySchedule ? {
              ...pkg.deliverySchedule,
              trackingNumber: updates.trackingNumber || pkg.deliverySchedule.trackingNumber,
              carrierCompany: updates.carrierCompany || pkg.deliverySchedule.carrierCompany,
              updatedAt: new Date().toISOString()
            } : pkg.deliverySchedule
          }
        }
        return pkg
      })
    }))

    alert('Teslimat bilgileri güncellendi!')
  }

  const handleCompleteDelivery = (packageId: string) => {
    console.log('Completing delivery:', packageId)
    
    setOrder(prevOrder => ({
      ...prevOrder,
      deliveryPackages: prevOrder.deliveryPackages.map(pkg => {
        if (pkg.id === packageId) {
          return {
            ...pkg,
            deliveryStatus: 'delivered' as const,
            actualDeliveryDate: new Date().toISOString().split('T')[0],
            deliverySchedule: pkg.deliverySchedule ? {
              ...pkg.deliverySchedule,
              status: 'delivered' as const,
              actualDate: new Date().toISOString().split('T')[0],
              updatedAt: new Date().toISOString()
            } : pkg.deliverySchedule
          }
        }
        return pkg
      })
    }))

    alert('Teslimat tamamlandı!')
  }

  const handleGoBack = () => {
    window.location.href = '/producer/orders/multi-delivery'
  }

  // Statistics
  const deliveryStats = React.useMemo(() => {
    const packages = order.deliveryPackages
    return {
      total: packages.length,
      pending: packages.filter(p => p.deliveryStatus === 'pending').length,
      ready: packages.filter(p => p.deliveryStatus === 'ready').length,
      shipped: packages.filter(p => p.deliveryStatus === 'shipped').length,
      delivered: packages.filter(p => p.deliveryStatus === 'delivered').length
    }
  }, [order.deliveryPackages])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleGoBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Geri Dön
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Teslimat Takvimi</h1>
          <p className="text-gray-600">
            Sipariş #{order.id} - Paket bazlı teslimat planlaması ve takibi
          </p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex gap-4 border-b">
        <Button
          variant="ghost"
          onClick={() => window.location.href = '/producer/orders/multi-delivery'}
        >
          Üretim Takibi
        </Button>
        <Button
          variant="ghost"
          className="border-b-2 border-blue-600 text-blue-600"
        >
          Teslimat Takvimi
        </Button>
        <Button
          variant="ghost"
          onClick={() => window.location.href = '/producer/orders/multi-delivery/payments'}
        >
          Ödeme Takibi
        </Button>
      </div>

      {/* Delivery Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Package className="w-5 h-5 text-gray-600" />
              <div>
                <p className="text-sm text-gray-600">Toplam Paket</p>
                <p className="text-2xl font-bold">{deliveryStats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="w-5 h-5 text-gray-600" />
              <div>
                <p className="text-sm text-gray-600">Bekliyor</p>
                <p className="text-2xl font-bold text-gray-700">{deliveryStats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Package className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Hazır</p>
                <p className="text-2xl font-bold text-purple-700">{deliveryStats.ready}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Truck className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Kargoda</p>
                <p className="text-2xl font-bold text-blue-700">{deliveryStats.shipped}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Package className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Teslim Edildi</p>
                <p className="text-2xl font-bold text-green-700">{deliveryStats.delivered}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Delivery Packages */}
      <div>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Teslimat Paketleri</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {order.deliveryPackages
            .sort((a, b) => a.packageNumber - b.packageNumber)
            .map((pkg) => (
              <DeliveryScheduleCard
                key={pkg.id}
                deliveryPackage={pkg}
                onScheduleDelivery={handleScheduleDelivery}
                onUpdateDelivery={handleUpdateDelivery}
                onCompleteDelivery={handleCompleteDelivery}
              />
            ))}
        </div>
      </div>

      {/* Development Info */}
      <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
        <h3 className="font-semibold text-purple-800 mb-2">🚚 Teslimat Yönetimi</h3>
        <p className="text-sm text-purple-700">
          Bu sayfa teslimat takvimi ve yükleme planlaması için oluşturulmuştur.
        </p>
        <div className="mt-3 text-xs text-purple-600">
          <p><strong>Özellikler:</strong></p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Paket bazlı teslimat planlaması</li>
            <li>Kargo takip numarası yönetimi</li>
            <li>Teslimat durumu güncelleme</li>
            <li>Teslimat istatistikleri</li>
            <li>Gerçek zamanlı durum takibi</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
