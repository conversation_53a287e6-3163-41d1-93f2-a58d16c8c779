#!/bin/bash

# Disaster Recovery Script
# Türkiye Doğal Taş Pazaryeri - Disaster Recovery Plan

set -e

# Configuration
BACKUP_DIR="./backups"
RECOVERY_LOG="./logs/disaster-recovery-$(date +%Y%m%d-%H%M%S).log"
RECOVERY_TYPE=${1:-"full"}  # full, database, files, config
BACKUP_DATE=${2:-"latest"}  # specific date or "latest"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$RECOVERY_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$RECOVERY_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$RECOVERY_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$RECOVERY_LOG"
}

# Create logs directory
mkdir -p "$(dirname "$RECOVERY_LOG")"

log_info "=== DISASTER RECOVERY STARTED ==="
log_info "Recovery Type: $RECOVERY_TYPE"
log_info "Backup Date: $BACKUP_DATE"
log_info "Log File: $RECOVERY_LOG"

# Function to list available backups
list_backups() {
    log_info "Available backups:"
    
    if [[ -d "$BACKUP_DIR" ]]; then
        find "$BACKUP_DIR" -name "*.sql.gz" -o -name "*.tar.gz" | sort -r | head -10
    else
        log_error "Backup directory not found: $BACKUP_DIR"
        return 1
    fi
}

# Function to find latest backup
find_latest_backup() {
    local backup_type=$1
    local latest_backup=""
    
    case "$backup_type" in
        "database")
            latest_backup=$(find "$BACKUP_DIR" -name "backup-*.sql.gz" | sort -r | head -1)
            ;;
        "code")
            latest_backup=$(find "$BACKUP_DIR" -name "code-backup-*.tar.gz" | sort -r | head -1)
            ;;
        *)
            log_error "Unknown backup type: $backup_type"
            return 1
            ;;
    esac
    
    if [[ -n "$latest_backup" && -f "$latest_backup" ]]; then
        echo "$latest_backup"
    else
        log_error "No $backup_type backup found"
        return 1
    fi
}

# Function to verify backup integrity
verify_backup() {
    local backup_file=$1
    
    log_info "Verifying backup integrity: $backup_file"
    
    if [[ ! -f "$backup_file" ]]; then
        log_error "Backup file not found: $backup_file"
        return 1
    fi
    
    # Check file size
    local file_size=$(stat -f%z "$backup_file" 2>/dev/null || stat -c%s "$backup_file" 2>/dev/null)
    if [[ $file_size -eq 0 ]]; then
        log_error "Backup file is empty: $backup_file"
        return 1
    fi
    
    # Check compression integrity
    if [[ "$backup_file" == *.gz ]]; then
        if gzip -t "$backup_file"; then
            log_success "Backup file integrity verified"
        else
            log_error "Backup file is corrupted: $backup_file"
            return 1
        fi
    fi
    
    return 0
}

# Function to stop services
stop_services() {
    log_info "Stopping services for recovery..."
    
    # Stop Docker containers
    if docker-compose -f docker-compose.production.yml ps | grep -q "Up"; then
        docker-compose -f docker-compose.production.yml down
        log_success "Production services stopped"
    fi
    
    if docker-compose -f docker-compose.staging.yml ps | grep -q "Up"; then
        docker-compose -f docker-compose.staging.yml down
        log_success "Staging services stopped"
    fi
}

# Function to start services
start_services() {
    log_info "Starting services after recovery..."
    
    # Start production services
    if docker-compose -f docker-compose.production.yml up -d; then
        log_success "Production services started"
    else
        log_error "Failed to start production services"
        return 1
    fi
    
    # Wait for services to be healthy
    local retries=30
    while [[ $retries -gt 0 ]]; do
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            log_success "Services are healthy"
            break
        fi
        log_info "Waiting for services to be healthy... ($retries retries left)"
        sleep 10
        ((retries--))
    done
    
    if [[ $retries -eq 0 ]]; then
        log_error "Services failed to become healthy"
        return 1
    fi
}

# Function to recover database
recover_database() {
    log_info "Starting database recovery..."
    
    local backup_file
    if [[ "$BACKUP_DATE" == "latest" ]]; then
        backup_file=$(find_latest_backup "database")
    else
        backup_file=$(find "$BACKUP_DIR" -name "*$BACKUP_DATE*.sql.gz" | head -1)
    fi
    
    if [[ -z "$backup_file" ]]; then
        log_error "Database backup not found for date: $BACKUP_DATE"
        return 1
    fi
    
    verify_backup "$backup_file" || return 1
    
    log_info "Recovering database from: $backup_file"
    
    # Create pre-recovery backup
    log_info "Creating pre-recovery backup..."
    cd backend
    node scripts/backup-database.js || log_warning "Pre-recovery backup failed"
    cd ..
    
    # Restore database
    cd backend
    if node scripts/restore-database.js --file="$backup_file" --auto-confirm; then
        log_success "Database recovery completed"
    else
        log_error "Database recovery failed"
        return 1
    fi
    cd ..
}

# Function to recover application files
recover_files() {
    log_info "Starting file recovery..."
    
    local backup_file
    if [[ "$BACKUP_DATE" == "latest" ]]; then
        backup_file=$(find_latest_backup "code")
    else
        backup_file=$(find "$BACKUP_DIR" -name "*$BACKUP_DATE*.tar.gz" | head -1)
    fi
    
    if [[ -z "$backup_file" ]]; then
        log_error "Code backup not found for date: $BACKUP_DATE"
        return 1
    fi
    
    verify_backup "$backup_file" || return 1
    
    log_info "Recovering files from: $backup_file"
    
    # Create current state backup
    log_info "Creating current state backup..."
    local current_backup="$BACKUP_DIR/pre-recovery-$(date +%Y%m%d-%H%M%S).tar.gz"
    tar -czf "$current_backup" --exclude=node_modules --exclude=.git --exclude=dist --exclude=.next . || log_warning "Current state backup failed"
    
    # Extract backup
    if tar -xzf "$backup_file" -C ./; then
        log_success "File recovery completed"
    else
        log_error "File recovery failed"
        return 1
    fi
}

# Function to recover configuration
recover_config() {
    log_info "Starting configuration recovery..."
    
    # Backup current configuration
    local config_backup="$BACKUP_DIR/config-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    tar -czf "$config_backup" .env* docker-compose*.yml nginx/ monitoring/ || log_warning "Config backup failed"
    
    # Restore configuration from backup
    local backup_file
    if [[ "$BACKUP_DATE" == "latest" ]]; then
        backup_file=$(find "$BACKUP_DIR" -name "config-*.tar.gz" | sort -r | head -1)
    else
        backup_file=$(find "$BACKUP_DIR" -name "*$BACKUP_DATE*config*.tar.gz" | head -1)
    fi
    
    if [[ -n "$backup_file" && -f "$backup_file" ]]; then
        verify_backup "$backup_file" || return 1
        
        if tar -xzf "$backup_file"; then
            log_success "Configuration recovery completed"
        else
            log_error "Configuration recovery failed"
            return 1
        fi
    else
        log_warning "No configuration backup found, using current configuration"
    fi
}

# Function to perform full recovery
full_recovery() {
    log_info "Starting full system recovery..."
    
    # Stop services
    stop_services
    
    # Recover in order
    recover_config || return 1
    recover_files || return 1
    recover_database || return 1
    
    # Rebuild and restart services
    log_info "Rebuilding services..."
    docker-compose -f docker-compose.production.yml build --no-cache
    
    start_services || return 1
    
    log_success "Full recovery completed"
}

# Function to verify recovery
verify_recovery() {
    log_info "Verifying recovery..."
    
    # Check service health
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "Backend health check passed"
    else
        log_error "Backend health check failed"
        return 1
    fi
    
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log_success "Frontend health check passed"
    else
        log_error "Frontend health check failed"
        return 1
    fi
    
    # Check database connectivity
    cd backend
    if npx prisma db push --accept-data-loss; then
        log_success "Database connectivity verified"
    else
        log_error "Database connectivity failed"
        return 1
    fi
    cd ..
    
    # Run basic functionality tests
    log_info "Running basic functionality tests..."
    if ./scripts/run-tests.sh staging unit; then
        log_success "Basic functionality tests passed"
    else
        log_warning "Some functionality tests failed"
    fi
    
    log_success "Recovery verification completed"
}

# Function to create recovery report
create_recovery_report() {
    local report_file="./logs/recovery-report-$(date +%Y%m%d-%H%M%S).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Disaster Recovery Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Disaster Recovery Report</h1>
        <p><strong>Recovery Type:</strong> $RECOVERY_TYPE</p>
        <p><strong>Backup Date:</strong> $BACKUP_DATE</p>
        <p><strong>Recovery Date:</strong> $(date)</p>
    </div>
    
    <div class="section">
        <h2>Recovery Summary</h2>
        <p>Recovery process completed. See log file for details: $RECOVERY_LOG</p>
    </div>
    
    <div class="section">
        <h2>Next Steps</h2>
        <ul>
            <li>Verify all functionality is working correctly</li>
            <li>Check data integrity</li>
            <li>Update monitoring and alerting</li>
            <li>Notify stakeholders of recovery completion</li>
            <li>Review and update disaster recovery procedures</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    log_success "Recovery report created: $report_file"
}

# Main recovery function
main() {
    local exit_code=0
    
    # Check if backup directory exists
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_error "Backup directory not found: $BACKUP_DIR"
        exit 1
    fi
    
    # List available backups
    list_backups
    
    # Perform recovery based on type
    case "$RECOVERY_TYPE" in
        "database")
            recover_database || exit_code=1
            ;;
        "files")
            stop_services
            recover_files || exit_code=1
            start_services || exit_code=1
            ;;
        "config")
            stop_services
            recover_config || exit_code=1
            start_services || exit_code=1
            ;;
        "full")
            full_recovery || exit_code=1
            ;;
        *)
            log_error "Unknown recovery type: $RECOVERY_TYPE"
            exit 1
            ;;
    esac
    
    # Verify recovery
    if [[ $exit_code -eq 0 ]]; then
        verify_recovery || exit_code=1
    fi
    
    # Create recovery report
    create_recovery_report
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "=== DISASTER RECOVERY COMPLETED SUCCESSFULLY ==="
    else
        log_error "=== DISASTER RECOVERY FAILED ==="
    fi
    
    exit $exit_code
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [recovery_type] [backup_date]"
        echo ""
        echo "Arguments:"
        echo "  recovery_type    Type of recovery (full|database|files|config) [default: full]"
        echo "  backup_date      Backup date to restore or 'latest' [default: latest]"
        echo ""
        echo "Examples:"
        echo "  $0 full latest"
        echo "  $0 database 20231201"
        echo "  $0 files latest"
        exit 0
        ;;
esac

# Confirmation prompt
echo -e "${YELLOW}WARNING: This will perform disaster recovery which may overwrite current data.${NC}"
echo -e "${YELLOW}Recovery Type: $RECOVERY_TYPE${NC}"
echo -e "${YELLOW}Backup Date: $BACKUP_DATE${NC}"
echo ""
read -p "Are you sure you want to continue? (yes/no): " confirm

if [[ "$confirm" != "yes" ]]; then
    echo "Recovery cancelled."
    exit 0
fi

# Run main function
main "$@"
