// RFC-301: Anonymous Bidding Form Component
'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Clock, DollarSign, Truck, FileText } from 'lucide-react';

interface BidFormData {
  price: number;
  deliveryTime: number;
  specifications: string;
  notes: string;
  currency: string;
}

interface AnonymousBidFormProps {
  bidRequestId: string;
  bidRequestTitle: string;
  deadline: Date;
  onSubmit: (bidData: BidFormData) => Promise<void>;
  onCancel: () => void;
}

export default function AnonymousBidForm({
  bidRequestId,
  bidRequestTitle,
  deadline,
  onSubmit,
  onCancel
}: AnonymousBidFormProps) {
  const [formData, setFormData] = useState<BidFormData>({
    price: 0,
    deliveryTime: 30,
    specifications: '',
    notes: '',
    currency: 'USD'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Partial<BidFormData>>({});

  const timeRemaining = Math.max(0, deadline.getTime() - Date.now());
  const hoursRemaining = Math.floor(timeRemaining / (1000 * 60 * 60));
  const minutesRemaining = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));

  const validateForm = (): boolean => {
    const newErrors: Partial<BidFormData> = {};

    if (!formData.price || formData.price <= 0) {
      newErrors.price = 'Price must be greater than 0';
    }

    if (!formData.deliveryTime || formData.deliveryTime <= 0) {
      newErrors.deliveryTime = 'Delivery time must be greater than 0';
    }

    if (!formData.specifications.trim()) {
      newErrors.specifications = 'Specifications are required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting bid:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof BidFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Submit Anonymous Bid</span>
            <Badge variant={hoursRemaining < 2 ? "destructive" : "secondary"}>
              <Clock className="w-4 h-4 mr-1" />
              {hoursRemaining}h {minutesRemaining}m remaining
            </Badge>
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Bid Request: {bidRequestTitle}
          </p>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Price Section */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <DollarSign className="w-5 h-5 text-green-600" />
                <Label className="text-base font-medium">Pricing</Label>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="price">Total Price *</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    value={formData.price || ''}
                    onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                    className={errors.price ? 'border-red-500' : ''}
                    placeholder="Enter your bid amount"
                  />
                  {errors.price && (
                    <p className="text-sm text-red-500 mt-1">{errors.price}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <Select value={formData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD ($)</SelectItem>
                      <SelectItem value="EUR">EUR (€)</SelectItem>
                      <SelectItem value="TRY">TRY (₺)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Delivery Section */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Truck className="w-5 h-5 text-blue-600" />
                <Label className="text-base font-medium">Delivery</Label>
              </div>
              
              <div>
                <Label htmlFor="deliveryTime">Delivery Time (days) *</Label>
                <Input
                  id="deliveryTime"
                  type="number"
                  value={formData.deliveryTime || ''}
                  onChange={(e) => handleInputChange('deliveryTime', parseInt(e.target.value) || 0)}
                  className={errors.deliveryTime ? 'border-red-500' : ''}
                  placeholder="Number of days for delivery"
                />
                {errors.deliveryTime && (
                  <p className="text-sm text-red-500 mt-1">{errors.deliveryTime}</p>
                )}
              </div>
            </div>

            {/* Specifications Section */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <FileText className="w-5 h-5 text-purple-600" />
                <Label className="text-base font-medium">Technical Details</Label>
              </div>
              
              <div>
                <Label htmlFor="specifications">Product Specifications *</Label>
                <Textarea
                  id="specifications"
                  value={formData.specifications}
                  onChange={(e) => handleInputChange('specifications', e.target.value)}
                  className={errors.specifications ? 'border-red-500' : ''}
                  placeholder="Describe your product specifications, quality, dimensions, etc."
                  rows={4}
                />
                {errors.specifications && (
                  <p className="text-sm text-red-500 mt-1">{errors.specifications}</p>
                )}
              </div>
              
              <div>
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Any additional information or special conditions"
                  rows={3}
                />
              </div>
            </div>

            {/* Privacy Notice */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">🔒 Anonymous Bidding</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Your identity will remain anonymous until payment is confirmed</li>
                <li>• Only your bid amount and specifications will be visible</li>
                <li>• Customer can see total number of bids and price ranges</li>
                <li>• Contact details will be shared only after escrow payment</li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4 pt-4">
              <Button
                type="submit"
                disabled={isSubmitting || timeRemaining <= 0}
                className="flex-1"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Anonymous Bid'}
              </Button>
              
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
