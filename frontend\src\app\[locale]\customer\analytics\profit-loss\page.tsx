'use client';

import React from 'react';
import ProfitLossAnalyticsPage from '@/components/dashboard/pages/ProfitLossAnalyticsPage';
import { useRouter } from 'next/navigation';

export default function CustomerProfitLossAnalyticsPage() {
  const router = useRouter();
  
  const handleNavigate = (route: string) => {
    router.push(route);
  };

  return <ProfitLossAnalyticsPage onNavigate={handleNavigate} />;
}
