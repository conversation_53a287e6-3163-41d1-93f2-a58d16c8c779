"use client"

import * as React from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Navigation } from "@/components/ui/navigation"
import { Container } from "@/components/ui/container"
import { Button } from "@/components/ui/button"
import { SkipLink } from "@/components/ui/skip-link"
import { VisuallyHidden } from "@/components/ui/visually-hidden"
import { Product3DViewerModal } from "@/components/ui/3d-viewer-modal"
import { QuoteRequestModal } from "@/components/ui/quote-request-modal"
import { FavoritesProvider, useFavorites } from "@/contexts/favorites-context"
import { useAuth } from "@/contexts/auth-context"

// Mock data - gerçek uygulamada API'den gelecek
const products = [
  {
    id: "1",
    name: "Carrara Beyaz Mermer",
    category: "Mermer",
    price: { min: 45, max: 65, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.8,
    reviewCount: 124,
    location: "Afyon, Türkiye",
    producer: "ABC Mermer Ltd.",

    description: "Carrara beyaz mermer, İtalya'nın ünlü Carrara bölgesinden ilham alan, zarif ve klasik bir doğal taş seçeneğidir. Yüksek kaliteli işçiliği ve dayanıklılığı ile öne çıkar.",
    specifications: {
      origin: "Türkiye",
      density: "2.7 g/cm³",
      waterAbsorption: "0.2%",
      compressiveStrength: "120 MPa",
      flexuralStrength: "15 MPa",
      frostResistance: "Yüksek"
    },

    applications: [
      "İç mekan zemin kaplaması",
      "Duvar kaplaması", 
      "Mutfak tezgahı",
      "Banyo uygulamaları",
      "Merdiven basamakları"
    ]
  }
  // Diğer ürünler...
]

const navigationLinks = [
  { name: "Ana Sayfa", href: "/" },
  { name: "Ürünler", href: "/products" },
  { name: "Favoriler", href: "/favorites" },
  { name: "Hakkımızda", href: "/about" },
  { name: "İletişim", href: "/contact" }
]

function ProductDetailContent() {
  const params = useParams()
  const router = useRouter()
  const { addToFavorites, removeFromFavorites, isFavorite } = useFavorites()
  const { isAuthenticated, showLoginModal } = useAuth()
  const [is3DViewerOpen, setIs3DViewerOpen] = React.useState(false)
  const [isQuoteModalOpen, setIsQuoteModalOpen] = React.useState(false)
  const [selectedImage, setSelectedImage] = React.useState(0)

  const product = products.find(p => p.id === params.id)

  if (!product) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-stone-900 mb-4">Ürün Bulunamadı</h1>
          <Button onClick={() => router.push('/products')}>
            Ürünlere Dön
          </Button>
        </div>
      </div>
    )
  }

  const handleToggleFavorite = () => {
    if (!isAuthenticated) {
      showLoginModal()
      return
    }
    if (isFavorite(product.id)) {
      removeFromFavorites(product.id)
    } else {
      addToFavorites(product)
    }
  }

  const handleRequestQuote = () => {
    if (!isAuthenticated) {
      showLoginModal()
      return
    }
    setIsQuoteModalOpen(true)
  }

  return (
    <div className="min-h-screen bg-[var(--background)]">
      <SkipLink />
      <Navigation
        brand={{
          name: "Türkiye Doğal Taş Pazarı",
          href: "/"
        }}
        links={navigationLinks}
        actions={
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              Giriş Yap
            </Button>
            <Button variant="primary" size="sm">
              Üye Ol
            </Button>
          </div>
        }
      />
      
      <main id="main-content">
        <Container size="xl" className="py-8">
          <VisuallyHidden>
            <h1>{product.name} - Ürün Detayı</h1>
          </VisuallyHidden>

          {/* Breadcrumb */}
          <nav className="mb-6">
            <ol className="flex items-center space-x-2 text-sm text-stone-600">
              <li><a href="/" className="hover:text-stone-900">Ana Sayfa</a></li>
              <li>/</li>
              <li><a href="/products" className="hover:text-stone-900">Ürünler</a></li>
              <li>/</li>
              <li className="text-stone-900">{product.name}</li>
            </ol>
          </nav>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            {/* Product Images */}
            <div className="space-y-4">
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>
              {/* Horizontal Scrollable Image Gallery */}
              <div className="relative">
                <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide">
                  {[1, 2, 3, 4, 5, 6, 7, 8].map((index) => (
                    <button
                      key={index}
                      className={`flex-shrink-0 w-20 h-20 bg-gray-100 rounded-lg overflow-hidden border-2 ${
                        selectedImage === index - 1 ? 'border-stone-500' : 'border-transparent'
                      }`}
                      onClick={() => setSelectedImage(index - 1)}
                    >
                      <img
                        src={product.image}
                        alt={`${product.name} ${index}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
                {/* Scroll indicators */}
                <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-white to-transparent pointer-events-none"></div>
              </div>
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              <div>
                <span className="inline-block px-3 py-1 bg-stone-100 text-stone-700 text-sm rounded-full mb-2">
                  {product.category}
                </span>
                <h1 className="text-3xl font-bold text-stone-900 mb-2">{product.name}</h1>
                <p className="text-stone-600 mb-6">{product.description}</p>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={handleRequestQuote}
                  className="w-full"
                >
                  📋 Teklif Al
                </Button>
                
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => setIs3DViewerOpen(true)}
                    className="flex-1"
                  >
                    🔄 3D Görünüm
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="lg"
                    onClick={handleToggleFavorite}
                    className="px-6"
                  >
                    {isFavorite(product.id) ? "❤️" : "🤍"}
                  </Button>
                </div>
              </div>


            </div>
          </div>

          {/* Product Details Tabs */}
          <div className="bg-white rounded-lg shadow-sm border border-stone-200 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Specifications */}
              <div>
                <h3 className="text-lg font-semibold text-stone-900 mb-4">Teknik Özellikler</h3>
                <dl className="space-y-2">
                  <div>
                    <dt className="text-sm font-medium text-stone-600">Menşei</dt>
                    <dd className="text-sm text-stone-900">{product.specifications.origin}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-stone-600">Yoğunluk</dt>
                    <dd className="text-sm text-stone-900">{product.specifications.density}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-stone-600">Su Emme</dt>
                    <dd className="text-sm text-stone-900">{product.specifications.waterAbsorption}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-stone-600">Basınç Dayanımı</dt>
                    <dd className="text-sm text-stone-900">{product.specifications.compressiveStrength}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-stone-600">Eğilme Dayanımı</dt>
                    <dd className="text-sm text-stone-900">{product.specifications.flexuralStrength}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-stone-600">Don Direnci</dt>
                    <dd className="text-sm text-stone-900">{product.specifications.frostResistance}</dd>
                  </div>
                </dl>
              </div>



              {/* Applications */}
              <div>
                <h3 className="text-lg font-semibold text-stone-900 mb-4">Kullanım Alanları</h3>
                <ul className="space-y-2">
                  {product.applications.map((application, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-stone-500 mt-1">•</span>
                      <span className="text-sm text-stone-700">{application}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* 3D Viewer Modal */}
          <Product3DViewerModal
            isOpen={is3DViewerOpen}
            onClose={() => setIs3DViewerOpen(false)}
            product={product}
          />

          {/* Quote Request Modal */}
          <QuoteRequestModal
            isOpen={isQuoteModalOpen}
            onClose={() => setIsQuoteModalOpen(false)}
            product={product}
          />
        </Container>
      </main>
    </div>
  )
}

export default function ProductDetailPage() {
  return (
    <FavoritesProvider>
      <ProductDetailContent />
    </FavoritesProvider>
  )
}
