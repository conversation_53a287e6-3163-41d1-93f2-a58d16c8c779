import { z } from 'zod';

export const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  userType: z.enum(['PRODUCER', 'CUSTOMER'], {
    errorMap: () => ({ message: 'User type must be either PRODUCER or CUSTOMER' })
  }),
  companyName: z.string().min(2, 'Company name must be at least 2 characters'),
  countryCode: z.string().length(2, 'Country code must be 2 characters'),
  contactPerson: z.string().optional(),
  phone: z.string().optional(),

  // Üretici için ek alanlar
  taxNumber: z.string().optional(),
  tradeRegistryNumber: z.string().optional(),
  companyAddress: z.string().optional(),
  companyPhone: z.string().optional(),
  companyEmail: z.string().email().optional().or(z.literal('')),
  website: z.string().url().optional().or(z.literal('')),
  foundedYear: z.string().optional(),
  employeeCount: z.string().optional(),
  productionCapacity: z.string().optional(),
  productCategories: z.array(z.string()).optional(),
  certifications: z.array(z.string()).optional(),
  hasQuarry: z.boolean().optional(),
  quarries: z.array(z.object({
    name: z.string(),
    address: z.string(),
    googleMapsLink: z.string().optional(),
    description: z.string().optional()
  })).optional(),
  factories: z.array(z.object({
    name: z.string(),
    address: z.string(),
    googleMapsLink: z.string().optional(),
    capacity: z.string().optional(),
    description: z.string().optional()
  })).optional(),
  providesCustomManufacturing: z.boolean().optional(),
  customManufacturingDetails: z.string().optional(),
  companyDescription: z.string().optional()
});

export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

export const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required'),
});

export const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email format'),
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

export const verifyEmailSchema = z.object({
  token: z.string().min(1, 'Verification token is required'),
});
