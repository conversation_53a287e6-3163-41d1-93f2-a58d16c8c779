'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { 
  FileText, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Package,
  Truck,
  Calculator,
  Send,
  X,
  Info,
  CheckCircle
} from 'lucide-react';
import { ProductPlacement, ShowroomConfiguration } from '../core/ShowroomEngine';

interface QuoteRequestModalProps {
  products: ProductPlacement[];
  configuration: ShowroomConfiguration;
  onClose: () => void;
  onSubmit: (quoteData: QuoteRequestData) => void;
  className?: string;
}

export interface QuoteRequestData {
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company?: string;
    address: string;
    city: string;
    country: string;
  };
  projectInfo: {
    projectName: string;
    projectType: 'residential' | 'commercial' | 'industrial';
    deliveryDate: string;
    installationRequired: boolean;
    notes: string;
  };
  products: Array<{
    productId: string;
    dimensions: { width: number; height: number; thickness: number };
    quantity: number;
    area: number;
    pattern: string;
    groutSettings: any;
    notes: string;
  }>;
  totalArea: number;
  designSnapshot: string; // Base64 encoded image
}

export const QuoteRequestModal: React.FC<QuoteRequestModalProps> = ({
  products,
  configuration,
  onClose,
  onSubmit,
  className = ''
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    address: '',
    city: '',
    country: 'Türkiye'
  });

  const [projectInfo, setProjectInfo] = useState({
    projectName: '',
    projectType: 'residential' as const,
    deliveryDate: '',
    installationRequired: false,
    notes: ''
  });

  const [productQuantities, setProductQuantities] = useState<Record<string, number>>(
    products.reduce((acc, product) => ({ ...acc, [product.id]: 1 }), {})
  );

  const [productNotes, setProductNotes] = useState<Record<string, string>>({});

  // Calculate totals
  const totals = useMemo(() => {
    const totalArea = products.reduce((sum, product) => {
      const quantity = productQuantities[product.id] || 1;
      return sum + (product.area * quantity);
    }, 0);

    const totalProducts = Object.values(productQuantities).reduce((sum, qty) => sum + qty, 0);

    return { totalArea, totalProducts };
  }, [products, productQuantities]);

  // Validate current step
  const isStepValid = useMemo(() => {
    switch (currentStep) {
      case 1:
        return customerInfo.name && customerInfo.email && customerInfo.phone && customerInfo.address;
      case 2:
        return projectInfo.projectName && projectInfo.deliveryDate;
      case 3:
        return true; // Product review step is always valid
      default:
        return false;
    }
  }, [currentStep, customerInfo, projectInfo]);

  const handleNext = useCallback(() => {
    if (isStepValid && currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, isStepValid]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const handleSubmit = useCallback(async () => {
    if (!isStepValid) return;

    setIsSubmitting(true);

    try {
      // Generate design snapshot (simplified)
      const designSnapshot = await generateDesignSnapshot();

      const quoteData: QuoteRequestData = {
        customerInfo,
        projectInfo,
        products: products.map(product => ({
          productId: product.productId,
          dimensions: {
            width: product.scale.width,
            height: product.scale.height,
            thickness: 2 // Default thickness
          },
          quantity: productQuantities[product.id] || 1,
          area: product.area * (productQuantities[product.id] || 1),
          pattern: product.pattern,
          groutSettings: product.groutSettings,
          notes: productNotes[product.id] || ''
        })),
        totalArea: totals.totalArea,
        designSnapshot
      };

      await onSubmit(quoteData);
      
    } catch (error) {
      alert('Teklif gönderilirken hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsSubmitting(false);
    }
  }, [customerInfo, projectInfo, products, productQuantities, productNotes, totals, onSubmit, isStepValid]);

  const generateDesignSnapshot = useCallback(async (): Promise<string> => {
    // In a real implementation, this would capture the 3D scene
    return `data:image/svg+xml,${encodeURIComponent(`
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <rect width="400" height="300" fill="#f3f4f6"/>
        <rect x="50" y="50" width="300" height="200" fill="#e5e7eb" stroke="#d1d5db"/>
        <text x="200" y="140" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="16">
          3D Showroom Tasarımı
        </text>
        <text x="200" y="160" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="12">
          ${products.length} Ürün - ${totals.totalArea.toFixed(1)} m²
        </text>
      </svg>
    `)}`;
  }, [products.length, totals.totalArea]);

  const steps = [
    { number: 1, title: 'Müşteri Bilgileri', icon: User },
    { number: 2, title: 'Proje Detayları', icon: FileText },
    { number: 3, title: 'Ürün İncelemesi', icon: Package }
  ];

  return (
    <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 ${className}`}>
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Teklif Talebi</h2>
              <p className="text-sm text-gray-600 mt-1">
                3D tasarımınız için profesyonel teklif alın
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <X size={20} />
            </button>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-center mt-6">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = currentStep === step.number;
              const isCompleted = currentStep > step.number;
              
              return (
                <React.Fragment key={step.number}>
                  <div className="flex flex-col items-center">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                      isCompleted 
                        ? 'bg-green-500 text-white' 
                        : isActive 
                          ? 'bg-amber-500 text-white' 
                          : 'bg-gray-200 text-gray-600'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle size={20} />
                      ) : (
                        <Icon size={20} />
                      )}
                    </div>
                    <span className={`text-xs mt-2 ${
                      isActive ? 'text-amber-600 font-medium' : 'text-gray-600'
                    }`}>
                      {step.title}
                    </span>
                  </div>
                  
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-0.5 mx-4 ${
                      currentStep > step.number ? 'bg-green-500' : 'bg-gray-200'
                    }`} />
                  )}
                </React.Fragment>
              );
            })}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {/* Step 1: Customer Information */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Müşteri Bilgileri</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ad Soyad *
                  </label>
                  <input
                    type="text"
                    value={customerInfo.name}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    placeholder="Adınız ve soyadınız"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    E-posta *
                  </label>
                  <input
                    type="email"
                    value={customerInfo.email}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Telefon *
                  </label>
                  <input
                    type="tel"
                    value={customerInfo.phone}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    placeholder="+90 555 123 45 67"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Şirket
                  </label>
                  <input
                    type="text"
                    value={customerInfo.company}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, company: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    placeholder="Şirket adı (opsiyonel)"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Adres *
                  </label>
                  <textarea
                    value={customerInfo.address}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, address: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    placeholder="Tam adresiniz"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Şehir
                  </label>
                  <input
                    type="text"
                    value={customerInfo.city}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, city: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    placeholder="İstanbul"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ülke
                  </label>
                  <select
                    value={customerInfo.country}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, country: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="Türkiye">Türkiye</option>
                    <option value="Almanya">Almanya</option>
                    <option value="Fransa">Fransa</option>
                    <option value="İtalya">İtalya</option>
                    <option value="İngiltere">İngiltere</option>
                    <option value="ABD">ABD</option>
                    <option value="Diğer">Diğer</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Project Information */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Proje Detayları</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Proje Adı *
                  </label>
                  <input
                    type="text"
                    value={projectInfo.projectName}
                    onChange={(e) => setProjectInfo(prev => ({ ...prev, projectName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    placeholder="Örn: Villa Salon Döşemesi"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Proje Türü
                  </label>
                  <select
                    value={projectInfo.projectType}
                    onChange={(e) => setProjectInfo(prev => ({ ...prev, projectType: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="residential">Konut</option>
                    <option value="commercial">Ticari</option>
                    <option value="industrial">Endüstriyel</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Teslimat Tarihi *
                  </label>
                  <input
                    type="date"
                    value={projectInfo.deliveryDate}
                    onChange={(e) => setProjectInfo(prev => ({ ...prev, deliveryDate: e.target.value }))}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={projectInfo.installationRequired}
                      onChange={(e) => setProjectInfo(prev => ({ ...prev, installationRequired: e.target.checked }))}
                      className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Montaj hizmeti gerekli</span>
                  </label>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ek Notlar
                  </label>
                  <textarea
                    value={projectInfo.notes}
                    onChange={(e) => setProjectInfo(prev => ({ ...prev, notes: e.target.value }))}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    placeholder="Proje hakkında özel istekleriniz, detaylar..."
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Product Review */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Ürün İncelemesi</h3>
              
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <Info size={16} className="text-amber-600" />
                  <span className="text-sm font-medium text-amber-800">Tasarım Özeti</span>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-amber-700">Toplam Ürün:</span>
                    <div className="font-medium text-amber-900">{totals.totalProducts}</div>
                  </div>
                  <div>
                    <span className="text-amber-700">Toplam Alan:</span>
                    <div className="font-medium text-amber-900">{totals.totalArea.toFixed(2)} m²</div>
                  </div>
                  <div>
                    <span className="text-amber-700">Mekan:</span>
                    <div className="font-medium text-amber-900 capitalize">{configuration.room}</div>
                  </div>
                  <div>
                    <span className="text-amber-700">Işık:</span>
                    <div className="font-medium text-amber-900 capitalize">{configuration.lighting.preset}</div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                {products.map((product, index) => (
                  <div key={product.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {index + 1}. {product.productId}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {product.scale.width}×{product.scale.height} cm • Pattern: {product.pattern}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <label className="text-sm text-gray-600">Adet:</label>
                        <input
                          type="number"
                          min="1"
                          value={productQuantities[product.id] || 1}
                          onChange={(e) => setProductQuantities(prev => ({
                            ...prev,
                            [product.id]: parseInt(e.target.value) || 1
                          }))}
                          className="w-16 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-600">Birim Alan:</span>
                        <span className="ml-2 font-medium">{product.area.toFixed(3)} m²</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Toplam Alan:</span>
                        <span className="ml-2 font-medium">
                          {(product.area * (productQuantities[product.id] || 1)).toFixed(3)} m²
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Ürün Notu
                      </label>
                      <input
                        type="text"
                        value={productNotes[product.id] || ''}
                        onChange={(e) => setProductNotes(prev => ({
                          ...prev,
                          [product.id]: e.target.value
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                        placeholder="Bu ürün için özel notlarınız..."
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Adım {currentStep} / {steps.length}
          </div>
          
          <div className="flex gap-3">
            {currentStep > 1 && (
              <button
                onClick={handlePrevious}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Geri
              </button>
            )}
            
            {currentStep < steps.length ? (
              <button
                onClick={handleNext}
                disabled={!isStepValid}
                className="px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                İleri
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={!isStepValid || isSubmitting}
                className="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                    Gönderiliyor...
                  </>
                ) : (
                  <>
                    <Send size={16} />
                    Teklif Gönder
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuoteRequestModal;
