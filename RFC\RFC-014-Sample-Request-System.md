# RFC-014: Numune Talep Sistemi (Sample Request System)

## 1. <PERSON><PERSON>ş

### 1.1 <PERSON><PERSON>ç
<PERSON>şterilerin teklif aldıktan sonra sipariş vermeden önce numune talep edebilmesi için kapsamlı bir sistem tasarımı.

### 1.2 Ka<PERSON><PERSON>
- <PERSON><PERSON><PERSON><PERSON> sonrası numune talep süreci
- Admin onay ve takip sistemi
- Müşteri dashboard entegrasyonu
- ID numarası bazlı takip sistemi

## 2. İş Akışı (Business Flow)

### 2.1 Numune Talep Süreci
```
1. Müşteri Teklif <PERSON>ır (Mevcut)
   ↓
2. Numune Talep Eder (YENİ)
   ↓
3. Üretici Direkt Onaylar/Reddeder (YENİ)
   ↓
4. Onaylanırsa → Numune Hazırlanır
   ↓
5. Numune Gönderilir (Kargo Bilgileri)
   ↓
6. Müşteri Numune Alır ve Değerlendirir
   ↓
7. Si<PERSON><PERSON>ş Kararı Verir

NOT: <PERSON><PERSON> tüm süreci takip eder ama onay vermez
```

### 2.2 Durum Yönetimi
- **pending**: Beklemede (üretici onayı bekleniyor)
- **approved**: Üretici onayladı (hazırlık başlayacak)
- **rejected**: Üretici reddetti (sebep belirtilir)
- **preparing**: Hazırlanıyor (üretici hazırlık aşaması)
- **shipped**: Gönderildi (kargo bilgileri ile)
- **delivered**: Teslim edildi
- **evaluated**: Değerlendirildi (müşteri geri bildirimi)

## 3. Veritabanı Tasarımı

### 3.1 SampleRequest Tablosu
```sql
CREATE TABLE sample_requests (
  id VARCHAR(50) PRIMARY KEY,
  quote_request_id VARCHAR(50) NOT NULL,
  quote_id VARCHAR(50) NOT NULL,
  customer_id VARCHAR(50) NOT NULL,
  producer_id VARCHAR(50) NOT NULL,
  
  -- Numune Detayları
  requested_products JSONB NOT NULL, -- Hangi ürünlerden numune isteniyor
  sample_specifications JSONB, -- Özel numune gereksinimleri
  delivery_address JSONB NOT NULL,
  
  -- Durum Takibi
  status VARCHAR(20) DEFAULT 'pending',
  admin_notes TEXT,
  rejection_reason TEXT,
  
  -- Üretici Bilgileri
  producer_response JSONB, -- Üreticinin yanıtı
  preparation_time_days INTEGER,
  shipping_info JSONB, -- Kargo bilgileri
  
  -- Müşteri Değerlendirmesi
  customer_evaluation JSONB, -- Müşteri geri bildirimi
  will_order BOOLEAN, -- Sipariş verecek mi?
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  approved_at TIMESTAMP,
  shipped_at TIMESTAMP,
  delivered_at TIMESTAMP,
  evaluated_at TIMESTAMP,
  
  -- Foreign Keys
  FOREIGN KEY (quote_request_id) REFERENCES quote_requests(id),
  FOREIGN KEY (customer_id) REFERENCES customers(id),
  FOREIGN KEY (producer_id) REFERENCES producers(id)
);
```

### 3.2 SampleRequestTracking Tablosu
```sql
CREATE TABLE sample_request_tracking (
  id SERIAL PRIMARY KEY,
  sample_request_id VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL,
  notes TEXT,
  created_by VARCHAR(50), -- admin_id, customer_id, producer_id
  created_by_type VARCHAR(20), -- 'admin', 'customer', 'producer'
  created_at TIMESTAMP DEFAULT NOW(),
  
  FOREIGN KEY (sample_request_id) REFERENCES sample_requests(id)
);
```

## 4. API Tasarımı

### 4.1 Müşteri API Endpoints
```typescript
// Numune talep oluşturma
POST /api/samples/request
Body: {
  quoteRequestId: string;
  quoteId: string;
  requestedProducts: ProductSampleRequest[];
  deliveryAddress: Address;
  specialRequirements?: string;
}

// Müşteri numune taleplerini listeleme
GET /api/samples/customer/:customerId

// Numune talep detayı
GET /api/samples/:sampleRequestId

// Müşteri değerlendirmesi
PUT /api/samples/:sampleRequestId/evaluate
Body: {
  rating: number;
  feedback: string;
  willOrder: boolean;
  orderNotes?: string;
}
```

### 4.2 Admin API Endpoints
```typescript
// Tüm numune taleplerini listeleme (sadece görüntüleme)
GET /api/admin/samples

// Numune talep detayı (admin görünümü - sadece görüntüleme)
GET /api/admin/samples/:sampleRequestId

// Numune istatistikleri
GET /api/admin/samples/stats

// NOT: Admin onay/red endpoint'leri kaldırıldı
// Üretici direkt onay/red yapacak
```

### 4.3 Üretici API Endpoints
```typescript
// Üreticiye gelen numune talepleri
GET /api/producer/samples/:producerId

// Numune onaylama/reddetme (YENİ - Ana Özellik)
PUT /api/producer/samples/:sampleRequestId/approve
Body: {
  approved: boolean;
  notes?: string;
  rejectionReason?: string;
  preparationDays?: number; // Onaylanırsa kaç günde hazır olacak
}

// Numune hazırlık durumu güncelleme
PUT /api/producer/samples/:sampleRequestId/status
Body: {
  status: 'preparing' | 'shipped' | 'delivered';
  preparationDays?: number;
  shippingInfo?: ShippingInfo;
  notes?: string;
}
```

## 5. Frontend Entegrasyonu

### 5.1 Müşteri Dashboard Değişiklikleri

#### 5.1.1 Talepler Sayfası Güncellemesi
```typescript
// /customer/requests/active sayfasında
interface QuoteRequestCard {
  // Mevcut alanlar...
  sampleRequest?: SampleRequest; // Numune talep durumu
  canRequestSample: boolean; // Numune talep edilebilir mi?
}

// Yeni buton ekleme
<Button 
  onClick={() => openSampleRequestModal(quote)}
  disabled={!canRequestSample}
>
  Numune Talep Et
</Button>
```

#### 5.1.2 Yeni Numune Sekmesi
```typescript
// /customer/requests/samples sayfası
interface SampleRequestsPage {
  tabs: ['pending', 'approved', 'shipped', 'delivered', 'evaluated'];
  sampleCards: SampleRequestCard[];
  filters: {
    status: string[];
    dateRange: DateRange;
    producer: string[];
  };
}
```

### 5.2 Admin Panel Entegrasyonu

#### 5.2.1 Yeni Admin Sayfası
```typescript
// /admin/sample-requests sayfası
interface AdminSampleRequestsPage {
  pendingApprovals: SampleRequest[];
  allSamples: SampleRequest[];
  statistics: {
    totalRequests: number;
    pendingApprovals: number;
    approvalRate: number;
    averageDeliveryTime: number;
  };
}
```

## 6. UI/UX Tasarım Gereksinimleri

### 6.1 Numune Talep Modalı
- Ürün seçimi (teklif alınan ürünlerden)
- Teslimat adresi formu
- Özel gereksinimler text alanı
- Tahmini teslimat süresi gösterimi

### 6.2 Durum Takip Sistemi
- Timeline görünümü
- Renk kodlu durum göstergeleri
- Kargo takip entegrasyonu
- Bildirim sistemi

### 6.3 Admin Onay Arayüzü
- Toplu onay/red işlemleri
- Detaylı numune bilgileri
- Üretici iletişim bilgileri
- Onay/red sebep formu

## 7. Bildirim Sistemi

### 7.1 Email Bildirimleri
- Müşteri: Numune talep onayı/reddi
- Üretici: Yeni numune talebi bildirimi
- Admin: Yeni numune talep bildirimi

### 7.2 Dashboard Bildirimleri
- Real-time durum güncellemeleri
- Kargo takip bildirimleri
- Değerlendirme hatırlatmaları

## 8. İş Kuralları

### 8.1 Numune Talep Koşulları
- Sadece teklif alınmış ürünlerden numune talep edilebilir
- Bir teklif için maksimum 1 numune talebi
- Numune ücretsiz (kargo müşteri öder)
- Numune boyutu: maksimum 10x10 cm

### 8.2 Admin Onay Kriterleri
- Müşteri geçmişi kontrolü
- Teklif tutarı eşik değeri (minimum $1000)
- Üretici kapasitesi kontrolü
- Fraud detection kontrolleri

## 9. Teknik Gereksinimler

### 9.1 Backend
- Node.js/Express API endpoints
- PostgreSQL veritabanı güncellemeleri
- Email service entegrasyonu
- File upload (numune fotoğrafları)

### 9.2 Frontend
- React/Next.js component'leri
- Modal ve form yönetimi
- State management (Context API)
- Responsive tasarım

### 9.3 Admin Panel
- Yeni admin sayfaları
- Onay/red workflow'u
- Raporlama dashboard'u
- Export işlevselliği

## 10. Test Senaryoları

### 10.1 Müşteri Test Senaryoları
- Numune talep oluşturma
- Durum takibi
- Değerlendirme gönderme
- Sipariş verme kararı

### 10.2 Admin Test Senaryoları
- Toplu onay işlemleri
- Red sebep belirtme
- İstatistik görüntüleme
- Export işlemleri

### 10.3 Üretici Test Senaryoları
- Numune talep alma
- Durum güncelleme
- Kargo bilgisi girme
- Müşteri iletişimi

## 11. Implementasyon Aşamaları

### Faz 1: Temel Altyapı (1 hafta)
- Veritabanı şeması oluşturma
- Temel API endpoint'leri
- Backend service'leri

### Faz 2: Müşteri Arayüzü (1 hafta)
- Numune talep modalı
- Durum takip sayfası
- Dashboard entegrasyonu

### Faz 3: Admin Panel (1 hafta)
- Admin onay sayfası
- İstatistik dashboard'u
- Raporlama sistemi

### Faz 4: Test ve Optimizasyon (3 gün)
- End-to-end testler
- Performance optimizasyonu
- Bug fixing

## 12. Başarı Metrikleri

### 12.1 İş Metrikleri
- Numune talep oranı (teklif başına)
- Onay oranı (admin)
- Sipariş dönüşüm oranı (numune sonrası)
- Müşteri memnuniyeti

### 12.2 Teknik Metrikleri
- API response time
- Database query performance
- Email delivery rate
- System uptime

---

**RFC Durumu**: Draft
**Versiyon**: 1.0
**Tarih**: 2025-07-05
**Hazırlayan**: Augment Agent
