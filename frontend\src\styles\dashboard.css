/* Customer Dashboard Styles */
/* <PERSON><PERSON><PERSON><PERSON><PERSON> Doğal Taş Marketplace Platformu */

/* CSS Variables for Dashboard Theme */
:root {
  /* Primary Colors */
  --dashboard-primary: #2563EB;
  --dashboard-primary-dark: #1D4ED8;
  --dashboard-secondary: #7C3AED;
  --dashboard-accent: #059669;
  
  /* Status Colors */
  --status-success: #10B981;
  --status-warning: #F59E0B;
  --status-error: #EF4444;
  --status-info: #3B82F6;
  
  /* Neutral Colors */
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;
}

/* Base Dashboard Styles */
.dashboard-container {
  min-height: 100vh;
  background-color: var(--gray-50);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Sidebar Styles */
.dashboard-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 16rem;
  background: white;
  border-right: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
  z-index: 50;
  transform: translateX(-100%);
  transition: transform var(--transition-normal);
}

.dashboard-sidebar.open {
  transform: translateX(0);
}

@media (min-width: 1024px) {
  .dashboard-sidebar {
    transform: translateX(0);
    position: relative;
  }
}

/* Main Content Area */
.dashboard-main {
  margin-left: 0;
  transition: margin-left var(--transition-normal);
}

@media (min-width: 1024px) {
  .dashboard-main {
    margin-left: 16rem;
  }
}

/* KPI Cards */
.kpi-card {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.kpi-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.kpi-card-icon {
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.kpi-card-icon.blue {
  background-color: #EBF8FF;
  color: var(--dashboard-primary);
}

.kpi-card-icon.green {
  background-color: #F0FDF4;
  color: var(--status-success);
}

.kpi-card-icon.purple {
  background-color: #FAF5FF;
  color: var(--dashboard-secondary);
}

.kpi-card-icon.orange {
  background-color: #FFF7ED;
  color: #EA580C;
}

/* Chart Widgets */
.chart-widget {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.chart-container {
  position: relative;
  height: 300px;
}

/* Quick Actions */
.quick-actions {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.quick-action-item {
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-100);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.quick-action-item:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-sm);
}

/* Recent Activity */
.activity-item {
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  transition: background-color var(--transition-fast);
  cursor: pointer;
}

.activity-item:hover {
  background-color: var(--gray-50);
}

.activity-icon {
  padding: var(--spacing-sm);
  border-radius: var(--radius-lg);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-normal);
}

.activity-item:hover .activity-icon {
  transform: scale(1.1);
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  gap: var(--spacing-xs);
}

.status-badge.completed {
  background-color: #D1FAE5;
  color: var(--status-success);
}

.status-badge.pending {
  background-color: #FEF3C7;
  color: var(--status-warning);
}

.status-badge.cancelled {
  background-color: #FEE2E2;
  color: var(--status-error);
}

.status-badge.in-progress {
  background-color: #DBEAFE;
  color: var(--status-info);
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.loading-spinner {
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--dashboard-primary);
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Grid Layouts */
.dashboard-grid {
  display: grid;
  gap: var(--spacing-lg);
}

.kpi-grid {
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .kpi-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .kpi-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.chart-grid {
  grid-template-columns: 1fr;
}

@media (min-width: 1024px) {
  .chart-grid {
    grid-template-columns: 2fr 1fr;
  }
}

.content-grid {
  grid-template-columns: 1fr;
}

@media (min-width: 1024px) {
  .content-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Mobile Responsive */
@media (max-width: 1023px) {
  .dashboard-sidebar {
    width: 100%;
    max-width: 20rem;
  }
  
  .dashboard-main {
    margin-left: 0;
  }
  
  .kpi-card {
    padding: var(--spacing-md);
  }
  
  .chart-widget {
    padding: var(--spacing-md);
  }
  
  .quick-actions {
    padding: var(--spacing-md);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #1F2937;
    --gray-100: #374151;
    --gray-200: #4B5563;
    --gray-300: #6B7280;
    --gray-400: #9CA3AF;
    --gray-500: #D1D5DB;
    --gray-600: #E5E7EB;
    --gray-700: #F3F4F6;
    --gray-800: #F9FAFB;
    --gray-900: #FFFFFF;
  }
  
  .dashboard-container {
    background-color: var(--gray-50);
    color: var(--gray-900);
  }
  
  .kpi-card,
  .chart-widget,
  .quick-actions {
    background-color: var(--gray-100);
    border-color: var(--gray-200);
  }
}

/* Print Styles */
@media print {
  .dashboard-sidebar {
    display: none;
  }
  
  .dashboard-main {
    margin-left: 0;
  }
  
  .kpi-card,
  .chart-widget {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus States */
.focus-visible:focus {
  outline: 2px solid var(--dashboard-primary);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .kpi-card,
  .chart-widget,
  .quick-actions {
    border-width: 2px;
  }
  
  .status-badge {
    border: 1px solid currentColor;
  }
}
