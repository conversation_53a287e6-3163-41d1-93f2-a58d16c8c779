'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Building2,
  AlertTriangle,
  Plus,
  FileText,
  MapPin,
  CheckCircle,
  ArrowRight,
  Info
} from 'lucide-react'

interface QuarryAccessControlProps {
  hasQuarry: boolean
  children: React.ReactNode
}

export function QuarryAccessControl({ hasQuarry, children }: QuarryAccessControlProps) {
  const router = useRouter()

  if (hasQuarry) {
    return <>{children}</>
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        <Card className="p-8 text-center">
          <div className="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Building2 className="w-10 h-10 text-orange-600" />
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Blok Satışı İçin Ocak Sahipliği Gerekli
          </h1>
          
          <p className="text-gray-600 mb-8 leading-relaxed">
            Blok ürünleri satabilmek için öncelikle bir ocağa sahip olmanız gerekmektedir. 
            Ocak sahipliğinizi belgeleyerek blok satış yetkisi alabilirsiniz.
          </p>

          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-8">
            <div className="flex items-start gap-3">
              <Info className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
              <div className="text-left">
                <h3 className="font-medium text-amber-900 mb-2">Blok Satış Yetki Süreci</h3>
                <ul className="text-sm text-amber-800 space-y-1">
                  <li>• Ocak sahiplik belgelerinizi yükleyin</li>
                  <li>• Admin onayını bekleyin (1-3 iş günü)</li>
                  <li>• Onay sonrası blok satışına başlayın</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <Button
              className="w-full bg-orange-600 hover:bg-orange-700"
              onClick={() => router.push('/producer/quarries/add')}
            >
              <Plus className="w-4 h-4 mr-2" />
              Ocak Bilgilerini Ekle
            </Button>
            
            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.push('/producer/profile')}
            >
              <FileText className="w-4 h-4 mr-2" />
              Profil Ayarlarına Git
            </Button>
            
            <Button
              variant="ghost"
              className="w-full"
              onClick={() => router.push('/producer/dashboard')}
            >
              Dashboard'a Dön
            </Button>
          </div>

          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="font-medium text-gray-900 mb-4">Gerekli Belgeler</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2 text-gray-600">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Ocak İşletme Ruhsatı
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Tapu Senedi / Kira Sözleşmesi
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Çevre İzin Belgesi
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Vergi Levhası
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}

// Blok sayfaları için wrapper hook
export function useQuarryAccess() {
  const [hasAccess, setHasAccess] = React.useState<boolean | null>(null)
  
  React.useEffect(() => {
    // Producer context'inden hasQuarry bilgisini al
    const checkQuarryAccess = () => {
      try {
        const storedProducer = localStorage.getItem('producer')
        if (storedProducer) {
          const producer = JSON.parse(storedProducer)
          setHasAccess(producer.hasQuarry || false)
        } else {
          setHasAccess(false)
        }
      } catch (error) {
        console.error('Error checking quarry access:', error)
        setHasAccess(false)
      }
    }

    checkQuarryAccess()
  }, [])

  return hasAccess
}

// Blok sayfaları için HOC
export function withQuarryAccess<P extends object>(
  Component: React.ComponentType<P>
) {
  return function QuarryProtectedComponent(props: P) {
    const hasAccess = useQuarryAccess()

    if (hasAccess === null) {
      // Loading state
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
        </div>
      )
    }

    return (
      <QuarryAccessControl hasQuarry={hasAccess}>
        <Component {...props} />
      </QuarryAccessControl>
    )
  }
}
