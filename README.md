# 🏛️ Türkiye Doğal Taş Marketplace Platformu

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Node.js](https://img.shields.io/badge/node.js-20.10.0-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/react-18.2.0-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/typescript-5.3.0-blue.svg)](https://www.typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/postgresql-16.1-blue.svg)](https://www.postgresql.org/)

> Türkiye'nin doğal taş sektörünü dijitalleştiren, üreticileri küresel pazarla buluşturan ve güvenli, şeffaf ticaret ortamı sağlayan lider B2B marketplace platformu.

## 🎯 Proje Vizyonu

Türkiye'nin doğal taş sektörünü dijitalleştirerek, yerel üreticileri küresel pazarla buluşturan ve uluslararası müşterilere güvenli, şeffaf bir ticaret ortamı sunan lider B2B marketplace platformu olmak.

## 🚀 Temel Özellikler

### 💰 Şeffaf Fiyatlandırma Sistemi (Güncellenmiş - Anonimlik Kaldırıldı)
- **Direkt Fiyatlandırma**: Üreticilerden direkt en uygun fiyatlarla doğal taş ürünlerine ulaşım
- **Rekabetçi Ortam**: Şeffaf ve rekabetçi fiyatlarla alışveriş (anonimlik sistemi kaldırıldı)
- **Güvenli Ödeme**: Güvenli ödeme sistemi ile ön ödeme
- **Açık Kimlik**: Üretici ve müşteri bilgileri artık gizli değil

### 🏭 Kapsamlı Üretici Profilleri
- Ocak ve fabrika lokasyonları (Google Maps entegrasyonu)
- Üretim kapasitesi raporları
- Taş analiz raporları ve teknik özellikler
- Sertifikalar (ISO, CE, vb.)
- Fason üretim hizmet bilgileri

### 📦 Çoklu Teslimat Sistemi ✅ NEW (RFC-015)
- **Büyük Sipariş Yönetimi**: 500m² veya $25,000 üzeri siparişler için özel sistem ✅
- **Müşteri Onayı Zorunlu**: Çoklu teslimat müşteri onayı olmadan oluşmaz ✅
- **Paket Bazlı Üretim**: Her teslimat paketi için ayrı üretim takvimi ✅
- **Esnek Teslimat**: Bağımsız teslimat tarihleri ve yöntemleri ✅
- **Paket Bazlı Ödeme**: Avans, teslimat, tamamlama ödemeleri ✅
- **Teklif Aşaması Entegrasyonu**: Üretici teklif verme aşamasında çoklu teslimat seçeneği ✅
- **Müşteri Onay Arayüzü**: Paket seçimi, şart onayı, değişiklik talepleri ✅
- **Paket Yönetimi**: Düzenleme, duraklatma, takip sistemi ✅
- **Risk Dağıtımı**: Büyük siparişlerde risk azaltma ✅
- **Nakit Akışı**: Üretici için daha iyi ödeme planlaması ✅

### 💎 Gelişmiş Ürün Yönetimi ✅ UPDATED (Basitleştirildi)
- **Ürün Kategorileri**: Mermer, Traverten, Granit, Oniks, Kireçtaşı, Bazalt, Andezit + Plaka (ayrı kategori) ✅
- **Basitleştirilmiş Ürün Kartları**: Sadece ürün adı ve resmi gösterimi ✅
- **Responsive Grid**: 1-4 sütun arası responsive tasarım ✅
- **Kategori Filtresi**: Sadece kategori bazlı filtreleme (diğer filtreler kaldırıldı) ✅
- **Infinite Scroll**: Sonsuz kaydırma ile ürün yükleme ✅
- **Ürün Detay Sayfası**: Basitleştirildi - sadece ülke bilgisi, kaydırılabilir galeri ✅
- **3D Görüntüleyici**: Modal pencerede 3D ürün görüntüleme (fiyatlandırma kaldırıldı) ✅
- **Favoriler Sistemi**: LocalStorage ile kalıcı favori ürün yönetimi ✅
- **Teklif Sistemi**: Üye girişi zorunlu, çoklu ürün seçimi, metrik ölçümler ✅
- **Üretici Ürün Yönetimi**: Görüntüle, düzenle, sil, stok ekle işlevleri ✅
- **Ürün Durumu Yönetimi**: Aktif/Pasif/Taslak durum değiştirme ✅
- **Stok Yönetimi**: Excel tablosu görünümü ile stok ekleme ✅

### 🤖 AI Destekli Özellikler (RFC-601)
- **Akıllı Chatbot**: 7/24 çok dilli müşteri desteği (OpenAI GPT-4)
- **Intent Sınıflandırma**: Kullanıcı niyetlerinin otomatik tespiti
- **Duygu Analizi**: Müşteri memnuniyetinin gerçek zamanlı takibi
- **İnsan Operatöre Yönlendirme**: Akıllı escalation sistemi
- **Sürekli Öğrenme**: Konversasyon analitiği ve iyileştirme
- **Çok Dilli Destek**: 10+ dilde doğal dil işleme
- **Bilgi Tabanı**: Ürün, süreç ve FAQ veritabanı entegrasyonu

### 🧠 Self-Learning AI Pazarlama Sistemi (RFC-013) ✅ NEW

Doğal taş sektörü için devamlı öğrenen ve gelişen AI destekli pazarlama otomasyonu:

#### 🚀 Temel AI Modülleri
- **Email Marketing AI**: Otomatik kampanya oluşturma ve optimizasyon ✅
- **Social Media AI**: Platform özel içerik üretimi ve zamanlama ✅
- **Customer Acquisition AI**: Müşteri arama ve iletişim otomasyonu ✅
- **Ads Management AI**: Reklam kampanyası optimizasyonu ✅
- **Analytics Engine**: Performans analizi ve raporlama ✅
- **Approval System**: İnsan onay sistemi kritik kararlar için ✅

#### 🧠 Self-Learning Modülleri ✅ NEW
- **Adaptive Learning Engine**: Kendi kendine öğrenen ve stratejiler geliştiren AI ✅
- **Continuous Research Module**: Sürekli pazar araştırması ve trend analizi ✅
- **Dynamic Strategy Generator**: Veriye dayalı strateji evrimi ✅
- **Real-Time Optimizer**: Gerçek zamanlı performans optimizasyonu ✅
- **Knowledge Base Evolution**: Sürekli genişleyen bilgi tabanı ✅

#### 🌟 Self-Learning Özellikleri
- **Devamlı Öğrenme**: Her döngüde performans verilerinden öğrenir
- **Otomatik Araştırma**: Pazar trendlerini ve fırsatları sürekli araştırır
- **Strateji Evrimi**: Başarılı paternlere göre stratejileri geliştirir
- **Real-Time Adaptasyon**: Anlık performans değişikliklerine tepki verir
- **Bilgi Sentezi**: Farklı kaynaklardan gelen bilgileri birleştirir
- **Kültürel Adaptasyon**: 50+ ülke için otomatik kültürel uyarlama
- **Çoklu Dil Desteği**: AI destekli çeviri ve yerelleştirme

### 📊 Müşteri Dashboard (RFC-007) ✅ UPDATED - Modüler Yapı
- **Modüler Routing**: Next.js App Router ile tam modüler yapı ✅
- **Ana Dashboard**: `/customer/dashboard` - KPI kartları, grafikler, hızlı işlemler ✅
- **Favoriler**: `/customer/favorites` - Favori ürün yönetimi ve toplu teklif ✅
- **Talepler**: `/customer/requests` - Teklif talep yönetimi (aktif/tamamlanan/iptal) ✅
- **Numune Taleplerim**: `/customer/requests/samples` - Numune talep sistemi ve takip ✅ NEW
- **Siparişler**: `/customer/orders` - Sipariş takibi (devam eden/tamamlanan/iptal) ✅
- **Analizler**: `/customer/analytics` - İş performansı analizi (alım/satış/kar-zarar) ✅
- **Ön Muhasebe**: `/customer/accounting` - Finansal takip (gelir-gider/faturalar/raporlar) ✅
- **Stok Takibi**: `/customer/stock` - Üretici stok güncellemeleri ✅
- **URL Tabanlı Navigation**: Her sayfa kendi URL'sine sahip, bookmarkable ✅
- **Responsive Layout**: Ortak sidebar ve header ile tutarlı tasarım ✅
- **Stok Bildirimleri**: Üretici stok güncellemeleri, filtreler ✅
- **Responsive Tasarım**: Mobile-first, modern UI/UX ✅
- **Grafik Entegrasyonu**: Chart.js ile interaktif grafikler ✅

### 🧪 Numune Talep Sistemi (RFC-014) ✅ NEW - Tam Entegrasyon
- **Teklif Sonrası Numune**: Müşteriler teklif aldıktan sonra sipariş öncesi numune talep edebilir ✅
- **Üretici Onay Sistemi**: Numune talepleri direkt üreticiye gider, üretici onay/red verir ✅
- **Admin Takip Sistemi**: Admin sadece tüm süreci takip eder, onay vermez ✅
- **Durum Takibi**: 7 aşamalı durum takibi (bekleyen/onaylanan/hazırlanıyor/gönderildi/teslim/değerlendirildi) ✅
- **Müşteri Dashboard**: `/customer/requests/samples` - Numune talep yönetimi ve takip ✅
- **Admin Panel**: `/admin/sample-requests` - Numune süreç takibi ve istatistikler ✅
- **Üretici Dashboard**: Üreticiler numune onay/red, durum güncelleme, kargo bilgisi girişi ✅
- **Değerlendirme Sistemi**: Müşteri numune aldıktan sonra değerlendirme yapabilir ✅
- **ID Takip Sistemi**: Her numune talebi benzersiz ID ile takip edilir ✅
- **Bildirim Sistemi**: Email ve dashboard bildirimleri ✅
- **İş Akışı Yönetimi**: Tam otomatik workflow, durum geçişleri ✅

### 📦 Stok Yönetim Sistemi ✅ NEW - Tam Entegrasyon
- **Üretici Stok Ekleme**: Excel tablosu görünümü ile çoklu stok ürün ekleme ✅
- **Resim Yükleme**: 5 adet stok ürün resmi yükleme özelliği ✅
- **Detaylı Bilgiler**: Metraj (m²), ebat (kalınlık/en/boy), fiyat girişi ✅
- **Admin Onay Sistemi**: Stok taleplerinin admin onayına düşmesi ✅
- **Admin Panel**: `/admin/stock` - Stok onay/red işlemleri ✅
- **Müşteri Görünümü**: `/customer/stock` - Onaylanmış stokları görüntüleme ✅
- **Filtreleme**: Ürün, üretici, fiyat bazlı filtreleme ✅
- **Teklif Entegrasyonu**: Stok ürünlerden direkt teklif isteme ✅

### 🎨 UI/UX Tasarım Sistemi (RFC-004) ✅ IMPLEMENTED
- **Modern Tasarım Dili**: Doğal taş sektörüne özel renk paleti ve tipografi ✅
- **Responsive Design**: Mobile-first yaklaşım, 320px-1536px+ destek ✅
- **Erişilebilirlik**: WCAG 2.1 AA uyumlu, keyboard navigation, screen reader desteği ✅
- **Dark Mode**: Otomatik tema geçişi, kullanıcı tercihi kaydetme ✅
- **Bileşen Kütüphanesi**: Yeniden kullanılabilir React bileşenleri ✅
- **Animasyon Sistemi**: Micro-interactions, hover efektleri, loading animasyonları ✅
- **Sektörel UI Elemanları**: 3D viewer kontrolleri, teklif kartları, durum göstergeleri ✅
- **Çok Dilli Tasarım**: RTL dil desteği, kültürel renk algısı ✅
- **Performance Optimized**: Critical CSS, font optimization, lazy loading ✅

### 🌐 Gelişmiş 3D Görselleştirme ve Sanal Deneyim (RFC-801) ✅ IMPLEMENTED (Fiyatlandırma Kaldırıldı)
- **Ebat Konfigüratörü**: Standart ve özel ebat seçimi ✅
- **Yüzey İşlemi Simülatörü**: 8 farklı yüzey işlemi (ham, honlu, cilalı, vb.) gerçekçi görselleştirme ✅
- **Sanal Mekan Simülasyonu**: Banyo, mutfak, salon şablonları ile döşeme pattern'leri ✅
- **PBR Rendering**: Physically Based Rendering ile gerçekçi materyal gösterimi ✅
- **~~Dinamik Fiyatlandırma~~**: ~~Ebat, yüzey işlemi ve miktar bazlı gerçek zamanlı hesaplama~~ - KALDIRILDI ❌
- **Performans Optimizasyonu**: LOD sistemi, texture streaming, adaptif kalite ✅
- **AR/VR Entegrasyonu**: Mobil AR ürün yerleştirme ve VR showroom deneyimi 🔄
- **Akıllı Öneri Sistemi**: Mekan analizi ve ürün uyumluluğu önerileri 🔄
- **Sosyal Özellikler**: Tasarım paylaşımı ve topluluk galerileri 🔄

## 🛠️ Teknoloji Stack

### Frontend
```json
{
  "react": "^18.2.0",
  "next": "^14.0.0",
  "typescript": "^5.3.0",
  "tailwindcss": "^3.4.0",
  "three": "^0.160.0",
  "@react-three/fiber": "^8.15.0",
  "@react-three/drei": "^9.92.0",
  "cannon-es": "^0.20.0",
  "leva": "^0.9.35"
}
```

### Backend
```json
{
  "node": "20.10.0",
  "express": "^4.18.0",
  "typescript": "^5.3.0",
  "prisma": "^5.7.0",
  "socket.io": "^4.7.0",
  "sharp": "^0.33.0",
  "gltf-pipeline": "^4.1.0",
  "draco3d": "^1.5.6"
}
```

### Veritabanı & Cache
- **PostgreSQL**: v16.1 (Ana veritabanı)
- **Redis**: v7.2.0 (Cache ve session)
- **Elasticsearch**: v8.11.0 (Arama ve analitik)

### AI/ML
- **Python**: v3.11.0
- **FastAPI**: v0.104.0
- **OpenAI GPT-4**: Latest API
- **TensorFlow.js**: v4.15.0

### DevOps
- **Docker**: v24.0.0
- **Kubernetes**: v1.28.0
- **AWS/Azure**: Cloud hosting
- **GitHub Actions**: CI/CD

## 📁 Proje Yapısı

```
├── 📄 README.md                    # Bu dosya
├── 📄 PRD.md                      # Product Requirements Document
├── 📁 RFC/                        # Request for Comments dokümantasyonu
│   ├── 📄 README.md               # RFC sistemi rehberi
│   ├── 📄 RFC-001-System-Architecture.md
│   ├── 📄 RFC-002-Technology-Stack.md
│   ├── 📄 RFC-003-Database-Design.md
│   ├── 📄 RFC-301-Anonymous-Bidding.md
│   ├── 📄 RFC-501-Admin-Dashboard.md
│   ├── 📄 RFC-601-AI-Chatbot.md
│   ├── 📄 RFC-701-3D-Product-View.md
│   ├── 📄 RFC-801-Advanced-3D-Visualization.md
│   └── 📄 RFC-004-UI-UX-Design.md
├── 📁 frontend/                   # Next.js uygulaması
│   ├── 📁 src/
│   │   ├── 📁 components/         # Yeniden kullanılabilir bileşenler
│   │   ├── 📁 pages/             # Sayfa bileşenleri
│   │   ├── 📁 modules/           # İş mantığı modülleri
│   │   ├── 📁 services/          # API servisleri
│   │   ├── 📁 hooks/             # Custom React hooks
│   │   ├── 📁 types/             # TypeScript tip tanımları
│   │   └── 📁 utils/             # Yardımcı fonksiyonlar
│   ├── 📄 package.json
│   └── 📄 next.config.js
├── 📁 backend/                    # Node.js API
│   ├── 📁 src/
│   │   ├── 📁 modules/           # Mikroservis modülleri
│   │   │   ├── 📁 ai/            # AI Chatbot modülü (RFC-601)
│   │   │   │   ├── 📁 services/  # OpenAI, Conversation, Escalation
│   │   │   │   ├── 📁 controllers/ # HTTP request handlers
│   │   │   │   ├── 📁 routes/    # API route definitions
│   │   │   │   └── 📁 types/     # TypeScript interfaces
│   │   ├── 📁 shared/            # Paylaşılan modüller
│   │   ├── 📁 database/          # Veritabanı şemaları
│   │   ├── 📁 middleware/        # Express middleware'ler
│   │   └── 📁 utils/             # Yardımcı fonksiyonlar
│   ├── 📄 package.json
│   └── 📄 prisma/schema.prisma
├── 📁 ai-services/               # Python AI servisleri
│   ├── 📁 chatbot/              # AI chatbot
│   ├── 📁 news-aggregation/     # Haber toplama
│   ├── 📁 marketing-ai/         # Pazarlama AI
│   └── 📄 requirements.txt
├── 📁 mobile/                    # React Native mobil uygulama
├── 📁 docs/                     # Dokümantasyon
├── 📁 tests/                    # Test dosyaları
├── 📁 docker/                   # Docker konfigürasyonları
└── 📁 k8s/                      # Kubernetes manifests
```

## 🚀 Hızlı Başlangıç

### Ön Gereksinimler
- Node.js 20.10.0+
- PostgreSQL 16.1+
- Redis 7.2.0+
- Docker 24.0.0+
- Python 3.11.0+ (AI servisleri için)

### Kurulum

1. **Repository'yi klonlayın**
```bash
git clone https://github.com/your-org/natural-stone-marketplace.git
cd natural-stone-marketplace
```

2. **Bağımlılıkları yükleyin**
```bash
# Frontend
cd frontend
npm install

# Backend
cd ../backend
npm install

# AI Services
cd ../ai-services
pip install -r requirements.txt
```

3. **Veritabanını kurun**
```bash
# PostgreSQL ve Redis'i başlatın
docker-compose up -d postgres redis

# Veritabanı migration'larını çalıştırın
cd backend
npx prisma migrate dev
npx prisma generate
```

4. **Environment variables'ları ayarlayın**
```bash
# .env dosyalarını oluşturun
cp frontend/.env.example frontend/.env.local
cp backend/.env.example backend/.env
cp ai-services/.env.example ai-services/.env
```

5. **Uygulamayı başlatın**
```powershell
# Development modunda tüm servisleri başlat (PowerShell)
npm run dev:all

# Veya ayrı ayrı (PowerShell için ; kullanın, && değil):
npm run dev:frontend    # http://localhost:3000
npm run dev:backend     # http://localhost:8000
npm run dev:ai          # http://localhost:8001

# Çoklu komut örneği (PowerShell):
npm install; npm run build; npm start
```

## 📋 Geliştirme Roadmap'i

### 🎯 Faz 1: Temel Altyapı (0-3 ay)
- [x] Proje kurulumu ve modüler yapı
- [x] Teknoloji stack konfigürasyonu
- [ ] Veritabanı şema implementasyonu
- [ ] Temel kullanıcı sistemi
- [ ] Admin paneli temel yapısı

### 🎯 Faz 2: Ürün ve Teklif Sistemi (3-6 ay)
- [ ] Ürün yönetimi modülü
- [ ] Teklif sistemi (anonimlik kaldırıldı)
- [ ] Ödeme sistemi entegrasyonu
- [ ] Bildirim sistemi
- [x] Müşteri dashboard sistemi - TAMAMLANDI ✅
- [ ] Temel mobil responsive tasarım

### 🎯 Faz 3: Gelişmiş Özellikler (6-9 ay)
- [x] Gelişmiş 3D görselleştirme sistemi (RFC-801)
- [x] Ebat konfigüratörü ve fiyat hesaplama
- [x] Yüzey işlemi simülatörü
- [x] Sanal mekan simülasyonu ve döşeme pattern'leri
- [x] PBR rendering sistemi
- [x] Dinamik fiyatlandırma servisi
- [x] Performans optimizasyonu (LOD, adaptif kalite)
- [ ] AR/VR entegrasyonu
- [ ] AI chatbot sistemi
- [ ] Email marketing otomasyonu

### 🎯 Faz 4: Sanal Fuar ve İleri Özellikler (9-12 ay)
- [ ] Sanal fuar platformu
- [ ] React Native mobil uygulama
- [ ] Gelişmiş analitik dashboard
- [ ] Predictive analytics
- [ ] Global ölçeklendirme

## 🏗️ Mimari Genel Bakış

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
├─────────────────────────────────────────────────────────────┤
│  Web App (Next.js)  │  Mobile App (React Native)  │ Admin  │
├─────────────────────────────────────────────────────────────┤
│                      API GATEWAY                            │
├─────────────────────────────────────────────────────────────┤
│                   MICROSERVICES LAYER                       │
├─────────────────────────────────────────────────────────────┤
│ Auth │ Users │ Products │ Orders │ Payments │ AI │ Notifications │
├─────────────────────────────────────────────────────────────┤
│                     DATA LAYER                              │
├─────────────────────────────────────────────────────────────┤
│  PostgreSQL  │  Redis  │  Elasticsearch  │  File Storage   │
└─────────────────────────────────────────────────────────────┘
```

## 💰 İş Modeli

### Gelir Kaynakları
- **Komisyon**: m² başına $1, blok satışlarında ton başına $10
- **Premium Üyelikler**: Gelişmiş özellikler için aylık abonelik
- **Reklam Gelirleri**: Sponsored listings ve banner reklamlar
- **Ek Hizmetler**: Lojistik, sigorta, finansman hizmetleri

### Hedef Pazar
- **Üreticiler**: 500+ Türk doğal taş üreticisi
- **Müşteriler**: 50+ ülkeden 1000+ ithalatçı ve distribütör
- **İşlem Hacmi**: Yıllık $100M+ hedef

## 🔒 Güvenlik

- **SSL/TLS**: End-to-end şifreleme
- **2FA**: İki faktörlü kimlik doğrulama
- **GDPR/KVKK**: Veri koruma uyumluluğu
- **PCI DSS**: Ödeme güvenliği standartları
- **Regular Audits**: Düzenli güvenlik denetimleri

## 🧪 Test Stratejisi

```bash
# Unit testler
npm run test:unit

# Integration testler
npm run test:integration

# E2E testler
npm run test:e2e

# Performance testler
npm run test:performance

# Security testler
npm run test:security
```

## 📊 Monitoring ve Analytics

- **Application Monitoring**: Prometheus + Grafana
- **Error Tracking**: Sentry
- **User Analytics**: Custom analytics dashboard
- **Performance Monitoring**: Real-time metrics
- **Business Intelligence**: Custom reporting system

## 🌍 Çok Dilli Destek

- **Birincil Diller**: Türkçe, İngilizce
- **İkincil Diller**: Arapça, Almanca, Fransızca, İspanyolca, İtalyanca, Rusça, Çince, Japonca
- **AI Çeviri**: Otomatik çeviri desteği
- **Yerelleştirme**: Kültürel uyarlama

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

### Kod Standartları
- **ESLint**: Kod kalitesi kontrolü
- **Prettier**: Kod formatı
- **Husky**: Git hooks
- **Conventional Commits**: Commit mesaj standardı

## 📚 Dokümantasyon

- **[PRD.md](PRD.md)**: Product Requirements Document
- **[RFC/](RFC/)**: Technical design documents
  - **[RFC-004](RFC/RFC-004-UI-UX-Design.md)**: UI/UX Tasarım Sistemi ve Kullanıcı Deneyimi
  - **[RFC-801](RFC/RFC-801-Advanced-3D-Visualization.md)**: Gelişmiş 3D Görselleştirme
  - **[RFC-601](RFC/RFC-601-AI-Chatbot.md)**: AI Chatbot Sistemi
- **[API Docs](docs/api/)**: API dokümantasyonu
- **[User Guide](docs/user-guide/)**: Kullanıcı rehberi
- **[Developer Guide](docs/developer-guide/)**: Geliştirici rehberi
- **[Design System](docs/design-system/)**: UI/UX tasarım rehberi

## 📞 İletişim

- **Email**: <EMAIL>
- **Website**: https://naturalstone-marketplace.com
- **LinkedIn**: [Company Page](https://linkedin.com/company/naturalstone-marketplace)
- **Twitter**: [@NaturalStoneMP](https://twitter.com/NaturalStoneMP)

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için [LICENSE](LICENSE) dosyasına bakın.

---

## 🎯 RFC Implementation Status

### ✅ Completed Features

#### RFC-301: Anonymous Bidding System
- **Anonymous ID Management**: Secure bidirectional mapping with Redis TTL
- **Real-time Competition Tracking**: Live bid counts and price ranges
- **Escrow Payment System**: 30% upfront payment with admin verification
- **Identity Reveal**: Contact details shared only after payment confirmation
- **Deadline Management**: 48-72 hour bidding windows with automatic expiration

#### RFC-501: Admin Dashboard
- **Producer Approval Workflow**: Document verification and facility inspection
- **Payment Management**: Bank transfer verification and escrow control
- **Commission Tracking**: $1/m² and $10/ton automated calculation
- **Real-time Monitoring**: System health and business metrics
- **Emergency Controls**: Account freezing, payment blocking, transaction cancellation

#### RFC-003: Database Design
- **PostgreSQL Schema**: Complete implementation with Prisma ORM
- **Redis Caching**: High-performance data structures for real-time features
- **Data Security**: Row-level security and encryption ready

### 🔧 Technical Implementation

#### Backend Services
```
src/services/
├── redis/
│   ├── BiddingRedisService.ts      # Real-time bid tracking
│   ├── AnonymousIdService.ts       # Identity management
│   └── BiddingNotificationService.ts # Real-time notifications
├── payment/
│   ├── EscrowPaymentService.ts     # Secure payment handling
│   └── CommissionCalculationService.ts # Platform fees
└── admin/
    └── ProducerApprovalService.ts  # Admin workflows
```

#### API Endpoints
```
/api/bidding/
├── POST /requests                  # Create anonymous bid request
├── POST /requests/:id/bids         # Submit anonymous bid
├── GET /requests/:id/bids          # View anonymous bids
├── POST /requests/:id/select-bid   # Select winning bid
└── POST /escrow/:id/verify         # Admin payment verification

/api/admin/
├── GET /producers/pending          # Pending approvals
├── POST /producers/:id/approve     # Approve producer
├── GET /payments/escrow/pending    # Pending payments
└── GET /dashboard/overview         # Admin dashboard
```

#### Frontend Components
```
src/components/
├── bidding/
│   └── AnonymousBidForm.tsx        # Anonymous bid submission
└── admin/
    └── ProducerApprovalDashboard.tsx # Admin approval interface
```

### 🚀 Quick Start (Updated)

```bash
# 1. Clone and install
git clone <repository-url>
cd mermer-pazaryeri

# 2. Backend setup
cd backend
npm install
cp .env.example .env
# Configure DATABASE_URL and REDIS_URL

# 3. Database setup
npx prisma generate
npx prisma migrate dev

# 4. Start services
npm run dev  # Backend on :8000

# 5. Frontend setup (new terminal)
cd ../frontend
npm install
npm run dev  # Frontend on :3000
```

### 🧪 Testing

```bash
# Run RFC implementation tests
cd backend
npm test src/tests/bidding.test.ts

# Test API endpoints
curl http://localhost:8000/api
curl http://localhost:8000/health
```

## 🎯 RFC-801 Implementation Status

### ✅ Completed Components

#### Advanced 3D Visualization System
- **DimensionConfigurator**: Standart ve özel ebat seçimi, gerçek zamanlı doğrulama
- **SurfaceFinishSimulator**: 8 farklı yüzey işlemi ile PBR materyal geçişleri
- **RoomSimulator**: Mekan şablonları, döşeme pattern'leri ve derz seçenekleri
- **PBRMaterialManager**: Physically Based Rendering materyal sistemi
- **AdvancedProductViewer**: Ana 3D görüntüleyici bileşeni
- **PerformanceOptimizer**: LOD sistemi, adaptif kalite ve performans izleme

#### Dynamic Pricing System
- **DynamicPricingService**: Gerçek zamanlı fiyat hesaplama servisi
- **Price Breakdown**: Detaylı maliyet analizi ve breakdown gösterimi
- **Configuration-based Pricing**: Ebat, yüzey işlemi ve miktar bazlı fiyatlandırma

#### Demo Implementation
- **Advanced Viewer Demo**: `/demo/advanced-3d-viewer` sayfasında tam özellikli demo
- **Interactive Configuration**: Canlı konfigürasyon panelleri
- **Real-time Updates**: Anlık fiyat ve görsel güncellemeler

### 🔧 Technical Features

#### Performance Optimization
```typescript
// LOD System with automatic quality adjustment
const performanceOptimizer = new PerformanceOptimizer({
  enableLOD: true,
  enableAdaptiveQuality: true,
  targetFPS: 60
});

// Device capability detection
const capabilities = detectDeviceCapabilities();
// Returns: { tier: 'high', maxTextureSize: 4096, supportsFloatTextures: true }
```

#### PBR Material System
```typescript
// Surface finish with realistic material properties
const surfaceFinish = {
  name: 'cilali',
  roughness: 0.1,
  metallic: 0.0,
  normalIntensity: 0.1,
  priceMultiplier: 1.5
};
```

#### Dynamic Pricing
```typescript
// Real-time price calculation
const price = await dynamicPricingService.calculatePrice(config, quantity, urgency);
// Returns detailed breakdown with base price, modifiers, and total
```

### 🚀 Quick Start (RFC-801)

```bash
# Navigate to demo
cd frontend
npm run dev

# Visit advanced 3D viewer
http://localhost:3000/demo/advanced-3d-viewer
```

### 📊 Performance Metrics

- **Load Time**: < 3 seconds for high-quality models
- **Frame Rate**: 60+ FPS with adaptive quality
- **Memory Usage**: < 100MB with texture streaming
- **Quality Levels**: 4 adaptive levels (low, medium, high, ultra)

---

## 🤖 AI-Powered Dijital Pazarlama Sistemi ✅ NEW (2025-07-04)

### 🎯 Kapsamlı AI Marketing Orchestrator
- **Çoklu AI Koordinasyonu**: OpenAI GPT-4, Claude, Gemini modellerini koordine eden merkezi sistem ✅
- **Otomatik Pazarlama Döngüsü**: 30 dakikalık döngülerle tüm pazarlama süreçlerini yönetme ✅
- **Task Queue Sistemi**: Öncelik bazlı görev kuyruğu ve akıllı görev dağıtımı ✅
- **Admin Onay Sistemi**: Kritik kararlar için insan onayı gerektiren akıllı sistem ✅
- **Performance Analytics**: Real-time performans izleme ve optimizasyon ✅

### 📧 Email Marketing AI Modülü
- **Ülke Bazlı Email Listeleri**: Otomatik ülke segmentasyonu ve liste yönetimi ✅
  - Müşteri kayıt olduğunda otomatik ülke listesine ekleme
  - Duplicate kontrolü ile aynı email'in tekrar eklenmesini önleme
  - GDPR uyumlu consent yönetimi
- **AI İçerik Üretimi**: OpenAI GPT-4 ile kişiselleştirilmiş email kampanyaları ✅
  - Ürün kategorisine göre özel içerik üretimi
  - Çoklu dil desteği ve yerelleştirme
  - A/B testing ile optimizasyon
- **Optimal Zamanlama**: Ülke saat dilimlerine göre en uygun gönderim zamanı ✅

### 📱 Sosyal Medya AI Yönetimi
- **Platform Entegrasyonu**: Facebook, Instagram, LinkedIn, Twitter, YouTube, TikTok ✅
- **AI İçerik Üretimi**: Platform özelliklerine göre optimize edilmiş içerik ✅
  - Platform bazlı karakter sınırları ve format optimizasyonu
  - Hashtag stratejileri ve engagement optimizasyonu
  - Ton ve stil uyarlaması (profesyonel, samimi, görsel odaklı)
- **Admin Onay Süreci**: Risk seviyesine göre otomatik/manuel onay sistemi ✅
- **Performance Tracking**: Real-time engagement ve reach metrikleri ✅

### 🎯 Müşteri Arama AI (Planlı - Faz 2)
- **Google Maps API**: İnşaat firmaları, mimarlar, distribütörler arama 🔄
- **LinkedIn API**: Profesyonel ağ taraması ve lead generation 🔄
- **AI Profilleme**: Şirket büyüklüğü, potansiyel değer hesaplama 🔄
- **İlk Temas Stratejisi**: Kişiselleştirilmiş outreach mesajları 🔄

### 💰 Reklam Yönetimi AI (Planlı - Faz 3)
- **Google Ads API**: Search, Display, Shopping, YouTube reklamları 🔄
- **Facebook Ads API**: Facebook ve Instagram reklam optimizasyonu 🔄
- **LinkedIn Ads API**: B2B odaklı profesyonel reklam yönetimi 🔄
- **Otomatik Optimizasyon**: Real-time bid ve audience optimization 🔄

### 🎛️ Admin Panel Entegrasyonu
- **AI Marketing Dashboard**: `/admin/ai-marketing` - Kapsamlı AI sistem yönetimi ✅
- **System Status Monitoring**: Tüm AI modüllerinin real-time durumu ✅
- **Email Marketing Panel**: `/admin/ai-marketing/email` - Ülke listeleri ve kampanya yönetimi ✅
- **Performance Metrics**: KPI kartları ve detaylı analitik ✅

### 🔧 Teknik Özellikler
- **Modüler Backend Yapısı**: `src/modules/ai-marketing/` altında organize edilmiş modüller ✅
- **TypeScript Interfaces**: Kapsamlı tip tanımları ve type safety ✅
- **API Routes**: RESTful API endpoints ile frontend-backend entegrasyonu ✅
- **Event-Driven Architecture**: EventEmitter ile loosely coupled sistem ✅
- **Mock Data Integration**: Test için hazır veri setleri ✅

### 🔒 Güvenlik ve Uyumluluk
- **API Key Management**: Güvenli AI API anahtarı yönetimi ✅
- **GDPR/KVKK Compliance**: Veri koruma kanunlarına uyumluluk ✅
- **Audit Logging**: Tüm AI işlemlerinin detaylı loglanması ✅
- **Role-based Access**: Admin seviyesinde erişim kontrolü ✅

## 🔄 Son Güncellemeler (2025-01-13)

### ✅ Kapsamlı Ödeme Sistemi Database Migration Tamamlandı (2025-01-13)
- **PaymentType Enum**: 5 farklı ödeme aşaması (FULL_PAYMENT, ADVANCE_PAYMENT, DELIVERY_PAYMENT, FINAL_PAYMENT, COMMISSION_PAYMENT)
- **CommissionTracking Tablosu**: Platform komisyon takibi (PRD: m² başına $1, ton başına $10)
- **PaymentSchedule Tablosu**: Çoklu teslimat ödemeleri için zamanlama sistemi
- **PaymentTracking Tablosu**: Ödeme durumu değişikliklerinin detaylı takibi
- **EscrowTransactionLog Tablosu**: Emanet hesap işlemlerinin audit trail'i
- **Payment Tablosu Geliştirmeleri**: Multi-delivery desteği için ek alanlar
- **Otomatik Komisyon Hesaplama**: Trigger ile otomatik komisyon hesaplama
- **Performance İndeksleri**: Sorgu optimizasyonu için gerekli indeksler
- **Migration Scripts**: PowerShell ve JavaScript validation scriptleri
- **Foreign Key İlişkileri**: Veri bütünlüğü için ilişkisel kısıtlamalar

### 🔧 Teknik Özellikler
- **Emanet Ödeme Sistemi**: Müşteri → Platform → Üretici ödeme akışı
- **Banka Havalesi Desteği**: Dekont yükleme ve admin onay sistemi
- **Çoklu Teslimat Ödemeleri**: RFC-015 uyumlu paket bazlı ödeme sistemi
- **Komisyon Gizliliği**: Komisyon miktarları sadece admin görebilir
- **Audit Trail**: Tüm ödeme ve emanet işlemlerinin detaylı loglanması
- **Real-time Tracking**: Ödeme durumu değişikliklerinin anlık takibi

## 🔄 Önceki Güncellemeler (2025-01-07)

### ✅ Çoklu Teslimat Sistemi Tamamlandı (2025-01-07)
- **Büyük Sipariş Yönetimi**: 500m² veya $25,000 üzeri siparişler için özel sistem
- **Müşteri Onayı Zorunlu**: Çoklu teslimat müşteri onayı olmadan oluşmaz
- **Teklif Aşaması Entegrasyonu**: `/producer/quote-requests/pending` - Çoklu teslimat teklif sistemi
- **Müşteri Onay Arayüzü**: Paket seçimi, şart onayı, değişiklik talepleri
- **Paket Bazlı Yönetim**: Ayrı üretim, teslimat ve ödeme takvimleri
- **Shipped Orders Sayfası**: `/producer/orders/shipped` - Sevk edilmiş siparişlerin takibi
- **Modal Düzeltmeleri**: Dropdown sorunları çözüldü, z-index optimizasyonu
- **Doğru İş Akışı**: Teklif → Müşteri Onayı → Sipariş → Üretim
- **RFC-015 Dokümantasyonu**: Kapsamlı sistem dokümantasyonu oluşturuldu

## 🔄 Önceki Güncellemeler (2025-06-30)

### ✅ Kapsamlı Admin Yönetim Sistemi Tamamlandı (2025-07-01)
- **Admin Panel Tasarım Düzeltmeleri**: Sol menü sabit pozisyon, uygun padding ve boşluklar
- **Ürün Onay Sayfası**: `/admin/product-approvals` - Bekleyen ürünleri görüntüleme ve onaylama
- **Tam Ekran Detay Sayfası**: `/admin/product-approvals/[id]` - 10 adımın tüm detaylarını görüntüleme
- **Detaylı Ocak Bilgileri**: Ocak adı, sahibi, konum, adres, kapasite, iletişim, ocaktan çıkan tüm taşlar
- **Ürün Detay Sayfası**: `/admin/products/[id]` - Kapsamlı ürün yönetimi
  - Genel bakış, üretici listesi, ocak bilgileri, fiyat analizi
  - Gerçek zamanlı stok takibi, üretici performans metrikleri
- **Üretici Profil Sayfası**: `/admin/producers/[id]` - Kapsamlı üretici yönetimi
  - Firma bilgileri, ürün portföyü, ocak yönetimi
  - Performans analizi, müşteri portföyü, finansal durum
- **İstatistik ve Stok Modalları**: Detaylı performans analizi ve stok yönetimi
  - İstatistik modalı: Performans, müşteriler, üreticiler, fiyat geçmişi
  - Stok modalı: Üretici bazlı detaylı stok görünümü, ebat/fiyat bilgileri
- **Müşteri Yönetim Sistemi**: `/admin/customers` - Kapsamlı müşteri profil yönetimi
  - Müşteri listesi, sektör filtreleme, performans metrikleri
  - Müşteri profili: Sipariş geçmişi, üretici ilişkileri, analitik
- **Güncel Talepler Sistemi**: `/admin/quote-requests` - Teklif süreç yönetimi
  - Tüm teklif taleplerini izleme, durum takibi
  - Gelen tekliflerin karşılaştırması, fiyat analizi
- **Gerçek Zamanlı Stok Sistemi**: Üretici bazlı stok takibi ve otomatik güncelleme
- **Tıklanabilir Navigasyon**: Üretici/müşteri adlarına tıklayarak profil sayfasına geçiş

### ✅ Ocak Tabanlı Ürün Yönetim Sistemi (2025-06-30)
- **RFC-011**: Quarry-Based Product Management System tasarımı tamamlandı
- **Ocak Sistemi**: Her ürün belirli bir ocağa bağlı, aynı ürün tekrarı önleniyor
- **Tam Ekran Form**: `/producer/products/add` sayfası ile daha iyi kullanıcı deneyimi
- **Taş Seçimi**: Ocaktan çıkan taşları resimlerle görüntüleme ve seçim
- **Üretici Listesi**: Her ürün için birden fazla üretici desteği
- **Admin Panel**: Ürün-üretici ilişkilerini görüntüleme ve yönetme
- **Gizlilik**: Üretici listesi sadece admin panelinde görünür
- **Veri Modeli**: Quarry, QuarryProduct, ProducerProduct interfaces oluşturuldu

### ✅ Önceki Güncellemeler (2025-06-29)

### ✅ Tamamlanan Değişiklikler
- **Anonimlik Sistemi Kaldırıldı**: Tüm arayüzden anonim teklif referansları kaldırıldı
- **Ürün Detay Sayfası Basitleştirildi**: Fiyat, değerlendirme, özellikler bölümleri kaldırıldı
- **Teklif Formu Güncellemeleri**: Çoklu ürün seçimi, metrik ölçümler, üye girişi zorunlu
- **3D Görüntüleyici**: Fiyatlandırma özellikleri kaldırıldı, sadece görselleştirme
- **Müşteri Kayıt**: Basitleştirilmiş form, adım adım doğrulama
- **PowerShell Desteği**: Çoklu komut çalıştırma için `;` operatörü kullanımı
- **Müşteri Dashboard Sistemi**: Tam implementasyon tamamlandı ✅
- **Giriş Sonrası Yönlendirme**: Otomatik /customer/dashboard yönlendirmesi ✅
- **Ürün Kartları Optimizasyonu**: Fiyat kaldırma ve buton aktivasyonu ✅
- **Kapsamlı Teklif Yönetim Sistemi**: Tam entegre teklif süreci ✅
- **Üretici Giriş/Kayıt Sistemi**: Ana sayfa footer'ında ayrı üretici girişi ✅
- **Üretici Dashboard Sistemi**: Tam implementasyon tamamlandı ✅
- **Detaylı Üretici Kayıt Formu**: 6 adımlı kapsamlı kayıt süreci ✅
- **Ürün Yönetim Sistemi**: CRUD işlemleri, görsel yükleme, teknik özellikler ✅
- **Gelişmiş Teklif Verme**: Detaylı teklif modalı, fiyat hesaplama ✅
- **Sipariş Takibi**: Üretim aşamaları, durum güncellemeleri ✅
- **Satış Analizleri**: Grafik tabanlı raporlar, trend analizleri ✅

### 🔧 Teknik Güncellemeler
- **Otomatik Dokümantasyon**: Tüm değişiklikler PRD.md, README.md ve RFC dosyalarında otomatik güncelleniyor
- **Versiyon Takibi**: Her güncelleme için detaylı changelog
- **PowerShell Uyumluluğu**: Windows geliştirme ortamı için optimize edildi
- **Chart.js Entegrasyonu**: İnteraktif grafikler ve veri görselleştirme
- **Framer Motion**: Smooth animasyonlar ve modern UX
- **TypeScript**: Tip güvenli kod geliştirme
- **Next.js 14**: App Router ve modern React özellikleri
- **Next.js Middleware**: Route protection ve authentication kontrolü
- **Cookie Management**: Session yönetimi ve middleware entegrasyonu
- **Quote Management System**: Kapsamlı teklif talep ve yönetim sistemi
- **Producer Interface**: Üreticiler için teklif verme arayüzü
- **Producer Authentication System**: Üreticiler için ayrı giriş/kayıt sistemi
- **Producer Dashboard**: Ürün yönetimi, teklif talepleri, sipariş takibi
- **Admin Settings System**: Kapsamlı sistem ayarları yönetimi
  - Modüler settings kategorileri (Platform, Security, Business, Notification, System, Integration)
  - Validation engine ile güvenli ayar değişiklikleri
  - Audit log sistemi ile değişiklik takibi
  - Real-time settings güncellemeleri
  - Import/Export işlevselliği
  - Role-based access control

---

**Proje Durumu**: ✅ AI-Powered Dijital Pazarlama Sistemi Implementasyonu Tamamlandı
**Son Güncelleme**: 2025-07-04
**Versiyon**: 4.0.0-ai-digital-marketing-system-complete

Made with ❤️ for Turkish Natural Stone Industry
