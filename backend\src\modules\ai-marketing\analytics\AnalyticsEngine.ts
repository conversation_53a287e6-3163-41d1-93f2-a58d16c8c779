// RFC-013: Analytics Engine
// Performans analizi ve raporlama sistemi

import { EventEmitter } from 'events';
import { MarketingTask, TaskResult } from '../types/ai-marketing.types';

interface PerformanceMetric {
  id: string;
  taskId: string;
  aiModel: string;
  executionTime: number;
  success: boolean;
  timestamp: Date;
  metadata?: any;
}

export class AnalyticsEngine extends EventEmitter {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private taskHistory: Map<string, TaskResult[]> = new Map();

  constructor() {
    super();
  }

  public async trackTaskPerformance(task: MarketingTask, result: TaskResult): Promise<void> {
    const metric: PerformanceMetric = {
      id: `metric-${Date.now()}`,
      taskId: task.id,
      aiModel: result.aiModel,
      executionTime: result.executionTime,
      success: result.success,
      timestamp: result.timestamp,
      metadata: {
        taskType: task.type,
        priority: task.priority,
        requiresApproval: task.requiresApproval
      }
    };

    this.metrics.set(metric.id, metric);

    // Track task history
    if (!this.taskHistory.has(result.aiModel)) {
      this.taskHistory.set(result.aiModel, []);
    }
    this.taskHistory.get(result.aiModel)!.push(result);

    console.log(`Performance tracked for task: ${task.id}`);
    this.emit('performanceTracked', metric);
  }

  public async analyzePerformance(): Promise<any> {
    const metrics = Array.from(this.metrics.values());
    
    if (metrics.length === 0) {
      return {
        totalTasks: 0,
        successRate: 0,
        averageExecutionTime: 0,
        aiModelPerformance: {}
      };
    }

    const successfulTasks = metrics.filter(m => m.success);
    const successRate = (successfulTasks.length / metrics.length) * 100;
    const averageExecutionTime = metrics.reduce((sum, m) => sum + m.executionTime, 0) / metrics.length;

    // AI model performance breakdown
    const aiModelPerformance: any = {};
    const modelGroups = this.groupBy(metrics, 'aiModel');

    for (const [model, modelMetrics] of Object.entries(modelGroups)) {
      const metricsArray = modelMetrics as PerformanceMetric[];
      const modelSuccessful = metricsArray.filter((m: PerformanceMetric) => m.success);
      aiModelPerformance[model] = {
        totalTasks: metricsArray.length,
        successRate: (modelSuccessful.length / metricsArray.length) * 100,
        averageExecutionTime: metricsArray.reduce((sum: number, m: PerformanceMetric) => sum + m.executionTime, 0) / metricsArray.length,
        lastExecution: Math.max(...metricsArray.map((m: PerformanceMetric) => m.timestamp.getTime()))
      };
    }

    return {
      totalTasks: metrics.length,
      successRate,
      averageExecutionTime,
      aiModelPerformance,
      timeRange: {
        start: Math.min(...metrics.map(m => m.timestamp.getTime())),
        end: Math.max(...metrics.map(m => m.timestamp.getTime()))
      }
    };
  }

  public async getCompletedTasksCount(): Promise<number> {
    return Array.from(this.metrics.values()).filter(m => m.success).length;
  }

  public async getErrorCount(): Promise<number> {
    return Array.from(this.metrics.values()).filter(m => !m.success).length;
  }

  public getTaskHistory(aiModel?: string): TaskResult[] {
    if (aiModel) {
      return this.taskHistory.get(aiModel) || [];
    }
    
    // Return all task history
    const allHistory: TaskResult[] = [];
    for (const history of this.taskHistory.values()) {
      allHistory.push(...history);
    }
    
    return allHistory.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  public generateReport(timeRange?: { start: Date; end: Date }): any {
    let metrics = Array.from(this.metrics.values());
    
    if (timeRange) {
      metrics = metrics.filter(m => 
        m.timestamp >= timeRange.start && m.timestamp <= timeRange.end
      );
    }

    const taskTypes = this.groupBy(metrics, 'metadata.taskType');
    const hourlyDistribution = this.getHourlyDistribution(metrics);
    const trends = this.calculateTrends(metrics);

    return {
      summary: {
        totalTasks: metrics.length,
        successRate: (metrics.filter(m => m.success).length / metrics.length) * 100,
        averageExecutionTime: metrics.reduce((sum, m) => sum + m.executionTime, 0) / metrics.length
      },
      taskTypeBreakdown: Object.entries(taskTypes).map(([type, typeMetrics]) => {
        const metricsArray = typeMetrics as PerformanceMetric[];
        return {
          type,
          count: metricsArray.length,
          successRate: (metricsArray.filter((m: PerformanceMetric) => m.success).length / metricsArray.length) * 100
        };
      }),
      hourlyDistribution,
      trends,
      generatedAt: new Date()
    };
  }

  private groupBy(array: any[], key: string): any {
    return array.reduce((groups, item) => {
      const value = this.getNestedValue(item, key);
      const group = groups[value] || [];
      group.push(item);
      groups[value] = group;
      return groups;
    }, {});
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private getHourlyDistribution(metrics: PerformanceMetric[]): any {
    const hourly = new Array(24).fill(0);
    
    metrics.forEach(metric => {
      const hour = metric.timestamp.getHours();
      hourly[hour]++;
    });

    return hourly.map((count, hour) => ({ hour, count }));
  }

  private calculateTrends(metrics: PerformanceMetric[]): any {
    // Simple trend calculation - can be enhanced
    const sortedMetrics = metrics.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    
    if (sortedMetrics.length < 2) {
      return { trend: 'stable', change: 0 };
    }

    const firstHalf = sortedMetrics.slice(0, Math.floor(sortedMetrics.length / 2));
    const secondHalf = sortedMetrics.slice(Math.floor(sortedMetrics.length / 2));

    const firstHalfSuccessRate = (firstHalf.filter(m => m.success).length / firstHalf.length) * 100;
    const secondHalfSuccessRate = (secondHalf.filter(m => m.success).length / secondHalf.length) * 100;

    const change = secondHalfSuccessRate - firstHalfSuccessRate;
    
    return {
      trend: change > 5 ? 'improving' : change < -5 ? 'declining' : 'stable',
      change: Math.round(change * 100) / 100
    };
  }
}
