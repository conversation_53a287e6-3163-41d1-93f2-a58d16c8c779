'use client';

import React, { Suspense } from 'react';
import { Navigation } from "@/components/ui/navigation";
import { Button } from "@/components/ui/button";
import dynamic from 'next/dynamic';

// Dynamically import 3D component to avoid SSR issues
const Simple3DTest = dynamic(
  () => import('@/components/3d-showroom/test/Simple3DTest'),
  {
    ssr: false,
    loading: () => (
      <div className="w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-amber-200 border-t-amber-600 rounded-full animate-spin mx-auto mb-2"></div>
          <p className="text-gray-600">3D Test Yükleniyor...</p>
        </div>
      </div>
    )
  }
);

const navigationLinks = [
  { name: "Ana Sayfa", href: "/" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/products" },
  { name: "3D Sanal Showroom", href: "/3d-showroom", active: true },
  { name: "<PERSON><PERSON><PERSON>", href: "/news" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/about" },
  { name: "İletişim", href: "/contact" }
];

export default function ThreeDShowroomTestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation
        brand={{
          name: "Türkiye Doğal Taş Pazarı",
          href: "/"
        }}
        links={navigationLinks}
        actions={
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              Giriş Yap
            </Button>
            <Button variant="primary" size="sm">
              Kayıt Ol
            </Button>
          </div>
        }
      />

      <main className="flex h-screen pt-16">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              3D Showroom Test Sayfası
            </h1>
            <p className="text-gray-600 mb-8">
              3D Showroom bileşenleri test ediliyor...
            </p>
            
            <div className="space-y-4">
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold mb-2">✅ Navigation</h3>
                <p className="text-sm text-gray-600">Navigation bileşeni çalışıyor</p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold mb-4">🎮 3D Engine Test</h3>
                <Simple3DTest />
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold mb-2">⏳ Product System</h3>
                <p className="text-sm text-gray-600">Ürün sistemi hazırlanıyor...</p>
              </div>
            </div>
            
            <div className="mt-8">
              <Button 
                onClick={() => window.location.href = '/3d-showroom'}
                variant="primary"
              >
                Ana 3D Showroom'a Git
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
