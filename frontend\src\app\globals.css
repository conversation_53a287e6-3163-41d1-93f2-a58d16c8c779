@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dashboard Styles Import */
@import '../styles/dashboard.css';

/* RFC-004 UI/UX Design System - CSS Custom Properties */
:root {
  /* Primary Colors - Natural Stone Theme */
  --primary-stone: #8B7355;
  --primary-dark: #5D4E37;
  --primary-light: #A68B5B;

  /* Secondary Colors */
  --secondary-marble: #F5F5DC;
  --secondary-granite: #2F4F4F;
  --secondary-travertine: #DEB887;

  /* System Colors */
  --success: #22C55E;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;

  /* Gray Scale */
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;

  /* Background and Text */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --border-primary: #E5E7EB;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-secondary: 'Playfair Display', Georgia, serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  --text-6xl: 3.75rem;   /* 60px */

  /* Font Weights */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Spacing Scale */
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  --space-32: 8rem;     /* 128px */

  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius: 0.25rem;       /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;   /* Full circle */

  /* Box Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Transitions */
  --transition-base: all 0.2s ease;
  --transition-colors: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
  --transition-transform: transform 0.3s ease;
  --transition-opacity: opacity 0.3s ease;
}

/* Dark Mode Theme */
[data-theme="dark"] {
  --bg-primary: #111827;
  --bg-secondary: #1F2937;
  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --border-primary: #374151;

  /* Adjusted Natural Stone Colors for Dark Mode */
  --primary-stone: #A68B5B;
  --primary-dark: #8B7355;
  --primary-light: #C4A373;

  --secondary-marble: #2D2D2D;
  --secondary-granite: #1A1A1A;
  --secondary-travertine: #3D3D3D;

  /* Inverted Gray Scale for Dark Mode */
  --gray-50: #1F2937;
  --gray-100: #374151;
  --gray-200: #4B5563;
  --gray-300: #6B7280;
  --gray-400: #9CA3AF;
  --gray-500: #D1D5DB;
  --gray-600: #E5E7EB;
  --gray-700: #F3F4F6;
  --gray-800: #F9FAFB;
  --gray-900: #FFFFFF;
}

/* System Dark Mode Preference */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --bg-primary: #111827;
    --bg-secondary: #1F2937;
    --text-primary: #F9FAFB;
    --text-secondary: #D1D5DB;
    --border-primary: #374151;

    --primary-stone: #A68B5B;
    --primary-dark: #8B7355;
    --primary-light: #C4A373;
  }
}

/* Base Styles */
body {
  color: var(--text-primary);
  background: var(--bg-primary);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: 1.5;
  transition: var(--transition-colors);
}

/* Global Transition for Theme Changes */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
