'use client'

import * as React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  DollarSign, 
  Calendar, 
  CreditCard, 
  Package,
  CheckCircle,
  Clock,
  AlertTriangle,
  Receipt,
  TrendingUp
} from 'lucide-react'
import { DeliveryPackage, PackagePayment } from '@/types/multi-delivery'

interface PaymentScheduleCardProps {
  deliveryPackage: DeliveryPackage
  onRecordPayment: (packageId: string, paymentId: string) => void
  onSendReminder: (packageId: string, paymentId: string) => void
  onViewInvoice: (packageId: string, paymentId: string) => void
}

export function PaymentScheduleCard({
  deliveryPackage,
  onRecordPayment,
  onSendReminder,
  onViewInvoice
}: PaymentScheduleCardProps) {
  
  const getPaymentStatusColor = (status: PackagePayment['status']) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-yellow-100 text-yellow-800'
    }
  }

  const getPaymentStatusText = (status: PackagePayment['status']) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'paid':
        return 'Ödendi'
      case 'overdue':
        return 'Gecikmiş'
      case 'cancelled':
        return 'İptal'
      default:
        return status
    }
  }

  const getPaymentTypeText = (type: PackagePayment['paymentType']) => {
    switch (type) {
      case 'advance':
        return 'Avans'
      case 'delivery':
        return 'Teslimat'
      case 'completion':
        return 'Tamamlama'
      default:
        return type
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`
  }

  const isOverdue = (dueDate: string, status: PackagePayment['status']) => {
    if (status === 'paid') return false
    return new Date(dueDate) < new Date()
  }

  // Update overdue payments
  React.useEffect(() => {
    deliveryPackage.payments.forEach(payment => {
      if (payment.status === 'pending' && isOverdue(payment.dueDate, payment.status)) {
        // In real implementation, this would update the backend
        payment.status = 'overdue'
      }
    })
  }, [deliveryPackage.payments])

  // Calculate payment statistics
  const paymentStats = React.useMemo(() => {
    const totalAmount = deliveryPackage.amount
    const paidAmount = deliveryPackage.payments
      .filter(p => p.status === 'paid')
      .reduce((sum, p) => sum + p.amount, 0)
    const pendingAmount = deliveryPackage.payments
      .filter(p => p.status === 'pending')
      .reduce((sum, p) => sum + p.amount, 0)
    const overdueAmount = deliveryPackage.payments
      .filter(p => p.status === 'overdue')
      .reduce((sum, p) => sum + p.amount, 0)
    
    return {
      totalAmount,
      paidAmount,
      pendingAmount,
      overdueAmount,
      remainingAmount: totalAmount - paidAmount,
      paymentPercentage: Math.round((paidAmount / totalAmount) * 100)
    }
  }, [deliveryPackage.payments, deliveryPackage.amount])

  const getPackageStatusColor = () => {
    if (paymentStats.overdueAmount > 0) return 'border-red-200 bg-red-50'
    if (paymentStats.remainingAmount === 0) return 'border-green-200 bg-green-50'
    if (paymentStats.paidAmount > 0) return 'border-blue-200 bg-blue-50'
    return 'border-gray-200 bg-gray-50'
  }

  return (
    <Card className={`overflow-hidden ${getPackageStatusColor()}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Package className="w-5 h-5" />
            Paket #{deliveryPackage.packageNumber} - Ödemeler
          </CardTitle>
          <Badge className={paymentStats.remainingAmount === 0 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
            {paymentStats.remainingAmount === 0 ? 'Tamamlandı' : 'Devam Ediyor'}
          </Badge>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <span className="font-medium">Miktar:</span> {deliveryPackage.quantity} m²
          </div>
          <div>
            <span className="font-medium">Toplam Tutar:</span> {formatCurrency(deliveryPackage.amount)}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Payment Progress */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Ödeme İlerlemesi</span>
            <span className="text-sm text-gray-600">
              {formatCurrency(paymentStats.paidAmount)} / {formatCurrency(paymentStats.totalAmount)}
            </span>
          </div>
          <Progress value={paymentStats.paymentPercentage} className="h-2" />
          <div className="text-xs text-gray-500 mt-1">
            %{paymentStats.paymentPercentage} ödendi
          </div>
        </div>

        {/* Payment Summary */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div className="bg-green-50 p-3 rounded-lg text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span className="text-xs font-medium text-green-800">Ödendi</span>
            </div>
            <p className="text-sm font-bold text-green-900">{formatCurrency(paymentStats.paidAmount)}</p>
          </div>
          
          <div className="bg-yellow-50 p-3 rounded-lg text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Clock className="w-4 h-4 text-yellow-600" />
              <span className="text-xs font-medium text-yellow-800">Bekliyor</span>
            </div>
            <p className="text-sm font-bold text-yellow-900">{formatCurrency(paymentStats.pendingAmount)}</p>
          </div>
          
          <div className="bg-red-50 p-3 rounded-lg text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <AlertTriangle className="w-4 h-4 text-red-600" />
              <span className="text-xs font-medium text-red-800">Gecikmiş</span>
            </div>
            <p className="text-sm font-bold text-red-900">{formatCurrency(paymentStats.overdueAmount)}</p>
          </div>
          
          <div className="bg-gray-50 p-3 rounded-lg text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <TrendingUp className="w-4 h-4 text-gray-600" />
              <span className="text-xs font-medium text-gray-800">Kalan</span>
            </div>
            <p className="text-sm font-bold text-gray-900">{formatCurrency(paymentStats.remainingAmount)}</p>
          </div>
        </div>

        {/* Overdue Warning */}
        {paymentStats.overdueAmount > 0 && (
          <div className="bg-red-50 p-3 rounded-lg border border-red-200">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium text-red-800">Gecikmiş Ödeme!</p>
                <p className="text-sm text-red-700 mt-1">
                  {formatCurrency(paymentStats.overdueAmount)} tutarında gecikmiş ödeme bulunmaktadır.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Payment List */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
            <CreditCard className="w-4 h-4" />
            Ödeme Planı
          </h4>
          <div className="space-y-2">
            {deliveryPackage.payments
              .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
              .map((payment) => (
                <div
                  key={payment.id}
                  className={`flex items-center justify-between p-3 rounded border ${
                    payment.status === 'paid' ? 'bg-green-50 border-green-200' :
                    payment.status === 'overdue' ? 'bg-red-50 border-red-200' :
                    isOverdue(payment.dueDate, payment.status) ? 'bg-red-50 border-red-200' :
                    'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-1">
                      <Badge className={getPaymentStatusColor(payment.status)} variant="outline">
                        {getPaymentStatusText(payment.status)}
                      </Badge>
                      <span className="font-medium text-sm">{getPaymentTypeText(payment.paymentType)}</span>
                      <span className="font-bold">{formatCurrency(payment.amount)}</span>
                    </div>
                    
                    <div className="text-xs text-gray-600 space-y-1">
                      <div className="flex items-center gap-4">
                        <span>Vade: {formatDate(payment.dueDate)}</span>
                        {payment.paidDate && (
                          <span>Ödeme: {formatDate(payment.paidDate)}</span>
                        )}
                      </div>
                      {payment.transactionId && (
                        <div>İşlem No: {payment.transactionId}</div>
                      )}
                      {payment.notes && (
                        <div>{payment.notes}</div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex gap-1">
                    {payment.status === 'pending' && (
                      <>
                        <Button
                          size="sm"
                          onClick={() => onRecordPayment(deliveryPackage.id, payment.id)}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Ödendi
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onSendReminder(deliveryPackage.id, payment.id)}
                        >
                          Hatırlat
                        </Button>
                      </>
                    )}
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onViewInvoice(deliveryPackage.id, payment.id)}
                    >
                      <Receipt className="w-4 h-4 mr-1" />
                      Fatura
                    </Button>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
