import { Router, Request, Response, NextFunction } from 'express';
import { FileUploadController } from '../../controllers/FileUploadController';
import { authMiddleware } from '../../middleware/authMiddleware';
import { asyncHandler } from '../../middleware/errorHandler';
import { strictFileValidation, standardFileValidation } from '../../middleware/fileValidation';

const router = Router();
const fileUploadController = new FileUploadController();

// Apply authentication middleware to all routes
router.use(authMiddleware);

/**
 * @route POST /api/upload/product-images
 * @desc Upload multiple product images
 * @access Private (Producers only)
 */
router.post('/product-images',
  // Check if user is producer
  asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    const user = req.user;
    if (!user || user.userType !== 'producer') {
      return res.status(403).json({
        success: false,
        error: 'Only producers can upload product images'
      });
    }
    next();
  }),
  strictFileValidation, // Add strict file validation for product images
  fileUploadController.uploadProductImages
);

/**
 * @route POST /api/upload/product-image
 * @desc Upload single product image
 * @access Private (Producers only)
 */
router.post('/product-image',
  // Check if user is producer
  asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    const user = req.user;
    if (!user || user.userType !== 'producer') {
      return res.status(403).json({
        success: false,
        error: 'Only producers can upload product images'
      });
    }
    next();
  }),
  fileUploadController.uploadProductImage
);

/**
 * @route POST /api/upload/document
 * @desc Upload document (analysis reports, certificates, etc.)
 * @access Private
 */
router.post('/document',
  standardFileValidation, // Add standard file validation for documents
  fileUploadController.uploadDocument
);

/**
 * @route POST /api/upload/avatar
 * @desc Upload user avatar
 * @access Private
 */
router.post('/avatar',
  strictFileValidation, // Add strict file validation for avatars
  fileUploadController.uploadAvatar
);

/**
 * @route POST /api/upload/3d-asset
 * @desc Upload 3D asset
 * @access Private (Producers only)
 */
router.post('/3d-asset',
  // Check if user is producer
  asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    const user = req.user;
    if (!user || user.userType !== 'producer') {
      return res.status(403).json({
        success: false,
        error: 'Only producers can upload 3D assets'
      });
    }
    next();
  }),
  fileUploadController.upload3DAsset
);

/**
 * @route DELETE /api/upload/file
 * @desc Delete uploaded file
 * @access Private
 */
router.delete('/file', fileUploadController.deleteFile);

/**
 * @route GET /api/upload/signed-url
 * @desc Get signed URL for private file access (S3 only)
 * @access Private
 */
router.get('/signed-url', fileUploadController.getSignedUrl);

/**
 * @route GET /api/upload/config
 * @desc Get upload configuration and limits
 * @access Private
 */
router.get('/config', asyncHandler(async (req: Request, res: Response) => {
  const config = {
    maxFileSize: {
      productImages: 5 * 1024 * 1024, // 5MB
      documents: 10 * 1024 * 1024, // 10MB
      avatars: 2 * 1024 * 1024, // 2MB
      assets3D: 100 * 1024 * 1024, // 100MB
    },
    allowedTypes: {
      productImages: ['jpg', 'jpeg', 'png', 'webp'],
      documents: ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
      avatars: ['jpg', 'jpeg', 'png'],
      assets3D: ['glb', 'gltf', 'fbx', 'obj', 'jpg', 'jpeg', 'png', 'webp', 'hdr', 'exr'],
    },
    storageType: process.env.AWS_S3_ENABLED === 'true' ? 's3' : 'local',
    imageProcessing: {
      productImage: {
        maxWidth: 1200,
        maxHeight: 1200,
        quality: 85,
        format: 'webp',
        generateThumbnail: true,
      },
      avatar: {
        maxWidth: 200,
        maxHeight: 200,
        quality: 85,
        format: 'webp',
      },
    },
  };

  res.json({
    success: true,
    data: config
  });
}));

/**
 * @route GET /api/upload/health
 * @desc Check upload service health
 * @access Private (Admin only)
 */
router.get('/health', 
  asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    const user = req.user;
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }
    next();
  }),
  asyncHandler(async (req: Request, res: Response) => {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      storage: {
        type: process.env.AWS_S3_ENABLED === 'true' ? 's3' : 'local',
        configured: true,
      },
      services: {
        imageProcessing: true,
        fileValidation: true,
      },
    };

    // Check S3 configuration if enabled
    if (process.env.AWS_S3_ENABLED === 'true') {
      health.storage.configured = !!(
        process.env.AWS_ACCESS_KEY_ID &&
        process.env.AWS_SECRET_ACCESS_KEY &&
        process.env.AWS_S3_BUCKET
      );
    }

    res.json({
      success: true,
      data: health
    });
  })
);

export default router;
