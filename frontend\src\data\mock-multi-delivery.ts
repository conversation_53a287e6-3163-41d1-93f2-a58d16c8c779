import { MultiDeliveryOrder, DeliveryPackage, ProductionSchedule } from '@/types/multi-delivery'

// Mock data for multi-delivery system
export const mockMultiDeliveryOrder: MultiDeliveryOrder = {
  id: 'ORD-MULTI-001',
  customerId: 'CUST-001',
  producerId: 'PROD-001',
  productId: 'PROD-GRANITE-001',
  
  totalQuantity: 1000, // 1000 m²
  totalAmount: 50000, // $50,000
  deliveryType: 'multiple',
  status: 'in_production',
  
  orderDate: '2025-01-01',
  estimatedCompletionDate: '2025-03-15',
  notes: 'Büyük otel projesi - 10 aşamalı teslimat',
  
  deliveryPackages: [
    {
      id: 'PKG-001',
      orderId: 'ORD-MULTI-001',
      packageNumber: 1,
      quantity: 100,
      amount: 5000,
      
      productionStatus: 'completed',
      deliveryStatus: 'delivered',
      paymentStatus: 'paid',
      
      productionStartDate: '2025-01-05',
      productionEndDate: '2025-01-15',
      deliveryDate: '2025-01-18',
      actualDeliveryDate: '2025-01-18',
      
      productionNotes: 'İlk paket başarıyla tamamlandı',
      deliveryNotes: 'Zamanında teslim edildi',
      
      productionSchedules: [
        {
          id: 'PS-001-1',
          deliveryPackageId: 'PKG-001',
          stageName: 'Sipariş Onayı',
          stageOrder: 1,
          status: 'completed',
          plannedStartDate: '2025-01-05',
          plannedEndDate: '2025-01-05',
          actualStartDate: '2025-01-05',
          actualEndDate: '2025-01-05',
          assignedWorker: 'Ahmet Yılmaz',
          notes: 'Onaylandı',
          createdAt: '2025-01-05T09:00:00Z',
          updatedAt: '2025-01-05T17:00:00Z'
        },
        {
          id: 'PS-001-2',
          deliveryPackageId: 'PKG-001',
          stageName: 'Kesim İşlemi',
          stageOrder: 2,
          status: 'completed',
          plannedStartDate: '2025-01-06',
          plannedEndDate: '2025-01-10',
          actualStartDate: '2025-01-06',
          actualEndDate: '2025-01-09',
          assignedWorker: 'Mehmet Demir',
          notes: 'Erken tamamlandı',
          createdAt: '2025-01-06T08:00:00Z',
          updatedAt: '2025-01-09T16:00:00Z'
        },
        {
          id: 'PS-001-3',
          deliveryPackageId: 'PKG-001',
          stageName: 'Yüzey İşlemi',
          stageOrder: 3,
          status: 'completed',
          plannedStartDate: '2025-01-10',
          plannedEndDate: '2025-01-13',
          actualStartDate: '2025-01-10',
          actualEndDate: '2025-01-13',
          assignedWorker: 'Ali Kaya',
          notes: 'Cilalama tamamlandı',
          createdAt: '2025-01-10T08:00:00Z',
          updatedAt: '2025-01-13T17:00:00Z'
        },
        {
          id: 'PS-001-4',
          deliveryPackageId: 'PKG-001',
          stageName: 'Kalite Kontrol',
          stageOrder: 4,
          status: 'completed',
          plannedStartDate: '2025-01-14',
          plannedEndDate: '2025-01-14',
          actualStartDate: '2025-01-14',
          actualEndDate: '2025-01-14',
          assignedWorker: 'Fatma Özkan',
          notes: 'Kalite standartlarına uygun',
          createdAt: '2025-01-14T09:00:00Z',
          updatedAt: '2025-01-14T15:00:00Z'
        },
        {
          id: 'PS-001-5',
          deliveryPackageId: 'PKG-001',
          stageName: 'Ambalajlama',
          stageOrder: 5,
          status: 'completed',
          plannedStartDate: '2025-01-15',
          plannedEndDate: '2025-01-15',
          actualStartDate: '2025-01-15',
          actualEndDate: '2025-01-15',
          assignedWorker: 'Hasan Çelik',
          notes: 'Kasalı ambalaj tamamlandı',
          createdAt: '2025-01-15T08:00:00Z',
          updatedAt: '2025-01-15T16:00:00Z'
        }
      ],
      
      payments: [
        {
          id: 'PAY-001-1',
          deliveryPackageId: 'PKG-001',
          amount: 2500,
          paymentType: 'advance',
          status: 'paid',
          dueDate: '2025-01-05',
          paidDate: '2025-01-05',
          paymentMethod: 'bank_transfer',
          transactionId: 'TXN-001-ADV',
          notes: 'Avans ödemesi',
          createdAt: '2025-01-05T10:00:00Z',
          updatedAt: '2025-01-05T14:00:00Z'
        },
        {
          id: 'PAY-001-2',
          deliveryPackageId: 'PKG-001',
          amount: 2500,
          paymentType: 'delivery',
          status: 'paid',
          dueDate: '2025-01-18',
          paidDate: '2025-01-18',
          paymentMethod: 'bank_transfer',
          transactionId: 'TXN-001-DEL',
          notes: 'Teslimat ödemesi',
          createdAt: '2025-01-18T09:00:00Z',
          updatedAt: '2025-01-18T11:00:00Z'
        }
      ],
      
      createdAt: '2025-01-05T08:00:00Z',
      updatedAt: '2025-01-18T12:00:00Z'
    },
    
    {
      id: 'PKG-002',
      orderId: 'ORD-MULTI-001',
      packageNumber: 2,
      quantity: 100,
      amount: 5000,
      
      productionStatus: 'in_progress',
      deliveryStatus: 'pending',
      paymentStatus: 'paid',
      
      productionStartDate: '2025-01-16',
      productionEndDate: '2025-01-26',
      deliveryDate: '2025-01-29',
      
      productionNotes: 'İkinci paket üretimde',
      
      productionSchedules: [
        {
          id: 'PS-002-1',
          deliveryPackageId: 'PKG-002',
          stageName: 'Sipariş Onayı',
          stageOrder: 1,
          status: 'completed',
          plannedStartDate: '2025-01-16',
          plannedEndDate: '2025-01-16',
          actualStartDate: '2025-01-16',
          actualEndDate: '2025-01-16',
          assignedWorker: 'Ahmet Yılmaz',
          notes: 'Onaylandı',
          createdAt: '2025-01-16T09:00:00Z',
          updatedAt: '2025-01-16T17:00:00Z'
        },
        {
          id: 'PS-002-2',
          deliveryPackageId: 'PKG-002',
          stageName: 'Kesim İşlemi',
          stageOrder: 2,
          status: 'completed',
          plannedStartDate: '2025-01-17',
          plannedEndDate: '2025-01-21',
          actualStartDate: '2025-01-17',
          actualEndDate: '2025-01-20',
          assignedWorker: 'Mehmet Demir',
          notes: 'Kesim tamamlandı',
          createdAt: '2025-01-17T08:00:00Z',
          updatedAt: '2025-01-20T16:00:00Z'
        },
        {
          id: 'PS-002-3',
          deliveryPackageId: 'PKG-002',
          stageName: 'Yüzey İşlemi',
          stageOrder: 3,
          status: 'in_progress',
          plannedStartDate: '2025-01-21',
          plannedEndDate: '2025-01-24',
          actualStartDate: '2025-01-21',
          assignedWorker: 'Ali Kaya',
          notes: 'Cilalama devam ediyor',
          createdAt: '2025-01-21T08:00:00Z',
          updatedAt: '2025-01-21T08:00:00Z'
        },
        {
          id: 'PS-002-4',
          deliveryPackageId: 'PKG-002',
          stageName: 'Kalite Kontrol',
          stageOrder: 4,
          status: 'pending',
          plannedStartDate: '2025-01-25',
          plannedEndDate: '2025-01-25',
          assignedWorker: 'Fatma Özkan',
          createdAt: '2025-01-16T09:00:00Z',
          updatedAt: '2025-01-16T09:00:00Z'
        },
        {
          id: 'PS-002-5',
          deliveryPackageId: 'PKG-002',
          stageName: 'Ambalajlama',
          stageOrder: 5,
          status: 'pending',
          plannedStartDate: '2025-01-26',
          plannedEndDate: '2025-01-26',
          assignedWorker: 'Hasan Çelik',
          createdAt: '2025-01-16T09:00:00Z',
          updatedAt: '2025-01-16T09:00:00Z'
        }
      ],
      
      payments: [
        {
          id: 'PAY-002-1',
          deliveryPackageId: 'PKG-002',
          amount: 2500,
          paymentType: 'advance',
          status: 'paid',
          dueDate: '2025-01-16',
          paidDate: '2025-01-16',
          paymentMethod: 'bank_transfer',
          transactionId: 'TXN-002-ADV',
          notes: 'Avans ödemesi',
          createdAt: '2025-01-16T10:00:00Z',
          updatedAt: '2025-01-16T14:00:00Z'
        },
        {
          id: 'PAY-002-2',
          deliveryPackageId: 'PKG-002',
          amount: 2500,
          paymentType: 'delivery',
          status: 'pending',
          dueDate: '2025-01-29',
          notes: 'Teslimat ödemesi',
          createdAt: '2025-01-16T10:00:00Z',
          updatedAt: '2025-01-16T10:00:00Z'
        }
      ],
      
      createdAt: '2025-01-16T08:00:00Z',
      updatedAt: '2025-01-21T12:00:00Z'
    }
  ],
  
  createdAt: '2025-01-01T10:00:00Z',
  updatedAt: '2025-01-21T12:00:00Z'
}

// Utility functions for mock data
export const getPackageProgress = (pkg: DeliveryPackage) => {
  const totalStages = pkg.productionSchedules.length
  const completedStages = pkg.productionSchedules.filter(s => s.status === 'completed').length
  const currentStage = pkg.productionSchedules.find(s => s.status === 'in_progress')
  
  return {
    packageId: pkg.id,
    packageNumber: pkg.packageNumber,
    totalStages,
    completedStages,
    currentStage,
    progressPercentage: Math.round((completedStages / totalStages) * 100)
  }
}

export const getOverallProgress = (order: MultiDeliveryOrder) => {
  const totalPackages = order.deliveryPackages.length
  const completedPackages = order.deliveryPackages.filter(p => p.productionStatus === 'completed').length
  const inProgressPackages = order.deliveryPackages.filter(p => p.productionStatus === 'in_progress').length
  
  return {
    totalPackages,
    completedPackages,
    inProgressPackages,
    pendingPackages: totalPackages - completedPackages - inProgressPackages,
    overallPercentage: Math.round((completedPackages / totalPackages) * 100)
  }
}
