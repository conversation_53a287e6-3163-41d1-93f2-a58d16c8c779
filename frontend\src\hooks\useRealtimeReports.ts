'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

interface RealtimeData {
  timestamp: Date;
  metrics: {
    [key: string]: number | string;
  };
}

interface RealtimeConfig {
  endpoint: string;
  interval: number; // milliseconds
  enabled: boolean;
  maxDataPoints?: number;
}

interface UseRealtimeReportsReturn {
  data: RealtimeData[];
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  lastUpdate: Date | null;
  startRealtime: () => void;
  stopRealtime: () => void;
  clearData: () => void;
  updateConfig: (config: Partial<RealtimeConfig>) => void;
}

export function useRealtimeReports(
  initialConfig: RealtimeConfig
): UseRealtimeReportsReturn {
  const [data, setData] = useState<RealtimeData[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [config, setConfig] = useState<RealtimeConfig>(initialConfig);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  // Mock data generator for demonstration
  const generateMockData = useCallback((): RealtimeData => {
    const baseMetrics = {
      revenue: Math.floor(Math.random() * 10000) + 50000,
      orders: Math.floor(Math.random() * 50) + 100,
      users: Math.floor(Math.random() * 20) + 200,
      conversion_rate: Math.random() * 10 + 20,
      response_time: Math.floor(Math.random() * 100) + 50,
      cpu_usage: Math.random() * 30 + 40,
      memory_usage: Math.random() * 20 + 60,
      active_sessions: Math.floor(Math.random() * 100) + 300
    };

    return {
      timestamp: new Date(),
      metrics: baseMetrics
    };
  }, []);

  // Fetch data from API
  const fetchRealtimeData = useCallback(async (): Promise<RealtimeData> => {
    try {
      // In a real implementation, this would be an actual API call
      // const response = await fetch(config.endpoint);
      // const data = await response.json();
      // return data;

      // For now, return mock data
      return generateMockData();
    } catch (err) {
      throw new Error('Failed to fetch realtime data');
    }
  }, [config.endpoint, generateMockData]);

  // WebSocket connection for real-time updates
  const connectWebSocket = useCallback(() => {
    if (!config.enabled) return;

    try {
      // In a real implementation, you would connect to your WebSocket server
      // wsRef.current = new WebSocket('ws://localhost:8000/ws/reports');

      // For demonstration, we'll simulate WebSocket behavior
      setIsConnected(true);
      setError(null);

      // Simulate WebSocket message handling
      const simulateWebSocketMessages = () => {
        const newData = generateMockData();
        setData(prevData => {
          const updatedData = [...prevData, newData];
          const maxPoints = config.maxDataPoints || 50;
          return updatedData.slice(-maxPoints);
        });
        setLastUpdate(new Date());
      };

      // Clear any existing interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      intervalRef.current = setInterval(simulateWebSocketMessages, config.interval);

    } catch (err) {
      setError('Failed to connect to WebSocket');
      setIsConnected(false);
    }
  }, []); // Empty dependency array to prevent infinite loops

  // Disconnect WebSocket
  const disconnectWebSocket = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setIsConnected(false);
  }, []);

  // Start realtime updates
  const startRealtime = useCallback(() => {
    setIsLoading(true);
    setError(null);
    
    // Try WebSocket first, fallback to polling
    connectWebSocket();
    
    setIsLoading(false);
  }, [connectWebSocket]);

  // Stop realtime updates
  const stopRealtime = useCallback(() => {
    disconnectWebSocket();
    setConfig(prev => ({ ...prev, enabled: false }));
  }, [disconnectWebSocket]);

  // Clear all data
  const clearData = useCallback(() => {
    setData([]);
    setLastUpdate(null);
    setError(null);
  }, []);

  // Update configuration
  const updateConfig = useCallback((newConfig: Partial<RealtimeConfig>) => {
    setConfig(prev => {
      const updated = { ...prev, ...newConfig };
      return updated;
    });
  }, []); // Removed dependencies to prevent infinite loops

  // Auto-start if enabled
  useEffect(() => {
    if (config.enabled) {
      startRealtime();
    }

    return () => {
      disconnectWebSocket();
    };
  }, [config.enabled]); // Removed startRealtime and disconnectWebSocket from dependencies

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnectWebSocket();
    };
  }, []); // Empty dependency array for cleanup only

  return {
    data,
    isConnected,
    isLoading,
    error,
    lastUpdate,
    startRealtime,
    stopRealtime,
    clearData,
    updateConfig
  };
}

// Hook for specific report metrics
export function useRealtimeMetrics(metricNames: string[], interval: number = 5000) {
  const { data, isConnected, error, lastUpdate } = useRealtimeReports({
    endpoint: '/api/realtime/metrics',
    interval,
    enabled: true,
    maxDataPoints: 20
  });

  const latestMetrics = data.length > 0 ? data[data.length - 1].metrics : {};
  
  const filteredMetrics = metricNames.reduce((acc, name) => {
    acc[name] = latestMetrics[name] || 0;
    return acc;
  }, {} as { [key: string]: number | string });

  const historicalData = data.map(point => ({
    timestamp: point.timestamp,
    ...metricNames.reduce((acc, name) => {
      acc[name] = point.metrics[name] || 0;
      return acc;
    }, {} as { [key: string]: number | string })
  }));

  return {
    metrics: filteredMetrics,
    historical: historicalData,
    isConnected,
    error,
    lastUpdate
  };
}

// Hook for KPI monitoring
export function useRealtimeKPIs() {
  const kpiMetrics = ['revenue', 'orders', 'users', 'conversion_rate'];
  
  const { metrics, historical, isConnected, error, lastUpdate } = useRealtimeMetrics(
    kpiMetrics,
    3000 // Update every 3 seconds for KPIs
  );

  // Calculate trends
  const trends = kpiMetrics.reduce((acc, metric) => {
    if (historical.length >= 2) {
      const current = historical[historical.length - 1][metric] as number;
      const previous = historical[historical.length - 2][metric] as number;
      const change = ((current - previous) / previous) * 100;
      acc[metric] = {
        current,
        previous,
        change: isFinite(change) ? change : 0,
        trend: change > 0 ? 'up' : change < 0 ? 'down' : 'stable'
      };
    } else {
      acc[metric] = {
        current: metrics[metric] as number,
        previous: 0,
        change: 0,
        trend: 'stable' as const
      };
    }
    return acc;
  }, {} as { [key: string]: { current: number; previous: number; change: number; trend: 'up' | 'down' | 'stable' } });

  return {
    kpis: trends,
    isConnected,
    error,
    lastUpdate
  };
}

// Hook for system health monitoring
export function useSystemHealth() {
  const systemMetrics = ['response_time', 'cpu_usage', 'memory_usage', 'active_sessions'];
  
  const { metrics, isConnected, error, lastUpdate } = useRealtimeMetrics(
    systemMetrics,
    2000 // Update every 2 seconds for system metrics
  );

  const healthStatus = {
    overall: 'healthy' as 'healthy' | 'warning' | 'critical',
    details: {
      response_time: {
        value: metrics.response_time as number,
        status: (metrics.response_time as number) < 200 ? 'healthy' : 
                (metrics.response_time as number) < 500 ? 'warning' : 'critical'
      },
      cpu_usage: {
        value: metrics.cpu_usage as number,
        status: (metrics.cpu_usage as number) < 70 ? 'healthy' : 
                (metrics.cpu_usage as number) < 90 ? 'warning' : 'critical'
      },
      memory_usage: {
        value: metrics.memory_usage as number,
        status: (metrics.memory_usage as number) < 80 ? 'healthy' : 
                (metrics.memory_usage as number) < 95 ? 'warning' : 'critical'
      }
    }
  };

  // Determine overall health
  const statuses = Object.values(healthStatus.details).map(detail => detail.status);
  if (statuses.includes('critical')) {
    healthStatus.overall = 'critical';
  } else if (statuses.includes('warning')) {
    healthStatus.overall = 'warning';
  }

  return {
    health: healthStatus,
    metrics,
    isConnected,
    error,
    lastUpdate
  };
}
