/**
 * Chatbot Controller
 * Handles HTTP requests for AI chatbot functionality
 */

import { Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { ChatbotService } from '../services/ChatbotService';
import { UserFeedback } from '../types';

export class ChatbotController {
  private chatbotService: ChatbotService;

  constructor() {
    this.chatbotService = new ChatbotService();
  }

  /**
   * Start new conversation
   * POST /api/chatbot/start
   */
  async startConversation(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: errors.array()
        });
        return;
      }

      const { userId, language = 'en' } = req.body;
      
      const result = await this.chatbotService.startConversation(userId, language);
      
      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Start conversation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to start conversation',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Send message to chatbot
   * POST /api/chatbot/message
   */
  async sendMessage(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: errors.array()
        });
        return;
      }

      const { sessionId, message, userId } = req.body;
      
      const response = await this.chatbotService.processMessage(sessionId, message, userId);
      
      res.status(200).json({
        success: true,
        data: response
      });
    } catch (error) {
      console.error('Send message error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process message',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get conversation history
   * GET /api/chatbot/conversations/:sessionId/history
   */
  async getConversationHistory(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: errors.array()
        });
        return;
      }

      const { sessionId } = req.params;
      const limit = parseInt(req.query.limit as string) || 20;
      
      const history = await this.chatbotService.conversationManager.getHistory(sessionId, limit);
      
      res.status(200).json({
        success: true,
        data: {
          sessionId,
          messages: history,
          count: history.length
        }
      });
    } catch (error) {
      console.error('Get conversation history error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get conversation history',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Submit feedback for chatbot response
   * POST /api/chatbot/feedback
   */
  async submitFeedback(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: errors.array()
        });
        return;
      }

      const { sessionId, messageId, rating, feedback } = req.body;
      
      const userFeedback: UserFeedback = {
        sessionId,
        messageId,
        rating,
        feedback,
        timestamp: new Date()
      };
      
      await this.chatbotService.submitFeedback(userFeedback);
      
      res.status(200).json({
        success: true,
        message: 'Feedback submitted successfully'
      });
    } catch (error) {
      console.error('Submit feedback error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to submit feedback',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get chatbot analytics (Admin only)
   * GET /api/chatbot/analytics
   */
  async getAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const analytics = await this.chatbotService.getAnalytics();
      
      res.status(200).json({
        success: true,
        data: analytics
      });
    } catch (error) {
      console.error('Get analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get analytics',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get active conversations (Admin only)
   * GET /api/chatbot/conversations
   */
  async getActiveConversations(req: Request, res: Response): Promise<void> {
    try {
      const conversations = await this.chatbotService.conversationManager.getAllActiveConversations();
      
      res.status(200).json({
        success: true,
        data: {
          conversations,
          count: conversations.length
        }
      });
    } catch (error) {
      console.error('Get active conversations error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get active conversations',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Delete conversation
   * DELETE /api/chatbot/conversations/:sessionId
   */
  async deleteConversation(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: errors.array()
        });
        return;
      }

      const { sessionId } = req.params;
      
      await this.chatbotService.conversationManager.deleteConversation(sessionId);
      
      res.status(200).json({
        success: true,
        message: 'Conversation deleted successfully'
      });
    } catch (error) {
      console.error('Delete conversation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete conversation',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get conversation statistics
   * GET /api/chatbot/conversations/:sessionId/stats
   */
  async getConversationStats(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: errors.array()
        });
        return;
      }

      const { sessionId } = req.params;
      
      const stats = await this.chatbotService.conversationManager.getConversationStats(sessionId);
      
      res.status(200).json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Get conversation stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get conversation statistics',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Update escalation criteria (Admin only)
   * PUT /api/chatbot/escalation-criteria
   */
  async updateEscalationCriteria(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: errors.array()
        });
        return;
      }

      const criteria = req.body;
      
      this.chatbotService.escalationManager.updateEscalationCriteria(criteria);
      
      res.status(200).json({
        success: true,
        message: 'Escalation criteria updated successfully'
      });
    } catch (error) {
      console.error('Update escalation criteria error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update escalation criteria',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get escalation criteria (Admin only)
   * GET /api/chatbot/escalation-criteria
   */
  async getEscalationCriteria(req: Request, res: Response): Promise<void> {
    try {
      const criteria = this.chatbotService.escalationManager.getEscalationCriteria();
      
      res.status(200).json({
        success: true,
        data: criteria
      });
    } catch (error) {
      console.error('Get escalation criteria error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get escalation criteria',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get available agents (Admin only)
   * GET /api/chatbot/agents
   */
  async getAvailableAgents(req: Request, res: Response): Promise<void> {
    try {
      const agents = await this.chatbotService.escalationManager.getAvailableAgents();

      res.status(200).json({
        success: true,
        data: {
          agents,
          count: agents.length
        }
      });
    } catch (error) {
      console.error('Get available agents error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get available agents',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Update agent availability (Admin only)
   * PUT /api/chatbot/agents/:agentId/availability
   */
  async updateAgentAvailability(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: errors.array()
        });
        return;
      }

      const { agentId } = req.params;
      const { available } = req.body;
      
      const success = await this.chatbotService.escalationManager.updateAgentAvailability(agentId, available);

      if (success) {
        res.status(200).json({
          success: true,
          message: 'Agent availability updated successfully'
        });
      } else {
        res.status(404).json({
          success: false,
          message: 'Agent not found'
        });
      }
    } catch (error) {
      console.error('Update agent availability error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update agent availability',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Health check endpoint
   * GET /api/chatbot/health
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      res.status(200).json({
        success: true,
        message: 'Chatbot service is healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Chatbot service is unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}

// Validation middleware
export const chatbotValidation = {
  startConversation: [
    body('language').optional().isString().isLength({ min: 2, max: 5 }),
    body('userId').optional().isString()
  ],
  
  sendMessage: [
    body('sessionId').isString().notEmpty(),
    body('message').isString().isLength({ min: 1, max: 1000 }),
    body('userId').optional().isString()
  ],
  
  getHistory: [
    param('sessionId').isString().notEmpty(),
    query('limit').optional().isInt({ min: 1, max: 100 })
  ],
  
  submitFeedback: [
    body('sessionId').isString().notEmpty(),
    body('messageId').isString().notEmpty(),
    body('rating').isInt({ min: 1, max: 5 }),
    body('feedback').optional().isString().isLength({ max: 500 })
  ],
  
  deleteConversation: [
    param('sessionId').isString().notEmpty()
  ],
  
  getConversationStats: [
    param('sessionId').isString().notEmpty()
  ],
  
  updateAgentAvailability: [
    param('agentId').isString().notEmpty(),
    body('available').isBoolean()
  ]
};
