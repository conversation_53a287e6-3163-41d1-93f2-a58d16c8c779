import { Server } from 'socket.io';

export function setupSocketHandlers(io: Server): void {
  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.id}`);

    // Join user to their personal room
    socket.on('join-user-room', (userId: string) => {
      socket.join(`user:${userId}`);
      console.log(`User ${userId} joined their room`);
    });

    // Handle bid updates
    socket.on('join-bid-room', (bidRequestId: string) => {
      socket.join(`bid:${bidRequestId}`);
      console.log(`User joined bid room: ${bidRequestId}`);
    });

    // Handle order updates
    socket.on('join-order-room', (orderId: string) => {
      socket.join(`order:${orderId}`);
      console.log(`User joined order room: ${orderId}`);
    });

    socket.on('disconnect', () => {
      console.log(`User disconnected: ${socket.id}`);
    });
  });

  // Helper functions for emitting events
  io.sendToUser = (userId: string, event: string, data: any) => {
    io.to(`user:${userId}`).emit(event, data);
  };

  io.sendToBidRoom = (bidRequestId: string, event: string, data: any) => {
    io.to(`bid:${bidRequestId}`).emit(event, data);
  };

  io.sendToOrderRoom = (orderId: string, event: string, data: any) => {
    io.to(`order:${orderId}`).emit(event, data);
  };
}

// Extend Socket.IO Server interface
declare module 'socket.io' {
  interface Server {
    sendToUser(userId: string, event: string, data: any): void;
    sendToBidRoom(bidRequestId: string, event: string, data: any): void;
    sendToOrderRoom(orderId: string, event: string, data: any): void;
  }
}
