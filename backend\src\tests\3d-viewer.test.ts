/**
 * 3D Viewer Tests
 * Test suite for 3D product visualization functionality
 */

import request from 'supertest';
import { PrismaClient } from '@prisma/client';
import { AssetManager } from '../modules/3d/services/AssetManager';
import { ViewerService } from '../modules/3d/services/ViewerService';
import { OptimizationService } from '../modules/3d/services/OptimizationService';
import { AssetType, AssetFormat, AssetQuality } from '../modules/3d/types';

const prisma = new PrismaClient();

describe('3D Viewer System', () => {
  let assetManager: AssetManager;
  let viewerService: ViewerService;
  let optimizationService: OptimizationService;
  let testProductId: string;
  let testAssetId: string;
  let testSessionId: string;

  beforeAll(async () => {
    assetManager = new AssetManager();
    viewerService = new ViewerService();
    optimizationService = new OptimizationService();

    // Create test product
    const testProduct = await prisma.product.create({
      data: {
        name: 'Test Marble Slab',
        description: 'Test product for 3D viewer',
        producerId: 'test-producer-id',
        categoryId: 'test-category-id',
        specifications: {},
        slug: 'test-marble-slab-3d',
        searchKeywords: ['test', 'marble']
      }
    });
    testProductId = testProduct.id;
  });

  afterAll(async () => {
    // Cleanup test data
    if (testAssetId) {
      await assetManager.deleteAsset(testAssetId);
    }
    if (testProductId) {
      await prisma.product.delete({ where: { id: testProductId } });
    }
    await assetManager.disconnect();
    await viewerService.disconnect();
    await prisma.$disconnect();
  });

  describe('Asset Management', () => {
    test('should create mock asset for testing', async () => {
      const mockAsset = await prisma.asset3D.create({
        data: {
          productId: testProductId,
          name: 'Test 3D Model',
          description: 'Test 3D model for unit testing',
          type: AssetType.MODEL_3D,
          format: AssetFormat.GLB,
          quality: AssetQuality.HIGH,
          originalFileName: 'test_model.glb',
          fileName: 'test_model_processed.glb',
          filePath: '/test/path/test_model.glb',
          fileSize: BigInt(1024000),
          mimeType: 'model/gltf-binary',
          vertices: 10000,
          faces: 3333,
          tags: ['test', 'marble'],
          metadata: { testData: true }
        }
      });

      testAssetId = mockAsset.id;
      expect(mockAsset).toBeDefined();
      expect(mockAsset.name).toBe('Test 3D Model');
      expect(mockAsset.type).toBe(AssetType.MODEL_3D);
    });

    test('should get asset by ID', async () => {
      const asset = await assetManager.getAsset(testAssetId);
      
      expect(asset).toBeDefined();
      expect(asset?.id).toBe(testAssetId);
      expect(asset?.name).toBe('Test 3D Model');
      expect(asset?.type).toBe(AssetType.MODEL_3D);
    });

    test('should get assets by product ID', async () => {
      const assets = await assetManager.getAssetsByProduct(testProductId);
      
      expect(assets).toBeDefined();
      expect(Array.isArray(assets)).toBe(true);
      expect(assets.length).toBeGreaterThan(0);
      expect(assets[0].productId).toBe(testProductId);
    });

    test('should handle non-existent asset', async () => {
      const asset = await assetManager.getAsset('non-existent-id');
      expect(asset).toBeNull();
    });
  });

  describe('Viewer Configuration', () => {
    test('should create default viewer configuration', async () => {
      const config = await viewerService.getViewerConfiguration(testProductId);
      
      expect(config).toBeDefined();
      expect(config.productId).toBe(testProductId);
      expect(config.cameraFov).toBe(75);
      expect(config.enableOrbitControls).toBe(true);
      expect(config.enableShadows).toBe(true);
    });

    test('should update viewer configuration', async () => {
      const updates = {
        cameraFov: 60,
        autoRotate: true,
        autoRotateSpeed: 3.0,
        backgroundColor: '#ffffff'
      };

      const updatedConfig = await viewerService.updateViewerConfiguration(
        testProductId, 
        updates
      );

      expect(updatedConfig.cameraFov).toBe(60);
      expect(updatedConfig.autoRotate).toBe(true);
      expect(updatedConfig.autoRotateSpeed).toBe(3.0);
      expect(updatedConfig.backgroundColor).toBe('#ffffff');
    });
  });

  describe('Viewer Sessions', () => {
    test('should start viewer session', async () => {
      const deviceInfo = {
        userAgent: 'Mozilla/5.0 (Test Browser)',
        deviceType: 'desktop' as const,
        screenResolution: '1920x1080'
      };

      const session = await viewerService.startViewerSession(
        testProductId,
        'test-user-id',
        deviceInfo
      );

      testSessionId = session.sessionId;
      
      expect(session).toBeDefined();
      expect(session.productId).toBe(testProductId);
      expect(session.userId).toBe('test-user-id');
      expect(session.deviceType).toBe('desktop');
      expect(session.viewDuration).toBe(0);
    });

    test('should update viewer session', async () => {
      const updates = {
        viewDuration: 120,
        interactionCount: 5,
        zoomCount: 3,
        rotationCount: 10,
        loadTime: 2500,
        frameRate: 60,
        memoryUsage: 150
      };

      await viewerService.updateViewerSession(testSessionId, updates);

      // Verify update by checking database
      const session = await prisma.viewerSession.findUnique({
        where: { sessionId: testSessionId }
      });

      expect(session?.viewDuration).toBe(120);
      expect(session?.interactionCount).toBe(5);
      expect(session?.zoomCount).toBe(3);
      expect(session?.rotationCount).toBe(10);
      expect(session?.loadTime).toBe(2500);
      expect(Number(session?.frameRate)).toBe(60);
      expect(session?.memoryUsage).toBe(150);
    });

    test('should end viewer session', async () => {
      await viewerService.endViewerSession(testSessionId);

      const session = await prisma.viewerSession.findUnique({
        where: { sessionId: testSessionId }
      });

      expect(session?.endedAt).toBeDefined();
    });
  });

  describe('Annotations', () => {
    test('should add annotation to product', async () => {
      const annotation = {
        position: { x: 1, y: 1, z: 1 },
        title: 'Test Annotation',
        description: 'This is a test annotation',
        type: 'info' as const,
        visible: true
      };

      const newAnnotation = await viewerService.addAnnotation(testProductId, annotation);

      expect(newAnnotation).toBeDefined();
      expect(newAnnotation.title).toBe('Test Annotation');
      expect(newAnnotation.type).toBe('info');
      expect(newAnnotation.visible).toBe(true);
    });

    test('should update annotation', async () => {
      // First get the configuration to find the annotation
      const config = await viewerService.getViewerConfiguration(testProductId);
      const annotation = config.annotations?.[0];
      
      if (annotation) {
        const updates = {
          title: 'Updated Test Annotation',
          description: 'This annotation has been updated'
        };

        const updatedAnnotation = await viewerService.updateAnnotation(
          testProductId,
          annotation.id,
          updates
        );

        expect(updatedAnnotation.title).toBe('Updated Test Annotation');
        expect(updatedAnnotation.description).toBe('This annotation has been updated');
      }
    });
  });

  describe('Analytics', () => {
    test('should get viewer analytics', async () => {
      const analytics = await viewerService.getViewerAnalytics(testProductId);

      expect(analytics).toBeDefined();
      expect(typeof analytics.totalSessions).toBe('number');
      expect(typeof analytics.averageViewDuration).toBe('number');
      expect(typeof analytics.averageInteractions).toBe('number');
      expect(Array.isArray(analytics.popularProducts)).toBe(true);
      expect(analytics.deviceBreakdown).toBeDefined();
      expect(analytics.performanceMetrics).toBeDefined();
    });

    test('should get analytics with date range', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');

      const analytics = await viewerService.getViewerAnalytics(
        testProductId,
        startDate,
        endDate
      );

      expect(analytics).toBeDefined();
      expect(typeof analytics.totalSessions).toBe('number');
    });
  });

  describe('Optimization Service', () => {
    test('should estimate optimization time', () => {
      const fileSize = 5 * 1024 * 1024; // 5MB
      const qualities = [AssetQuality.LOW, AssetQuality.MEDIUM, AssetQuality.HIGH];
      const enableDraco = true;

      const estimatedTime = optimizationService.estimateOptimizationTime(
        fileSize,
        qualities,
        enableDraco
      );

      expect(typeof estimatedTime).toBe('number');
      expect(estimatedTime).toBeGreaterThan(0);
    });

    test('should get texture resolution for quality', () => {
      // This would test private methods if they were public
      // For now, we'll test the overall functionality
      expect(true).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid product ID for configuration', async () => {
      await expect(
        viewerService.getViewerConfiguration('invalid-product-id')
      ).rejects.toThrow();
    });

    test('should handle invalid session ID for updates', async () => {
      await expect(
        viewerService.updateViewerSession('invalid-session-id', {
          viewDuration: 100
        })
      ).rejects.toThrow();
    });

    test('should handle invalid annotation operations', async () => {
      await expect(
        viewerService.updateAnnotation(
          'invalid-product-id',
          'invalid-annotation-id',
          { title: 'Test' }
        )
      ).rejects.toThrow();
    });
  });

  describe('Performance Tests', () => {
    test('should handle multiple concurrent sessions', async () => {
      const sessionPromises = [];
      
      for (let i = 0; i < 5; i++) {
        sessionPromises.push(
          viewerService.startViewerSession(
            testProductId,
            `test-user-${i}`,
            {
              userAgent: 'Test Browser',
              deviceType: 'desktop',
              screenResolution: '1920x1080'
            }
          )
        );
      }

      const sessions = await Promise.all(sessionPromises);
      
      expect(sessions).toHaveLength(5);
      sessions.forEach(session => {
        expect(session.productId).toBe(testProductId);
      });

      // Cleanup sessions
      for (const session of sessions) {
        await viewerService.endViewerSession(session.sessionId);
      }
    });

    test('should handle rapid session updates', async () => {
      const session = await viewerService.startViewerSession(
        testProductId,
        'test-user-performance'
      );

      const updatePromises = [];
      
      for (let i = 0; i < 10; i++) {
        updatePromises.push(
          viewerService.updateViewerSession(session.sessionId, {
            viewDuration: i * 10,
            interactionCount: i
          })
        );
      }

      await Promise.all(updatePromises);
      
      // Verify final state
      const finalSession = await prisma.viewerSession.findUnique({
        where: { sessionId: session.sessionId }
      });

      expect(finalSession).toBeDefined();
      
      // Cleanup
      await viewerService.endViewerSession(session.sessionId);
    });
  });
});
