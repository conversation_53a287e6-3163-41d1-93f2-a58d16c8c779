/**
 * Advanced File Upload Validation and Security
 * Implements content-based validation, malware detection, and security scanning
 */

import { Request, Response, NextFunction } from 'express';
import { fileTypeFromBuffer } from 'file-type';
import crypto from 'crypto';
import path from 'path';
import fs from 'fs/promises';
import { logWarn, logError, logInfo } from '../utils/logger';

export interface FileValidationConfig {
  allowedTypes: string[];
  allowedMimeTypes: string[];
  maxFileSize: number;
  scanForMalware: boolean;
  checkFileContent: boolean;
  allowExecutables: boolean;
}

export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fileInfo: {
    originalName: string;
    detectedType?: string;
    detectedMimeType?: string;
    size: number;
    hash: string;
  };
}

const defaultConfig: FileValidationConfig = {
  allowedTypes: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
  allowedMimeTypes: [
    'image/jpeg',
    'image/png',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ],
  maxFileSize: 10 * 1024 * 1024, // 10MB
  scanForMalware: true,
  checkFileContent: true,
  allowExecutables: false
};

/**
 * Suspicious file patterns that might indicate malware
 */
const MALICIOUS_PATTERNS = [
  // Executable signatures
  Buffer.from([0x4D, 0x5A]), // MZ (PE executable)
  Buffer.from([0x7F, 0x45, 0x4C, 0x46]), // ELF executable
  Buffer.from([0xCA, 0xFE, 0xBA, 0xBE]), // Mach-O executable
  
  // Script patterns
  Buffer.from('<?php', 'utf8'),
  Buffer.from('<script', 'utf8'),
  Buffer.from('javascript:', 'utf8'),
  Buffer.from('vbscript:', 'utf8'),
  
  // Archive with executables
  Buffer.from('PK'), // ZIP signature (need additional checks)
];

/**
 * Suspicious file extensions
 */
const DANGEROUS_EXTENSIONS = [
  'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar',
  'app', 'deb', 'pkg', 'rpm', 'dmg', 'iso', 'msi', 'dll', 'so',
  'php', 'asp', 'jsp', 'py', 'rb', 'pl', 'sh', 'ps1'
];

/**
 * Calculate file hash for duplicate detection and integrity
 */
function calculateFileHash(buffer: Buffer): string {
  return crypto.createHash('sha256').update(buffer).digest('hex');
}

/**
 * Check if file contains suspicious patterns
 */
function containsMaliciousPatterns(buffer: Buffer): string[] {
  const issues: string[] = [];
  
  for (const pattern of MALICIOUS_PATTERNS) {
    if (buffer.includes(pattern)) {
      issues.push(`Suspicious pattern detected: ${pattern.toString('hex')}`);
    }
  }
  
  // Check for embedded scripts in images
  const content = buffer.toString('utf8', 0, Math.min(buffer.length, 1024));
  if (content.includes('<script') || content.includes('javascript:') || content.includes('<?php')) {
    issues.push('Embedded script detected in file content');
  }
  
  return issues;
}

/**
 * Validate file extension against dangerous extensions
 */
function validateFileExtension(filename: string): string[] {
  const issues: string[] = [];
  const ext = path.extname(filename).toLowerCase().substring(1);
  
  if (DANGEROUS_EXTENSIONS.includes(ext)) {
    issues.push(`Dangerous file extension: .${ext}`);
  }
  
  // Check for double extensions (e.g., file.pdf.exe)
  const parts = filename.split('.');
  if (parts.length > 2) {
    for (let i = 1; i < parts.length - 1; i++) {
      if (DANGEROUS_EXTENSIONS.includes(parts[i].toLowerCase())) {
        issues.push(`Hidden dangerous extension detected: .${parts[i]}`);
      }
    }
  }
  
  return issues;
}

/**
 * Advanced file content validation
 */
async function validateFileContent(buffer: Buffer, filename: string, config: FileValidationConfig): Promise<FileValidationResult> {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Calculate file hash
  const hash = calculateFileHash(buffer);
  
  // Detect actual file type from content
  const detectedType = await fileTypeFromBuffer(buffer);
  
  // Basic file info
  const fileInfo = {
    originalName: filename,
    detectedType: detectedType?.ext,
    detectedMimeType: detectedType?.mime,
    size: buffer.length,
    hash
  };
  
  // Size validation
  if (buffer.length > config.maxFileSize) {
    errors.push(`File size (${buffer.length} bytes) exceeds maximum allowed size (${config.maxFileSize} bytes)`);
  }
  
  // Empty file check
  if (buffer.length === 0) {
    errors.push('File is empty');
  }
  
  // File extension validation
  const extensionIssues = validateFileExtension(filename);
  errors.push(...extensionIssues);
  
  // Content-based type validation
  if (config.checkFileContent && detectedType) {
    const declaredExt = path.extname(filename).toLowerCase().substring(1);
    
    if (declaredExt && detectedType.ext !== declaredExt) {
      // Special cases for compatible formats
      const compatibleFormats: { [key: string]: string[] } = {
        'jpg': ['jpeg'],
        'jpeg': ['jpg'],
        'tiff': ['tif'],
        'tif': ['tiff']
      };
      
      const compatible = compatibleFormats[declaredExt]?.includes(detectedType.ext) ||
                        compatibleFormats[detectedType.ext]?.includes(declaredExt);
      
      if (!compatible) {
        errors.push(`File extension mismatch: declared .${declaredExt}, detected .${detectedType.ext}`);
      }
    }
    
    // Check if detected type is allowed
    if (!config.allowedTypes.includes(detectedType.ext)) {
      errors.push(`File type not allowed: ${detectedType.ext}`);
    }
    
    if (!config.allowedMimeTypes.includes(detectedType.mime)) {
      errors.push(`MIME type not allowed: ${detectedType.mime}`);
    }
  }
  
  // Malware pattern detection
  if (config.scanForMalware) {
    const malwareIssues = containsMaliciousPatterns(buffer);
    errors.push(...malwareIssues);
  }
  
  // Executable detection
  if (!config.allowExecutables && detectedType) {
    const executableTypes = ['exe', 'msi', 'app', 'deb', 'rpm', 'dmg'];
    if (executableTypes.includes(detectedType.ext)) {
      errors.push(`Executable file detected: ${detectedType.ext}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fileInfo
  };
}

/**
 * Express middleware for file validation
 */
export function createFileValidationMiddleware(config: Partial<FileValidationConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config };
  
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Check if files were uploaded
      if (!req.files && !req.file) {
        return next();
      }
      
      const files = req.files ? (Array.isArray(req.files) ? req.files : Object.values(req.files).flat()) : [req.file];
      const validationResults: FileValidationResult[] = [];
      
      for (const file of files) {
        if (!file || !file.buffer) {
          continue;
        }
        
        const result = await validateFileContent(file.buffer, file.originalname, finalConfig);
        validationResults.push(result);
        
        if (!result.isValid) {
          logWarn('File validation failed', {
            filename: file.originalname,
            errors: result.errors,
            ip: req.ip,
            userAgent: req.get('User-Agent')
          });
          
          return res.status(400).json({
            success: false,
            error: 'File validation failed',
            details: {
              filename: file.originalname,
              errors: result.errors,
              warnings: result.warnings
            }
          });
        }
        
        if (result.warnings.length > 0) {
          logWarn('File validation warnings', {
            filename: file.originalname,
            warnings: result.warnings,
            ip: req.ip
          });
        }
        
        logInfo('File validation passed', {
          filename: file.originalname,
          detectedType: result.fileInfo.detectedType,
          size: result.fileInfo.size,
          hash: result.fileInfo.hash.substring(0, 16) + '...'
        });
      }
      
      // Add validation results to request for later use
      (req as any).fileValidationResults = validationResults;
      
      next();
      
    } catch (error) {
      logError('File validation middleware error', error as Error, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(500).json({
        success: false,
        error: 'File validation failed due to server error'
      });
    }
  };
}

/**
 * Strict file validation for sensitive uploads
 */
export const strictFileValidation = createFileValidationMiddleware({
  allowedTypes: ['jpg', 'jpeg', 'png', 'pdf'],
  allowedMimeTypes: ['image/jpeg', 'image/png', 'application/pdf'],
  maxFileSize: 5 * 1024 * 1024, // 5MB
  scanForMalware: true,
  checkFileContent: true,
  allowExecutables: false
});

/**
 * Standard file validation for general uploads
 */
export const standardFileValidation = createFileValidationMiddleware();

/**
 * Permissive file validation for development
 */
export const permissiveFileValidation = createFileValidationMiddleware({
  allowedTypes: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'txt', 'csv'],
  maxFileSize: 20 * 1024 * 1024, // 20MB
  scanForMalware: false,
  checkFileContent: false,
  allowExecutables: false
});
