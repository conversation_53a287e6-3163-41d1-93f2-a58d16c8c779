// RFC-013: Self-Learning AI Marketing Orchestrator
// Devamlı öğrenen ve gelişen AI koordinasyon merkezi

import { EventEmitter } from 'events';
import { EmailMarketingAI } from '../email-marketing/EmailMarketingAI';
import { SocialMediaAI } from '../social-media/SocialMediaAI';
import { CustomerAcquisitionAI } from '../customer-acquisition/CustomerAcquisitionAI';
import { AdsManagementAI } from '../ads-management/AdsManagementAI';
import { ApprovalSystem } from '../approval-system/ApprovalSystem';
import { AnalyticsEngine } from '../analytics/AnalyticsEngine';
import { DatabaseManager } from '../database/DatabaseManager';
import { AdaptiveLearningEngine } from '../learning/AdaptiveLearningEngine';
import { ContinuousResearchModule } from '../research/ContinuousResearchModule';
import { DynamicStrategyGenerator } from '../strategy/DynamicStrategyGenerator';
import { RealTimeOptimizer } from '../optimization/RealTimeOptimizer';
import { KnowledgeBaseEvolution } from '../knowledge/KnowledgeBaseEvolution';
import { AIModel, MarketingTask, TaskResult, AISystemStatus } from '../types/ai-marketing.types';

export class AIMarketingOrchestrator extends EventEmitter {
  private aiModels: Map<string, AIModel>;
  private database: DatabaseManager;
  private taskQueue: MarketingTask[];
  private isRunning: boolean = false;
  private cycleInterval: NodeJS.Timeout | null = null;

  // AI Modülleri
  private emailMarketingAI!: EmailMarketingAI;
  private socialMediaAI!: SocialMediaAI;
  private customerAcquisitionAI!: CustomerAcquisitionAI;
  private adsManagementAI!: AdsManagementAI;
  private approvalSystem!: ApprovalSystem;
  private analytics!: AnalyticsEngine;

  // Self-Learning Modülleri
  private adaptiveLearningEngine!: AdaptiveLearningEngine;
  private continuousResearchModule!: ContinuousResearchModule;
  private dynamicStrategyGenerator!: DynamicStrategyGenerator;
  private realTimeOptimizer!: RealTimeOptimizer;
  private knowledgeBaseEvolution!: KnowledgeBaseEvolution;

  // Learning State
  private learningCycleCount: number = 0;
  private performanceHistory: Map<string, number[]> = new Map();
  private strategyEvolutionLog: any[] = [];
  private researchInsights: Map<string, any> = new Map();

  constructor() {
    super();
    this.aiModels = new Map();
    this.taskQueue = [];
    this.database = new DatabaseManager({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_NAME || 'marketplace',
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password'
    });
    this.initializeAIModules();
  }

  private initializeAIModules(): void {
    try {
      // Temel AI modüllerini başlat
      this.emailMarketingAI = new EmailMarketingAI();
      this.socialMediaAI = new SocialMediaAI();
      this.customerAcquisitionAI = new CustomerAcquisitionAI();
      this.adsManagementAI = new AdsManagementAI();
      this.approvalSystem = new ApprovalSystem();
      this.analytics = new AnalyticsEngine();

      // Self-Learning modüllerini başlat
      this.adaptiveLearningEngine = new AdaptiveLearningEngine(this.database);
      this.continuousResearchModule = new ContinuousResearchModule();
      this.dynamicStrategyGenerator = new DynamicStrategyGenerator();
      this.realTimeOptimizer = new RealTimeOptimizer();
      this.knowledgeBaseEvolution = new KnowledgeBaseEvolution();

      // AI modellerini kaydet
      this.aiModels.set('email', this.emailMarketingAI);
      this.aiModels.set('social', this.socialMediaAI);
      this.aiModels.set('customer', this.customerAcquisitionAI);
      this.aiModels.set('ads', this.adsManagementAI);
      this.aiModels.set('learning', this.adaptiveLearningEngine);
      this.aiModels.set('research', this.continuousResearchModule);
      this.aiModels.set('strategy', this.dynamicStrategyGenerator);
      this.aiModels.set('optimizer', this.realTimeOptimizer);
      this.aiModels.set('knowledge', this.knowledgeBaseEvolution);

      // Self-learning event listeners
      this.setupLearningEventListeners();

      console.log('🚀 Self-Learning AI Marketing Orchestrator initialized successfully');
      this.emit('initialized');
    } catch (error) {
      console.error('Error initializing AI modules:', error);
      this.emit('error', error);
    }
  }

  /**
   * Self-learning event listeners kurulumu
   */
  private setupLearningEventListeners(): void {
    // Learning Engine events
    this.adaptiveLearningEngine.on('learningCycleCompleted', (data) => {
      console.log(`🧠 Learning cycle ${data.cycle} completed`);
      this.emit('learningProgress', data);
    });

    // Research Module events
    this.continuousResearchModule.on('researchCycleCompleted', (data) => {
      console.log(`🔬 Research cycle ${data.cycle} completed`);
      this.emit('researchProgress', data);
    });

    // Strategy Generator events
    this.dynamicStrategyGenerator.on('strategyGenerated', (data) => {
      console.log(`🎯 New strategy generated: ${data.strategyId}`);
      this.emit('strategyEvolution', data);
    });

    // Real-time Optimizer events
    this.realTimeOptimizer.on('optimizationCycleCompleted', () => {
      console.log('⚡ Real-time optimization cycle completed');
      this.emit('optimizationProgress');
    });

    this.realTimeOptimizer.on('criticalAlert', (alerts) => {
      console.log(`🚨 Critical alerts: ${alerts.length}`);
      this.emit('criticalAlert', alerts);
    });

    // Knowledge Base events
    this.knowledgeBaseEvolution.on('evolutionCycleCompleted', (data) => {
      console.log(`🧬 Knowledge evolution cycle ${data.cycle} completed`);
      this.emit('knowledgeEvolution', data);
    });
  }

  /**
   * Gelişmiş self-learning pazarlama döngüsünü başlat
   */
  public async startMarketingCycle(): Promise<void> {
    if (this.isRunning) {
      console.log('Marketing cycle is already running');
      return;
    }

    this.isRunning = true;
    console.log('🚀 Starting Self-Learning AI Marketing cycle...');

    // Her 15 dakikada bir döngüyü çalıştır (daha sık optimizasyon)
    this.cycleInterval = setInterval(async () => {
      await this.processAdvancedMarketingCycle();
    }, 15 * 60 * 1000); // 30 dakika

    // İlk döngüyü hemen başlat
    await this.processAdvancedMarketingCycle();

    this.emit('started');
  }

  /**
   * Pazarlama döngüsünü durdur
   */
  public stopMarketingCycle(): void {
    if (!this.isRunning) {
      console.log('Marketing cycle is not running');
      return;
    }

    this.isRunning = false;
    
    if (this.cycleInterval) {
      clearInterval(this.cycleInterval);
      this.cycleInterval = null;
    }

    console.log('AI Marketing cycle stopped');
    this.emit('stopped');
  }

  /**
   * Gelişmiş self-learning pazarlama döngüsü
   */
  private async processAdvancedMarketingCycle(): Promise<void> {
    try {
      this.learningCycleCount++;
      console.log(`🔄 Processing advanced marketing cycle ${this.learningCycleCount}...`);

      // 1. Performans verilerini topla ve analiz et
      const performanceData = await this.collectPerformanceData();

      // 2. Real-time optimizasyonları uygula
      await this.applyRealTimeOptimizations(performanceData);

      // 3. Yeni araştırma verilerini entegre et
      const researchInsights = await this.integrateResearchInsights();

      // 4. Stratejileri dinamik olarak güncelle
      await this.updateStrategiesDynamically(performanceData, researchInsights);

      // 5. Bilgi tabanını genişlet
      await this.expandKnowledgeBase(performanceData, researchInsights);

      // 6. Geleneksel pazarlama görevlerini planla ve işle
      const tasks = await this.planMarketingTasks();

      // 7. Görevleri öğrenme ile işle
      for (const task of tasks) {
        await this.processTaskWithLearning(task);
      }

      // 8. Performans analizi yap
      await this.analytics.analyzePerformance();

      // 9. Döngü sonrası öğrenme analizi
      await this.performPostCycleLearning(performanceData);

      console.log(`✅ Advanced marketing cycle ${this.learningCycleCount} completed`);
      this.emit('cycleCompleted', {
        cycle: this.learningCycleCount,
        performanceData,
        researchInsights: researchInsights.length,
        strategiesUpdated: true
      });

    } catch (error) {
      console.error('❌ Advanced marketing cycle error:', error);
      this.emit('cycleError', error);
    }
  }

  /**
   * Pazarlama görevlerini planla
   */
  private async planMarketingTasks(): Promise<MarketingTask[]> {
    const tasks: MarketingTask[] = [];
    const currentHour = new Date().getHours();

    // Email marketing görevleri (sabah 9-11 arası)
    if (currentHour >= 9 && currentHour <= 11) {
      tasks.push({
        id: `email-${Date.now()}`,
        type: 'email_campaign',
        priority: 'high',
        aiModel: 'email',
        data: {
          action: 'process_campaigns',
          timestamp: new Date()
        },
        requiresApproval: true,
        createdAt: new Date()
      });
    }

    // Sosyal medya görevleri (her 2 saatte bir)
    if (currentHour % 2 === 0) {
      tasks.push({
        id: `social-${Date.now()}`,
        type: 'social_content',
        priority: 'medium',
        aiModel: 'social',
        data: {
          action: 'generate_content',
          platforms: ['facebook', 'instagram', 'linkedin', 'twitter'],
          timestamp: new Date()
        },
        requiresApproval: true,
        createdAt: new Date()
      });
    }

    // Müşteri arama görevleri (öğleden sonra 14-17 arası)
    if (currentHour >= 14 && currentHour <= 17) {
      tasks.push({
        id: `customer-${Date.now()}`,
        type: 'customer_acquisition',
        priority: 'medium',
        aiModel: 'customer',
        data: {
          action: 'find_prospects',
          regions: ['europe', 'north_america', 'middle_east'],
          timestamp: new Date()
        },
        requiresApproval: false,
        createdAt: new Date()
      });
    }

    // Reklam optimizasyonu (her 4 saatte bir)
    if (currentHour % 4 === 0) {
      tasks.push({
        id: `ads-${Date.now()}`,
        type: 'ads_optimization',
        priority: 'high',
        aiModel: 'ads',
        data: {
          action: 'optimize_campaigns',
          platforms: ['google', 'facebook', 'linkedin'],
          timestamp: new Date()
        },
        requiresApproval: false,
        createdAt: new Date()
      });
    }

    return tasks;
  }

  /**
   * Tek bir görevi işle
   */
  private async processTask(task: MarketingTask): Promise<void> {
    try {
      console.log(`Processing task: ${task.type} (${task.id})`);

      // En uygun AI modelini seç
      const aiModel = this.selectOptimalAI(task);
      if (!aiModel) {
        throw new Error(`No suitable AI model found for task: ${task.type}`);
      }

      // Görevi çalıştır
      const result = await aiModel.execute(task);

      // Onay gerekiyorsa onay sistemine gönder
      if (task.requiresApproval) {
        await this.approvalSystem.requestApproval(task, result);
      } else {
        // Direkt uygula
        await this.applyResult(task, result);
      }

      // Performansı takip et
      await this.analytics.trackTaskPerformance(task, result);

      console.log(`Task completed: ${task.id}`);
      this.emit('taskCompleted', { task, result });
    } catch (error) {
      console.error(`Error processing task ${task.id}:`, error);
      this.emit('taskError', { task, error });
    }
  }

  /**
   * Görev için en uygun AI modelini seç
   */
  private selectOptimalAI(task: MarketingTask): AIModel | null {
    const aiModel = this.aiModels.get(task.aiModel);
    
    if (!aiModel) {
      console.error(`AI model not found: ${task.aiModel}`);
      return null;
    }

    // Model sağlık kontrolü
    if (!aiModel.isHealthy()) {
      console.warn(`AI model is not healthy: ${task.aiModel}`);
      return null;
    }

    return aiModel;
  }

  /**
   * Görev sonucunu uygula
   */
  private async applyResult(task: MarketingTask, result: TaskResult): Promise<void> {
    try {
      switch (task.type) {
        case 'email_campaign':
          await this.emailMarketingAI.applyResult(result);
          break;
        case 'social_content':
          await this.socialMediaAI.applyResult(result);
          break;
        case 'customer_acquisition':
          await this.customerAcquisitionAI.applyResult(result);
          break;
        case 'ads_optimization':
          await this.adsManagementAI.applyResult(result);
          break;
        default:
          console.warn(`Unknown task type: ${task.type}`);
      }
    } catch (error) {
      console.error('Error applying result:', error);
      throw error;
    }
  }

  /**
   * Sistem durumunu al
   */
  public async getSystemStatus(): Promise<AISystemStatus> {
    return {
      orchestrator: this.isRunning ? 'active' : 'inactive',
      emailAI: this.emailMarketingAI?.isHealthy() ? 'active' : 'error',
      socialAI: this.socialMediaAI?.isHealthy() ? 'active' : 'error',
      customerAI: this.customerAcquisitionAI?.isHealthy() ? 'active' : 'error',
      adsAI: this.adsManagementAI?.isHealthy() ? 'active' : 'error',
      lastUpdate: new Date(),
      totalTasks: this.taskQueue.length,
      completedTasks: await this.analytics.getCompletedTasksCount(),
      errorCount: await this.analytics.getErrorCount()
    };
  }

  /**
   * Gelişmiş sistem istatistiklerini al
   */
  public async getSystemStats(): Promise<any> {
    return {
      // Temel AI modülleri
      emailMarketing: await this.emailMarketingAI.getStats(),
      socialMedia: await this.socialMediaAI.getStats(),
      customerAcquisition: await this.customerAcquisitionAI.getStats(),
      adManagement: await this.adsManagementAI.getStats(),

      // Self-learning modülleri
      adaptiveLearning: await this.adaptiveLearningEngine.getStats(),
      continuousResearch: await this.continuousResearchModule.getStats(),
      dynamicStrategy: await this.dynamicStrategyGenerator.getStats(),
      realTimeOptimizer: await this.realTimeOptimizer.getStats(),
      knowledgeBase: await this.knowledgeBaseEvolution.getStats(),

      // Sistem durumu
      systemHealth: await this.getSystemStatus(),
      learningProgress: {
        cycleCount: this.learningCycleCount,
        performanceHistory: Object.fromEntries(this.performanceHistory),
        strategyEvolutions: this.strategyEvolutionLog.length,
        researchInsights: this.researchInsights.size
      }
    };
  }

  /**
   * Manuel görev ekleme
   */
  public addTask(task: Omit<MarketingTask, 'id' | 'createdAt'>): void {
    const fullTask: MarketingTask = {
      ...task,
      id: `manual-${Date.now()}`,
      createdAt: new Date()
    };

    this.taskQueue.push(fullTask);
    console.log(`Manual task added: ${fullTask.id}`);
    this.emit('taskAdded', fullTask);
  }

  // ============ SELF-LEARNING METHODS ============

  /**
   * Performans verilerini topla ve analiz et
   */
  private async collectPerformanceData(): Promise<any> {
    const performanceData = {
      email: await this.emailMarketingAI.getStats(),
      social: await this.socialMediaAI.getStats(),
      customer: await this.customerAcquisitionAI.getStats(),
      ads: await this.adsManagementAI.getStats(),
      timestamp: new Date()
    };

    // Performans geçmişini güncelle
    for (const [key, value] of Object.entries(performanceData)) {
      if (key !== 'timestamp' && typeof value === 'object' && value !== null) {
        const history = this.performanceHistory.get(key) || [];
        history.push(this.calculatePerformanceScore(value));

        // Son 100 veriyi tut
        if (history.length > 100) {
          history.shift();
        }

        this.performanceHistory.set(key, history);
      }
    }

    return performanceData;
  }

  /**
   * Real-time optimizasyonları uygula
   */
  private async applyRealTimeOptimizations(performanceData: any): Promise<void> {
    try {
      await this.realTimeOptimizer.execute({
        id: `optimize-${Date.now()}`,
        type: 'ads_optimization',
        priority: 'high',
        aiModel: 'optimizer',
        data: {
          action: 'optimize_performance',
          campaigns: await this.getAllActiveCampaigns(),
          metrics: performanceData,
          timeframe: 'realtime'
        },
        requiresApproval: false,
        createdAt: new Date()
      });
    } catch (error) {
      console.error('Real-time optimization error:', error);
    }
  }

  /**
   * Araştırma verilerini entegre et
   */
  private async integrateResearchInsights(): Promise<any[]> {
    try {
      const result = await this.continuousResearchModule.execute({
        id: `research-${Date.now()}`,
        type: 'analytics_report',
        priority: 'medium',
        aiModel: 'research',
        data: {
          action: 'research_market_trends',
          keywords: ['natural stone', 'marble', 'granite', 'construction'],
          targetMarkets: ['US', 'DE', 'GB', 'FR', 'JP']
        },
        requiresApproval: false,
        createdAt: new Date()
      });

      const insights = result.data?.trends || [];

      // Araştırma içgörülerini kaydet
      for (const insight of insights) {
        this.researchInsights.set(`insight-${Date.now()}-${Math.random()}`, insight);
      }

      return insights;
    } catch (error) {
      console.error('Research integration error:', error);
      return [];
    }
  }

  /**
   * Stratejileri dinamik olarak güncelle
   */
  private async updateStrategiesDynamically(performanceData: any, researchInsights: any[]): Promise<void> {
    try {
      const result = await this.dynamicStrategyGenerator.execute({
        id: `strategy-${Date.now()}`,
        type: 'strategy',
        priority: 'high',
        aiModel: 'strategy',
        data: {
          action: 'evolve_strategy',
          currentStrategy: await this.getCurrentStrategy(),
          performanceData,
          marketInsights: researchInsights
        },
        requiresApproval: false,
        createdAt: new Date()
      });

      if (result.success && result.data) {
        this.strategyEvolutionLog.push({
          timestamp: new Date(),
          evolution: result.data,
          performanceImpact: this.calculateStrategyImpact(result.data)
        });
      }
    } catch (error) {
      console.error('Strategy update error:', error);
    }
  }

  /**
   * Bilgi tabanını genişlet
   */
  private async expandKnowledgeBase(performanceData: any, researchInsights: any[]): Promise<void> {
    try {
      await this.knowledgeBaseEvolution.execute({
        id: `knowledge-${Date.now()}`,
        type: 'knowledge',
        priority: 'medium',
        aiModel: 'knowledge',
        data: {
          action: 'learn_from_data',
          dataSource: 'performance_analysis',
          dataType: 'metrics',
          content: { performanceData, researchInsights }
        },
        requiresApproval: false,
        createdAt: new Date()
      });
    } catch (error) {
      console.error('Knowledge expansion error:', error);
    }
  }

  /**
   * Görevi öğrenme ile işle
   */
  private async processTaskWithLearning(task: MarketingTask): Promise<void> {
    try {
      console.log(`🧠 Processing task with learning: ${task.type} (${task.id})`);

      // Görev öncesi öğrenme analizi
      const preTaskInsights = await this.analyzeTaskContext(task);

      // Optimal AI modelini seç (öğrenme ile)
      const aiModel = await this.selectOptimalAIWithLearning(task, preTaskInsights);

      if (!aiModel) {
        throw new Error(`No suitable AI model found for task: ${task.type}`);
      }

      // Görevi çalıştır
      const result = await aiModel.execute(task);

      // Sonucu öğrenme ile analiz et
      await this.analyzeTaskResult(task, result, preTaskInsights);

      // Onay gerekiyorsa
      if (task.requiresApproval) {
        await this.approvalSystem.requestApproval(task, result);
      } else {
        await this.applyResult(task, result);
      }

      // Performans takibi
      await this.analytics.trackTaskPerformance(task, result);

      // Öğrenme verilerini kaydet
      await this.recordLearningData(task, result, preTaskInsights);

      console.log(`✅ Task completed with learning: ${task.id}`);
      this.emit('taskCompleted', { task, result, learningApplied: true });

    } catch (error) {
      console.error(`❌ Error processing task with learning ${task.id}:`, error);
      this.emit('taskError', { task, error });
    }
  }

  /**
   * Döngü sonrası öğrenme analizi
   */
  private async performPostCycleLearning(performanceData: any): Promise<void> {
    try {
      // Adaptive learning engine ile analiz
      await this.adaptiveLearningEngine.execute({
        id: `learning-${Date.now()}`,
        type: 'learning',
        priority: 'high',
        aiModel: 'learning',
        data: {
          action: 'analyze_performance',
          performanceData,
          cycleNumber: this.learningCycleCount
        },
        requiresApproval: false,
        createdAt: new Date()
      });
    } catch (error) {
      console.error('Post-cycle learning error:', error);
    }
  }

  // ============ HELPER METHODS ============

  /**
   * Performans skorunu hesapla
   */
  private calculatePerformanceScore(stats: any): number {
    let score = 0;
    let count = 0;

    if (stats.openRate !== undefined) {
      score += stats.openRate;
      count++;
    }
    if (stats.clickRate !== undefined) {
      score += stats.clickRate * 10;
      count++;
    }
    if (stats.conversionRate !== undefined) {
      score += stats.conversionRate * 20;
      count++;
    }
    if (stats.engagementRate !== undefined) {
      score += stats.engagementRate * 5;
      count++;
    }

    return count > 0 ? score / count : 0;
  }

  /**
   * Aktif kampanyaları getir
   */
  private async getAllActiveCampaigns(): Promise<any[]> {
    return [
      { id: 'email-campaign-1', type: 'email', status: 'active' },
      { id: 'social-campaign-1', type: 'social', status: 'active' },
      { id: 'ads-campaign-1', type: 'ads', status: 'active' }
    ];
  }

  /**
   * Mevcut stratejiyi getir
   */
  private async getCurrentStrategy(): Promise<any> {
    return {
      name: 'Natural Stone B2B International',
      channels: ['email', 'linkedin', 'google-ads'],
      targetMarkets: ['US', 'DE', 'GB'],
      budget: 10000,
      timeline: '3 months'
    };
  }

  /**
   * Strateji etkisini hesapla
   */
  private calculateStrategyImpact(strategyData: any): number {
    return strategyData.expectedImprovement || 0;
  }

  /**
   * Görev bağlamını analiz et
   */
  private async analyzeTaskContext(task: MarketingTask): Promise<any> {
    return {
      taskType: task.type,
      priority: task.priority,
      historicalPerformance: this.performanceHistory.get(task.type) || [],
      relatedInsights: Array.from(this.researchInsights.values()).filter(
        insight => insight.category === task.type
      ),
      timestamp: new Date()
    };
  }

  /**
   * Öğrenme ile optimal AI seç
   */
  private async selectOptimalAIWithLearning(task: MarketingTask, insights: any): Promise<AIModel | null> {
    const baseModel = this.selectOptimalAI(task);

    if (insights.historicalPerformance.length > 0) {
      const avgPerformance = insights.historicalPerformance.reduce((a: number, b: number) => a + b, 0) / insights.historicalPerformance.length;

      if (avgPerformance < 50) {
        console.log(`🔄 Low performance detected for ${task.type}, considering alternatives`);
      }
    }

    return baseModel;
  }

  /**
   * Görev sonucunu analiz et
   */
  private async analyzeTaskResult(task: MarketingTask, result: TaskResult, preTaskInsights: any): Promise<void> {
    const analysis = {
      taskId: task.id,
      taskType: task.type,
      success: result.success,
      executionTime: result.executionTime,
      preTaskInsights,
      resultData: result.data,
      timestamp: new Date()
    };

    try {
      await this.adaptiveLearningEngine.execute({
        id: `analysis-${Date.now()}`,
        type: 'analytics_report',
        priority: 'low',
        aiModel: 'learning',
        data: {
          action: 'analyze_task_result',
          analysis
        },
        requiresApproval: false,
        createdAt: new Date()
      });
    } catch (error) {
      console.error('Task result analysis error:', error);
    }
  }

  /**
   * Öğrenme verilerini kaydet
   */
  private async recordLearningData(task: MarketingTask, result: TaskResult, insights: any): Promise<void> {
    const learningData = {
      task: { id: task.id, type: task.type, priority: task.priority },
      result: { success: result.success, executionTime: result.executionTime, aiModel: result.aiModel },
      insights,
      timestamp: new Date()
    };

    try {
      await this.knowledgeBaseEvolution.execute({
        id: `record-${Date.now()}`,
        type: 'knowledge',
        priority: 'low',
        aiModel: 'knowledge',
        data: {
          action: 'learn_from_data',
          dataSource: 'task_execution',
          dataType: 'performance',
          content: learningData
        },
        requiresApproval: false,
        createdAt: new Date()
      });
    } catch (error) {
      console.error('Learning data recording error:', error);
    }
  }

  /**
   * Temizlik işlemleri
   */
  public async cleanup(): Promise<void> {
    this.stopMarketingCycle();

    // AI modüllerini temizle
    for (const [name, model] of this.aiModels) {
      try {
        await model.cleanup();
      } catch (error) {
        console.error(`Error cleaning up ${name}:`, error);
      }
    }

    this.aiModels.clear();
    this.taskQueue = [];

    console.log('🧠 Self-Learning AI Marketing Orchestrator cleaned up');
    this.emit('cleanup');
  }
}
