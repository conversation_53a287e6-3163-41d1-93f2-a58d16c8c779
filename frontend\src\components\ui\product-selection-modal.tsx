"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Button } from "./button"
import { Input } from "./input"
import { Badge } from "./badge"
import { Card, CardContent } from "./card"

interface Product {
  id: string
  name: string
  category: string
  image: string
  price?: {
    min: number
    max: number
    currency: string
    unit: string
  }
}

interface ProductSelectionModalProps {
  isOpen: boolean
  onClose: () => void
  onProductsSelected: (products: Product[]) => void
  maxSelection?: number
}

// Mock products data - bu gerçek uygulamada API'den gelecek
const mockProducts: Product[] = [
  {
    id: "1",
    name: "<PERSON>az Mermer",
    category: "Mermer",
    image: "/images/products/beyaz-mermer.jpg"
  },
  {
    id: "2", 
    name: "Siyah Granit",
    category: "Granit",
    image: "/images/products/siyah-granit.jpg"
  },
  {
    id: "3",
    name: "Traverten Bej",
    category: "Traverten", 
    image: "/images/products/traverten-bej.jpg"
  },
  {
    id: "4",
    name: "Onik<PERSON>",
    category: "Oniks",
    image: "/images/products/oniks-yesil.jpg"
  },
  {
    id: "5",
    name: "Kireçtaşı Gri",
    category: "Kireçtaşı",
    image: "/images/products/kirec-gri.jpg"
  },
  {
    id: "6",
    name: "Kuvars Beyaz",
    category: "Kuvars",
    image: "/images/products/kuvars-beyaz.jpg"
  }
]

const categories = ["Tümü", "Mermer", "Granit", "Traverten", "Oniks", "Kireçtaşı", "Kuvars"]

export function ProductSelectionModal({ 
  isOpen, 
  onClose, 
  onProductsSelected, 
  maxSelection = 5 
}: ProductSelectionModalProps) {
  const [selectedProducts, setSelectedProducts] = React.useState<Product[]>([])
  const [searchTerm, setSearchTerm] = React.useState("")
  const [selectedCategory, setSelectedCategory] = React.useState("Tümü")

  // Filter products based on search and category
  const filteredProducts = React.useMemo(() => {
    return mockProducts.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === "Tümü" || product.category === selectedCategory
      return matchesSearch && matchesCategory
    })
  }, [searchTerm, selectedCategory])

  const handleProductToggle = (product: Product) => {
    setSelectedProducts(prev => {
      const isSelected = prev.some(p => p.id === product.id)
      if (isSelected) {
        return prev.filter(p => p.id !== product.id)
      } else if (prev.length < maxSelection) {
        return [...prev, product]
      }
      return prev
    })
  }

  const handleConfirmSelection = () => {
    onProductsSelected(selectedProducts)
    onClose()
    // Reset state
    setSelectedProducts([])
    setSearchTerm("")
    setSelectedCategory("Tümü")
  }

  const handleCancel = () => {
    onClose()
    // Reset state
    setSelectedProducts([])
    setSearchTerm("")
    setSelectedCategory("Tümü")
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={handleCancel}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Ürün Seçimi</h2>
            <p className="text-gray-600">
              Teklif isteyeceğiniz ürünleri seçin (En fazla {maxSelection} ürün)
            </p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </Button>
        </div>

        {/* Search and Filter */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Ürün adı ile ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex gap-2 flex-wrap">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Selected Products Summary */}
        {selectedProducts.length > 0 && (
          <div className="p-4 bg-blue-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-900">
                Seçilen Ürünler ({selectedProducts.length}/{maxSelection})
              </span>
              <div className="flex gap-2 flex-wrap">
                {selectedProducts.map((product) => (
                  <Badge key={product.id} variant="secondary" className="text-xs">
                    {product.name}
                    <button
                      onClick={() => handleProductToggle(product)}
                      className="ml-1 text-gray-500 hover:text-gray-700"
                    >
                      ×
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Products Grid */}
        <div className="flex-1 overflow-y-auto p-6">
          {filteredProducts.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-lg mb-2">🔍</div>
              <p className="text-gray-600">Aradığınız kriterlere uygun ürün bulunamadı.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filteredProducts.map((product) => {
                const isSelected = selectedProducts.some(p => p.id === product.id)
                const canSelect = selectedProducts.length < maxSelection || isSelected
                
                return (
                  <Card 
                    key={product.id}
                    className={cn(
                      "cursor-pointer transition-all duration-200 hover:shadow-md",
                      isSelected && "ring-2 ring-blue-500 bg-blue-50",
                      !canSelect && "opacity-50 cursor-not-allowed"
                    )}
                    onClick={() => canSelect && handleProductToggle(product)}
                  >
                    <CardContent className="p-4">
                      <div className="aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                        <span className="text-2xl">🪨</span>
                      </div>
                      <h3 className="font-medium text-gray-900 mb-1">{product.name}</h3>
                      <p className="text-sm text-gray-600">{product.category}</p>
                      {isSelected && (
                        <div className="mt-2">
                          <Badge variant="default" className="text-xs">
                            ✓ Seçildi
                          </Badge>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            {selectedProducts.length} ürün seçildi
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={handleCancel}>
              İptal
            </Button>
            <Button 
              onClick={handleConfirmSelection}
              disabled={selectedProducts.length === 0}
            >
              Devam Et ({selectedProducts.length})
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
