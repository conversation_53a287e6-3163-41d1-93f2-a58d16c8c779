'use client';

import React, { useEffect, useRef } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
    fill?: boolean;
    tension?: number;
  }[];
}

interface ReportChartProps {
  type: 'line' | 'bar' | 'pie' | 'doughnut';
  data: ChartData;
  title?: string;
  height?: number;
  options?: any;
}

export default function ReportChart({ 
  type, 
  data, 
  title, 
  height = 300,
  options = {} 
}: ReportChartProps) {
  const chartRef = useRef<any>(null);

  // Default chart options
  const defaultOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: {
          bottom: 20
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            const label = context.dataset.label || '';
            const value = context.parsed.y || context.parsed;
            
            // Format based on chart type and data
            if (type === 'pie' || type === 'doughnut') {
              const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
              const percentage = ((value / total) * 100).toFixed(1);
              return `${label}: ${value.toLocaleString('tr-TR')} (${percentage}%)`;
            }
            
            // Check if value looks like currency
            if (value > 1000) {
              return `${label}: ${new Intl.NumberFormat('tr-TR', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0
              }).format(value)}`;
            }
            
            return `${label}: ${value.toLocaleString('tr-TR')}`;
          }
        }
      }
    },
    scales: type === 'pie' || type === 'doughnut' ? {} : {
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 11
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          },
          callback: function(value: any) {
            // Format large numbers
            if (value >= 1000000) {
              return (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
              return (value / 1000).toFixed(0) + 'K';
            }
            return value;
          }
        }
      }
    },
    animation: {
      duration: 1000,
      easing: 'easeInOutQuart'
    },
    interaction: {
      intersect: false,
      mode: 'index' as const
    }
  };

  // Merge default options with custom options
  const mergedOptions = {
    ...defaultOptions,
    ...options,
    plugins: {
      ...defaultOptions.plugins,
      ...options.plugins
    }
  };

  const getDefaultColors = (chartType: string, index: number, total: number) => {
    const colors = [
      '#3B82F6', // Blue
      '#10B981', // Green
      '#F59E0B', // Yellow
      '#EF4444', // Red
      '#8B5CF6', // Purple
      '#06B6D4', // Cyan
      '#F97316', // Orange
      '#84CC16', // Lime
      '#EC4899', // Pink
      '#6B7280'  // Gray
    ];

    if (chartType === 'pie' || chartType === 'doughnut') {
      return colors.slice(0, total);
    }

    return colors[index % colors.length];
  };

  const getDefaultBorderColors = (chartType: string, index: number) => {
    const borderColors = [
      '#2563EB', // Blue
      '#059669', // Green
      '#D97706', // Yellow
      '#DC2626', // Red
      '#7C3AED', // Purple
      '#0891B2', // Cyan
      '#EA580C', // Orange
      '#65A30D', // Lime
      '#DB2777', // Pink
      '#4B5563'  // Gray
    ];

    return borderColors[index % borderColors.length];
  };

  // Enhanced data with better colors
  const enhancedData = {
    ...data,
    datasets: data.datasets.map((dataset, index) => ({
      ...dataset,
      backgroundColor: dataset.backgroundColor || getDefaultColors(type, index, data.datasets.length),
      borderColor: dataset.borderColor || getDefaultBorderColors(type, index),
      borderWidth: dataset.borderWidth || (type === 'line' ? 2 : 1),
      tension: type === 'line' ? (dataset.tension || 0.4) : undefined,
      fill: type === 'line' ? (dataset.fill !== undefined ? dataset.fill : false) : undefined
    }))
  };

  const renderChart = () => {
    const commonProps = {
      ref: chartRef,
      data: enhancedData,
      options: mergedOptions,
      height
    };

    switch (type) {
      case 'line':
        return <Line {...commonProps} />;
      case 'bar':
        return <Bar {...commonProps} />;
      case 'pie':
        return <Pie {...commonProps} />;
      case 'doughnut':
        return <Doughnut {...commonProps} />;
      default:
        return <div>Unsupported chart type</div>;
    }
  };

  return (
    <div style={{ height: `${height}px`, position: 'relative' }}>
      {renderChart()}
    </div>
  );
}

// Sample data generators for testing
export const generateSampleLineData = (): ChartData => ({
  labels: ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran'],
  datasets: [
    {
      label: 'Gelir',
      data: [450000, 520000, 480000, 610000, 580000, 650000],
      borderColor: '#3B82F6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      fill: true,
      tension: 0.4
    },
    {
      label: 'Komisyon',
      data: [45000, 52000, 48000, 61000, 58000, 65000],
      borderColor: '#10B981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      fill: true,
      tension: 0.4
    }
  ]
});

export const generateSampleBarData = (): ChartData => ({
  labels: ['Mermer', 'Traverten', 'Granit', 'Oniks', 'Plaka'],
  datasets: [
    {
      label: 'Sipariş Sayısı',
      data: [156, 98, 87, 45, 32],
      backgroundColor: [
        '#3B82F6',
        '#10B981',
        '#F59E0B',
        '#EF4444',
        '#8B5CF6'
      ]
    }
  ]
});

export const generateSamplePieData = (): ChartData => ({
  labels: ['Banka Havalesi', 'Kredi Kartı', 'Akreditif'],
  datasets: [
    {
      label: 'Ödeme Yöntemleri',
      data: [75, 20, 5],
      backgroundColor: [
        '#3B82F6',
        '#10B981',
        '#F59E0B'
      ]
    }
  ]
});
