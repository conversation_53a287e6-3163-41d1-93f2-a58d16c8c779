import { Router, Request, Response } from 'express';
import { authMiddleware } from '../../middleware/authMiddleware';
import { asyncHandler } from '../../middleware/errorHandler';
import { CommissionService } from '../../services/CommissionService';
import { z } from 'zod';

const router = Router();
const commissionService = new CommissionService();

// Apply authentication middleware to all routes
router.use(authMiddleware);

/**
 * @route GET /api/commission/stats
 * @desc Get commission statistics for admin dashboard
 * @access Private (Admin only)
 */
router.get('/stats', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const { startDate, endDate } = req.query;
  
  const start = startDate ? new Date(startDate as string) : undefined;
  const end = endDate ? new Date(endDate as string) : undefined;

  const stats = await commissionService.getCommissionStats(start, end);

  res.json({
    success: true,
    data: stats
  });
}));

/**
 * @route GET /api/commission/by-category
 * @desc Get commission breakdown by product category
 * @access Private (Admin only)
 */
router.get('/by-category', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const { startDate, endDate } = req.query;
  
  const start = startDate ? new Date(startDate as string) : undefined;
  const end = endDate ? new Date(endDate as string) : undefined;

  const categoryStats = await commissionService.getCommissionByCategory(start, end);

  // Calculate percentages
  const totalAmount = categoryStats.reduce((sum, cat) => sum + cat.amount, 0);
  const categoriesWithPercentage = categoryStats.map(cat => ({
    ...cat,
    percentage: totalAmount > 0 ? (cat.amount / totalAmount) * 100 : 0
  }));

  res.json({
    success: true,
    data: categoriesWithPercentage
  });
}));

/**
 * @route GET /api/commission/by-producer
 * @desc Get commission breakdown by producer
 * @access Private (Admin only)
 */
router.get('/by-producer', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const { startDate, endDate } = req.query;
  
  const start = startDate ? new Date(startDate as string) : undefined;
  const end = endDate ? new Date(endDate as string) : undefined;

  const producerStats = await commissionService.getCommissionByProducer(start, end);

  res.json({
    success: true,
    data: producerStats
  });
}));

/**
 * @route POST /api/commission/calculate
 * @desc Calculate commission for a given amount and m²
 * @access Private (Admin only)
 */
router.post('/calculate', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const schema = z.object({
    totalAmount: z.number().positive(),
    totalM2: z.number().positive().optional(),
    rate: z.number().min(0).max(1).optional()
  });

  const { totalAmount, totalM2, rate } = schema.parse(req.body);

  const calculation = commissionService.calculateCommission(totalAmount, totalM2, rate);
  const producerBreakdown = commissionService.calculateProducerAmount(totalAmount, totalM2);

  res.json({
    success: true,
    data: {
      calculation,
      breakdown: {
        totalAmount,
        commission: producerBreakdown.commission,
        producerAmount: producerBreakdown.producerAmount,
        commissionPercentage: totalAmount > 0 ? (producerBreakdown.commission / totalAmount) * 100 : 0
      }
    }
  });
}));

/**
 * @route GET /api/commission/settings
 * @desc Get commission settings
 * @access Private (Admin only)
 */
router.get('/settings', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const settings = {
    commissionPerM2: parseFloat(process.env.PLATFORM_COMMISSION_PER_M2 || '1.00'),
    commissionCurrency: process.env.PLATFORM_COMMISSION_CURRENCY || 'USD',
    fallbackCommissionRate: parseFloat(process.env.PLATFORM_COMMISSION_RATE || '0.05'),
    calculationMethod: process.env.COMMISSION_CALCULATION_METHOD || 'PER_M2',
    platformBankInfo: {
      iban: process.env.PLATFORM_IBAN,
      bankName: process.env.PLATFORM_BANK_NAME,
      accountHolder: process.env.PLATFORM_ACCOUNT_HOLDER
    }
  };

  res.json({
    success: true,
    data: settings
  });
}));

/**
 * @route GET /api/commission/monthly-trend
 * @desc Get monthly commission trend data
 * @access Private (Admin only)
 */
router.get('/monthly-trend', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const { months = 12 } = req.query;
  
  // Calculate date range for the last N months
  const endDate = new Date();
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - parseInt(months as string));

  const stats = await commissionService.getCommissionStats(startDate, endDate);

  res.json({
    success: true,
    data: {
      monthlyStats: stats.monthlyStats,
      summary: {
        totalCommission: stats.totalCommission,
        totalM2Processed: stats.totalM2Processed,
        totalTransactions: stats.totalTransactions,
        averageCommissionPerM2: stats.averageCommissionPerM2
      }
    }
  });
}));

/**
 * @route POST /api/commission/process-payment
 * @desc Process commission for a payment (internal use)
 * @access Private (Admin only)
 */
router.post('/process-payment', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const schema = z.object({
    paymentId: z.string(),
    orderId: z.string(),
    totalAmount: z.number().positive(),
    totalM2: z.number().positive().optional()
  });

  const { paymentId, orderId, totalAmount, totalM2 } = schema.parse(req.body);

  const calculation = commissionService.calculateCommission(totalAmount, totalM2);
  const commissionRecord = await commissionService.createCommissionRecord(
    paymentId,
    orderId,
    calculation
  );

  res.json({
    success: true,
    message: 'Commission processed successfully',
    data: {
      commissionRecord,
      calculation,
      producerAmount: totalAmount - calculation.amount
    }
  });
}));

export default router;
