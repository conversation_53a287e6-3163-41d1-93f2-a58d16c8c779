'use client'

import React from 'react'
import { useProducts } from '@/contexts/products-context'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye, 
  MessageSquare,
  Calendar,
  User,
  Package
} from 'lucide-react'

interface PendingProduct {
  id: string
  name: string
  category: string
  producer: string
  submittedAt: Date
  status: 'pending' | 'approved' | 'rejected'
  rejectionReason?: string
  coverImage: string
  description: string
  approvalStatus: 'pending' | 'approved' | 'rejected'
}

export default function ProductApprovalsPage() {
  const { products, updateProduct, getPendingProducts } = useProducts()
  const pendingProducts = getPendingProducts()

  const [selectedProduct, setSelectedProduct] = React.useState<any>(null)
  const [rejectionReason, setRejectionReason] = React.useState('')
  const [showRejectModal, setShowRejectModal] = React.useState(false)

  const handleApprove = async (productId: string) => {
    try {
      // API call to approve product
      console.log('Approving product:', productId)

      updateProduct(productId, {
        approvalStatus: 'approved',
        reviewedAt: new Date(),
        reviewedBy: 'Admin'
      })

      alert('Ürün başarıyla onaylandı! Artık müşteriler tarafından görülebilir.')
    } catch (error) {
      alert('Onaylama işlemi sırasında bir hata oluştu.')
    }
  }

  const handleReject = async (productId: string, reason: string) => {
    try {
      // API call to reject product
      console.log('Rejecting product:', productId, 'Reason:', reason)

      updateProduct(productId, {
        approvalStatus: 'rejected',
        rejectionReason: reason,
        reviewedAt: new Date(),
        reviewedBy: 'Admin'
      })

      setShowRejectModal(false)
      setRejectionReason('')
      setSelectedProduct(null)

      alert('Ürün reddedildi ve üreticiye bildirim gönderildi.')
    } catch (error) {
      alert('Reddetme işlemi sırasında bir hata oluştu.')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Beklemede</Badge>
      case 'approved':
        return <Badge variant="secondary" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Onaylandı</Badge>
      case 'rejected':
        return <Badge variant="secondary" className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Reddedildi</Badge>
      default:
        return null
    }
  }

  const pendingCount = pendingProducts.filter(p => p.status === 'pending').length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Ürün Onayları</h1>
          <p className="text-gray-600 mt-1">
            Üreticiler tarafından gönderilen ürünleri inceleyin ve onaylayın
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-lg px-4 py-2">
            <Clock className="w-4 h-4 mr-2" />
            {pendingCount} Bekleyen Onay
          </Badge>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {pendingProducts.map((product) => (
          <Card key={product.id} className="overflow-hidden">
            {/* Product Image */}
            <div className="aspect-video bg-gray-200 relative">
              <img
                src={product.image || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'}
                alt={product.name}
                className="w-full h-full object-cover"
              />
              <div className="absolute top-2 right-2">
                {getStatusBadge(product.approvalStatus)}
              </div>
            </div>

            {/* Product Info */}
            <div className="p-4">
              <div className="flex items-start justify-between mb-2">
                <h3 className="font-semibold text-lg text-gray-900">{product.name}</h3>
                <Badge variant="outline">{product.category}</Badge>
              </div>

              <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                {product.description}
              </p>

              <div className="space-y-2 mb-4">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <User className="w-4 h-4" />
                  {product.producer}
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Calendar className="w-4 h-4" />
                  {product.submittedAt ? new Date(product.submittedAt).toLocaleDateString('tr-TR') : new Date(product.createdAt).toLocaleDateString('tr-TR')}
                </div>
              </div>

              {/* View Details Button */}
              <div className="mb-3">
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    window.location.href = `/admin/product-approvals/${product.id}`
                  }}
                >
                  <Eye className="w-4 h-4 mr-1" />
                  Detayları Görüntüle
                </Button>
              </div>

              {/* Action Buttons */}
              {product.approvalStatus === 'pending' && (
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    className="flex-1 bg-green-600 hover:bg-green-700"
                    onClick={() => handleApprove(product.id)}
                  >
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Onayla
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1 text-red-600 border-red-600 hover:bg-red-50"
                    onClick={() => {
                      setSelectedProduct(product)
                      setShowRejectModal(true)
                    }}
                  >
                    <XCircle className="w-4 h-4 mr-1" />
                    Reddet
                  </Button>
                </div>
              )}

              {product.approvalStatus === 'rejected' && product.rejectionReason && (
                <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded">
                  <p className="text-sm text-red-800">
                    <strong>Red Sebebi:</strong> {product.rejectionReason}
                  </p>
                </div>
              )}
            </div>
          </Card>
        ))}
      </div>



      {/* Reject Modal */}
      {showRejectModal && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Ürünü Reddet</h3>
            <p className="text-gray-600 mb-4">
              <strong>{selectedProduct.name}</strong> ürününü neden reddediyorsunuz?
            </p>
            <textarea
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder="Red sebebini açıklayın..."
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
              rows={4}
            />
            <div className="flex gap-3 mt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setShowRejectModal(false)
                  setRejectionReason('')
                  setSelectedProduct(null)
                }}
                className="flex-1"
              >
                İptal
              </Button>
              <Button
                onClick={() => handleReject(selectedProduct.id, rejectionReason)}
                disabled={!rejectionReason.trim()}
                className="flex-1 bg-red-600 hover:bg-red-700"
              >
                Reddet
              </Button>
            </div>
          </div>
        </div>
      )}

      {pendingProducts.length === 0 && (
        <div className="text-center py-12">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Bekleyen ürün yok</h3>
          <p className="text-gray-600">Şu anda onay bekleyen ürün bulunmuyor.</p>
        </div>
      )}
    </div>
  )
}
