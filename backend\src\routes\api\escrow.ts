import { Router, Request, Response } from 'express';
import { EscrowController } from '../../controllers/EscrowController';
import { authMiddleware } from '../../middleware/authMiddleware';
import { asyncHandler } from '../../middleware/errorHandler';

const router = Router();
const escrowController = new EscrowController();

// Apply authentication middleware to all routes
router.use(authMiddleware);

/**
 * @route POST /api/escrow/create
 * @desc Create escrow account for order
 * @access Private (Admin only)
 */
router.post('/create', escrowController.createEscrow);

/**
 * @route GET /api/escrow/:escrowId/bank-info
 * @desc Get bank transfer information for customer
 * @access Private (Customer or Admin)
 */
router.get('/:escrowId/bank-info', escrowController.getBankTransferInfo);

/**
 * @route POST /api/escrow/confirm-payment
 * @desc Confirm customer payment received
 * @access Private (Admin only)
 */
router.post('/confirm-payment', escrowController.confirmPayment);

/**
 * @route POST /api/escrow/goods-ready
 * @desc Producer notifies goods are ready
 * @access Private (Producer only)
 */
router.post('/goods-ready', escrowController.notifyGoodsReady);

/**
 * @route POST /api/escrow/:escrowId/approve
 * @desc Customer approves payment to producer
 * @access Private (Customer only)
 */
router.post('/:escrowId/approve', escrowController.approvePayment);

/**
 * @route GET /api/escrow/:escrowId/workflow
 * @desc Get payment workflow status
 * @access Private (Customer, Producer, or Admin)
 */
router.get('/:escrowId/workflow', escrowController.getWorkflow);

/**
 * @route POST /api/escrow/:escrowId/dispute
 * @desc Create dispute
 * @access Private (Customer or Producer)
 */
router.post('/:escrowId/dispute', escrowController.createDispute);

/**
 * @route POST /api/escrow/:escrowId/resolve-dispute
 * @desc Resolve dispute
 * @access Private (Admin only)
 */
router.post('/:escrowId/resolve-dispute', escrowController.resolveDispute);

/**
 * @route GET /api/escrow/stats
 * @desc Get escrow statistics
 * @access Private (Admin only)
 */
router.get('/stats', escrowController.getStats);

/**
 * @route GET /api/escrow/my-escrows
 * @desc Get user's escrow accounts
 * @access Private
 */
router.get('/my-escrows', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();

  let whereClause: any = {};
  
  if (user.userType === 'customer') {
    whereClause.customerId = user.id;
  } else if (user.userType === 'producer') {
    whereClause.producerId = user.id;
  } else if (user.userType === 'admin') {
    // Admin can see all
  } else {
    return res.status(403).json({
      success: false,
      error: 'Access denied'
    });
  }

  const escrows = await prisma.escrowAccount.findMany({
    where: whereClause,
    include: {
      order: {
        include: {
          customer: { select: { id: true, email: true, companyName: true } },
          producer: { select: { id: true, email: true, companyName: true } },
        }
      }
    },
    orderBy: { createdAt: 'desc' },
  });

  res.json({
    success: true,
    data: { escrows }
  });
}));

/**
 * @route GET /api/escrow/pending-actions
 * @desc Get pending actions for user
 * @access Private
 */
router.get('/pending-actions', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();

  let pendingActions: any[] = [];

  if (user.userType === 'customer') {
    // Customer pending actions
    const pendingPayments = await prisma.escrowAccount.findMany({
      where: {
        customerId: user.id,
        status: 'PENDING',
      },
      include: { order: true },
    });

    const pendingApprovals = await prisma.escrowAccount.findMany({
      where: {
        customerId: user.id,
        status: 'HELD',
        producerNotifiedAt: { not: null },
        customerApprovedAt: null,
      },
      include: { order: true },
    });

    pendingActions = [
      ...pendingPayments.map((escrow: any) => ({
        type: 'payment_required',
        escrowId: escrow.id,
        orderId: escrow.orderId,
        amount: escrow.totalAmount,
        description: 'Ödeme yapmanız bekleniyor',
        priority: 'high',
      })),
      ...pendingApprovals.map((escrow: any) => ({
        type: 'approval_required',
        escrowId: escrow.id,
        orderId: escrow.orderId,
        description: 'Malın hazır olduğu bildirildi, onayınız bekleniyor',
        priority: 'medium',
      })),
    ];
  } else if (user.userType === 'producer') {
    // Producer pending actions
    const pendingProduction = await prisma.escrowAccount.findMany({
      where: {
        producerId: user.id,
        status: 'HELD',
        producerNotifiedAt: null,
      },
      include: { order: true },
    });

    pendingActions = pendingProduction.map((escrow: any) => ({
      type: 'production_required',
      escrowId: escrow.id,
      orderId: escrow.orderId,
      description: 'Ödeme alındı, üretimi tamamlayıp bildirin',
      priority: 'high',
    }));
  } else if (user.userType === 'admin') {
    // Admin pending actions
    const pendingConfirmations = await prisma.escrowAccount.findMany({
      where: { status: 'PENDING' },
      include: { order: true },
    });

    const pendingPayments = await prisma.escrowAccount.findMany({
      where: {
        status: 'HELD',
        customerApprovedAt: { not: null },
        producerPaidAt: null,
      },
      include: { order: true },
    });

    pendingActions = [
      ...pendingConfirmations.map((escrow: any) => ({
        type: 'payment_confirmation',
        escrowId: escrow.id,
        orderId: escrow.orderId,
        description: 'Müşteri ödemesi onaylanması bekleniyor',
        priority: 'high',
      })),
      ...pendingPayments.map((escrow: any) => ({
        type: 'producer_payment',
        escrowId: escrow.id,
        orderId: escrow.orderId,
        amount: escrow.producerAmount,
        description: 'Üreticiye ödeme yapılması bekleniyor',
        priority: 'medium',
      })),
    ];
  }

  res.json({
    success: true,
    data: { pendingActions }
  });
}));

export default router;
