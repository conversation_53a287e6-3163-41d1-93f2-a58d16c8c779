'use client';

import React, { useState } from 'react';
import { 
  RotateCcw, 
  Grid3X3, 
  MapPin, 
  RotateCw, 
  Settings, 
  Eye, 
  EyeOff,
  Palette,
  Layers
} from 'lucide-react';
import { ViewerControlsProps, AssetQuality } from '../../types/3d';

export const ViewerControls: React.FC<ViewerControlsProps> = ({
  onCameraReset,
  onToggleWireframe,
  onToggleAnnotations,
  onToggleAutoRotate,
  onQualityChange,
  onMaterialChange,
  materials = [],
  currentQuality = AssetQuality.HIGH,
  currentMaterial,
  showWireframe = false,
  showAnnotations = true,
  autoRotate = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showQualityMenu, setShowQualityMenu] = useState(false);
  const [showMaterialMenu, setShowMaterialMenu] = useState(false);

  const qualityOptions = [
    { value: AssetQuality.LOW, label: 'Low', description: 'Faster loading' },
    { value: AssetQuality.MEDIUM, label: 'Medium', description: 'Balanced' },
    { value: AssetQuality.HIGH, label: 'High', description: 'Best quality' },
    { value: AssetQuality.ULTRA, label: 'Ultra', description: 'Maximum detail' }
  ];

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
      {/* Main Controls */}
      <div className="p-3">
        <div className="flex items-center space-x-2">
          {/* Camera Reset */}
          <button
            onClick={onCameraReset}
            className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
            title="Reset Camera"
          >
            <RotateCcw className="w-4 h-4 text-gray-600" />
          </button>

          {/* Wireframe Toggle */}
          <button
            onClick={onToggleWireframe}
            className={`p-2 rounded-lg transition-colors ${
              showWireframe 
                ? 'bg-blue-100 text-blue-600' 
                : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
            }`}
            title="Toggle Wireframe"
          >
            <Grid3X3 className="w-4 h-4" />
          </button>

          {/* Annotations Toggle */}
          <button
            onClick={onToggleAnnotations}
            className={`p-2 rounded-lg transition-colors ${
              showAnnotations 
                ? 'bg-green-100 text-green-600' 
                : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
            }`}
            title="Toggle Annotations"
          >
            {showAnnotations ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          </button>

          {/* Auto Rotate Toggle */}
          <button
            onClick={onToggleAutoRotate}
            className={`p-2 rounded-lg transition-colors ${
              autoRotate 
                ? 'bg-purple-100 text-purple-600' 
                : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
            }`}
            title="Toggle Auto Rotate"
          >
            <RotateCw className="w-4 h-4" />
          </button>

          {/* Settings Toggle */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className={`p-2 rounded-lg transition-colors ${
              isExpanded 
                ? 'bg-gray-200 text-gray-700' 
                : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
            }`}
            title="More Settings"
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Expanded Controls */}
      {isExpanded && (
        <div className="border-t border-gray-200 p-3 space-y-3">
          {/* Quality Control */}
          <div className="relative">
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Quality
            </label>
            <button
              onClick={() => setShowQualityMenu(!showQualityMenu)}
              className="w-full flex items-center justify-between px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm hover:bg-gray-100"
            >
              <div className="flex items-center">
                <Layers className="w-4 h-4 text-gray-500 mr-2" />
                <span>{qualityOptions.find(q => q.value === currentQuality)?.label}</span>
              </div>
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {showQualityMenu && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                {qualityOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => {
                      onQualityChange?.(option.value);
                      setShowQualityMenu(false);
                    }}
                    className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg ${
                      currentQuality === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                    }`}
                  >
                    <div className="font-medium">{option.label}</div>
                    <div className="text-xs text-gray-500">{option.description}</div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Material Control */}
          {materials.length > 0 && (
            <div className="relative">
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Material
              </label>
              <button
                onClick={() => setShowMaterialMenu(!showMaterialMenu)}
                className="w-full flex items-center justify-between px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm hover:bg-gray-100"
              >
                <div className="flex items-center">
                  <Palette className="w-4 h-4 text-gray-500 mr-2" />
                  <span>
                    {currentMaterial 
                      ? materials.find(m => m.id === currentMaterial)?.name || 'Custom'
                      : 'Default'
                    }
                  </span>
                </div>
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {showMaterialMenu && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-48 overflow-y-auto">
                  <button
                    onClick={() => {
                      onMaterialChange?.('');
                      setShowMaterialMenu(false);
                    }}
                    className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-50 first:rounded-t-lg ${
                      !currentMaterial ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                    }`}
                  >
                    <div className="font-medium">Default</div>
                    <div className="text-xs text-gray-500">Original material</div>
                  </button>
                  {materials.map((material) => (
                    <button
                      key={material.id}
                      onClick={() => {
                        onMaterialChange?.(material.id);
                        setShowMaterialMenu(false);
                      }}
                      className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-50 last:rounded-b-lg ${
                        currentMaterial === material.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                      }`}
                    >
                      <div className="font-medium">{material.name}</div>
                      {material.description && (
                        <div className="text-xs text-gray-500 truncate">{material.description}</div>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Info Panel */}
          <div className="text-xs text-gray-500 space-y-1">
            <div className="flex justify-between">
              <span>Mouse:</span>
              <span>Rotate, Zoom, Pan</span>
            </div>
            <div className="flex justify-between">
              <span>Touch:</span>
              <span>Pinch to zoom</span>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close menus */}
      {(showQualityMenu || showMaterialMenu) && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => {
            setShowQualityMenu(false);
            setShowMaterialMenu(false);
          }}
        />
      )}
    </div>
  );
};

export default ViewerControls;
