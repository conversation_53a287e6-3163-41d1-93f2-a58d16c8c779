// Test Setup for AI Marketing System
// Global test configuration and utilities

import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

beforeAll(() => {
  // Suppress console output during tests unless explicitly needed
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  // Restore console methods
  console.log = originalConsoleLog;
  console.warn = originalConsoleWarn;
  console.error = originalConsoleError;
});

// Mock OpenAI API
jest.mock('openai', () => {
  return jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn().mockResolvedValue({
          choices: [{
            message: {
              content: JSON.stringify({
                patterns: [
                  {
                    pattern: 'email_timing_morning',
                    confidence: 0.85,
                    context: 'email-marketing'
                  }
                ],
                insights: [
                  {
                    insight: 'Morning emails show 23% higher open rates',
                    confidence: 0.8,
                    actionable: true
                  }
                ],
                strategies: [
                  {
                    name: 'Optimized Email Timing',
                    improvement: 15,
                    channels: ['email']
                  }
                ]
              })
            }
          }]
        })
      }
    }
  }));
});

// Mock external APIs
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn().mockResolvedValue({
      data: {
        elements: [],
        totalValue: 1000000,
        suppliers: [],
        countries: []
      }
    }),
    post: jest.fn().mockResolvedValue({
      data: {
        id: 'mock-id-123',
        status: 'success'
      }
    }),
    patch: jest.fn().mockResolvedValue({
      data: { success: true }
    }),
    interceptors: {
      response: {
        use: jest.fn()
      }
    }
  })),
  default: {
    get: jest.fn().mockResolvedValue({ data: {} }),
    post: jest.fn().mockResolvedValue({ data: {} })
  }
}));

// Mock Google Ads API
jest.mock('google-ads-api', () => ({
  GoogleAdsApi: jest.fn().mockImplementation(() => ({
    Customer: jest.fn().mockImplementation(() => ({
      campaigns: {
        create: jest.fn().mockResolvedValue({
          id: 'campaign-123',
          resource_name: 'customers/123/campaigns/456'
        }),
        update: jest.fn().mockResolvedValue({ success: true })
      },
      campaignBudgets: {
        create: jest.fn().mockResolvedValue({
          resource_name: 'customers/123/campaignBudgets/789'
        })
      },
      adGroups: {
        create: jest.fn().mockResolvedValue({
          id: 'adgroup-123',
          name: 'Test Ad Group'
        })
      },
      adGroupCriteria: {
        create: jest.fn().mockResolvedValue({
          resource_name: 'customers/123/adGroupCriteria/456'
        })
      },
      adGroupAds: {
        create: jest.fn().mockResolvedValue({
          resource_name: 'customers/123/adGroupAds/789'
        })
      },
      keywordPlanIdeas: {
        generateKeywordIdeas: jest.fn().mockResolvedValue({
          results: [
            {
              text: 'natural stone',
              keyword_idea_metrics: {
                avg_monthly_searches: 5000,
                competition: 'MEDIUM',
                high_top_of_page_bid_micros: 2500000
              }
            }
          ]
        })
      },
      report: jest.fn().mockResolvedValue([
        {
          metrics: {
            impressions: '1000',
            clicks: '50',
            cost_micros: '25000000',
            conversions: '5',
            ctr: '0.05'
          }
        }
      ]),
      customers: {
        list: jest.fn().mockResolvedValue([])
      }
    }))
  }))
}));

// Test utilities
export const TestUtils = {
  // Create mock marketing task
  createMockTask: (overrides: any = {}) => ({
    id: `test-task-${Date.now()}`,
    type: 'learning',
    priority: 'medium',
    aiModel: 'learning',
    data: {
      action: 'analyze_performance',
      performanceData: {
        email: { openRate: 25, clickRate: 3.5 }
      }
    },
    requiresApproval: false,
    createdAt: new Date(),
    ...overrides
  }),

  // Create mock database manager
  createMockDatabase: () => ({
    initialize: jest.fn().mockResolvedValue(undefined),
    query: jest.fn().mockResolvedValue({ rows: [] }),
    transaction: jest.fn().mockImplementation(async (callback) => {
      const mockClient = {
        query: jest.fn().mockResolvedValue({ rows: [] })
      };
      return await callback(mockClient);
    }),
    getLearningPatterns: jest.fn().mockResolvedValue([]),
    getMarketInsights: jest.fn().mockResolvedValue([]),
    saveLearningPattern: jest.fn().mockResolvedValue('pattern-123'),
    saveMarketInsight: jest.fn().mockResolvedValue('insight-123'),
    savePerformanceMetric: jest.fn().mockResolvedValue('metric-123'),
    getPerformanceMetrics: jest.fn().mockResolvedValue([]),
    setCache: jest.fn().mockResolvedValue(undefined),
    getCache: jest.fn().mockResolvedValue(null),
    logSystemEvent: jest.fn().mockResolvedValue(undefined),
    getStatistics: jest.fn().mockResolvedValue({
      learningPatterns: 10,
      marketInsights: 5,
      knowledgeEntries: 20,
      performanceMetrics24h: 100,
      systemEvents24h: 50
    }),
    cleanup: jest.fn().mockResolvedValue(undefined)
  }),

  // Create mock API integration manager
  createMockAPIManager: () => ({
    initialize: jest.fn().mockResolvedValue(undefined),
    performHealthCheck: jest.fn().mockResolvedValue({
      tradeMap: true,
      linkedIn: true,
      googleAds: true,
      overall: true,
      lastChecked: new Date()
    }),
    performMarketResearch: jest.fn().mockResolvedValue({
      tradeData: [],
      linkedInInsights: [],
      keywordData: [],
      marketOpportunities: []
    }),
    createIntegratedCampaign: jest.fn().mockResolvedValue({
      campaignId: 'integrated-123',
      linkedInCampaign: { id: 'linkedin-123' },
      googleAdsCampaign: { id: 'google-123' },
      marketInsights: {}
    }),
    getIntegrationMetrics: jest.fn().mockResolvedValue({
      tradeMap: {
        requestsToday: 10,
        dataPointsCollected: 100,
        lastSync: new Date(),
        errorRate: 0.01
      },
      linkedIn: {
        campaignsActive: 2,
        leadsGenerated: 15,
        lastCampaignUpdate: new Date(),
        apiCallsRemaining: 500
      },
      googleAds: {
        campaignsRunning: 3,
        totalSpend: 1500,
        conversions: 25,
        lastOptimization: new Date()
      }
    }),
    cleanup: jest.fn().mockResolvedValue(undefined)
  }),

  // Wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Generate random test data
  generateRandomMetrics: () => ({
    openRate: Math.random() * 40 + 10,
    clickRate: Math.random() * 8 + 1,
    conversionRate: Math.random() * 5 + 0.5,
    engagementRate: Math.random() * 6 + 1
  }),

  // Clean test data
  cleanTestData: async (database: any) => {
    if (database && database.query) {
      try {
        await database.query('DELETE FROM learning_patterns WHERE context LIKE $1', ['%test%']);
        await database.query('DELETE FROM market_insights WHERE source LIKE $1', ['%test%']);
        await database.query('DELETE FROM performance_metrics WHERE module_name LIKE $1', ['%test%']);
      } catch (error) {
        // Ignore cleanup errors in tests
      }
    }
  }
};

// Global test environment setup
global.TestUtils = TestUtils;

// Extend Jest matchers
expect.extend({
  toBeValidTaskResult(received) {
    const pass = received &&
      typeof received.taskId === 'string' &&
      typeof received.success === 'boolean' &&
      typeof received.executionTime === 'number' &&
      typeof received.aiModel === 'string' &&
      received.timestamp instanceof Date;

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid task result`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid task result`,
        pass: false
      };
    }
  },

  toBeValidLearningPattern(received) {
    const pass = received &&
      typeof received.id === 'string' &&
      typeof received.pattern === 'string' &&
      typeof received.confidence === 'number' &&
      received.confidence >= 0 && received.confidence <= 1 &&
      typeof received.context === 'string';

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid learning pattern`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid learning pattern`,
        pass: false
      };
    }
  }
});

// Type declarations for custom matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidTaskResult(): R;
      toBeValidLearningPattern(): R;
    }
  }
  
  var TestUtils: any;
}
