"use client"

import * as React from "react"

type Theme = "light" | "dark" | "system"

interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
  enableSystem?: boolean
}

interface ThemeProviderState {
  theme: Theme
  setTheme: (theme: Theme) => void
  systemTheme: "light" | "dark"
  resolvedTheme: "light" | "dark"
}

const ThemeProviderContext = React.createContext<ThemeProviderState | undefined>(
  undefined
)

/**
 * ThemeProvider component following RFC-004 UI/UX Design System
 * Provides theme context with system preference detection and persistence
 */
export function ThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "ui-theme",
  enableSystem = true,
  ...props
}: ThemeProviderProps) {
  const [theme, setThemeState] = React.useState<Theme>(defaultTheme)
  const [systemTheme, setSystemTheme] = React.useState<"light" | "dark">("light")
  const [mounted, setMounted] = React.useState(false)

  // Detect system theme preference
  React.useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
    setSystemTheme(mediaQuery.matches ? "dark" : "light")

    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? "dark" : "light")
    }

    mediaQuery.addEventListener("change", handleChange)
    return () => mediaQuery.removeEventListener("change", handleChange)
  }, [])

  // Load theme from localStorage on mount
  React.useEffect(() => {
    try {
      const storedTheme = localStorage.getItem(storageKey) as Theme
      if (storedTheme) {
        setThemeState(storedTheme)
      }
    } catch (error) {
      console.warn("Failed to load theme from localStorage:", error)
    }
    setMounted(true)
  }, [storageKey])

  // Apply theme to document
  React.useEffect(() => {
    if (!mounted) return

    const resolvedTheme = theme === "system" ? systemTheme : theme
    const root = document.documentElement

    // Remove existing theme classes
    root.classList.remove("light", "dark")
    
    // Set data-theme attribute for CSS custom properties
    root.setAttribute("data-theme", resolvedTheme)
    
    // Add theme class for compatibility
    root.classList.add(resolvedTheme)

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
      metaThemeColor.setAttribute(
        "content",
        resolvedTheme === "dark" ? "#111827" : "#ffffff"
      )
    }
  }, [theme, systemTheme, mounted])

  const setTheme = React.useCallback(
    (newTheme: Theme) => {
      try {
        localStorage.setItem(storageKey, newTheme)
      } catch (error) {
        console.warn("Failed to save theme to localStorage:", error)
      }
      setThemeState(newTheme)
    },
    [storageKey]
  )

  const resolvedTheme = theme === "system" ? systemTheme : theme

  const value = React.useMemo(
    () => ({
      theme,
      setTheme,
      systemTheme,
      resolvedTheme,
    }),
    [theme, setTheme, systemTheme, resolvedTheme]
  )

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return <div style={{ visibility: "hidden" }}>{children}</div>
  }

  return (
    <ThemeProviderContext.Provider value={value} {...props}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

/**
 * Hook to use theme context
 */
export const useTheme = () => {
  const context = React.useContext(ThemeProviderContext)

  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }

  return context
}
