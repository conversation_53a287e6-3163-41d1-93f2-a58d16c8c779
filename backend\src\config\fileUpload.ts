import { FileUploadConfig } from '../services/FileUploadService';

/**
 * File upload configuration factory
 */
export function createFileUploadConfig(): FileUploadConfig {
  const useS3 = process.env.AWS_S3_ENABLED === 'true' && 
                process.env.AWS_ACCESS_KEY_ID && 
                process.env.AWS_SECRET_ACCESS_KEY;

  const config: FileUploadConfig = {
    useS3: !!useS3,
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB default
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,pdf,doc,docx').split(','),
  };

  if (useS3) {
    // S3 config temporarily disabled
    // config.s3Config = {
    //   accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    //   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
    //   region: process.env.AWS_REGION || 'us-east-1',
    //   bucket: process.env.AWS_S3_BUCKET!,
    // };
  } else {
    config.localConfig = {
      uploadPath: process.env.UPLOAD_PATH || 'uploads',
    };
  }

  return config;
}

/**
 * Specific configurations for different file types
 */
export const fileUploadConfigs = {
  // Product images
  productImages: (): FileUploadConfig => ({
    ...createFileUploadConfig(),
    allowedTypes: ['jpg', 'jpeg', 'png', 'webp'],
    maxFileSize: 5 * 1024 * 1024, // 5MB
  }),

  // 3D assets
  assets3D: (): FileUploadConfig => ({
    ...createFileUploadConfig(),
    allowedTypes: ['glb', 'gltf', 'fbx', 'obj', 'jpg', 'jpeg', 'png', 'webp', 'hdr', 'exr'],
    maxFileSize: 100 * 1024 * 1024, // 100MB
  }),

  // Documents
  documents: (): FileUploadConfig => ({
    ...createFileUploadConfig(),
    allowedTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
    maxFileSize: 10 * 1024 * 1024, // 10MB
  }),

  // Analysis reports
  analysisReports: (): FileUploadConfig => ({
    ...createFileUploadConfig(),
    allowedTypes: ['pdf', 'jpg', 'jpeg', 'png'],
    maxFileSize: 20 * 1024 * 1024, // 20MB
  }),

  // User avatars
  avatars: (): FileUploadConfig => ({
    ...createFileUploadConfig(),
    allowedTypes: ['jpg', 'jpeg', 'png'],
    maxFileSize: 2 * 1024 * 1024, // 2MB
  }),

  // Company logos
  companyLogos: (): FileUploadConfig => ({
    ...createFileUploadConfig(),
    allowedTypes: ['jpg', 'jpeg', 'png', 'svg'],
    maxFileSize: 1 * 1024 * 1024, // 1MB
  }),
};

/**
 * Image processing presets
 */
export const imageProcessingPresets = {
  productImage: {
    resize: { width: 1200, height: 1200, fit: 'inside' as const },
    quality: 85,
    format: 'webp' as const,
    generateThumbnail: true,
    thumbnailSize: { width: 300, height: 300 },
  },

  productThumbnail: {
    resize: { width: 400, height: 400, fit: 'cover' as const },
    quality: 80,
    format: 'webp' as const,
  },

  avatar: {
    resize: { width: 200, height: 200, fit: 'cover' as const },
    quality: 85,
    format: 'webp' as const,
  },

  companyLogo: {
    resize: { width: 300, height: 300, fit: 'inside' as const },
    quality: 90,
    format: 'png' as const,
  },

  analysisReport: {
    quality: 90,
    format: 'jpeg' as const,
    generateThumbnail: true,
    thumbnailSize: { width: 200, height: 280 },
  },
};

/**
 * File validation helpers
 */
export const fileValidation = {
  isImage: (mimetype: string): boolean => {
    return mimetype.startsWith('image/');
  },

  isDocument: (mimetype: string): boolean => {
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];
    return documentTypes.includes(mimetype);
  },

  is3DAsset: (mimetype: string, filename: string): boolean => {
    const ext = filename.toLowerCase().split('.').pop();
    const asset3DTypes = ['glb', 'gltf', 'fbx', 'obj'];
    return asset3DTypes.includes(ext || '');
  },

  getFileCategory: (mimetype: string, filename: string): string => {
    if (fileValidation.isImage(mimetype)) return 'image';
    if (fileValidation.isDocument(mimetype)) return 'document';
    if (fileValidation.is3DAsset(mimetype, filename)) return '3d-asset';
    return 'other';
  },
};

/**
 * Storage path generators
 */
export const storagePaths = {
  productImages: (productId: string) => `products/${productId}/images`,
  productDocuments: (productId: string) => `products/${productId}/documents`,
  assets3D: (productId: string) => `products/${productId}/3d-assets`,
  analysisReports: (reportId: string) => `analysis-reports/${reportId}`,
  userAvatars: (userId: string) => `users/${userId}/avatar`,
  companyLogos: (companyId: string) => `companies/${companyId}/logo`,
  sampleImages: (sampleId: string) => `samples/${sampleId}/images`,
  orderDocuments: (orderId: string) => `orders/${orderId}/documents`,
  temp: () => `temp/${Date.now()}`,
};
