import type { <PERSON><PERSON><PERSON> } from "next";
import { Inter, Playfair_Display, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/ui/theme-provider";
import { AuthProvider } from "@/contexts/auth-context"
import { ProducerAuthProvider } from "@/contexts/producer-auth-context";
import { QuoteProvider } from "@/contexts/quote-context";
import { ProductsProvider } from "@/contexts/products-context";
import { StockProvider } from "@/contexts/stock-context";
import { SettingsProvider } from "@/contexts/settings-context";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { Toaster } from "react-hot-toast";
import { ConditionalWhatsApp } from "@/components/ui/conditional-whatsapp";
import WhatsAppWidget from "@/components/whatsapp/WhatsAppWidget";


// RFC-004 UI/UX Design System Fonts
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const playfairDisplay = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-jetbrains",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Türkiye Doğal Taş Pazarı | Natural Stone Marketplace",
  description: "Türkiye'nin en büyük doğal taş B2B pazarı. Rekabetçi teklif sistemi ile mermer, granit, traverten ve oniks ürünlerine ulaşın. 3D sanal showroom ile ürünleri keşfedin.",
  keywords: "doğal taş, mermer, granit, traverten, oniks, B2B, marketplace, Türkiye, 3D showroom, sanal showroom, doğal taş ihracat, doğal taş ithalat",
  authors: [{ name: "Türkiye Doğal Taş Pazarı" }],
  creator: "Türkiye Doğal Taş Pazarı",
  publisher: "Türkiye Doğal Taş Pazarı",
  robots: "index, follow",
  alternates: {
    canonical: "https://dogaltaspazari.com",
    languages: {
      'tr': 'https://dogaltaspazari.com',
      'en': 'https://dogaltaspazari.com/en',
    },
  },
  openGraph: {
    type: "website",
    locale: "tr_TR",
    url: "https://dogaltaspazari.com",
    title: "Türkiye Doğal Taş Pazarı | Natural Stone Marketplace",
    description: "Türkiye'nin en büyük doğal taş B2B pazarı. 3D sanal showroom ile ürünleri keşfedin.",
    siteName: "Türkiye Doğal Taş Pazarı",
    images: [
      {
        url: "https://dogaltaspazari.com/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Türkiye Doğal Taş Pazarı",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Türkiye Doğal Taş Pazarı",
    description: "Türkiye'nin en büyük doğal taş B2B pazarı",
  },
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#8B7355" },
    { media: "(prefers-color-scheme: dark)", color: "#A68B5B" },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="tr"
      className={`${inter.variable} ${playfairDisplay.variable} ${jetbrainsMono.variable}`}
      suppressHydrationWarning
    >
      <head>
        {/* Preload critical fonts */}
        <link
          rel="preload"
          href="/_next/static/media/inter-latin.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />

        {/* Theme color meta tags */}
        <meta name="theme-color" content="#8B7355" />
        <meta name="msapplication-TileColor" content="#8B7355" />

        {/* Accessibility */}
        <meta name="color-scheme" content="light dark" />
      </head>
      <body className="font-primary antialiased">
        <ThemeProvider
          defaultTheme="system"
          enableSystem
          storageKey="stone-marketplace-theme"
        >
          <SettingsProvider>
              <AuthProvider>
                <ProducerAuthProvider>
                  <NotificationProvider>
                    <ProductsProvider>
                      <StockProvider>
                        <QuoteProvider>
                    {children}
                    <Toaster
                      position="top-right"
                      toastOptions={{
                        duration: 4000,
                        style: {
                          background: '#363636',
                          color: '#fff',
                        },
                        success: {
                          duration: 3000,
                          iconTheme: {
                            primary: '#10B981',
                            secondary: '#fff',
                          },
                        },
                        error: {
                          duration: 5000,
                          iconTheme: {
                            primary: '#EF4444',
                            secondary: '#fff',
                          },
                        },
                      }}
                    />

                        </QuoteProvider>
                      </StockProvider>
                    </ProductsProvider>
                  </NotificationProvider>
                </ProducerAuthProvider>
              </AuthProvider>

          {/* WhatsApp Widget */}
          <WhatsAppWidget
            position="bottom-right"
            hideOnPages={['/3d-showroom', '/dashboard', '/customer', '/producer', '/admin']}
          />
        </SettingsProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
