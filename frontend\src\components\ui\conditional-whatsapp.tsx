'use client';

import { usePathname } from 'next/navigation';
import { WhatsAppWidget } from '@/components/ui/whatsapp-widget';
import { useSettings } from '@/contexts/settings-context';

// Conditional WhatsApp Widget Component
export function ConditionalWhatsApp() {
  const pathname = usePathname();
  const { settings } = useSettings();
  
  // Don't show WhatsApp widget on these pages
  const excludedPaths = [
    '/3d-showroom',
    '/customer',  // Tüm customer dashboard sayfaları
    '/producer',  // Tüm producer sayfaları
    '/admin'  // Tüm admin sayfaları
  ];
  
  // Check if current path should exclude WhatsApp widget
  const shouldExclude = excludedPaths.some(path => 
    pathname === path || pathname.startsWith(path + '/')
  );
  
  if (shouldExclude || !settings.whatsapp.isEnabled) {
    return null;
  }

  return (
    <WhatsAppWidget
      phoneNumber={settings.whatsapp.phoneNumber}
      message={settings.whatsapp.defaultMessage}
      position={settings.whatsapp.position}
    />
  );
}
