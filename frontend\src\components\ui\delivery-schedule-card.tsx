'use client'

import * as React from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Truck, 
  Calendar, 
  MapPin, 
  Package,
  CheckCircle,
  Clock,
  AlertTriangle,
  Edit,
  Save,
  X
} from 'lucide-react'
import { DeliveryPackage } from '@/types/multi-delivery'

interface DeliveryScheduleCardProps {
  deliveryPackage: DeliveryPackage
  onScheduleDelivery: (packageId: string, date: string) => void
  onUpdateDelivery: (packageId: string, updates: any) => void
  onCompleteDelivery: (packageId: string) => void
}

export function DeliveryScheduleCard({
  deliveryPackage,
  onScheduleDelivery,
  onUpdateDelivery,
  onCompleteDelivery
}: DeliveryScheduleCardProps) {
  const [isEditing, setIsEditing] = React.useState(false)
  const [editData, setEditData] = React.useState({
    deliveryDate: deliveryPackage.deliveryDate || '',
    trackingNumber: deliveryPackage.deliverySchedule?.trackingNumber || '',
    carrierCompany: deliveryPackage.deliverySchedule?.carrierCompany || '',
    notes: deliveryPackage.deliveryNotes || ''
  })

  const getDeliveryStatusColor = (status: DeliveryPackage['deliveryStatus']) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'shipped':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'ready':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getDeliveryStatusText = (status: DeliveryPackage['deliveryStatus']) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'ready':
        return 'Hazır'
      case 'shipped':
        return 'Kargoda'
      case 'delivered':
        return 'Teslim Edildi'
      default:
        return status
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  const handleSaveEdit = () => {
    onUpdateDelivery(deliveryPackage.id, editData)
    setIsEditing(false)
  }

  const handleCancelEdit = () => {
    setEditData({
      deliveryDate: deliveryPackage.deliveryDate || '',
      trackingNumber: deliveryPackage.deliverySchedule?.trackingNumber || '',
      carrierCompany: deliveryPackage.deliverySchedule?.carrierCompany || '',
      notes: deliveryPackage.deliveryNotes || ''
    })
    setIsEditing(false)
  }

  const canScheduleDelivery = deliveryPackage.productionStatus === 'completed' && 
                             deliveryPackage.deliveryStatus === 'pending'
  
  const canEditDelivery = deliveryPackage.deliveryStatus === 'ready' || 
                         deliveryPackage.deliveryStatus === 'shipped'
  
  const canCompleteDelivery = deliveryPackage.deliveryStatus === 'shipped'

  return (
    <Card className={`overflow-hidden ${getDeliveryStatusColor(deliveryPackage.deliveryStatus)}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Package className="w-5 h-5" />
            Paket #{deliveryPackage.packageNumber} - Teslimat
          </CardTitle>
          <Badge className={getDeliveryStatusColor(deliveryPackage.deliveryStatus)}>
            {getDeliveryStatusText(deliveryPackage.deliveryStatus)}
          </Badge>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <span className="font-medium">Miktar:</span> {deliveryPackage.quantity} m²
          </div>
          <div>
            <span className="font-medium">Üretim Durumu:</span> 
            <Badge variant="outline" className="ml-2">
              {deliveryPackage.productionStatus === 'completed' ? 'Tamamlandı' : 'Devam Ediyor'}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Production Not Ready Warning */}
        {deliveryPackage.productionStatus !== 'completed' && (
          <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium text-orange-800">Üretim Devam Ediyor</p>
                <p className="text-sm text-orange-700 mt-1">
                  Bu paket henüz üretim aşamasında. Teslimat planlaması üretim tamamlandıktan sonra yapılabilir.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Delivery Information */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900 flex items-center gap-2">
            <Truck className="w-4 h-4" />
            Teslimat Bilgileri
          </h4>
          
          {isEditing ? (
            <div className="space-y-3 bg-gray-50 p-3 rounded-lg">
              <div>
                <Label htmlFor="deliveryDate" className="text-sm font-medium">
                  Teslimat Tarihi
                </Label>
                <Input
                  id="deliveryDate"
                  type="date"
                  value={editData.deliveryDate}
                  onChange={(e) => setEditData(prev => ({ ...prev, deliveryDate: e.target.value }))}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="carrierCompany" className="text-sm font-medium">
                  Kargo Şirketi
                </Label>
                <Input
                  id="carrierCompany"
                  value={editData.carrierCompany}
                  onChange={(e) => setEditData(prev => ({ ...prev, carrierCompany: e.target.value }))}
                  placeholder="Kargo şirketi adı"
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="trackingNumber" className="text-sm font-medium">
                  Takip Numarası
                </Label>
                <Input
                  id="trackingNumber"
                  value={editData.trackingNumber}
                  onChange={(e) => setEditData(prev => ({ ...prev, trackingNumber: e.target.value }))}
                  placeholder="Kargo takip numarası"
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="notes" className="text-sm font-medium">
                  Teslimat Notları
                </Label>
                <Input
                  id="notes"
                  value={editData.notes}
                  onChange={(e) => setEditData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Teslimat ile ilgili notlar"
                  className="mt-1"
                />
              </div>
              
              <div className="flex gap-2">
                <Button size="sm" onClick={handleSaveEdit} className="bg-green-600 hover:bg-green-700">
                  <Save className="w-4 h-4 mr-1" />
                  Kaydet
                </Button>
                <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                  <X className="w-4 h-4 mr-1" />
                  İptal
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="w-4 h-4 text-gray-500" />
                <span className="font-medium">Planlanan Tarih:</span>
                <span>{formatDate(deliveryPackage.deliveryDate)}</span>
              </div>
              
              {deliveryPackage.actualDeliveryDate && (
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="font-medium">Gerçek Tarih:</span>
                  <span>{formatDate(deliveryPackage.actualDeliveryDate)}</span>
                </div>
              )}
              
              {deliveryPackage.deliverySchedule?.carrierCompany && (
                <div className="flex items-center gap-2 text-sm">
                  <Truck className="w-4 h-4 text-gray-500" />
                  <span className="font-medium">Kargo:</span>
                  <span>{deliveryPackage.deliverySchedule.carrierCompany}</span>
                </div>
              )}
              
              {deliveryPackage.deliverySchedule?.trackingNumber && (
                <div className="flex items-center gap-2 text-sm">
                  <Package className="w-4 h-4 text-gray-500" />
                  <span className="font-medium">Takip No:</span>
                  <span className="font-mono">{deliveryPackage.deliverySchedule.trackingNumber}</span>
                </div>
              )}
              
              {deliveryPackage.deliveryNotes && (
                <div className="text-sm">
                  <span className="font-medium">Notlar:</span>
                  <p className="text-gray-600 mt-1">{deliveryPackage.deliveryNotes}</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2 border-t">
          {canScheduleDelivery && (
            <Button
              size="sm"
              onClick={() => {
                const date = prompt('Teslimat tarihi (YYYY-MM-DD):')
                if (date) onScheduleDelivery(deliveryPackage.id, date)
              }}
              className="bg-purple-600 hover:bg-purple-700"
            >
              <Calendar className="w-4 h-4 mr-1" />
              Teslimat Planla
            </Button>
          )}
          
          {canEditDelivery && !isEditing && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsEditing(true)}
            >
              <Edit className="w-4 h-4 mr-1" />
              Düzenle
            </Button>
          )}
          
          {canCompleteDelivery && (
            <Button
              size="sm"
              onClick={() => onCompleteDelivery(deliveryPackage.id)}
              className="bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="w-4 h-4 mr-1" />
              Teslim Edildi
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
