import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

/**
 * Button component following RFC-004 UI/UX Design System
 * Natural Stone Marketplace themed button with accessibility support
 */
const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap font-medium transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed",
  {
    variants: {
      variant: {
        // Primary - Natural Stone Theme
        primary: [
          "bg-[var(--primary-stone)] text-white border-none",
          "hover:bg-[var(--primary-dark)] hover:transform hover:-translate-y-0.5 hover:shadow-md",
          "focus-visible:ring-[var(--primary-stone)] focus-visible:ring-opacity-30",
          "active:transform active:translate-y-0"
        ].join(" "),

        // Secondary - Light background
        secondary: [
          "bg-[var(--gray-100)] text-[var(--gray-700)] border border-[var(--gray-300)]",
          "hover:bg-[var(--gray-200)] hover:transform hover:-translate-y-0.5 hover:shadow-md",
          "focus-visible:ring-[var(--gray-400)] focus-visible:ring-opacity-30"
        ].join(" "),

        // Outline - Bordered style
        outline: [
          "bg-transparent text-[var(--primary-stone)] border-2 border-[var(--primary-stone)]",
          "hover:bg-[var(--primary-stone)] hover:text-white hover:transform hover:-translate-y-0.5 hover:shadow-md",
          "focus-visible:ring-[var(--primary-stone)] focus-visible:ring-opacity-30"
        ].join(" "),

        // Ghost - Minimal style
        ghost: [
          "bg-transparent text-[var(--gray-700)] border-none",
          "hover:bg-[var(--gray-100)] hover:text-[var(--primary-stone)]",
          "focus-visible:ring-[var(--gray-400)] focus-visible:ring-opacity-30"
        ].join(" "),

        // Link - Text only
        link: [
          "bg-transparent text-[var(--primary-stone)] border-none underline-offset-4",
          "hover:underline hover:text-[var(--primary-dark)]",
          "focus-visible:ring-[var(--primary-stone)] focus-visible:ring-opacity-30"
        ].join(" "),

        // Destructive - Error actions
        destructive: [
          "bg-[var(--error)] text-white border-none",
          "hover:bg-red-600 hover:transform hover:-translate-y-0.5 hover:shadow-md",
          "focus-visible:ring-[var(--error)] focus-visible:ring-opacity-30"
        ].join(" "),
      },
      size: {
        sm: "h-9 px-3 text-sm rounded-[var(--radius-md)]",
        md: "h-10 px-4 py-2 text-base rounded-[var(--radius-md)]",
        lg: "h-11 px-8 text-lg rounded-[var(--radius-md)]",
        xl: "h-12 px-10 text-xl rounded-[var(--radius-lg)]",
        icon: "h-10 w-10 rounded-[var(--radius-md)]",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
