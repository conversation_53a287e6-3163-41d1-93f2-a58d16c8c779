'use client'

import React from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  X,
  TrendingUp,
  Users,
  Eye,
  ShoppingCart,
  DollarSign,
  Calendar,
  Building2,
  Target,
  BarChart3,
  Star,
  Clock
} from 'lucide-react'

interface ProductStatsModalProps {
  isOpen: boolean
  onClose: () => void
  product: any
}

export function ProductStatsModal({ isOpen, onClose, product }: ProductStatsModalProps) {
  const [selectedTab, setSelectedTab] = React.useState('performance')

  if (!isOpen) return null

  // Mock istatistik verileri - gerçek uygulamada API'den gelecek
  const mockStats = {
    performance: {
      views: { daily: 45, weekly: 280, monthly: 1200 },
      quotes: { daily: 8, weekly: 52, monthly: 220 },
      orders: { daily: 2, weekly: 15, monthly: 68 },
      conversionRate: { viewToQuote: 18.3, quoteToOrder: 30.9 }
    },
    customers: [
      {
        id: '1',
        name: '<PERSON>n<PERSON><PERSON><PERSON> A<PERSON>',
        sector: 'İnşaat',
        quotesRequested: 12,
        ordersPlaced: 4,
        totalValue: 850000,
        lastActivity: '2 gün önce',
        avgOrderSize: 212500
      },
      {
        id: '2',
        name: 'Dekorasyon Ltd.',
        sector: 'Dekorasyon',
        quotesRequested: 8,
        ordersPlaced: 3,
        totalValue: 420000,
        lastActivity: '1 hafta önce',
        avgOrderSize: 140000
      },
      {
        id: '3',
        name: 'Villa Projeleri A.Ş.',
        sector: 'Konut',
        quotesRequested: 15,
        ordersPlaced: 6,
        totalValue: 1200000,
        lastActivity: '3 gün önce',
        avgOrderSize: 200000
      }
    ],
    producers: [
      {
        id: '1',
        name: 'Afyon Doğal Taş A.Ş.',
        quotesGiven: 45,
        quotesWon: 18,
        winRate: 40,
        avgResponseTime: '2 saat',
        totalRevenue: 2400000,
        avgPrice: 120
      },
      {
        id: '2',
        name: 'Premium Stone Co.',
        quotesGiven: 32,
        quotesWon: 12,
        winRate: 37.5,
        avgResponseTime: '4 saat',
        totalRevenue: 1800000,
        avgPrice: 125
      }
    ],
    priceHistory: [
      { dimension: '60x120x2cm', prices: [115, 118, 120, 122, 120, 118], months: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'] },
      { dimension: '80x160x3cm', prices: [125, 128, 130, 135, 132, 130], months: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'] },
      { dimension: '40x80x2cm', prices: [110, 112, 115, 118, 115, 113], months: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'] }
    ],
    salesHistory: [
      { month: 'Ocak', meterage: 450, revenue: 54000 },
      { month: 'Şubat', meterage: 380, revenue: 45600 },
      { month: 'Mart', meterage: 520, revenue: 62400 },
      { month: 'Nisan', meterage: 680, revenue: 81600 },
      { month: 'Mayıs', meterage: 590, revenue: 70800 },
      { month: 'Haziran', meterage: 720, revenue: 86400 }
    ]
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-2xl font-bold text-gray-900">{product.name} - İstatistikler</h3>
              <p className="text-gray-600">Detaylı performans analizi ve raporlar</p>
            </div>
            <Button variant="outline" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Eye className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Aylık Görüntüleme</p>
                  <p className="text-xl font-bold text-gray-900">{mockStats.performance.views.monthly}</p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Target className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Aylık Teklif</p>
                  <p className="text-xl font-bold text-gray-900">{mockStats.performance.quotes.monthly}</p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-amber-100 rounded-lg">
                  <ShoppingCart className="w-5 h-5 text-amber-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Aylık Sipariş</p>
                  <p className="text-xl font-bold text-gray-900">{mockStats.performance.orders.monthly}</p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <TrendingUp className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Dönüşüm Oranı</p>
                  <p className="text-xl font-bold text-gray-900">{mockStats.performance.conversionRate.quoteToOrder}%</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'performance', label: 'Performans', icon: BarChart3 },
                { id: 'customers', label: 'Müşteriler', icon: Users },
                { id: 'producers', label: 'Üreticiler', icon: Building2 },
                { id: 'pricing', label: 'Fiyat Geçmişi', icon: DollarSign }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setSelectedTab(tab.id)}
                    className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                      selectedTab === tab.id
                        ? 'border-amber-500 text-amber-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="space-y-6">
            {selectedTab === 'performance' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="p-6">
                  <h4 className="text-lg font-semibold mb-4">Görüntüleme Trendi</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Günlük Ortalama</span>
                      <span className="font-medium">{mockStats.performance.views.daily}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Haftalık Toplam</span>
                      <span className="font-medium">{mockStats.performance.views.weekly}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Aylık Toplam</span>
                      <span className="font-medium">{mockStats.performance.views.monthly}</span>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <h4 className="text-lg font-semibold mb-4">Dönüşüm Oranları</h4>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-600">Görüntüleme → Teklif</span>
                        <span className="font-medium">{mockStats.performance.conversionRate.viewToQuote}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${mockStats.performance.conversionRate.viewToQuote}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-600">Teklif → Sipariş</span>
                        <span className="font-medium">{mockStats.performance.conversionRate.quoteToOrder}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ width: `${mockStats.performance.conversionRate.quoteToOrder}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card className="p-6 lg:col-span-2">
                  <h4 className="text-lg font-semibold mb-4">Aylık Satış Performansı</h4>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2">Ay</th>
                          <th className="text-left py-2">Metraj (m²)</th>
                          <th className="text-left py-2">Gelir ($)</th>
                          <th className="text-left py-2">Ort. Fiyat</th>
                        </tr>
                      </thead>
                      <tbody>
                        {mockStats.salesHistory.map((month, index) => (
                          <tr key={index} className="border-b">
                            <td className="py-2">{month.month}</td>
                            <td className="py-2">{month.meterage}</td>
                            <td className="py-2">${month.revenue.toLocaleString()}</td>
                            <td className="py-2">${(month.revenue / month.meterage).toFixed(0)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </Card>
              </div>
            )}

            {selectedTab === 'customers' && (
              <div className="space-y-4">
                <h4 className="text-lg font-semibold">Müşteri Analizi ({mockStats.customers.length})</h4>
                
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">Müşteri</th>
                        <th className="text-left py-3 px-4">Sektör</th>
                        <th className="text-left py-3 px-4">Teklif Talebi</th>
                        <th className="text-left py-3 px-4">Sipariş</th>
                        <th className="text-left py-3 px-4">Toplam Değer</th>
                        <th className="text-left py-3 px-4">Ort. Sipariş</th>
                        <th className="text-left py-3 px-4">Son Aktivite</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockStats.customers.map((customer) => (
                        <tr key={customer.id} className="border-b">
                          <td className="py-3 px-4">
                            <button className="text-blue-600 hover:underline font-medium">
                              {customer.name}
                            </button>
                          </td>
                          <td className="py-3 px-4">
                            <Badge variant="outline">{customer.sector}</Badge>
                          </td>
                          <td className="py-3 px-4">{customer.quotesRequested}</td>
                          <td className="py-3 px-4">{customer.ordersPlaced}</td>
                          <td className="py-3 px-4">₺{customer.totalValue.toLocaleString()}</td>
                          <td className="py-3 px-4">₺{customer.avgOrderSize.toLocaleString()}</td>
                          <td className="py-3 px-4 text-gray-600">{customer.lastActivity}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {selectedTab === 'producers' && (
              <div className="space-y-4">
                <h4 className="text-lg font-semibold">Üretici Performansı ({mockStats.producers.length})</h4>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {mockStats.producers.map((producer) => (
                    <Card key={producer.id} className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <button className="text-lg font-semibold text-blue-600 hover:underline">
                          {producer.name}
                        </button>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {producer.winRate}% Kazanma
                        </Badge>
                      </div>
                      
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Verilen Teklif</span>
                          <span className="font-medium">{producer.quotesGiven}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Kazanılan Teklif</span>
                          <span className="font-medium">{producer.quotesWon}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Yanıt Süresi</span>
                          <span className="font-medium">{producer.avgResponseTime}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Toplam Gelir</span>
                          <span className="font-medium">₺{producer.totalRevenue.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Ort. Fiyat</span>
                          <span className="font-medium">${producer.avgPrice}/m²</span>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {selectedTab === 'pricing' && (
              <div className="space-y-6">
                <h4 className="text-lg font-semibold">Ebat Bazında Fiyat Geçmişi</h4>
                
                {mockStats.priceHistory.map((item, index) => (
                  <Card key={index} className="p-6">
                    <h5 className="font-semibold mb-4">{item.dimension}</h5>
                    <div className="grid grid-cols-6 gap-4">
                      {item.months.map((month, monthIndex) => (
                        <div key={monthIndex} className="text-center">
                          <p className="text-sm text-gray-600">{month}</p>
                          <p className="text-lg font-semibold">${item.prices[monthIndex]}</p>
                          {monthIndex > 0 && (
                            <p className={`text-xs ${
                              item.prices[monthIndex] > item.prices[monthIndex - 1] 
                                ? 'text-green-600' 
                                : item.prices[monthIndex] < item.prices[monthIndex - 1]
                                ? 'text-red-600'
                                : 'text-gray-500'
                            }`}>
                              {item.prices[monthIndex] > item.prices[monthIndex - 1] && '+'}
                              {item.prices[monthIndex] - item.prices[monthIndex - 1]}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
