import { SettingsModule } from '../core/SettingsModule';
import { SettingsCategory } from '../types/settings.types';

export class NotificationSettingsModule extends SettingsModule {
  protected category: SettingsCategory = 'NOTIFICATIONS';

  protected getDefaultSettings() {
    return {
      emailNotificationsEnabled: {
        key: 'email_notifications_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable email notifications'
      },
      smsNotificationsEnabled: {
        key: 'sms_notifications_enabled',
        value: false,
        type: 'boolean',
        category: this.category,
        description: 'Enable SMS notifications'
      },
      pushNotificationsEnabled: {
        key: 'push_notifications_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable push notifications'
      },
      whatsappNotificationsEnabled: {
        key: 'whatsapp_notifications_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable WhatsApp notifications'
      },
      telegramNotificationsEnabled: {
        key: 'telegram_notifications_enabled',
        value: false,
        type: 'boolean',
        category: this.category,
        description: 'Enable Telegram notifications'
      },
      adminEmailAddress: {
        key: 'admin_email_address',
        value: '<EMAIL>',
        type: 'string',
        category: this.category,
        description: 'Admin email address for notifications'
      },
      supportEmailAddress: {
        key: 'support_email_address',
        value: '<EMAIL>',
        type: 'string',
        category: this.category,
        description: 'Support email address'
      },
      noReplyEmailAddress: {
        key: 'no_reply_email_address',
        value: '<EMAIL>',
        type: 'string',
        category: this.category,
        description: 'No-reply email address'
      },
      emailTemplateLanguage: {
        key: 'email_template_language',
        value: 'tr',
        type: 'string',
        category: this.category,
        description: 'Default email template language'
      },
      notifyOnNewOrder: {
        key: 'notify_on_new_order',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Notify admin on new orders'
      },
      notifyOnNewUser: {
        key: 'notify_on_new_user',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Notify admin on new user registrations'
      },
      notifyOnPaymentReceived: {
        key: 'notify_on_payment_received',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Notify on payment received'
      },
      notifyOnDispute: {
        key: 'notify_on_dispute',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Notify admin on disputes'
      },
      whatsappApiKey: {
        key: 'whatsapp_api_key',
        value: '',
        type: 'string',
        category: this.category,
        description: 'WhatsApp API key'
      },
      whatsappPhoneNumber: {
        key: 'whatsapp_phone_number',
        value: '',
        type: 'string',
        category: this.category,
        description: 'WhatsApp business phone number'
      },
      telegramBotToken: {
        key: 'telegram_bot_token',
        value: '',
        type: 'string',
        category: this.category,
        description: 'Telegram bot token'
      },
      notificationRetryAttempts: {
        key: 'notification_retry_attempts',
        value: 3,
        type: 'number',
        category: this.category,
        description: 'Number of retry attempts for failed notifications'
      },
      notificationRetryDelay: {
        key: 'notification_retry_delay',
        value: 300, // 5 minutes in seconds
        type: 'number',
        category: this.category,
        description: 'Delay between retry attempts in seconds'
      }
    };
  }

  async validateSetting(key: string, value: any): Promise<boolean> {
    switch (key) {
      case 'email_notifications_enabled':
      case 'sms_notifications_enabled':
      case 'push_notifications_enabled':
      case 'whatsapp_notifications_enabled':
      case 'telegram_notifications_enabled':
      case 'notify_on_new_order':
      case 'notify_on_new_user':
      case 'notify_on_payment_received':
      case 'notify_on_dispute':
        return typeof value === 'boolean';
      case 'admin_email_address':
      case 'support_email_address':
      case 'no_reply_email_address':
        return typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      case 'email_template_language':
        return typeof value === 'string' && ['tr', 'en'].includes(value);
      case 'whatsapp_api_key':
      case 'whatsapp_phone_number':
      case 'telegram_bot_token':
        return typeof value === 'string';
      case 'notification_retry_attempts':
        return typeof value === 'number' && value >= 0 && value <= 10;
      case 'notification_retry_delay':
        return typeof value === 'number' && value > 0;
      default:
        return true;
    }
  }
}
