'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { ReactNode } from 'react';
import {
  ChartBarIcon,
  HeartIcon,
  ClipboardDocumentListIcon,
  ShoppingCartIcon,
  BuildingStorefrontIcon,
  HomeIcon,
  BeakerIcon,
  BellIcon
} from '@heroicons/react/24/outline';
import Sidebar from '@/components/dashboard/components/Sidebar';
import { ProductSelectionModal } from '@/components/ui/product-selection-modal';
import { QuoteRequestModal } from '@/components/ui/quote-request-modal';
import { useAuth } from '@/contexts/auth-context';



interface CustomerLayoutProps {
  children: ReactNode;
}

function CustomerLayoutClient({ children }: CustomerLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, isLoading } = useAuth();
  const [isProductSelectionModalOpen, setIsProductSelectionModalOpen] = useState(false);
  const [isQuoteRequestModalOpen, setIsQuoteRequestModalOpen] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);

  // Mock unread notifications count
  const unreadNotificationsCount = 3;

  // Auth kontrolü
  React.useEffect(() => {
    console.log('=== CUSTOMER LAYOUT AUTH CHECK ===');
    console.log('Customer Layout - isLoading:', isLoading);
    console.log('Customer Layout - Current user:', user);
    console.log('Customer Layout - User role:', user?.role);
    console.log('Customer Layout - Current pathname:', pathname);

    // Auth context henüz yükleniyorsa bekle
    if (isLoading) {
      console.log('⏳ Auth context loading, waiting...');
      return;
    }

    // Eğer kullanıcı giriş yapmamışsa ana sayfaya yönlendir
    if (!user) {
      console.log('❌ No user found, redirecting to home');
      router.replace('/?auth_required=true');
      return;
    }

    // Eğer kullanıcı customer değilse uygun sayfaya yönlendir
    if (user.role !== 'customer') {
      console.log('❌ User role is not customer:', user.role);
      switch (user.role) {
        case 'producer':
          router.replace('/producer/dashboard');
          break;
        case 'admin':
          router.replace('/admin/dashboard');
          break;
        default:
          router.replace('/?role_mismatch=true');
      }
      return;
    }

    console.log('✅ User is customer, access granted');
  }, [user, isLoading, router, pathname]);

  // Quote modal event listener
  React.useEffect(() => {
    const handleOpenQuoteModal = () => {
      setIsProductSelectionModalOpen(true);
    };

    window.addEventListener('openQuoteModal', handleOpenQuoteModal);
    return () => window.removeEventListener('openQuoteModal', handleOpenQuoteModal);
  }, []);

  const handleProductSelection = (products: any[]) => {
    setSelectedProducts(products);
    setIsProductSelectionModalOpen(false);
    setIsQuoteRequestModalOpen(true);
  };

  const handleQuoteSubmit = (quoteData: any) => {
    console.log('Quote submitted:', { products: selectedProducts, ...quoteData });
    setIsQuoteRequestModalOpen(false);
    setSelectedProducts([]);
  };

  const sidebarItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: HomeIcon,
      route: '/customer/dashboard',
    },
    {
      id: 'requests',
      label: 'Taleplerim',
      icon: ClipboardDocumentListIcon,
      route: '/customer/requests',
      submenu: [
        { label: 'Aktif Talepler', route: '/customer/requests/active' },
        { label: 'Tamamlanan', route: '/customer/requests/completed' },
        { label: 'İptal Edilen', route: '/customer/requests/cancelled' },
        { label: 'Numune Taleplerim', route: '/customer/requests/samples' },
      ]
    },
    {
      id: 'orders',
      label: 'Siparişlerim',
      icon: ShoppingCartIcon,
      route: '/customer/orders',
      submenu: [
        { label: 'Devam Eden', route: '/customer/orders/ongoing' },
        { label: 'Tamamlanan', route: '/customer/orders/completed' },
        { label: 'İptal Edilen', route: '/customer/orders/cancelled' },
      ]
    },
    {
      id: 'favorites',
      label: 'Favorilerim',
      icon: HeartIcon,
      route: '/customer/favorites',
    },
    {
      id: 'analytics',
      label: 'Analizler',
      icon: ChartBarIcon,
      route: '/customer/analytics',
      submenu: [
        { label: 'Alım Analizi', route: '/customer/analytics/purchases' },
        { label: 'Satış Analizi', route: '/customer/analytics/sales' },
        { label: 'Kar-Zarar Analizi', route: '/customer/analytics/profit-loss' },
      ]
    },
    {
      id: 'notifications',
      label: 'Bildirimler',
      icon: BellIcon,
      route: '/customer/notifications',
      badge: unreadNotificationsCount,
      submenu: [
        { label: 'Tüm Bildirimler', route: '/customer/notifications' },
        { label: 'Bildirim Ayarları', route: '/customer/notifications/settings' },
      ]
    },
    {
      id: 'stock',
      label: 'Stok Takibi',
      icon: BuildingStorefrontIcon,
      route: '/customer/stock',
    },
  ];

  const handleTabChange = (route: string) => {
    router.push(route);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Yükleniyor...</p>
        </div>
      </div>
    );
  }

  // Auth failed - don't render anything, redirect will happen
  if (!user || user.role !== 'customer') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Yönlendiriliyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar
        items={sidebarItems}
        activeTab={pathname}
        onTabChange={handleTabChange}
        userName={user?.name || 'Müşteri'}
        userEmail={user?.email || ''}
      />

      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
          <div className="w-full px-6 py-8">
            {children}
          </div>
        </main>
      </div>

      {/* Product Selection Modal */}
      <ProductSelectionModal
        isOpen={isProductSelectionModalOpen}
        onClose={() => setIsProductSelectionModalOpen(false)}
        onProductsSelected={handleProductSelection}
      />

      {/* Quote Request Modal */}
      <QuoteRequestModal
        isOpen={isQuoteRequestModalOpen}
        onClose={() => setIsQuoteRequestModalOpen(false)}
        onSubmit={handleQuoteSubmit}
        selectedProducts={selectedProducts}
      />
    </div>
  );
}

export default function CustomerLayout({ children }: CustomerLayoutProps) {
  return <CustomerLayoutClient>{children}</CustomerLayoutClient>;
}