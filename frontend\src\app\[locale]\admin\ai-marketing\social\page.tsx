'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Share2,
  Users,
  TrendingUp,
  Search,
  Plus,
  Eye,
  Edit,
  Trash2,
  Send,
  Clock,
  CheckCircle,
  XCircle,
  Play,
  Pause,
  Calendar,
  BarChart3,
  Download,
  Upload,
  AlertTriangle,
  Save
} from 'lucide-react';

interface SocialMediaAccount {
  id: string;
  platform: string;
  accountName: string;
  isActive: boolean;
  followers: number;
  engagement: number;
  lastSync: Date;
}

interface SocialMediaPost {
  id: string;
  platform: string;
  content: string;
  scheduledFor: Date;
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  metrics: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
  };
  aiGenerated: boolean;
  approvalStatus: 'pending' | 'approved' | 'rejected';
}

export default function SocialMediaPage() {
  const [accounts, setAccounts] = useState<SocialMediaAccount[]>([]);
  const [posts, setPosts] = useState<SocialMediaPost[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [isConnectingAccount, setIsConnectingAccount] = useState(false);
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);
  const [showConnectModal, setShowConnectModal] = useState(false);
  const [showContentModal, setShowContentModal] = useState(false);

  // Modal form states
  const [selectedPlatform, setSelectedPlatform] = useState('');
  const [contentTopic, setContentTopic] = useState('');
  const [contentTone, setContentTone] = useState('professional');

  // Notification states
  const [notification, setNotification] = useState<{
    show: boolean;
    message: string;
    type: 'success' | 'error' | 'info';
  }>({ show: false, message: '', type: 'info' });

  // Modal states for account operations
  const [showAccountDetailModal, setShowAccountDetailModal] = useState(false);
  const [showAccountEditModal, setShowAccountEditModal] = useState(false);
  const [showAccountAnalyticsModal, setShowAccountAnalyticsModal] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<SocialMediaAccount | null>(null);

  // Modal states for post operations
  const [showPostDetailModal, setShowPostDetailModal] = useState(false);
  const [showPostEditModal, setShowPostEditModal] = useState(false);
  const [showPostScheduleModal, setShowPostScheduleModal] = useState(false);
  const [showPostDeleteModal, setShowPostDeleteModal] = useState(false);
  const [selectedPost, setSelectedPost] = useState<SocialMediaPost | null>(null);

  useEffect(() => {
    fetchSocialMediaData();
  }, []);

  const showNotification = (message: string, type: 'success' | 'error' | 'info') => {
    setNotification({ show: true, message, type });
    setTimeout(() => {
      setNotification({ show: false, message: '', type: 'info' });
    }, 5000);
  };

  const fetchSocialMediaData = async () => {
    setLoading(true);
    try {
      // Fetch accounts from API
      const accountsResponse = await fetch('/api/admin/ai-marketing/social?action=accounts');
      const accountsData = await accountsResponse.json();

      // Fetch posts from API
      const postsResponse = await fetch('/api/admin/ai-marketing/social?action=posts');
      const postsData = await postsResponse.json();

      if (accountsData.success && postsData.success) {
        setAccounts(accountsData.data);
        setPosts(postsData.data);
      } else {
        console.error('API Error:', accountsData.error || postsData.error);
        // Fallback to mock data
        const mockAccounts: SocialMediaAccount[] = [
        {
          id: '1',
          platform: 'Facebook',
          accountName: 'Doğal Taş Pazaryeri',
          isActive: true,
          followers: 15420,
          engagement: 4.2,
          lastSync: new Date()
        },
        {
          id: '2',
          platform: 'Instagram',
          accountName: 'naturalstone_marketplace',
          isActive: true,
          followers: 8930,
          engagement: 6.8,
          lastSync: new Date()
        },
        {
          id: '3',
          platform: 'LinkedIn',
          accountName: 'Natural Stone Marketplace',
          isActive: true,
          followers: 3240,
          engagement: 3.1,
          lastSync: new Date()
        },
        {
          id: '4',
          platform: 'Twitter',
          accountName: '@NaturalStoneMP',
          isActive: true,
          followers: 5670,
          engagement: 2.9,
          lastSync: new Date()
        }
      ];

      const mockPosts: SocialMediaPost[] = [
        {
          id: '1',
          platform: 'Facebook',
          content: 'Yeni traverten koleksiyonumuz ile doğanın eşsiz güzelliğini evinize taşıyın! 🏠✨ #DoğalTaş #Traverten #Mimarlık',
          scheduledFor: new Date('2025-07-05T10:00:00'),
          status: 'scheduled',
          metrics: { views: 0, likes: 0, comments: 0, shares: 0 },
          aiGenerated: true,
          approvalStatus: 'approved'
        },
        {
          id: '2',
          platform: 'Instagram',
          content: 'Mermer işçiliğinin inceliklerini keşfedin 🎨 Her parça bir sanat eseri! #Mermer #Sanat #Kalite',
          scheduledFor: new Date('2025-07-04T15:30:00'),
          status: 'published',
          metrics: { views: 2340, likes: 156, comments: 23, shares: 12 },
          aiGenerated: true,
          approvalStatus: 'approved'
        },
        {
          id: '3',
          platform: 'LinkedIn',
          content: 'Türkiye doğal taş sektöründe sürdürülebilirlik ve kalite standartları hakkında düşüncelerimiz...',
          scheduledFor: new Date('2025-07-06T09:00:00'),
          status: 'draft',
          metrics: { views: 0, likes: 0, comments: 0, shares: 0 },
          aiGenerated: true,
          approvalStatus: 'pending'
        }
      ];

        setAccounts(mockAccounts);
        setPosts(mockPosts);
      }
    } catch (error) {
      console.error('Error fetching social media data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary">Taslak</Badge>;
      case 'scheduled':
        return <Badge variant="outline">Zamanlandı</Badge>;
      case 'published':
        return <Badge variant="default" className="bg-green-100 text-green-800">Yayınlandı</Badge>;
      case 'failed':
        return <Badge variant="destructive">Başarısız</Badge>;
      default:
        return <Badge variant="outline">Bilinmiyor</Badge>;
    }
  };

  const getApprovalBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Onay Bekliyor</Badge>;
      case 'approved':
        return <Badge variant="default" className="bg-green-100 text-green-800">Onaylandı</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Reddedildi</Badge>;
      default:
        return <Badge variant="outline">Bilinmiyor</Badge>;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'facebook':
        return '📘';
      case 'instagram':
        return '📷';
      case 'linkedin':
        return '💼';
      case 'twitter':
        return '🐦';
      case 'youtube':
        return '📺';
      case 'tiktok':
        return '🎵';
      default:
        return '📱';
    }
  };

  // İşlem fonksiyonları
  const generateOAuthURL = (platform: string): string => {
    const baseURL = window.location.origin;
    const redirectURI = `${baseURL}/api/auth/${platform.toLowerCase()}/callback`;

    switch (platform.toLowerCase()) {
      case 'facebook':
        return `https://www.facebook.com/v18.0/dialog/oauth?client_id=YOUR_FACEBOOK_APP_ID&redirect_uri=${encodeURIComponent(redirectURI)}&scope=pages_manage_posts,pages_read_engagement,pages_show_list&response_type=code`;

      case 'instagram':
        return `https://api.instagram.com/oauth/authorize?client_id=YOUR_INSTAGRAM_APP_ID&redirect_uri=${encodeURIComponent(redirectURI)}&scope=user_profile,user_media&response_type=code`;

      case 'linkedin':
        return `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=YOUR_LINKEDIN_CLIENT_ID&redirect_uri=${encodeURIComponent(redirectURI)}&scope=w_member_social`;

      case 'twitter':
        return `https://twitter.com/i/oauth2/authorize?response_type=code&client_id=YOUR_TWITTER_CLIENT_ID&redirect_uri=${encodeURIComponent(redirectURI)}&scope=tweet.read%20tweet.write%20users.read`;

      default:
        return '#';
    }
  };

  const handleConnectAccount = async (platform?: string, credentials?: any) => {
    console.log('handleConnectAccount called with:', { platform, credentials });
    if (!platform) {
      console.log('Opening connect modal');
      setShowConnectModal(true);
      return;
    }

    setIsConnectingAccount(true);
    try {
      // Gerçek OAuth flow için
      if (credentials?.oauth) {
        const oauthURL = generateOAuthURL(platform);
        if (oauthURL !== '#') {
          // Yeni pencerede OAuth sayfasını aç
          const popup = window.open(
            oauthURL,
            'oauth',
            'width=600,height=600,scrollbars=yes,resizable=yes'
          );

          // OAuth callback'i dinle
          const checkClosed = setInterval(() => {
            if (popup?.closed) {
              clearInterval(checkClosed);
              // OAuth tamamlandıktan sonra hesapları yenile
              fetchSocialMediaData();
              showNotification(`${platform} hesabı başarıyla bağlandı!`, 'success');
              setShowConnectModal(false);
              setSelectedPlatform('');
              setIsConnectingAccount(false);
            }
          }, 1000);

          return;
        }
      }

      // Fallback: Mock API çağrısı
      const response = await fetch('/api/admin/ai-marketing/social', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'connect-account',
          platform,
          credentials
        }),
      });

      const data = await response.json();

      if (data.success) {
        showNotification(data.message, 'success');
        // Refresh accounts list
        await fetchSocialMediaData();
        setShowConnectModal(false);
        setSelectedPlatform('');
      } else {
        showNotification('Hesap bağlama hatası: ' + data.error, 'error');
      }
    } catch (error) {
      console.error('Hesap bağlama hatası:', error);
      showNotification('Hesap bağlama sırasında bir hata oluştu.', 'error');
    } finally {
      setIsConnectingAccount(false);
    }
  };

  const handleGenerateContent = async (contentData?: any) => {
    console.log('handleGenerateContent called with:', contentData);
    if (!contentData) {
      console.log('Opening content modal');
      setShowContentModal(true);
      return;
    }

    setIsGeneratingContent(true);
    try {
      const response = await fetch('/api/admin/ai-marketing/social', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate-content',
          ...contentData
        }),
      });

      const data = await response.json();

      if (data.success) {
        showNotification(data.message, 'success');
        // Refresh posts list
        await fetchSocialMediaData();
        setShowContentModal(false);
        setSelectedPlatform('');
        setContentTopic('');
        setContentTone('professional');
      } else {
        showNotification('İçerik üretme hatası: ' + data.error, 'error');
      }
    } catch (error) {
      console.error('İçerik üretme hatası:', error);
      showNotification('İçerik üretme sırasında bir hata oluştu.', 'error');
    } finally {
      setIsGeneratingContent(false);
    }
  };

  const handleViewPost = (postId: string) => {
    console.log('Post görüntüleniyor:', postId);
    // Post detay modalı açılacak
  };

  const handleEditPost = (postId: string) => {
    console.log('Post düzenleniyor:', postId);
    // Post düzenleme modalı açılacak
  };

  const handleDeletePost = async (postId: string) => {
    if (confirm('Bu içeriği silmek istediğinizden emin misiniz?')) {
      try {
        console.log('Post siliniyor:', postId);
        // Backend API çağrısı yapılacak
        setPosts(posts.filter(p => p.id !== postId));
      } catch (error) {
        console.error('Post silme hatası:', error);
      }
    }
  };

  const handleSchedulePost = (postId: string) => {
    console.log('Post zamanlanıyor:', postId);
    // Zamanlama modalı açılacak
  };

  const handlePublishPost = async (postId: string) => {
    try {
      console.log('Post yayınlanıyor:', postId);
      // Backend API çağrısı yapılacak
      const updatedPosts = posts.map(p =>
        p.id === postId ? { ...p, status: 'published' as const } : p
      );
      setPosts(updatedPosts);
    } catch (error) {
      console.error('Post yayınlama hatası:', error);
    }
  };

  // Account operations
  const handleViewAccount = (account: SocialMediaAccount) => {
    console.log('Hesap görüntüleniyor:', account);
    setSelectedAccount(account);
    setShowAccountDetailModal(true);
  };

  const handleEditAccount = (account: SocialMediaAccount) => {
    console.log('Hesap düzenleniyor:', account);
    setSelectedAccount(account);
    setShowAccountEditModal(true);
  };

  const handleAccountAnalytics = (account: SocialMediaAccount) => {
    console.log('Hesap analitikleri:', account);
    setSelectedAccount(account);
    setShowAccountAnalyticsModal(true);
  };

  // Post operations
  const handleViewPostDetail = (post: SocialMediaPost) => {
    console.log('Post detayları görüntüleniyor:', post);
    setSelectedPost(post);
    setShowPostDetailModal(true);
  };

  const handleEditPostDetail = (post: SocialMediaPost) => {
    console.log('Post düzenleniyor:', post);
    setSelectedPost(post);
    setShowPostEditModal(true);
  };

  const handleSchedulePostDetail = (post: SocialMediaPost) => {
    console.log('Post zamanlaması düzenleniyor:', post);
    setSelectedPost(post);
    setShowPostScheduleModal(true);
  };

  const handleDeletePostDetail = (post: SocialMediaPost) => {
    console.log('Post siliniyor:', post);
    setSelectedPost(post);
    setShowPostDeleteModal(true);
  };

  // Analytics functions
  const handleExportAnalytics = async (reportType: string) => {
    try {
      showNotification(`${reportType} raporu hazırlanıyor...`, 'info');

      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 2000));

      const reportData = {
        reportType,
        accounts: accounts.length,
        posts: posts.length,
        totalEngagement: posts.reduce((sum, post) => sum + post.metrics.likes + post.metrics.comments + post.metrics.shares, 0),
        generatedAt: new Date().toISOString()
      };

      // Create and download file
      const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `social-media-${reportType}-${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showNotification(`${reportType} raporu başarıyla indirildi`, 'success');
    } catch (error) {
      console.error('Export error:', error);
      showNotification('Rapor oluşturulurken bir hata oluştu.', 'error');
    }
  };

  // Settings functions
  const handleSaveSettings = async () => {
    try {
      showNotification('Sosyal medya ayarları kaydediliyor...', 'info');

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      showNotification('Sosyal medya ayarları başarıyla kaydedildi', 'success');
    } catch (error) {
      console.error('Settings save error:', error);
      showNotification('Ayarlar kaydedilirken bir hata oluştu.', 'error');
    }
  };

  const handleResetSettings = async () => {
    if (confirm('Tüm sosyal medya ayarlarını varsayılan değerlere sıfırlamak istediğinizden emin misiniz?')) {
      try {
        showNotification('Ayarlar sıfırlanıyor...', 'info');

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        showNotification('Ayarlar varsayılan değerlere sıfırlandı', 'success');
      } catch (error) {
        console.error('Settings reset error:', error);
        showNotification('Ayarlar sıfırlanırken bir hata oluştu.', 'error');
      }
    }
  };

  const filteredPosts = posts.filter(post =>
    post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    post.platform.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalFollowers = accounts.reduce((sum, account) => sum + account.followers, 0);
  const avgEngagement = accounts.length > 0
    ? accounts.reduce((sum, account) => sum + account.engagement, 0) / accounts.length
    : 0;
  const pendingApproval = posts.filter(p => p.approvalStatus === 'pending').length;
  const scheduledPosts = posts.filter(p => p.status === 'scheduled').length;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <span className="text-lg">Sosyal medya verileri yükleniyor...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Sosyal Medya AI Yönetimi</h1>
          <p className="text-gray-600 mt-1">
            Tüm sosyal medya hesaplarınızı AI ile yönetin ve içerik üretin
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => handleConnectAccount()}
            disabled={isConnectingAccount}
            className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
          >
            <Plus className="w-4 h-4 mr-2" />
            {isConnectingAccount ? 'Bağlanıyor...' : 'Hesap Bağla'}
          </Button>
          <Button
            onClick={() => handleGenerateContent()}
            disabled={isGeneratingContent}
            className="hover:bg-blue-600 transition-colors"
          >
            <Send className="w-4 h-4 mr-2" />
            {isGeneratingContent ? 'Üretiliyor...' : 'AI İçerik Üret'}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Toplam Takipçi</p>
                <p className="text-2xl font-bold">{totalFollowers.toLocaleString()}</p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Ortalama Etkileşim</p>
                <p className="text-2xl font-bold">{avgEngagement.toFixed(1)}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Zamanlanmış Post</p>
                <p className="text-2xl font-bold">{scheduledPosts}</p>
              </div>
              <Calendar className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Onay Bekleyen</p>
                <p className="text-2xl font-bold">{pendingApproval}</p>
              </div>
              <Clock className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Tabs */}
      <Tabs defaultValue="accounts" className="space-y-4">
        <TabsList>
          <TabsTrigger value="accounts">Hesaplar</TabsTrigger>
          <TabsTrigger value="posts">
            İçerikler
            {pendingApproval > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {pendingApproval}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="analytics">Analitik</TabsTrigger>
          <TabsTrigger value="settings">AI Ayarları</TabsTrigger>
        </TabsList>

        <TabsContent value="accounts" className="space-y-4">
          {/* Social Media Accounts */}
          <Card>
            <CardHeader>
              <CardTitle>Bağlı Sosyal Medya Hesapları</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Platform</TableHead>
                    <TableHead>Hesap Adı</TableHead>
                    <TableHead>Takipçi</TableHead>
                    <TableHead>Etkileşim</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead>Son Senkronizasyon</TableHead>
                    <TableHead>İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {accounts.map((account) => (
                    <TableRow key={account.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="text-2xl">{getPlatformIcon(account.platform)}</span>
                          <span className="font-medium">{account.platform}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {account.accountName}
                      </TableCell>
                      <TableCell>
                        {account.followers.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <span className="font-medium text-green-600">
                          {account.engagement}%
                        </span>
                      </TableCell>
                      <TableCell>
                        {account.isActive ? (
                          <Badge variant="default" className="bg-green-100 text-green-800">
                            Aktif
                          </Badge>
                        ) : (
                          <Badge variant="secondary">Pasif</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {account.lastSync.toLocaleString('tr-TR')}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewAccount(account)}
                            title="Hesap Detayları"
                            className="hover:bg-blue-50 hover:text-blue-600"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditAccount(account)}
                            title="Hesap Düzenle"
                            className="hover:bg-green-50 hover:text-green-600"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAccountAnalytics(account)}
                            title="Analitikler"
                            className="hover:bg-purple-50 hover:text-purple-600"
                          >
                            <BarChart3 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="posts" className="space-y-4">
          {/* Search */}
          <div className="flex items-center space-x-2">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="İçerik ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Posts Table */}
          <Card>
            <CardHeader>
              <CardTitle>Sosyal Medya İçerikleri</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Platform</TableHead>
                    <TableHead>İçerik</TableHead>
                    <TableHead>Zamanlama</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead>Onay Durumu</TableHead>
                    <TableHead>Performans</TableHead>
                    <TableHead>İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPosts.map((post) => (
                    <TableRow key={post.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{getPlatformIcon(post.platform)}</span>
                          <span className="font-medium">{post.platform}</span>
                        </div>
                      </TableCell>
                      <TableCell className="max-w-xs">
                        <div className="truncate">
                          {post.content}
                        </div>
                        {post.aiGenerated && (
                          <Badge variant="outline" className="mt-1 text-xs">
                            AI Üretimi
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {post.scheduledFor.toLocaleString('tr-TR')}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(post.status)}
                      </TableCell>
                      <TableCell>
                        {getApprovalBadge(post.approvalStatus)}
                      </TableCell>
                      <TableCell>
                        {post.status === 'published' ? (
                          <div className="text-xs space-y-1">
                            <div>👁️ {post.metrics.views}</div>
                            <div>❤️ {post.metrics.likes}</div>
                            <div>💬 {post.metrics.comments}</div>
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewPostDetail(post)}
                            title="İçerik Detayları"
                            className="hover:bg-blue-50 hover:text-blue-600"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditPostDetail(post)}
                            title="İçerik Düzenle"
                            className="hover:bg-green-50 hover:text-green-600"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          {post.status === 'scheduled' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleSchedulePostDetail(post)}
                              title="Zamanlamayı Düzenle"
                              className="hover:bg-orange-50 hover:text-orange-600"
                            >
                              <Pause className="w-4 h-4" />
                            </Button>
                          )}
                          {post.status === 'draft' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePublishPost(post.id)}
                              title="Hemen Yayınla"
                              className="hover:bg-green-50 hover:text-green-600"
                            >
                              <Play className="w-4 h-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeletePostDetail(post)}
                            title="İçeriği Sil"
                            className="text-red-600 hover:bg-red-50 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {/* Analytics Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card
              className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => showNotification('Toplam gönderi detayları görüntüleniyor...', 'info')}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Toplam Gönderi</p>
                    <p className="text-2xl font-bold">{posts.length}</p>
                    <p className="text-xs text-blue-600 mt-1">Bu ay +{Math.floor(posts.length * 0.15)} • Detayları gör →</p>
                  </div>
                  <Share2 className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card
              className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => showNotification('Etkileşim detayları görüntüleniyor...', 'info')}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Toplam Etkileşim</p>
                    <p className="text-2xl font-bold">{(posts.reduce((sum, post) => sum + post.metrics.likes + post.metrics.comments + post.metrics.shares, 0)).toLocaleString()}</p>
                    <p className="text-xs text-green-600 mt-1">+12.5% bu hafta • Detayları gör →</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card
              className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => showNotification('Erişim detayları görüntüleniyor...', 'info')}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Ortalama Erişim</p>
                    <p className="text-2xl font-bold">{Math.floor(posts.reduce((sum, post) => sum + post.metrics.views, 0) / posts.length || 0).toLocaleString()}</p>
                    <p className="text-xs text-purple-600 mt-1">Post başına • Detayları gör →</p>
                  </div>
                  <Eye className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
            <Card
              className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => showNotification('AI performans detayları görüntüleniyor...', 'info')}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">AI Başarı Oranı</p>
                    <p className="text-2xl font-bold">94.2%</p>
                    <p className="text-xs text-orange-600 mt-1">AI üretimi • Detayları gör →</p>
                  </div>
                  <BarChart3 className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Platform Performance */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Platform Performansı</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {accounts.map((account) => (
                    <div key={account.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{getPlatformIcon(account.platform)}</span>
                        <div>
                          <p className="font-medium">{account.platform}</p>
                          <p className="text-sm text-gray-600">{account.followers.toLocaleString()} takipçi</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-green-600">{account.engagement}%</p>
                        <p className="text-xs text-gray-600">Etkileşim</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>İçerik Performansı</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {posts.slice(0, 5).map((post) => (
                    <div key={post.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <div className="flex-1">
                        <p className="text-sm font-medium line-clamp-1">{post.content}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs">{getPlatformIcon(post.platform)}</span>
                          <span className="text-xs text-gray-600">{post.platform}</span>
                          {getStatusBadge(post.status)}
                        </div>
                      </div>
                      <div className="text-right ml-4">
                        <p className="text-sm font-semibold">{(post.metrics.likes + post.metrics.comments + post.metrics.shares).toLocaleString()}</p>
                        <p className="text-xs text-gray-600">Etkileşim</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Haftalık Etkileşim Trendi</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-50 rounded flex items-center justify-center">
                  <div className="text-center">
                    <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Grafik yükleniyor...</p>
                    <p className="text-xs text-gray-400 mt-1">Etkileşim trend analizi</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Platform Dağılımı</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-50 rounded flex items-center justify-center">
                  <div className="text-center">
                    <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Grafik yükleniyor...</p>
                    <p className="text-xs text-gray-400 mt-1">Platform bazlı dağılım</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Export Options */}
          <Card>
            <CardHeader>
              <CardTitle>Rapor ve Export</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Button
                  variant="outline"
                  className="h-20 flex flex-col hover:bg-blue-50 hover:border-blue-300 transition-colors"
                  onClick={() => handleExportAnalytics('performans-raporu')}
                >
                  <Download className="w-6 h-6 mb-2" />
                  <span>Performans Raporu</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-20 flex flex-col hover:bg-green-50 hover:border-green-300 transition-colors"
                  onClick={() => handleExportAnalytics('analitik-raporu')}
                >
                  <BarChart3 className="w-6 h-6 mb-2" />
                  <span>Analitik Raporu</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-20 flex flex-col hover:bg-purple-50 hover:border-purple-300 transition-colors"
                  onClick={() => handleExportAnalytics('haftalik-ozet')}
                >
                  <Calendar className="w-6 h-6 mb-2" />
                  <span>Haftalık Özet</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-20 flex flex-col hover:bg-orange-50 hover:border-orange-300 transition-colors"
                  onClick={() => handleExportAnalytics('trend-analizi')}
                >
                  <TrendingUp className="w-6 h-6 mb-2" />
                  <span>Trend Analizi</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          {/* AI Content Generation Settings */}
          <Card>
            <CardHeader>
              <CardTitle>AI İçerik Üretim Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Varsayılan Ton</label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="professional">Profesyonel</option>
                    <option value="friendly">Samimi</option>
                    <option value="promotional">Tanıtım</option>
                    <option value="educational">Eğitici</option>
                    <option value="casual">Günlük</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Hedef Kitle</label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="general">Genel</option>
                    <option value="architects">Mimarlar</option>
                    <option value="contractors">Müteahhitler</option>
                    <option value="homeowners">Ev Sahipleri</option>
                    <option value="designers">İç Mimarlar</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">İçerik Uzunluğu</label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="short">Kısa (50-100 karakter)</option>
                    <option value="medium">Orta (100-200 karakter)</option>
                    <option value="long">Uzun (200+ karakter)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Hashtag Sayısı</label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="3">3 hashtag</option>
                    <option value="5">5 hashtag</option>
                    <option value="8">8 hashtag</option>
                    <option value="10">10 hashtag</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Otomatik Hashtag Önerileri</label>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="auto-hashtags" defaultChecked className="rounded" />
                  <label htmlFor="auto-hashtags" className="text-sm">AI otomatik hashtag önersin</label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Platform Specific Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Platform Bazlı Ayarlar</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {accounts.map((account) => (
                <div key={account.id} className="border rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-4">
                    <span className="text-2xl">{getPlatformIcon(account.platform)}</span>
                    <div>
                      <h4 className="font-medium">{account.platform}</h4>
                      <p className="text-sm text-gray-600">{account.accountName}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Günlük Post Limiti</label>
                      <input type="number" defaultValue="3" min="1" max="10" className="w-full border rounded px-3 py-2" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Optimal Paylaşım Saati</label>
                      <input type="time" defaultValue="09:00" className="w-full border rounded px-3 py-2" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Otomatik Paylaşım</label>
                      <div className="flex items-center space-x-2 mt-2">
                        <input type="checkbox" id={`auto-${account.id}`} defaultChecked={account.isActive} className="rounded" />
                        <label htmlFor={`auto-${account.id}`} className="text-sm">Aktif</label>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* AI Model Settings */}
          <Card>
            <CardHeader>
              <CardTitle>AI Model Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Kullanılacak AI Model</label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="gpt-4">GPT-4 (Önerilen)</option>
                    <option value="gpt-3.5">GPT-3.5 Turbo</option>
                    <option value="claude">Claude 3</option>
                    <option value="gemini">Gemini Pro</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Yaratıcılık Seviyesi</label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="0.3">Düşük (Tutarlı)</option>
                    <option value="0.7">Orta (Dengeli)</option>
                    <option value="1.0">Yüksek (Yaratıcı)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">İçerik Filtreleme</label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="filter-spam" defaultChecked className="rounded" />
                      <label htmlFor="filter-spam" className="text-sm">Spam filtreleme</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="filter-inappropriate" defaultChecked className="rounded" />
                      <label htmlFor="filter-inappropriate" className="text-sm">Uygunsuz içerik filtreleme</label>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Otomatik Onay</label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="auto-approve" className="rounded" />
                      <label htmlFor="auto-approve" className="text-sm">AI içeriklerini otomatik onayla</label>
                    </div>
                    <p className="text-xs text-gray-500">Dikkat: Bu seçenek tüm AI üretimlerini otomatik olarak yayınlar</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Save Settings */}
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={handleResetSettings}
              className="hover:bg-red-50 hover:border-red-300 transition-colors"
            >
              Varsayılana Sıfırla
            </Button>
            <Button
              onClick={handleSaveSettings}
              className="hover:bg-green-600 transition-colors"
            >
              <Save className="w-4 h-4 mr-2" />
              Ayarları Kaydet
            </Button>
          </div>
        </TabsContent>
      </Tabs>

      {/* Connect Account Modal */}
      {showConnectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Sosyal Medya Hesabı Bağla</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Platform Seçin</label>
                <select
                  className="w-full border rounded px-3 py-2"
                  value={selectedPlatform}
                  onChange={(e) => setSelectedPlatform(e.target.value)}
                >
                  <option value="">Platform seçin...</option>
                  <option value="facebook">Facebook</option>
                  <option value="instagram">Instagram</option>
                  <option value="linkedin">LinkedIn</option>
                  <option value="twitter">Twitter</option>
                  <option value="youtube">YouTube</option>
                  <option value="tiktok">TikTok</option>
                </select>
              </div>
              {selectedPlatform && (
                <div>
                  <label className="block text-sm font-medium mb-2">Hesap Bilgileri</label>
                  <div className="p-3 bg-blue-50 rounded border">
                    <p className="text-sm text-blue-700 mb-2">
                      {selectedPlatform} hesabınızı bağlamak için OAuth yetkilendirmesi gereklidir.
                    </p>
                    <div className="text-xs text-gray-600 space-y-1">
                      <p>• {selectedPlatform} Developer hesabınızın olması gerekir</p>
                      <p>• API anahtarları ve izinler gereklidir</p>
                      <p>• Bağlantı sonrası otomatik içerik paylaşımı başlayacak</p>
                    </div>
                  </div>

                  {/* OAuth URL Generator */}
                  <div className="mt-3 p-3 bg-yellow-50 rounded border border-yellow-200">
                    <p className="text-sm text-yellow-800 mb-2">
                      <strong>Geliştirici Notu:</strong> Gerçek OAuth entegrasyonu için:
                    </p>
                    <div className="text-xs text-yellow-700 space-y-1">
                      <p>1. {selectedPlatform} Developer Console'da uygulama oluşturun</p>
                      <p>2. Callback URL'i ayarlayın: <code className="bg-yellow-100 px-1 rounded">
                        {window.location.origin}/api/auth/{selectedPlatform.toLowerCase()}/callback
                      </code></p>
                      <p>3. Client ID ve Secret'ı environment variables'a ekleyin</p>
                      <p>4. OAuth flow'u implement edin</p>
                    </div>
                  </div>
                </div>
              )}
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowConnectModal(false);
                    setSelectedPlatform('');
                  }}
                >
                  İptal
                </Button>
                <Button
                  onClick={() => handleConnectAccount(selectedPlatform, { oauth: true })}
                  disabled={!selectedPlatform || isConnectingAccount}
                >
                  {isConnectingAccount ? 'Bağlanıyor...' : 'Bağla'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Generate Content Modal */}
      {showContentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <h3 className="text-lg font-semibold mb-4">AI İçerik Üret</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Platform</label>
                <select
                  className="w-full border rounded px-3 py-2"
                  value={selectedPlatform}
                  onChange={(e) => setSelectedPlatform(e.target.value)}
                >
                  <option value="">Platform seçin...</option>
                  <option value="facebook">Facebook</option>
                  <option value="instagram">Instagram</option>
                  <option value="linkedin">LinkedIn</option>
                  <option value="twitter">Twitter</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">İçerik Konusu</label>
                <input
                  type="text"
                  placeholder="Örn: Doğal taş ürünleri tanıtımı"
                  className="w-full border rounded px-3 py-2"
                  value={contentTopic}
                  onChange={(e) => setContentTopic(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Ton</label>
                <select
                  className="w-full border rounded px-3 py-2"
                  value={contentTone}
                  onChange={(e) => setContentTone(e.target.value)}
                >
                  <option value="professional">Profesyonel</option>
                  <option value="friendly">Samimi</option>
                  <option value="promotional">Tanıtım</option>
                  <option value="educational">Eğitici</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Hedef Kitle</label>
                <select className="w-full border rounded px-3 py-2">
                  <option value="general">Genel</option>
                  <option value="architects">Mimarlar</option>
                  <option value="contractors">Müteahhitler</option>
                  <option value="homeowners">Ev Sahipleri</option>
                  <option value="designers">İç Mimarlar</option>
                </select>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowContentModal(false);
                    setSelectedPlatform('');
                    setContentTopic('');
                    setContentTone('professional');
                  }}
                >
                  İptal
                </Button>
                <Button
                  onClick={() => handleGenerateContent({
                    platform: selectedPlatform,
                    topic: contentTopic,
                    tone: contentTone,
                    targetAudience: 'general'
                  })}
                  disabled={!selectedPlatform || !contentTopic || isGeneratingContent}
                >
                  {isGeneratingContent ? 'Üretiliyor...' : 'İçerik Üret'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Account Detail Modal */}
      {showAccountDetailModal && selectedAccount && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">Hesap Detayları</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAccountDetailModal(false)}
              >
                <XCircle className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Platform</label>
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl">{getPlatformIcon(selectedAccount.platform)}</span>
                    <span className="font-medium">{selectedAccount.platform}</span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Hesap Adı</label>
                  <p className="text-gray-900">{selectedAccount.accountName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Takipçi Sayısı</label>
                  <p className="text-gray-900 font-semibold">{selectedAccount.followers.toLocaleString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Etkileşim Oranı</label>
                  <p className="text-gray-900 font-semibold text-green-600">{selectedAccount.engagement}%</p>
                </div>
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Durum</label>
                <div className="flex items-center space-x-4">
                  {selectedAccount.isActive ? (
                    <Badge variant="default" className="bg-green-100 text-green-800">Aktif</Badge>
                  ) : (
                    <Badge variant="secondary">Pasif</Badge>
                  )}
                  <span className="text-sm text-gray-600">
                    Son senkronizasyon: {selectedAccount.lastSync.toLocaleString('tr-TR')}
                  </span>
                </div>
              </div>

              {/* Recent Activity */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Son Aktiviteler</label>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Bu hafta paylaşılan post:</span>
                      <span className="font-medium">12</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Ortalama etkileşim:</span>
                      <span className="font-medium">156</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>En iyi performans:</span>
                      <span className="font-medium">2.3k beğeni</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowAccountDetailModal(false)}
              >
                Kapat
              </Button>
              <Button
                onClick={() => {
                  setShowAccountDetailModal(false);
                  handleEditAccount(selectedAccount);
                }}
              >
                Düzenle
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Account Edit Modal */}
      {showAccountEditModal && selectedAccount && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">Hesap Düzenle</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAccountEditModal(false)}
              >
                <XCircle className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Hesap Adı</label>
                <Input
                  defaultValue={selectedAccount.accountName}
                  placeholder="Hesap adını girin"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Durum</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="account-active"
                    defaultChecked={selectedAccount.isActive}
                    className="rounded"
                  />
                  <label htmlFor="account-active" className="text-sm">
                    Hesabı aktif tut
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Otomatik Paylaşım Saatleri</label>
                <div className="grid grid-cols-2 gap-2">
                  <Input type="time" defaultValue="09:00" />
                  <Input type="time" defaultValue="18:00" />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  İçerikler bu saatlerde otomatik olarak paylaşılacak
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Günlük Post Limiti</label>
                <Input
                  type="number"
                  defaultValue="3"
                  min="1"
                  max="10"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowAccountEditModal(false)}
              >
                İptal
              </Button>
              <Button
                onClick={() => {
                  showNotification('Hesap ayarları güncellendi', 'success');
                  setShowAccountEditModal(false);
                }}
              >
                Kaydet
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Account Analytics Modal */}
      {showAccountAnalyticsModal && selectedAccount && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">
                {selectedAccount.platform} Analitikleri - {selectedAccount.accountName}
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAccountAnalyticsModal(false)}
              >
                <XCircle className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* KPI Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Takipçi</p>
                        <p className="text-2xl font-bold">{selectedAccount.followers.toLocaleString()}</p>
                        <p className="text-xs text-green-600">+12% bu ay</p>
                      </div>
                      <Users className="w-8 h-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Etkileşim</p>
                        <p className="text-2xl font-bold">{selectedAccount.engagement}%</p>
                        <p className="text-xs text-green-600">+0.3% bu hafta</p>
                      </div>
                      <TrendingUp className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Bu Ay Post</p>
                        <p className="text-2xl font-bold">24</p>
                        <p className="text-xs text-blue-600">Hedef: 30</p>
                      </div>
                      <Share2 className="w-8 h-8 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Erişim</p>
                        <p className="text-2xl font-bold">12.5K</p>
                        <p className="text-xs text-green-600">+8% bu hafta</p>
                      </div>
                      <Eye className="w-8 h-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Charts Placeholder */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Takipçi Artışı (Son 30 Gün)</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-48 bg-gray-50 rounded flex items-center justify-center">
                      <div className="text-center">
                        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-500">Grafik yükleniyor...</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Etkileşim Oranları</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-48 bg-gray-50 rounded flex items-center justify-center">
                      <div className="text-center">
                        <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-500">Grafik yükleniyor...</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Top Posts */}
              <Card>
                <CardHeader>
                  <CardTitle>En İyi Performans Gösteren Postlar</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div className="flex-1">
                          <p className="text-sm font-medium">Post #{i} - Doğal taş koleksiyonu tanıtımı</p>
                          <p className="text-xs text-gray-600">2 gün önce yayınlandı</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-bold">{(Math.random() * 1000 + 500).toFixed(0)} etkileşim</p>
                          <p className="text-xs text-green-600">%{(Math.random() * 5 + 2).toFixed(1)} etkileşim oranı</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowAccountAnalyticsModal(false)}
              >
                Kapat
              </Button>
              <Button>
                <Download className="w-4 h-4 mr-2" />
                Rapor İndir
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Post Detail Modal */}
      {showPostDetailModal && selectedPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">İçerik Detayları</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPostDetailModal(false)}
              >
                <XCircle className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Platform</label>
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl">{getPlatformIcon(selectedPost.platform)}</span>
                    <span className="font-medium">{selectedPost.platform}</span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Durum</label>
                  {getStatusBadge(selectedPost.status)}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Onay Durumu</label>
                  {getApprovalBadge(selectedPost.approvalStatus)}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">AI Üretimi</label>
                  <Badge variant={selectedPost.aiGenerated ? "default" : "secondary"}>
                    {selectedPost.aiGenerated ? "AI Üretimi" : "Manuel"}
                  </Badge>
                </div>
              </div>

              {/* Content */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">İçerik</label>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-900 whitespace-pre-wrap">{selectedPost.content}</p>
                </div>
              </div>

              {/* Scheduling */}
              {selectedPost.scheduledFor && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Zamanlama</label>
                  <p className="text-gray-900">
                    {new Date(selectedPost.scheduledFor).toLocaleString('tr-TR')}
                  </p>
                </div>
              )}

              {/* Metrics */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Performans Metrikleri</label>
                <div className="grid grid-cols-4 gap-4">
                  <div className="bg-blue-50 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedPost.metrics.views}</div>
                    <div className="text-xs text-blue-600">Görüntülenme</div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-green-600">{selectedPost.metrics.likes}</div>
                    <div className="text-xs text-green-600">Beğeni</div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-purple-600">{selectedPost.metrics.comments}</div>
                    <div className="text-xs text-purple-600">Yorum</div>
                  </div>
                  <div className="bg-orange-50 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-orange-600">{selectedPost.metrics.shares}</div>
                    <div className="text-xs text-orange-600">Paylaşım</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowPostDetailModal(false)}
              >
                Kapat
              </Button>
              <Button
                onClick={() => {
                  setShowPostDetailModal(false);
                  handleEditPostDetail(selectedPost);
                }}
              >
                Düzenle
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Post Edit Modal */}
      {showPostEditModal && selectedPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">İçerik Düzenle</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPostEditModal(false)}
              >
                <XCircle className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-4">
              {/* Platform Info */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Platform</label>
                <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                  <span className="text-xl">{getPlatformIcon(selectedPost.platform)}</span>
                  <span className="font-medium">{selectedPost.platform}</span>
                </div>
              </div>

              {/* Content */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">İçerik Metni</label>
                <textarea
                  className="w-full border rounded px-3 py-2 h-32 resize-none"
                  defaultValue={selectedPost.content}
                  placeholder="İçerik metnini girin..."
                />
                <p className="text-xs text-gray-500 mt-1">
                  {selectedPost.platform === 'Twitter' ? 'Maksimum 280 karakter' : 'Maksimum 2200 karakter'}
                </p>
              </div>

              {/* Hashtags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Hashtag'ler</label>
                <Input
                  placeholder="#doğaltaş #mermer #granit"
                  defaultValue="#doğaltaş #mermer"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Hashtag'leri boşlukla ayırın
                </p>
              </div>

              {/* Media Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Medya</label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <div className="space-y-2">
                    <div className="text-gray-400">
                      <Upload className="w-8 h-8 mx-auto" />
                    </div>
                    <div>
                      <Button variant="outline" size="sm">
                        Resim/Video Seç
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500">
                      PNG, JPG, MP4 (Maks. 10MB)
                    </p>
                  </div>
                </div>
              </div>

              {/* Scheduling */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Zamanlama</label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="date"
                    defaultValue={selectedPost.scheduledFor ?
                      new Date(selectedPost.scheduledFor).toISOString().split('T')[0] :
                      new Date().toISOString().split('T')[0]
                    }
                  />
                  <Input
                    type="time"
                    defaultValue={selectedPost.scheduledFor ?
                      new Date(selectedPost.scheduledFor).toTimeString().slice(0, 5) :
                      "09:00"
                    }
                  />
                </div>
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Durum</label>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="postStatus"
                      value="draft"
                      defaultChecked={selectedPost.status === 'draft'}
                      className="mr-2"
                    />
                    Taslak
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="postStatus"
                      value="scheduled"
                      defaultChecked={selectedPost.status === 'scheduled'}
                      className="mr-2"
                    />
                    Zamanlanmış
                  </label>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowPostEditModal(false)}
              >
                İptal
              </Button>
              <Button
                onClick={() => {
                  showNotification('İçerik başarıyla güncellendi', 'success');
                  setShowPostEditModal(false);
                  fetchSocialMediaData(); // Refresh data
                }}
              >
                Kaydet
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Post Schedule Modal */}
      {showPostScheduleModal && selectedPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">Zamanlama Ayarları</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPostScheduleModal(false)}
              >
                <XCircle className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-4">
              {/* Current Content Preview */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">İçerik Önizlemesi</label>
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">{getPlatformIcon(selectedPost.platform)}</span>
                    <span className="font-medium text-sm">{selectedPost.platform}</span>
                  </div>
                  <p className="text-sm text-gray-700 line-clamp-3">{selectedPost.content}</p>
                </div>
              </div>

              {/* Current Schedule */}
              {selectedPost.scheduledFor && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Mevcut Zamanlama</label>
                  <div className="bg-blue-50 rounded p-2 text-sm text-blue-700">
                    📅 {new Date(selectedPost.scheduledFor).toLocaleString('tr-TR')}
                  </div>
                </div>
              )}

              {/* New Schedule */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Yeni Zamanlama</label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">Tarih</label>
                    <Input
                      type="date"
                      defaultValue={selectedPost.scheduledFor ?
                        new Date(selectedPost.scheduledFor).toISOString().split('T')[0] :
                        new Date().toISOString().split('T')[0]
                      }
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">Saat</label>
                    <Input
                      type="time"
                      defaultValue={selectedPost.scheduledFor ?
                        new Date(selectedPost.scheduledFor).toTimeString().slice(0, 5) :
                        "09:00"
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Quick Schedule Options */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Hızlı Seçenekler</label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const tomorrow = new Date();
                      tomorrow.setDate(tomorrow.getDate() + 1);
                      tomorrow.setHours(9, 0, 0, 0);
                      console.log('Yarın 09:00 için zamanlandı');
                    }}
                  >
                    <Calendar className="w-4 h-4 mr-1" />
                    Yarın 09:00
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const nextWeek = new Date();
                      nextWeek.setDate(nextWeek.getDate() + 7);
                      nextWeek.setHours(10, 0, 0, 0);
                      console.log('Gelecek hafta 10:00 için zamanlandı');
                    }}
                  >
                    <Calendar className="w-4 h-4 mr-1" />
                    Gelecek Hafta
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const evening = new Date();
                      evening.setHours(18, 0, 0, 0);
                      console.log('Bugün 18:00 için zamanlandı');
                    }}
                  >
                    <Clock className="w-4 h-4 mr-1" />
                    Bu Akşam 18:00
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const weekend = new Date();
                      const daysUntilSaturday = 6 - weekend.getDay();
                      weekend.setDate(weekend.getDate() + daysUntilSaturday);
                      weekend.setHours(11, 0, 0, 0);
                      console.log('Hafta sonu 11:00 için zamanlandı');
                    }}
                  >
                    <Calendar className="w-4 h-4 mr-1" />
                    Hafta Sonu
                  </Button>
                </div>
              </div>

              {/* Repeat Options */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Tekrarlama</label>
                <select className="w-full border rounded px-3 py-2">
                  <option value="none">Tekrarlanmasın</option>
                  <option value="daily">Günlük</option>
                  <option value="weekly">Haftalık</option>
                  <option value="monthly">Aylık</option>
                </select>
              </div>

              {/* Action Buttons */}
              {selectedPost.status === 'scheduled' && (
                <div className="bg-yellow-50 rounded p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-yellow-800">Zamanlanmış İçerik</p>
                      <p className="text-xs text-yellow-600">Bu içerik zaten zamanlanmış durumda</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        showNotification('İçerik zamanlaması durduruldu', 'info');
                        setShowPostScheduleModal(false);
                      }}
                    >
                      <Pause className="w-4 h-4 mr-1" />
                      Durdur
                    </Button>
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowPostScheduleModal(false)}
              >
                İptal
              </Button>
              <Button
                onClick={() => {
                  showNotification('İçerik başarıyla zamanlandı', 'success');
                  setShowPostScheduleModal(false);
                  fetchSocialMediaData(); // Refresh data
                }}
              >
                <Clock className="w-4 h-4 mr-2" />
                Zamanla
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Post Delete Modal */}
      {showPostDeleteModal && selectedPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-red-600">İçeriği Sil</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPostDeleteModal(false)}
              >
                <XCircle className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-4">
              {/* Warning */}
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
                  <p className="text-sm font-medium text-red-800">Dikkat!</p>
                </div>
                <p className="text-sm text-red-700 mt-1">
                  Bu işlem geri alınamaz. İçerik kalıcı olarak silinecektir.
                </p>
              </div>

              {/* Content Preview */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Silinecek İçerik</label>
                <div className="bg-gray-50 rounded-lg p-3 border">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">{getPlatformIcon(selectedPost.platform)}</span>
                    <span className="font-medium text-sm">{selectedPost.platform}</span>
                    {getStatusBadge(selectedPost.status)}
                  </div>
                  <p className="text-sm text-gray-700 line-clamp-3">{selectedPost.content}</p>
                  {selectedPost.scheduledFor && (
                    <p className="text-xs text-gray-500 mt-2">
                      📅 Zamanlanmış: {new Date(selectedPost.scheduledFor).toLocaleString('tr-TR')}
                    </p>
                  )}
                </div>
              </div>

              {/* Delete Reason */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Silme Sebebi</label>
                <select className="w-full border rounded px-3 py-2">
                  <option value="">Sebep seçin...</option>
                  <option value="content-error">İçerik hatası</option>
                  <option value="wrong-timing">Yanlış zamanlama</option>
                  <option value="duplicate">Tekrar eden içerik</option>
                  <option value="policy-violation">Politika ihlali</option>
                  <option value="client-request">Müşteri talebi</option>
                  <option value="other">Diğer</option>
                </select>
              </div>

              {/* Additional Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ek Notlar (İsteğe bağlı)</label>
                <textarea
                  className="w-full border rounded px-3 py-2 h-20 resize-none"
                  placeholder="Silme işlemi hakkında ek bilgiler..."
                />
              </div>

              {/* Impact Warning */}
              {selectedPost.status === 'published' && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                  <div className="flex items-center">
                    <AlertTriangle className="w-4 h-4 text-orange-600 mr-2" />
                    <p className="text-sm font-medium text-orange-800">Yayınlanmış İçerik</p>
                  </div>
                  <p className="text-xs text-orange-700 mt-1">
                    Bu içerik zaten yayınlanmış. Silme işlemi sadece sistemden kaldıracak,
                    sosyal medya platformundaki içeriği etkilemeyecektir.
                  </p>
                </div>
              )}

              {selectedPost.status === 'scheduled' && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 text-blue-600 mr-2" />
                    <p className="text-sm font-medium text-blue-800">Zamanlanmış İçerik</p>
                  </div>
                  <p className="text-xs text-blue-700 mt-1">
                    Bu içerik zamanlanmış durumda. Silme işlemi zamanlanan yayını iptal edecektir.
                  </p>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowPostDeleteModal(false)}
              >
                İptal
              </Button>
              <Button
                variant="destructive"
                onClick={() => {
                  // Remove from posts array
                  const updatedPosts = posts.filter(p => p.id !== selectedPost.id);
                  setPosts(updatedPosts);
                  showNotification('İçerik başarıyla silindi', 'success');
                  setShowPostDeleteModal(false);
                }}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Sil
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Notification Toast */}
      {notification.show && (
        <div className={`fixed top-4 right-4 z-[10000] p-4 rounded-lg shadow-lg transition-all duration-300 ${
          notification.type === 'success' ? 'bg-green-500 text-white' :
          notification.type === 'error' ? 'bg-red-500 text-white' :
          'bg-blue-500 text-white'
        }`}>
          <div className="flex items-center space-x-2">
            {notification.type === 'success' && <CheckCircle className="w-5 h-5" />}
            {notification.type === 'error' && <XCircle className="w-5 h-5" />}
            {notification.type === 'info' && <Clock className="w-5 h-5" />}
            <span>{notification.message}</span>
            <button
              onClick={() => setNotification({ show: false, message: '', type: 'info' })}
              className="ml-2 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
