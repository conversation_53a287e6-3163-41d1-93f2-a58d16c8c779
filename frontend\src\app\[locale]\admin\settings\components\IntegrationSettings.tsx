'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Plug, CreditCard, MapPin, Bot, AlertTriangle } from 'lucide-react';
import { useSettings } from '../context/SettingsContext';

const IntegrationSettings = () => {
  const { getSettingValue, updateSetting } = useSettings();

  // Integration settings values
  const stripeEnabled = getSettingValue('integration', 'stripeEnabled') || false;
  const googleMapsApiKey = getSettingValue('integration', 'googleMapsApiKey') || '';
  const chatbotEnabled = getSettingValue('integration', 'chatbotEnabled') || false;

  return (
    <div className="space-y-6">
      {/* Payment Integrations */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <CreditCard className="w-5 h-5 text-green-600" />
            <CardTitle>Ödeme Entegrasyonları</CardTitle>
          </div>
          <CardDescription>
            Ödeme sistemleri ve finansal entegrasyonlar
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <CreditCard className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <Label htmlFor="stripeEnabled">Stripe Entegrasyonu</Label>
                <p className="text-sm text-gray-500">Kredi kartı ödemeleri</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="stripeEnabled"
                checked={stripeEnabled}
                onCheckedChange={(checked) => updateSetting('integration', 'stripeEnabled', checked)}
              />
              <Badge variant={stripeEnabled ? "default" : "secondary"}>
                {stripeEnabled ? "Aktif" : "Pasif"}
              </Badge>
            </div>
          </div>

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Ödeme entegrasyonları geliştirilme aşamasında. Şu anda sadece banka havalesi desteklenmektedir.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Maps Integration */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <MapPin className="w-5 h-5 text-red-600" />
            <CardTitle>Harita Entegrasyonu</CardTitle>
          </div>
          <CardDescription>
            Google Maps ve konum servisleri
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="googleMapsApiKey">Google Maps API Anahtarı</Label>
            <Input
              id="googleMapsApiKey"
              type="password"
              value={googleMapsApiKey}
              onChange={(e) => updateSetting('integration', 'googleMapsApiKey', e.target.value)}
              placeholder="AIzaSy..."
            />
            <p className="text-xs text-gray-500">
              Ocak ve fabrika konumları için Google Maps entegrasyonu
            </p>
          </div>

          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-2">Kullanım Alanları:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Üretici ocak konumları</li>
              <li>• Fabrika adresleri</li>
              <li>• Teslimat noktaları</li>
              <li>• Lojistik rotalar</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* AI Integrations */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Bot className="w-5 h-5 text-purple-600" />
            <CardTitle>AI Entegrasyonları</CardTitle>
          </div>
          <CardDescription>
            Yapay zeka servisleri ve chatbot ayarları
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Bot className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <Label htmlFor="chatbotEnabled">AI Chatbot</Label>
                <p className="text-sm text-gray-500">Müşteri destek chatbot'u</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="chatbotEnabled"
                checked={chatbotEnabled}
                onCheckedChange={(checked) => updateSetting('integration', 'chatbotEnabled', checked)}
              />
              <Badge variant={chatbotEnabled ? "default" : "secondary"}>
                {chatbotEnabled ? "Aktif" : "Pasif"}
              </Badge>
            </div>
          </div>

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              AI Chatbot sistemi RFC-601 kapsamında geliştirilme aşamasında.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Integration Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Plug className="w-5 h-5 text-blue-600" />
            <CardTitle>Entegrasyon Durumu</CardTitle>
          </div>
          <CardDescription>
            Aktif entegrasyonların özeti
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-800">Ödeme Sistemleri</h4>
              <p className="text-sm text-green-600 mt-1">
                Banka Havalesi: Aktif
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Stripe: {stripeEnabled ? 'Aktif' : 'Pasif'}
              </p>
            </div>
            
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800">Harita Servisleri</h4>
              <p className="text-sm text-blue-600 mt-1">
                Google Maps: {googleMapsApiKey ? 'Yapılandırılmış' : 'Yapılandırılmamış'}
              </p>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-medium text-purple-800">AI Servisleri</h4>
              <p className="text-sm text-purple-600 mt-1">
                Chatbot: {chatbotEnabled ? 'Aktif' : 'Pasif'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default IntegrationSettings;
