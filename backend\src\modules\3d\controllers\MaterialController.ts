/**
 * Material Controller
 * Handles HTTP requests for material management
 */

import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { MaterialDefinition } from '../types';

const prisma = new PrismaClient();

export class MaterialController {
  /**
   * Get materials for product
   */
  static async getProductMaterials(req: Request, res: Response) {
    try {
      const { productId } = req.params;

      const materials = await prisma.materialDefinition.findMany({
        where: {
          products: {
            some: {
              id: productId
            }
          },
          isActive: true
        },
        include: {
          albedoMap: true,
          normalMap: true,
          roughnessMap: true,
          metallicMap: true,
          emissionMap: true,
          heightMap: true,
          occlusionMap: true
        }
      });

      res.json({
        success: true,
        data: materials
      });
    } catch (error) {
      console.error('Get product materials error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get product materials'
      });
    }
  }

  /**
   * Get material by ID
   */
  static async getMaterial(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const material = await prisma.materialDefinition.findUnique({
        where: { id },
        include: {
          albedoMap: true,
          normalMap: true,
          roughnessMap: true,
          metallicMap: true,
          emissionMap: true,
          heightMap: true,
          occlusionMap: true
        }
      });

      if (!material) {
        return res.status(404).json({
          success: false,
          message: 'Material not found'
        });
      }

      res.json({
        success: true,
        data: material
      });
    } catch (error) {
      console.error('Get material error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get material'
      });
    }
  }

  /**
   * Create material
   */
  static async createMaterial(req: Request, res: Response) {
    try {
      const materialData = req.body;
      const userId = req.user?.id; // Assuming auth middleware

      const material = await prisma.materialDefinition.create({
        data: {
          ...materialData,
          createdBy: userId
        },
        include: {
          albedoMap: true,
          normalMap: true,
          roughnessMap: true,
          metallicMap: true,
          emissionMap: true,
          heightMap: true,
          occlusionMap: true
        }
      });

      res.status(201).json({
        success: true,
        data: material
      });
    } catch (error) {
      console.error('Create material error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create material'
      });
    }
  }

  /**
   * Update material
   */
  static async updateMaterial(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const updates = req.body;

      const material = await prisma.materialDefinition.update({
        where: { id },
        data: {
          ...updates,
          updatedAt: new Date()
        },
        include: {
          albedoMap: true,
          normalMap: true,
          roughnessMap: true,
          metallicMap: true,
          emissionMap: true,
          heightMap: true,
          occlusionMap: true
        }
      });

      res.json({
        success: true,
        data: material
      });
    } catch (error) {
      console.error('Update material error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update material'
      });
    }
  }

  /**
   * Delete material
   */
  static async deleteMaterial(req: Request, res: Response) {
    try {
      const { id } = req.params;

      await prisma.materialDefinition.update({
        where: { id },
        data: {
          isActive: false,
          updatedAt: new Date()
        }
      });

      res.json({
        success: true,
        message: 'Material deleted successfully'
      });
    } catch (error) {
      console.error('Delete material error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete material'
      });
    }
  }

  /**
   * Clone material
   */
  static async cloneMaterial(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { name } = req.body;
      const userId = req.user?.id;

      const originalMaterial = await prisma.materialDefinition.findUnique({
        where: { id }
      });

      if (!originalMaterial) {
        return res.status(404).json({
          success: false,
          message: 'Material not found'
        });
      }

      const { id: _, createdAt, updatedAt, ...materialData } = originalMaterial;

      const clonedMaterial = await prisma.materialDefinition.create({
        data: {
          ...materialData,
          name: name || `${originalMaterial.name} (Copy)`,
          createdBy: userId
        },
        include: {
          albedoMap: true,
          normalMap: true,
          roughnessMap: true,
          metallicMap: true,
          emissionMap: true,
          heightMap: true,
          occlusionMap: true
        }
      });

      res.status(201).json({
        success: true,
        data: clonedMaterial
      });
    } catch (error) {
      console.error('Clone material error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to clone material'
      });
    }
  }

  /**
   * Get material presets
   */
  static async getMaterialPresets(req: Request, res: Response) {
    try {
      const presets = [
        {
          id: 'marble_white',
          name: 'White Marble',
          baseColor: '#f8f8f8',
          metallic: 0.0,
          roughness: 0.1,
          description: 'Classic white marble material'
        },
        {
          id: 'marble_black',
          name: 'Black Marble',
          baseColor: '#2a2a2a',
          metallic: 0.0,
          roughness: 0.15,
          description: 'Elegant black marble material'
        },
        {
          id: 'travertine_beige',
          name: 'Beige Travertine',
          baseColor: '#d4c4a8',
          metallic: 0.0,
          roughness: 0.3,
          description: 'Natural beige travertine material'
        },
        {
          id: 'granite_gray',
          name: 'Gray Granite',
          baseColor: '#6b6b6b',
          metallic: 0.1,
          roughness: 0.2,
          description: 'Polished gray granite material'
        },
        {
          id: 'limestone_cream',
          name: 'Cream Limestone',
          baseColor: '#f5f0e8',
          metallic: 0.0,
          roughness: 0.4,
          description: 'Soft cream limestone material'
        }
      ];

      res.json({
        success: true,
        data: presets
      });
    } catch (error) {
      console.error('Get material presets error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get material presets'
      });
    }
  }

  /**
   * Apply material preset
   */
  static async applyPreset(req: Request, res: Response) {
    try {
      const { presetId, name, productId } = req.body;
      const userId = req.user?.id;

      // Get preset data (in a real app, this would come from a database)
      const presets: Record<string, any> = {
        marble_white: {
          name: 'White Marble',
          baseColor: '#f8f8f8',
          metallic: 0.0,
          roughness: 0.1,
          description: 'Classic white marble material'
        },
        marble_black: {
          name: 'Black Marble',
          baseColor: '#2a2a2a',
          metallic: 0.0,
          roughness: 0.15,
          description: 'Elegant black marble material'
        },
        travertine_beige: {
          name: 'Beige Travertine',
          baseColor: '#d4c4a8',
          metallic: 0.0,
          roughness: 0.3,
          description: 'Natural beige travertine material'
        },
        granite_gray: {
          name: 'Gray Granite',
          baseColor: '#6b6b6b',
          metallic: 0.1,
          roughness: 0.2,
          description: 'Polished gray granite material'
        },
        limestone_cream: {
          name: 'Cream Limestone',
          baseColor: '#f5f0e8',
          metallic: 0.0,
          roughness: 0.4,
          description: 'Soft cream limestone material'
        }
      };

      const presetData = presets[presetId];
      if (!presetData) {
        return res.status(404).json({
          success: false,
          message: 'Preset not found'
        });
      }

      const material = await prisma.materialDefinition.create({
        data: {
          name: name || presetData.name,
          description: presetData.description,
          baseColor: presetData.baseColor,
          metallic: presetData.metallic,
          roughness: presetData.roughness,
          tilingU: 1.0,
          tilingV: 1.0,
          createdBy: userId,
          products: productId ? {
            connect: { id: productId }
          } : undefined
        },
        include: {
          albedoMap: true,
          normalMap: true,
          roughnessMap: true,
          metallicMap: true,
          emissionMap: true,
          heightMap: true,
          occlusionMap: true
        }
      });

      res.status(201).json({
        success: true,
        data: material
      });
    } catch (error) {
      console.error('Apply preset error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to apply preset'
      });
    }
  }
}

export default MaterialController;
