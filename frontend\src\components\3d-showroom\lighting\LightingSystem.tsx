'use client';

import React, { useMemo } from 'react';
import * as THREE from 'three';

export type LightingPreset = 'natural' | 'warm' | 'cool' | 'dramatic' | 'sunset' | 'studio';
export type TimeOfDay = 'morning' | 'noon' | 'afternoon' | 'evening' | 'night';

export interface LightingConfiguration {
  preset: LightingPreset;
  timeOfDay: TimeOfDay;
  intensity: number;
  shadows: boolean;
  ambientIntensity: number;
  directionalIntensity: number;
  spotlightIntensity: number;
  colorTemperature: number; // in Kelvin
}

interface LightingSystemProps {
  configuration: LightingConfiguration;
  roomType: 'living' | 'kitchen' | 'bathroom' | 'bedroom' | 'outdoor';
}

const LIGHTING_PRESETS: Record<LightingPreset, Partial<LightingConfiguration>> = {
  natural: {
    ambientIntensity: 0.4,
    directionalIntensity: 1.0,
    spotlightIntensity: 0,
    colorTemperature: 5500
  },
  warm: {
    ambientIntensity: 0.3,
    directionalIntensity: 0.8,
    spotlightIntensity: 0.5,
    colorTemperature: 3000
  },
  cool: {
    ambientIntensity: 0.5,
    directionalIntensity: 1.2,
    spotlightIntensity: 0,
    colorTemperature: 6500
  },
  dramatic: {
    ambientIntensity: 0.1,
    directionalIntensity: 0.5,
    spotlightIntensity: 1.5,
    colorTemperature: 4000
  },
  sunset: {
    ambientIntensity: 0.2,
    directionalIntensity: 0.6,
    spotlightIntensity: 0.3,
    colorTemperature: 2500
  },
  studio: {
    ambientIntensity: 0.6,
    directionalIntensity: 1.0,
    spotlightIntensity: 0.8,
    colorTemperature: 5000
  }
};

const TIME_OF_DAY_MODIFIERS: Record<TimeOfDay, { intensity: number; angle: number; color: string }> = {
  morning: { intensity: 0.8, angle: 30, color: '#fff4e6' },
  noon: { intensity: 1.0, angle: 90, color: '#ffffff' },
  afternoon: { intensity: 0.9, angle: 60, color: '#fff8f0' },
  evening: { intensity: 0.6, angle: 15, color: '#ffe4b3' },
  night: { intensity: 0.3, angle: 0, color: '#e6f3ff' }
};

function kelvinToRGB(kelvin: number): THREE.Color {
  const temp = kelvin / 100;
  let red, green, blue;

  if (temp <= 66) {
    red = 255;
    green = temp;
    green = 99.4708025861 * Math.log(green) - 161.1195681661;
    
    if (temp >= 19) {
      blue = temp - 10;
      blue = 138.5177312231 * Math.log(blue) - 305.0447927307;
    } else {
      blue = 0;
    }
  } else {
    red = temp - 60;
    red = 329.698727446 * Math.pow(red, -0.1332047592);
    
    green = temp - 60;
    green = 288.1221695283 * Math.pow(green, -0.0755148492);
    
    blue = 255;
  }

  return new THREE.Color(
    Math.max(0, Math.min(255, red)) / 255,
    Math.max(0, Math.min(255, green)) / 255,
    Math.max(0, Math.min(255, blue)) / 255
  );
}

export const LightingSystem: React.FC<LightingSystemProps> = ({
  configuration,
  roomType
}) => {
  const preset = LIGHTING_PRESETS[configuration.preset];
  const timeModifier = TIME_OF_DAY_MODIFIERS[configuration.timeOfDay];
  
  // Calculate final lighting values
  const finalConfig = useMemo(() => {
    const baseIntensity = configuration.intensity;
    const timeIntensity = timeModifier.intensity;
    
    return {
      ambientIntensity: (preset.ambientIntensity || 0.4) * baseIntensity * timeIntensity,
      directionalIntensity: (preset.directionalIntensity || 1.0) * baseIntensity * timeIntensity,
      spotlightIntensity: (preset.spotlightIntensity || 0) * baseIntensity * timeIntensity,
      lightColor: kelvinToRGB(preset.colorTemperature || 5500),
      timeColor: new THREE.Color(timeModifier.color),
      sunAngle: timeModifier.angle
    };
  }, [configuration, preset, timeModifier]);

  // Calculate sun position based on time of day
  const sunPosition = useMemo(() => {
    const angle = (finalConfig.sunAngle * Math.PI) / 180;
    const distance = 10;
    
    return [
      Math.cos(angle) * distance,
      Math.sin(angle) * distance,
      5
    ] as [number, number, number];
  }, [finalConfig.sunAngle]);

  // Room-specific lighting adjustments
  const roomLightingAdjustments = useMemo(() => {
    switch (roomType) {
      case 'kitchen':
        return {
          additionalSpots: [
            { position: [-2, 3, 0], intensity: 0.5 },
            { position: [2, 3, 0], intensity: 0.5 }
          ],
          ambientBoost: 1.2
        };
      case 'bathroom':
        return {
          additionalSpots: [
            { position: [0, 2.5, 1], intensity: 0.8 }
          ],
          ambientBoost: 1.1
        };
      case 'bedroom':
        return {
          additionalSpots: [
            { position: [-1.5, 2, -1.5], intensity: 0.3 }
          ],
          ambientBoost: 0.9
        };
      case 'outdoor':
        return {
          additionalSpots: [],
          ambientBoost: 1.3
        };
      default: // living
        return {
          additionalSpots: [
            { position: [0, 3, 0], intensity: 0.4 }
          ],
          ambientBoost: 1.0
        };
    }
  }, [roomType]);

  return (
    <group>
      {/* Ambient Light */}
      <ambientLight 
        intensity={finalConfig.ambientIntensity * roomLightingAdjustments.ambientBoost}
        color={finalConfig.timeColor}
      />

      {/* Main Directional Light (Sun) */}
      <directionalLight
        position={sunPosition}
        intensity={finalConfig.directionalIntensity}
        color={finalConfig.lightColor}
        castShadow={configuration.shadows}
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />

      {/* Dramatic Spotlight */}
      {configuration.preset === 'dramatic' && (
        <spotLight
          position={[0, 8, 0]}
          angle={0.4}
          penumbra={1}
          intensity={finalConfig.spotlightIntensity}
          color={finalConfig.lightColor}
          castShadow={configuration.shadows}
        />
      )}

      {/* Room-specific Additional Lights */}
      {roomLightingAdjustments.additionalSpots.map((spot, index) => (
        <spotLight
          key={`room-light-${index}`}
          position={spot.position as [number, number, number]}
          angle={0.3}
          penumbra={0.5}
          intensity={spot.intensity * finalConfig.directionalIntensity}
          color={finalConfig.lightColor}
          castShadow={false}
        />
      ))}

      {/* Sunset/Evening Fill Light */}
      {(configuration.timeOfDay === 'evening' || configuration.timeOfDay === 'sunset') && (
        <pointLight
          position={[-5, 2, -5]}
          intensity={0.3 * finalConfig.directionalIntensity}
          color="#ff8c42"
          distance={15}
          decay={2}
        />
      )}

      {/* Night Time Artificial Lights */}
      {configuration.timeOfDay === 'night' && (
        <>
          <pointLight
            position={[0, 2.5, 0]}
            intensity={0.8}
            color="#fff2cc"
            distance={8}
            decay={2}
          />
          <pointLight
            position={[3, 2, 3]}
            intensity={0.4}
            color="#fff2cc"
            distance={6}
            decay={2}
          />
        </>
      )}
    </group>
  );
};

export default LightingSystem;
