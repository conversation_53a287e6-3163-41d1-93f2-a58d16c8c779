/**
 * Database Escalation Manager
 * Handles escalation logic with database persistence
 */

import { PrismaClient } from '@prisma/client';
import { 
  ConversationContext, 
  EscalationCriteria, 
  EscalationResult, 
  Agent, 
  ChatbotIntent 
} from '../types';

export class DatabaseEscalationManager {
  private prisma: PrismaClient;
  private escalationCriteria: EscalationCriteria;

  constructor() {
    this.prisma = new PrismaClient();
    this.escalationCriteria = this.getDefaultEscalationCriteria();
  }

  /**
   * Check if conversation should be escalated
   */
  shouldEscalate(context: ConversationContext): boolean {
    const criteria = this.escalationCriteria;
    
    // Check confidence level
    if (context.confidence < criteria.lowConfidenceThreshold) {
      return true;
    }
    
    // Check intent-based rules
    if (criteria.alwaysEscalate.includes(context.currentIntent)) {
      return true;
    }
    
    // Never escalate certain intents
    if (criteria.neverEscalate.includes(context.currentIntent)) {
      return false;
    }
    
    // Check sentiment
    if (context.sentiment < criteria.negativeSentimentThreshold) {
      return true;
    }
    
    // Check multiple unresolved issues
    if (context.unresolvedIssues.length >= criteria.multipleUnresolvedIssues) {
      return true;
    }
    
    // Check conversation length
    if (context.conversationHistory.length >= criteria.conversationLength && 
        context.resolvedIssues.length === 0) {
      return true;
    }
    
    // Check for repeated questions (frustration indicator)
    if (this.detectRepeatedQuestions(context) >= criteria.repeatedQuestions) {
      return true;
    }
    
    // Check if VIP customer
    if (context.userId && criteria.vipCustomers.includes(context.userId)) {
      return true;
    }
    
    return false;
  }

  /**
   * Escalate conversation to human agent
   */
  async escalateToHuman(context: ConversationContext): Promise<EscalationResult> {
    try {
      // Find available agent
      const agent = await this.findAvailableAgent(context.language, this.getRequiredExpertise(context));
      
      if (!agent) {
        return await this.scheduleCallback(context);
      }
      
      // Update conversation in database
      await this.prisma.chatbotConversation.update({
        where: { sessionId: context.sessionId },
        data: {
          escalationLevel: 1,
          escalationReason: this.getEscalationReason(context),
          humanAgentId: agent.id,
          escalatedAt: new Date(),
          status: 'ESCALATED'
        }
      });

      // Update agent load
      await this.prisma.chatbotAgent.update({
        where: { id: agent.id },
        data: {
          currentLoad: {
            increment: 1
          }
        }
      });
      
      // Log escalation
      await this.logEscalation(context, agent.id, this.getEscalationReason(context));
      
      return {
        escalated: true,
        agentId: agent.id,
        estimatedWaitTime: this.calculateWaitTime(agent),
        reason: this.getEscalationReason(context)
      };
    } catch (error) {
      console.error('Escalation error:', error);
      return {
        escalated: false,
        reason: 'System error during escalation'
      };
    }
  }

  /**
   * Find available agent based on language and expertise
   */
  private async findAvailableAgent(language: string, expertise: string[]): Promise<Agent | null> {
    try {
      // Find agents that support the language and are available
      const agents = await this.prisma.chatbotAgent.findMany({
        where: {
          status: 'AVAILABLE',
          isActive: true,
          languages: {
            has: language
          },
          currentLoad: {
            lt: this.prisma.chatbotAgent.fields.maxConcurrentChats
          }
        },
        include: {
          user: true
        }
      });

      if (agents.length === 0) {
        // Fallback to English-speaking agents
        const englishAgents = await this.prisma.chatbotAgent.findMany({
          where: {
            status: 'AVAILABLE',
            isActive: true,
            languages: {
              has: 'en'
            },
            currentLoad: {
              lt: this.prisma.chatbotAgent.fields.maxConcurrentChats
            }
          },
          include: {
            user: true
          }
        });
        
        if (englishAgents.length === 0) {
          return null;
        }
        
        return this.selectBestAgent(englishAgents, expertise);
      }
      
      return this.selectBestAgent(agents, expertise);
    } catch (error) {
      console.error('Error finding available agent:', error);
      return null;
    }
  }

  /**
   * Select best agent based on expertise and current load
   */
  private selectBestAgent(agents: any[], requiredExpertise: string[]): Agent {
    // Score agents based on expertise match and current load
    const scoredAgents = agents.map(agent => {
      const expertiseScore = requiredExpertise.reduce((score, exp) => {
        return score + (agent.expertise.includes(exp) ? 1 : 0);
      }, 0);
      
      const loadScore = Math.max(0, agent.maxConcurrentChats - agent.currentLoad);
      const totalScore = expertiseScore * 2 + loadScore; // Expertise weighted more
      
      return { 
        agent: {
          id: agent.id,
          name: agent.name,
          languages: agent.languages,
          expertise: agent.expertise,
          currentLoad: agent.currentLoad,
          available: agent.status === 'AVAILABLE'
        }, 
        score: totalScore 
      };
    });
    
    // Sort by score (highest first) and return best agent
    scoredAgents.sort((a, b) => b.score - a.score);
    return scoredAgents[0].agent;
  }

  /**
   * Schedule callback when no agents available
   */
  private async scheduleCallback(context: ConversationContext): Promise<EscalationResult> {
    const estimatedWaitTime = await this.calculateCallbackWaitTime();
    
    // Update conversation status
    await this.prisma.chatbotConversation.update({
      where: { sessionId: context.sessionId },
      data: {
        escalationLevel: 1,
        escalationReason: 'No agents available - callback scheduled',
        status: 'ESCALATED'
      }
    });
    
    await this.logEscalation(context, null, 'No agents available - callback scheduled');
    
    return {
      escalated: true,
      estimatedWaitTime,
      reason: 'No agents currently available. A callback has been scheduled.'
    };
  }

  /**
   * Calculate wait time based on agent load
   */
  private calculateWaitTime(agent: Agent): number {
    // Base wait time of 5 minutes per conversation in queue
    return agent.currentLoad * 5;
  }

  /**
   * Calculate callback wait time
   */
  private async calculateCallbackWaitTime(): Promise<number> {
    try {
      const agents = await this.prisma.chatbotAgent.findMany({
        where: { isActive: true }
      });
      
      const totalLoad = agents.reduce((sum, agent) => sum + agent.currentLoad, 0);
      const averageLoad = agents.length > 0 ? totalLoad / agents.length : 0;
      
      // Base callback time of 30 minutes + load factor
      return 30 + (averageLoad * 10);
    } catch (error) {
      console.error('Error calculating callback wait time:', error);
      return 60; // Default 1 hour
    }
  }

  /**
   * Get required expertise based on conversation context
   */
  private getRequiredExpertise(context: ConversationContext): string[] {
    const expertise: string[] = [];
    
    switch (context.currentIntent) {
      case ChatbotIntent.PRODUCT_INQUIRY:
      case ChatbotIntent.PRODUCT_SPECIFICATIONS:
      case ChatbotIntent.PRODUCT_PRICING:
        expertise.push('product_specialist');
        break;
      
      case ChatbotIntent.ORDER_STATUS:
      case ChatbotIntent.ORDER_TRACKING:
      case ChatbotIntent.ORDER_MODIFICATION:
        expertise.push('order_management');
        break;
      
      case ChatbotIntent.BID_PROCESS:
      case ChatbotIntent.BID_STATUS:
        expertise.push('bidding_specialist');
        break;
      
      case ChatbotIntent.PAYMENT_METHODS:
      case ChatbotIntent.PAYMENT_STATUS:
      case ChatbotIntent.REFUND_REQUEST:
        expertise.push('payment_specialist');
        break;
      
      case ChatbotIntent.TECHNICAL_ISSUE:
        expertise.push('technical_support');
        break;
      
      case ChatbotIntent.COMPLAINT:
        expertise.push('customer_relations');
        break;
      
      default:
        expertise.push('general_support');
    }
    
    // Add user type specific expertise
    if (context.userProfile?.type === 'producer') {
      expertise.push('producer_support');
    } else if (context.userProfile?.type === 'customer') {
      expertise.push('customer_support');
    }
    
    return expertise;
  }

  /**
   * Get escalation reason
   */
  private getEscalationReason(context: ConversationContext): string {
    if (context.confidence < this.escalationCriteria.lowConfidenceThreshold) {
      return 'Low confidence in AI response';
    }
    
    if (context.sentiment < this.escalationCriteria.negativeSentimentThreshold) {
      return 'Negative customer sentiment detected';
    }
    
    if (context.unresolvedIssues.length >= this.escalationCriteria.multipleUnresolvedIssues) {
      return 'Multiple unresolved issues';
    }
    
    if (context.currentIntent === ChatbotIntent.HUMAN_HANDOFF) {
      return 'Customer requested human agent';
    }
    
    if (context.currentIntent === ChatbotIntent.COMPLAINT) {
      return 'Customer complaint requires human attention';
    }
    
    if (context.conversationHistory.length >= this.escalationCriteria.conversationLength) {
      return 'Long conversation without resolution';
    }
    
    return 'Escalation criteria met';
  }

  /**
   * Detect repeated questions (frustration indicator)
   */
  private detectRepeatedQuestions(context: ConversationContext): number {
    const userMessages = context.conversationHistory
      .filter(msg => msg.role === 'user')
      .map(msg => msg.content.toLowerCase());
    
    if (userMessages.length < 2) return 0;
    
    let repeatedCount = 0;
    const recentMessages = userMessages.slice(-5); // Check last 5 user messages
    
    for (let i = 0; i < recentMessages.length - 1; i++) {
      for (let j = i + 1; j < recentMessages.length; j++) {
        if (this.calculateSimilarity(recentMessages[i], recentMessages[j]) > 0.7) {
          repeatedCount++;
        }
      }
    }
    
    return repeatedCount;
  }

  /**
   * Calculate similarity between two strings
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const words1 = str1.split(' ');
    const words2 = str2.split(' ');
    
    const commonWords = words1.filter(word => words2.includes(word));
    const totalWords = new Set([...words1, ...words2]).size;
    
    return commonWords.length / totalWords;
  }

  /**
   * Log escalation for analytics
   */
  private async logEscalation(
    context: ConversationContext, 
    agentId: string | null, 
    reason: string
  ): Promise<void> {
    try {
      // This could be expanded to store in a separate escalation log table
      console.log('Escalation logged:', {
        sessionId: context.sessionId,
        userId: context.userId,
        agentId,
        reason,
        timestamp: new Date(),
        conversationLength: context.conversationHistory.length,
        confidence: context.confidence,
        sentiment: context.sentiment
      });
    } catch (error) {
      console.error('Error logging escalation:', error);
    }
  }

  /**
   * Get default escalation criteria
   */
  private getDefaultEscalationCriteria(): EscalationCriteria {
    return {
      lowConfidenceThreshold: 0.3,
      mediumConfidenceThreshold: 0.7,
      alwaysEscalate: [
        ChatbotIntent.HUMAN_HANDOFF,
        ChatbotIntent.COMPLAINT,
        ChatbotIntent.REFUND_REQUEST
      ],
      neverEscalate: [
        ChatbotIntent.GREETING,
        ChatbotIntent.GOODBYE
      ],
      negativeSentimentThreshold: -0.5,
      multipleUnresolvedIssues: 3,
      conversationLength: 15,
      repeatedQuestions: 2,
      vipCustomers: [], // Would be populated from database
      highValueOrders: 10000
    };
  }

  /**
   * Update escalation criteria
   */
  updateEscalationCriteria(criteria: Partial<EscalationCriteria>): void {
    this.escalationCriteria = { ...this.escalationCriteria, ...criteria };
  }

  /**
   * Get current escalation criteria
   */
  getEscalationCriteria(): EscalationCriteria {
    return this.escalationCriteria;
  }

  /**
   * Get available agents from database
   */
  async getAvailableAgents(): Promise<Agent[]> {
    try {
      const agents = await this.prisma.chatbotAgent.findMany({
        where: {
          isActive: true,
          status: 'AVAILABLE'
        },
        include: {
          user: true
        }
      });

      return agents.map(agent => ({
        id: agent.id,
        name: agent.name,
        languages: agent.languages,
        expertise: agent.expertise,
        currentLoad: agent.currentLoad,
        available: agent.status === 'AVAILABLE'
      }));
    } catch (error) {
      console.error('Error getting available agents:', error);
      return [];
    }
  }

  /**
   * Update agent availability
   */
  async updateAgentAvailability(agentId: string, available: boolean): Promise<boolean> {
    try {
      await this.prisma.chatbotAgent.update({
        where: { id: agentId },
        data: {
          status: available ? 'AVAILABLE' : 'OFFLINE',
          currentLoad: available ? undefined : 0 // Reset load when going offline
        }
      });
      return true;
    } catch (error) {
      console.error('Error updating agent availability:', error);
      return false;
    }
  }

  /**
   * Update agent load
   */
  async updateAgentLoad(agentId: string, loadChange: number): Promise<boolean> {
    try {
      await this.prisma.chatbotAgent.update({
        where: { id: agentId },
        data: {
          currentLoad: {
            increment: loadChange
          }
        }
      });
      return true;
    } catch (error) {
      console.error('Error updating agent load:', error);
      return false;
    }
  }

  /**
   * Get all agents (for admin)
   */
  async getAllAgents(): Promise<Agent[]> {
    try {
      const agents = await this.prisma.chatbotAgent.findMany({
        include: {
          user: true
        }
      });

      return agents.map(agent => ({
        id: agent.id,
        name: agent.name,
        languages: agent.languages,
        expertise: agent.expertise,
        currentLoad: agent.currentLoad,
        available: agent.status === 'AVAILABLE'
      }));
    } catch (error) {
      console.error('Error getting all agents:', error);
      return [];
    }
  }

  /**
   * Create new agent
   */
  async createAgent(agentData: {
    userId: string;
    name: string;
    email: string;
    languages: string[];
    expertise: string[];
    maxConcurrentChats?: number;
  }): Promise<Agent | null> {
    try {
      const agent = await this.prisma.chatbotAgent.create({
        data: {
          userId: agentData.userId,
          name: agentData.name,
          email: agentData.email,
          languages: agentData.languages,
          expertise: agentData.expertise,
          maxConcurrentChats: agentData.maxConcurrentChats || 5,
          status: 'OFFLINE'
        },
        include: {
          user: true
        }
      });

      return {
        id: agent.id,
        name: agent.name,
        languages: agent.languages,
        expertise: agent.expertise,
        currentLoad: agent.currentLoad,
        available: agent.status === 'AVAILABLE'
      };
    } catch (error) {
      console.error('Error creating agent:', error);
      return null;
    }
  }

  /**
   * Cleanup - close database connection
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
