'use client';

import React, { useRef, useEffect, useState } from 'react';
import { use<PERSON>rame, useLoader } from '@react-three/fiber';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import { useGLTF, useFBX } from '@react-three/drei';
import * as THREE from 'three';
import { 
  Asset3D, 
  AssetQuality, 
  MaterialDefinition, 
  ViewerError, 
  LoadingProgress 
} from '../../types/3d';
import { threeDApi } from '../../services/3dApi';

interface ModelLoaderProps {
  asset: Asset3D;
  quality: AssetQuality;
  wireframe?: boolean;
  material?: string;
  materials?: MaterialDefinition[];
  onLoad?: () => void;
  onError?: (error: ViewerError) => void;
  onProgress?: (progress: LoadingProgress) => void;
}

export const ModelLoader: React.FC<ModelLoaderProps> = ({
  asset,
  quality,
  wireframe = false,
  material,
  materials = [],
  onLoad,
  onError,
  onProgress
}) => {
  const meshRef = useRef<THREE.Group>(null);
  const [model, setModel] = useState<THREE.Group | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get the appropriate asset variant based on quality
  const getAssetVariant = () => {
    if (!asset.variants || asset.variants.length === 0) {
      return asset;
    }

    const variant = asset.variants.find(v => v.quality === quality);
    return variant || asset.variants[0] || asset;
  };

  const currentAsset = getAssetVariant();
  const modelUrl = threeDApi.getAssetUrl(currentAsset.id || asset.id, quality);

  // Load model based on format
  const loadModel = async () => {
    try {
      setIsLoading(true);
      onProgress?.({
        stage: 'downloading',
        progress: 0,
        message: 'Downloading model...'
      });

      let loadedModel: THREE.Group;

      if (asset.format === 'GLB' || asset.format === 'GLTF') {
        loadedModel = await loadGLTF(modelUrl);
      } else if (asset.format === 'FBX') {
        loadedModel = await loadFBXModel(modelUrl);
      } else {
        throw new Error(`Unsupported model format: ${asset.format}`);
      }

      // Apply materials if specified
      if (material && materials.length > 0) {
        applyMaterial(loadedModel, material);
      }

      // Apply wireframe if enabled
      if (wireframe) {
        applyWireframe(loadedModel);
      }

      setModel(loadedModel);
      setIsLoading(false);
      onLoad?.();

    } catch (error) {
      console.error('Error loading model:', error);
      setIsLoading(false);
      onError?.({
        code: 'MODEL_LOAD_ERROR',
        message: error instanceof Error ? error.message : 'Failed to load 3D model',
        details: error,
        recoverable: true
      });
    }
  };

  // Load GLTF/GLB model
  const loadGLTF = async (url: string): Promise<THREE.Group> => {
    return new Promise((resolve, reject) => {
      const loader = new GLTFLoader();
      
      // Setup Draco decoder for compressed models
      const dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath('/draco/');
      loader.setDRACOLoader(dracoLoader);

      loader.load(
        url,
        (gltf) => {
          onProgress?.({
            stage: 'processing',
            progress: 100,
            message: 'Processing model...'
          });
          resolve(gltf.scene);
        },
        (progress) => {
          const percent = progress.total > 0 ? (progress.loaded / progress.total) * 100 : 0;
          onProgress?.({
            stage: 'downloading',
            progress: percent,
            message: 'Downloading model...',
            bytesLoaded: progress.loaded,
            bytesTotal: progress.total
          });
        },
        (error) => {
          reject(error);
        }
      );
    });
  };

  // Load FBX model
  const loadFBXModel = async (url: string): Promise<THREE.Group> => {
    return new Promise((resolve, reject) => {
      const loader = new THREE.FBXLoader();
      
      loader.load(
        url,
        (fbx) => {
          onProgress?.({
            stage: 'processing',
            progress: 100,
            message: 'Processing model...'
          });
          resolve(fbx);
        },
        (progress) => {
          const percent = progress.total > 0 ? (progress.loaded / progress.total) * 100 : 0;
          onProgress?.({
            stage: 'downloading',
            progress: percent,
            message: 'Downloading model...',
            bytesLoaded: progress.loaded,
            bytesTotal: progress.total
          });
        },
        (error) => {
          reject(error);
        }
      );
    });
  };

  // Apply material to model
  const applyMaterial = (model: THREE.Group, materialId: string) => {
    const materialDef = materials.find(m => m.id === materialId);
    if (!materialDef) return;

    model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        const material = createMaterialFromDefinition(materialDef);
        child.material = material;
      }
    });
  };

  // Create Three.js material from material definition
  const createMaterialFromDefinition = (materialDef: MaterialDefinition): THREE.Material => {
    const material = new THREE.MeshStandardMaterial({
      color: materialDef.baseColor || '#ffffff',
      metalness: materialDef.metallic || 0,
      roughness: materialDef.roughness || 0.5,
      emissive: materialDef.emission || '#000000',
      emissiveIntensity: materialDef.emissionIntensity || 0
    });

    // Load textures if available
    if (materialDef.albedoMapId) {
      const textureUrl = threeDApi.getTextureUrl(materialDef.albedoMapId);
      const texture = new THREE.TextureLoader().load(textureUrl);
      texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
      texture.repeat.set(materialDef.tilingU || 1, materialDef.tilingV || 1);
      material.map = texture;
    }

    if (materialDef.normalMapId) {
      const textureUrl = threeDApi.getTextureUrl(materialDef.normalMapId);
      const normalMap = new THREE.TextureLoader().load(textureUrl);
      normalMap.wrapS = normalMap.wrapT = THREE.RepeatWrapping;
      normalMap.repeat.set(materialDef.tilingU || 1, materialDef.tilingV || 1);
      material.normalMap = normalMap;
    }

    if (materialDef.roughnessMapId) {
      const textureUrl = threeDApi.getTextureUrl(materialDef.roughnessMapId);
      const roughnessMap = new THREE.TextureLoader().load(textureUrl);
      roughnessMap.wrapS = roughnessMap.wrapT = THREE.RepeatWrapping;
      roughnessMap.repeat.set(materialDef.tilingU || 1, materialDef.tilingV || 1);
      material.roughnessMap = roughnessMap;
    }

    if (materialDef.metallicMapId) {
      const textureUrl = threeDApi.getTextureUrl(materialDef.metallicMapId);
      const metalnessMap = new THREE.TextureLoader().load(textureUrl);
      metalnessMap.wrapS = metalnessMap.wrapT = THREE.RepeatWrapping;
      metalnessMap.repeat.set(materialDef.tilingU || 1, materialDef.tilingV || 1);
      material.metalnessMap = metalnessMap;
    }

    return material;
  };

  // Apply wireframe to model
  const applyWireframe = (model: THREE.Group) => {
    model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach(mat => {
            if (mat instanceof THREE.Material) {
              mat.wireframe = true;
            }
          });
        } else if (child.material instanceof THREE.Material) {
          child.material.wireframe = true;
        }
      }
    });
  };

  // Load model on mount and when dependencies change
  useEffect(() => {
    loadModel();
  }, [asset.id, quality]);

  // Update wireframe when wireframe prop changes
  useEffect(() => {
    if (model) {
      model.traverse((child) => {
        if (child instanceof THREE.Mesh && child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(mat => {
              if (mat instanceof THREE.Material) {
                mat.wireframe = wireframe;
              }
            });
          } else if (child.material instanceof THREE.Material) {
            child.material.wireframe = wireframe;
          }
        }
      });
    }
  }, [wireframe, model]);

  // Update material when material prop changes
  useEffect(() => {
    if (model && material) {
      applyMaterial(model, material);
    }
  }, [material, model]);

  // Auto-rotation animation
  useFrame((state, delta) => {
    if (meshRef.current) {
      // Optional: Add subtle animations or interactions here
    }
  });

  if (isLoading || !model) {
    return null;
  }

  return (
    <group ref={meshRef}>
      <primitive object={model} />
    </group>
  );
};

export default ModelLoader;
