#!/bin/bash

# Local CI/CD Pipeline Script
# Türkiye Doğal Taş Pazaryeri - Continuous Integration/Deployment

set -e  # Exit on any error

# Configuration
PROJECT_NAME="natural-stone-marketplace"
BACKUP_DIR="./backups"
LOG_DIR="./logs"
DEPLOY_ENV=${1:-"staging"}  # staging or production
BRANCH=${2:-"main"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Create log directory
mkdir -p "$LOG_DIR"
PIPELINE_LOG="$LOG_DIR/pipeline-$(date +%Y%m%d-%H%M%S).log"

# Redirect all output to log file as well
exec > >(tee -a "$PIPELINE_LOG")
exec 2>&1

log_info "Starting CI/CD Pipeline for $PROJECT_NAME"
log_info "Environment: $DEPLOY_ENV"
log_info "Branch: $BRANCH"
log_info "Log file: $PIPELINE_LOG"

# Step 1: Pre-deployment checks
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if required tools are installed
    local tools=("node" "npm" "docker" "docker-compose")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is not installed"
            return 1
        fi
    done
    
    # Check if environment files exist
    if [[ ! -f ".env.$DEPLOY_ENV" ]]; then
        log_error "Environment file .env.$DEPLOY_ENV not found"
        return 1
    fi
    
    log_success "Prerequisites check passed"
}

# Step 2: Code quality checks
run_code_quality_checks() {
    log_info "Running code quality checks..."
    
    # Backend linting and type checking
    log_info "Checking backend code quality..."
    cd backend
    
    # Install dependencies if needed
    if [[ ! -d "node_modules" ]]; then
        npm ci
    fi
    
    # Run linting
    if npm run lint; then
        log_success "Backend linting passed"
    else
        log_error "Backend linting failed"
        return 1
    fi
    
    # Run type checking
    if npx tsc --noEmit; then
        log_success "Backend type checking passed"
    else
        log_error "Backend type checking failed"
        return 1
    fi
    
    cd ..
    
    # Frontend linting and type checking
    log_info "Checking frontend code quality..."
    cd frontend
    
    # Install dependencies if needed
    if [[ ! -d "node_modules" ]]; then
        npm ci
    fi
    
    # Run linting
    if npm run lint; then
        log_success "Frontend linting passed"
    else
        log_error "Frontend linting failed"
        return 1
    fi
    
    # Run type checking
    if npx tsc --noEmit; then
        log_success "Frontend type checking passed"
    else
        log_error "Frontend type checking failed"
        return 1
    fi
    
    cd ..
    
    log_success "Code quality checks completed"
}

# Step 3: Run tests
run_tests() {
    log_info "Running tests..."
    
    # Backend tests
    log_info "Running backend tests..."
    cd backend
    
    if npm test; then
        log_success "Backend tests passed"
    else
        log_error "Backend tests failed"
        return 1
    fi
    
    cd ..
    
    # Frontend tests
    log_info "Running frontend tests..."
    cd frontend
    
    if npm test -- --watchAll=false; then
        log_success "Frontend tests passed"
    else
        log_error "Frontend tests failed"
        return 1
    fi
    
    cd ..
    
    log_success "All tests passed"
}

# Step 4: Security scan
run_security_scan() {
    log_info "Running security scan..."
    
    # Backend security audit
    cd backend
    if npm audit --audit-level=high; then
        log_success "Backend security audit passed"
    else
        log_warning "Backend security audit found issues"
    fi
    cd ..
    
    # Frontend security audit
    cd frontend
    if npm audit --audit-level=high; then
        log_success "Frontend security audit passed"
    else
        log_warning "Frontend security audit found issues"
    fi
    cd ..
    
    log_success "Security scan completed"
}

# Step 5: Build applications
build_applications() {
    log_info "Building applications..."
    
    # Build backend
    log_info "Building backend..."
    cd backend
    if node build-production.js; then
        log_success "Backend build completed"
    else
        log_error "Backend build failed"
        return 1
    fi
    cd ..
    
    # Build frontend
    log_info "Building frontend..."
    cd frontend
    if node build-production.js; then
        log_success "Frontend build completed"
    else
        log_error "Frontend build failed"
        return 1
    fi
    cd ..
    
    log_success "Application builds completed"
}

# Step 6: Database migration (if needed)
run_database_migration() {
    log_info "Checking database migration..."
    
    cd backend
    
    # Check if migration is needed
    if npx prisma migrate status | grep -q "Database is up to date"; then
        log_info "Database is up to date, no migration needed"
    else
        log_info "Running database migration..."
        if npx prisma migrate deploy; then
            log_success "Database migration completed"
        else
            log_error "Database migration failed"
            return 1
        fi
    fi
    
    cd ..
}

# Step 7: Create backup before deployment
create_backup() {
    log_info "Creating backup before deployment..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Database backup
    cd backend
    if node scripts/backup-database.js; then
        log_success "Database backup created"
    else
        log_warning "Database backup failed"
    fi
    cd ..
    
    # Code backup
    BACKUP_FILE="$BACKUP_DIR/code-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    if tar -czf "$BACKUP_FILE" --exclude=node_modules --exclude=.git --exclude=dist --exclude=.next .; then
        log_success "Code backup created: $BACKUP_FILE"
    else
        log_warning "Code backup failed"
    fi
}

# Step 8: Deploy to environment
deploy_to_environment() {
    log_info "Deploying to $DEPLOY_ENV environment..."
    
    if [[ "$DEPLOY_ENV" == "production" ]]; then
        # Production deployment
        if ./scripts/deploy-production.sh; then
            log_success "Production deployment completed"
        else
            log_error "Production deployment failed"
            return 1
        fi
    else
        # Staging deployment
        if docker-compose -f docker-compose.staging.yml up -d --build; then
            log_success "Staging deployment completed"
        else
            log_error "Staging deployment failed"
            return 1
        fi
    fi
}

# Step 9: Post-deployment tests
run_post_deployment_tests() {
    log_info "Running post-deployment tests..."
    
    # Wait for services to be ready
    sleep 30
    
    # Health check
    local health_url="http://localhost:8000/health"
    if [[ "$DEPLOY_ENV" == "production" ]]; then
        health_url="https://yourdomain.com/api/health"
    fi
    
    if curl -f "$health_url" > /dev/null 2>&1; then
        log_success "Health check passed"
    else
        log_error "Health check failed"
        return 1
    fi
    
    # Basic functionality tests
    log_info "Running basic functionality tests..."
    
    # Test API endpoints
    local api_url="http://localhost:8000/api"
    if [[ "$DEPLOY_ENV" == "production" ]]; then
        api_url="https://yourdomain.com/api"
    fi
    
    # Test categories endpoint
    if curl -f "$api_url/categories" > /dev/null 2>&1; then
        log_success "Categories API test passed"
    else
        log_warning "Categories API test failed"
    fi
    
    log_success "Post-deployment tests completed"
}

# Step 10: Send notifications
send_notifications() {
    log_info "Sending deployment notifications..."
    
    local status="SUCCESS"
    local message="Deployment to $DEPLOY_ENV completed successfully"
    
    # Create notification payload
    local notification_data="{
        \"environment\": \"$DEPLOY_ENV\",
        \"status\": \"$status\",
        \"message\": \"$message\",
        \"timestamp\": \"$(date -Iseconds)\",
        \"branch\": \"$BRANCH\",
        \"log_file\": \"$PIPELINE_LOG\"
    }"
    
    # Save notification to file (can be picked up by notification service)
    echo "$notification_data" > "$LOG_DIR/deployment-notification.json"
    
    log_success "Deployment notification created"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    
    # Clean up old log files (keep last 10)
    find "$LOG_DIR" -name "pipeline-*.log" -type f | sort -r | tail -n +11 | xargs rm -f
    
    # Clean up old backup files (keep last 5)
    find "$BACKUP_DIR" -name "code-backup-*.tar.gz" -type f | sort -r | tail -n +6 | xargs rm -f
    
    log_success "Cleanup completed"
}

# Error handling
handle_error() {
    local exit_code=$?
    log_error "Pipeline failed with exit code $exit_code"
    
    # Send failure notification
    local notification_data="{
        \"environment\": \"$DEPLOY_ENV\",
        \"status\": \"FAILED\",
        \"message\": \"Deployment to $DEPLOY_ENV failed\",
        \"timestamp\": \"$(date -Iseconds)\",
        \"branch\": \"$BRANCH\",
        \"log_file\": \"$PIPELINE_LOG\",
        \"exit_code\": $exit_code
    }"
    
    echo "$notification_data" > "$LOG_DIR/deployment-failure.json"
    
    exit $exit_code
}

# Set error trap
trap handle_error ERR

# Main pipeline execution
main() {
    log_info "=== CI/CD Pipeline Started ==="
    
    check_prerequisites
    run_code_quality_checks
    run_tests
    run_security_scan
    build_applications
    create_backup
    run_database_migration
    deploy_to_environment
    run_post_deployment_tests
    send_notifications
    cleanup
    
    log_success "=== CI/CD Pipeline Completed Successfully ==="
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [environment] [branch]"
        echo ""
        echo "Arguments:"
        echo "  environment    Deployment environment (staging|production) [default: staging]"
        echo "  branch         Git branch to deploy [default: main]"
        echo ""
        echo "Examples:"
        echo "  $0 staging main"
        echo "  $0 production main"
        exit 0
        ;;
esac

# Run main function
main "$@"
