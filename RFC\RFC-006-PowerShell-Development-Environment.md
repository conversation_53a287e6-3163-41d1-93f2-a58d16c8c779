# RFC-006: PowerShell Geliştirme Ortamı ve Komut Kullanımı

**Durum**: Implemented ✅  
**<PERSON><PERSON>**: Augment Agent  
**Tarih**: 2025-06-29  
**Kategori**: Development Environment, Windows  

## 1. <PERSON>zet

Bu RFC, Windows PowerShell geliştirme ortamında çoklu komut çalıştırma, paket yönetimi ve geliştirme workflow'u için best practice'leri tanımlar.

## 2. Motivasyon

### 2.1 Problem
- PowerShell'de `&&` operatörü bash'teki gibi çalışmıyor
- Windows geliştirme ortamında komut zinciri hataları
- Paket yönetimi komutlarında tutarsızlık
- Cross-platform geliştirme zorlukları

### 2.2 Çözüm İhtiyacı
- PowerShell'e özel komut syntax'ı
- Hata yönetimi stratejileri
- Geliştirme ortamı standardizasyonu

## 3. PowerShell Komut Syntax'ı

### 3.1 Çoklu Komut Çalıştırma

#### ❌ Yanlış Kullanım (Bash Syntax'ı):
```bash
# Bu PowerShell'de hata verir
npm install && npm run build && npm start
```

#### ✅ Doğru Kullanım (PowerShell Syntax'ı):

**Basit Sıralı Çalıştırma:**
```powershell
# Semicolon ile sıralı çalıştırma
npm install; npm run build; npm start
```

**Koşullu Çalıştırma:**
```powershell
# Önceki komut başarılı olursa devam et
npm install; if ($?) { npm run build }; if ($?) { npm start }
```

**Hata Durumunda Durdurma:**
```powershell
# Hata durumunda tüm pipeline'ı durdur
npm install && npm run build && npm start  # Bu çalışmaz!

# Doğru yöntem:
try {
    npm install
    if ($LASTEXITCODE -ne 0) { throw "npm install failed" }
    npm run build
    if ($LASTEXITCODE -ne 0) { throw "npm run build failed" }
    npm start
} catch {
    Write-Error $_.Exception.Message
    exit 1
}
```

### 3.2 Paket Yönetimi Komutları

#### Frontend (Node.js):
```powershell
# Bağımlılık yükleme
cd frontend
npm install

# Geliştirme sunucusu başlatma
npm run dev

# Build işlemi
npm run build

# Çoklu komut örneği
npm install; npm run build; npm run start
```

#### Backend (Node.js):
```powershell
# Backend kurulumu
cd backend
npm install

# Veritabanı migration
npx prisma migrate dev
npx prisma generate

# Sunucu başlatma
npm run dev

# Çoklu komut
npm install; npx prisma generate; npm run dev
```

#### AI Services (Python):
```powershell
# Python sanal ortam
python -m venv venv
.\venv\Scripts\Activate.ps1

# Bağımlılık yükleme
pip install -r requirements.txt

# Servis başlatma
python main.py

# Çoklu komut
.\venv\Scripts\Activate.ps1; pip install -r requirements.txt; python main.py
```

## 4. Geliştirme Workflow'u

### 4.1 Proje Kurulumu
```powershell
# Repository klonlama
git clone https://github.com/your-org/natural-stone-marketplace.git
cd natural-stone-marketplace

# Tüm bağımlılıkları yükleme
cd frontend; npm install; cd ../backend; npm install; cd ../ai-services; pip install -r requirements.txt; cd ..
```

### 4.2 Geliştirme Sunucularını Başlatma
```powershell
# Ayrı terminal pencerelerinde:

# Terminal 1 - Frontend
cd frontend
npm run dev

# Terminal 2 - Backend  
cd backend
npm run dev

# Terminal 3 - AI Services
cd ai-services
python main.py

# Terminal 4 - Database
docker-compose up postgres redis
```

### 4.3 Build ve Deploy
```powershell
# Production build
cd frontend; npm run build; cd ../backend; npm run build

# Docker build
docker-compose build

# Deploy
docker-compose up -d
```

## 5. Hata Yönetimi

### 5.1 Exit Code Kontrolü
```powershell
# Komut başarı durumunu kontrol et
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Error "npm install failed with exit code $LASTEXITCODE"
    exit 1
}
```

### 5.2 Try-Catch Blokları
```powershell
try {
    npm install
    npm run build
    npm run test
} catch {
    Write-Error "Build process failed: $($_.Exception.Message)"
    exit 1
} finally {
    # Cleanup işlemleri
    Write-Host "Build process completed"
}
```

## 6. PowerShell Profil Konfigürasyonu

### 6.1 Profil Dosyası Oluşturma
```powershell
# PowerShell profil dosyasını düzenle
notepad $PROFILE
```

### 6.2 Yararlı Alias'lar
```powershell
# PowerShell Profile içeriği
Set-Alias -Name ll -Value Get-ChildItem
Set-Alias -Name grep -Value Select-String

# Proje spesifik fonksiyonlar
function Start-DevServers {
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd frontend; npm run dev"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; npm run dev"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd ai-services; python main.py"
}

function Build-Project {
    cd frontend; npm run build; cd ../backend; npm run build; cd ..
}
```

## 7. VS Code Entegrasyonu

### 7.1 Terminal Konfigürasyonu
```json
// .vscode/settings.json
{
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.profiles.windows": {
    "PowerShell": {
      "source": "PowerShell",
      "args": ["-NoExit"]
    }
  }
}
```

### 7.2 Task Konfigürasyonu
```json
// .vscode/tasks.json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Install Dependencies",
      "type": "shell",
      "command": "cd frontend; npm install; cd ../backend; npm install",
      "group": "build"
    },
    {
      "label": "Start Dev Servers",
      "type": "shell", 
      "command": "Start-Process powershell -ArgumentList '-NoExit', '-Command', 'cd frontend; npm run dev'",
      "group": "build"
    }
  ]
}
```

## 8. Best Practices

### 8.1 Komut Zinciri
- ✅ Semicolon (`;`) kullan sıralı çalıştırma için
- ✅ `if ($?)` kullan koşullu çalıştırma için
- ✅ Try-catch blokları kullan hata yönetimi için
- ❌ `&&` operatörü kullanma (bash syntax'ı)

### 8.2 Path Yönetimi
```powershell
# Relative path kullanımı
cd .\frontend
cd ..\backend

# Absolute path
cd C:\Projects\natural-stone-marketplace\frontend
```

### 8.3 Environment Variables
```powershell
# Environment variable set etme
$env:NODE_ENV = "development"
$env:DATABASE_URL = "postgresql://localhost:5432/marketplace"

# .env dosyası yükleme
Get-Content .env | ForEach-Object {
    $name, $value = $_.split('=')
    Set-Content env:\$name $value
}
```

## 9. Troubleshooting

### 9.1 Yaygın Hatalar
```powershell
# Execution Policy hatası
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Path bulunamadı hatası
$env:PATH += ";C:\Program Files\nodejs"

# Permission denied
Start-Process powershell -Verb RunAs
```

### 9.2 Debug Komutları
```powershell
# Komut geçmişi
Get-History

# Process listesi
Get-Process | Where-Object {$_.ProcessName -like "*node*"}

# Port kullanımı
netstat -ano | findstr :3000
```

## 10. Sonuç

Bu RFC, Windows PowerShell ortamında etkili geliştirme için gerekli komut syntax'ı ve best practice'leri tanımlar. Özellikle çoklu komut çalıştırma konusunda bash'ten farklı yaklaşımlar gerektiğini vurgular.

### Temel Kurallar:
1. `&&` yerine `;` veya `if ($?)` kullan
2. Hata yönetimi için try-catch blokları kullan
3. Exit code'ları kontrol et
4. PowerShell profili ile workflow'u optimize et

---

**Uygulama Durumu**: ✅ Implemented
**Test Durumu**: ✅ Verified
**Dokümantasyon**: ✅ Complete
