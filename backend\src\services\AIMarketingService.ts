import { PrismaClient } from '@prisma/client';
import OpenAI from 'openai';
import axios from 'axios';

interface LeadGenerationParams {
  targetCountries: string[];
  targetIndustries: string[];
  keywords: string[];
  maxLeads: number;
  confidenceThreshold: number;
}

interface MarketAnalysisParams {
  country: string;
  productCategory: string;
  analysisType: 'import_potential' | 'export_opportunity' | 'competition_analysis' | 'price_analysis';
}

interface EmailCampaignParams {
  templateId: string;
  targetSegment: any;
  personalizationData: any;
  sendSchedule: any;
}

export class AIMarketingService {
  private prisma: PrismaClient;
  private openai?: OpenAI;

  constructor() {
    this.prisma = new PrismaClient();

    // OpenAI is optional - only initialize if API key is provided
    if (process.env.OPENAI_API_KEY) {
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY
      });
    } else {
      console.warn('⚠️ OpenAI API key not provided. AI features will be limited.');
    }
  }

  // AI Lead Generation
  async generateLeads(campaignId: string, params: LeadGenerationParams) {
    try {
      console.log(`🤖 Starting AI lead generation for campaign: ${campaignId}`);

      // 1. Generate AI strategy
      const strategy = await this.generateLeadStrategy(params);
      
      // 2. Search for leads using multiple sources
      const leads = await this.searchLeads(params);
      
      // 3. Enrich leads with AI analysis
      const enrichedLeads = await this.enrichLeads(leads);
      
      // 4. Score and filter leads
      const qualifiedLeads = await this.scoreLeads(enrichedLeads, params.confidenceThreshold);
      
      // 5. Save leads to database
      const savedLeads = await this.saveLeads(campaignId, qualifiedLeads);
      
      // 6. Update campaign performance
      await this.updateCampaignMetrics(campaignId, savedLeads.length);

      console.log(`✅ Generated ${savedLeads.length} qualified leads`);
      return savedLeads;

    } catch (error) {
      console.error('❌ Lead generation failed:', error);
      throw error;
    }
  }

  // Generate AI-powered lead generation strategy
  private async generateLeadStrategy(params: LeadGenerationParams) {
    const prompt = `
    Create a comprehensive lead generation strategy for natural stone marketplace targeting:
    
    Countries: ${params.targetCountries.join(', ')}
    Industries: ${params.targetIndustries.join(', ')}
    Keywords: ${params.keywords.join(', ')}
    
    Provide:
    1. Target customer profiles
    2. Best lead sources for each country
    3. Messaging strategies
    4. Timing recommendations
    5. Success metrics
    
    Format as JSON with actionable insights.
    `;

    if (!this.openai) {
      throw new Error('OpenAI API key not configured. Cannot generate AI insights.');
    }

    const response = await this.openai!.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.7,
      max_tokens: 2000
    });

    return JSON.parse(response.choices[0].message.content || '{}');
  }

  // Search for leads from multiple sources
  private async searchLeads(params: LeadGenerationParams) {
    const leads: any[] = [];

    // 1. Search from existing country email lists
    const existingLeads = await this.searchExistingLeads(params);
    leads.push(...(existingLeads as any[]));

    // 2. Web scraping (simulated - in real implementation use proper APIs)
    const webLeads = await this.searchWebLeads(params);
    leads.push(...(webLeads as any[]));

    // 3. Trade databases (simulated)
    const tradeLeads = await this.searchTradeLeads(params);
    leads.push(...(tradeLeads as any[]));

    return leads;
  }

  private async searchExistingLeads(params: LeadGenerationParams) {
    return await this.prisma.$queryRaw`
      SELECT * FROM country_email_lists 
      WHERE country IN (${params.targetCountries.join(',')})
      AND (industry IN (${params.targetIndustries.join(',')}) OR industry IS NULL)
      AND verified = true
      ORDER BY engagement_score DESC
      LIMIT ${params.maxLeads}
    `;
  }

  private async searchWebLeads(params: LeadGenerationParams) {
    // Simulated web lead search
    // In real implementation, integrate with:
    // - LinkedIn Sales Navigator API
    // - ZoomInfo API
    // - Apollo.io API
    // - Hunter.io API
    
    const mockLeads = params.targetCountries.map(country => ({
      company_name: `${country} Stone Company`,
      contact_person: 'Business Development Manager',
      email: `contact@${country.toLowerCase()}stone.com`,
      country: country,
      industry: params.targetIndustries[0],
      source: 'web_search',
      confidence_score: 0.75
    }));

    return mockLeads;
  }

  private async searchTradeLeads(params: LeadGenerationParams) {
    // Simulated trade database search
    // In real implementation, integrate with:
    // - Trade Map API
    // - Export.gov API
    // - Kompass API
    // - Global Trade Atlas
    
    return [];
  }

  // Enrich leads with AI analysis
  private async enrichLeads(leads: any[]) {
    const enrichedLeads = [];

    for (const lead of leads) {
      try {
        const enrichment = await this.enrichSingleLead(lead);
        enrichedLeads.push({ ...lead, ...enrichment });
      } catch (error) {
        console.error(`Failed to enrich lead: ${lead.email}`, error);
        enrichedLeads.push(lead);
      }
    }

    return enrichedLeads;
  }

  private async enrichSingleLead(lead: any) {
    const prompt = `
    Analyze this business lead for natural stone marketplace:
    
    Company: ${lead.company_name}
    Contact: ${lead.contact_person}
    Country: ${lead.country}
    Industry: ${lead.industry}
    
    Provide analysis:
    1. Company size estimation
    2. Annual revenue range
    3. Natural stone import/usage potential
    4. Best approach strategy
    5. Conversion probability (0-1)
    6. Estimated deal value
    
    Format as JSON.
    `;

    if (!this.openai) {
      throw new Error('OpenAI API key not configured. Cannot generate AI analysis.');
    }

    const response = await this.openai!.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.3,
      max_tokens: 1000
    });

    const analysis = JSON.parse(response.choices[0].message.content || '{}');
    
    return {
      ai_analysis: analysis,
      company_size: analysis.company_size || 'unknown',
      annual_revenue: analysis.annual_revenue || 'unknown',
      conversion_probability: analysis.conversion_probability || 0.5,
      estimated_value: analysis.estimated_value || 0
    };
  }

  // Score and filter leads based on AI confidence
  private async scoreLeads(leads: any[], threshold: number) {
    return leads.filter(lead => {
      const score = this.calculateLeadScore(lead);
      lead.ai_confidence_score = score;
      return score >= threshold;
    }).sort((a, b) => b.ai_confidence_score - a.ai_confidence_score);
  }

  private calculateLeadScore(lead: any): number {
    let score = 0.5; // Base score

    // Company size factor
    const sizeScores = {
      'enterprise': 0.9,
      'large': 0.8,
      'medium': 0.7,
      'small': 0.5,
      'startup': 0.3
    };
    score += (sizeScores[lead.company_size as keyof typeof sizeScores] || 0.5) * 0.3;

    // Industry relevance
    const relevantIndustries = ['construction', 'architecture', 'interior_design', 'real_estate'];
    if (relevantIndustries.includes(lead.industry?.toLowerCase())) {
      score += 0.2;
    }

    // Email verification
    if (lead.verified) {
      score += 0.1;
    }

    // Engagement history
    score += (lead.engagement_score || 0) * 0.1;

    // AI analysis confidence
    if (lead.conversion_probability) {
      score += lead.conversion_probability * 0.2;
    }

    return Math.min(score, 1.0);
  }

  // Save leads to database
  private async saveLeads(campaignId: string, leads: any[]) {
    const savedLeads = [];

    for (const lead of leads) {
      try {
        const saved = await this.prisma.$queryRaw`
          INSERT INTO ai_leads (
            campaign_id, company_name, contact_person, email, phone,
            country, industry, company_size, annual_revenue, website,
            ai_confidence_score, ai_analysis, lead_source, 
            conversion_probability, estimated_value
          ) VALUES (
            ${campaignId}, ${lead.company_name}, ${lead.contact_person},
            ${lead.email}, ${lead.phone || null}, ${lead.country},
            ${lead.industry}, ${lead.company_size}, ${lead.annual_revenue},
            ${lead.website || null}, ${lead.ai_confidence_score},
            ${JSON.stringify(lead.ai_analysis)}, ${lead.source},
            ${lead.conversion_probability}, ${lead.estimated_value}
          )
        `;
        savedLeads.push(saved);
      } catch (error) {
        console.error(`Failed to save lead: ${lead.email}`, error);
      }
    }

    return savedLeads;
  }

  // Market Analysis with AI
  async analyzeMarket(params: MarketAnalysisParams) {
    try {
      console.log(`🔍 Starting market analysis for ${params.country} - ${params.productCategory}`);

      // 1. Gather trade data
      const tradeData = await this.gatherTradeData(params);
      
      // 2. AI analysis
      const aiInsights = await this.generateMarketInsights(params, tradeData);
      
      // 3. Calculate opportunity score
      const opportunityScore = this.calculateOpportunityScore(aiInsights, tradeData);
      
      // 4. Save analysis
      const analysis = await this.saveMarketAnalysis(params, aiInsights, tradeData, opportunityScore);

      console.log(`✅ Market analysis completed with opportunity score: ${opportunityScore}`);
      return analysis;

    } catch (error) {
      console.error('❌ Market analysis failed:', error);
      throw error;
    }
  }

  private async gatherTradeData(params: MarketAnalysisParams) {
    // Simulated trade data gathering
    // In real implementation, integrate with:
    // - Trade Map API
    // - UN Comtrade API
    // - World Bank API
    // - Export.gov API
    
    return {
      import_value: Math.random() * 1000000,
      export_value: Math.random() * 500000,
      growth_rate: (Math.random() - 0.5) * 20,
      main_suppliers: ['Turkey', 'Italy', 'China', 'India'],
      market_size: Math.random() * ********,
      competition_level: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)]
    };
  }

  private async generateMarketInsights(params: MarketAnalysisParams, tradeData: any) {
    const prompt = `
    Analyze the natural stone market for:
    Country: ${params.country}
    Product Category: ${params.productCategory}
    Analysis Type: ${params.analysisType}
    
    Trade Data:
    - Import Value: $${tradeData.import_value}
    - Export Value: $${tradeData.export_value}
    - Growth Rate: ${tradeData.growth_rate}%
    - Market Size: $${tradeData.market_size}
    - Competition: ${tradeData.competition_level}
    
    Provide comprehensive analysis:
    1. Market opportunity assessment
    2. Entry strategy recommendations
    3. Pricing strategy
    4. Risk factors
    5. Success probability
    6. Recommended actions
    
    Format as detailed JSON.
    `;

    if (!this.openai) {
      throw new Error('OpenAI API key not configured. Cannot generate market analysis.');
    }

    const response = await this.openai!.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.3,
      max_tokens: 2500
    });

    return JSON.parse(response.choices[0].message.content || '{}');
  }

  private calculateOpportunityScore(insights: any, tradeData: any): number {
    let score = 0.5;

    // Market size factor
    if (tradeData.market_size > 5000000) score += 0.2;
    else if (tradeData.market_size > 1000000) score += 0.1;

    // Growth rate factor
    if (tradeData.growth_rate > 10) score += 0.2;
    else if (tradeData.growth_rate > 5) score += 0.1;
    else if (tradeData.growth_rate < -5) score -= 0.2;

    // Competition factor
    if (tradeData.competition_level === 'low') score += 0.2;
    else if (tradeData.competition_level === 'high') score -= 0.1;

    // AI insights factor
    if (insights.success_probability) {
      score += insights.success_probability * 0.3;
    }

    return Math.max(0, Math.min(1, score));
  }

  private async saveMarketAnalysis(params: MarketAnalysisParams, insights: any, tradeData: any, opportunityScore: number) {
    return await this.prisma.$queryRaw`
      INSERT INTO ai_market_analysis (
        country, product_category, analysis_type, ai_insights,
        market_size, growth_rate, competition_level, opportunity_score,
        trade_data, confidence_level, data_freshness
      ) VALUES (
        ${params.country}, ${params.productCategory}, ${params.analysisType},
        ${JSON.stringify(insights)}, ${tradeData.market_size}, ${tradeData.growth_rate},
        ${tradeData.competition_level}, ${opportunityScore}, ${JSON.stringify(tradeData)},
        ${insights.confidence_level || 0.8}, NOW()
      )
    `;
  }

  // Update campaign metrics
  private async updateCampaignMetrics(campaignId: string, leadsGenerated: number) {
    await this.prisma.$queryRaw`
      UPDATE ai_campaigns 
      SET performance_metrics = JSON_SET(
        COALESCE(performance_metrics, '{}'),
        '$.leads_generated', ${leadsGenerated},
        '$.last_updated', NOW()
      )
      WHERE id = ${campaignId}
    `;
  }

  // AI Learning from campaign results
  async learnFromCampaign(campaignId: string, results: any) {
    try {
      const learningData = {
        data_type: 'campaign_performance',
        source_id: campaignId,
        source_type: 'ai_campaign',
        input_data: results.input_parameters,
        output_data: results.performance_metrics,
        success_metrics: results.success_metrics,
        ai_model_version: '1.0',
        feedback_score: results.feedback_score || 0.5
      };

      await this.prisma.$queryRaw`
        INSERT INTO ai_learning_data (
          data_type, source_id, source_type, input_data, output_data,
          success_metrics, ai_model_version, feedback_score
        ) VALUES (
          ${learningData.data_type}, ${learningData.source_id}, ${learningData.source_type},
          ${JSON.stringify(learningData.input_data)}, ${JSON.stringify(learningData.output_data)},
          ${JSON.stringify(learningData.success_metrics)}, ${learningData.ai_model_version},
          ${learningData.feedback_score}
        )
      `;

      console.log(`🧠 AI learned from campaign: ${campaignId}`);
    } catch (error) {
      console.error('❌ AI learning failed:', error);
    }
  }
}
