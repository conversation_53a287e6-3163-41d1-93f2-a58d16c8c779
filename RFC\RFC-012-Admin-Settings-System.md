# RFC-012: Admin Settings System
**Request for Comments - Admin Settings System**

## Metadata
- **RFC ID**: RFC-012
- **Title**: Admin Settings System
- **Author**: Augment Agent
- **Status**: Draft
- **Created**: 2025-07-03
- **Updated**: 2025-07-03
- **Version**: 1.0

## Abstract

Bu RFC, Türkiye Doğal Taş Marketplace platformu için kapsamlı admin settings (yönetici ayarları) sisteminin tasarımını ve implementasyonunu tanımlar. Sistem, platform yöneticilerinin tüm sistem ayarlarını merkezi bir arayüzden yönetmesine olanak sağlar.

## 1. Motivation

Admin settings sistemi aşağıdaki ihtiyaçları karşılamak için geliştirilmektedir:

- **Merkezi Yönetim**: Tüm platform ayarlarının tek bir yerden yönetimi
- **Güvenlik**: Kritik sistem ayarlarının gü<PERSON><PERSON> şekilde de<PERSON>iştirilmesi
- **Esneklik**: İş kurallarının kod değişikliği olmadan güncellenmesi
- **Auditability**: Ayar değişikliklerinin izlenebilirliği
- **Kullanım Kolaylığı**: Teknik olmayan kullanıcılar için kolay arayüz

## 2. System Overview

### 2.1 Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    ADMIN SETTINGS SYSTEM                    │
├─────────────────────────────────────────────────────────────┤
│  Frontend (React)  │  Backend API  │  Database  │  Cache    │
├─────────────────────────────────────────────────────────────┤
│ • Settings Forms   │ • CRUD APIs   │ • Settings │ • Redis   │
│ • Validation       │ • Validation  │   Table    │ • Config  │
│ • Real-time UI     │ • Audit Log   │ • Audit    │   Cache   │
│ • Permission Check │ • Middleware  │   Log      │           │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Core Components

1. **Settings Categories**: Modüler ayar kategorileri
2. **Permission System**: Role-based access control
3. **Validation Engine**: Server-side ve client-side validation
4. **Audit System**: Tüm değişikliklerin loglanması
5. **Cache Management**: Performans için cache sistemi

## 3. Settings Categories

### 3.1 Platform Genel Ayarları
- **Site Bilgileri**: Site adı, açıklama, logo, favicon
- **Tema Ayarları**: Renk paleti, font, layout seçenekleri
- **Dil ve Yerelleştirme**: Varsayılan dil, desteklenen diller
- **Bakım Modu**: Bakım modu açma/kapama, bakım mesajı
- **SEO Ayarları**: Meta tags, sitemap, robots.txt

### 3.2 Kullanıcı ve Güvenlik Ayarları
- **Kayıt Ayarları**: Otomatik onay, email doğrulama
- **Şifre Politikaları**: Minimum uzunluk, karmaşıklık kuralları
- **Session Yönetimi**: Timeout süreleri, concurrent sessions
- **2FA Ayarları**: Zorunlu 2FA, desteklenen yöntemler
- **IP Kısıtlamaları**: Whitelist/blacklist IP adresleri

### 3.3 İş Kuralları ve Komisyon Ayarları
- **Komisyon Oranları**: m² ve ton başına komisyon oranları
- **Ödeme Koşulları**: Ön ödeme yüzdesi, ödeme süreleri
- **Teklif Süreleri**: Teklif verme süreleri, otomatik iptal
- **Onay Süreçleri**: Ürün onay, üretici onay süreçleri
- **Minimum Sipariş**: Kategori bazlı minimum sipariş miktarları

### 3.4 Bildirim ve İletişim Ayarları
- **Email Ayarları**: SMTP konfigürasyonu, şablonlar
- **SMS Ayarları**: SMS provider, şablonlar
- **Push Notification**: Firebase/OneSignal konfigürasyonu
- **Bildirim Kuralları**: Hangi durumlarda bildirim gönderilecek
- **İletişim Şablonları**: Email/SMS şablonları yönetimi

### 3.5 Sistem ve Performans Ayarları
- **Cache Ayarları**: Redis TTL, cache stratejileri
- **Log Seviyeleri**: Debug, info, warning, error seviyeleri
- **Backup Konfigürasyonu**: Otomatik backup, retention policy
- **API Rate Limiting**: Request limitleri, throttling
- **File Upload**: Maksimum dosya boyutu, desteklenen formatlar

### 3.6 Entegrasyon Ayarları
- **Ödeme Sistemleri**: Stripe, PayPal, banka entegrasyonları
- **Lojistik Entegrasyonları**: Kargo firmaları, tracking
- **AI Servisleri**: OpenAI API, chatbot ayarları
- **3rd Party APIs**: Google Maps, analytics, monitoring
- **Webhook Konfigürasyonu**: Outgoing webhook'lar

## 4. Database Schema

### 4.1 Settings Table
```sql
CREATE TABLE admin_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category VARCHAR(100) NOT NULL,
  key VARCHAR(200) NOT NULL,
  value JSONB NOT NULL,
  data_type VARCHAR(50) NOT NULL, -- string, number, boolean, json, array
  description TEXT,
  is_sensitive BOOLEAN DEFAULT FALSE,
  requires_restart BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  updated_by UUID REFERENCES users(id),
  UNIQUE(category, key)
);
```

### 4.2 Settings Audit Log
```sql
CREATE TABLE admin_settings_audit (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  setting_id UUID REFERENCES admin_settings(id),
  category VARCHAR(100) NOT NULL,
  key VARCHAR(200) NOT NULL,
  old_value JSONB,
  new_value JSONB,
  changed_by UUID REFERENCES users(id),
  change_reason TEXT,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 5. API Design

### 5.1 Settings CRUD Operations

```typescript
// GET /api/admin/settings
// Get all settings or by category
interface GetSettingsRequest {
  category?: string;
  includeDescription?: boolean;
}

// PUT /api/admin/settings
// Update multiple settings
interface UpdateSettingsRequest {
  settings: Array<{
    category: string;
    key: string;
    value: any;
    changeReason?: string;
  }>;
}

// GET /api/admin/settings/schema
// Get settings schema for validation
interface SettingsSchema {
  [category: string]: {
    [key: string]: {
      type: string;
      required: boolean;
      validation: any;
      description: string;
      sensitive: boolean;
    };
  };
}
```

### 5.2 Validation Rules

```typescript
const settingsValidation = {
  platform: {
    siteName: {
      type: 'string',
      required: true,
      minLength: 3,
      maxLength: 100
    },
    maintenanceMode: {
      type: 'boolean',
      required: true
    }
  },
  business: {
    commissionRateM2: {
      type: 'number',
      required: true,
      min: 0,
      max: 100
    },
    commissionRateTon: {
      type: 'number',
      required: true,
      min: 0,
      max: 1000
    }
  }
};
```

## 6. Frontend Implementation

### 6.1 Component Structure

```
src/app/admin/settings/
├── page.tsx                    # Main settings page
├── components/
│   ├── SettingsLayout.tsx      # Layout with tabs
│   ├── SettingsForm.tsx        # Generic form component
│   ├── SettingsCategory.tsx    # Category container
│   └── SettingsField.tsx       # Individual field component
├── categories/
│   ├── PlatformSettings.tsx    # Platform settings
│   ├── SecuritySettings.tsx    # Security settings
│   ├── BusinessSettings.tsx    # Business rules
│   ├── NotificationSettings.tsx # Notifications
│   ├── SystemSettings.tsx      # System settings
│   └── IntegrationSettings.tsx # Integrations
└── hooks/
    ├── useSettings.tsx         # Settings management hook
    └── useSettingsValidation.tsx # Validation hook
```

### 6.2 Settings Context

```typescript
interface SettingsContextType {
  settings: Record<string, any>;
  loading: boolean;
  error: string | null;
  updateSettings: (updates: SettingUpdate[]) => Promise<void>;
  resetToDefaults: (category: string) => Promise<void>;
  exportSettings: () => Promise<void>;
  importSettings: (file: File) => Promise<void>;
}
```

## 7. Security Considerations

### 7.1 Access Control
- **Role-based permissions**: Sadece admin rolü erişebilir
- **Granular permissions**: Kategori bazlı erişim kontrolü
- **Sensitive settings**: Özel izin gerektiren ayarlar

### 7.2 Data Protection
- **Encryption**: Hassas ayarların şifrelenmesi
- **Audit logging**: Tüm değişikliklerin loglanması
- **Backup**: Ayar değişikliklerinden önce otomatik backup

### 7.3 Validation
- **Server-side validation**: Tüm girişlerin sunucu tarafında doğrulanması
- **Type safety**: TypeScript ile tip güvenliği
- **Business rules**: İş kurallarına uygunluk kontrolü

## 8. Implementation Plan

### Phase 1: Core Infrastructure (1 week)
- Database schema oluşturma
- Basic CRUD API'leri
- Settings context ve hooks

### Phase 2: Basic Categories (1 week)
- Platform genel ayarları
- Güvenlik ayarları
- İş kuralları ayarları

### Phase 3: Advanced Features (1 week)
- Bildirim ayarları
- Sistem ayarları
- Entegrasyon ayarları

### Phase 4: Polish & Testing (3 days)
- UI/UX iyileştirmeleri
- Validation ve error handling
- Testing ve documentation

## 9. Testing Strategy

### 9.1 Unit Tests
- Settings validation logic
- API endpoints
- React components

### 9.2 Integration Tests
- Database operations
- Cache invalidation
- Permission checks

### 9.3 E2E Tests
- Settings update flow
- Permission scenarios
- Error handling

## 10. Monitoring & Maintenance

### 10.1 Metrics
- Settings change frequency
- Error rates
- Performance metrics

### 10.2 Alerts
- Failed setting updates
- Permission violations
- System configuration errors

---

**Status**: Ready for Implementation
**Next Steps**: Begin Phase 1 implementation
**Dependencies**: Admin authentication system, database setup
