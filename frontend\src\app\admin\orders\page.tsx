'use client'

import React from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  ShoppingCart,
  Package,
  CreditCard,
  Truck,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  Eye,
  Edit,
  Search,
  Filter,
  Download,
  Plus,
  TrendingUp,
  Users,
  DollarSign
} from 'lucide-react'
import { mockOrderStats, mockOrderSummaries, mockOrders } from '@/data/mockOrders'
import { AdminOrderDetailsModal } from '@/components/ui/admin-order-details-modal'
import { OrderStatusUpdateModal } from '@/components/ui/order-status-update-modal'
import { OrderCommunicationModal } from '@/components/ui/order-communication-modal'
import { OrderStatus } from '@/types/orders'

export default function AdminOrdersPage() {
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedStatus, setSelectedStatus] = React.useState('all')
  const [selectedDateRange, setSelectedDateRange] = React.useState('all')
  const [showAdvancedFilters, setShowAdvancedFilters] = React.useState(false)
  const [selectedCustomer, setSelectedCustomer] = React.useState('all')
  const [selectedProducer, setSelectedProducer] = React.useState('all')
  const [minAmount, setMinAmount] = React.useState('')
  const [maxAmount, setMaxAmount] = React.useState('')
  const [selectedOrder, setSelectedOrder] = React.useState<any>(null)
  const [isOrderDetailsModalOpen, setIsOrderDetailsModalOpen] = React.useState(false)
  const [isStatusUpdateModalOpen, setIsStatusUpdateModalOpen] = React.useState(false)
  const [isCommunicationModalOpen, setIsCommunicationModalOpen] = React.useState(false)
  const [orders, setOrders] = React.useState(mockOrderSummaries)

  // İstatistikleri hesapla
  const stats = React.useMemo(() => {
    const pendingPaymentCount = orders.filter(o => o.status === 'pending-payment').length
    const inProductionCount = orders.filter(o => o.status === 'in-production').length
    const thisMonthRevenue = orders
      .filter(o => {
        const orderMonth = new Date(o.createdAt).getMonth()
        const currentMonth = new Date().getMonth()
        return orderMonth === currentMonth
      })
      .reduce((sum, o) => sum + o.totalAmount, 0)

    return {
      totalOrders: orders.length,
      pendingPayment: pendingPaymentCount,
      inProduction: inProductionCount,
      monthlyRevenue: thisMonthRevenue
    }
  }, [orders])

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Filtrelenmiş siparişler
  const filteredOrders = React.useMemo(() => {
    return orders.filter(order => {
      const matchesSearch =
        order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.producerName.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesStatus = selectedStatus === 'all' || order.status === selectedStatus

      const matchesCustomer = selectedCustomer === 'all' || order.customerName === selectedCustomer
      const matchesProducer = selectedProducer === 'all' || order.producerName === selectedProducer

      const matchesAmount =
        (minAmount === '' || order.totalAmount >= parseFloat(minAmount)) &&
        (maxAmount === '' || order.totalAmount <= parseFloat(maxAmount))

      // Tarih filtresi (basit implementasyon)
      let matchesDate = true
      if (selectedDateRange !== 'all') {
        const now = new Date()
        const orderDate = new Date(order.createdAt)

        switch (selectedDateRange) {
          case 'today':
            matchesDate = orderDate.toDateString() === now.toDateString()
            break
          case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            matchesDate = orderDate >= weekAgo
            break
          case 'month':
            matchesDate = orderDate.getMonth() === now.getMonth() && orderDate.getFullYear() === now.getFullYear()
            break
          case 'quarter':
            const quarter = Math.floor(now.getMonth() / 3)
            const orderQuarter = Math.floor(orderDate.getMonth() / 3)
            matchesDate = orderQuarter === quarter && orderDate.getFullYear() === now.getFullYear()
            break
          case 'year':
            matchesDate = orderDate.getFullYear() === now.getFullYear()
            break
        }
      }

      return matchesSearch && matchesStatus && matchesDate && matchesCustomer && matchesProducer && matchesAmount
    })
  }, [orders, searchTerm, selectedStatus, selectedDateRange, selectedCustomer, selectedProducer, minAmount, maxAmount])

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'pending-payment': { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Ön Ödeme Bekliyor' },
      'payment-confirmed': { color: 'bg-blue-100 text-blue-800', icon: CreditCard, label: 'Ödeme Onaylandı' },
      'in-production': { color: 'bg-orange-100 text-orange-800', icon: Package, label: 'Üretimde' },
      'ready-for-shipment': { color: 'bg-purple-100 text-purple-800', icon: Truck, label: 'Sevkiyat Hazır' },
      'shipped': { color: 'bg-indigo-100 text-indigo-800', icon: Truck, label: 'Kargoya Verildi' },
      'delivered': { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Teslim Edildi' },
      'completed': { color: 'bg-gray-100 text-gray-800', icon: CheckCircle, label: 'Tamamlandı' },
      'cancelled': { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'İptal Edildi' },
      'refunded': { color: 'bg-red-100 text-red-800', icon: AlertTriangle, label: 'İade Edildi' }
    }

    const config = statusConfig[status as keyof typeof statusConfig]
    if (!config) return null

    const Icon = config.icon
    return (
      <Badge variant="secondary" className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    )
  }

  const handleViewOrderDetails = (orderSummary: any) => {
    // Mock order'dan tam detayı bul
    const fullOrder = mockOrders.find(o => o.id === orderSummary.id)
    if (fullOrder) {
      setSelectedOrder(fullOrder)
      setIsOrderDetailsModalOpen(true)
    }
  }

  const handleStatusUpdate = (orderSummary: any) => {
    const fullOrder = mockOrders.find(o => o.id === orderSummary.id)
    if (fullOrder) {
      setSelectedOrder(fullOrder)
      setIsStatusUpdateModalOpen(true)
    }
  }

  const handleStatusUpdateSubmit = (orderId: string, newStatus: OrderStatus, note?: string) => {
    // Mock data güncelleme
    setOrders(prevOrders =>
      prevOrders.map(order =>
        order.id === orderId
          ? { ...order, status: newStatus, updatedAt: new Date() }
          : order
      )
    )

    // Mock orders'ı da güncelle
    const orderIndex = mockOrders.findIndex(o => o.id === orderId)
    if (orderIndex !== -1) {
      mockOrders[orderIndex].status = newStatus
      mockOrders[orderIndex].updatedAt = new Date()

      // Timeline'a yeni event ekle
      mockOrders[orderIndex].timeline.unshift({
        id: `evt-${Date.now()}`,
        type: 'status-change',
        title: 'Durum Güncellendi',
        description: `Sipariş durumu "${getStatusText(newStatus)}" olarak güncellendi${note ? `. Not: ${note}` : ''}`,
        timestamp: new Date(),
        userId: 'admin-001',
        userName: 'Admin'
      })
    }

    alert(`Sipariş durumu "${getStatusText(newStatus)}" olarak güncellendi!`)
  }

  const getStatusText = (status: string) => {
    const statusLabels = {
      'pending-payment': 'Ön Ödeme Bekliyor',
      'payment-confirmed': 'Ödeme Onaylandı',
      'in-production': 'Üretimde',
      'ready-for-shipment': 'Sevkiyat Hazır',
      'shipped': 'Kargoya Verildi',
      'delivered': 'Teslim Edildi',
      'completed': 'Tamamlandı',
      'cancelled': 'İptal Edildi',
      'refunded': 'İade Edildi'
    }
    return statusLabels[status as keyof typeof statusLabels] || status
  }

  const handleSendMessage = (recipient: 'customer' | 'producer', subject: string, message: string) => {
    // Burada gerçek API çağrısı yapılacak
    console.log('Send message:', { recipient, subject, message })
    alert(`Mesaj ${recipient === 'customer' ? 'müşteriye' : 'üreticiye'} gönderildi!`)
  }

  // Sipariş durumları
  const orderStatuses = React.useMemo(() => [
    { value: 'all', label: 'Tümü', count: orders.length },
    { value: 'pending-payment', label: 'Ön Ödeme Bekliyor', count: orders.filter(o => o.status === 'pending-payment').length },
    { value: 'payment-confirmed', label: 'Ödeme Onaylandı', count: orders.filter(o => o.status === 'payment-confirmed').length },
    { value: 'in-production', label: 'Üretimde', count: orders.filter(o => o.status === 'in-production').length },
    { value: 'ready-for-shipment', label: 'Sevkiyat Hazır', count: orders.filter(o => o.status === 'ready-for-shipment').length },
    { value: 'shipped', label: 'Kargoya Verildi', count: orders.filter(o => o.status === 'shipped').length },
    { value: 'delivered', label: 'Teslim Edildi', count: orders.filter(o => o.status === 'delivered').length },
    { value: 'completed', label: 'Tamamlandı', count: orders.filter(o => o.status === 'completed').length },
    { value: 'cancelled', label: 'İptal Edildi', count: orders.filter(o => o.status === 'cancelled').length },
    { value: 'refunded', label: 'İade Edildi', count: orders.filter(o => o.status === 'refunded').length }
  ], [orders])

  const dateRanges = [
    { value: 'all', label: 'Tüm Zamanlar' },
    { value: 'today', label: 'Bugün' },
    { value: 'week', label: 'Bu Hafta' },
    { value: 'month', label: 'Bu Ay' },
    { value: 'quarter', label: 'Bu Çeyrek' },
    { value: 'year', label: 'Bu Yıl' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Sipariş Yönetimi</h1>
          <p className="text-gray-600 mt-1">
            Tüm siparişleri görüntüleyin ve yönetin
          </p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => {
              // Excel raporu oluştur ve indir
              const reportData = filteredOrders.map(order => ({
                'Sipariş No': order.orderNumber,
                'Tarih': new Date(order.createdAt).toLocaleDateString('tr-TR'),
                'Müşteri': order.customerName,
                'Üretici': order.producerName,
                'Ürün Sayısı': order.productCount,
                'Toplam Tutar': `${order.totalAmount} ${order.currency}`,
                'Durum': getStatusText(order.status),
                'Son Güncelleme': new Date(order.updatedAt).toLocaleDateString('tr-TR')
              }))

              // CSV formatında indir
              const csvContent = [
                Object.keys(reportData[0] || {}).join(','),
                ...reportData.map(row => Object.values(row).join(','))
              ].join('\n')

              const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
              const link = document.createElement('a')
              const url = URL.createObjectURL(blob)
              link.setAttribute('href', url)
              link.setAttribute('download', `siparis-raporu-${new Date().toISOString().split('T')[0]}.csv`)
              link.style.visibility = 'hidden'
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
            }}
          >
            <Download className="w-4 h-4 mr-2" />
            Rapor İndir
          </Button>
          <Button
            onClick={() => {
              window.location.href = '/admin/orders/create'
            }}
          >
            <Plus className="w-4 h-4 mr-2" />
            Manuel Sipariş
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <ShoppingCart className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Sipariş</p>
              <p className="text-xl font-bold text-gray-900">{stats.totalOrders}</p>
              <p className="text-xs text-gray-500 mt-1">Aktif siparişler</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Bekleyen Ödeme</p>
              <p className="text-xl font-bold text-gray-900">{stats.pendingPayment}</p>
              <p className="text-xs text-gray-500 mt-1">Ön ödeme bekliyor</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Package className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Aktif Üretim</p>
              <p className="text-xl font-bold text-gray-900">{stats.inProduction}</p>
              <p className="text-xs text-gray-500 mt-1">Üretim aşamasında</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Bu Ay Ciro</p>
              <p className="text-xl font-bold text-gray-900">{formatCurrency(stats.monthlyRevenue)}</p>
              <p className="text-xs text-gray-500 mt-1">Aylık gelir</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 min-w-64 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Sipariş no, müşteri veya ürün ile ara..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
          />
        </div>
        
        <select
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
        >
          {orderStatuses.map(status => (
            <option key={status.value} value={status.value}>
              {status.label} ({status.count})
            </option>
          ))}
        </select>

        <select
          value={selectedDateRange}
          onChange={(e) => setSelectedDateRange(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
        >
          {dateRanges.map(range => (
            <option key={range.value} value={range.value}>
              {range.label}
            </option>
          ))}
        </select>

        <Button
          variant="outline"
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
        >
          <Filter className="w-4 h-4 mr-2" />
          Gelişmiş Filtre
        </Button>
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <Card className="p-4">
          <h4 className="font-medium mb-4">Gelişmiş Filtreler</h4>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Müşteri</label>
              <select
                value={selectedCustomer}
                onChange={(e) => setSelectedCustomer(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                <option value="all">Tüm Müşteriler</option>
                {Array.from(new Set(orders.map(o => o.customerName))).map(customer => (
                  <option key={customer} value={customer}>{customer}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Üretici</label>
              <select
                value={selectedProducer}
                onChange={(e) => setSelectedProducer(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                <option value="all">Tüm Üreticiler</option>
                {Array.from(new Set(orders.map(o => o.producerName))).map(producer => (
                  <option key={producer} value={producer}>{producer}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Min. Tutar ($)</label>
              <input
                type="number"
                value={minAmount}
                onChange={(e) => setMinAmount(e.target.value)}
                placeholder="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Max. Tutar ($)</label>
              <input
                type="number"
                value={maxAmount}
                onChange={(e) => setMaxAmount(e.target.value)}
                placeholder="999999"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
              />
            </div>
          </div>

          <div className="flex gap-2 mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedCustomer('all')
                setSelectedProducer('all')
                setMinAmount('')
                setMaxAmount('')
                setSearchTerm('')
                setSelectedStatus('all')
                setSelectedDateRange('all')
              }}
            >
              Filtreleri Temizle
            </Button>
          </div>
        </Card>
      )}

      {/* Orders Table */}
      <Card>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b border-gray-200 bg-gray-50">
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Sipariş No</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Tarih</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Müşteri</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Üretici</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Ürün Sayısı</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Toplam Tutar</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Durum</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Son Tarih</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">İşlemler</th>
              </tr>
            </thead>
            <tbody>
              {filteredOrders.length > 0 ? filteredOrders.map((order) => (
                <tr key={order.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="px-4 py-3">
                    <div className="font-medium text-blue-600">
                      {order.orderNumber}
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-600">
                    {new Date(order.createdAt).toLocaleDateString('tr-TR')}
                  </td>
                  <td className="px-4 py-3">
                    <button
                      className="text-blue-600 hover:underline font-medium"
                      onClick={() => {
                        // Müşteri profil sayfasına git
                        const fullOrder = mockOrders.find(o => o.id === order.id)
                        if (fullOrder) {
                          window.open(`/admin/customers/${fullOrder.customer.id}`, '_blank')
                        }
                      }}
                    >
                      {order.customerName}
                    </button>
                  </td>
                  <td className="px-4 py-3">
                    <button
                      className="text-blue-600 hover:underline font-medium"
                      onClick={() => {
                        // Üretici profil sayfasına git
                        const fullOrder = mockOrders.find(o => o.id === order.id)
                        if (fullOrder) {
                          window.open(`/admin/producers/${fullOrder.producer.id}`, '_blank')
                        }
                      }}
                    >
                      {order.producerName}
                    </button>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Package className="w-4 h-4" />
                      {order.productCount} ürün
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="font-semibold text-green-600">
                      {formatCurrency(order.totalAmount, order.currency)}
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    {getStatusBadge(order.status)}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-600">
                    {order.dueDate ? new Date(order.dueDate).toLocaleDateString('tr-TR') : '-'}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewOrderDetails(order)}
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        Detay
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStatusUpdate(order)}
                      >
                        <Edit className="w-4 h-4 mr-1" />
                        Durum
                      </Button>
                    </div>
                  </td>
                </tr>
              )) : (
                <tr>
                  <td colSpan={9} className="px-4 py-8 text-center">
                    <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-600">Arama kriterlerinize uygun sipariş bulunamadı.</p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Table Footer */}
        {filteredOrders.length > 0 && (
          <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div>
                Toplam {filteredOrders.length} sipariş gösteriliyor
              </div>
              <div className="flex items-center gap-4">
                <div>
                  Toplam Tutar: <span className="font-semibold text-green-600">
                    {formatCurrency(filteredOrders.reduce((sum, order) => sum + order.totalAmount, 0))}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Status Legend */}
      <Card className="p-4">
        <h4 className="font-medium mb-3">Sipariş Durumları</h4>
        <div className="flex flex-wrap gap-2">
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            Ön Ödeme Bekliyor
          </Badge>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            <CreditCard className="w-3 h-3 mr-1" />
            Ödeme Onaylandı
          </Badge>
          <Badge variant="secondary" className="bg-orange-100 text-orange-800">
            <Package className="w-3 h-3 mr-1" />
            Üretimde
          </Badge>
          <Badge variant="secondary" className="bg-purple-100 text-purple-800">
            <Truck className="w-3 h-3 mr-1" />
            Sevkiyat Hazır
          </Badge>
          <Badge variant="secondary" className="bg-indigo-100 text-indigo-800">
            <Truck className="w-3 h-3 mr-1" />
            Kargoya Verildi
          </Badge>
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Teslim Edildi
          </Badge>
          <Badge variant="secondary" className="bg-gray-100 text-gray-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Tamamlandı
          </Badge>
          <Badge variant="secondary" className="bg-red-100 text-red-800">
            <XCircle className="w-3 h-3 mr-1" />
            İptal Edildi
          </Badge>
        </div>
      </Card>

      {/* Order Details Modal */}
      <AdminOrderDetailsModal
        isOpen={isOrderDetailsModalOpen}
        onClose={() => setIsOrderDetailsModalOpen(false)}
        order={selectedOrder}
        onStatusUpdate={(order) => {
          setSelectedOrder(order)
          setIsOrderDetailsModalOpen(false)
          setIsStatusUpdateModalOpen(true)
        }}
        onSendMessage={(order) => {
          setSelectedOrder(order)
          setIsOrderDetailsModalOpen(false)
          setIsCommunicationModalOpen(true)
        }}
      />

      {/* Status Update Modal */}
      <OrderStatusUpdateModal
        isOpen={isStatusUpdateModalOpen}
        onClose={() => setIsStatusUpdateModalOpen(false)}
        order={selectedOrder}
        onStatusUpdate={handleStatusUpdateSubmit}
      />

      {/* Communication Modal */}
      <OrderCommunicationModal
        isOpen={isCommunicationModalOpen}
        onClose={() => setIsCommunicationModalOpen(false)}
        order={selectedOrder}
        onSendMessage={handleSendMessage}
      />
    </div>
  )
}
