'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Server, Database, FileText, Zap, AlertTriangle } from 'lucide-react';
import { useSettings } from '../context/SettingsContext';

const SystemSettings = () => {
  const { getSettingValue, updateSetting } = useSettings();

  // System settings values
  const cacheTimeout = getSettingValue('system', 'cacheTimeout') || 3600;
  const logLevel = getSettingValue('system', 'logLevel') || 'info';
  const maxFileUploadSize = getSettingValue('system', 'maxFileUploadSize') || 10;

  const logLevels = [
    { value: 'debug', label: 'Debug', description: 'Tüm detaylar' },
    { value: 'info', label: 'Info', description: 'Genel bilgiler' },
    { value: 'warn', label: 'Warning', description: 'Uyarılar' },
    { value: 'error', label: 'Error', description: 'Sadece hatalar' }
  ];

  return (
    <div className="space-y-6">
      {/* Cache Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Database className="w-5 h-5 text-blue-600" />
            <CardTitle>Cache Ayarları</CardTitle>
          </div>
          <CardDescription>
            Sistem performansı için cache konfigürasyonu
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="cacheTimeout">Cache Timeout (Saniye)</Label>
            <Input
              id="cacheTimeout"
              type="number"
              min="60"
              max="86400"
              value={cacheTimeout}
              onChange={(e) => updateSetting('system', 'cacheTimeout', parseInt(e.target.value) || 3600)}
            />
            <p className="text-xs text-gray-500">
              {Math.floor(cacheTimeout / 60)} dakika ({Math.floor(cacheTimeout / 3600)} saat)
            </p>
          </div>

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Uyarı:</strong> Bu ayar değiştirildiğinde sistem yeniden başlatılması gerekir.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Logging Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <FileText className="w-5 h-5 text-green-600" />
            <CardTitle>Log Ayarları</CardTitle>
          </div>
          <CardDescription>
            Sistem logları ve hata takibi konfigürasyonu
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="logLevel">Log Seviyesi</Label>
            <Select
              value={logLevel}
              onValueChange={(value) => updateSetting('system', 'logLevel', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Log seviyesi seçin" />
              </SelectTrigger>
              <SelectContent>
                {logLevels.map((level) => (
                  <SelectItem key={level.value} value={level.value}>
                    <div className="flex items-center justify-between w-full">
                      <span>{level.label}</span>
                      <span className="text-xs text-gray-500 ml-2">{level.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500">
              Daha düşük seviyeler daha fazla log üretir
            </p>
          </div>

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Uyarı:</strong> Bu ayar değiştirildiğinde sistem yeniden başlatılması gerekir.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* File Upload Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-orange-600" />
            <CardTitle>Dosya Yükleme Ayarları</CardTitle>
          </div>
          <CardDescription>
            Dosya yükleme limitleri ve kısıtlamaları
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="maxFileUploadSize">Maksimum Dosya Boyutu (MB)</Label>
            <Input
              id="maxFileUploadSize"
              type="number"
              min="1"
              max="100"
              value={maxFileUploadSize}
              onChange={(e) => updateSetting('system', 'maxFileUploadSize', parseInt(e.target.value) || 10)}
            />
            <p className="text-xs text-gray-500">
              Kullanıcıların yükleyebileceği maksimum dosya boyutu
            </p>
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Server className="w-5 h-5 text-blue-600" />
            <CardTitle>Sistem Durumu</CardTitle>
          </div>
          <CardDescription>
            Mevcut sistem ayarlarının özeti
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800">Cache Durumu</h4>
              <p className="text-sm text-blue-600 mt-1">
                {Math.floor(cacheTimeout / 3600)} saat timeout
              </p>
              <Badge variant="default" className="mt-2">Aktif</Badge>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-800">Log Seviyesi</h4>
              <p className="text-sm text-green-600 mt-1">
                {logLevels.find(l => l.value === logLevel)?.label}
              </p>
              <Badge variant="default" className="mt-2">Aktif</Badge>
            </div>
            
            <div className="p-4 bg-orange-50 rounded-lg">
              <h4 className="font-medium text-orange-800">Dosya Limiti</h4>
              <p className="text-sm text-orange-600 mt-1">
                {maxFileUploadSize} MB maksimum
              </p>
              <Badge variant="default" className="mt-2">Aktif</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemSettings;
