'use client'

import * as React from 'react'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  RefreshCw, 
  AlertTriangle, 
  DollarSign,
  Calendar,
  Package,
  User,
  MessageSquare,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface ResubmitQuoteModalProps {
  isOpen: boolean
  onClose: () => void
  request: any
  onSubmitQuote: (quoteData: any) => Promise<boolean>
}

export function ResubmitQuoteModal({
  isOpen,
  onClose,
  request,
  onSubmitQuote
}: ResubmitQuoteModalProps) {
  const [quoteData, setQuoteData] = React.useState({
    unitPrice: '',
    totalPrice: '',
    currency: 'USD',
    deliveryTime: '',
    deliveryMethod: '',
    paymentTerms: '',
    notes: '',
    improvements: ''
  })
  const [isLoading, setIsLoading] = React.useState(false)
  const [step, setStep] = React.useState(1) // 1: Review, 2: Quote Form, 3: Confirmation

  React.useEffect(() => {
    if (isOpen && request) {
      // Reset form when modal opens
      setQuoteData({
        unitPrice: '',
        totalPrice: '',
        currency: request.currency || 'USD',
        deliveryTime: '',
        deliveryMethod: '',
        paymentTerms: '',
        notes: '',
        improvements: ''
      })
      setStep(1)
    }
  }, [isOpen, request])

  const handleInputChange = (field: string, value: string) => {
    setQuoteData(prev => {
      const updated = { ...prev, [field]: value }
      
      // Auto-calculate total price when unit price or quantity changes
      if (field === 'unitPrice' && value && request?.quantity) {
        updated.totalPrice = (parseFloat(value) * request.quantity).toFixed(2)
      }
      
      return updated
    })
  }

  const handleSubmit = async () => {
    if (!quoteData.unitPrice || !quoteData.deliveryTime) {
      alert('Lütfen zorunlu alanları doldurun!')
      return
    }

    setIsLoading(true)
    try {
      const success = await onSubmitQuote({
        ...quoteData,
        requestId: request.id,
        isResubmission: true,
        originalRejectionReason: request.rejectionReason
      })
      
      if (success) {
        setStep(3)
        setTimeout(() => {
          onClose()
        }, 2000)
      }
    } catch (error) {
      console.error('Error submitting quote:', error)
      alert('Teklif gönderilirken hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const getImprovementSuggestions = () => {
    const reason = request?.rejectionReason?.toLowerCase() || ''
    
    if (reason.includes('fiyat') || reason.includes('price')) {
      return [
        'Alternatif kalite seçenekleri sunun',
        'Toplu alım indirimi teklif edin',
        'Farklı ödeme koşulları önerin',
        'Benzer özellikli daha ekonomik ürün önerin'
      ]
    } else if (reason.includes('teslimat') || reason.includes('delivery')) {
      return [
        'Kısmi teslimat seçeneği sunun',
        'Express teslimat ücreti ile hızlandırın',
        'Farklı teslimat noktaları önerin',
        'Esnek teslimat tarihleri belirtin'
      ]
    } else if (reason.includes('stok') || reason.includes('stock')) {
      return [
        'Benzer alternatif ürünler önerin',
        'Ön sipariş ile üretim planlayın',
        'Mevcut stoktan kısmi teslimat yapın',
        'Tedarik süresini netleştirin'
      ]
    }
    
    return [
      'Müşteri ihtiyaçlarını daha iyi anlayın',
      'Esnek çözümler sunun',
      'Detaylı açıklama yapın',
      'Alternatif seçenekler önerin'
    ]
  }

  if (!request) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <RefreshCw className="w-5 h-5" />
            Yeniden Teklif Ver - #{request.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step 1: Review Rejection */}
          {step === 1 && (
            <>
              {/* Original Request Info */}
              <Card className="bg-blue-50 border-blue-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg text-blue-800">Orijinal Talep Bilgileri</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-blue-700">Müşteri:</span>
                    <p className="text-blue-900">{request.customerName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">Ürün:</span>
                    <p className="text-blue-900">{request.productName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">Miktar:</span>
                    <p className="text-blue-900">{request.quantity} m²</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">Hedef Fiyat:</span>
                    <p className="text-blue-900">{request.targetPrice} {request.currency}/m²</p>
                  </div>
                </CardContent>
              </Card>

              {/* Rejection Reason */}
              <Card className="bg-red-50 border-red-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg text-red-800 flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5" />
                    Red Sebebi
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-red-700">{request.rejectionReason}</p>
                </CardContent>
              </Card>

              {/* Improvement Suggestions */}
              <Card className="bg-green-50 border-green-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg text-green-800">💡 İyileştirme Önerileri</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {getImprovementSuggestions().map((suggestion, index) => (
                      <li key={index} className="flex items-start gap-2 text-green-700">
                        <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{suggestion}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={onClose}>
                  İptal
                </Button>
                <Button onClick={() => setStep(2)} className="bg-green-600 hover:bg-green-700">
                  Yeni Teklif Hazırla
                </Button>
              </div>
            </>
          )}

          {/* Step 2: Quote Form */}
          {step === 2 && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Yeni Teklif Bilgileri</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Price Information */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="unitPrice">Birim Fiyat *</Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          id="unitPrice"
                          type="number"
                          step="0.01"
                          value={quoteData.unitPrice}
                          onChange={(e) => handleInputChange('unitPrice', e.target.value)}
                          className="pl-10"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="currency">Para Birimi</Label>
                      <Select value={quoteData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="TRY">TRY</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor="totalPrice">Toplam Fiyat</Label>
                      <Input
                        id="totalPrice"
                        value={quoteData.totalPrice}
                        readOnly
                        className="bg-gray-50"
                        placeholder="Otomatik hesaplanacak"
                      />
                    </div>
                  </div>

                  {/* Delivery Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="deliveryTime">Teslimat Süresi *</Label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          id="deliveryTime"
                          value={quoteData.deliveryTime}
                          onChange={(e) => handleInputChange('deliveryTime', e.target.value)}
                          className="pl-10"
                          placeholder="örn: 15 gün"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="deliveryMethod">Teslimat Yöntemi</Label>
                      <Select value={quoteData.deliveryMethod} onValueChange={(value) => handleInputChange('deliveryMethod', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Teslimat yöntemi seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="factory">Fabrika Teslim</SelectItem>
                          <SelectItem value="delivery">Adrese Teslimat</SelectItem>
                          <SelectItem value="port">Liman Teslim</SelectItem>
                          <SelectItem value="pickup">Müşteri Alacak</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Payment Terms */}
                  <div>
                    <Label htmlFor="paymentTerms">Ödeme Koşulları</Label>
                    <Select value={quoteData.paymentTerms} onValueChange={(value) => handleInputChange('paymentTerms', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Ödeme koşulları seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="advance_50">%50 Avans, %50 Teslimat</SelectItem>
                        <SelectItem value="advance_30">%30 Avans, %70 Teslimat</SelectItem>
                        <SelectItem value="full_advance">%100 Avans</SelectItem>
                        <SelectItem value="delivery">Teslimat Sonrası Ödeme</SelectItem>
                        <SelectItem value="custom">Özel Koşullar</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Improvements Made */}
                  <div>
                    <Label htmlFor="improvements">Red Sebebine Yönelik İyileştirmeleriniz</Label>
                    <Textarea
                      id="improvements"
                      value={quoteData.improvements}
                      onChange={(e) => handleInputChange('improvements', e.target.value)}
                      rows={3}
                      placeholder="Önceki red sebebini nasıl çözdüğünüzü açıklayın..."
                    />
                  </div>

                  {/* Additional Notes */}
                  <div>
                    <Label htmlFor="notes">Ek Notlar</Label>
                    <Textarea
                      id="notes"
                      value={quoteData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      rows={3}
                      placeholder="Teklif ile ilgili ek bilgiler, özel koşullar..."
                    />
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>
                  Geri Dön
                </Button>
                <Button 
                  onClick={handleSubmit} 
                  disabled={!quoteData.unitPrice || !quoteData.deliveryTime || isLoading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Gönderiliyor...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Yeniden Teklif Gönder
                    </>
                  )}
                </Button>
              </div>
            </>
          )}

          {/* Step 3: Confirmation */}
          {step === 3 && (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-green-800 mb-2">
                Yeniden Teklif Başarıyla Gönderildi!
              </h3>
              <p className="text-green-600">
                Teklifiniz {request.customerName} müşterisine iletildi.
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
