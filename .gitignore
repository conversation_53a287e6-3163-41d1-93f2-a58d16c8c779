# ============ SECURITY - SENSITIVE FILES ============
# Environment files with secrets
.env.production*
.env.staging*
.env.local
.env

# SSL certificates and keys
*.pem
*.key
*.crt
*.p12
*.pfx

# Database dumps
*.sql
*.dump

# Backup files
*.backup
*.bak

# ============ DEPENDENCIES ============
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# ============ BUILD OUTPUTS ============
dist/
build/
out/
.next/
coverage/

# ============ LOGS ============
logs/
*.log
*.log.*

# ============ TEMPORARY FILES ============
tmp/
temp/
.tmp/
.cache/

# ============ IDE AND EDITOR FILES ============
.vscode/
.idea/
*.swp
*.swo
*~

# ============ OS GENERATED FILES ============
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ============ DOCKER ============
.dockerignore

# ============ UPLOADS ============
uploads/
public/uploads/

# ============ TESTING ============
.nyc_output/
coverage/
*.lcov

# ============ TYPESCRIPT ============
*.tsbuildinfo

# ============ MISC ============
.vercel
.netlify
