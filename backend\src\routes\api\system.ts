// RFC-501: System Monitoring API Routes
import express, { Request, Response } from 'express';
import { SystemMonitoringService } from '../../services/system/SystemMonitoringService';
import { LogManagementService } from '../../services/system/LogManagementService';
import { MaintenanceService } from '../../services/system/MaintenanceService';

const router = express.Router();
const systemService = new SystemMonitoringService();
const logService = new LogManagementService();
const maintenanceService = new MaintenanceService();

/**
 * @swagger
 * /api/system/metrics:
 *   get:
 *     summary: Get comprehensive system metrics
 *     tags: [System Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     cpu:
 *                       type: object
 *                       properties:
 *                         usage:
 *                           type: number
 *                         cores:
 *                           type: number
 *                         temperature:
 *                           type: number
 *                     memory:
 *                       type: object
 *                       properties:
 *                         used:
 *                           type: number
 *                         total:
 *                           type: number
 *                         percentage:
 *                           type: number
 *       403:
 *         description: Admin access required
 */
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    const metrics = await systemService.getSystemMetrics();
    res.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    console.error('Error fetching system metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system metrics'
    });
  }
});

/**
 * @swagger
 * /api/system/services:
 *   get:
 *     summary: Get status of system services
 *     tags: [System Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Service status retrieved successfully
 */
router.get('/services', async (req: Request, res: Response) => {
  try {
    const services = await systemService.getServiceStatus();
    res.json({
      success: true,
      data: services
    });
  } catch (error) {
    console.error('Error fetching service status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service status'
    });
  }
});

/**
 * @swagger
 * /api/system/alerts:
 *   get:
 *     summary: Get system alerts
 *     tags: [System Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of alerts to retrieve
 *     responses:
 *       200:
 *         description: System alerts retrieved successfully
 */
router.get('/alerts', async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const alerts = await systemService.getSystemAlerts(limit);
    res.json({
      success: true,
      data: alerts
    });
  } catch (error) {
    console.error('Error fetching system alerts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system alerts'
    });
  }
});

/**
 * @swagger
 * /api/system/alerts:
 *   delete:
 *     summary: Clear all system alerts
 *     tags: [System Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System alerts cleared successfully
 */
router.delete('/alerts', async (req: Request, res: Response) => {
  try {
    await systemService.clearSystemAlerts();
    res.json({
      success: true,
      message: 'System alerts cleared successfully'
    });
  } catch (error) {
    console.error('Error clearing system alerts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear system alerts'
    });
  }
});

/**
 * @swagger
 * /api/system/uptime:
 *   get:
 *     summary: Get system uptime
 *     tags: [System Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System uptime retrieved successfully
 */
router.get('/uptime', async (req: Request, res: Response) => {
  try {
    const uptime = systemService.getSystemUptime();
    res.json({
      success: true,
      data: {
        uptime,
        uptimeFormatted: formatUptime(uptime)
      }
    });
  } catch (error) {
    console.error('Error fetching system uptime:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system uptime'
    });
  }
});

/**
 * @swagger
 * /api/system/maintenance/cache:
 *   post:
 *     summary: Clear system cache
 *     tags: [System Maintenance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cache cleared successfully
 */
router.post('/maintenance/cache', async (req: Request, res: Response) => {
  try {
    const task = await maintenanceService.clearCache();

    res.json({
      success: true,
      data: task,
      message: 'Cache clearing task started'
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear cache'
    });
  }
});

/**
 * @swagger
 * /api/system/maintenance/database:
 *   post:
 *     summary: Optimize database
 *     tags: [System Maintenance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Database optimized successfully
 */
router.post('/maintenance/database', async (req: Request, res: Response) => {
  try {
    const task = await maintenanceService.optimizeDatabase();

    res.json({
      success: true,
      data: task,
      message: 'Database optimization task started'
    });
  } catch (error) {
    console.error('Error optimizing database:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to optimize database'
    });
  }
});

/**
 * @swagger
 * /api/system/maintenance/cleanup:
 *   post:
 *     summary: Clean temporary files and old logs
 *     tags: [System Maintenance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cleanup task started successfully
 */
router.post('/maintenance/cleanup', async (req: Request, res: Response) => {
  try {
    const task = await maintenanceService.cleanTempFiles();

    res.json({
      success: true,
      data: task,
      message: 'Cleanup task started'
    });
  } catch (error) {
    console.error('Error cleaning temp files:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clean temp files'
    });
  }
});

/**
 * @swagger
 * /api/system/maintenance/backup:
 *   post:
 *     summary: Create database backup
 *     tags: [System Maintenance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Backup task started successfully
 */
router.post('/maintenance/backup', async (req: Request, res: Response) => {
  try {
    const task = await maintenanceService.createBackup();

    res.json({
      success: true,
      data: task,
      message: 'Backup task started'
    });
  } catch (error) {
    console.error('Error creating backup:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create backup'
    });
  }
});

/**
 * @swagger
 * /api/system/maintenance/stats:
 *   get:
 *     summary: Get maintenance statistics
 *     tags: [System Maintenance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Maintenance statistics retrieved successfully
 */
router.get('/maintenance/stats', async (req: Request, res: Response) => {
  try {
    const stats = await maintenanceService.getMaintenanceStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching maintenance stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch maintenance statistics'
    });
  }
});

/**
 * @swagger
 * /api/system/maintenance/tasks:
 *   get:
 *     summary: Get running maintenance tasks
 *     tags: [System Maintenance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Running tasks retrieved successfully
 */
router.get('/maintenance/tasks', async (req: Request, res: Response) => {
  try {
    const tasks = maintenanceService.getRunningTasks();

    res.json({
      success: true,
      data: tasks
    });
  } catch (error) {
    console.error('Error fetching maintenance tasks:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch maintenance tasks'
    });
  }
});

/**
 * @swagger
 * /api/system/maintenance/tasks/:taskId:
 *   get:
 *     summary: Get maintenance task status
 *     tags: [System Maintenance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: taskId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Task status retrieved successfully
 */
router.get('/maintenance/tasks/:taskId', async (req: Request, res: Response) => {
  try {
    const { taskId } = req.params;
    const task = maintenanceService.getTask(taskId);

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    return res.json({
      success: true,
      data: task
    });
  } catch (error) {
    console.error('Error fetching maintenance task:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch maintenance task'
    });
  }
});

/**
 * @swagger
 * /api/system/health:
 *   get:
 *     summary: Get comprehensive system health check
 *     tags: [System Monitoring]
 *     responses:
 *       200:
 *         description: System health check completed
 */
router.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        api: 'healthy',
        database: 'healthy',
        redis: 'healthy'
      },
      uptime: systemService.getSystemUptime()
    };

    res.json({
      success: true,
      data: health
    });
  } catch (error) {
    console.error('Error checking system health:', error);
    res.status(500).json({
      success: false,
      message: 'System health check failed'
    });
  }
});

/**
 * Helper function to format uptime
 */
function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  return `${days}d ${hours}h ${minutes}m`;
}

/**
 * @swagger
 * /api/system/logs:
 *   get:
 *     summary: Get system logs with filtering
 *     tags: [System Logs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [debug, info, warning, error, all]
 *         description: Filter by log level
 *       - in: query
 *         name: source
 *         schema:
 *           type: string
 *         description: Filter by log source
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in log messages
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Number of logs to retrieve
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Offset for pagination
 *     responses:
 *       200:
 *         description: System logs retrieved successfully
 */
router.get('/logs', async (req: Request, res: Response) => {
  try {
    const filter = {
      level: req.query.level as string,
      source: req.query.source as string,
      search: req.query.search as string,
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
      limit: parseInt(req.query.limit as string) || 100,
      offset: parseInt(req.query.offset as string) || 0
    };

    const result = await logService.getLogs(filter);
    res.json({
      success: true,
      data: result.logs,
      total: result.total
    });
  } catch (error) {
    console.error('Error fetching system logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system logs'
    });
  }
});

/**
 * @swagger
 * /api/system/logs/stats:
 *   get:
 *     summary: Get system log statistics
 *     tags: [System Logs]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Log statistics retrieved successfully
 */
router.get('/logs/stats', async (req: Request, res: Response) => {
  try {
    const filter = {
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string
    };

    const stats = await logService.getLogStats(filter);
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching log stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch log statistics'
    });
  }
});

/**
 * @swagger
 * /api/system/logs/sources:
 *   get:
 *     summary: Get available log sources
 *     tags: [System Logs]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Log sources retrieved successfully
 */
router.get('/logs/sources', async (req: Request, res: Response) => {
  try {
    const sources = await logService.getLogSources();
    res.json({
      success: true,
      data: sources
    });
  } catch (error) {
    console.error('Error fetching log sources:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch log sources'
    });
  }
});

/**
 * @swagger
 * /api/system/logs/export:
 *   post:
 *     summary: Export system logs
 *     tags: [System Logs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               format:
 *                 type: string
 *                 enum: [json, csv, txt]
 *                 default: json
 *               filter:
 *                 type: object
 *     responses:
 *       200:
 *         description: Logs exported successfully
 */
router.post('/logs/export', async (req: Request, res: Response) => {
  try {
    const { format = 'json', filter = {} } = req.body;
    const filepath = await logService.exportLogs(filter, format);

    res.json({
      success: true,
      data: {
        filepath,
        message: 'Logs exported successfully'
      }
    });
  } catch (error) {
    console.error('Error exporting logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export logs'
    });
  }
});

/**
 * @swagger
 * /api/system/logs/clear:
 *   delete:
 *     summary: Clear old system logs
 *     tags: [System Logs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               olderThanDays:
 *                 type: integer
 *                 default: 30
 *     responses:
 *       200:
 *         description: Old logs cleared successfully
 */
router.delete('/logs/clear', async (req: Request, res: Response) => {
  try {
    const { olderThanDays = 30 } = req.body;
    const clearedCount = await logService.clearLogs(olderThanDays);

    res.json({
      success: true,
      data: {
        clearedCount,
        message: `${clearedCount} old log files cleared`
      }
    });
  } catch (error) {
    console.error('Error clearing logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear logs'
    });
  }
});

export default router;
