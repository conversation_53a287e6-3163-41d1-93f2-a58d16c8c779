'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface ContactSettings {
  phone: string;
  email: string;
  address: string;
  workingHours: string;
  whatsappNumber: string;
  faxNumber?: string;
}

export interface WhatsAppSettings {
  phoneNumber: string;
  defaultMessage: string;
  isEnabled: boolean;
  position: 'bottom-right' | 'bottom-left';
}

export interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpUser: string;
  smtpPassword: string;
  fromEmail: string;
  fromName: string;
}

export interface SecuritySettings {
  maxLoginAttempts: number;
  sessionTimeout: number;
  passwordMinLength: number;
  requireTwoFactor: boolean;
}

export interface SiteSettings {
  contact: ContactSettings;
  whatsapp: WhatsAppSettings;
  email: EmailSettings;
  security: SecuritySettings;
}

interface SettingsContextType {
  settings: SiteSettings;
  isLoading: boolean;
  updateContactSettings: (settings: Partial<ContactSettings>) => Promise<void>;
  updateWhatsAppSettings: (settings: Partial<WhatsAppSettings>) => Promise<void>;
  updateEmailSettings: (settings: Partial<EmailSettings>) => Promise<void>;
  updateSecuritySettings: (settings: Partial<SecuritySettings>) => Promise<void>;
  refreshSettings: () => Promise<void>;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<SiteSettings>({
    contact: {
      phone: '+90 555 123 45 67',
      email: '<EMAIL>',
      address: 'İstanbul, Türkiye',
      workingHours: 'Pazartesi - Cuma: 09:00 - 18:00',
      whatsappNumber: '+90 555 123 45 67',
      faxNumber: '+90 212 123 45 67'
    },
    whatsapp: {
      phoneNumber: '+905551234567',
      defaultMessage: 'Merhaba! Doğal taş ürünleri hakkında bilgi almak istiyorum.',
      isEnabled: true,
      position: 'bottom-right'
    },
    email: {
      smtpHost: 'smtp.gmail.com',
      smtpPort: 587,
      smtpUser: '<EMAIL>',
      smtpPassword: '',
      fromEmail: '<EMAIL>',
      fromName: 'Doğal Taş Pazarı'
    },
    security: {
      maxLoginAttempts: 5,
      sessionTimeout: 30,
      passwordMinLength: 8,
      requireTwoFactor: false
    }
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setIsLoading(true);
    try {
      // Simulate API call - gerçek uygulamada backend'den gelecek
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - gerçek uygulamada API'den gelecek
      setSettings(prevSettings => ({
        ...prevSettings,
        // API'den gelen veriler burada set edilecek
      }));
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateContactSettings = async (newSettings: Partial<ContactSettings>) => {
    try {
      // API call to update contact settings
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setSettings(prev => ({
        ...prev,
        contact: { ...prev.contact, ...newSettings }
      }));
      
      console.log('Contact settings updated:', newSettings);
    } catch (error) {
      console.error('Error updating contact settings:', error);
      throw error;
    }
  };

  const updateWhatsAppSettings = async (newSettings: Partial<WhatsAppSettings>) => {
    try {
      // API call to update WhatsApp settings
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setSettings(prev => ({
        ...prev,
        whatsapp: { ...prev.whatsapp, ...newSettings }
      }));
      
      console.log('WhatsApp settings updated:', newSettings);
    } catch (error) {
      console.error('Error updating WhatsApp settings:', error);
      throw error;
    }
  };

  const updateEmailSettings = async (newSettings: Partial<EmailSettings>) => {
    try {
      // API call to update email settings
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setSettings(prev => ({
        ...prev,
        email: { ...prev.email, ...newSettings }
      }));
      
      console.log('Email settings updated:', newSettings);
    } catch (error) {
      console.error('Error updating email settings:', error);
      throw error;
    }
  };

  const updateSecuritySettings = async (newSettings: Partial<SecuritySettings>) => {
    try {
      // API call to update security settings
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setSettings(prev => ({
        ...prev,
        security: { ...prev.security, ...newSettings }
      }));
      
      console.log('Security settings updated:', newSettings);
    } catch (error) {
      console.error('Error updating security settings:', error);
      throw error;
    }
  };

  const refreshSettings = async () => {
    await loadSettings();
  };

  const value: SettingsContextType = {
    settings,
    isLoading,
    updateContactSettings,
    updateWhatsAppSettings,
    updateEmailSettings,
    updateSecuritySettings,
    refreshSettings
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

export default SettingsProvider;
