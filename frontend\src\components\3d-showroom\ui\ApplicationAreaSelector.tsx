'use client';

import React, { useState, useCallback } from 'react';
import { 
  Square, 
  Home, 
  Layers, 
  Grid3X3,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

export type ApplicationArea = 'floor' | 'wall' | 'ceiling' | 'countertop' | 'backsplash';

export interface ApplicationAreaConfig {
  area: ApplicationArea;
  coverage: 'full' | 'partial' | 'accent';
  pattern: 'single' | 'dual' | 'multi';
  layout?: 'horizontal' | 'vertical' | 'mixed';
}

interface ApplicationAreaSelectorProps {
  selectedArea: ApplicationArea | null;
  onAreaSelect: (config: ApplicationAreaConfig) => void;
  productCount: number;
  className?: string;
}

const APPLICATION_AREAS = [
  {
    id: 'floor' as ApplicationArea,
    name: '<PERSON><PERSON><PERSON>',
    icon: Grid3X3,
    description: 'Yer döşemesi için uygulam<PERSON>',
    color: 'bg-blue-500',
    patterns: ['single', 'dual', 'multi']
  },
  {
    id: 'wall' as ApplicationArea,
    name: '<PERSON><PERSON>',
    icon: Square,
    description: '<PERSON><PERSON> y<PERSON> i<PERSON>in <PERSON>',
    color: 'bg-green-500',
    patterns: ['single', 'dual', 'multi']
  },
  {
    id: 'ceiling' as ApplicationArea,
    name: 'Tavan Kaplama',
    icon: Layers,
    description: 'Tavan yüzeyi için uygulama',
    color: 'bg-purple-500',
    patterns: ['single', 'dual']
  },
  {
    id: 'countertop' as ApplicationArea,
    name: 'Tezgah',
    icon: Home,
    description: 'Mutfak/banyo tezgahı',
    color: 'bg-orange-500',
    patterns: ['single', 'dual']
  },
  {
    id: 'backsplash' as ApplicationArea,
    name: 'Arkalık',
    icon: Square,
    description: 'Mutfak arkalığı',
    color: 'bg-red-500',
    patterns: ['single', 'dual', 'multi']
  }
];

const DUAL_PATTERNS = [
  {
    id: 'half-half',
    name: 'Yarı Yarıya',
    description: 'İki ürün eşit oranda',
    preview: '50% - 50%'
  },
  {
    id: 'dominant-accent',
    name: 'Ana + Vurgu',
    description: 'Bir ana ürün, bir vurgu',
    preview: '70% - 30%'
  },
  {
    id: 'stripe-horizontal',
    name: 'Yatay Şerit',
    description: 'Yatay bantlar halinde',
    preview: 'Yatay'
  },
  {
    id: 'stripe-vertical',
    name: 'Dikey Şerit',
    description: 'Dikey bantlar halinde',
    preview: 'Dikey'
  },
  {
    id: 'checkerboard',
    name: 'Satranç',
    description: 'Satranç tahtası düzeni',
    preview: 'Satranç'
  }
];

export const ApplicationAreaSelector: React.FC<ApplicationAreaSelectorProps> = ({
  selectedArea,
  onAreaSelect,
  productCount,
  className = ''
}) => {
  const [selectedAreaType, setSelectedAreaType] = useState<ApplicationArea | null>(selectedArea);
  const [selectedPattern, setSelectedPattern] = useState<string>('single');
  const [showPatterns, setShowPatterns] = useState(false);

  const handleAreaSelect = useCallback((areaType: ApplicationArea) => {
    setSelectedAreaType(areaType);
    setShowPatterns(true);
    
    // If only one product, auto-select single pattern
    if (productCount === 1) {
      const config: ApplicationAreaConfig = {
        area: areaType,
        coverage: 'full',
        pattern: 'single'
      };
      onAreaSelect(config);
      setShowPatterns(false);
    }
  }, [productCount, onAreaSelect]);

  const handlePatternSelect = useCallback((patternId: string) => {
    if (!selectedAreaType) return;
    
    setSelectedPattern(patternId);
    
    const config: ApplicationAreaConfig = {
      area: selectedAreaType,
      coverage: 'full',
      pattern: productCount === 1 ? 'single' : productCount === 2 ? 'dual' : 'multi',
      layout: patternId.includes('horizontal') ? 'horizontal' : 
              patternId.includes('vertical') ? 'vertical' : 'mixed'
    };
    
    onAreaSelect(config);
  }, [selectedAreaType, productCount, onAreaSelect]);

  const availableAreas = APPLICATION_AREAS.filter(area => 
    area.patterns.includes(productCount === 1 ? 'single' : productCount === 2 ? 'dual' : 'multi')
  );

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
          <Home size={16} className="text-amber-500" />
          Uygulama Alanı Seçin
        </h3>
        <p className="text-xs text-gray-600 mt-1">
          {productCount} ürün için uygulama alanını belirleyin
        </p>
      </div>

      {/* Area Selection */}
      {!showPatterns && (
        <div className="p-4 space-y-3">
          {availableAreas.map((area) => {
            const Icon = area.icon;
            const isSelected = selectedAreaType === area.id;
            
            return (
              <button
                key={area.id}
                onClick={() => handleAreaSelect(area.id)}
                className={`w-full p-3 rounded-lg border-2 transition-all text-left ${
                  isSelected
                    ? 'border-amber-500 bg-amber-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-10 h-10 rounded-lg ${area.color} flex items-center justify-center`}>
                    <Icon size={20} className="text-white" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{area.name}</div>
                    <div className="text-xs text-gray-600">{area.description}</div>
                  </div>
                  
                  <ArrowRight size={16} className="text-gray-400" />
                </div>
              </button>
            );
          })}
        </div>
      )}

      {/* Pattern Selection for Multiple Products */}
      {showPatterns && productCount > 1 && (
        <div className="p-4 space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">
              {APPLICATION_AREAS.find(a => a.id === selectedAreaType)?.name} Tasarımı
            </h4>
            <button
              onClick={() => setShowPatterns(false)}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              Geri
            </button>
          </div>

          {productCount === 2 && (
            <div className="space-y-2">
              <p className="text-xs text-gray-600 mb-3">
                İki ürün için tasarım seçin:
              </p>
              
              {DUAL_PATTERNS.map((pattern) => (
                <button
                  key={pattern.id}
                  onClick={() => handlePatternSelect(pattern.id)}
                  className={`w-full p-3 rounded-lg border transition-all text-left ${
                    selectedPattern === pattern.id
                      ? 'border-amber-500 bg-amber-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900 text-sm">{pattern.name}</div>
                      <div className="text-xs text-gray-600">{pattern.description}</div>
                    </div>
                    <div className="text-xs font-mono bg-gray-100 px-2 py-1 rounded">
                      {pattern.preview}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}

          {productCount > 2 && (
            <div className="text-center py-4">
              <div className="text-sm text-gray-600">
                Çoklu ürün tasarımları yakında eklenecek
              </div>
            </div>
          )}
        </div>
      )}

      {/* Selected Configuration */}
      {selectedAreaType && !showPatterns && (
        <div className="p-4 border-t border-gray-200 bg-green-50">
          <div className="flex items-center gap-2 text-green-700">
            <CheckCircle size={16} />
            <span className="text-sm font-medium">
              {APPLICATION_AREAS.find(a => a.id === selectedAreaType)?.name} seçildi
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApplicationAreaSelector;
