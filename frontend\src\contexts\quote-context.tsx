"use client"

import * as React from "react"

// Teklif <PERSON> Interface'leri
export interface QuoteRequest {
  id: string
  customerId: string
  customerName: string
  customerEmail: string
  products: QuoteRequestProduct[]
  message: string
  status: 'pending' | 'quoted' | 'accepted' | 'rejected' | 'completed'
  createdAt: Date
  updatedAt: Date
  quotes: Quote[]
}

export interface QuoteRequestProduct {
  id: string
  productId: string
  productName: string
  productCategory: string
  productImage: string
  specifications: ProductSpecification[]
}

export interface ProductSpecification {
  id: string
  type: 'sized' | 'slab'
  thickness: string
  width: string
  length: string
  surface: string
  packaging: string
  delivery: string
  area: string
  targetPrice: string
  currency: string
}

// Teklif Interface'leri
export interface Quote {
  id: string
  quoteRequestId: string
  producerId: string
  producerName: string
  producerCompany: string
  producerEmail: string
  items: QuoteItem[]
  totalAmount: number
  currency: string
  validUntil: Date
  terms: string
  status: 'pending' | 'accepted' | 'rejected' | 'expired'
  createdAt: Date
  updatedAt: Date
}

export interface QuoteItem {
  id: string
  productId: string
  productName: string
  specificationId: string
  quantity: number
  unitPrice: number
  totalPrice: number
  currency: string
  deliveryTime: string
  notes: string
}

// Context Interface
interface QuoteContextType {
  // Quote Requests
  quoteRequests: QuoteRequest[]
  customerQuoteRequests: QuoteRequest[]
  producerQuoteRequests: QuoteRequest[]
  
  // Quotes
  quotes: Quote[]
  customerQuotes: Quote[]
  producerQuotes: Quote[]
  
  // Actions
  createQuoteRequest: (request: Omit<QuoteRequest, 'id' | 'createdAt' | 'updatedAt' | 'quotes'>) => Promise<string>
  updateQuoteRequestStatus: (id: string, status: QuoteRequest['status']) => Promise<void>
  
  createQuote: (quote: Omit<Quote, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>
  updateQuoteStatus: (id: string, status: Quote['status']) => Promise<void>
  acceptQuote: (quoteId: string) => Promise<void>
  rejectQuote: (quoteId: string) => Promise<void>
  
  // Getters
  getQuoteRequestById: (id: string) => QuoteRequest | undefined
  getQuoteById: (id: string) => Quote | undefined
  getQuotesByRequestId: (requestId: string) => Quote[]
  
  // Loading states
  isLoading: boolean
  error: string | null
}

const QuoteContext = React.createContext<QuoteContextType | undefined>(undefined)

// Mock Data
const mockQuoteRequests: QuoteRequest[] = [
  {
    id: '1',
    customerId: '1',
    customerName: 'ABC İnşaat Ltd.',
    customerEmail: '<EMAIL>',
    products: [
      {
        id: 'prod-1',
        productId: '1',
        productName: 'Beyaz Mermer',
        productCategory: 'Mermer',
        productImage: '/api/placeholder/300/200',
        specifications: [
          {
            id: 'spec-1-1',
            type: 'sized',
            thickness: '2',
            width: '60',
            length: '120',
            surface: 'Cilalı',
            packaging: 'Kasalı',
            delivery: 'Fabrika',
            area: '50',
            targetPrice: '45',
            currency: 'USD'
          },
          {
            id: 'spec-1-2',
            type: 'sized',
            thickness: '3',
            width: '80',
            length: '120',
            surface: 'Honlu',
            packaging: 'Kasalı',
            delivery: 'Fabrika',
            area: '30',
            targetPrice: '50',
            currency: 'USD'
          }
        ]
      },
      {
        id: 'prod-2',
        productId: '2',
        productName: 'Traverten Bej',
        productCategory: 'Traverten',
        productImage: '/api/placeholder/300/200',
        specifications: [
          {
            id: 'spec-2-1',
            type: 'slab',
            thickness: '2',
            width: '40',
            length: '60',
            surface: 'Fırçalı',
            packaging: 'Paletüstü',
            delivery: 'Liman',
            area: '25',
            targetPrice: '35',
            currency: 'USD'
          }
        ]
      }
    ],
    message: 'Kaliteli ürünler arıyoruz. Fiyat ve teslimat süresi önemli.',
    status: 'quoted',
    createdAt: new Date('2025-06-28'),
    updatedAt: new Date('2025-06-28'),
    quotes: []
  },
  {
    id: '4',
    customerId: '2',
    customerName: 'GHI Dekorasyon',
    customerEmail: '<EMAIL>',
    products: [
      {
        id: 'prod-3',
        productId: '3',
        productName: 'Oniks Yeşil',
        productCategory: 'Oniks',
        productImage: '/api/placeholder/300/200',
        specifications: [
          {
            id: 'spec-3-1',
            type: 'slab',
            thickness: '3',
            width: '30',
            length: '60',
            surface: 'Cilalı',
            packaging: 'Özel ambalaj',
            delivery: 'Liman',
            area: '15',
            targetPrice: '120',
            currency: 'USD'
          },
          {
            id: 'spec-3-2',
            type: 'sized',
            thickness: '2',
            width: '40',
            length: '80',
            surface: 'Cilalı',
            packaging: 'Özel ambalaj',
            delivery: 'Liman',
            area: '20',
            targetPrice: '110',
            currency: 'USD'
          }
        ]
      }
    ],
    message: 'Premium kalite ürünler istiyoruz. Hızlı teslimat gerekli.',
    status: 'pending',
    createdAt: new Date('2025-06-29'),
    updatedAt: new Date('2025-06-29'),
    quotes: []
  },
  {
    id: '4',
    customerId: '4',
    customerName: 'Mega Proje İnşaat A.Ş.',
    customerEmail: '<EMAIL>',
    products: [
      {
        id: 'prod-4',
        productId: '4',
        productName: 'Granit Siyah',
        productCategory: 'Granit',
        productImage: '/api/placeholder/300/200',
        specifications: [
          {
            id: 'spec-4-1',
            type: 'sized',
            thickness: '3',
            width: '60',
            length: '120',
            surface: 'Cilalı',
            packaging: 'Kasalı',
            delivery: 'Şantiye',
            area: '800',
            targetPrice: '75',
            currency: 'USD'
          },
          {
            id: 'spec-4-2',
            type: 'sized',
            thickness: '2',
            width: '40',
            length: '80',
            surface: 'Honlu',
            packaging: 'Kasalı',
            delivery: 'Şantiye',
            area: '400',
            targetPrice: '65',
            currency: 'USD'
          }
        ]
      }
    ],
    message: 'Büyük metrajlı otel projesi için granit ihtiyacımız var. Toplam 1200m² alan. Çoklu teslimat mümkün mü?',
    status: 'pending',
    createdAt: new Date('2025-01-07'),
    updatedAt: new Date('2025-01-07'),
    quotes: []
  }
]

const mockQuotes: Quote[] = [
  {
    id: "quote-1",
    quoteRequestId: "1", // Match with our request ID
    producerId: "1", // Match with our producer ID
    producerName: "Ahmet Taş",
    producerCompany: "Taş Üretim A.Ş.",
    producerEmail: "<EMAIL>",
    items: [
      {
        id: "item-1",
        productId: "1",
        productName: "Traverten Klasik",
        specificationId: "spec-1",
        quantity: 100,
        unitPrice: 22.50,
        totalPrice: 2250,
        currency: "USD",
        deliveryTime: "15 gün",
        notes: "Premium kalite, A sınıf"
      }
    ],
    totalAmount: 2250,
    currency: "USD",
    validUntil: new Date("2025-07-15"),
    terms: "Ödeme: %50 peşin, %50 sevkiyatta. Garanti: 2 yıl.",
    status: "pending",
    createdAt: new Date("2025-06-29"),
    updatedAt: new Date("2025-06-29")
  },
  {
    id: "quote-2",
    quoteRequestId: "req-1",
    producerId: "prod-2",
    producerName: "Fatma Kaya",
    producerCompany: "Kaya Mermer A.Ş.",
    producerEmail: "<EMAIL>",
    items: [
      {
        id: "item-2",
        productId: "1",
        productName: "Traverten Klasik",
        specificationId: "spec-1",
        quantity: 100,
        unitPrice: 24.00,
        totalPrice: 2400,
        currency: "USD",
        deliveryTime: "10 gün",
        notes: "Hızlı teslimat, özel işçilik"
      }
    ],
    totalAmount: 2400,
    currency: "USD",
    validUntil: new Date("2025-07-20"),
    terms: "Ödeme: %30 peşin, %70 sevkiyatta. Garanti: 3 yıl.",
    status: "pending",
    createdAt: new Date("2025-06-29"),
    updatedAt: new Date("2025-06-29")
  }
]

export function QuoteProvider({ children }: { children: React.ReactNode }) {
  const [quoteRequests, setQuoteRequests] = React.useState<QuoteRequest[]>([])
  const [quotes, setQuotes] = React.useState<Quote[]>([])
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)

  // Load data from backend on mount
  React.useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      try {
        // Load quote requests
        const requestsResponse = await fetch('/api/quotes/requests')
        if (requestsResponse.ok) {
          const requestsResult = await requestsResponse.json()
          if (requestsResult.success) {
            const formattedRequests = requestsResult.data.map((req: any) => ({
              ...req,
              createdAt: new Date(req.createdAt),
              updatedAt: new Date(req.updatedAt),
              quotes: req.quotes || []
            }))
            setQuoteRequests(formattedRequests)
          }
        }

        // Load quotes
        const quotesResponse = await fetch('/api/quotes')
        if (quotesResponse.ok) {
          const quotesResult = await quotesResponse.json()
          if (quotesResult.success) {
            const formattedQuotes = quotesResult.data.map((quote: any) => ({
              ...quote,
              createdAt: new Date(quote.createdAt),
              updatedAt: new Date(quote.updatedAt),
              validUntil: new Date(quote.validUntil)
            }))
            setQuotes(formattedQuotes)
          }
        }
      } catch (err) {
        console.error('Error loading data:', err)
        setError('Veriler yüklenirken hata oluştu')
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [])

  // Computed values
  const customerQuoteRequests = React.useMemo(() => 
    quoteRequests.filter(req => req.customerId === "1"), // Mock customer ID
    [quoteRequests]
  )

  const producerQuoteRequests = React.useMemo(() => 
    quoteRequests, // Producers see all requests
    [quoteRequests]
  )

  const customerQuotes = React.useMemo(() => 
    quotes.filter(quote => 
      quoteRequests.find(req => req.id === quote.quoteRequestId && req.customerId === "1")
    ),
    [quotes, quoteRequests]
  )

  const producerQuotes = React.useMemo(() => 
    quotes.filter(quote => quote.producerId === "prod-1"), // Mock producer ID
    [quotes]
  )

  // Actions
  const createQuoteRequest = React.useCallback(async (
    request: Omit<QuoteRequest, 'id' | 'createdAt' | 'updatedAt' | 'quotes'>
  ): Promise<string> => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/quotes/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        throw new Error('Teklif talebi oluşturulamadı')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Teklif talebi oluşturulamadı')
      }

      const newRequest: QuoteRequest = {
        ...result.data,
        createdAt: new Date(result.data.createdAt),
        updatedAt: new Date(result.data.updatedAt),
        quotes: result.data.quotes || []
      }

      setQuoteRequests(prev => [...prev, newRequest])
      setError(null)
      return newRequest.id
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Teklif talebi oluşturulurken hata oluştu'
      setError(errorMessage)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  const updateQuoteRequestStatus = React.useCallback(async (
    id: string,
    status: QuoteRequest['status']
  ): Promise<void> => {
    console.log('QuoteContext: Updating request status:', { id, status })
    setQuoteRequests(prev => {
      const updated = prev.map(req =>
        req.id === id
          ? { ...req, status, updatedAt: new Date() }
          : req
      )
      console.log('QuoteContext: Updated quote requests:', updated)
      return updated
    })
  }, [])

  const createQuote = React.useCallback(async (
    quote: Omit<Quote, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<string> => {
    console.log('QuoteContext: Creating quote:', quote)
    setIsLoading(true)
    try {
      const newQuote: Quote = {
        ...quote,
        id: `quote-${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      console.log('QuoteContext: New quote created:', newQuote)
      setQuotes(prev => {
        const updated = [...prev, newQuote]
        console.log('QuoteContext: Updated quotes array:', updated)
        return updated
      })

      // Update quote request status
      console.log('QuoteContext: Updating request status to quoted for:', quote.quoteRequestId)
      await updateQuoteRequestStatus(quote.quoteRequestId, 'quoted')

      setError(null)
      return newQuote.id
    } catch (err) {
      console.error('QuoteContext: Error creating quote:', err)
      setError('Teklif oluşturulurken hata oluştu')
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [updateQuoteRequestStatus])

  const updateQuoteStatus = React.useCallback(async (
    id: string, 
    status: Quote['status']
  ): Promise<void> => {
    setQuotes(prev => prev.map(quote => 
      quote.id === id 
        ? { ...quote, status, updatedAt: new Date() }
        : quote
    ))
  }, [])

  const acceptQuote = React.useCallback(async (quoteId: string): Promise<void> => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/quotes/quotes/${quoteId}/accept`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Teklif kabul edilemedi')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Teklif kabul edilemedi')
      }

      // Update local state
      const quote = quotes.find(q => q.id === quoteId)
      if (quote) {
        // Accept this quote
        setQuotes(prev => prev.map(q =>
          q.id === quoteId
            ? { ...q, status: 'accepted' as const, updatedAt: new Date() }
            : q
        ))

        // Reject other quotes for the same request
        setQuotes(prev => prev.map(q =>
          q.quoteRequestId === quote.quoteRequestId && q.id !== quoteId && q.status === 'pending'
            ? { ...q, status: 'rejected' as const, updatedAt: new Date() }
            : q
        ))

        // Update request status
        setQuoteRequests(prev => prev.map(req =>
          req.id === quote.quoteRequestId
            ? { ...req, status: 'accepted' as const, updatedAt: new Date() }
            : req
        ))
      }

      setError(null)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Teklif kabul edilirken hata oluştu'
      setError(errorMessage)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [quotes])

  const rejectQuote = React.useCallback(async (quoteId: string): Promise<void> => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/quotes/quotes/${quoteId}/reject`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Teklif reddedilemedi')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Teklif reddedilemedi')
      }

      // Update local state
      setQuotes(prev => prev.map(quote =>
        quote.id === quoteId
          ? { ...quote, status: 'rejected' as const, updatedAt: new Date() }
          : quote
      ))

      setError(null)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Teklif reddedilirken hata oluştu'
      setError(errorMessage)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Getters
  const getQuoteRequestById = React.useCallback((id: string) => 
    quoteRequests.find(req => req.id === id),
    [quoteRequests]
  )

  const getQuoteById = React.useCallback((id: string) => 
    quotes.find(quote => quote.id === id),
    [quotes]
  )

  const getQuotesByRequestId = React.useCallback((requestId: string) => 
    quotes.filter(quote => quote.quoteRequestId === requestId),
    [quotes]
  )

  const value: QuoteContextType = {
    quoteRequests,
    customerQuoteRequests,
    producerQuoteRequests,
    quotes,
    customerQuotes,
    producerQuotes,
    createQuoteRequest,
    updateQuoteRequestStatus,
    createQuote,
    updateQuoteStatus,
    acceptQuote,
    rejectQuote,
    getQuoteRequestById,
    getQuoteById,
    getQuotesByRequestId,
    isLoading,
    error
  }

  return (
    <QuoteContext.Provider value={value}>
      {children}
    </QuoteContext.Provider>
  )
}

export function useQuote() {
  const context = React.useContext(QuoteContext)
  if (context === undefined) {
    throw new Error('useQuote must be used within a QuoteProvider')
  }
  return context
}
