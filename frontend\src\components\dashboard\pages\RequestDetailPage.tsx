'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ArrowLeftIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { useQuote, QuoteRequest } from '@/contexts/quote-context';

interface RequestDetailPageProps {
  requestId: string;
  onNavigate?: (route: string) => void;
}

const RequestDetailPage: React.FC<RequestDetailPageProps> = ({ requestId, onNavigate }) => {
  const { getQuoteRequestById, isLoading } = useQuote();
  const [request, setRequest] = useState<QuoteRequest | null>(null);

  useEffect(() => {
    const foundRequest = getQuoteRequestById(requestId);
    setRequest(foundRequest || null);
  }, [requestId, getQuoteRequestById]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'quoted':
        return <ChatBubbleLeftRightIcon className="h-5 w-5 text-blue-500" />;
      case 'accepted':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <DocumentTextIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor';
      case 'quoted':
        return 'Teklif Alındı';
      case 'accepted':
        return 'Kabul Edildi';
      case 'rejected':
        return 'Reddedildi';
      case 'cancelled':
        return 'İptal Edildi';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'quoted':
        return 'bg-blue-100 text-blue-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-stone-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Talep detayları yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!request) {
    return (
      <div className="text-center py-12">
        <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Talep Bulunamadı</h3>
        <p className="text-gray-600 mb-4">Aradığınız talep bulunamadı veya silinmiş olabilir.</p>
        <button
          onClick={() => onNavigate?.('/customer/requests/active')}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-stone-600 hover:bg-stone-700"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Geri Dön
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => onNavigate?.('/customer/requests/active')}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeftIcon className="h-5 w-5" />
            <span>Geri</span>
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{request.id}. Talep Detayı</h1>
            <p className="text-gray-600 mt-1">
              {request.createdAt && new Date(request.createdAt).toLocaleDateString('tr-TR')}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {getStatusIcon(request.status)}
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(request.status)}`}>
            {getStatusLabel(request.status)}
          </span>
        </div>
      </div>

      {/* Request Info */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-2">Müşteri Bilgileri</h3>
            <div className="space-y-1">
              <p className="text-sm text-gray-600">Ad: {request.customerName}</p>
              <p className="text-sm text-gray-600">E-posta: {request.customerEmail}</p>
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-2">Talep Durumu</h3>
            <div className="space-y-1">
              <p className="text-sm text-gray-600">Durum: {getStatusLabel(request.status)}</p>
              <p className="text-sm text-gray-600">Alınan Teklif: {request.quotes?.length || 0}</p>
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-2">Tarih Bilgileri</h3>
            <div className="space-y-1">
              <p className="text-sm text-gray-600">
                Oluşturulma: {request.createdAt && new Date(request.createdAt).toLocaleDateString('tr-TR')}
              </p>
              <p className="text-sm text-gray-600">
                Güncelleme: {request.updatedAt && new Date(request.updatedAt).toLocaleDateString('tr-TR')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Message */}
      {request.message && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Müşteri Mesajı</h3>
          <div className="bg-blue-50 rounded-lg p-4">
            <p className="text-blue-800">{request.message}</p>
          </div>
        </div>
      )}

      {/* Products Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Ürün Detayları</h3>
          <p className="text-sm text-gray-600 mt-1">Talep edilen ürünler ve özellikleri</p>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ürün
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Alan (m²)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Kalınlık (cm)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ölçüler (cm)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Yüzey İşlemi
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ambalaj
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Teslimat
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Hedef Fiyat
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {request.products.map((product, productIndex) => (
                product.specifications.map((spec, specIndex) => (
                  <tr key={`${product.id}-${spec.id}`} className="hover:bg-gray-50">
                    {specIndex === 0 && (
                      <td 
                        className="px-6 py-4 whitespace-nowrap border-r border-gray-200"
                        rowSpan={product.specifications.length}
                      >
                        <div>
                          <div className="text-sm font-medium text-gray-900">{product.productName}</div>
                          <div className="text-sm text-gray-500">{product.productCategory}</div>
                        </div>
                      </td>
                    )}
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {spec.area || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {spec.thickness || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {spec.type === 'slab' ? 'Plaka' :
                       (spec.width && spec.length ? `${spec.width} x ${spec.length}` : '-')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {spec.surface || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {spec.packaging || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {spec.delivery || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {spec.targetPrice ? `${spec.targetPrice} ${spec.currency}` : '-'}
                    </td>
                  </tr>
                ))
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Quotes Section */}
      {request.quotes && request.quotes.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Alınan Teklifler</h3>
          <div className="space-y-4">
            {request.quotes.map((quote) => (
              <div key={quote.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">{quote.producerName}</h4>
                    <p className="text-sm text-gray-600">{quote.producerCompany}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-green-600">
                      {quote.totalAmount} {quote.currency}
                    </p>
                    <p className="text-sm text-gray-600">
                      Geçerlilik: {new Date(quote.validUntil).toLocaleDateString('tr-TR')}
                    </p>
                  </div>
                </div>
                {quote.terms && (
                  <div className="bg-gray-50 rounded p-3">
                    <p className="text-sm text-gray-700">{quote.terms}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default RequestDetailPage;
