// RFC-301: Escrow Payment Service Implementation
import { createClient, RedisClientType } from 'redis';
import crypto from 'crypto';
import { AnonymousIdService } from '../redis/AnonymousIdService';
import { BiddingNotificationService } from '../redis/BiddingNotificationService';

export interface EscrowPayment {
  id: string;
  orderId: string;
  bidRequestId: string;
  selectedBidId: string;
  customerId: string;
  amount: number;
  totalAmount: number;
  status: 'pending' | 'receipt_uploaded' | 'verified' | 'rejected';
  createdAt: Date;
  expiresAt: Date;
  bankTransferReceipt?: {
    receiptUrl: string;
    uploadedBy: string;
    bankReference: string;
    uploadedAt: Date;
  };
  adminVerification?: {
    verifiedBy: string;
    verifiedAt: Date;
    approved: boolean;
  };
}

export interface BidSelectionData {
  bidRequestId: string;
  selectedBidId: string;
  customerId: string;
  totalAmount: number;
}

export interface ReceiptData {
  receiptUrl: string;
  uploadedBy: string;
  bankReference: string;
}

export interface IdentityRevealData {
  customerId: string;
  producerId: string;
  customerDetails: any;
  producerDetails: any;
}

export class EscrowPaymentService {
  private redis: RedisClientType;
  private anonymousIdService: AnonymousIdService;
  private notificationService: BiddingNotificationService;

  constructor() {
    this.redis = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    this.anonymousIdService = new AnonymousIdService();
    this.notificationService = new BiddingNotificationService();
    
    this.redis.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });
  }

  async connect(): Promise<void> {
    if (!this.redis.isOpen) {
      await this.redis.connect();
    }
    await this.anonymousIdService.connect();
    await this.notificationService.connect();
  }

  async disconnect(): Promise<void> {
    if (this.redis.isOpen) {
      await this.redis.disconnect();
    }
    await this.anonymousIdService.disconnect();
    await this.notificationService.disconnect();
  }

  // Trigger escrow payment after bid selection
  async initializeEscrowPayment(bidSelectionData: BidSelectionData): Promise<EscrowPayment> {
    await this.connect();
    const escrowAmount = bidSelectionData.totalAmount * 0.3; // 30% PRD requirement
    
    const escrowPayment: EscrowPayment = {
      id: crypto.randomUUID(),
      orderId: crypto.randomUUID(),
      bidRequestId: bidSelectionData.bidRequestId,
      selectedBidId: bidSelectionData.selectedBidId,
      customerId: bidSelectionData.customerId,
      amount: escrowAmount,
      totalAmount: bidSelectionData.totalAmount,
      status: 'pending',
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours to pay
    };

    // Cache escrow payment data
    await this.redis.setEx(
      `escrow:${escrowPayment.id}`, 
      24 * 60 * 60, 
      JSON.stringify(escrowPayment)
    );

    // Notify customer about payment requirement
    await this.notifyCustomerPaymentRequired(escrowPayment);

    return escrowPayment;
  }

  // Process bank transfer receipt upload (PRD: dekont yükleme)
  async processBankTransferReceipt(escrowId: string, receiptData: ReceiptData): Promise<void> {
    await this.connect();
    const escrowData = await this.redis.get(`escrow:${escrowId}`);
    if (!escrowData) throw new Error('Escrow payment not found');

    const escrow: EscrowPayment = JSON.parse(escrowData);
    escrow.bankTransferReceipt = {
      ...receiptData,
      uploadedAt: new Date()
    };
    escrow.status = 'receipt_uploaded';

    await this.redis.setEx(`escrow:${escrowId}`, 24 * 60 * 60, JSON.stringify(escrow));

    // Notify admin for verification (PRD: admin kontrol eder)
    await this.notifyAdminForVerification(escrowId, receiptData);
  }

  // Admin verification (PRD: admin onaylarsa ödeme olmüş olur)
  async adminVerifyPayment(escrowId: string, adminId: string, approved: boolean): Promise<void> {
    await this.connect();
    const escrowData = await this.redis.get(`escrow:${escrowId}`);
    if (!escrowData) throw new Error('Escrow payment not found');

    const escrow: EscrowPayment = JSON.parse(escrowData);
    escrow.adminVerification = {
      verifiedBy: adminId,
      verifiedAt: new Date(),
      approved
    };
    escrow.status = approved ? 'verified' : 'rejected';

    await this.redis.setEx(`escrow:${escrowId}`, 7 * 24 * 60 * 60, JSON.stringify(escrow));

    if (approved) {
      // Trigger identity reveal (PRD: bilgi paylaşımı)
      await this.triggerIdentityReveal(escrow);
    } else {
      // Notify customer of rejection
      await this.notifyCustomerPaymentRejected(escrow);
    }
  }

  // Get escrow payment details
  async getEscrowPayment(escrowId: string): Promise<EscrowPayment | null> {
    await this.connect();
    const escrowData = await this.redis.get(`escrow:${escrowId}`);
    return escrowData ? JSON.parse(escrowData) : null;
  }

  // Get pending escrow payments for admin
  async getPendingEscrowPayments(): Promise<EscrowPayment[]> {
    await this.connect();
    const keys = await this.redis.keys('escrow:*');
    const escrows: EscrowPayment[] = [];

    for (const key of keys) {
      const data = await this.redis.get(key);
      if (data) {
        const escrow: EscrowPayment = JSON.parse(data);
        if (escrow.status === 'receipt_uploaded') {
          escrows.push(escrow);
        }
      }
    }

    return escrows.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
  }

  // Identity reveal after payment confirmation
  private async triggerIdentityReveal(escrow: EscrowPayment): Promise<void> {
    // Resolve anonymous IDs to real IDs
    const identities = await this.anonymousIdService.resolveAnonymousIds(
      escrow.bidRequestId, 
      escrow.selectedBidId
    );

    // Create order and reveal identities
    const order = await this.createOrder(escrow, identities);
    
    // Notify both parties with contact details
    const identityRevealData: IdentityRevealData = {
      customerId: identities.customerId || escrow.customerId,
      producerId: identities.producerId || 'unknown',
      customerDetails: {},
      producerDetails: {}
    };
    await this.notifyIdentityReveal(order, identityRevealData);
  }

  private async createOrder(escrow: EscrowPayment, identities: any): Promise<any> {
    // This would integrate with your Prisma client to create the order
    // For now, return mock order
    return {
      id: escrow.orderId,
      customerId: identities.customerId,
      producerId: identities.producerId,
      totalAmount: escrow.totalAmount,
      escrowAmount: escrow.amount,
      status: 'confirmed'
    };
  }

  private async notifyCustomerPaymentRequired(escrow: EscrowPayment): Promise<void> {
    // Notify customer about payment requirement
    console.log(`Customer ${escrow.customerId} needs to pay ${escrow.amount} for escrow ${escrow.id}`);
  }

  private async notifyAdminForVerification(escrowId: string, receiptData: ReceiptData): Promise<void> {
    // Notify admin about receipt upload
    console.log(`Admin verification required for escrow ${escrowId}`);
  }

  private async notifyCustomerPaymentRejected(escrow: EscrowPayment): Promise<void> {
    // Notify customer about payment rejection
    console.log(`Payment rejected for escrow ${escrow.id}`);
  }

  private async notifyIdentityReveal(order: any, identities: IdentityRevealData): Promise<void> {
    // Notify both parties with revealed contact details
    console.log(`Identity revealed for order ${order.id}`);
  }

  // Cleanup expired escrow payments
  async cleanupExpiredEscrows(): Promise<void> {
    await this.connect();
    const keys = await this.redis.keys('escrow:*');

    for (const key of keys) {
      const data = await this.redis.get(key);
      if (data) {
        const escrow: EscrowPayment = JSON.parse(data);
        if (escrow.expiresAt.getTime() < Date.now() && escrow.status === 'pending') {
          await this.redis.del(key);
          console.log(`Expired escrow ${escrow.id} cleaned up`);
        }
      }
    }
  }
}
