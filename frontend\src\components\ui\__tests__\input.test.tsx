import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { axe, toHaveNoViolations } from 'jest-axe'
import { Input } from '../input'

expect.extend(toHaveNoViolations)

describe('Input Component', () => {
  // Basic rendering tests
  describe('Rendering', () => {
    it('renders with default props', () => {
      render(<Input />)
      const input = screen.getByRole('textbox')
      expect(input).toBeInTheDocument()
      expect(input).toHaveClass('w-full')
      expect(input).toHaveClass('border-[var(--gray-300)]')
    })

    it('renders with placeholder', () => {
      render(<Input placeholder="Enter your name" />)
      const input = screen.getByPlaceholderText('Enter your name')
      expect(input).toBeInTheDocument()
    })

    it('renders with custom className', () => {
      render(<Input className="custom-input" />)
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('custom-input')
    })

    it('renders with different input types', () => {
      const { rerender } = render(<Input type="email" />)
      let input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('type', 'email')

      rerender(<Input type="password" />)
      input = screen.getByDisplayValue('')
      expect(input).toHaveAttribute('type', 'password')

      rerender(<Input type="number" />)
      input = screen.getByRole('spinbutton')
      expect(input).toHaveAttribute('type', 'number')
    })
  })

  // Error state tests
  describe('Error State', () => {
    it('renders error state correctly', () => {
      render(<Input error />)
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('border-[var(--error)]')
      expect(input).toHaveClass('focus:border-[var(--error)]')
    })

    it('renders error with helper text', () => {
      render(<Input error helperText="This field is required" />)
      const input = screen.getByRole('textbox')
      const helperText = screen.getByText('This field is required')
      
      expect(input).toHaveClass('border-[var(--error)]')
      expect(helperText).toBeInTheDocument()
      expect(helperText).toHaveClass('text-[var(--error)]')
    })

    it('shows invalid state for HTML5 validation', () => {
      render(<Input type="email" defaultValue="invalid-email" />)
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('invalid:border-[var(--error)]')
    })
  })

  // Helper text tests
  describe('Helper Text', () => {
    it('renders helper text without error', () => {
      render(<Input helperText="This is helpful information" />)
      const helperText = screen.getByText('This is helpful information')
      
      expect(helperText).toBeInTheDocument()
      expect(helperText).toHaveClass('text-[var(--text-secondary)]')
      expect(helperText).not.toHaveClass('text-[var(--error)]')
    })

    it('renders helper text with error styling when error is true', () => {
      render(<Input error helperText="Error message" />)
      const helperText = screen.getByText('Error message')
      
      expect(helperText).toHaveClass('text-[var(--error)]')
    })

    it('does not render helper text when not provided', () => {
      render(<Input />)
      const container = screen.getByRole('textbox').parentElement
      const helperText = container?.querySelector('p')
      
      expect(helperText).not.toBeInTheDocument()
    })
  })

  // State tests
  describe('States', () => {
    it('handles disabled state correctly', () => {
      render(<Input disabled />)
      const input = screen.getByRole('textbox')
      
      expect(input).toBeDisabled()
      expect(input).toHaveClass('disabled:cursor-not-allowed')
      expect(input).toHaveClass('disabled:opacity-50')
    })

    it('handles readonly state correctly', () => {
      render(<Input readOnly defaultValue="Read only value" />)
      const input = screen.getByRole('textbox')
      
      expect(input).toHaveAttribute('readonly')
      expect(input).toHaveValue('Read only value')
    })

    it('handles required state correctly', () => {
      render(<Input required />)
      const input = screen.getByRole('textbox')
      
      expect(input).toBeRequired()
    })
  })

  // Focus and interaction tests
  describe('Focus and Interaction', () => {
    it('handles focus correctly', async () => {
      const user = userEvent.setup()
      render(<Input />)
      const input = screen.getByRole('textbox')
      
      await user.click(input)
      expect(input).toHaveFocus()
      expect(input).toHaveClass('focus:border-[var(--primary-stone)]')
    })

    it('handles blur correctly', async () => {
      const user = userEvent.setup()
      const handleBlur = jest.fn()
      render(<Input onBlur={handleBlur} />)
      const input = screen.getByRole('textbox')
      
      await user.click(input)
      await user.tab()
      
      expect(handleBlur).toHaveBeenCalledTimes(1)
    })

    it('handles typing correctly', async () => {
      const user = userEvent.setup()
      const handleChange = jest.fn()
      render(<Input onChange={handleChange} />)
      const input = screen.getByRole('textbox')
      
      await user.type(input, 'Hello World')
      
      expect(input).toHaveValue('Hello World')
      expect(handleChange).toHaveBeenCalled()
    })
  })

  // Mobile optimization tests
  describe('Mobile Optimization', () => {
    it('has correct font size for mobile to prevent zoom', () => {
      render(<Input />)
      const input = screen.getByRole('textbox')
      
      // Should have text-base for mobile (16px) to prevent iOS zoom
      expect(input).toHaveClass('text-base')
      expect(input).toHaveClass('sm:text-sm')
    })

    it('handles touch interactions', () => {
      render(<Input />)
      const input = screen.getByRole('textbox')
      
      fireEvent.touchStart(input)
      fireEvent.touchEnd(input)
      
      expect(input).toBeInTheDocument()
    })
  })

  // File input tests
  describe('File Input', () => {
    it('handles file input correctly', () => {
      render(<Input type="file" />)
      const input = screen.getByRole('textbox', { hidden: true }) // file inputs are hidden
      
      expect(input).toHaveAttribute('type', 'file')
      expect(input).toHaveClass('file:border-0')
      expect(input).toHaveClass('file:bg-transparent')
    })

    it('styles file input correctly', () => {
      render(<Input type="file" />)
      const input = screen.getByRole('textbox', { hidden: true })
      
      expect(input).toHaveClass('file:text-sm')
      expect(input).toHaveClass('file:font-medium')
    })
  })

  // Accessibility tests
  describe('Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <div>
          <label htmlFor="test-input">Test Input</label>
          <Input id="test-input" />
        </div>
      )
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('supports aria-label', () => {
      render(<Input aria-label="Search products" />)
      const input = screen.getByLabelText('Search products')
      expect(input).toBeInTheDocument()
    })

    it('supports aria-describedby with helper text', () => {
      render(<Input aria-describedby="help" helperText="Helper text" />)
      const input = screen.getByRole('textbox')
      const helperText = screen.getByText('Helper text')
      
      expect(input).toHaveAttribute('aria-describedby', 'help')
      expect(helperText).toBeInTheDocument()
    })

    it('has proper focus indicators', () => {
      render(<Input />)
      const input = screen.getByRole('textbox')
      
      expect(input).toHaveClass('focus:outline-none')
      expect(input).toHaveClass('focus:shadow-[0_0_0_3px_rgb(139_115_85_/_0.1)]')
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      render(
        <div>
          <Input placeholder="First input" />
          <Input placeholder="Second input" />
        </div>
      )
      
      const firstInput = screen.getByPlaceholderText('First input')
      const secondInput = screen.getByPlaceholderText('Second input')
      
      await user.click(firstInput)
      expect(firstInput).toHaveFocus()
      
      await user.tab()
      expect(secondInput).toHaveFocus()
    })
  })

  // Edge cases
  describe('Edge Cases', () => {
    it('handles controlled input correctly', async () => {
      const user = userEvent.setup()
      const ControlledInput = () => {
        const [value, setValue] = React.useState('')
        return (
          <Input 
            value={value} 
            onChange={(e) => setValue(e.target.value)}
            data-testid="controlled-input"
          />
        )
      }
      
      render(<ControlledInput />)
      const input = screen.getByTestId('controlled-input')
      
      await user.type(input, 'controlled')
      expect(input).toHaveValue('controlled')
    })

    it('forwards ref correctly', () => {
      const ref = React.createRef<HTMLInputElement>()
      render(<Input ref={ref} />)
      
      expect(ref.current).toBeInstanceOf(HTMLInputElement)
    })

    it('handles special characters in input', async () => {
      const user = userEvent.setup()
      render(<Input />)
      const input = screen.getByRole('textbox')
      
      await user.type(input, 'Special chars: !@#$%^&*()')
      expect(input).toHaveValue('Special chars: !@#$%^&*()')
    })

    it('handles long text input', async () => {
      const user = userEvent.setup()
      const longText = 'A'.repeat(1000)
      render(<Input />)
      const input = screen.getByRole('textbox')
      
      await user.type(input, longText)
      expect(input).toHaveValue(longText)
    })
  })
})
