'use client'

import * as React from 'react'
import { useRouter } from 'next/navigation'
import { useProducts } from '@/contexts/products-context'
import { useStock } from '@/contexts/stock-context'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ProductFormModal } from '@/components/ui/product-form-modal'
import { DeleteProductModal } from '@/components/ui/delete-product-modal'
import { AddStockModal } from '@/components/ui/add-stock-modal'
import {
  ArrowLeft,
  Package,
  Edit,
  Trash2,
  Plus,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Image as ImageIcon,
  FileText,
  DollarSign,
  Archive,
  Settings,
  Save
} from 'lucide-react'

interface ProductDetailPageProps {
  params: {
    id: string
  }
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
  const router = useRouter()
  const { products, updateProduct, deleteProduct } = useProducts()
  const { addStockItems } = useStock()
  const { producer } = useProducerAuth()
  const [product, setProduct] = React.useState<any>(null)

  // Modal states
  const [isEditModalOpen, setIsEditModalOpen] = React.useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = React.useState(false)
  const [isStockModalOpen, setIsStockModalOpen] = React.useState(false)
  const [isDeleting, setIsDeleting] = React.useState(false)
  const [isAddingStock, setIsAddingStock] = React.useState(false)

  // Status management
  const [isEditingStatus, setIsEditingStatus] = React.useState(false)
  const [newStatus, setNewStatus] = React.useState('')

  React.useEffect(() => {
    const foundProduct = products.find(p => p.id === params.id)
    if (foundProduct) {
      setProduct(foundProduct)
      setNewStatus(foundProduct.status)
    }
  }, [params.id, products])

  // Handler functions
  const handleEditProduct = () => {
    setIsEditModalOpen(true)
  }

  const handleDeleteProduct = () => {
    setIsDeleteModalOpen(true)
  }

  const handleAddStock = () => {
    setIsStockModalOpen(true)
  }

  const handleProductSave = async (productData: any) => {
    try {
      updateProduct(product.id, productData)
      setIsEditModalOpen(false)
      return true
    } catch (error) {
      console.error('Error updating product:', error)
      return false
    }
  }

  const confirmDeleteProduct = async (reason: string) => {
    setIsDeleting(true)
    try {
      deleteProduct(product.id, reason)
      router.push('/producer/products')
    } catch (error) {
      console.error('Error deleting product:', error)
      alert('Ürün silinirken bir hata oluştu')
    } finally {
      setIsDeleting(false)
    }
  }

  const confirmAddStock = async (stockItems: any[]) => {
    setIsAddingStock(true)
    try {
      addStockItems(
        product.id,
        product.name,
        producer?.companyName || 'Bilinmeyen Üretici',
        stockItems
      )
      alert('Stok ürünler admin onayına gönderildi!')
      setIsStockModalOpen(false)
    } catch (error) {
      console.error('Error adding stock:', error)
      alert('Stok eklenirken bir hata oluştu')
    } finally {
      setIsAddingStock(false)
    }
  }

  const handleStatusChange = async () => {
    if (newStatus === product.status) {
      setIsEditingStatus(false)
      return
    }

    try {
      const updates: any = { status: newStatus }

      // If changing to active, set approval status to pending
      if (newStatus === 'active') {
        updates.approvalStatus = 'pending'
        updates.submittedAt = new Date()
      } else {
        // If changing to draft/inactive, remove approval status
        updates.approvalStatus = undefined
        updates.submittedAt = undefined
      }

      updateProduct(product.id, updates)
      setIsEditingStatus(false)

      if (newStatus === 'active') {
        alert('Ürün admin onayına gönderildi!')
      } else {
        alert('Ürün durumu güncellendi!')
      }
    } catch (error) {
      console.error('Error updating product status:', error)
      alert('Durum güncellenirken bir hata oluştu')
      setNewStatus(product.status) // Reset to original status
    }
  }

  if (!product) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Ürün bulunamadı</h2>
          <p className="text-gray-600 mb-4">Aradığınız ürün mevcut değil.</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Geri Dön
          </Button>
        </div>
      </div>
    )
  }

  const getStatusBadge = (status: string, approvalStatus?: string) => {
    if (approvalStatus === 'pending') {
      return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Onay Bekliyor</Badge>
    }
    if (approvalStatus === 'approved') {
      return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Onaylandı</Badge>
    }
    if (approvalStatus === 'rejected') {
      return <Badge className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Reddedildi</Badge>
    }
    if (status === 'active') {
      return <Badge className="bg-green-100 text-green-800">Aktif</Badge>
    }
    if (status === 'draft') {
      return <Badge className="bg-gray-100 text-gray-800">Taslak</Badge>
    }
    return <Badge className="bg-gray-100 text-gray-800">Pasif</Badge>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Geri
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{product.name}</h1>
            <p className="text-gray-600">{product.category}</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          {/* Product Status Management */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">Durum:</span>
            {isEditingStatus ? (
              <div className="flex items-center gap-2">
                <Select value={newStatus} onValueChange={setNewStatus}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Taslak</SelectItem>
                    <SelectItem value="active">Aktif</SelectItem>
                    <SelectItem value="inactive">Pasif</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  size="sm"
                  onClick={handleStatusChange}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Save className="w-3 h-3 mr-1" />
                  Kaydet
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setIsEditingStatus(false)
                    setNewStatus(product.status)
                  }}
                >
                  İptal
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                {getStatusBadge(product.status, product.approvalStatus)}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsEditingStatus(true)}
                  className="text-xs"
                >
                  <Settings className="w-3 h-3 mr-1" />
                  Değiştir
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Product Image and Basic Info */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="w-5 h-5" />
              Ürün Görseli
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden">
              <img
                src={product.image}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'
                }}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="w-5 h-5" />
              Temel Bilgiler
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700">Ürün Adı</label>
              <p className="text-gray-900">{product.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Kategori</label>
              <p className="text-gray-900">{product.category}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Açıklama</label>
              <p className="text-gray-900">{product.description || 'Açıklama eklenmemiş'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Ülke</label>
              <p className="text-gray-900">Türkiye</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Oluşturulma Tarihi</label>
              <p className="text-gray-900">{product.createdAt}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Ürün Durumu</label>
              <div className="mt-1">
                {getStatusBadge(product.status, product.approvalStatus)}
              </div>
            </div>
            {product.approvalStatus === 'rejected' && product.rejectionReason && (
              <div>
                <label className="text-sm font-medium text-gray-700">Red Sebebi</label>
                <p className="text-red-600 text-sm">{product.rejectionReason}</p>
              </div>
            )}
            {product.stock && (
              <div>
                <label className="text-sm font-medium text-gray-700">Stok</label>
                <p className="text-gray-900">{product.stock} {product.unit}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Status Information */}
      {product.approvalStatus === 'pending' && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Clock className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-yellow-800">Admin Onayı Bekleniyor</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Ürününüz admin onayına gönderilmiştir. Onaylandıktan sonra müşteriler tarafından görülebilir olacaktır.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {product.approvalStatus === 'rejected' && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <XCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-red-800">Ürün Reddedildi</h4>
                <p className="text-sm text-red-700 mt-1">
                  Ürününüz admin tarafından reddedilmiştir. Düzenleyerek tekrar gönderebilirsiniz.
                </p>
                {product.rejectionReason && (
                  <div className="mt-2 p-2 bg-red-100 rounded border border-red-200">
                    <p className="text-sm text-red-800">
                      <strong>Red Sebebi:</strong> {product.rejectionReason}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {product.status === 'draft' && (
        <Card className="border-gray-200 bg-gray-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Package className="w-5 h-5 text-gray-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-gray-800">Taslak Ürün</h4>
                <p className="text-sm text-gray-700 mt-1">
                  Bu ürün taslak durumunda. Aktif hale getirmek için durumu değiştirin.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Technical Specifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Teknik Özellikler
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700">Ülke</label>
              <p className="text-gray-900">Türkiye</p>
            </div>
            {product.media?.technicalSpecs?.customSpecs?.map((spec: any, index: number) => (
              <div key={index}>
                <label className="text-sm font-medium text-gray-700">{spec.name}</label>
                <p className="text-gray-900">{spec.value}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Price Lists */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Fiyat Listeleri
          </CardTitle>
        </CardHeader>
        <CardContent>
          {product.media?.dimensionPrices && product.media.dimensionPrices.length > 0 && (
            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3">Ebatlı Ürün Fiyat Listesi</h4>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Kalınlık (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">En (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Boy (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Ambalaj</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Teslimat</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Fiyat</th>
                    </tr>
                  </thead>
                  <tbody>
                    {product.media.dimensionPrices.map((price: any, index: number) => (
                      <tr key={index}>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.thickness}</td>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.width}</td>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.length}</td>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.surfaceFinish}</td>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.packaging}</td>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.delivery}</td>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.price} {price.currency}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {product.media?.slabPrices && product.media.slabPrices.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Plaka Ürün Fiyat Listesi</h4>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Kalınlık (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Ambalaj</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Teslimat</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Fiyat (m²)</th>
                    </tr>
                  </thead>
                  <tbody>
                    {product.media.slabPrices.map((price: any, index: number) => (
                      <tr key={index}>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.thickness}</td>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.surfaceFinish}</td>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.packaging}</td>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.delivery}</td>
                        <td className="border border-gray-300 px-3 py-2 text-sm">{price.price} {price.currency}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {(!product.media?.dimensionPrices || product.media.dimensionPrices.length === 0) && 
           (!product.media?.slabPrices || product.media.slabPrices.length === 0) && (
            <p className="text-gray-500 text-center py-4">Henüz fiyat listesi eklenmemiş</p>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-4">
        <Button
          className="bg-amber-600 hover:bg-amber-700"
          onClick={handleEditProduct}
        >
          <Edit className="w-4 h-4 mr-2" />
          Düzenle
        </Button>
        <Button
          variant="outline"
          className="text-red-600 hover:text-red-700"
          onClick={handleDeleteProduct}
        >
          <Trash2 className="w-4 h-4 mr-2" />
          Sil
        </Button>
        <Button
          variant="outline"
          className="bg-blue-600 hover:bg-blue-700 text-white"
          onClick={handleAddStock}
        >
          <Archive className="w-4 h-4 mr-2" />
          Stok Ekle
        </Button>
      </div>

      {/* Modals */}
      {/* Edit Product Modal */}
      <ProductFormModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={handleProductSave}
        product={product}
        mode="edit"
      />

      {/* Delete Product Modal */}
      <DeleteProductModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDeleteProduct}
        productName={product?.name || ''}
        isLoading={isDeleting}
      />

      {/* Add Stock Modal */}
      <AddStockModal
        isOpen={isStockModalOpen}
        onClose={() => setIsStockModalOpen(false)}
        onSave={confirmAddStock}
        productName={product?.name || ''}
        isLoading={isAddingStock}
      />
    </div>
  )
}
