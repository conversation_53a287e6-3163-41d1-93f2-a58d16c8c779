#!/usr/bin/env node

/**
 * Comprehensive Test Runner for RFC-004 UI/UX Design System
 * 
 * This script runs all tests including:
 * - Unit tests with Jest
 * - Accessibility tests with jest-axe
 * - E2E tests with Playwright
 * - Bundle analysis
 * - Performance audits
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🧪 Starting comprehensive test suite for RFC-004 UI/UX Design System\n')

const results = {
  unit: { passed: false, duration: 0 },
  a11y: { passed: false, duration: 0 },
  e2e: { passed: false, duration: 0 },
  bundle: { passed: false, duration: 0 },
  storybook: { passed: false, duration: 0 }
}

// Helper function to run command and measure time
function runTest(name, command, options = {}) {
  console.log(`🔄 Running ${name}...`)
  const startTime = Date.now()
  
  try {
    execSync(command, { 
      stdio: 'inherit',
      ...options
    })
    
    const duration = Date.now() - startTime
    results[name.toLowerCase().replace(/\s+/g, '')] = { passed: true, duration }
    console.log(`✅ ${name} completed in ${duration}ms\n`)
    return true
  } catch (error) {
    const duration = Date.now() - startTime
    results[name.toLowerCase().replace(/\s+/g, '')] = { passed: false, duration }
    console.error(`❌ ${name} failed after ${duration}ms`)
    console.error(error.message)
    return false
  }
}

// 1. Unit Tests
console.log('📋 Step 1: Unit Tests')
const unitTestsPassed = runTest('Unit Tests', 'npm run test -- --coverage --watchAll=false')

// 2. Accessibility Tests
console.log('📋 Step 2: Accessibility Tests')
const a11yTestsPassed = runTest('A11y Tests', 'npm run test:a11y -- --watchAll=false')

// 3. Build and Bundle Analysis
console.log('📋 Step 3: Bundle Analysis')
const bundleAnalysisPassed = runTest('Bundle Analysis', 'node scripts/analyze-bundle.js')

// 4. Storybook Build Test
console.log('📋 Step 4: Storybook Build')
const storybookPassed = runTest('Storybook Build', 'npm run build-storybook')

// 5. E2E Tests (only if previous tests passed)
let e2eTestsPassed = false
if (unitTestsPassed && a11yTestsPassed) {
  console.log('📋 Step 5: E2E Tests')
  e2eTestsPassed = runTest('E2E Tests', 'npm run e2e')
} else {
  console.log('⏭️  Skipping E2E tests due to previous failures')
}

// Generate comprehensive report
console.log('📊 Generating test report...')

const report = {
  timestamp: new Date().toISOString(),
  summary: {
    total: Object.keys(results).length,
    passed: Object.values(results).filter(r => r.passed).length,
    failed: Object.values(results).filter(r => r.passed === false).length,
    totalDuration: Object.values(results).reduce((sum, r) => sum + r.duration, 0)
  },
  results,
  recommendations: []
}

// Add recommendations based on results
if (!unitTestsPassed) {
  report.recommendations.push('Fix unit test failures before proceeding with deployment')
}

if (!a11yTestsPassed) {
  report.recommendations.push('Address accessibility violations to ensure WCAG 2.1 AA compliance')
}

if (!bundleAnalysisPassed) {
  report.recommendations.push('Review bundle size and optimize large dependencies')
}

if (!e2eTestsPassed && unitTestsPassed && a11yTestsPassed) {
  report.recommendations.push('Investigate E2E test failures in browser environments')
}

if (!storybookPassed) {
  report.recommendations.push('Fix Storybook build issues for component documentation')
}

// Performance recommendations
if (report.summary.totalDuration > 300000) { // 5 minutes
  report.recommendations.push('Consider optimizing test performance - total duration exceeds 5 minutes')
}

// Save report
const reportsDir = path.join(__dirname, '../test-reports')
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true })
}

const reportPath = path.join(reportsDir, `test-report-${Date.now()}.json`)
fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

// Display summary
console.log('\n' + '='.repeat(60))
console.log('📊 TEST SUMMARY')
console.log('='.repeat(60))
console.log(`Total Tests: ${report.summary.total}`)
console.log(`Passed: ${report.summary.passed} ✅`)
console.log(`Failed: ${report.summary.failed} ❌`)
console.log(`Total Duration: ${Math.round(report.summary.totalDuration / 1000)}s`)
console.log(`Report saved: ${reportPath}`)

// Detailed results
console.log('\n📋 Detailed Results:')
Object.entries(results).forEach(([test, result]) => {
  const status = result.passed ? '✅' : '❌'
  const duration = Math.round(result.duration / 1000)
  console.log(`  ${status} ${test.charAt(0).toUpperCase() + test.slice(1)}: ${duration}s`)
})

// Recommendations
if (report.recommendations.length > 0) {
  console.log('\n💡 Recommendations:')
  report.recommendations.forEach((rec, index) => {
    console.log(`  ${index + 1}. ${rec}`)
  })
}

// Quality Gates
console.log('\n🚦 Quality Gates:')
const criticalTestsPassed = unitTestsPassed && a11yTestsPassed
const allTestsPassed = Object.values(results).every(r => r.passed)

if (allTestsPassed) {
  console.log('✅ All tests passed - Ready for deployment')
  process.exit(0)
} else if (criticalTestsPassed) {
  console.log('⚠️  Critical tests passed - Review failed tests before deployment')
  process.exit(1)
} else {
  console.log('❌ Critical tests failed - Do not deploy')
  process.exit(2)
}

// Coverage report summary
function displayCoverageReport() {
  const coverageDir = path.join(__dirname, '../coverage')
  const coverageSummaryPath = path.join(coverageDir, 'coverage-summary.json')
  
  if (fs.existsSync(coverageSummaryPath)) {
    try {
      const coverageData = JSON.parse(fs.readFileSync(coverageSummaryPath, 'utf8'))
      const total = coverageData.total
      
      console.log('\n📈 Coverage Summary:')
      console.log(`  Lines: ${total.lines.pct}%`)
      console.log(`  Functions: ${total.functions.pct}%`)
      console.log(`  Branches: ${total.branches.pct}%`)
      console.log(`  Statements: ${total.statements.pct}%`)
      
      // Coverage recommendations
      if (total.lines.pct < 80) {
        report.recommendations.push('Increase line coverage to at least 80%')
      }
      if (total.functions.pct < 90) {
        report.recommendations.push('Increase function coverage to at least 90%')
      }
    } catch (error) {
      console.log('⚠️  Could not read coverage summary')
    }
  }
}

// Display coverage if unit tests passed
if (unitTestsPassed) {
  displayCoverageReport()
}
