'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  CalculatorIcon,
  PlusIcon,
  DocumentArrowUpIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

interface IncomeExpenseEntry {
  id: string;
  type: 'income' | 'expense';
  category: string;
  amount: number;
  currency: string;
  description: string;
  date: Date;
  receipt?: string;
}

interface Invoice {
  id: string;
  type: 'incoming' | 'outgoing';
  supplier?: string;
  customer?: string;
  amount: number;
  currency: string;
  issueDate?: Date;
  dueDate?: Date;
  status: 'draft' | 'sent' | 'pending' | 'paid' | 'overdue';
}

interface AccountingPageProps {
  onNavigate?: (route: string) => void;
}

const AccountingPage: React.FC<AccountingPageProps> = ({ onNavigate }) => {
  const [entries, setEntries] = useState<IncomeExpenseEntry[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'income-expense' | 'invoices'>('income-expense');
  const [showAddForm, setShowAddForm] = useState(false);
  const [newEntry, setNewEntry] = useState({
    type: 'income' as 'income' | 'expense',
    category: '',
    amount: 0,
    currency: 'USD',
    description: '',
    date: new Date().toISOString().split('T')[0]
  });

  // Mock data
  const mockEntries: IncomeExpenseEntry[] = [
    {
      id: '1',
      type: 'income',
      category: 'Satış Geliri',
      amount: 15000,
      currency: 'USD',
      description: 'Otel projesi mermer satışı',
      date: new Date('2024-01-20')
    },
    {
      id: '2',
      type: 'expense',
      category: 'Alım Maliyeti',
      amount: 8000,
      currency: 'USD',
      description: 'ABC Mermer Ltd. alımı',
      date: new Date('2024-01-18')
    },
    {
      id: '3',
      type: 'expense',
      category: 'Kira',
      amount: 2500,
      currency: 'USD',
      description: 'Depo kirası - Ocak',
      date: new Date('2024-01-01')
    }
  ];

  const mockInvoices: Invoice[] = [
    {
      id: 'INV-001',
      type: 'incoming',
      supplier: 'ABC Mermer Ltd.',
      amount: 8500,
      currency: 'USD',
      dueDate: new Date('2024-02-15'),
      status: 'pending'
    },
    {
      id: 'INV-002',
      type: 'outgoing',
      customer: 'Lüks Otel Projesi',
      amount: 15000,
      currency: 'USD',
      issueDate: new Date('2024-01-20'),
      status: 'paid'
    }
  ];

  useEffect(() => {
    setTimeout(() => {
      setEntries(mockEntries);
      setInvoices(mockInvoices);
      setLoading(false);
    }, 1000);
  }, []);

  const incomeCategories = ['Satış Geliri', 'Kira Geliri', 'Faiz Geliri', 'Diğer'];
  const expenseCategories = ['Alım Maliyeti', 'Kira', 'Maaş', 'Elektrik', 'Su', 'İnternet', 'Yakıt', 'Diğer'];

  const totalIncome = entries.filter(e => e.type === 'income').reduce((sum, e) => sum + e.amount, 0);
  const totalExpense = entries.filter(e => e.type === 'expense').reduce((sum, e) => sum + e.amount, 0);
  const netProfit = totalIncome - totalExpense;

  const handleAddEntry = () => {
    const entry: IncomeExpenseEntry = {
      id: Date.now().toString(),
      type: newEntry.type,
      category: newEntry.category,
      amount: newEntry.amount,
      currency: newEntry.currency,
      description: newEntry.description,
      date: new Date(newEntry.date)
    };
    
    setEntries(prev => [entry, ...prev]);
    setNewEntry({
      type: 'income',
      category: '',
      amount: 0,
      currency: 'USD',
      description: '',
      date: new Date().toISOString().split('T')[0]
    });
    setShowAddForm(false);
  };



  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <CalculatorIcon className="h-8 w-8 text-green-600 mr-3" />
              Ön Muhasebe
            </h1>
            <p className="text-gray-600 mt-2">
              Gelir-gider takibi ve finansal raporlarınızı yönetin
            </p>
          </div>
          
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Yeni Kayıt</span>
          </button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Toplam Gelir</h3>
                <p className="text-3xl font-bold text-green-600">
                  ${totalIncome.toLocaleString()}
                </p>
              </div>
              <ArrowTrendingUpIcon className="h-8 w-8 text-green-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Toplam Gider</h3>
                <p className="text-3xl font-bold text-red-600">
                  ${totalExpense.toLocaleString()}
                </p>
              </div>
              <ChartBarIcon className="h-8 w-8 text-red-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Net Kar</h3>
                <p className={`text-3xl font-bold ${netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${netProfit.toLocaleString()}
                </p>
              </div>
              <CurrencyDollarIcon className={`h-8 w-8 ${netProfit >= 0 ? 'text-green-500' : 'text-red-500'}`} />
            </div>
          </motion.div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'income-expense', label: 'Gelir-Gider', icon: CurrencyDollarIcon },
              { id: 'invoices', label: 'Faturalar', icon: DocumentTextIcon }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      {activeTab === 'income-expense' && (
        <div className="space-y-6">
          {/* Recent Entries */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
          >
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Son Kayıtlar</h3>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                Tümünü Gör
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tarih
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tür
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Kategori
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Açıklama
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tutar
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {entries.slice(0, 5).map((entry) => (
                    <tr key={entry.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {entry.date.toLocaleDateString('tr-TR')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          entry.type === 'income' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {entry.type === 'income' ? 'Gelir' : 'Gider'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {entry.category}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        {entry.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <span className={entry.type === 'income' ? 'text-green-600' : 'text-red-600'}>
                          {entry.type === 'income' ? '+' : '-'}${entry.amount.toLocaleString()}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </motion.div>
        </div>
      )}



      {/* Add Entry Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl p-6 w-full max-w-md mx-4"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Yeni Kayıt Ekle</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tür</label>
                <select
                  value={newEntry.type}
                  onChange={(e) => setNewEntry(prev => ({ ...prev, type: e.target.value as 'income' | 'expense' }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="income">Gelir</option>
                  <option value="expense">Gider</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Kategori</label>
                <select
                  value={newEntry.category}
                  onChange={(e) => setNewEntry(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="">Kategori Seçin</option>
                  {(newEntry.type === 'income' ? incomeCategories : expenseCategories).map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tutar</label>
                <input
                  type="number"
                  value={newEntry.amount}
                  onChange={(e) => setNewEntry(prev => ({ ...prev, amount: Number(e.target.value) }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Açıklama</label>
                <textarea
                  value={newEntry.description}
                  onChange={(e) => setNewEntry(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  rows={3}
                  placeholder="Açıklama girin..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tarih</label>
                <input
                  type="date"
                  value={newEntry.date}
                  onChange={(e) => setNewEntry(prev => ({ ...prev, date: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleAddEntry}
                disabled={!newEntry.category || !newEntry.amount}
                className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                Kaydet
              </button>
              <button
                onClick={() => setShowAddForm(false)}
                className="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors"
              >
                İptal
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default AccountingPage;
