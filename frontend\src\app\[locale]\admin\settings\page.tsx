'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Settings, 
  Shield, 
  DollarSign, 
  Bell, 
  Server, 
  Plug,
  Save,
  RotateCcw,
  Download,
  Upload,
  History,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

// Import modular settings components
import PlatformSettings from './components/PlatformSettings';
import SecuritySettings from './components/SecuritySettings';
import BusinessSettings from './components/BusinessSettings';
import NotificationSettings from './components/NotificationSettings';
import SystemSettings from './components/SystemSettings';
import IntegrationSettings from './components/IntegrationSettings';
import SettingsAuditLog from './components/SettingsAuditLog';

// Import settings context
import { SettingsProvider, useSettings } from './context/SettingsContext';

const AdminSettingsPage = () => {
  return (
    <SettingsProvider>
      <AdminSettingsContent />
    </SettingsProvider>
  );
};

const AdminSettingsContent = () => {
  const { 
    settings, 
    loading, 
    error, 
    hasUnsavedChanges,
    saveSettings,
    resetCategory,
    exportSettings,
    importSettings 
  } = useSettings();

  const [activeTab, setActiveTab] = useState('platform');
  const [showAuditLog, setShowAuditLog] = useState(false);

  const settingsCategories = [
    {
      id: 'platform',
      name: 'Platform',
      description: 'Site bilgileri, tema ve genel ayarlar',
      icon: Settings,
      component: PlatformSettings
    },
    {
      id: 'security',
      name: 'Güvenlik',
      description: 'Kullanıcı güvenliği ve erişim kontrolü',
      icon: Shield,
      component: SecuritySettings
    },
    {
      id: 'business',
      name: 'İş Kuralları',
      description: 'Komisyon, ödeme ve teklif ayarları',
      icon: DollarSign,
      component: BusinessSettings
    },
    {
      id: 'notification',
      name: 'Bildirimler',
      description: 'Email, SMS ve push notification ayarları',
      icon: Bell,
      component: NotificationSettings
    },
    {
      id: 'system',
      name: 'Sistem',
      description: 'Cache, log ve performans ayarları',
      icon: Server,
      component: SystemSettings
    },
    {
      id: 'integration',
      name: 'Entegrasyonlar',
      description: 'Ödeme, lojistik ve 3rd party API ayarları',
      icon: Plug,
      component: IntegrationSettings
    }
  ];

  const handleSave = async () => {
    try {
      await saveSettings();
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  const handleReset = async () => {
    if (confirm(`${activeTab} kategorisindeki tüm ayarları varsayılan değerlere sıfırlamak istediğinizden emin misiniz?`)) {
      try {
        await resetCategory(activeTab as any);
      } catch (error) {
        console.error('Error resetting settings:', error);
      }
    }
  };

  const handleExport = async () => {
    try {
      await exportSettings();
    } catch (error) {
      console.error('Error exporting settings:', error);
    }
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        await importSettings(file);
      } catch (error) {
        console.error('Error importing settings:', error);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Sistem Ayarları</h1>
          <p className="text-gray-600 mt-1">
            Platform yapılandırmasını ve sistem ayarlarını yönetin
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Audit Log Button */}
          <Button
            variant="outline"
            onClick={() => setShowAuditLog(!showAuditLog)}
          >
            <History className="w-4 h-4 mr-2" />
            Değişiklik Geçmişi
          </Button>

          {/* Export Button */}
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            Dışa Aktar
          </Button>

          {/* Import Button */}
          <div className="relative">
            <input
              type="file"
              accept=".json"
              onChange={handleImport}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
            <Button variant="outline">
              <Upload className="w-4 h-4 mr-2" />
              İçe Aktar
            </Button>
          </div>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Unsaved Changes Alert */}
      {hasUnsavedChanges && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Kaydedilmemiş değişiklikleriniz var. Değişiklikleri kaydetmeyi unutmayın.
          </AlertDescription>
        </Alert>
      )}

      {/* Audit Log Modal/Panel */}
      {showAuditLog && (
        <Card>
          <CardHeader>
            <CardTitle>Değişiklik Geçmişi</CardTitle>
            <CardDescription>
              Son yapılan ayar değişikliklerini görüntüleyin
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SettingsAuditLog />
          </CardContent>
        </Card>
      )}

      {/* Main Settings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="flex items-center justify-between">
          <TabsList className="grid w-full grid-cols-6">
            {settingsCategories.map((category) => {
              const IconComponent = category.icon;
              return (
                <TabsTrigger key={category.id} value={category.id} className="flex items-center space-x-2">
                  <IconComponent className="w-4 h-4" />
                  <span className="hidden sm:inline">{category.name}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={loading}
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Sıfırla
            </Button>
            
            <Button
              onClick={handleSave}
              disabled={loading || !hasUnsavedChanges}
              className="relative"
            >
              <Save className="w-4 h-4 mr-2" />
              Kaydet
              {hasUnsavedChanges && (
                <Badge variant="destructive" className="absolute -top-2 -right-2 w-3 h-3 p-0">
                  !
                </Badge>
              )}
            </Button>
          </div>
        </div>

        {/* Settings Content */}
        {settingsCategories.map((category) => {
          const SettingsComponent = category.component;
          return (
            <TabsContent key={category.id} value={category.id} className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <category.icon className="w-6 h-6 text-blue-600" />
                    <div>
                      <CardTitle>{category.name} Ayarları</CardTitle>
                      <CardDescription>{category.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <SettingsComponent />
                </CardContent>
              </Card>
            </TabsContent>
          );
        })}
      </Tabs>

      {/* Footer Info */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <span>Son güncelleme: {new Date().toLocaleString('tr-TR')}</span>
              <Badge variant="outline">
                <CheckCircle className="w-3 h-3 mr-1" />
                Sistem Aktif
              </Badge>
            </div>
            <div className="text-xs">
              RFC-012: Admin Settings System v1.0
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminSettingsPage;
