version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16.1
    container_name: natural-stone-postgres
    environment:
      POSTGRES_DB: natural_stone_marketplace
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - natural-stone-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7.2.0-alpine
    container_name: natural-stone-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - natural-stone-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Elasticsearch (Optional for search)
  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: natural-stone-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - natural-stone-network
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: natural-stone-backend
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/natural_stone_marketplace?schema=public
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - natural-stone-network
    restart: unless-stopped
    volumes:
      - ../backend:/app
      - /app/node_modules

  # Frontend Web App
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: natural-stone-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - natural-stone-network
    restart: unless-stopped
    volumes:
      - ../frontend:/app
      - /app/node_modules

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: natural-stone-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - natural-stone-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:

networks:
  natural-stone-network:
    driver: bridge
