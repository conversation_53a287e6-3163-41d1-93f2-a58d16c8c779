/**
 * Chatbot API Service
 * Handles API calls for chatbot functionality
 */

import { 
  ChatbotResponse, 
  ConversationHistory, 
  ChatbotAnalytics, 
  Agent, 
  EscalationCriteria,
  ConversationStatsResponse,
  StartConversationResponse,
  ApiResponse,
  ChatbotError,
  ChatbotErrorCode
} from '../types/chatbot';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

class ChatbotApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/api/chatbot`;
  }

  /**
   * Make HTTP request with error handling
   */
  private async makeRequest<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ChatbotError(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          response.status === 429 ? ChatbotErrorCode.RATE_LIMIT_EXCEEDED : ChatbotErrorCode.API_ERROR,
          errorData
        );
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new ChatbotError(
          data.message || 'API request failed',
          ChatbotErrorCode.API_ERROR,
          data
        );
      }

      return data.data || data;
    } catch (error) {
      if (error instanceof ChatbotError) {
        throw error;
      }
      
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new ChatbotError(
          'Network error. Please check your connection.',
          ChatbotErrorCode.NETWORK_ERROR,
          error
        );
      }

      throw new ChatbotError(
        'An unexpected error occurred',
        ChatbotErrorCode.API_ERROR,
        error
      );
    }
  }

  /**
   * Start new conversation
   */
  async startConversation(userId?: string, language: string = 'en'): Promise<StartConversationResponse> {
    return this.makeRequest<StartConversationResponse>('/start', {
      method: 'POST',
      body: JSON.stringify({ userId, language }),
    });
  }

  /**
   * Send message to chatbot
   */
  async sendMessage(
    sessionId: string, 
    message: string, 
    userId?: string
  ): Promise<ChatbotResponse> {
    return this.makeRequest<ChatbotResponse>('/message', {
      method: 'POST',
      body: JSON.stringify({ sessionId, message, userId }),
    });
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(
    sessionId: string, 
    limit: number = 20
  ): Promise<ConversationHistory> {
    return this.makeRequest<ConversationHistory>(
      `/conversations/${sessionId}/history?limit=${limit}`
    );
  }

  /**
   * Submit feedback for chatbot response
   */
  async submitFeedback(
    sessionId: string,
    messageId: string,
    rating: number,
    feedback?: string
  ): Promise<void> {
    await this.makeRequest('/feedback', {
      method: 'POST',
      body: JSON.stringify({ sessionId, messageId, rating, feedback }),
    });
  }

  /**
   * Get conversation statistics
   */
  async getConversationStats(sessionId: string): Promise<ConversationStatsResponse> {
    return this.makeRequest<ConversationStatsResponse>(`/conversations/${sessionId}/stats`);
  }

  /**
   * Delete conversation
   */
  async deleteConversation(sessionId: string): Promise<void> {
    await this.makeRequest(`/conversations/${sessionId}`, {
      method: 'DELETE',
    });
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ status: string; timestamp: string; version: string }> {
    return this.makeRequest<{ status: string; timestamp: string; version: string }>('/health');
  }

  // Admin API methods (require authentication)

  /**
   * Get chatbot analytics (Admin only)
   */
  async getAnalytics(token?: string): Promise<ChatbotAnalytics> {
    return this.makeRequest<ChatbotAnalytics>('/analytics', {
      headers: token ? { Authorization: `Bearer ${token}` } : {},
    });
  }

  /**
   * Get active conversations (Admin only)
   */
  async getActiveConversations(token?: string): Promise<{
    conversations: any[];
    count: number;
  }> {
    return this.makeRequest<{
      conversations: any[];
      count: number;
    }>('/conversations', {
      headers: token ? { Authorization: `Bearer ${token}` } : {},
    });
  }

  /**
   * Get escalation criteria (Admin only)
   */
  async getEscalationCriteria(token?: string): Promise<EscalationCriteria> {
    return this.makeRequest<EscalationCriteria>('/escalation-criteria', {
      headers: token ? { Authorization: `Bearer ${token}` } : {},
    });
  }

  /**
   * Update escalation criteria (Admin only)
   */
  async updateEscalationCriteria(
    criteria: Partial<EscalationCriteria>,
    token?: string
  ): Promise<void> {
    await this.makeRequest('/escalation-criteria', {
      method: 'PUT',
      headers: token ? { Authorization: `Bearer ${token}` } : {},
      body: JSON.stringify(criteria),
    });
  }

  /**
   * Get available agents (Admin only)
   */
  async getAvailableAgents(token?: string): Promise<{
    agents: Agent[];
    count: number;
  }> {
    return this.makeRequest<{
      agents: Agent[];
      count: number;
    }>('/agents', {
      headers: token ? { Authorization: `Bearer ${token}` } : {},
    });
  }

  /**
   * Update agent availability (Admin only)
   */
  async updateAgentAvailability(
    agentId: string,
    available: boolean,
    token?: string
  ): Promise<void> {
    await this.makeRequest(`/agents/${agentId}/availability`, {
      method: 'PUT',
      headers: token ? { Authorization: `Bearer ${token}` } : {},
      body: JSON.stringify({ available }),
    });
  }

  // Utility methods

  /**
   * Check if service is available
   */
  async isServiceAvailable(): Promise<boolean> {
    try {
      await this.healthCheck();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages(): string[] {
    return ['en', 'tr', 'ar', 'de', 'fr', 'es', 'it', 'ru', 'zh', 'ja'];
  }

  /**
   * Validate session ID format
   */
  isValidSessionId(sessionId: string): boolean {
    // UUID v4 format validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(sessionId);
  }

  /**
   * Format error message for display
   */
  formatErrorMessage(error: ChatbotError, language: string = 'en'): string {
    const messages = {
      en: {
        [ChatbotErrorCode.SESSION_NOT_FOUND]: 'Session not found. Please start a new conversation.',
        [ChatbotErrorCode.INVALID_MESSAGE]: 'Invalid message format. Please try again.',
        [ChatbotErrorCode.API_ERROR]: 'Service error. Please try again later.',
        [ChatbotErrorCode.NETWORK_ERROR]: 'Network error. Please check your connection.',
        [ChatbotErrorCode.RATE_LIMIT_EXCEEDED]: 'Too many requests. Please wait a moment.',
        [ChatbotErrorCode.SERVICE_UNAVAILABLE]: 'Service temporarily unavailable. Please try again later.',
      },
      tr: {
        [ChatbotErrorCode.SESSION_NOT_FOUND]: 'Oturum bulunamadı. Lütfen yeni bir konuşma başlatın.',
        [ChatbotErrorCode.INVALID_MESSAGE]: 'Geçersiz mesaj formatı. Lütfen tekrar deneyin.',
        [ChatbotErrorCode.API_ERROR]: 'Servis hatası. Lütfen daha sonra tekrar deneyin.',
        [ChatbotErrorCode.NETWORK_ERROR]: 'Ağ hatası. Lütfen bağlantınızı kontrol edin.',
        [ChatbotErrorCode.RATE_LIMIT_EXCEEDED]: 'Çok fazla istek. Lütfen bir dakika bekleyin.',
        [ChatbotErrorCode.SERVICE_UNAVAILABLE]: 'Servis geçici olarak kullanılamıyor. Lütfen daha sonra tekrar deneyin.',
      }
    };

    const languageMessages = messages[language as keyof typeof messages] || messages.en;
    return languageMessages[error.code] || error.message;
  }

  /**
   * Get default suggestions based on language
   */
  getDefaultSuggestions(language: string = 'en'): string[] {
    const suggestions = {
      en: [
        'Tell me about marble types',
        'How does bidding work?',
        'What are your delivery terms?',
        'What payment methods do you accept?',
        'How can I contact support?'
      ],
      tr: [
        'Mermer çeşitleri hakkında bilgi ver',
        'Teklif sistemi nasıl çalışır?',
        'Teslimat koşullarınız nelerdir?',
        'Hangi ödeme yöntemlerini kabul ediyorsunuz?',
        'Destek ekibiyle nasıl iletişime geçebilirim?'
      ],
      ar: [
        'أخبرني عن أنواع الرخام',
        'كيف يعمل نظام المناقصات؟',
        'ما هي شروط التسليم؟',
        'ما هي طرق الدفع المقبولة؟',
        'كيف يمكنني الاتصال بالدعم؟'
      ]
    };

    return suggestions[language as keyof typeof suggestions] || suggestions.en;
  }

  /**
   * Detect language from text (simple heuristic)
   */
  detectLanguage(text: string): string {
    // Simple language detection based on character patterns
    if (/[\u0600-\u06FF]/.test(text)) return 'ar'; // Arabic
    if (/[\u4e00-\u9fff]/.test(text)) return 'zh'; // Chinese
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) return 'ja'; // Japanese
    if (/[çğıöşüÇĞIİÖŞÜ]/.test(text)) return 'tr'; // Turkish
    if (/[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/.test(text)) return 'fr'; // French
    if (/[äöüßÄÖÜ]/.test(text)) return 'de'; // German
    if (/[ñáéíóúü¿¡]/.test(text)) return 'es'; // Spanish
    if (/[àèéìíîòóù]/.test(text)) return 'it'; // Italian
    if (/[абвгдеёжзийклмнопрстуфхцчшщъыьэюя]/.test(text.toLowerCase())) return 'ru'; // Russian
    
    return 'en'; // Default to English
  }
}

// Export singleton instance
export const chatbotApi = new ChatbotApiService();
export default chatbotApi;
