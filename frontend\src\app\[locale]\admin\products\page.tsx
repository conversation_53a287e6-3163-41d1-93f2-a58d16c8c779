'use client'

import React from 'react'
import { useProducts } from '@/contexts/products-context'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ProductStatsModal } from '@/components/admin/ProductStatsModal'
import { ProductStockModal } from '@/components/admin/ProductStockModal'
import {
  Package,
  Users,
  MapPin,
  Building2,
  Eye,
  TrendingUp,
  DollarSign,
  Calendar,
  Search,
  Filter,
  Warehouse
} from 'lucide-react'

// Mock quarry data - gerçek uygulamada API'den gelecek
const mockQuarries = {
  '1': { name: 'Afyon Beyaz Mermer Ocağı', city: 'Afyon', owner: 'Afyon Mermer A.Ş.' },
  '2': { name: 'Denizli Traverten Ocağı', city: 'Denizli', owner: 'Pamukkale Doğaltaş Ltd.' },
  '3': { name: 'Granit Pro Ocağı', city: '<PERSON><PERSON><PERSON>', owner: 'Granit Pro Ltd.' }
}

// Mock producer data - gerçek uygulamada API'den gelecek
const mockProducers = {
  'producer1': { name: 'ABC Mermer Ltd.', companyName: 'ABC Mermer Ltd.', joinedAt: '2024-01-15' },
  'producer2': { name: 'XYZ Doğaltaş A.Ş.', companyName: 'XYZ Doğaltaş A.Ş.', joinedAt: '2024-02-10' },
  'producer3': { name: 'Granit Pro Ltd.', companyName: 'Granit Pro Ltd.', joinedAt: '2024-03-05' }
}

// Mock blocks data - gerçek uygulamada API'den gelecek
const mockBlocks = [
  {
    id: '1',
    name: 'Afyon Beyaz Mermer Blok - A Kalite',
    category: 'Mermer',
    producer: 'Afyon Mermer A.Ş.',
    quarry: 'Afyon Merkez Ocağı',
    dimensions: { length: 2.5, width: 1.8, height: 1.2, volume: 5.4, weight: 14.58 },
    quality: 'A',
    pricing: { pricePerTon: 1200, currency: 'USD' },
    status: 'active',
    createdAt: '2025-06-20',
    views: 45,
    inquiries: 8
  },
  {
    id: '2',
    name: 'Denizli Traverten Blok - B Kalite',
    category: 'Traverten',
    producer: 'Denizli Traverten Ltd.',
    quarry: 'Denizli Ana Ocak',
    dimensions: { length: 3.0, width: 2.0, height: 1.5, volume: 9.0, weight: 24.3 },
    quality: 'B',
    pricing: { pricePerTon: 800, currency: 'USD' },
    status: 'active',
    createdAt: '2025-06-18',
    views: 32,
    inquiries: 5
  },
  {
    id: '3',
    name: 'Muğla Granit Blok - A Kalite',
    category: 'Granit',
    producer: 'Muğla Granit A.Ş.',
    quarry: 'Muğla Granit Ocağı',
    dimensions: { length: 2.2, width: 1.5, height: 1.0, volume: 3.3, weight: 8.91 },
    quality: 'A',
    pricing: { pricePerTon: 1500, currency: 'USD' },
    status: 'sold',
    createdAt: '2025-06-15',
    views: 67,
    inquiries: 12
  }
]

export default function AdminProductsPage() {
  const { products } = useProducts()
  const [activeTab, setActiveTab] = React.useState('products')
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedCategory, setSelectedCategory] = React.useState('all')
  const [selectedQuarry, setSelectedQuarry] = React.useState('all')
  const [showStatsModal, setShowStatsModal] = React.useState(false)
  const [selectedProductForStats, setSelectedProductForStats] = React.useState<any>(null)
  const [showStockModal, setShowStockModal] = React.useState(false)
  const [selectedProductForStock, setSelectedProductForStock] = React.useState<any>(null)

  // Sadece onaylanmış ürünleri göster
  const approvedProducts = products.filter(product => product.approvalStatus === 'approved')

  const filteredProducts = approvedProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory
    const matchesQuarry = selectedQuarry === 'all' || true
    return matchesSearch && matchesCategory && matchesQuarry
  })

  const categories = ['all', 'Mermer', 'Traverten', 'Granit', 'Oniks', 'Limestone']
  const quarries = ['all', 'Afyon', 'Denizli', 'Muğla']

  const getProducerCount = (product: any) => {
    return product.producers?.length || 1
  }

  const getQuarryInfo = (product: any) => {
    const quarryId = product.id === '1' ? '1' : product.id === '2' ? '2' : '3'
    return mockQuarries[quarryId as keyof typeof mockQuarries] || { name: 'Bilinmeyen Ocak', city: 'N/A', owner: 'N/A' }
  }

  const getProducerDetails = (product: any) => {
    return product.producers?.map((producerId: string) => {
      const producer = mockProducers[producerId as keyof typeof mockProducers]
      return producer || { name: 'Bilinmeyen Üretici', companyName: 'N/A', joinedAt: 'N/A' }
    }) || [{ name: product.producer || 'Test Üretici', companyName: product.producer || 'Test Üretici', joinedAt: '2024-01-01' }]
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Ürün Yönetimi</h1>
          <p className="text-gray-600 mt-1">
            Tüm ürünleri, ocakları ve üretici ilişkilerini yönetin
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-lg px-4 py-2">
            <Package className="w-4 h-4 mr-2" />
            {approvedProducts.length} Aktif Ürün
          </Badge>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('products')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'products'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Package className="w-4 h-4 inline mr-2" />
            Ebatlı Ürünler ({approvedProducts.filter(p => p.type !== 'blok').length})
          </button>
          <button
            onClick={() => setActiveTab('blocks')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'blocks'
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Building2 className="w-4 h-4 inline mr-2" />
            Blok Ürünler ({mockBlocks.length})
          </button>
        </nav>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Package className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Ürün</p>
              <p className="text-xl font-semibold">{approvedProducts.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Users className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Aktif Üretici</p>
              <p className="text-xl font-semibold">
                {new Set(approvedProducts.flatMap(p => p.producers || [p.producer])).size}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 rounded-lg">
              <MapPin className="w-5 h-5 text-amber-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Aktif Ocak</p>
              <p className="text-xl font-semibold">{Object.keys(mockQuarries).length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Building2 className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Blok Ürün</p>
              <p className="text-xl font-semibold">{mockBlocks.length}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Filtreler</h3>
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Ürün adı ile arayın..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
              />
            </div>

            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'Tüm Kategoriler' : category}
                </option>
              ))}
            </select>

            <select
              value={selectedQuarry}
              onChange={(e) => setSelectedQuarry(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
            >
              {quarries.map(quarry => (
                <option key={quarry} value={quarry}>
                  {quarry === 'all' ? 'Tüm Ocaklar' : quarry}
                </option>
              ))}
            </select>
          </div>
        </div>
      </Card>

      {/* Content based on active tab */}
      {activeTab === 'products' ? (
        /* Products Grid */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredProducts.map((product) => {
            const quarryInfo = getQuarryInfo(product)
            const producerDetails = getProducerDetails(product)
            const producerCount = getProducerCount(product)

            return (
              <Card key={product.id} className="overflow-hidden">
                <div className="p-4 border-b bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold text-lg text-gray-900">{product.name}</h3>
                      <p className="text-sm text-gray-600 flex items-center gap-1 mt-1">
                        <MapPin className="w-3 h-3" />
                        {quarryInfo.name} • {quarryInfo.city}
                      </p>
                    </div>
                    <Badge variant="outline">{product.category}</Badge>
                  </div>
                </div>

                <div className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Üretici Sayısı:</span>
                      <Badge variant="secondary">{producerCount} Üretici</Badge>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Ocak Sahibi:</span>
                      <span className="text-sm font-medium">{quarryInfo.owner}</span>
                    </div>

                    <div className="flex gap-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => {
                          setSelectedProductForStats(product)
                          setShowStatsModal(true)
                        }}
                      >
                        <TrendingUp className="w-4 h-4 mr-1" />
                        İstatistikler
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => {
                          setSelectedProductForStock(product)
                          setShowStockModal(true)
                        }}
                      >
                        <Warehouse className="w-4 h-4 mr-1" />
                        Stok Detayları
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            )
          })}

          {filteredProducts.length === 0 && (
            <div className="text-center py-12 col-span-2">
              <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Ürün bulunamadı</h3>
              <p className="text-gray-600">
                Arama kriterlerinize uygun ürün bulunamadı.
              </p>
            </div>
          )}
        </div>
      ) : (
        /* Blocks Grid */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {mockBlocks.map((block) => (
            <Card key={block.id} className="overflow-hidden">
              <div className="p-4 border-b bg-orange-50">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="font-semibold text-lg text-gray-900">{block.name}</h3>
                    <p className="text-sm text-gray-600 flex items-center gap-1 mt-1">
                      <Building2 className="w-3 h-3" />
                      {block.producer} • {block.quarry}
                    </p>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <Badge variant="outline" className="bg-orange-100 text-orange-800">
                      🧱 BLOK
                    </Badge>
                    <Badge className={
                      block.status === 'active' ? 'bg-green-100 text-green-800' :
                      block.status === 'sold' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }>
                      {block.status === 'active' ? 'Aktif' :
                       block.status === 'sold' ? 'Satıldı' : block.status}
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="p-4">
                <div className="space-y-4">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">Boyutlar</h4>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Ölçüler:</span>
                        <span className="font-medium">
                          {block.dimensions.length}×{block.dimensions.width}×{block.dimensions.height}m
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Hacim:</span>
                        <span className="font-medium">{block.dimensions.volume} m³</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Ağırlık:</span>
                        <span className="font-medium">{block.dimensions.weight} ton</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Kalite:</span>
                        <Badge className={
                          block.quality === 'A' ? 'bg-emerald-100 text-emerald-800' :
                          block.quality === 'B' ? 'bg-blue-100 text-blue-800' :
                          'bg-orange-100 text-orange-800'
                        }>
                          {block.quality} Kalite
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-600">Fiyat</p>
                      <p className="text-lg font-bold text-gray-900">
                        {block.pricing.currency} {block.pricing.pricePerTon}/ton
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Toplam Değer</p>
                      <p className="text-lg font-bold text-orange-600">
                        {block.pricing.currency} {(block.pricing.pricePerTon * block.dimensions.weight).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center text-sm text-gray-500 pt-2 border-t">
                    <span>{block.views} görüntülenme</span>
                    <span>{block.inquiries} talep</span>
                    <span>{new Date(block.createdAt).toLocaleDateString('tr-TR')}</span>
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="w-4 h-4 mr-1" />
                      Detaylar
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <TrendingUp className="w-4 h-4 mr-1" />
                      İstatistikler
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Modals */}
      <ProductStatsModal
        isOpen={showStatsModal}
        onClose={() => setShowStatsModal(false)}
        product={selectedProductForStats}
      />

      <ProductStockModal
        isOpen={showStockModal}
        onClose={() => setShowStockModal(false)}
        product={selectedProductForStock}
      />
    </div>
  )
}
