import axios, { AxiosInstance } from 'axios';
import { 
  GoogleAdsConfig, 
  GoogleAdsCredentials, 
  GoogleAdsCustomer, 
  GoogleAdsCampaign, 
  GoogleAdsAdGroup, 
  GoogleAdsKeyword, 
  GoogleAdsReport, 
  GoogleAdsResponse,
  GoogleAdsMetrics 
} from './types';

export class GoogleAdsClient {
  private apiClient: AxiosInstance;
  private config: GoogleAdsConfig;
  private credentials: GoogleAdsCredentials | null = null;

  constructor(config: GoogleAdsConfig) {
    this.config = config;
    this.apiClient = axios.create({
      baseURL: 'https://googleads.googleapis.com/v14',
      headers: {
        'Content-Type': 'application/json',
        'developer-token': config.developerToken,
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor to add auth token
    this.apiClient.interceptors.request.use(async (config) => {
      if (!this.credentials || this.isTokenExpired()) {
        await this.refreshAccessToken();
      }
      
      if (this.credentials) {
        config.headers.Authorization = `Bearer ${this.credentials.access_token}`;
      }
      
      return config;
    });

    // Response interceptor for error handling
    this.apiClient.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          await this.refreshAccessToken();
          // Retry the original request
          return this.apiClient.request(error.config);
        }
        return Promise.reject(error);
      }
    );
  }

  private isTokenExpired(): boolean {
    if (!this.credentials) return true;
    return Date.now() >= this.credentials.expiry_date;
  }

  private async refreshAccessToken(): Promise<void> {
    try {
      const response = await axios.post('https://oauth2.googleapis.com/token', {
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        refresh_token: this.config.refreshToken,
        grant_type: 'refresh_token',
      });

      this.credentials = {
        access_token: response.data.access_token,
        refresh_token: this.config.refreshToken,
        scope: response.data.scope || 'https://www.googleapis.com/auth/adwords',
        token_type: response.data.token_type || 'Bearer',
        expiry_date: Date.now() + (response.data.expires_in * 1000),
      };
    } catch (error) {
      console.error('Failed to refresh Google Ads access token:', error);
      throw new Error('Failed to authenticate with Google Ads API');
    }
  }

  async getCustomer(): Promise<GoogleAdsCustomer> {
    const response = await this.apiClient.get<GoogleAdsResponse<GoogleAdsCustomer>>(
      `/customers/${this.config.customerId}`
    );
    
    if (response.data.error) {
      throw new Error(`Google Ads API Error: ${response.data.error.message}`);
    }
    
    return response.data.results?.[0] || {} as GoogleAdsCustomer;
  }

  async getCampaigns(): Promise<GoogleAdsCampaign[]> {
    const query = `
      SELECT 
        campaign.id,
        campaign.name,
        campaign.status,
        campaign.serving_status,
        campaign.advertising_channel_type,
        campaign.start_date,
        campaign.end_date,
        metrics.impressions,
        metrics.clicks,
        metrics.ctr,
        metrics.average_cpc,
        metrics.cost_micros
      FROM campaign
      WHERE campaign.status != 'REMOVED'
      ORDER BY campaign.id
    `;

    const response = await this.apiClient.post<GoogleAdsResponse<GoogleAdsCampaign>>(
      `/customers/${this.config.customerId}/googleAds:searchStream`,
      { query }
    );

    if (response.data.error) {
      throw new Error(`Google Ads API Error: ${response.data.error.message}`);
    }

    return response.data.results || [];
  }

  async getAdGroups(campaignId?: string): Promise<GoogleAdsAdGroup[]> {
    let query = `
      SELECT 
        ad_group.id,
        ad_group.name,
        ad_group.status,
        ad_group.type,
        ad_group.campaign,
        ad_group.cpc_bid_micros,
        metrics.impressions,
        metrics.clicks,
        metrics.ctr,
        metrics.average_cpc,
        metrics.cost_micros
      FROM ad_group
      WHERE ad_group.status != 'REMOVED'
    `;

    if (campaignId) {
      query += ` AND ad_group.campaign = 'customers/${this.config.customerId}/campaigns/${campaignId}'`;
    }

    query += ' ORDER BY ad_group.id';

    const response = await this.apiClient.post<GoogleAdsResponse<GoogleAdsAdGroup>>(
      `/customers/${this.config.customerId}/googleAds:searchStream`,
      { query }
    );

    if (response.data.error) {
      throw new Error(`Google Ads API Error: ${response.data.error.message}`);
    }

    return response.data.results || [];
  }

  async getKeywords(adGroupId?: string): Promise<GoogleAdsKeyword[]> {
    let query = `
      SELECT 
        ad_group_criterion.criterion_id,
        ad_group_criterion.keyword.text,
        ad_group_criterion.keyword.match_type,
        ad_group_criterion.status,
        ad_group_criterion.ad_group,
        ad_group_criterion.cpc_bid_micros,
        metrics.impressions,
        metrics.clicks,
        metrics.ctr,
        metrics.average_cpc,
        metrics.cost_micros
      FROM keyword_view
      WHERE ad_group_criterion.status != 'REMOVED'
    `;

    if (adGroupId) {
      query += ` AND ad_group_criterion.ad_group = 'customers/${this.config.customerId}/adGroups/${adGroupId}'`;
    }

    query += ' ORDER BY ad_group_criterion.criterion_id';

    const response = await this.apiClient.post<GoogleAdsResponse<GoogleAdsKeyword>>(
      `/customers/${this.config.customerId}/googleAds:searchStream`,
      { query }
    );

    if (response.data.error) {
      throw new Error(`Google Ads API Error: ${response.data.error.message}`);
    }

    return response.data.results || [];
  }

  async getPerformanceReport(
    dateFrom: string,
    dateTo: string,
    level: 'campaign' | 'adgroup' | 'keyword' = 'campaign'
  ): Promise<GoogleAdsReport> {
    let query = '';
    
    switch (level) {
      case 'campaign':
        query = `
          SELECT 
            campaign.id,
            campaign.name,
            metrics.impressions,
            metrics.clicks,
            metrics.ctr,
            metrics.average_cpc,
            metrics.cost_micros,
            metrics.conversions,
            metrics.conversion_rate,
            metrics.cost_per_conversion,
            metrics.conversion_value
          FROM campaign
          WHERE segments.date BETWEEN '${dateFrom}' AND '${dateTo}'
            AND campaign.status != 'REMOVED'
          ORDER BY metrics.impressions DESC
        `;
        break;
      case 'adgroup':
        query = `
          SELECT 
            ad_group.id,
            ad_group.name,
            ad_group.campaign,
            metrics.impressions,
            metrics.clicks,
            metrics.ctr,
            metrics.average_cpc,
            metrics.cost_micros,
            metrics.conversions,
            metrics.conversion_rate
          FROM ad_group
          WHERE segments.date BETWEEN '${dateFrom}' AND '${dateTo}'
            AND ad_group.status != 'REMOVED'
          ORDER BY metrics.impressions DESC
        `;
        break;
      case 'keyword':
        query = `
          SELECT 
            ad_group_criterion.criterion_id,
            ad_group_criterion.keyword.text,
            ad_group_criterion.keyword.match_type,
            ad_group_criterion.ad_group,
            metrics.impressions,
            metrics.clicks,
            metrics.ctr,
            metrics.average_cpc,
            metrics.cost_micros,
            metrics.conversions
          FROM keyword_view
          WHERE segments.date BETWEEN '${dateFrom}' AND '${dateTo}'
            AND ad_group_criterion.status != 'REMOVED'
          ORDER BY metrics.impressions DESC
        `;
        break;
    }

    const response = await this.apiClient.post<GoogleAdsResponse>(
      `/customers/${this.config.customerId}/googleAds:searchStream`,
      { query }
    );

    if (response.data.error) {
      throw new Error(`Google Ads API Error: ${response.data.error.message}`);
    }

    return {
      results: response.data.results || [],
      fieldMask: response.data.fieldMask || '',
      nextPageToken: response.data.nextPageToken,
      totalResultsCount: response.data.totalResultsCount,
    };
  }

  async createCampaign(campaignData: Partial<GoogleAdsCampaign>): Promise<GoogleAdsCampaign> {
    const response = await this.apiClient.post<GoogleAdsResponse<GoogleAdsCampaign>>(
      `/customers/${this.config.customerId}/campaigns:mutate`,
      {
        operations: [{
          create: campaignData
        }]
      }
    );

    if (response.data.error) {
      throw new Error(`Google Ads API Error: ${response.data.error.message}`);
    }

    return response.data.results?.[0] || {} as GoogleAdsCampaign;
  }

  async updateCampaign(campaignId: string, updates: Partial<GoogleAdsCampaign>): Promise<GoogleAdsCampaign> {
    const response = await this.apiClient.post<GoogleAdsResponse<GoogleAdsCampaign>>(
      `/customers/${this.config.customerId}/campaigns:mutate`,
      {
        operations: [{
          update: {
            resourceName: `customers/${this.config.customerId}/campaigns/${campaignId}`,
            ...updates
          },
          updateMask: Object.keys(updates).join(',')
        }]
      }
    );

    if (response.data.error) {
      throw new Error(`Google Ads API Error: ${response.data.error.message}`);
    }

    return response.data.results?.[0] || {} as GoogleAdsCampaign;
  }

  async pauseCampaign(campaignId: string): Promise<void> {
    await this.updateCampaign(campaignId, { status: 'PAUSED' });
  }

  async resumeCampaign(campaignId: string): Promise<void> {
    await this.updateCampaign(campaignId, { status: 'ENABLED' });
  }

  async getAccountInfo(): Promise<GoogleAdsCustomer> {
    return this.getCustomer();
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.getCustomer();
      return true;
    } catch (error) {
      console.error('Google Ads connection test failed:', error);
      return false;
    }
  }
}
