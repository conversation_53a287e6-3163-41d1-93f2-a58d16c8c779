'use client';

import React from 'react';
import PurchaseAnalyticsPage from '@/components/dashboard/pages/PurchaseAnalyticsPage';
import { useRouter } from 'next/navigation';

export default function CustomerPurchaseAnalyticsPage() {
  const router = useRouter();
  
  const handleNavigate = (route: string) => {
    router.push(route);
  };

  return <PurchaseAnalyticsPage onNavigate={handleNavigate} />;
}
