import { Router } from 'express';
import { WhatsAppController } from '../../controllers/WhatsAppController';
import { authMiddleware } from '../../middleware/authMiddleware';

const router = Router();
const whatsappController = new WhatsAppController();

/**
 * @route POST /api/whatsapp/payment-instructions
 * @desc Send payment instructions via WhatsApp
 * @access Private (Admin or System)
 */
router.post('/payment-instructions', authMiddleware, whatsappController.sendPaymentInstructions);

/**
 * @route POST /api/whatsapp/order-status
 * @desc Send order status update via WhatsApp
 * @access Private (Admin, Producer, or System)
 */
router.post('/order-status', authMiddleware, whatsappController.sendOrderStatus);

/**
 * @route POST /api/whatsapp/escrow-notification
 * @desc Send escrow notification via WhatsApp
 * @access Private (Admin or System)
 */
router.post('/escrow-notification', authMiddleware, whatsappController.sendEscrowNotification);

/**
 * @route POST /api/whatsapp/support-url
 * @desc Generate customer support WhatsApp URL
 * @access Public
 */
router.post('/support-url', whatsappController.generateSupportURL);

/**
 * @route GET /api/whatsapp/business-url
 * @desc Get business WhatsApp URL
 * @access Public
 */
router.get('/business-url', whatsappController.getBusinessURL);

/**
 * @route GET /api/whatsapp/auto-reply
 * @desc Get auto-reply message
 * @access Public
 */
router.get('/auto-reply', whatsappController.getAutoReply);

/**
 * @route GET /api/whatsapp/config
 * @desc Get WhatsApp configuration
 * @access Private (Admin only)
 */
router.get('/config', authMiddleware, whatsappController.getConfig);

/**
 * @route PUT /api/whatsapp/config
 * @desc Update WhatsApp configuration
 * @access Private (Admin only)
 */
router.put('/config', authMiddleware, whatsappController.updateConfig);

/**
 * @route GET /api/whatsapp/widget-config
 * @desc Get WhatsApp widget configuration
 * @access Public
 */
router.get('/widget-config', whatsappController.getWidgetConfig);

/**
 * @route POST /api/whatsapp/generate-url
 * @desc Generate WhatsApp URL for specific use case
 * @access Private
 */
router.post('/generate-url', authMiddleware, whatsappController.generateURL);

/**
 * @route GET /api/whatsapp/health
 * @desc Get WhatsApp health status
 * @access Private (Admin only)
 */
router.get('/health', authMiddleware, whatsappController.getHealth);

export default router;
