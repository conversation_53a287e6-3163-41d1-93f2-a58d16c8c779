'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { websocketService, Notification, NotificationPreferences } from '../services/websocketService';
import { useAuth } from './auth-context';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  preferences: NotificationPreferences;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  updatePreferences: (preferences: NotificationPreferences) => void;
  loadNotifications: (page?: number, limit?: number) => void;
  loading: boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { user, token } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    emailEnabled: true,
    browserEnabled: true,
    escrowNotifications: true,
    orderNotifications: true,
    quoteNotifications: true,
    systemNotifications: true,
    marketingEmails: false,
  });

  useEffect(() => {
    if (user && token) {
      // Connect to WebSocket
      websocketService.connect(token);

      // Setup event listeners
      const unsubscribeNotification = websocketService.onNotification((notification) => {
        setNotifications(prev => [notification, ...prev]);
      });

      const unsubscribeUnreadCount = websocketService.onUnreadCount((count) => {
        setUnreadCount(count);
      });

      const unsubscribeConnection = websocketService.onConnection((connected) => {
        setIsConnected(connected);
      });

      // Load initial data
      loadNotifications();
      loadPreferences();

      return () => {
        unsubscribeNotification();
        unsubscribeUnreadCount();
        unsubscribeConnection();
      };
    } else {
      // Disconnect if no user
      websocketService.disconnect();
      setIsConnected(false);
      setNotifications([]);
      setUnreadCount(0);
    }
  }, [user, token]);

  const loadNotifications = async (page: number = 1, limit: number = 20) => {
    if (!token) return;

    setLoading(true);
    try {
      const response = await fetch(`${process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000'}/api/notifications?page=${page}&limit=${limit}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (page === 1) {
          setNotifications(data.data.notifications);
        } else {
          setNotifications(prev => [...prev, ...data.data.notifications]);
        }
      }
    } catch (error) {
      console.error('Failed to load notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadPreferences = async () => {
    if (!token) return;

    try {
      const response = await fetch(`${process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000'}/api/notifications/preferences`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPreferences(data.data);
      }
    } catch (error) {
      console.error('Failed to load preferences:', error);
    }
  };

  const loadUnreadCount = async () => {
    if (!token) return;

    try {
      const response = await fetch(`${process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000'}/api/notifications/unread-count`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUnreadCount(data.data.count);
      }
    } catch (error) {
      console.error('Failed to load unread count:', error);
    }
  };

  const markAsRead = async (notificationId: string) => {
    if (!token) return;

    try {
      const response = await fetch(`${process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000'}/api/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Update local state
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === notificationId 
              ? { ...notification, isRead: true }
              : notification
          )
        );
        
        // Also emit to WebSocket for real-time update
        websocketService.markAsRead(notificationId);
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    if (!token) return;

    try {
      const response = await fetch(`${process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000'}/api/notifications/mark-all-read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Update local state
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, isRead: true }))
        );
        setUnreadCount(0);
        
        // Also emit to WebSocket for real-time update
        websocketService.markAllAsRead();
      }
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  const updatePreferences = async (newPreferences: NotificationPreferences) => {
    if (!token) return;

    try {
      const response = await fetch(`${process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000'}/api/notifications/preferences`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newPreferences),
      });

      if (response.ok) {
        setPreferences(newPreferences);
        
        // Also emit to WebSocket for real-time update
        websocketService.updatePreferences(newPreferences);
      }
    } catch (error) {
      console.error('Failed to update preferences:', error);
    }
  };

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isConnected,
    preferences,
    markAsRead,
    markAllAsRead,
    updatePreferences,
    loadNotifications,
    loading,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export default NotificationContext;
