'use client'

import * as React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Package, 
  Calendar, 
  DollarSign, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  Truck,
  Factory,
  MessageSquare,
  Eye
} from 'lucide-react'
import { MultiDeliveryOrder } from '@/types/multi-delivery'
import { getOverallProgress } from '@/data/mock-multi-delivery'

interface CustomerMultiDeliveryDashboardProps {
  order: MultiDeliveryOrder
  onContactSupplier: (orderId: string) => void
  onViewPackageDetails: (packageId: string) => void
  onApproveOrder?: (orderId: string) => void
  onRejectOrder?: (orderId: string, reason: string) => void
}

export function CustomerMultiDeliveryDashboard({
  order,
  onContactSupplier,
  onViewPackageDetails,
  onApproveOrder,
  onRejectOrder
}: CustomerMultiDeliveryDashboardProps) {
  const overallProgress = getOverallProgress(order)
  
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  const getStatusColor = (status: MultiDeliveryOrder['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in_production':
        return 'bg-blue-100 text-blue-800'
      case 'partially_delivered':
        return 'bg-purple-100 text-purple-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: MultiDeliveryOrder['status']) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'confirmed':
        return 'Onaylandı'
      case 'in_production':
        return 'Üretimde'
      case 'partially_delivered':
        return 'Kısmi Teslim'
      case 'completed':
        return 'Tamamlandı'
      case 'cancelled':
        return 'İptal Edildi'
      default:
        return status
    }
  }

  const getPackageStatusText = (status: any) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'in_progress':
        return 'Üretimde'
      case 'completed':
        return 'Tamamlandı'
      case 'paused':
        return 'Duraklatıldı'
      default:
        return status
    }
  }

  const getDeliveryStatusText = (status: any) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'ready':
        return 'Hazır'
      case 'shipped':
        return 'Kargoda'
      case 'delivered':
        return 'Teslim Edildi'
      default:
        return status
    }
  }

  // Calculate delivery statistics
  const deliveryStats = React.useMemo(() => {
    const packages = order.deliveryPackages
    return {
      pending: packages.filter(p => p.deliveryStatus === 'pending').length,
      ready: packages.filter(p => p.deliveryStatus === 'ready').length,
      shipped: packages.filter(p => p.deliveryStatus === 'shipped').length,
      delivered: packages.filter(p => p.deliveryStatus === 'delivered').length
    }
  }, [order.deliveryPackages])

  // Calculate payment statistics
  const paymentStats = React.useMemo(() => {
    const allPayments = order.deliveryPackages.flatMap(pkg => pkg.payments)
    const totalAmount = order.totalAmount
    const paidAmount = allPayments
      .filter(p => p.status === 'paid')
      .reduce((sum, p) => sum + p.amount, 0)
    const overdueAmount = allPayments
      .filter(p => p.status === 'overdue')
      .reduce((sum, p) => sum + p.amount, 0)
    
    return {
      totalAmount,
      paidAmount,
      overdueAmount,
      paymentPercentage: Math.round((paidAmount / totalAmount) * 100),
      overduePayments: allPayments.filter(p => p.status === 'overdue').length
    }
  }, [order])

  return (
    <div className="space-y-6">
      {/* Order Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl flex items-center gap-2">
              <Package className="w-6 h-6" />
              Çoklu Teslimat Siparişi #{order.id}
            </CardTitle>
            <Badge className={getStatusColor(order.status)}>
              {getStatusText(order.status)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Package className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-blue-800">Toplam Miktar</span>
              </div>
              <p className="text-2xl font-bold text-blue-900">{order.totalQuantity} m²</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="w-5 h-5 text-green-600" />
                <span className="font-medium text-green-800">Toplam Tutar</span>
              </div>
              <p className="text-2xl font-bold text-green-900">${order.totalAmount.toLocaleString()}</p>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Truck className="w-5 h-5 text-purple-600" />
                <span className="font-medium text-purple-800">Teslimat Paketleri</span>
              </div>
              <p className="text-2xl font-bold text-purple-900">{order.deliveryPackages.length}</p>
            </div>
            
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="w-5 h-5 text-orange-600" />
                <span className="font-medium text-orange-800">Tahmini Bitiş</span>
              </div>
              <p className="text-lg font-bold text-orange-900">{formatDate(order.estimatedCompletionDate)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Genel İlerleme
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Üretim İlerlemesi</span>
                <span className="text-sm text-gray-600">
                  {overallProgress.completedPackages}/{overallProgress.totalPackages} paket
                </span>
              </div>
              <Progress value={overallProgress.overallPercentage} className="h-3" />
              <div className="text-sm text-gray-500 mt-1">
                %{overallProgress.overallPercentage} tamamlandı
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-sm">
                  <span className="font-medium">{overallProgress.completedPackages}</span> Tamamlandı
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-blue-600" />
                <span className="text-sm">
                  <span className="font-medium">{overallProgress.inProgressPackages}</span> Üretimde
                </span>
              </div>
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-gray-600" />
                <span className="text-sm">
                  <span className="font-medium">{overallProgress.pendingPackages}</span> Bekliyor
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Alert */}
      {paymentStats.overdueAmount > 0 && (
        <div className="bg-red-50 p-4 rounded-lg border border-red-200">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-6 h-6 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-red-800 mb-1">Ödeme Hatırlatması!</h3>
              <p className="text-red-700">
                ${paymentStats.overdueAmount.toLocaleString()} tutarında {paymentStats.overduePayments} ödemeniz gecikmiş durumda. 
                Lütfen en kısa sürede ödeme yapınız.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Order Notes */}
      {order.notes && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Sipariş Notları</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">{order.notes}</p>
          </CardContent>
        </Card>
      )}

      {/* Delivery Packages - Customer View */}
      <div>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Teslimat Paketleri</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {order.deliveryPackages
            .sort((a, b) => a.packageNumber - b.packageNumber)
            .map((pkg) => (
              <Card key={pkg.id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Package className="w-5 h-5" />
                      Paket #{pkg.packageNumber}
                    </CardTitle>
                    <div className="flex gap-2">
                      <Badge className={pkg.productionStatus === 'completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}>
                        {getPackageStatusText(pkg.productionStatus)}
                      </Badge>
                      <Badge className={pkg.deliveryStatus === 'delivered' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                        {getDeliveryStatusText(pkg.deliveryStatus)}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">Miktar:</span> {pkg.quantity} m²
                    </div>
                    <div>
                      <span className="font-medium">Tutar:</span> ${pkg.amount.toLocaleString()}
                    </div>
                    <div>
                      <span className="font-medium">Üretim:</span> {formatDate(pkg.productionStartDate)} - {formatDate(pkg.productionEndDate)}
                    </div>
                    <div>
                      <span className="font-medium">Teslimat:</span> {formatDate(pkg.deliveryDate)}
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-3">
                  {/* Package Progress */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Üretim İlerlemesi</span>
                      <span className="text-sm text-gray-600">
                        {pkg.productionSchedules.filter(s => s.status === 'completed').length}/{pkg.productionSchedules.length} aşama
                      </span>
                    </div>
                    <Progress 
                      value={Math.round((pkg.productionSchedules.filter(s => s.status === 'completed').length / pkg.productionSchedules.length) * 100)} 
                      className="h-2" 
                    />
                  </div>

                  {/* Current Stage */}
                  {pkg.productionSchedules.find(s => s.status === 'in_progress') && (
                    <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                      <h4 className="font-medium text-blue-800 mb-1">Mevcut Aşama</h4>
                      <p className="text-sm text-blue-700">
                        {pkg.productionSchedules.find(s => s.status === 'in_progress')?.stageName}
                      </p>
                    </div>
                  )}

                  {/* Tracking Info */}
                  {pkg.deliverySchedule?.trackingNumber && (
                    <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                      <h4 className="font-medium text-purple-800 mb-1">Kargo Takibi</h4>
                      <p className="text-sm text-purple-700">
                        <span className="font-medium">Takip No:</span> {pkg.deliverySchedule.trackingNumber}
                      </p>
                      {pkg.deliverySchedule.carrierCompany && (
                        <p className="text-sm text-purple-700">
                          <span className="font-medium">Kargo:</span> {pkg.deliverySchedule.carrierCompany}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-2 border-t">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onViewPackageDetails(pkg.id)}
                      className="flex-1"
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      Detayları Gör
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
        </div>
      </div>

      {/* Contact Supplier */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-900 mb-1">Üretici ile İletişim</h3>
              <p className="text-sm text-gray-600">
                Siparişiniz hakkında sorularınız varsa üretici ile iletişime geçebilirsiniz.
              </p>
            </div>
            <Button
              onClick={() => onContactSupplier(order.id)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              İletişime Geç
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
