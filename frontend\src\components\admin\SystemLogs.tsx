// Sistem Logları Bileşeni
'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  FileText,
  Search,
  Filter,
  Download,
  RefreshCw,
  AlertTriangle,
  Info,
  XCircle,
  CheckCircle
} from 'lucide-react';
import { systemService, LogEntry, LogFilter } from '@/services/systemService';
import { toast } from 'react-hot-toast';



interface SystemLogsProps {
  className?: string;
}

export default function SystemLogs({ className = '' }: SystemLogsProps) {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [sources, setSources] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const [sourceFilter, setSourceFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [autoScroll, setAutoScroll] = useState(true);
  const [total, setTotal] = useState(0);
  const logsEndRef = useRef<HTMLDivElement>(null);

  // Load logs and sources
  useEffect(() => {
    loadLogs();
    loadSources();
  }, []);

  // Reload logs when filters change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadLogs();
    }, 500); // Debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, levelFilter, sourceFilter]);

  const loadLogs = async () => {
    try {
      setIsLoading(true);

      const filter: LogFilter = {
        limit: 100,
        offset: 0
      };

      if (searchTerm) filter.search = searchTerm;
      if (levelFilter !== 'all') filter.level = levelFilter;
      if (sourceFilter !== 'all') filter.source = sourceFilter;

      const result = await systemService.getLogs(filter);
      setLogs(result.logs);
      setTotal(result.total);
    } catch (error) {
      console.error('Error loading logs:', error);
      toast.error('Loglar yüklenirken hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSources = async () => {
    try {
      const sourcesData = await systemService.getLogSources();
      setSources(sourcesData);
    } catch (error) {
      console.error('Error loading log sources:', error);
    }
  };

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (autoScroll && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs, autoScroll]);

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
      case 'debug':
        return <CheckCircle className="w-4 h-4 text-gray-500" />;
      default:
        return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'debug':
        return 'bg-gray-50 border-gray-200 text-gray-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const refreshLogs = async () => {
    await loadLogs();
    toast.success('Loglar güncellendi');
  };

  const exportLogs = async () => {
    try {
      const filter: LogFilter = {};
      if (searchTerm) filter.search = searchTerm;
      if (levelFilter !== 'all') filter.level = levelFilter;
      if (sourceFilter !== 'all') filter.source = sourceFilter;

      await systemService.exportLogs(filter, 'txt');
      toast.success('Loglar başarıyla dışa aktarıldı');
    } catch (error) {
      toast.error('Loglar dışa aktarılırken hata oluştu');
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Sistem Logları
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshLogs}
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={exportLogs}
            >
              <Download className="w-4 h-4 mr-2" />
              Dışa Aktar
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 mt-4">
          <div className="flex-1 min-w-[200px]">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Logları ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select value={levelFilter} onValueChange={setLevelFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Seviye" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tüm Seviyeler</SelectItem>
              <SelectItem value="error">Error</SelectItem>
              <SelectItem value="warning">Warning</SelectItem>
              <SelectItem value="info">Info</SelectItem>
              <SelectItem value="debug">Debug</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sourceFilter} onValueChange={setSourceFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Kaynak" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tüm Kaynaklar</SelectItem>
              {sources.map(source => (
                <SelectItem key={source} value={source}>
                  {source}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="text-center py-8">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4" />
              <p>Loglar yükleniyor...</p>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Filtre kriterlerine uygun log bulunamadı</p>
            </div>
          ) : (
            logs.map((log) => (
              <div
                key={log.id}
                className={`p-3 rounded-lg border ${getLevelColor(log.level)}`}
              >
                <div className="flex items-start gap-3">
                  {getLevelIcon(log.level)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className="text-xs">
                        {log.source}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {new Date(log.timestamp).toLocaleString('tr-TR')}
                      </span>
                    </div>
                    <p className="text-sm font-medium">{log.message}</p>
                    {log.details && (
                      <pre className="text-xs mt-2 p-2 bg-black/5 rounded overflow-x-auto">
                        {JSON.stringify(log.details, null, 2)}
                      </pre>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
          <div ref={logsEndRef} />
        </div>

        {/* Auto-scroll toggle */}
        <div className="flex items-center justify-between mt-4 pt-4 border-t">
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="autoScroll"
              checked={autoScroll}
              onChange={(e) => setAutoScroll(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="autoScroll" className="text-sm text-gray-600">
              Otomatik kaydırma
            </label>
          </div>
          <p className="text-xs text-gray-500">
            {logs.length} / {total} log gösteriliyor
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
