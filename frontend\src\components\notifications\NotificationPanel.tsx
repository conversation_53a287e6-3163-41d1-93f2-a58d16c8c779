import React, { useState } from 'react';
import { 
  CheckIcon, 
  XMarkIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { useNotifications } from '../../contexts/NotificationContext';
import NotificationItem from './NotificationItem';
import NotificationSettings from './NotificationSettings';

interface NotificationPanelProps {
  onClose: () => void;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({ onClose }) => {
  const { 
    notifications, 
    unreadCount, 
    markAllAsRead, 
    loadNotifications, 
    loading 
  } = useNotifications();
  
  const [showSettings, setShowSettings] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread'>('all');

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread') {
      return !(notification as any).isRead;
    }
    return true;
  });

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  const handleLoadMore = () => {
    const nextPage = Math.floor(notifications.length / 20) + 1;
    loadNotifications(nextPage, 20);
  };

  if (showSettings) {
    return (
      <NotificationSettings 
        onBack={() => setShowSettings(false)}
        onClose={onClose}
      />
    );
  }

  return (
    <div className="max-h-96 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-semibold text-gray-900">Bildirimler</h3>
          {unreadCount > 0 && (
            <span className="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
              {unreadCount} yeni
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Filter Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setFilter('all')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                filter === 'all'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Tümü
            </button>
            <button
              onClick={() => setFilter('unread')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                filter === 'unread'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Okunmamış
            </button>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-1">
            {unreadCount > 0 && (
              <button
                onClick={handleMarkAllAsRead}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
                title="Tümünü okundu işaretle"
              >
                <CheckIcon className="h-5 w-5" />
              </button>
            )}
            
            <button
              onClick={() => setShowSettings(true)}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
              title="Bildirim ayarları"
            >
              <Cog6ToothIcon className="h-5 w-5" />
            </button>
            
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
              title="Kapat"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Notifications List */}
      <div className="flex-1 overflow-y-auto">
        {loading && notifications.length === 0 ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredNotifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 text-gray-500">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <CheckIcon className="h-8 w-8 text-gray-400" />
            </div>
            <p className="text-sm font-medium">
              {filter === 'unread' ? 'Okunmamış bildirim yok' : 'Henüz bildirim yok'}
            </p>
            <p className="text-xs text-gray-400 mt-1">
              {filter === 'unread' 
                ? 'Tüm bildirimlerinizi okudunuz' 
                : 'Yeni bildirimler burada görünecek'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredNotifications.map((notification) => (
              <NotificationItem
                key={notification.id}
                notification={notification}
              />
            ))}
            
            {/* Load More Button */}
            {notifications.length >= 20 && notifications.length % 20 === 0 && (
              <div className="p-4">
                <button
                  onClick={handleLoadMore}
                  disabled={loading}
                  className="w-full py-2 px-4 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
                >
                  {loading ? 'Yükleniyor...' : 'Daha fazla göster'}
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      {filteredNotifications.length > 0 && (
        <div className="border-t border-gray-200 p-3">
          <button
            onClick={() => {
              // Navigate to full notifications page
              window.location.href = '/notifications';
            }}
            className="w-full text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            Tüm bildirimleri görüntüle
          </button>
        </div>
      )}
    </div>
  );
};

export default NotificationPanel;
