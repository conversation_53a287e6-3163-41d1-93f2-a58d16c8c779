'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';

interface QueryProviderProps {
  children: React.ReactNode;
}

export default function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Time in milliseconds that unused/inactive cache data remains in memory
            gcTime: 1000 * 60 * 60 * 24, // 24 hours
            // Time in milliseconds after data is considered stale
            staleTime: 1000 * 60 * 5, // 5 minutes
            // Retry failed requests
            retry: (failureCount, error: any) => {
              // Don't retry on 4xx errors except 408, 429
              if (error?.statusCode >= 400 && error?.statusCode < 500) {
                if (error?.statusCode === 408 || error?.statusCode === 429) {
                  return failureCount < 2;
                }
                return false;
              }
              // Retry on 5xx errors and network errors
              return failureCount < 3;
            },
            // Retry delay
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
            // Refetch on window focus
            refetchOnWindowFocus: false,
            // Refetch on reconnect
            refetchOnReconnect: true,
          },
          mutations: {
            // Retry failed mutations
            retry: (failureCount, error: any) => {
              // Don't retry on 4xx errors
              if (error?.statusCode >= 400 && error?.statusCode < 500) {
                return false;
              }
              // Retry on 5xx errors and network errors
              return failureCount < 2;
            },
            // Retry delay for mutations
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools
          initialIsOpen={false}
          position="bottom-right"
          buttonPosition="bottom-right"
        />
      )}
    </QueryClientProvider>
  );
}
