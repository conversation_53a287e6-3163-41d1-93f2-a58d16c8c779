import Script from 'next/script';

interface OrganizationData {
  name: string;
  url: string;
  logo: string;
  description: string;
  address: {
    streetAddress: string;
    addressLocality: string;
    addressCountry: string;
  };
  contactPoint: {
    telephone: string;
    email: string;
    contactType: string;
  };
}

interface WebsiteData {
  name: string;
  url: string;
  description: string;
  potentialAction: {
    target: string;
    queryInput: string;
  };
}

interface ProductData {
  name: string;
  description: string;
  image: string;
  category: string;
  offers: {
    price: string;
    priceCurrency: string;
    availability: string;
  };
}

export function OrganizationStructuredData() {
  const organizationData: OrganizationData = {
    name: "Türkiye Doğal Taş Pazarı",
    url: "https://dogaltaspazari.com",
    logo: "https://dogaltaspazari.com/logo.png",
    description: "Türkiye'nin en büyük doğal taş B2B pazarı. Rekabetçi teklif sistemi ile mermer, granit, traverten ve oniks ürünlerine ulaşın.",
    address: {
      streetAddress: "Atatürk Mahallesi, Doğal Taş Caddesi No:123",
      addressLocality: "Afyonkarahisar",
      addressCountry: "TR"
    },
    contactPoint: {
      telephone: "+90-272-123-45-67",
      email: "<EMAIL>",
      contactType: "customer service"
    }
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: organizationData.name,
    url: organizationData.url,
    logo: organizationData.logo,
    description: organizationData.description,
    address: {
      "@type": "PostalAddress",
      streetAddress: organizationData.address.streetAddress,
      addressLocality: organizationData.address.addressLocality,
      addressCountry: organizationData.address.addressCountry
    },
    contactPoint: {
      "@type": "ContactPoint",
      telephone: organizationData.contactPoint.telephone,
      email: organizationData.contactPoint.email,
      contactType: organizationData.contactPoint.contactType,
      availableLanguage: ["Turkish", "English"]
    },
    sameAs: [
      "https://www.linkedin.com/company/dogaltaspazari",
      "https://twitter.com/dogaltaspazari",
      "https://www.facebook.com/dogaltaspazari"
    ]
  };

  return (
    <Script
      id="organization-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}

export function WebsiteStructuredData() {
  const websiteData: WebsiteData = {
    name: "Türkiye Doğal Taş Pazarı",
    url: "https://dogaltaspazari.com",
    description: "Türkiye'nin en büyük doğal taş B2B pazarı",
    potentialAction: {
      target: "https://dogaltaspazari.com/products?search={search_term_string}",
      queryInput: "required name=search_term_string"
    }
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: websiteData.name,
    url: websiteData.url,
    description: websiteData.description,
    potentialAction: {
      "@type": "SearchAction",
      target: websiteData.potentialAction.target,
      "query-input": websiteData.potentialAction.queryInput
    }
  };

  return (
    <Script
      id="website-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}

export function ProductStructuredData({ product }: { product: ProductData }) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: product.name,
    description: product.description,
    image: product.image,
    category: product.category,
    brand: {
      "@type": "Brand",
      name: "Türkiye Doğal Taş Pazarı"
    },
    offers: {
      "@type": "Offer",
      price: product.offers.price,
      priceCurrency: product.offers.priceCurrency,
      availability: product.offers.availability,
      seller: {
        "@type": "Organization",
        name: "Türkiye Doğal Taş Pazarı"
      }
    }
  };

  return (
    <Script
      id="product-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}

export function BreadcrumbStructuredData({ items }: { items: Array<{ name: string; url: string }> }) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  };

  return (
    <Script
      id="breadcrumb-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}
