"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "./button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "./card"
import { Badge } from "./badge"
import {
  Eye,
  X,
  User,
  Package,
  Calendar,
  DollarSign,
  MapPin,
  FileText,
  Clock,
  XCircle
} from "lucide-react"

interface RequestDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  request: any
}

export function RequestDetailsModal({ isOpen, onClose, request }: RequestDetailsModalProps) {
  if (!isOpen || !request) return null

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'quoted':
        return 'bg-blue-100 text-blue-800'
      case 'accepted':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'quoted':
        return '<PERSON><PERSON><PERSON><PERSON>'
      case 'accepted':
        return 'Kabul Edildi'
      case 'rejected':
        return 'Reddedildi'
      default:
        return 'Bilinmiyor'
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Eye className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold text-gray-900">Talep Detayları</h2>
              <p className="text-gray-600">
                Talep #{request.id} - {request.customerName}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Badge className={getStatusColor(request.status)}>
              {getStatusText(request.status)}
            </Badge>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-6">
            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Müşteri Bilgileri
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Şirket Adı</label>
                    <p className="text-base font-medium">{request.customerName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">E-posta</label>
                    <p className="text-base">{request.customerEmail}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Teslimat Lokasyonu</label>
                    <p className="text-base flex items-center gap-1">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      {request.deliveryLocation}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Product Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  Ürün Bilgileri
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Ürün Adı</label>
                    <p className="text-base font-medium">{request.productName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Miktar</label>
                    <p className="text-base">{request.quantity} {request.unit}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Hedef Fiyat</label>
                    <p className="text-base flex items-center gap-1">
                      <DollarSign className="w-4 h-4 text-gray-400" />
                      ${request.targetPrice} / {request.unit}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Products and Specifications */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Talep Edilen Ürünler ve Özellikler
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {request.products?.map((product: any, index: number) => (
                    <div key={product.id} className="border rounded-lg p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <img
                          src={product.productImage}
                          alt={product.productName}
                          className="w-16 h-16 object-cover rounded"
                        />
                        <div>
                          <h4 className="font-medium">{product.productName}</h4>
                          <p className="text-sm text-gray-500">{product.productCategory}</p>
                        </div>
                      </div>

                      <div className="space-y-3">
                        {product.specifications?.map((spec: any, specIndex: number) => (
                          <div key={spec.id} className="bg-gray-50 p-3 rounded">
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                              <div>
                                <label className="font-medium text-gray-600">Tip</label>
                                <p>{spec.type === 'sized' ? 'Ebatlı' : 'Plaka'}</p>
                              </div>
                              <div>
                                <label className="font-medium text-gray-600">Kalınlık</label>
                                <p>{spec.thickness} cm</p>
                              </div>
                              <div>
                                <label className="font-medium text-gray-600">Boyutlar</label>
                                <p>{spec.width} x {spec.height} cm</p>
                              </div>
                              <div>
                                <label className="font-medium text-gray-600">Alan</label>
                                <p>{spec.area} m²</p>
                              </div>
                            </div>
                            {spec.notes && (
                              <div className="mt-2">
                                <label className="font-medium text-gray-600 text-sm">Notlar</label>
                                <p className="text-sm text-gray-700">{spec.notes}</p>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Request Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Talep Detayları
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Talep Tarihi</label>
                      <p className="text-base">{request.requestDate}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500 flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        Son Teklif Tarihi
                      </label>
                      <p className="text-base font-medium text-red-600">{request.deadline}</p>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-500">Açıklama</label>
                    <p className="text-base mt-1 p-3 bg-gray-50 rounded-md">
                      {request.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Rejection Details (if rejected) */}
            {request.status === 'rejected' && request.rejectionReason && (
              <Card className="bg-red-50 border-red-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-800">
                    <XCircle className="w-5 h-5" />
                    Red Detayları
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-red-700">Red Tarihi</label>
                      <p className="text-base text-red-900">{request.rejectedDate}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-red-700">Red Sebebi</label>
                      <p className="text-base text-red-900 p-3 bg-red-100 rounded-md">{request.rejectionReason}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-red-700">Yeniden Teklif Durumu</label>
                      <p className="text-base text-red-900">
                        {request.canResubmit ? '✅ Yeniden teklif verilebilir' : '❌ Yeniden teklif verilemez'}
                      </p>
                    </div>
                    {request.lastContactDate && (
                      <div>
                        <label className="text-sm font-medium text-red-700">Son İletişim Tarihi</label>
                        <p className="text-base text-red-900">{request.lastContactDate}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end p-6 border-t border-gray-200">
          <Button onClick={onClose}>
            Kapat
          </Button>
        </div>
      </div>
    </div>
  )
}
