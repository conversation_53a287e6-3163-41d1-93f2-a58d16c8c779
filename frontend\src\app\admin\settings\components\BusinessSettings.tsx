'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DollarSign, Clock, CheckCircle, AlertTriangle, Calculator } from 'lucide-react';
import { useSettings } from '../context/SettingsContext';

const BusinessSettings = () => {
  const { getSettingValue, updateSetting } = useSettings();

  // Business settings values
  const commissionRateM2 = getSettingValue('business', 'commissionRateM2') || 1.0;
  const commissionRateTon = getSettingValue('business', 'commissionRateTon') || 10.0;
  const upfrontPaymentPercentage = getSettingValue('business', 'upfrontPaymentPercentage') || 30;
  const quoteValidityDays = getSettingValue('business', 'quoteValidityDays') || 7;
  const autoQuoteExpiry = getSettingValue('business', 'autoQuoteExpiry') || true;
  const minimumOrderValueM2 = getSettingValue('business', 'minimumOrderValueM2') || 100;
  const minimumOrderValueTon = getSettingValue('business', 'minimumOrderValueTon') || 1;
  const maxQuoteRequestsPerDay = getSettingValue('business', 'maxQuoteRequestsPerDay') || 10;
  const producerApprovalRequired = getSettingValue('business', 'producerApprovalRequired') || true;
  const productApprovalRequired = getSettingValue('business', 'productApprovalRequired') || true;

  // Calculate example commission
  const exampleOrderM2 = 1000; // 1000 m²
  const exampleOrderTon = 50; // 50 ton
  const exampleCommissionM2 = exampleOrderM2 * commissionRateM2;
  const exampleCommissionTon = exampleOrderTon * commissionRateTon;

  return (
    <div className="space-y-6">
      {/* Commission Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <DollarSign className="w-5 h-5 text-green-600" />
            <CardTitle>Komisyon Ayarları</CardTitle>
          </div>
          <CardDescription>
            Platform komisyon oranlarını ve ödeme koşullarını yapılandırın
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="commissionRateM2">M² Başına Komisyon ($)</Label>
              <Input
                id="commissionRateM2"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={commissionRateM2}
                onChange={(e) => updateSetting('business', 'commissionRateM2', parseFloat(e.target.value) || 0)}
              />
              <p className="text-xs text-gray-500">
                Her m² satış için alınacak komisyon miktarı
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="commissionRateTon">Ton Başına Komisyon ($)</Label>
              <Input
                id="commissionRateTon"
                type="number"
                step="1"
                min="0"
                max="1000"
                value={commissionRateTon}
                onChange={(e) => updateSetting('business', 'commissionRateTon', parseFloat(e.target.value) || 0)}
              />
              <p className="text-xs text-gray-500">
                Her ton blok satışı için alınacak komisyon miktarı
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="upfrontPaymentPercentage">Ön Ödeme Yüzdesi (%)</Label>
            <Input
              id="upfrontPaymentPercentage"
              type="number"
              min="10"
              max="100"
              value={upfrontPaymentPercentage}
              onChange={(e) => updateSetting('business', 'upfrontPaymentPercentage', parseInt(e.target.value) || 30)}
            />
            <p className="text-xs text-gray-500">
              Sipariş onayından sonra müşteriden istenecek ön ödeme yüzdesi
            </p>
          </div>

          {/* Commission Calculator */}
          <Alert>
            <Calculator className="h-4 w-4" />
            <AlertDescription>
              <strong>Komisyon Hesaplama Örneği:</strong>
              <br />
              • {exampleOrderM2} m² sipariş = ${exampleCommissionM2.toFixed(2)} komisyon
              <br />
              • {exampleOrderTon} ton sipariş = ${exampleCommissionTon.toFixed(2)} komisyon
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Quote Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Clock className="w-5 h-5 text-blue-600" />
            <CardTitle>Teklif Ayarları</CardTitle>
          </div>
          <CardDescription>
            Teklif süreçleri ve geçerlilik sürelerini yapılandırın
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="quoteValidityDays">Teklif Geçerlilik Süresi (Gün)</Label>
              <Input
                id="quoteValidityDays"
                type="number"
                min="1"
                max="90"
                value={quoteValidityDays}
                onChange={(e) => updateSetting('business', 'quoteValidityDays', parseInt(e.target.value) || 7)}
              />
              <p className="text-xs text-gray-500">
                Tekliflerin kaç gün geçerli olacağı
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxQuoteRequestsPerDay">Günlük Maksimum Teklif Talebi</Label>
              <Input
                id="maxQuoteRequestsPerDay"
                type="number"
                min="1"
                max="100"
                value={maxQuoteRequestsPerDay}
                onChange={(e) => updateSetting('business', 'maxQuoteRequestsPerDay', parseInt(e.target.value) || 10)}
              />
              <p className="text-xs text-gray-500">
                Bir müşterinin günde yapabileceği maksimum teklif talebi sayısı
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="autoQuoteExpiry">Otomatik Teklif Sona Erdirme</Label>
              <p className="text-sm text-gray-500">
                Süresi dolan teklifler otomatik olarak iptal edilsin
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="autoQuoteExpiry"
                checked={autoQuoteExpiry}
                onCheckedChange={(checked) => updateSetting('business', 'autoQuoteExpiry', checked)}
              />
              <Badge variant={autoQuoteExpiry ? "default" : "secondary"}>
                {autoQuoteExpiry ? "Aktif" : "Pasif"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Limits */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-orange-600" />
            <CardTitle>Sipariş Limitleri</CardTitle>
          </div>
          <CardDescription>
            Minimum sipariş miktarları ve kısıtlamaları belirleyin
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minimumOrderValueM2">Minimum Sipariş (m²)</Label>
              <Input
                id="minimumOrderValueM2"
                type="number"
                min="1"
                value={minimumOrderValueM2}
                onChange={(e) => updateSetting('business', 'minimumOrderValueM2', parseInt(e.target.value) || 100)}
              />
              <p className="text-xs text-gray-500">
                Plaka ürünler için minimum sipariş miktarı
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="minimumOrderValueTon">Minimum Sipariş (Ton)</Label>
              <Input
                id="minimumOrderValueTon"
                type="number"
                min="0.1"
                step="0.1"
                value={minimumOrderValueTon}
                onChange={(e) => updateSetting('business', 'minimumOrderValueTon', parseFloat(e.target.value) || 1)}
              />
              <p className="text-xs text-gray-500">
                Blok ürünler için minimum sipariş miktarı
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Approval Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <CardTitle>Onay Süreçleri</CardTitle>
          </div>
          <CardDescription>
            Üretici ve ürün onay süreçlerini yapılandırın
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="producerApprovalRequired">Üretici Onayı Gerekli</Label>
                <p className="text-sm text-gray-500">
                  Yeni üreticiler admin onayından sonra aktif olsun
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="producerApprovalRequired"
                  checked={producerApprovalRequired}
                  onCheckedChange={(checked) => updateSetting('business', 'producerApprovalRequired', checked)}
                />
                <Badge variant={producerApprovalRequired ? "default" : "secondary"}>
                  {producerApprovalRequired ? "Gerekli" : "Otomatik"}
                </Badge>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="productApprovalRequired">Ürün Onayı Gerekli</Label>
                <p className="text-sm text-gray-500">
                  Yeni ürünler admin onayından sonra yayınlansın
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="productApprovalRequired"
                  checked={productApprovalRequired}
                  onCheckedChange={(checked) => updateSetting('business', 'productApprovalRequired', checked)}
                />
                <Badge variant={productApprovalRequired ? "default" : "secondary"}>
                  {productApprovalRequired ? "Gerekli" : "Otomatik"}
                </Badge>
              </div>
            </div>
          </div>

          {(!producerApprovalRequired || !productApprovalRequired) && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Uyarı:</strong> Otomatik onay sistemleri güvenlik risklerine neden olabilir. 
                Kalite kontrolü için manuel onay süreçlerini aktif tutmanız önerilir.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Business Rules Summary */}
      <Card>
        <CardHeader>
          <CardTitle>İş Kuralları Özeti</CardTitle>
          <CardDescription>
            Mevcut ayarların özeti ve etkisi
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-800">Komisyon Oranları</h4>
              <p className="text-sm text-green-600 mt-1">
                M²: ${commissionRateM2} | Ton: ${commissionRateTon}
              </p>
            </div>
            
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800">Ön Ödeme</h4>
              <p className="text-sm text-blue-600 mt-1">
                %{upfrontPaymentPercentage} ön ödeme gerekli
              </p>
            </div>
            
            <div className="p-4 bg-orange-50 rounded-lg">
              <h4 className="font-medium text-orange-800">Teklif Süresi</h4>
              <p className="text-sm text-orange-600 mt-1">
                {quoteValidityDays} gün geçerlilik
              </p>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-medium text-purple-800">Minimum Sipariş</h4>
              <p className="text-sm text-purple-600 mt-1">
                {minimumOrderValueM2} m² | {minimumOrderValueTon} ton
              </p>
            </div>
            
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-800">Onay Süreçleri</h4>
              <p className="text-sm text-gray-600 mt-1">
                {producerApprovalRequired && productApprovalRequired ? 'Tam Kontrol' : 
                 producerApprovalRequired || productApprovalRequired ? 'Kısmi Kontrol' : 'Otomatik'}
              </p>
            </div>
            
            <div className="p-4 bg-indigo-50 rounded-lg">
              <h4 className="font-medium text-indigo-800">Günlük Limit</h4>
              <p className="text-sm text-indigo-600 mt-1">
                {maxQuoteRequestsPerDay} teklif talebi/gün
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BusinessSettings;
