'use client'

import React from 'react'
import { Card } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  Building2,
  Package,
  Users,
  Clock,
  DollarSign,
  Eye,
  TrendingUp,
  Calendar,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Target
} from 'lucide-react'
import { AdminQuoteRequestDetailsModal } from '@/components/ui/admin-quote-request-details-modal'

export default function AdminQuoteRequestsPage() {
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedStatus, setSelectedStatus] = React.useState('all')
  const [selectedTab, setSelectedTab] = React.useState('active')
  const [selectedRequest, setSelectedRequest] = React.useState<any>(null)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = React.useState(false)

  // Quote requests data will be loaded from API
  const mockQuoteRequests: any[] = []

  const statuses = ['all', 'active', 'pending', 'completed', 'expired']
  const statusLabels = {
    all: 'Tümü',
    active: 'Aktif',
    pending: 'Teklif Bekleniyor',
    completed: 'Tamamlandı',
    expired: 'Süresi Doldu'
  }

  const filteredRequests = mockQuoteRequests.filter(request => {
    const matchesSearch = request.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.productName?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || request.status === selectedStatus
    
    return matchesSearch && matchesStatus
  })

  const totalRequests = mockQuoteRequests.length
  const activeRequests = mockQuoteRequests.filter(r => r.status === 'active').length
  const completedRequests = mockQuoteRequests.filter(r => r.status === 'completed').length
  const avgQuotesPerRequest = mockQuoteRequests.length > 0 
    ? mockQuoteRequests.reduce((sum, r) => sum + (r.quotesReceived || 0), 0) / mockQuoteRequests.length 
    : 0

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-blue-100 text-blue-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'expired':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleViewDetails = (request: any) => {
    setSelectedRequest(request)
    setIsDetailsModalOpen(true)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Teklif Talepleri</h1>
          <p className="text-gray-600">Müşteri teklif taleplerini yönetin ve takip edin</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MessageSquare className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Toplam Talep</p>
              <p className="text-2xl font-bold text-gray-900">{totalRequests}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Clock className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Aktif Talep</p>
              <p className="text-2xl font-bold text-gray-900">{activeRequests}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Tamamlanan</p>
              <p className="text-2xl font-bold text-gray-900">{completedRequests}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Ort. Teklif</p>
              <p className="text-2xl font-bold text-gray-900">{avgQuotesPerRequest.toFixed(1)}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Müşteri adı veya ürün ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {statuses.map(status => (
                <option key={status} value={status}>
                  {statusLabels[status as keyof typeof statusLabels]}
                </option>
              ))}
            </select>
          </div>
        </div>
      </Card>

      {/* Quote Requests List */}
      <Card>
        <div className="p-6">
          {filteredRequests.length === 0 ? (
            <div className="text-center py-12">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz teklif talebi yok</h3>
              <p className="text-gray-600">Müşteriler teklif taleplerini gönderdiğinde burada görünecek.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredRequests.map((request) => (
                <div key={request.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-medium text-gray-900">{request.customerName}</h3>
                        <Badge className={getStatusColor(request.status)}>
                          {statusLabels[request.status as keyof typeof statusLabels]}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-1">{request.productName}</p>
                      <p className="text-xs text-gray-500">
                        {request.dimension} • {request.quantity} {request.unit}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">{request.quotesReceived || 0} Teklif</p>
                      <p className="text-xs text-gray-500">{request.requestDate}</p>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewDetails(request)}
                        className="mt-2"
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        Detay
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>

      {/* Details Modal */}
      <AdminQuoteRequestDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        request={selectedRequest}
      />
    </div>
  )
}
