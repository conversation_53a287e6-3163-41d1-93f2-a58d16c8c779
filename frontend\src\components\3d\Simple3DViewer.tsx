'use client';

import React, { Suspense, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei/core/OrbitControls';
import * as THREE from 'three';

// Simple rotating cube component
function RotatingCube() {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state, delta) => {
    if (meshRef.current) {
      meshRef.current.rotation.x += delta * 0.5;
      meshRef.current.rotation.y += delta * 0.3;
    }
  });

  return (
    <mesh ref={meshRef} position={[0, 0, 0]}>
      <boxGeometry args={[2, 2, 2]} />
      <meshStandardMaterial 
        color="#8B4513" 
        roughness={0.3}
        metalness={0.1}
      />
    </mesh>
  );
}

// Simple marble sphere
function MarbleSphere() {
  return (
    <mesh position={[3, 0, 0]}>
      <sphereGeometry args={[1, 32, 32]} />
      <meshStandardMaterial 
        color="#F5F5DC" 
        roughness={0.2}
        metalness={0.05}
      />
    </mesh>
  );
}

// Simple granite block
function GraniteBlock() {
  return (
    <mesh position={[-3, 0, 0]}>
      <boxGeometry args={[1.5, 1.5, 1.5]} />
      <meshStandardMaterial 
        color="#696969" 
        roughness={0.4}
        metalness={0.1}
      />
    </mesh>
  );
}

// Loading component
function LoadingFallback() {
  return (
    <Html center>
      <div className="flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">3D Model Yükleniyor...</span>
      </div>
    </Html>
  );
}

interface Simple3DViewerProps {
  className?: string;
}

export default function Simple3DViewer({ className = '' }: Simple3DViewerProps) {
  return (
    <div className={`w-full h-96 bg-gray-100 rounded-lg overflow-hidden ${className}`}>
      <Canvas
        camera={{ position: [5, 5, 5], fov: 50 }}
        shadows
        dpr={[1, 2]}
        gl={{ antialias: true }}
      >
        <Suspense fallback={<LoadingFallback />}>
          {/* Lighting */}
          <ambientLight intensity={0.4} />
          <directionalLight 
            position={[10, 10, 5]} 
            intensity={1}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
          />
          <pointLight position={[-10, -10, -10]} intensity={0.3} />

          {/* 3D Objects */}
          <RotatingCube />
          <MarbleSphere />
          <GraniteBlock />

          {/* Ground plane */}
          <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]} receiveShadow>
            <planeGeometry args={[20, 20]} />
            <meshStandardMaterial color="#f0f0f0" />
          </mesh>

          {/* Controls */}
          <OrbitControls 
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={3}
            maxDistance={20}
          />
        </Suspense>
      </Canvas>

      {/* UI Overlay */}
      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
        <h3 className="font-semibold text-gray-800 mb-2">3D Doğal Taş Görüntüleyici</h3>
        <div className="text-sm text-gray-600 space-y-1">
          <div>🟤 Mermer Küp (Dönen)</div>
          <div>⚪ Mermer Küre</div>
          <div>⬛ Granit Blok</div>
        </div>
        <div className="mt-2 text-xs text-gray-500">
          Fare ile döndürün, zoom yapın
        </div>
      </div>
    </div>
  );
}
