import * as React from "react"
import { cn } from "@/lib/utils"
import { <PERSON>, CardContent, CardFooter } from "./card"
import { Button } from "./button"

export interface BidCardProps extends React.HTMLAttributes<HTMLDivElement> {
  bid: {
    id: string
    rank: number
    price: number
    currency: string
    deliveryTime: number
    deliveryUnit: 'days' | 'weeks'
    paymentTerms: string
    notes?: string
    isWinning?: boolean
    isSelected?: boolean
  }
  onSelect?: (bidId: string) => void
  onViewDetails?: (bidId: string) => void
  showRanking?: boolean
  anonymous?: boolean
}

/**
 * BidCard component following RFC-004 UI/UX Design System
 * Anonymous bidding card for competitive marketplace
 */
const BidCard = React.forwardRef<HTMLDivElement, BidCardProps>(
  ({ 
    className, 
    bid, 
    onSelect,
    onViewDetails,
    showRanking = true,
    anonymous = true,
    ...props 
  }, ref) => {
    
    const getRankIcon = (rank: number) => {
      switch (rank) {
        case 1: return "🥇"
        case 2: return "🥈"
        case 3: return "🥉"
        default: return `#${rank}`
      }
    }
    
    const getRankColor = (rank: number) => {
      switch (rank) {
        case 1: return "border-[var(--success)] bg-green-50"
        case 2: return "border-[var(--warning)] bg-yellow-50"
        case 3: return "border-[var(--error)] bg-red-50"
        default: return "border-[var(--gray-200)] bg-[var(--bg-primary)]"
      }
    }
    
    const handleSelect = () => {
      onSelect?.(bid.id)
    }
    
    const handleViewDetails = () => {
      onViewDetails?.(bid.id)
    }

    return (
      <Card
        ref={ref}
        className={cn(
          "relative transition-all duration-300",
          getRankColor(bid.rank),
          bid.isSelected && "ring-2 ring-[var(--primary-stone)] ring-opacity-50",
          bid.isWinning && "shadow-[var(--shadow-lg)]",
          className
        )}
        {...props}
      >
        {/* Rank Indicator */}
        {showRanking && (
          <div className="absolute -top-2 -left-2 z-10">
            <div className={cn(
              "w-8 h-8 rounded-full flex items-center justify-center",
              "text-white font-bold text-sm shadow-md",
              bid.rank === 1 && "bg-[var(--success)]",
              bid.rank === 2 && "bg-[var(--warning)]",
              bid.rank === 3 && "bg-[var(--error)]",
              bid.rank > 3 && "bg-[var(--gray-500)]"
            )}>
              {bid.rank <= 3 ? getRankIcon(bid.rank) : bid.rank}
            </div>
          </div>
        )}
        
        {/* Winning Badge */}
        {bid.isWinning && (
          <div className="absolute top-2 right-2">
            <span className={cn(
              "px-2 py-1 text-xs font-medium rounded-full",
              "bg-[var(--success)] text-white shadow-sm"
            )}>
              En İyi Teklif
            </span>
          </div>
        )}

        <CardContent className="p-4">
          {/* Anonymous Identifier */}
          {anonymous && (
            <div className="flex items-center gap-2 mb-3">
              <div className="w-8 h-8 bg-[var(--gray-300)] rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-[var(--gray-600)]">
                  {String.fromCharCode(65 + (bid.rank - 1))}
                </span>
              </div>
              <span className="text-[var(--text-sm)] text-[var(--text-secondary)]">
                Anonim Üretici {String.fromCharCode(65 + (bid.rank - 1))}
              </span>
            </div>
          )}
          
          {/* Price */}
          <div className="mb-4">
            <div className="flex items-baseline gap-2">
              <span className={cn(
                "text-2xl font-bold",
                bid.isWinning ? "text-[var(--success)]" : "text-[var(--primary-stone)]"
              )}>
                {bid.currency}{bid.price.toLocaleString()}
              </span>
              <span className="text-[var(--text-sm)] text-[var(--text-secondary)]">
                toplam
              </span>
            </div>
          </div>
          
          {/* Delivery and Payment Terms */}
          <div className="space-y-2 mb-4">
            <div className="flex justify-between items-center">
              <span className="text-[var(--text-sm)] text-[var(--text-secondary)]">
                Teslimat Süresi:
              </span>
              <span className="text-[var(--text-sm)] font-medium">
                {bid.deliveryTime} {bid.deliveryUnit === 'days' ? 'gün' : 'hafta'}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-[var(--text-sm)] text-[var(--text-secondary)]">
                Ödeme Koşulları:
              </span>
              <span className="text-[var(--text-sm)] font-medium">
                {bid.paymentTerms}
              </span>
            </div>
          </div>
          
          {/* Notes */}
          {bid.notes && (
            <div className="mb-4">
              <p className="text-[var(--text-sm)] text-[var(--text-secondary)] italic">
                "{bid.notes}"
              </p>
            </div>
          )}
        </CardContent>

        <CardFooter className="p-4 pt-0 flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleViewDetails}
            className="flex-1"
          >
            Detayları Gör
          </Button>
          
          <Button
            variant={bid.isWinning ? "primary" : "secondary"}
            size="sm"
            onClick={handleSelect}
            className="flex-1"
            disabled={bid.isSelected}
          >
            {bid.isSelected ? "Seçildi" : "Seç"}
          </Button>
        </CardFooter>
      </Card>
    )
  }
)

BidCard.displayName = "BidCard"

export { BidCard }
