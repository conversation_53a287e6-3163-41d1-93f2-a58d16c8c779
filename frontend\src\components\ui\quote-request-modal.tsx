﻿"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Button } from "./button"
import { useQuote } from "@/contexts/quote-context"
import { useAuth } from "@/contexts/auth-context"

interface Product {
  id: string
  name: string
  category: string
  image: string
  price?: {
    min: number
    max: number
    currency: string
    unit: string
  }
}

interface QuoteRequestModalProps {
  isOpen: boolean
  onClose: () => void
  product?: Product | null
  selectedProducts?: QuoteProduct[] // Çoklu ürün seçimi için
  onSubmit?: (quoteData: any) => void
}

interface ProductSpec {
  id: string
  type: 'sized' | 'slab' // ebatlı ürün veya plaka
  thickness: string
  width: string
  length: string
  surface: string
  packaging: string
  delivery: string
  area: string // metraj m2
  targetPrice: string // hedef fiyat
  currency: string
}

interface QuoteProduct {
  id: string
  name: string
  category: string
  image: string
}

export function QuoteRequestModal({ isOpen, onClose, product, selectedProducts = [], onSubmit }: QuoteRequestModalProps) {
  const { createQuoteRequest } = useQuote()
  const { user } = useAuth()

  const [formData, setFormData] = React.useState({
    message: ""
  })

  const [selectedProductsList, setSelectedProductsList] = React.useState<QuoteProduct[]>([])
  const [selectedCategory, setSelectedCategory] = React.useState<string>("all")
  const [searchQuery, setSearchQuery] = React.useState<string>("")

  // selectedProducts prop'undan gelen ürünleri selectedProductsList'e aktar
  React.useEffect(() => {
    if (isOpen) {
      if (selectedProducts && selectedProducts.length > 0) {
        const convertedProducts = selectedProducts.map(p => ({
          id: p.id,
          name: p.name,
          category: p.category,
          image: p.image || "/api/placeholder/300/200"
        }))
        setSelectedProductsList(convertedProducts)
      } else if (product) {
        // Tek ürün seçimi durumu
        setSelectedProductsList([{
          id: product.id,
          name: product.name,
          category: product.category,
          image: product.image || "/api/placeholder/300/200"
        }])
      }
    }
  }, [selectedProducts, product])
  const [availableProducts] = React.useState<QuoteProduct[]>([
    { id: "1", name: "Traverten Klasik", category: "Traverten", image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+" },
    { id: "2", name: "Traverten Premium", category: "Traverten", image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+" },
    { id: "3", name: "Mermer Beyaz", category: "Mermer", image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+" },
    { id: "4", name: "Mermer Siyah", category: "Mermer", image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+" },
    { id: "5", name: "Granit Siyah", category: "Granit", image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+" },
    { id: "6", name: "Granit Gri", category: "Granit", image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+" },
    { id: "7", name: "Oniks Yeşil", category: "Oniks", image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+" },
    { id: "8", name: "Oniks Sarı", category: "Oniks", image: "/api/placeholder/300/200" },
    { id: "9", name: "Kireçtaşı Bej", category: "Kireçtaşı", image: "/api/placeholder/300/200" },
    { id: "10", name: "Kireçtaşı Krem", category: "Kireçtaşı", image: "/api/placeholder/300/200" },
    { id: "11", name: "Kuvarsit Beyaz", category: "Kuvarsit", image: "/api/placeholder/300/200" },
    { id: "12", name: "Kuvarsit Gri", category: "Kuvarsit", image: "/api/placeholder/300/200" },
  ])

  // Kategorileri otomatik olarak çıkar
  const categories = React.useMemo(() => {
    const uniqueCategories = Array.from(new Set(availableProducts.map(p => p.category)))
    return uniqueCategories.sort()
  }, [availableProducts])

  // Filtrelenmiş ürünler
  const filteredAvailableProducts = React.useMemo(() => {
    let filtered = availableProducts.filter(p => !selectedProductsList.find(sp => sp.id === p.id))

    // Kategori filtresi
    if (selectedCategory !== "all") {
      filtered = filtered.filter(p => p.category === selectedCategory)
    }

    // İsim filtresi
    if (searchQuery.trim()) {
      filtered = filtered.filter(p =>
        p.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        p.category.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Maksimum 3 ürün göster
    return filtered.slice(0, 3)
  }, [availableProducts, selectedProductsList, selectedCategory, searchQuery])

  const [productSpecs, setProductSpecs] = React.useState<ProductSpec[]>([])

  // Başlangıçta seçili ürünü ekle
  React.useEffect(() => {
    if (product && isOpen && selectedProductsList.length === 0) {
      const newProduct: QuoteProduct = {
        id: product.id,
        name: product.name,
        category: product.category,
        image: product.image
      }
      setSelectedProductsList([newProduct])

      // İlk ürün için spec ekle
      const newSpec: ProductSpec = {
        id: product.id,
        type: "sized",
        thickness: "",
        width: "",
        length: "",
        surface: "",
        packaging: "",
        delivery: "",
        area: "",
        targetPrice: "",
        currency: "USD"
      }
      setProductSpecs([newSpec])
    }
  }, [product, isOpen])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const addProduct = (productToAdd: QuoteProduct) => {
    // Ürün zaten seçili mi kontrol et
    if (selectedProductsList.find(p => p.id === productToAdd.id)) {
      alert('Bu ürün zaten seçili!')
      return
    }

    setSelectedProductsList(prev => [...prev, productToAdd])

    // Yeni ürün için spec ekle
    const newSpec: ProductSpec = {
      id: productToAdd.id,
      type: "sized",
      thickness: "",
      width: "",
      length: "",
      surface: "",
      packaging: "",
      delivery: "",
      area: "",
      targetPrice: "",
      currency: "USD"
    }
    setProductSpecs(prev => [...prev, newSpec])
  }

  const removeProduct = (productId: string) => {
    setSelectedProductsList(prev => prev.filter(p => p.id !== productId))
    setProductSpecs(prev => prev.filter(spec => spec.id !== productId))
    // Kategori filtresini sıfırla
    setSelectedCategory("all")
  }

  const handleSpecChange = (id: string, field: keyof ProductSpec, value: string) => {
    setProductSpecs(prev => prev.map(spec =>
      spec.id === id ? { ...spec, [field]: value } : spec
    ))
  }

  const addNewSpec = (productId: string, type: 'sized' | 'slab' = 'sized') => {
    const newSpec: ProductSpec = {
      id: `${productId}_${Date.now()}`,
      type,
      thickness: "",
      width: type === 'sized' ? "" : "0", // Plaka için genişlik/uzunluk gerekmez
      length: type === 'sized' ? "" : "0",
      surface: "",
      packaging: "",
      delivery: "",
      area: "",
      targetPrice: "",
      currency: ""
    }
    setProductSpecs(prev => [...prev, newSpec])
  }

  const removeSpec = (id: string) => {
    if (productSpecs.length > 1) {
      setProductSpecs(prev => prev.filter(spec => spec.id !== id))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    console.log('User in quote modal:', user); // Debug log
    console.log('Auth context user:', user);
    console.log('LocalStorage user:', typeof window !== 'undefined' ? localStorage.getItem('user') : 'N/A');

    if (!user) {
      console.error('User not found in auth context');
      alert("Teklif talebi göndermek için giriş yapmalısınız.")
      return
    }

    try {
      // Convert selected products and specs to quote request format
      const products = selectedProductsList.map(selectedProduct => ({
        id: `prod-${selectedProduct.id}`,
        productId: selectedProduct.id,
        productName: selectedProduct.name,
        productCategory: selectedProduct.category,
        productImage: selectedProduct.image,
        specifications: productSpecs
          .filter(spec => spec.id.startsWith(selectedProduct.id))
          .map(spec => ({
            id: spec.id,
            type: spec.type,
            thickness: spec.thickness,
            width: spec.width,
            length: spec.length,
            surface: spec.surface,
            packaging: spec.packaging,
            delivery: spec.delivery,
            area: spec.area,
            targetPrice: spec.targetPrice,
            currency: spec.currency
          }))
      }))

      const quoteRequestData = {
        customerId: user.id,
        customerName: user.name,
        customerEmail: user.email,
        products,
        message: formData.message,
        status: 'pending' as const
      }

      const requestId = await createQuoteRequest(quoteRequestData)

      alert("Teklif talebiniz başarıyla gönderildi! Taleplerim bölümünden takip edebilirsiniz.")
      console.log("Quote request created with ID:", requestId)

      // Reset form
      onClose()
      setFormData({ message: "" })
      setSelectedProductsList([])
      setProductSpecs([])
      setSelectedCategory("all")

      // Kullanıcıyı talepler sayfasına yönlendir
      if (typeof window !== 'undefined') {
        window.location.href = '/customer/requests/active'
      }

      // onSubmit callback'i varsa çağır
      if (onSubmit) {
        onSubmit(quoteRequestData)
      }

    } catch (error) {
      console.error("Error creating quote request:", error)
      alert("Teklif talebi gönderilirken bir hata oluştu. Lütfen tekrar deneyin.")
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Teklif Talebi</h2>
            <p className="text-gray-600">
              {product ?
                `${product.name} - ${product.category}` :
                selectedProducts.length > 0 ?
                  `${selectedProducts.length} ürün seçildi` :
                  'Ürün seçimi yapılmadı'
              }
            </p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 140px)' }}>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Seçili Ürünler */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Seçili Ürünler</h3>
                <div className="text-sm text-gray-600">
                  {selectedProductsList.length} ürün seçili
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {selectedProductsList.map((selectedProduct) => (
                  <div key={selectedProduct.id} className="bg-gray-50 p-4 rounded-lg relative">
                    <button
                      type="button"
                      onClick={() => removeProduct(selectedProduct.id)}
                      className="absolute top-2 right-2 text-red-600 hover:text-red-800 text-sm"
                    >
                      ✕
                    </button>
                    <div className="flex items-center gap-3">
                      <img
                        src={selectedProduct.image}
                        alt={selectedProduct.name}
                        className="w-12 h-12 object-cover rounded"
                      />
                      <div>
                        <h4 className="font-medium text-gray-900 text-sm">{selectedProduct.name}</h4>
                        <p className="text-gray-600 text-xs">{selectedProduct.category}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Ürün Ekleme */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <div className="mb-4">
                  <h4 className="font-medium text-gray-900 mb-3">Yeni Ürün Ekle</h4>

                  {/* Filtre Alanları */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                    {/* Arama Kutusu */}
                    <div>
                      <label className="block text-sm text-gray-600 mb-1">Ürün Ara:</label>
                      <input
                        type="text"
                        placeholder="Ürün adı veya kategori yazın..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-stone-500"
                      />
                    </div>

                    {/* Kategori Filtresi */}
                    <div>
                      <label className="block text-sm text-gray-600 mb-1">Kategori:</label>
                      <select
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-stone-500"
                      >
                        <option value="all">Tümü</option>
                        {categories.map(category => (
                          <option key={category} value={category}>
                            {category}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Filtre Temizleme */}
                  {(searchQuery || selectedCategory !== "all") && (
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-sm text-gray-600">Aktif filtreler:</span>
                      {searchQuery && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-stone-100 text-stone-700 rounded text-xs">
                          "{searchQuery}"
                          <button
                            type="button"
                            onClick={() => setSearchQuery("")}
                            className="text-stone-500 hover:text-stone-700"
                          >
                            ✕
                          </button>
                        </span>
                      )}
                      {selectedCategory !== "all" && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-stone-100 text-stone-700 rounded text-xs">
                          {selectedCategory}
                          <button
                            type="button"
                            onClick={() => setSelectedCategory("all")}
                            className="text-stone-500 hover:text-stone-700"
                          >
                            ✕
                          </button>
                        </span>
                      )}
                      <button
                        type="button"
                        onClick={() => {
                          setSearchQuery("")
                          setSelectedCategory("all")
                        }}
                        className="text-xs text-stone-600 hover:text-stone-800 underline"
                      >
                        Tümünü temizle
                      </button>
                    </div>
                  )}
                </div>

                {/* Ürün Listesi */}
                <div className="space-y-2">
                  {filteredAvailableProducts.length > 0 ? (
                    <>
                      <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                        <span>Bulunan ürünler (en fazla 3 gösteriliyor):</span>
                        <span>{filteredAvailableProducts.length}/3</span>
                      </div>

                      <div className="grid grid-cols-1 gap-2">
                        {filteredAvailableProducts.map((availableProduct) => (
                          <button
                            key={availableProduct.id}
                            type="button"
                            onClick={() => addProduct(availableProduct)}
                            className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 text-left transition-colors"
                          >
                            <img
                              src={availableProduct.image}
                              alt={availableProduct.name}
                              className="w-10 h-10 object-cover rounded"
                            />
                            <div className="flex-1">
                              <div className="font-medium text-sm">{availableProduct.name}</div>
                              <div className="text-xs text-gray-600">{availableProduct.category}</div>
                            </div>
                            <div className="text-xs text-stone-600">
                              + Ekle
                            </div>
                          </button>
                        ))}
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-gray-500 text-sm mb-2">
                        {searchQuery || selectedCategory !== "all"
                          ? "Arama kriterlerinize uygun ürün bulunamadı"
                          : "Tüm ürünler seçildi"
                        }
                      </p>
                      {(searchQuery || selectedCategory !== "all") && (
                        <button
                          type="button"
                          onClick={() => {
                            setSearchQuery("")
                            setSelectedCategory("all")
                          }}
                          className="text-xs text-stone-600 hover:text-stone-800 underline"
                        >
                          Filtreleri temizle
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Ürün Özellikleri */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Ürün Talep Detayları</h3>

              {selectedProductsList.map((selectedProduct) => {
                const productSpecs_filtered = productSpecs.filter(spec => spec.id.startsWith(selectedProduct.id))

                return (
                  <div key={selectedProduct.id} className="border border-gray-200 rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <img
                          src={selectedProduct.image}
                          alt={selectedProduct.name}
                          className="w-10 h-10 object-cover rounded"
                        />
                        <div>
                          <h4 className="font-medium text-gray-900">{selectedProduct.name}</h4>
                          <p className="text-sm text-gray-600">{selectedProduct.category}</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => addNewSpec(selectedProduct.id, 'sized')}
                        >
                          + Ebatlı Talep
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => addNewSpec(selectedProduct.id, 'slab')}
                        >
                          + Plaka Talep
                        </Button>
                      </div>
                    </div>

                    {productSpecs_filtered.length === 0 && (
                      <p className="text-gray-500 text-sm">Bu ürün için henüz talep eklenmedi. Yukarıdaki butonları kullanarak talep ekleyin.</p>
                    )}

                    {productSpecs_filtered.map((spec, index) => (
                <div key={spec.id} className="border border-gray-200 rounded-lg p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">
                      {spec.type === 'sized' ? 'Ebatlı Ürün' : 'Plaka'} {index + 1}
                    </h4>
                    {productSpecs.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeSpec(spec.id)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Kaldır
                      </button>
                    )}
                  </div>

                  {/* Ürün Tipi Seçimi */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ürün Tipi
                    </label>
                    <select
                      value={spec.type}
                      onChange={(e) => handleSpecChange(spec.id, 'type', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                    >
                      <option value="sized">Ebatlı Ürün</option>
                      <option value="slab">Plaka</option>
                    </select>
                  </div>

                  {/* Ebat Bilgileri */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Kalınlık (cm)
                      </label>
                      <input
                        type="number"
                        value={spec.thickness}
                        onChange={(e) => handleSpecChange(spec.id, 'thickness', e.target.value)}
                        placeholder="Örn: 2"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      />
                    </div>
                    {spec.type === 'sized' && (
                      <>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            En (cm)
                          </label>
                          <input
                            type="number"
                            value={spec.width}
                            onChange={(e) => handleSpecChange(spec.id, 'width', e.target.value)}
                            placeholder="Örn: 60"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Boy (cm)
                          </label>
                          <input
                            type="number"
                            value={spec.length}
                            onChange={(e) => handleSpecChange(spec.id, 'length', e.target.value)}
                            placeholder="Örn: 60"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                          />
                        </div>
                      </>
                    )}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Metraj (m²)
                      </label>
                      <input
                        type="number"
                        value={spec.area}
                        onChange={(e) => handleSpecChange(spec.id, 'area', e.target.value)}
                        placeholder="Örn: 100"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Yüzey İşlemi
                      </label>
                      <select
                        value={spec.surface}
                        onChange={(e) => handleSpecChange(spec.id, 'surface', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      >
                        <option value="">Seçin</option>
                        <option value="ham">Ham</option>
                        <option value="honlu">Honlu</option>
                        <option value="cilali">Cilalı</option>
                        <option value="fircali">Fırçalı</option>
                        <option value="kumlama">Kumlama</option>
                        <option value="eskitme">Eskitme</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Ambalaj
                      </label>
                      <select
                        value={spec.packaging}
                        onChange={(e) => handleSpecChange(spec.id, 'packaging', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      >
                        <option value="">Seçin</option>
                        <option value="kasali">Kasalı</option>
                        <option value="bandilli">Bandıllı</option>
                        <option value="paletustu">Palet Üstü</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Teslimat Şekli
                      </label>
                      <select
                        value={spec.delivery}
                        onChange={(e) => handleSpecChange(spec.id, 'delivery', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      >
                        <option value="">Seçin</option>
                        <option value="fabrika">Fabrika Teslim</option>
                        <option value="liman">Liman Teslim</option>
                        <option value="fob">FOB</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Hedef Fiyat (İsteğe Bağlı)
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        value={spec.targetPrice}
                        onChange={(e) => handleSpecChange(spec.id, 'targetPrice', e.target.value)}
                        placeholder="Örn: 25.50"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Para Birimi
                      </label>
                      <select
                        value={spec.currency}
                        onChange={(e) => handleSpecChange(spec.id, 'currency', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      >
                        <option value="USD">USD</option>
                        <option value="EUR">Euro</option>
                        <option value="TL">TL</option>
                      </select>
                    </div>
                  </div>
                </div>
              ))}
                  </div>
                )
              })}
            </div>

            {/* Message */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ek Notlar
              </label>
              <textarea
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                rows={4}
                placeholder="Projeniz hakkında detaylar, özel istekler..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
              />
            </div>

            {/* Submit Button */}
            <div className="flex gap-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="flex-1"
              >
                İptal
              </Button>
              <Button
                type="submit"
                variant="primary"
                className="flex-1"
              >
                Teklif Talebi Gönder
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
