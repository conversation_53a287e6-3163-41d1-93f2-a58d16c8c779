# RFC-008: Authentication and Post-Login Redirect System
**Türkiye Doğal Taş Marketplace - Giriş Sonrası Yönlendirme Sistemi**

## Metadata
- **RFC ID**: RFC-008
- **Title**: Authentication and Post-Login Redirect System
- **Author**: Augment Agent
- **Status**: Implemented ✅
- **Created**: 2025-06-29
- **Updated**: 2025-06-29
- **Version**: 1.0.0

## Abstract

Bu RFC, Türkiye Doğal Taş Marketplace platformunda kullanıcı girişi sonrası otomatik yönlendirme sistemini tanımlar. Sistem, kullanıcıların giriş yaptıktan sonra rollerine uygun dashboard sayfalarına otomatik olarak yönlendirilmesini sağlar.

## 1. Problem Statement

### 1.1 Mevcut Durum
- Kullanıcılar giriş yaptıktan sonra ana sayfada kalıyor
- <PERSON> o<PERSON> dashboard sayfasına gitmeleri gerekiyor
- Korumalı sayfalara erişim kontrolü eksik
- Session yönetimi yetersiz

### 1.2 Gereksinimler
- <PERSON><PERSON><PERSON> sonrası otomatik dashboard yönlendirmesi
- Role-based routing sistemi
- Protected route kontrolü
- Session management entegrasyonu

## 2. Solution Design

### 2.1 Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    AUTHENTICATION FLOW                      │
├─────────────────────────────────────────────────────────────┤
│  Login/Register → Auth Context → Cookie Set → Redirect     │
├─────────────────────────────────────────────────────────────┤
│                    MIDDLEWARE LAYER                         │
├─────────────────────────────────────────────────────────────┤
│  Route Check → Auth Check → Role Check → Allow/Redirect    │
├─────────────────────────────────────────────────────────────┤
│                    PROTECTED ROUTES                         │
├─────────────────────────────────────────────────────────────┤
│  /customer/dashboard  │  /producer/dashboard  │  /admin/*   │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Component Structure

```typescript
// Authentication Context
interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  // ... other methods
}

// User Interface
interface User {
  id: string
  name: string
  email: string
  role: 'customer' | 'producer' | 'admin'
}
```

## 3. Implementation Details

### 3.1 Next.js Middleware

```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Protected routes
  const protectedRoutes = [
    '/customer/dashboard',
    '/customer/*',
    '/producer/*',
    '/admin/*'
  ]
  
  // Authentication check
  const isAuthenticated = checkAuthentication(request)
  const userRole = getUserRole(request)
  
  // Route protection logic
  if (protectedRoutes.some(route => pathname.startsWith(route))) {
    if (!isAuthenticated) {
      return redirectToLogin(request)
    }
    
    // Role-based access control
    if (!hasAccess(pathname, userRole)) {
      return redirectToHome(request)
    }
  }
  
  return NextResponse.next()
}
```

### 3.2 Authentication Context Updates

```typescript
// Auth Context - Login Function
const login = async (email: string, password: string): Promise<boolean> => {
  if (email && password) {
    const mockUser: User = {
      id: '1',
      name: email.split('@')[0],
      email: email,
      role: 'customer'
    }
    
    setUser(mockUser)
    setIsLoginModalOpen(false)
    
    // Post-login redirect
    if (typeof window !== 'undefined') {
      window.location.href = '/customer/dashboard'
    }
    
    return true
  }
  return false
}
```

### 3.3 Cookie Management

```typescript
// Save user to localStorage and cookies
React.useEffect(() => {
  if (user) {
    localStorage.setItem('user', JSON.stringify(user))
    // Set cookie for middleware access
    document.cookie = `user=${JSON.stringify(user)}; path=/; max-age=${7 * 24 * 60 * 60}`
  } else {
    localStorage.removeItem('user')
    // Clear cookie
    document.cookie = 'user=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
  }
}, [user])
```

## 4. Route Configuration

### 4.1 Protected Routes

| Route Pattern | Required Role | Description |
|---------------|---------------|-------------|
| `/customer/dashboard` | customer | Müşteri ana dashboard |
| `/customer/*` | customer | Tüm müşteri sayfaları |
| `/producer/dashboard` | producer | Üretici ana dashboard |
| `/producer/*` | producer | Tüm üretici sayfaları |
| `/admin/*` | admin | Admin panel sayfaları |

### 4.2 Public Routes

| Route | Description |
|-------|-------------|
| `/` | Ana sayfa |
| `/products` | Ürün listesi |
| `/about` | Hakkımızda |
| `/contact` | İletişim |
| `/favorites` | Public favoriler |

### 4.3 Redirect Logic

```typescript
// Role-based redirect mapping
const redirectMap = {
  customer: '/customer/dashboard',
  producer: '/producer/dashboard',
  admin: '/admin/dashboard'
}

// Auto-redirect for authenticated users
if (isAuthenticated && pathname === '/') {
  const targetPath = redirectMap[userRole]
  return NextResponse.redirect(new URL(targetPath, request.url))
}
```

## 5. Security Considerations

### 5.1 Authentication Validation

```typescript
function checkAuthentication(request: NextRequest): boolean {
  // Check auth token in cookies
  const authToken = request.cookies.get('auth-token')?.value
  const userCookie = request.cookies.get('user')?.value
  
  if (userCookie) {
    try {
      const user = JSON.parse(userCookie)
      return !!(user && user.id)
    } catch {
      return false
    }
  }
  
  return !!authToken
}
```

### 5.2 Role Validation

```typescript
function getUserRole(request: NextRequest): string | null {
  const userCookie = request.cookies.get('user')?.value
  
  if (userCookie) {
    try {
      const user = JSON.parse(userCookie)
      return user.role || 'customer'
    } catch {
      return null
    }
  }
  
  return 'customer' // Default role
}
```

## 6. User Experience Flow

### 6.1 Login Flow

```
1. User clicks "Giriş Yap" button
2. Login modal opens
3. User enters credentials
4. Authentication successful
5. Modal closes
6. Automatic redirect to /customer/dashboard
7. Dashboard loads with user data
```

### 6.2 Protected Route Access

```
1. User tries to access /customer/dashboard
2. Middleware checks authentication
3. If not authenticated:
   - Redirect to /?login=required
   - Login modal auto-opens
4. If authenticated:
   - Check role permissions
   - Allow access or redirect
```

### 6.3 Logout Flow

```
1. User clicks "Çıkış Yap" button
2. Clear user data from context
3. Clear localStorage and cookies
4. Automatic redirect to home page
```

## 7. Error Handling

### 7.1 Authentication Errors

```typescript
// Handle authentication failures
try {
  const success = await login(email, password)
  if (!success) {
    setError('Geçersiz e-posta veya şifre')
  }
} catch (error) {
  setError('Giriş yapılırken bir hata oluştu')
}
```

### 7.2 Redirect Errors

```typescript
// Safe redirect with fallback
const safeRedirect = (url: string) => {
  try {
    if (typeof window !== 'undefined') {
      window.location.href = url
    }
  } catch (error) {
    console.error('Redirect failed:', error)
    // Fallback to router.push or manual navigation
  }
}
```

## 8. Testing Strategy

### 8.1 Unit Tests

```typescript
// Test authentication context
describe('AuthContext', () => {
  it('should redirect to dashboard after login', async () => {
    const { result } = renderHook(() => useAuth())
    await act(async () => {
      await result.current.login('<EMAIL>', 'password')
    })
    expect(window.location.href).toBe('/customer/dashboard')
  })
})
```

### 8.2 Integration Tests

```typescript
// Test middleware functionality
describe('Middleware', () => {
  it('should redirect unauthenticated users', () => {
    const request = new NextRequest('http://localhost:3000/customer/dashboard')
    const response = middleware(request)
    expect(response.status).toBe(302)
    expect(response.headers.get('location')).toContain('login=required')
  })
})
```

## 9. Performance Considerations

### 9.1 Cookie Size Optimization

```typescript
// Minimize cookie data
const minimalUser = {
  id: user.id,
  role: user.role
}
document.cookie = `user=${JSON.stringify(minimalUser)}; path=/; max-age=${7 * 24 * 60 * 60}`
```

### 9.2 Middleware Optimization

```typescript
// Skip middleware for static files
const staticRoutes = ['/api/', '/_next/', '/favicon.ico', '/images/']
if (staticRoutes.some(route => pathname.startsWith(route))) {
  return NextResponse.next()
}
```

## 10. Future Enhancements

### 10.1 JWT Token Integration

```typescript
// Replace cookie-based auth with JWT
const token = jwt.sign({ userId, role }, JWT_SECRET, { expiresIn: '7d' })
response.cookies.set('auth-token', token, {
  httpOnly: true,
  secure: true,
  sameSite: 'strict'
})
```

### 10.2 Remember Me Functionality

```typescript
// Extended session for "Remember Me"
const maxAge = rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60 // 30 days vs 1 day
document.cookie = `user=${JSON.stringify(user)}; path=/; max-age=${maxAge}`
```

## 11. Conclusion

Bu RFC, kullanıcı deneyimini iyileştiren kapsamlı bir authentication ve redirect sistemi tanımlar. Sistem, güvenli, performanslı ve kullanıcı dostu bir yaklaşım benimser.

### 11.1 Benefits

- **Improved UX**: Otomatik yönlendirme ile sorunsuz kullanıcı deneyimi
- **Security**: Role-based access control ve route protection
- **Performance**: Optimized middleware ve cookie management
- **Maintainability**: Modüler ve test edilebilir kod yapısı

### 11.2 Implementation Status

- ✅ Next.js Middleware
- ✅ Authentication Context Updates
- ✅ Cookie Management
- ✅ Protected Route Configuration
- ✅ Auto-redirect Logic
- ✅ Error Handling
- ✅ Documentation

---

**Status**: Implemented ✅  
**Last Updated**: 2025-06-29  
**Next Review**: 2025-07-29
