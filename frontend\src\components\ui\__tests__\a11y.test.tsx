import React from 'react'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { axe, toHaveNoViolations } from 'jest-axe'
import { Button } from '../button'
import { Input } from '../input'
import ProductCard from '../product-card'
import { ThemeProvider } from '../theme-provider'
import { ThemeToggle } from '../theme-toggle'
import { Navigation } from '../navigation'

expect.extend(toHaveNoViolations)

/**
 * Comprehensive Accessibility Tests for RFC-004 UI/UX Design System
 * 
 * These tests ensure WCAG 2.1 AA compliance across all UI components
 */

describe('Accessibility Tests', () => {
  // Helper function to test component with theme provider
  const renderWithTheme = (component: React.ReactElement) => {
    return render(
      <ThemeProvider defaultTheme="light">
        {component}
      </ThemeProvider>
    )
  }

  describe('Button Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(<Button>Accessible Button</Button>)
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      const handleClick = jest.fn()
      
      render(<Button onClick={handleClick}>Keyboard Button</Button>)
      const button = screen.getByRole('button')
      
      // Tab to focus
      await user.tab()
      expect(button).toHaveFocus()
      
      // Enter to activate
      await user.keyboard('{Enter}')
      expect(handleClick).toHaveBeenCalledTimes(1)
      
      // Space to activate
      await user.keyboard(' ')
      expect(handleClick).toHaveBeenCalledTimes(2)
    })

    it('has proper ARIA attributes', () => {
      render(
        <Button 
          aria-label="Close dialog"
          aria-describedby="help-text"
          disabled
        >
          ×
        </Button>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-label', 'Close dialog')
      expect(button).toHaveAttribute('aria-describedby', 'help-text')
      expect(button).toHaveAttribute('aria-disabled', 'true')
    })

    it('has sufficient color contrast', () => {
      const { container } = render(<Button variant="primary">Primary Button</Button>)
      const button = container.querySelector('button')
      
      // Check if button has the correct CSS classes for contrast
      expect(button).toHaveClass('bg-[var(--primary-stone)]')
      expect(button).toHaveClass('text-white')
    })

    it('maintains focus visibility', async () => {
      const user = userEvent.setup()
      render(<Button>Focus Visible</Button>)
      
      const button = screen.getByRole('button')
      await user.tab()
      
      expect(button).toHaveFocus()
      expect(button).toHaveClass('focus-visible:outline-none')
      expect(button).toHaveClass('focus-visible:ring-2')
    })
  })

  describe('Input Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <div>
          <label htmlFor="test-input">Test Label</label>
          <Input id="test-input" />
        </div>
      )
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('supports proper labeling', () => {
      render(
        <div>
          <label htmlFor="email-input">Email Address</label>
          <Input id="email-input" type="email" required />
        </div>
      )
      
      const input = screen.getByLabelText('Email Address')
      expect(input).toBeInTheDocument()
      expect(input).toBeRequired()
      expect(input).toHaveAttribute('type', 'email')
    })

    it('provides error feedback accessibly', () => {
      render(
        <Input 
          error 
          helperText="This field is required"
          aria-describedby="error-message"
        />
      )
      
      const input = screen.getByRole('textbox')
      const errorMessage = screen.getByText('This field is required')
      
      expect(input).toHaveAttribute('aria-describedby', 'error-message')
      expect(errorMessage).toHaveClass('text-[var(--error)]')
    })

    it('prevents iOS zoom with proper font size', () => {
      render(<Input />)
      const input = screen.getByRole('textbox')
      
      // Should have text-base (16px) for mobile to prevent zoom
      expect(input).toHaveClass('text-base')
    })
  })

  describe('ProductCard Accessibility', () => {
    const mockProduct = {
      id: 'test-product',
      name: 'Test Product',
      category: 'Mermer',
      price: { min: 45, max: 65, currency: '$', unit: 'm²' },
      image: '/test-image.jpg',
      rating: 4.8,
      reviewCount: 127,
    }

    it('should not have accessibility violations', async () => {
      const { container } = render(<ProductCard product={mockProduct} />)
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('has proper semantic structure', () => {
      render(<ProductCard product={mockProduct} />)
      
      const card = screen.getByRole('button')
      const image = screen.getByRole('img')
      const heading = screen.getByText('Test Product')
      
      expect(card).toBeInTheDocument()
      expect(image).toHaveAttribute('alt', 'Test Product')
      expect(heading).toBeInTheDocument()
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      const handleViewDetails = jest.fn()
      
      render(
        <ProductCard 
          product={mockProduct} 
          onViewDetails={handleViewDetails}
        />
      )
      
      const card = screen.getByRole('button')
      
      await user.tab()
      expect(card).toHaveFocus()
      
      await user.keyboard('{Enter}')
      expect(handleViewDetails).toHaveBeenCalledWith('test-product')
    })

    it('provides accessible button labels', async () => {
      const user = userEvent.setup()
      render(<ProductCard product={mockProduct} />)
      
      const card = screen.getByRole('button')
      await user.hover(card)
      
      // Check for accessible button labels in overlay
      const quoteButton = screen.getByRole('button', { name: /teklif al/i })
      expect(quoteButton).toBeInTheDocument()
    })
  })

  describe('ThemeToggle Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = renderWithTheme(<ThemeToggle />)
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('provides clear state information', () => {
      renderWithTheme(<ThemeToggle showLabel />)
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('title')
    })

    it('supports keyboard interaction', async () => {
      const user = userEvent.setup()
      renderWithTheme(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      
      await user.tab()
      expect(button).toHaveFocus()
      
      await user.keyboard('{Enter}')
      // Theme should change (tested in theme provider tests)
    })

    it('switch variant has proper ARIA attributes', () => {
      renderWithTheme(<ThemeToggle variant="switch" />)
      
      const switchElement = screen.getByRole('button')
      expect(switchElement).toBeInTheDocument()
    })
  })

  describe('Navigation Accessibility', () => {
    const mockLinks = [
      { name: 'Ana Sayfa', href: '/', active: true },
      { name: 'Ürünler', href: '/products' },
      { name: 'Hakkımızda', href: '/about' },
    ]

    it('should not have accessibility violations', async () => {
      const { container } = render(
        <Navigation 
          links={mockLinks}
          brand={{ name: 'Test Site', href: '/' }}
        />
      )
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('has proper landmark roles', () => {
      render(
        <Navigation 
          links={mockLinks}
          brand={{ name: 'Test Site', href: '/' }}
        />
      )
      
      const nav = screen.getByRole('navigation')
      expect(nav).toBeInTheDocument()
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      render(
        <Navigation 
          links={mockLinks}
          brand={{ name: 'Test Site', href: '/' }}
        />
      )
      
      // Tab through navigation links
      await user.tab() // Brand link
      expect(screen.getByRole('link', { name: 'Test Site' })).toHaveFocus()
      
      await user.tab() // First nav link
      expect(screen.getByRole('link', { name: 'Ana Sayfa' })).toHaveFocus()
    })

    it('mobile menu has proper ARIA attributes', async () => {
      const user = userEvent.setup()
      render(
        <Navigation 
          links={mockLinks}
          brand={{ name: 'Test Site', href: '/' }}
        />
      )
      
      const menuButton = screen.getByRole('button', { name: /toggle mobile menu/i })
      expect(menuButton).toHaveAttribute('aria-label', 'Toggle mobile menu')
      
      await user.click(menuButton)
      // Mobile menu should be visible
    })
  })

  describe('Color Contrast', () => {
    it('primary colors meet WCAG AA standards', () => {
      const { container } = render(
        <div className="bg-[var(--primary-stone)] text-white p-4">
          Primary Stone Background
        </div>
      )
      
      const element = container.firstChild
      expect(element).toHaveClass('bg-[var(--primary-stone)]')
      expect(element).toHaveClass('text-white')
    })

    it('text colors have sufficient contrast', () => {
      const { container } = render(
        <div>
          <p className="text-[var(--text-primary)]">Primary text</p>
          <p className="text-[var(--text-secondary)]">Secondary text</p>
        </div>
      )
      
      const primaryText = container.querySelector('.text-\\[var\\(--text-primary\\)\\]')
      const secondaryText = container.querySelector('.text-\\[var\\(--text-secondary\\)\\]')
      
      expect(primaryText).toBeInTheDocument()
      expect(secondaryText).toBeInTheDocument()
    })

    it('error states have sufficient contrast', () => {
      render(<Input error helperText="Error message" />)
      
      const input = screen.getByRole('textbox')
      const errorText = screen.getByText('Error message')
      
      expect(input).toHaveClass('border-[var(--error)]')
      expect(errorText).toHaveClass('text-[var(--error)]')
    })
  })

  describe('Focus Management', () => {
    it('maintains logical tab order', async () => {
      const user = userEvent.setup()
      render(
        <div>
          <Button>First</Button>
          <Input placeholder="Second" />
          <Button>Third</Button>
        </div>
      )
      
      const firstButton = screen.getByRole('button', { name: 'First' })
      const input = screen.getByRole('textbox')
      const thirdButton = screen.getByRole('button', { name: 'Third' })
      
      await user.tab()
      expect(firstButton).toHaveFocus()
      
      await user.tab()
      expect(input).toHaveFocus()
      
      await user.tab()
      expect(thirdButton).toHaveFocus()
    })

    it('skip links work correctly', async () => {
      const user = userEvent.setup()
      render(
        <div>
          <a href="#main" className="sr-only focus:not-sr-only">
            Skip to main content
          </a>
          <nav>Navigation</nav>
          <main id="main">Main content</main>
        </div>
      )
      
      const skipLink = screen.getByRole('link', { name: /skip to main content/i })
      
      await user.tab()
      expect(skipLink).toHaveFocus()
      
      await user.keyboard('{Enter}')
      // Should focus main content
    })
  })

  describe('Screen Reader Support', () => {
    it('provides meaningful text alternatives', () => {
      render(
        <div>
          <img src="/test.jpg" alt="Carrara white marble texture" />
          <button aria-label="Add to favorites">❤️</button>
          <div aria-live="polite" aria-atomic="true">
            Status updates appear here
          </div>
        </div>
      )
      
      const image = screen.getByRole('img')
      const button = screen.getByRole('button', { name: 'Add to favorites' })
      const liveRegion = screen.getByText('Status updates appear here')
      
      expect(image).toHaveAttribute('alt', 'Carrara white marble texture')
      expect(button).toHaveAttribute('aria-label', 'Add to favorites')
      expect(liveRegion).toHaveAttribute('aria-live', 'polite')
    })

    it('uses semantic HTML elements', () => {
      render(
        <article>
          <header>
            <h1>Product Title</h1>
          </header>
          <section>
            <h2>Description</h2>
            <p>Product description</p>
          </section>
          <footer>
            <button>Add to Cart</button>
          </footer>
        </article>
      )
      
      expect(screen.getByRole('article')).toBeInTheDocument()
      expect(screen.getByRole('banner')).toBeInTheDocument() // header
      expect(screen.getByRole('contentinfo')).toBeInTheDocument() // footer
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument()
      expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument()
    })
  })
})
