// Adaptive Learning Engine Tests
// Unit tests for the self-learning AI engine

import { AdaptiveLearningEngine } from '../learning/AdaptiveLearningEngine';
import { DatabaseManager } from '../database/DatabaseManager';
import { MarketingTask, TaskResult } from '../types/ai-marketing.types';

// Mock dependencies
jest.mock('../database/DatabaseManager');
jest.mock('openai');

describe('AdaptiveLearningEngine', () => {
  let engine: AdaptiveLearningEngine;
  let mockDatabase: jest.Mocked<DatabaseManager>;

  beforeEach(() => {
    // Create mock database
    mockDatabase = {
      getLearningPatterns: jest.fn().mockResolvedValue([]),
      getMarketInsights: jest.fn().mockResolvedValue([]),
      saveLearningPattern: jest.fn().mockResolvedValue('pattern-123'),
      saveMarketInsight: jest.fn().mockResolvedValue('insight-123'),
      savePerformanceMetric: jest.fn().mockResolvedValue('metric-123'),
      logSystemEvent: jest.fn().mockResolvedValue(undefined)
    } as any;

    // Create engine instance
    engine = new AdaptiveLearningEngine(mockDatabase);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should initialize with correct name and version', () => {
      expect(engine.name).toBe('AdaptiveLearningEngine');
      expect(engine.version).toBe('1.0.0');
    });

    test('should be healthy after initialization', () => {
      expect(engine.isHealthy()).toBe(true);
    });

    test('should load existing learning data on initialization', async () => {
      const mockPatterns = [
        {
          id: 'pattern-1',
          pattern_data: { type: 'email', success: true },
          confidence: 0.85,
          success_rate: 0.9,
          context: 'email-marketing',
          usage_count: 5,
          created_at: new Date().toISOString(),
          last_used: new Date().toISOString()
        }
      ];

      const mockInsights = [
        {
          id: 'insight-1',
          insight_text: 'Email campaigns perform better on Tuesdays',
          source: 'performance_analysis',
          confidence: 0.8,
          actionable: true,
          category: 'trend',
          created_at: new Date().toISOString()
        }
      ];

      mockDatabase.getLearningPatterns.mockResolvedValue(mockPatterns);
      mockDatabase.getMarketInsights.mockResolvedValue(mockInsights);

      // Reinitialize to test loading
      const newEngine = new AdaptiveLearningEngine(mockDatabase);
      
      // Wait for initialization
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockDatabase.getLearningPatterns).toHaveBeenCalled();
      expect(mockDatabase.getMarketInsights).toHaveBeenCalled();
    });
  });

  describe('Task Execution', () => {
    test('should execute analyze_performance task successfully', async () => {
      const task: MarketingTask = {
        id: 'task-1',
        type: 'learning',
        priority: 'high',
        aiModel: 'learning',
        data: {
          action: 'analyze_performance',
          performanceData: {
            email: { openRate: 25, clickRate: 3.5 },
            social: { engagementRate: 2.8 }
          }
        },
        requiresApproval: false,
        createdAt: new Date()
      };

      const result = await engine.execute(task);

      expect(result.success).toBe(true);
      expect(result.taskId).toBe('task-1');
      expect(result.aiModel).toBe('AdaptiveLearningEngine');
      expect(result.data).toBeDefined();
    });

    test('should execute evolve_strategy task successfully', async () => {
      const task: MarketingTask = {
        id: 'task-2',
        type: 'learning',
        priority: 'medium',
        aiModel: 'learning',
        data: {
          action: 'evolve_strategy',
          currentStrategy: {
            name: 'Email Campaign',
            channels: ['email']
          },
          performanceData: {
            email: { openRate: 20, clickRate: 2.5 }
          }
        },
        requiresApproval: false,
        createdAt: new Date()
      };

      const result = await engine.execute(task);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.strategiesEvolved).toBeGreaterThanOrEqual(0);
    });

    test('should handle unknown action gracefully', async () => {
      const task: MarketingTask = {
        id: 'task-3',
        type: 'learning',
        priority: 'low',
        aiModel: 'learning',
        data: {
          action: 'unknown_action'
        },
        requiresApproval: false,
        createdAt: new Date()
      };

      const result = await engine.execute(task);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Unknown learning action');
    });

    test('should handle database errors gracefully', async () => {
      mockDatabase.saveLearningPattern.mockRejectedValue(new Error('Database error'));

      const task: MarketingTask = {
        id: 'task-4',
        type: 'learning',
        priority: 'high',
        aiModel: 'learning',
        data: {
          action: 'analyze_performance',
          performanceData: {
            email: { openRate: 25, clickRate: 3.5 }
          }
        },
        requiresApproval: false,
        createdAt: new Date()
      };

      const result = await engine.execute(task);

      // Should still succeed even if database save fails
      expect(result.success).toBe(true);
    });
  });

  describe('Learning Pattern Management', () => {
    test('should save new learning patterns to database', async () => {
      const pattern = {
        pattern: { type: 'email', timing: 'morning' },
        confidence: 0.75,
        successRate: 0.8,
        context: 'email-marketing'
      };

      // Access private method through any
      await (engine as any).saveNewLearningPattern(pattern);

      expect(mockDatabase.saveLearningPattern).toHaveBeenCalledWith({
        patternData: pattern.pattern,
        confidence: 0.75,
        successRate: 0.8,
        context: 'email-marketing'
      });
    });

    test('should handle pattern save errors gracefully', async () => {
      mockDatabase.saveLearningPattern.mockRejectedValue(new Error('Save failed'));

      const pattern = {
        pattern: { type: 'email' },
        confidence: 0.5,
        context: 'test'
      };

      // Should not throw error
      await expect((engine as any).saveNewLearningPattern(pattern)).resolves.not.toThrow();
    });
  });

  describe('Performance Analysis', () => {
    test('should analyze performance data correctly', async () => {
      const performanceData = {
        email: {
          openRate: 25,
          clickRate: 3.5,
          conversionRate: 1.2
        },
        social: {
          engagementRate: 2.8,
          reachGrowth: 12
        }
      };

      const task: MarketingTask = {
        id: 'perf-task',
        type: 'learning',
        priority: 'high',
        aiModel: 'learning',
        data: {
          action: 'analyze_performance',
          performanceData
        },
        requiresApproval: false,
        createdAt: new Date()
      };

      const result = await engine.execute(task);

      expect(result.success).toBe(true);
      expect(result.data.patternsIdentified).toBeGreaterThanOrEqual(0);
      expect(result.data.insightsGenerated).toBeGreaterThanOrEqual(0);
    });

    test('should identify success patterns from performance data', async () => {
      const performanceData = {
        email: {
          openRate: 35, // High performance
          clickRate: 5.5,
          conversionRate: 2.1
        }
      };

      // Mock successful pattern identification
      const patterns = await (engine as any).identifySuccessPatterns(performanceData);
      
      expect(Array.isArray(patterns)).toBe(true);
    });
  });

  describe('Strategy Evolution', () => {
    test('should evolve strategies based on performance', async () => {
      const currentStrategy = {
        name: 'Basic Email Campaign',
        channels: ['email'],
        targeting: { age: '25-45' }
      };

      const performanceData = {
        email: { openRate: 20, clickRate: 2.0 }
      };

      const task: MarketingTask = {
        id: 'strategy-task',
        type: 'learning',
        priority: 'high',
        aiModel: 'learning',
        data: {
          action: 'evolve_strategy',
          currentStrategy,
          performanceData
        },
        requiresApproval: false,
        createdAt: new Date()
      };

      const result = await engine.execute(task);

      expect(result.success).toBe(true);
      expect(result.data.strategiesEvolved).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Market Insights', () => {
    test('should generate market insights from data', async () => {
      const marketData = {
        trends: ['increasing mobile usage', 'video content popularity'],
        competitors: ['competitor1', 'competitor2'],
        opportunities: ['new market segment']
      };

      const task: MarketingTask = {
        id: 'insight-task',
        type: 'learning',
        priority: 'medium',
        aiModel: 'learning',
        data: {
          action: 'generate_insights',
          marketData
        },
        requiresApproval: false,
        createdAt: new Date()
      };

      const result = await engine.execute(task);

      expect(result.success).toBe(true);
      expect(result.data.insightsGenerated).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Statistics and Health', () => {
    test('should return comprehensive statistics', async () => {
      const stats = await engine.getStats();

      expect(stats).toHaveProperty('learningCycles');
      expect(stats).toHaveProperty('patternsDiscovered');
      expect(stats).toHaveProperty('strategiesEvolved');
      expect(stats).toHaveProperty('insightsGenerated');
      expect(stats).toHaveProperty('averageConfidence');
      expect(stats).toHaveProperty('successRate');
    });

    test('should handle cleanup properly', async () => {
      await expect(engine.cleanup()).resolves.not.toThrow();
    });
  });

  describe('Error Handling', () => {
    test('should handle OpenAI API errors gracefully', async () => {
      // Mock OpenAI error
      const mockOpenAI = require('openai');
      mockOpenAI.mockImplementation(() => ({
        chat: {
          completions: {
            create: jest.fn().mockRejectedValue(new Error('OpenAI API Error'))
          }
        }
      }));

      const task: MarketingTask = {
        id: 'error-task',
        type: 'learning',
        priority: 'high',
        aiModel: 'learning',
        data: {
          action: 'analyze_performance',
          performanceData: { email: { openRate: 25 } }
        },
        requiresApproval: false,
        createdAt: new Date()
      };

      const result = await engine.execute(task);

      // Should handle error gracefully
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    test('should handle malformed task data', async () => {
      const task: MarketingTask = {
        id: 'malformed-task',
        type: 'learning',
        priority: 'high',
        aiModel: 'learning',
        data: null as any, // Malformed data
        requiresApproval: false,
        createdAt: new Date()
      };

      const result = await engine.execute(task);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('Event Emission', () => {
    test('should emit events during learning cycles', (done) => {
      engine.on('learningCycleCompleted', (data) => {
        expect(data).toHaveProperty('cycle');
        expect(data).toHaveProperty('patternsIdentified');
        done();
      });

      // Trigger a learning cycle event
      (engine as any).emit('learningCycleCompleted', {
        cycle: 1,
        patternsIdentified: 2,
        insightsGenerated: 1
      });
    });

    test('should emit error events when learning fails', (done) => {
      engine.on('learningCycleError', (data) => {
        expect(data).toHaveProperty('cycle');
        expect(data).toHaveProperty('error');
        done();
      });

      // Trigger an error event
      (engine as any).emit('learningCycleError', {
        cycle: 1,
        error: new Error('Test error')
      });
    });
  });
});
