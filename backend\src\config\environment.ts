/**
 * Environment Variables Validation and Configuration
 * Ensures all required environment variables are present and valid
 */

export interface EnvironmentConfig {
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;
  FRONTEND_URL: string;
  ADMIN_URL?: string;
  
  // Database
  DATABASE_URL: string;
  
  // Redis
  REDIS_HOST: string;
  REDIS_PORT: number;
  REDIS_PASSWORD?: string;
  REDIS_DB: number;
  
  // JWT
  JWT_SECRET: string;
  JWT_REFRESH_SECRET: string;
  JWT_EXPIRES_IN: string;
  JWT_REFRESH_EXPIRES_IN: string;
  
  // Email
  SMTP_HOST: string;
  SMTP_PORT: number;
  SMTP_USER: string;
  SMTP_PASS: string;
  FROM_EMAIL: string;
  FROM_NAME: string;
  
  // Security
  BCRYPT_ROUNDS: number;
  SESSION_SECRET: string;
  
  // File Upload
  MAX_FILE_SIZE: number;
  UPLOAD_PATH: string;
  ALLOWED_FILE_TYPES: string[];
  
  // External APIs
  OPENAI_API_KEY?: string;
  GOOGLE_MAPS_API_KEY?: string;
  
  // Monitoring
  SENTRY_DSN?: string;
  LOG_LEVEL: string;
}

/**
 * Validates and parses environment variables
 */
export function validateEnvironment(): EnvironmentConfig {
  const errors: string[] = [];
  
  // Helper function to get required env var
  const getRequired = (key: string): string => {
    const value = process.env[key];
    if (!value) {
      errors.push(`Missing required environment variable: ${key}`);
      return '';
    }
    return value;
  };
  
  // Helper function to get optional env var
  const getOptional = (key: string, defaultValue: string = ''): string => {
    return process.env[key] || defaultValue;
  };
  
  // Helper function to get number env var
  const getNumber = (key: string, defaultValue?: number): number => {
    const value = process.env[key];
    if (!value) {
      if (defaultValue !== undefined) return defaultValue;
      errors.push(`Missing required environment variable: ${key}`);
      return 0;
    }
    const parsed = parseInt(value, 10);
    if (isNaN(parsed)) {
      errors.push(`Invalid number for environment variable: ${key}`);
      return 0;
    }
    return parsed;
  };
  
  // Validate NODE_ENV
  const nodeEnv = process.env.NODE_ENV as 'development' | 'production' | 'test';
  if (!['development', 'production', 'test'].includes(nodeEnv)) {
    errors.push('NODE_ENV must be one of: development, production, test');
  }
  
  // Validate JWT secrets in production
  if (nodeEnv === 'production') {
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret || jwtSecret.length < 32) {
      errors.push('JWT_SECRET must be at least 32 characters long in production');
    }
    
    const jwtRefreshSecret = process.env.JWT_REFRESH_SECRET;
    if (!jwtRefreshSecret || jwtRefreshSecret.length < 32) {
      errors.push('JWT_REFRESH_SECRET must be at least 32 characters long in production');
    }
    
    const sessionSecret = process.env.SESSION_SECRET;
    if (!sessionSecret || sessionSecret.length < 32) {
      errors.push('SESSION_SECRET must be at least 32 characters long in production');
    }
  }
  
  // Validate database URL
  const databaseUrl = getRequired('DATABASE_URL');
  if (databaseUrl && !databaseUrl.startsWith('postgresql://')) {
    errors.push('DATABASE_URL must be a valid PostgreSQL connection string');
  }
  
  // Build config object
  const config: EnvironmentConfig = {
    NODE_ENV: nodeEnv,
    PORT: getNumber('PORT', 8000),
    FRONTEND_URL: getRequired('FRONTEND_URL'),
    ADMIN_URL: getOptional('ADMIN_URL'),
    
    // Database
    DATABASE_URL: databaseUrl,
    
    // Redis
    REDIS_HOST: getRequired('REDIS_HOST'),
    REDIS_PORT: getNumber('REDIS_PORT', 6379),
    REDIS_PASSWORD: getOptional('REDIS_PASSWORD'),
    REDIS_DB: getNumber('REDIS_DB', 0),
    
    // JWT
    JWT_SECRET: getRequired('JWT_SECRET'),
    JWT_REFRESH_SECRET: getRequired('JWT_REFRESH_SECRET'),
    JWT_EXPIRES_IN: getOptional('JWT_EXPIRES_IN', '15m'),
    JWT_REFRESH_EXPIRES_IN: getOptional('JWT_REFRESH_EXPIRES_IN', '7d'),
    
    // Email
    SMTP_HOST: getRequired('SMTP_HOST'),
    SMTP_PORT: getNumber('SMTP_PORT', 587),
    SMTP_USER: getRequired('SMTP_USER'),
    SMTP_PASS: getRequired('SMTP_PASS'),
    FROM_EMAIL: getRequired('FROM_EMAIL'),
    FROM_NAME: getRequired('FROM_NAME'),
    
    // Security
    BCRYPT_ROUNDS: getNumber('BCRYPT_ROUNDS', 12),
    SESSION_SECRET: getRequired('SESSION_SECRET'),
    
    // File Upload
    MAX_FILE_SIZE: getNumber('MAX_FILE_SIZE', 10485760), // 10MB
    UPLOAD_PATH: getOptional('UPLOAD_PATH', './uploads'),
    ALLOWED_FILE_TYPES: getOptional('ALLOWED_FILE_TYPES', 'jpg,jpeg,png,pdf,doc,docx').split(','),
    
    // External APIs
    OPENAI_API_KEY: getOptional('OPENAI_API_KEY'),
    GOOGLE_MAPS_API_KEY: getOptional('GOOGLE_MAPS_API_KEY'),
    
    // Monitoring
    SENTRY_DSN: getOptional('SENTRY_DSN'),
    LOG_LEVEL: getOptional('LOG_LEVEL', 'info')
  };
  
  // Throw error if validation failed
  if (errors.length > 0) {
    console.error('❌ Environment validation failed:');
    errors.forEach(error => console.error(`  - ${error}`));
    throw new Error(`Environment validation failed: ${errors.join(', ')}`);
  }
  
  console.log('✅ Environment validation passed');
  return config;
}

// Export validated config
export const env = validateEnvironment();
