'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from './card'
import { But<PERSON> } from './button'
import { Badge } from './badge'
import { 
  Search,
  Building2,
  Mail,
  Phone,
  MapPin,
  Package,
  Star,
  Check,
  X,
  Eye
} from 'lucide-react'

// Mock producer data
const mockProducers = [
  {
    id: 'prod-001',
    companyName: 'Afyon Doğal Taş A.Ş.',
    contactPerson: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+90 ************',
    factoryAddress: {
      street: 'Sanayi Sitesi 1. Cad. No:45',
      city: 'Afyonkarahisar',
      state: 'Afyonkarahisar',
      country: 'Türkiye',
      postalCode: '03000'
    },
    specialties: ['Mermer', 'Traverten'],
    rating: 4.8,
    totalOrders: 156,
    activeProducts: 24,
    lastActivity: '2025-07-01',
    productCategories: [
      { category: 'Mermer', count: 12 },
      { category: 'Traverten', count: 8 },
      { category: 'Granit', count: 4 }
    ]
  },
  {
    id: 'prod-002',
    companyName: 'Premium Stone Co.',
    contactPerson: '<PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+90 ************',
    factoryAddress: {
      street: 'OSB 3. Cad. No:78',
      city: 'İstanbul',
      state: 'İstanbul',
      country: 'Türkiye',
      postalCode: '34500'
    },
    specialties: ['Oniks', 'Mermer', 'Granit'],
    rating: 4.9,
    totalOrders: 203,
    activeProducts: 31,
    lastActivity: '2025-06-30',
    productCategories: [
      { category: 'Oniks', count: 15 },
      { category: 'Mermer', count: 10 },
      { category: 'Granit', count: 6 }
    ]
  },
  {
    id: 'prod-003',
    companyName: 'Marmara Mermer Ltd.',
    contactPerson: 'Ali Özkan',
    email: '<EMAIL>',
    phone: '+90 ************',
    factoryAddress: {
      street: 'Mermer Sanayi Sitesi No:12',
      city: 'Balıkesir',
      state: 'Balıkesir',
      country: 'Türkiye',
      postalCode: '10000'
    },
    specialties: ['Mermer', 'Traverten'],
    rating: 4.6,
    totalOrders: 89,
    activeProducts: 18,
    lastActivity: '2025-06-28',
    productCategories: [
      { category: 'Mermer', count: 12 },
      { category: 'Traverten', count: 6 }
    ]
  }
]

interface ProducerSelectionStepProps {
  formData: any
  setFormData: (data: any) => void
}

export function ProducerSelectionStep({ formData, setFormData }: ProducerSelectionStepProps) {
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedProducer, setSelectedProducer] = React.useState(formData.producer)
  const [selectedCategory, setSelectedCategory] = React.useState('all')

  const categories = ['all', 'Mermer', 'Traverten', 'Granit', 'Oniks']

  const filteredProducers = mockProducers.filter(producer => {
    const matchesSearch = 
      producer.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      producer.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
      producer.specialties.some(s => s.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === 'all' || 
      producer.specialties.includes(selectedCategory)

    return matchesSearch && matchesCategory
  })

  const handleSelectProducer = (producer: any) => {
    setSelectedProducer(producer)
    setFormData({ ...formData, producer })
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ))
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Üretici ara (şirket adı, kişi adı, uzmanlık alanı)..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {categories.map(category => (
            <option key={category} value={category}>
              {category === 'all' ? 'Tüm Kategoriler' : category}
            </option>
          ))}
        </select>
      </div>

      {/* Producer List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 max-h-96 overflow-y-auto">
        {filteredProducers.map((producer) => (
          <Card 
            key={producer.id} 
            className={`cursor-pointer transition-all hover:shadow-md ${
              selectedProducer?.id === producer.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
            }`}
            onClick={() => handleSelectProducer(producer)}
          >
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 text-lg">{producer.companyName}</h3>
                  <p className="text-sm text-gray-600">{producer.contactPerson}</p>
                  <div className="flex items-center gap-1 mt-1">
                    {renderStars(producer.rating)}
                    <span className="text-sm text-gray-600 ml-1">({producer.rating})</span>
                  </div>
                </div>
                {selectedProducer?.id === producer.id && (
                  <Check className="w-6 h-6 text-blue-600" />
                )}
              </div>
              
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  <span>{producer.email}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  <span>{producer.phone}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>{producer.factoryAddress.city}, {producer.factoryAddress.country}</span>
                </div>
              </div>

              {/* Specialties */}
              <div className="mt-4">
                <p className="text-xs font-medium text-gray-700 mb-2">Uzmanlık Alanları:</p>
                <div className="flex flex-wrap gap-1">
                  {producer.specialties.map((specialty, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {specialty}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Product Categories */}
              <div className="mt-4">
                <p className="text-xs font-medium text-gray-700 mb-2">Ürün Kategorileri:</p>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  {producer.productCategories.map((cat, index) => (
                    <div key={index} className="flex justify-between">
                      <span className="text-gray-600">{cat.category}:</span>
                      <span className="font-medium">{cat.count} ürün</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Stats */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-lg font-semibold text-gray-900">{producer.totalOrders}</p>
                    <p className="text-xs text-gray-600">Toplam Sipariş</p>
                  </div>
                  <div>
                    <p className="text-lg font-semibold text-gray-900">{producer.activeProducts}</p>
                    <p className="text-xs text-gray-600">Aktif Ürün</p>
                  </div>
                  <div>
                    <p className="text-lg font-semibold text-green-600">Aktif</p>
                    <p className="text-xs text-gray-600">Durum</p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mt-4 flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={(e) => {
                    e.stopPropagation()
                    window.open(`/admin/producers/${producer.id}`, '_blank')
                  }}
                >
                  <Eye className="w-4 h-4 mr-1" />
                  Profil
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={(e) => {
                    e.stopPropagation()
                    alert('Ürün kataloğu yakında eklenecek')
                  }}
                >
                  <Package className="w-4 h-4 mr-1" />
                  Ürünler
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredProducers.length === 0 && (
        <div className="text-center py-12">
          <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Üretici Bulunamadı</h3>
          <p className="text-gray-600">Arama kriterlerinize uygun üretici bulunamadı.</p>
        </div>
      )}

      {/* Selected Producer Summary */}
      {selectedProducer && (
        <Card className="bg-green-50 border-green-200">
          <CardHeader>
            <CardTitle className="text-green-800 flex items-center gap-2">
              <Check className="w-5 h-5" />
              Seçilen Üretici
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-semibold text-green-900">{selectedProducer.companyName}</h3>
                <p className="text-green-700">{selectedProducer.contactPerson}</p>
                <p className="text-green-600 text-sm">{selectedProducer.email}</p>
                <div className="flex items-center gap-2 mt-2">
                  <div className="flex items-center gap-1">
                    {renderStars(selectedProducer.rating)}
                    <span className="text-sm text-green-600">({selectedProducer.rating})</span>
                  </div>
                  <span className="text-green-600 text-sm">•</span>
                  <span className="text-green-600 text-sm">{selectedProducer.activeProducts} aktif ürün</span>
                </div>
                <div className="flex flex-wrap gap-1 mt-2">
                  {selectedProducer.specialties.map((specialty: string, index: number) => (
                    <Badge key={index} variant="secondary" className="text-xs bg-green-100 text-green-800">
                      {specialty}
                    </Badge>
                  ))}
                </div>
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  setSelectedProducer(null)
                  setFormData({ ...formData, producer: null })
                }}
              >
                <X className="w-4 h-4 mr-1" />
                Değiştir
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
