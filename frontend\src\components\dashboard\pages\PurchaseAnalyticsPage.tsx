'use client';

import React from 'react';
import { ChartBarIcon } from '@heroicons/react/24/outline';

interface PurchaseAnalyticsPageProps {
  onNavigate?: (route: string) => void;
}

const PurchaseAnalyticsPage: React.FC<PurchaseAnalyticsPageProps> = ({ onNavigate }) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900"><PERSON><PERSON><PERSON></h1>
          <p className="text-gray-600 mt-1"><PERSON>ım performansınızı analiz edin ve raporlarınızı görüntüleyin</p>
        </div>
      </div>

      {/* Empty State */}
      <div className="text-center py-12">
        <ChartBarIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          <PERSON>ım verisi bulunmuyor
        </h3>
        <p className="text-gray-600 mb-6">
          <PERSON><PERSON><PERSON> yaptıktan sonra analizler burada görünecek.
        </p>
        <button
          onClick={() => onNavigate?.('/customer/dashboard')}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Dashboard'a Dön
        </button>
      </div>
    </div>
  );
};

export default PurchaseAnalyticsPage;
