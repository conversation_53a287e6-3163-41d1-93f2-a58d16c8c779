{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noImplicitOverride": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": false, "noPropertyAccessFromIndexSignature": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/modules/*": ["modules/*"], "@/shared/*": ["shared/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/middleware/*": ["middleware/*"], "@/database/*": ["database/*"]}}, "include": ["src/**/*", "prisma/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}