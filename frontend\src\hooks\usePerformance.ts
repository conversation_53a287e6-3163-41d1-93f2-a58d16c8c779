import { useEffect, useState, useCallback } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage?: number;
  connectionType?: string;
  isSlowConnection: boolean;
}

interface WebVitals {
  CLS?: number; // Cumulative Layout Shift
  FID?: number; // First Input Delay
  FCP?: number; // First Contentful Paint
  LCP?: number; // Largest Contentful Paint
  TTFB?: number; // Time to First Byte
}

export const usePerformance = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    isSlowConnection: false
  });
  
  const [webVitals, setWebVitals] = useState<WebVitals>({});
  const [isLoading, setIsLoading] = useState(true);

  // Measure page load time
  const measureLoadTime = useCallback(() => {
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const loadTime = navigation.loadEventEnd - navigation.fetchStart;
      
      setMetrics(prev => ({
        ...prev,
        loadTime: Math.round(loadTime)
      }));
    }
  }, []);

  // Measure render time
  const measureRenderTime = useCallback(() => {
    if (typeof window !== 'undefined' && window.performance) {
      const renderStart = performance.now();
      
      // Use requestAnimationFrame to measure after render
      requestAnimationFrame(() => {
        const renderEnd = performance.now();
        const renderTime = renderEnd - renderStart;
        
        setMetrics(prev => ({
          ...prev,
          renderTime: Math.round(renderTime)
        }));
      });
    }
  }, []);

  // Check connection quality
  const checkConnection = useCallback(() => {
    if (typeof window !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      const isSlowConnection = connection.effectiveType === 'slow-2g' || 
                              connection.effectiveType === '2g' ||
                              connection.downlink < 1.5;
      
      setMetrics(prev => ({
        ...prev,
        connectionType: connection.effectiveType,
        isSlowConnection
      }));
    }
  }, []);

  // Measure memory usage (if available)
  const measureMemoryUsage = useCallback(() => {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      const memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024); // MB
      
      setMetrics(prev => ({
        ...prev,
        memoryUsage
      }));
    }
  }, []);

  // Web Vitals measurement
  const measureWebVitals = useCallback(() => {
    if (typeof window !== 'undefined') {
      // Import web-vitals dynamically
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS((metric) => {
          setWebVitals(prev => ({ ...prev, CLS: metric.value }));
        });

        getFID((metric) => {
          setWebVitals(prev => ({ ...prev, FID: metric.value }));
        });

        getFCP((metric) => {
          setWebVitals(prev => ({ ...prev, FCP: metric.value }));
        });

        getLCP((metric) => {
          setWebVitals(prev => ({ ...prev, LCP: metric.value }));
        });

        getTTFB((metric) => {
          setWebVitals(prev => ({ ...prev, TTFB: metric.value }));
        });
      }).catch(() => {
        // web-vitals not available, continue without it
      });
    }
  }, []);

  // Send metrics to analytics
  const sendMetrics = useCallback((data: PerformanceMetrics & WebVitals) => {
    if (process.env.NODE_ENV === 'production') {
      // Send to your analytics service
      fetch('/api/analytics/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      }).catch(() => {
        // Silently fail analytics
      });
    }
  }, []);

  // Performance observer for monitoring
  const observePerformance = useCallback(() => {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // Observe long tasks
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.duration > 50) { // Tasks longer than 50ms
              console.warn('Long task detected:', entry.duration + 'ms');
            }
          });
        });
        longTaskObserver.observe({ entryTypes: ['longtask'] });

        // Observe layout shifts
        const layoutShiftObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (entry.value > 0.1) { // CLS threshold
              console.warn('Layout shift detected:', entry.value);
            }
          });
        });
        layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });

        return () => {
          longTaskObserver.disconnect();
          layoutShiftObserver.disconnect();
        };
      } catch (error) {
        // PerformanceObserver not supported
        return () => {};
      }
    }
    return () => {};
  }, []);

  // Main effect
  useEffect(() => {
    const cleanup = observePerformance();
    
    // Measure initial metrics
    measureLoadTime();
    measureRenderTime();
    checkConnection();
    measureMemoryUsage();
    measureWebVitals();

    // Set up periodic measurements
    const interval = setInterval(() => {
      measureMemoryUsage();
      checkConnection();
    }, 30000); // Every 30 seconds

    // Send metrics after initial load
    setTimeout(() => {
      const combinedMetrics = { ...metrics, ...webVitals };
      sendMetrics(combinedMetrics);
      setIsLoading(false);
    }, 2000);

    return () => {
      cleanup();
      clearInterval(interval);
    };
  }, []);

  // Performance optimization suggestions
  const getOptimizationSuggestions = useCallback(() => {
    const suggestions: string[] = [];

    if (metrics.loadTime > 3000) {
      suggestions.push('Page load time is slow. Consider optimizing images and reducing bundle size.');
    }

    if (metrics.memoryUsage && metrics.memoryUsage > 50) {
      suggestions.push('High memory usage detected. Check for memory leaks.');
    }

    if (webVitals.LCP && webVitals.LCP > 2500) {
      suggestions.push('Largest Contentful Paint is slow. Optimize critical resources.');
    }

    if (webVitals.CLS && webVitals.CLS > 0.1) {
      suggestions.push('Cumulative Layout Shift is high. Ensure proper image dimensions.');
    }

    if (webVitals.FID && webVitals.FID > 100) {
      suggestions.push('First Input Delay is high. Reduce JavaScript execution time.');
    }

    if (metrics.isSlowConnection) {
      suggestions.push('Slow connection detected. Consider implementing progressive loading.');
    }

    return suggestions;
  }, [metrics, webVitals]);

  // Performance score calculation
  const getPerformanceScore = useCallback(() => {
    let score = 100;

    // Deduct points based on metrics
    if (metrics.loadTime > 3000) score -= 20;
    if (metrics.loadTime > 5000) score -= 30;
    
    if (webVitals.LCP && webVitals.LCP > 2500) score -= 15;
    if (webVitals.LCP && webVitals.LCP > 4000) score -= 25;
    
    if (webVitals.CLS && webVitals.CLS > 0.1) score -= 15;
    if (webVitals.CLS && webVitals.CLS > 0.25) score -= 25;
    
    if (webVitals.FID && webVitals.FID > 100) score -= 10;
    if (webVitals.FID && webVitals.FID > 300) score -= 20;

    return Math.max(0, score);
  }, [metrics, webVitals]);

  return {
    metrics,
    webVitals,
    isLoading,
    getOptimizationSuggestions,
    getPerformanceScore,
    measureRenderTime,
    sendMetrics
  };
};

// Hook for measuring specific component performance
export const useComponentPerformance = (componentName: string) => {
  const [renderTime, setRenderTime] = useState(0);

  useEffect(() => {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      setRenderTime(duration);

      // Log slow components in development
      if (process.env.NODE_ENV === 'development' && duration > 16) {
        console.warn(`Slow component render: ${componentName} took ${duration.toFixed(2)}ms`);
      }
    };
  }, [componentName]);

  return { renderTime };
};
