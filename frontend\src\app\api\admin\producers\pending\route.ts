import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Admin token'ını cookie'den al
    const adminToken = request.cookies.get('admin_token')

    console.log('API Route - Admin token:', !!adminToken?.value)

    if (!adminToken?.value) {
      console.log('API Route - No admin token found')
      return NextResponse.json(
        { success: false, message: 'Admin token not found' },
        { status: 401 }
      )
    }

    // Backend API'ye istek yap - cookie ile
    const response = await fetch('http://localhost:8000/api/admin/producers/pending', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `admin_token=${adminToken.value}`
      }
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || 'API request failed' },
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('API proxy error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
