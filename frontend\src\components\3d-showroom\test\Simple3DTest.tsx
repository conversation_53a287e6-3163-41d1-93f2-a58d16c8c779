'use client';

import React, { useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Box, Sphere } from '@react-three/drei';
import * as THREE from 'three';

// Rotating Box Component
function RotatingBox() {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);
  const [clicked, setClicked] = useState(false);

  useFrame((state, delta) => {
    if (meshRef.current) {
      meshRef.current.rotation.x += delta * 0.5;
      meshRef.current.rotation.y += delta * 0.2;
    }
  });

  return (
    <Box
      ref={meshRef}
      args={[1, 1, 1]}
      scale={clicked ? 1.5 : 1}
      onClick={() => setClicked(!clicked)}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    >
      <meshStandardMaterial color={hovered ? 'hotpink' : '#f59e0b'} />
    </Box>
  );
}

// Floating Sphere Component
function FloatingSphere() {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime) * 0.5;
    }
  });

  return (
    <Sphere ref={meshRef} args={[0.5, 32, 32]} position={[2, 0, 0]}>
      <meshStandardMaterial color="#10b981" />
    </Sphere>
  );
}

// Ground Plane Component
function Ground() {
  return (
    <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1, 0]} receiveShadow>
      <planeGeometry args={[10, 10]} />
      <meshStandardMaterial color="#f3f4f6" />
    </mesh>
  );
}

// Main Simple3DTest Component
export const Simple3DTest: React.FC = () => {
  const [showWireframe, setShowWireframe] = useState(false);
  const [autoRotate, setAutoRotate] = useState(true);

  return (
    <div className="w-full h-96 bg-gray-100 rounded-lg overflow-hidden relative">
      {/* Controls */}
      <div className="absolute top-4 left-4 z-10 flex gap-2">
        <button
          onClick={() => setShowWireframe(!showWireframe)}
          className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
            showWireframe 
              ? 'bg-amber-500 text-white' 
              : 'bg-white text-gray-700 hover:bg-gray-50'
          }`}
        >
          Wireframe
        </button>
        
        <button
          onClick={() => setAutoRotate(!autoRotate)}
          className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
            autoRotate 
              ? 'bg-green-500 text-white' 
              : 'bg-white text-gray-700 hover:bg-gray-50'
          }`}
        >
          Auto Rotate
        </button>
      </div>

      {/* Info */}
      <div className="absolute bottom-4 left-4 z-10 bg-black/70 text-white px-3 py-2 rounded-lg text-sm">
        <div>Three.js Test - Kutuya tıklayın!</div>
        <div className="text-xs opacity-75">Mouse ile döndürün, zoom yapın</div>
      </div>

      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [3, 3, 3], fov: 50 }}
        shadows
        dpr={[1, 2]}
        gl={{ antialias: true }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.4} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />

        {/* 3D Objects */}
        <RotatingBox />
        <FloatingSphere />
        <Ground />

        {/* Wireframe overlay */}
        {showWireframe && (
          <mesh>
            <boxGeometry args={[1, 1, 1]} />
            <meshBasicMaterial wireframe color="#ff0000" />
          </mesh>
        )}

        {/* Controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          autoRotate={autoRotate}
          autoRotateSpeed={2}
        />
      </Canvas>
    </div>
  );
};

export default Simple3DTest;
