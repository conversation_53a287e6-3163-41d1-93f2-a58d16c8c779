/**
 * Conversation Manager
 * Manages conversation state, context, and flow
 */

import { v4 as uuidv4 } from 'uuid';
import Redis from 'ioredis';
import { 
  ConversationContext, 
  ChatMessage, 
  ChatbotIntent, 
  ExtractedEntity,
  UserProfile,
  ChatbotResponse 
} from '../types';

export class ConversationManager {
  private redis: Redis;
  private conversationTTL = 24 * 60 * 60; // 24 hours in seconds

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      maxRetriesPerRequest: 3,
    });
  }

  /**
   * Create new conversation session
   */
  async createSession(userId?: string, language: string = 'en'): Promise<string> {
    const sessionId = uuidv4();
    
    const context: ConversationContext = {
      sessionId,
      userId,
      language,
      currentIntent: ChatbotIntent.GREETING,
      entities: [],
      conversationHistory: [],
      escalationLevel: 0,
      resolvedIssues: [],
      unresolvedIssues: [],
      confidence: 1.0,
      sentiment: 0.0
    };

    await this.saveContext(sessionId, context);
    return sessionId;
  }

  /**
   * Get conversation context
   */
  async getContext(sessionId: string): Promise<ConversationContext | null> {
    try {
      const contextStr = await this.redis.get(`conversation:${sessionId}`);
      if (!contextStr) return null;

      const context = JSON.parse(contextStr);
      
      // Convert date strings back to Date objects
      context.conversationHistory = context.conversationHistory.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }));

      return context;
    } catch (error) {
      console.error('Error getting conversation context:', error);
      return null;
    }
  }

  /**
   * Save conversation context
   */
  async saveContext(sessionId: string, context: ConversationContext): Promise<void> {
    try {
      const contextStr = JSON.stringify(context);
      await this.redis.setex(`conversation:${sessionId}`, this.conversationTTL, contextStr);
    } catch (error) {
      console.error('Error saving conversation context:', error);
      throw error;
    }
  }

  /**
   * Add message to conversation
   */
  async addMessage(
    sessionId: string, 
    role: 'user' | 'assistant' | 'system',
    content: string,
    intent?: ChatbotIntent,
    confidence?: number,
    entities?: ExtractedEntity[]
  ): Promise<void> {
    const context = await this.getContext(sessionId);
    if (!context) {
      throw new Error('Conversation not found');
    }

    const message: ChatMessage = {
      id: uuidv4(),
      role,
      content,
      timestamp: new Date(),
      language: context.language,
      intent,
      confidence,
      entities
    };

    context.conversationHistory.push(message);
    
    // Update current intent if provided
    if (intent) {
      context.currentIntent = intent;
    }

    // Update entities
    if (entities) {
      context.entities = [...context.entities, ...entities];
    }

    // Keep only last 50 messages to prevent memory issues
    if (context.conversationHistory.length > 50) {
      context.conversationHistory = context.conversationHistory.slice(-50);
    }

    await this.saveContext(sessionId, context);
  }

  /**
   * Update conversation context
   */
  async updateContext(
    sessionId: string,
    updates: Partial<ConversationContext>
  ): Promise<void> {
    const context = await this.getContext(sessionId);
    if (!context) {
      throw new Error('Conversation not found');
    }

    Object.assign(context, updates);
    await this.saveContext(sessionId, context);
  }

  /**
   * Get conversation history
   */
  async getHistory(sessionId: string, limit: number = 20): Promise<ChatMessage[]> {
    const context = await this.getContext(sessionId);
    if (!context) return [];

    return context.conversationHistory.slice(-limit);
  }

  /**
   * Delete conversation
   */
  async deleteConversation(sessionId: string): Promise<void> {
    try {
      await this.redis.del(`conversation:${sessionId}`);
    } catch (error) {
      console.error('Error deleting conversation:', error);
      throw error;
    }
  }

  /**
   * Get active conversations for a user
   */
  async getUserConversations(userId: string): Promise<string[]> {
    try {
      const keys = await this.redis.keys('conversation:*');
      const userSessions: string[] = [];

      for (const key of keys) {
        const contextStr = await this.redis.get(key);
        if (contextStr) {
          const context = JSON.parse(contextStr);
          if (context.userId === userId) {
            userSessions.push(context.sessionId);
          }
        }
      }

      return userSessions;
    } catch (error) {
      console.error('Error getting user conversations:', error);
      return [];
    }
  }

  /**
   * Get conversation statistics
   */
  async getConversationStats(sessionId: string): Promise<{
    messageCount: number;
    duration: number;
    intents: Record<string, number>;
    averageConfidence: number;
    escalationLevel: number;
  }> {
    const context = await this.getContext(sessionId);
    if (!context) {
      throw new Error('Conversation not found');
    }

    const messageCount = context.conversationHistory.length;
    const firstMessage = context.conversationHistory[0];
    const lastMessage = context.conversationHistory[messageCount - 1];
    
    const duration = firstMessage && lastMessage 
      ? lastMessage.timestamp.getTime() - firstMessage.timestamp.getTime()
      : 0;

    const intents: Record<string, number> = {};
    let totalConfidence = 0;
    let confidenceCount = 0;

    context.conversationHistory.forEach(msg => {
      if (msg.intent) {
        intents[msg.intent] = (intents[msg.intent] || 0) + 1;
      }
      if (msg.confidence !== undefined) {
        totalConfidence += msg.confidence;
        confidenceCount++;
      }
    });

    const averageConfidence = confidenceCount > 0 ? totalConfidence / confidenceCount : 0;

    return {
      messageCount,
      duration,
      intents,
      averageConfidence,
      escalationLevel: context.escalationLevel
    };
  }

  /**
   * Check if conversation should be escalated
   */
  shouldEscalate(context: ConversationContext): boolean {
    // Low confidence threshold
    if (context.confidence < 0.3) return true;

    // Negative sentiment threshold
    if (context.sentiment < -0.5) return true;

    // Multiple unresolved issues
    if (context.unresolvedIssues.length >= 3) return true;

    // Long conversation without resolution
    if (context.conversationHistory.length > 20 && context.resolvedIssues.length === 0) return true;

    // Explicit request for human agent
    if (context.currentIntent === ChatbotIntent.HUMAN_HANDOFF) return true;

    // Complaint intent
    if (context.currentIntent === ChatbotIntent.COMPLAINT) return true;

    return false;
  }

  /**
   * Mark issue as resolved
   */
  async markIssueResolved(sessionId: string, issue: string): Promise<void> {
    const context = await this.getContext(sessionId);
    if (!context) return;

    if (!context.resolvedIssues.includes(issue)) {
      context.resolvedIssues.push(issue);
    }

    // Remove from unresolved issues if present
    context.unresolvedIssues = context.unresolvedIssues.filter(i => i !== issue);

    await this.saveContext(sessionId, context);
  }

  /**
   * Mark issue as unresolved
   */
  async markIssueUnresolved(sessionId: string, issue: string): Promise<void> {
    const context = await this.getContext(sessionId);
    if (!context) return;

    if (!context.unresolvedIssues.includes(issue)) {
      context.unresolvedIssues.push(issue);
    }

    await this.saveContext(sessionId, context);
  }

  /**
   * Set user profile
   */
  async setUserProfile(sessionId: string, profile: UserProfile): Promise<void> {
    await this.updateContext(sessionId, { userProfile: profile });
  }

  /**
   * Update sentiment score
   */
  async updateSentiment(sessionId: string, sentiment: number): Promise<void> {
    await this.updateContext(sessionId, { sentiment });
  }

  /**
   * Update confidence score
   */
  async updateConfidence(sessionId: string, confidence: number): Promise<void> {
    await this.updateContext(sessionId, { confidence });
  }

  /**
   * Get all active conversations (for admin)
   */
  async getAllActiveConversations(): Promise<Array<{
    sessionId: string;
    userId?: string;
    language: string;
    messageCount: number;
    lastActivity: Date;
    escalationLevel: number;
  }>> {
    try {
      const keys = await this.redis.keys('conversation:*');
      const conversations = [];

      for (const key of keys) {
        const contextStr = await this.redis.get(key);
        if (contextStr) {
          const context = JSON.parse(contextStr);
          const lastMessage = context.conversationHistory[context.conversationHistory.length - 1];
          
          conversations.push({
            sessionId: context.sessionId,
            userId: context.userId,
            language: context.language,
            messageCount: context.conversationHistory.length,
            lastActivity: lastMessage ? new Date(lastMessage.timestamp) : new Date(),
            escalationLevel: context.escalationLevel
          });
        }
      }

      return conversations.sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime());
    } catch (error) {
      console.error('Error getting all conversations:', error);
      return [];
    }
  }

  /**
   * Cleanup expired conversations
   */
  async cleanupExpiredConversations(): Promise<number> {
    try {
      const keys = await this.redis.keys('conversation:*');
      let deletedCount = 0;
      const now = new Date();
      const expirationTime = 24 * 60 * 60 * 1000; // 24 hours

      for (const key of keys) {
        const contextStr = await this.redis.get(key);
        if (contextStr) {
          const context = JSON.parse(contextStr);
          const lastMessage = context.conversationHistory[context.conversationHistory.length - 1];
          
          if (lastMessage) {
            const lastActivity = new Date(lastMessage.timestamp);
            if (now.getTime() - lastActivity.getTime() > expirationTime) {
              await this.redis.del(key);
              deletedCount++;
            }
          }
        }
      }

      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up conversations:', error);
      return 0;
    }
  }
}
