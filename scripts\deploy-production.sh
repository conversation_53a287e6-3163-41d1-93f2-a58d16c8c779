#!/bin/bash

# Production Deployment Script
# Türkiye Doğal Taş Pazaryeri - Production Deployment

set -e  # Exit on any error

echo "🚀 Starting Production Deployment..."
echo "=================================="

# Configuration
DOMAIN=${DOMAIN:-"yourdomain.com"}
BACKUP_BEFORE_DEPLOY=${BACKUP_BEFORE_DEPLOY:-"true"}
HEALTH_CHECK_TIMEOUT=${HEALTH_CHECK_TIMEOUT:-"300"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if .env.production exists
    if [[ ! -f ".env.production" ]]; then
        log_error ".env.production file not found"
        log_info "Please create .env.production with your production configuration"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Create backup before deployment
create_backup() {
    if [[ "$BACKUP_BEFORE_DEPLOY" == "true" ]]; then
        log_info "Creating backup before deployment..."
        
        # Check if database is running
        if docker-compose -f docker-compose.production.yml ps postgres | grep -q "Up"; then
            docker-compose -f docker-compose.production.yml exec -T postgres pg_dump -U postgres natural_stone_marketplace_prod > "backup-$(date +%Y%m%d-%H%M%S).sql"
            log_success "Database backup created"
        else
            log_warning "Database not running, skipping backup"
        fi
    fi
}

# Build Docker images
build_images() {
    log_info "Building Docker images..."
    
    # Build backend
    log_info "Building backend image..."
    docker build -f backend/Dockerfile.production -t natural-stone-backend:latest ./backend
    
    # Build frontend
    log_info "Building frontend image..."
    docker build -f frontend/Dockerfile.production -t natural-stone-frontend:latest ./frontend
    
    log_success "Docker images built successfully"
}

# Deploy services
deploy_services() {
    log_info "Deploying services..."
    
    # Stop existing services
    log_info "Stopping existing services..."
    docker-compose -f docker-compose.production.yml down
    
    # Start services
    log_info "Starting services..."
    docker-compose -f docker-compose.production.yml up -d
    
    log_success "Services deployed"
}

# Wait for services to be healthy
wait_for_health() {
    log_info "Waiting for services to be healthy..."
    
    local timeout=$HEALTH_CHECK_TIMEOUT
    local elapsed=0
    local interval=10
    
    while [[ $elapsed -lt $timeout ]]; do
        # Check backend health
        if curl -f -s http://localhost:8000/health > /dev/null; then
            log_success "Backend is healthy"
            break
        fi
        
        log_info "Waiting for backend to be healthy... (${elapsed}s/${timeout}s)"
        sleep $interval
        elapsed=$((elapsed + interval))
    done
    
    if [[ $elapsed -ge $timeout ]]; then
        log_error "Backend health check timeout"
        return 1
    fi
    
    # Check frontend health
    elapsed=0
    while [[ $elapsed -lt $timeout ]]; do
        if curl -f -s http://localhost:3000 > /dev/null; then
            log_success "Frontend is healthy"
            break
        fi
        
        log_info "Waiting for frontend to be healthy... (${elapsed}s/${timeout}s)"
        sleep $interval
        elapsed=$((elapsed + interval))
    done
    
    if [[ $elapsed -ge $timeout ]]; then
        log_error "Frontend health check timeout"
        return 1
    fi
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Wait for database to be ready
    sleep 10
    
    # Run migrations
    docker-compose -f docker-compose.production.yml exec -T backend npx prisma migrate deploy
    
    log_success "Database migrations completed"
}

# Setup SSL certificates
setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    # Create SSL directory
    mkdir -p nginx/ssl
    
    # Check if certificates exist
    if [[ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]]; then
        # Copy Let's Encrypt certificates
        sudo cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" nginx/ssl/
        sudo cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" nginx/ssl/
        sudo chown $(whoami):$(whoami) nginx/ssl/*
        log_success "SSL certificates copied from Let's Encrypt"
    else
        # Generate self-signed certificates for testing
        log_warning "Let's Encrypt certificates not found, generating self-signed certificates"
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/privkey.pem \
            -out nginx/ssl/fullchain.pem \
            -subj "/C=TR/ST=Istanbul/L=Istanbul/O=Natural Stone Marketplace/CN=$DOMAIN"
        log_success "Self-signed certificates generated"
    fi
}

# Cleanup old images and containers
cleanup() {
    log_info "Cleaning up old images and containers..."
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused containers
    docker container prune -f
    
    # Remove unused volumes (be careful with this)
    # docker volume prune -f
    
    log_success "Cleanup completed"
}

# Show deployment status
show_status() {
    log_info "Deployment Status:"
    echo "=================="
    
    # Show running containers
    docker-compose -f docker-compose.production.yml ps
    
    echo ""
    log_info "Service URLs:"
    echo "Frontend: https://$DOMAIN"
    echo "Backend API: https://$DOMAIN/api"
    echo "Health Check: https://$DOMAIN/api/health"
    echo "Grafana: http://localhost:3001"
    echo "Prometheus: http://localhost:9090"
}

# Main deployment function
main() {
    log_info "Starting deployment for domain: $DOMAIN"
    
    check_root
    check_prerequisites
    create_backup
    setup_ssl
    build_images
    deploy_services
    run_migrations
    
    if wait_for_health; then
        cleanup
        show_status
        log_success "🎉 Production deployment completed successfully!"
        
        echo ""
        log_info "Next steps:"
        echo "1. Update your DNS records to point to this server"
        echo "2. Test all functionality"
        echo "3. Monitor logs: docker-compose -f docker-compose.production.yml logs -f"
        echo "4. Set up monitoring alerts"
        
    else
        log_error "❌ Deployment failed - services are not healthy"
        log_info "Check logs: docker-compose -f docker-compose.production.yml logs"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --no-backup         Skip backup before deployment"
        echo "  --domain DOMAIN     Set domain name (default: yourdomain.com)"
        echo ""
        echo "Environment variables:"
        echo "  DOMAIN              Domain name"
        echo "  BACKUP_BEFORE_DEPLOY Set to 'false' to skip backup"
        echo "  HEALTH_CHECK_TIMEOUT Health check timeout in seconds"
        exit 0
        ;;
    --no-backup)
        BACKUP_BEFORE_DEPLOY="false"
        shift
        ;;
    --domain)
        DOMAIN="$2"
        shift 2
        ;;
esac

# Run main function
main "$@"
