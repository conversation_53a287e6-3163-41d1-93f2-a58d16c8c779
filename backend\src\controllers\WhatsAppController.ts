import { Request, Response } from 'express';
import { WhatsAppService } from '../services/WhatsAppService';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/authMiddleware';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Validation schemas
const sendPaymentInstructionsSchema = z.object({
  customerPhone: z.string().min(10),
  customerName: z.string().min(1),
  orderNumber: z.string().min(1),
  amount: z.number().positive(),
  currency: z.string().default('TRY'),
  referenceCode: z.string().min(1),
  bankInfo: z.object({
    bankName: z.string(),
    iban: z.string(),
    accountHolder: z.string(),
  }),
});

const sendOrderStatusSchema = z.object({
  customerPhone: z.string().min(10),
  customerName: z.string().min(1),
  orderNumber: z.string().min(1),
  status: z.string().min(1),
  message: z.string().min(1),
});

const sendEscrowNotificationSchema = z.object({
  customerPhone: z.string().min(10),
  customerName: z.string().min(1),
  orderNumber: z.string().min(1),
  type: z.enum(['payment_confirmed', 'goods_ready', 'payment_released']),
  amount: z.number().positive().optional(),
  currency: z.string().default('TRY'),
});

const generateSupportURLSchema = z.object({
  name: z.string().optional(),
  orderNumber: z.string().optional(),
  issue: z.string().optional(),
});

const updateConfigSchema = z.object({
  businessNumber: z.string().optional(),
  enabled: z.boolean().optional(),
  autoReply: z.boolean().optional(),
  businessHours: z.object({
    start: z.string().optional(),
    end: z.string().optional(),
    timezone: z.string().optional(),
  }).optional(),
});

export class WhatsAppController {
  private whatsappService: WhatsAppService;

  constructor() {
    this.whatsappService = new WhatsAppService();
  }

  /**
   * Send payment instructions via WhatsApp
   * @route POST /api/whatsapp/payment-instructions
   * @access Private (Admin or System)
   */
  sendPaymentInstructions = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user;
    
    if (!user) {
      return res.status(403).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const data = sendPaymentInstructionsSchema.parse(req.body);

    const result = await this.whatsappService.sendPaymentInstructions(
      data.customerPhone,
      data
    );

    res.json({
      success: true,
      data: result,
      message: 'Payment instructions WhatsApp URL generated'
    });
  });

  /**
   * Send order status update via WhatsApp
   * @route POST /api/whatsapp/order-status
   * @access Private (Admin, Producer, or System)
   */
  sendOrderStatus = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user;
    
    if (!user || !['admin', 'producer', 'system'].includes(user.userType)) {
      return res.status(403).json({
        success: false,
        error: 'Admin, producer, or system access required'
      });
    }

    const data = sendOrderStatusSchema.parse(req.body);

    const result = await this.whatsappService.sendOrderStatusUpdate(
      data.customerPhone,
      data
    );

    res.json({
      success: true,
      data: result,
      message: 'Order status WhatsApp URL generated'
    });
  });

  /**
   * Send escrow notification via WhatsApp
   * @route POST /api/whatsapp/escrow-notification
   * @access Private (Admin or System)
   */
  sendEscrowNotification = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user;
    
    if (!user) {
      return res.status(403).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const data = sendEscrowNotificationSchema.parse(req.body);

    const result = await this.whatsappService.sendEscrowNotification(
      data.customerPhone,
      data
    );

    res.json({
      success: true,
      data: result,
      message: 'Escrow notification WhatsApp URL generated'
    });
  });

  /**
   * Generate customer support WhatsApp URL
   * @route POST /api/whatsapp/support-url
   * @access Public
   */
  generateSupportURL = asyncHandler(async (req: Request, res: Response) => {
    const data = generateSupportURLSchema.parse(req.body);

    const supportURL = this.whatsappService.generateSupportURL(data);

    res.json({
      success: true,
      data: { supportURL },
      message: 'Support WhatsApp URL generated'
    });
  });

  /**
   * Get business WhatsApp URL
   * @route GET /api/whatsapp/business-url
   * @access Public
   */
  getBusinessURL = asyncHandler(async (req: Request, res: Response) => {
    const message = req.query.message as string;
    
    const businessURL = this.whatsappService.generateBusinessWhatsAppURL(message);

    res.json({
      success: true,
      data: { businessURL },
      message: 'Business WhatsApp URL generated'
    });
  });

  /**
   * Get auto-reply message
   * @route GET /api/whatsapp/auto-reply
   * @access Public
   */
  getAutoReply = asyncHandler(async (req: Request, res: Response) => {
    const autoReplyMessage = this.whatsappService.getAutoReplyMessage();
    const isWithinBusinessHours = this.whatsappService.isWithinBusinessHours();

    res.json({
      success: true,
      data: {
        message: autoReplyMessage,
        isWithinBusinessHours,
      }
    });
  });

  /**
   * Get WhatsApp configuration
   * @route GET /api/whatsapp/config
   * @access Private (Admin only)
   */
  getConfig = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const config = this.whatsappService.getConfig();

    res.json({
      success: true,
      data: config
    });
  });

  /**
   * Update WhatsApp configuration
   * @route PUT /api/whatsapp/config
   * @access Private (Admin only)
   */
  updateConfig = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const newConfig = updateConfigSchema.parse(req.body);

    // BusinessHours tipini düzelt
    const configToUpdate: any = { ...newConfig };
    if (configToUpdate.businessHours) {
      const { start, end, timezone } = configToUpdate.businessHours;
      if (start && end && timezone) {
        configToUpdate.businessHours = { start, end, timezone };
      } else {
        delete configToUpdate.businessHours;
      }
    }

    this.whatsappService.updateConfig(configToUpdate);

    res.json({
      success: true,
      message: 'WhatsApp configuration updated successfully'
    });
  });

  /**
   * Get WhatsApp widget configuration
   * @route GET /api/whatsapp/widget-config
   * @access Public
   */
  getWidgetConfig = asyncHandler(async (req: Request, res: Response) => {
    const config = this.whatsappService.getConfig();
    const businessURL = this.whatsappService.generateBusinessWhatsAppURL();

    res.json({
      success: true,
      data: {
        enabled: config.enabled,
        businessNumber: config.businessNumber,
        businessURL,
        autoReply: config.autoReply,
        isWithinBusinessHours: this.whatsappService.isWithinBusinessHours(),
      }
    });
  });

  /**
   * Generate WhatsApp URL for specific use case
   * @route POST /api/whatsapp/generate-url
   * @access Private
   */
  generateURL = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const { phoneNumber, message, useCase } = req.body;

    if (!phoneNumber || !message) {
      return res.status(400).json({
        success: false,
        error: 'Phone number and message are required'
      });
    }

    let whatsappURL: string;

    if (useCase === 'support') {
      whatsappURL = this.whatsappService.generateSupportURL({
        name: user.email,
        issue: message,
      });
    } else {
      whatsappURL = this.whatsappService.generateWhatsAppURL(phoneNumber, message);
    }

    res.json({
      success: true,
      data: { whatsappURL },
      message: 'WhatsApp URL generated successfully'
    });
  });

  /**
   * Get WhatsApp health status
   * @route GET /api/whatsapp/health
   * @access Private (Admin only)
   */
  getHealth = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const config = this.whatsappService.getConfig();
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      configuration: {
        enabled: config.enabled,
        businessNumber: config.businessNumber,
        autoReply: config.autoReply,
        businessHours: config.businessHours,
      },
      businessStatus: {
        isWithinBusinessHours: this.whatsappService.isWithinBusinessHours(),
        currentTime: new Date().toLocaleTimeString('tr-TR', { 
          timeZone: config.businessHours.timezone 
        }),
      },
    };

    res.json({
      success: true,
      data: health
    });
  });
}
