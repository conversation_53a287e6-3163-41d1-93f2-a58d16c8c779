'use client'

import * as React from 'react'
import { useStock } from '@/contexts/stock-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import {
  Package,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  AlertTriangle
} from 'lucide-react'

export default function AdminStockPage() {
  const { stockItems, approveStockItem, rejectStockItem, getPendingStockItems } = useStock()
  const [selectedTab, setSelectedTab] = React.useState<'pending' | 'all'>('pending')
  const [rejectingItem, setRejectingItem] = React.useState<string | null>(null)
  const [rejectionReason, setRejectionReason] = React.useState('')
  const [isProcessing, setIsProcessing] = React.useState(false)

  const pendingItems = getPendingStockItems()
  const displayItems = selectedTab === 'pending' ? pendingItems : stockItems

  const handleApprove = async (itemId: string) => {
    setIsProcessing(true)
    try {
      approveStockItem(itemId, 'Admin')
      alert('Stok ürün onaylandı!')
    } catch (error) {
      console.error('Error approving stock item:', error)
      alert('Onaylama sırasında bir hata oluştu')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRejectClick = (itemId: string) => {
    setRejectingItem(itemId)
    setRejectionReason('')
  }

  const handleRejectConfirm = async () => {
    if (!rejectingItem || !rejectionReason.trim()) {
      alert('Red sebebi zorunludur')
      return
    }

    setIsProcessing(true)
    try {
      rejectStockItem(rejectingItem, rejectionReason.trim(), 'Admin')
      setRejectingItem(null)
      setRejectionReason('')
      alert('Stok ürün reddedildi!')
    } catch (error) {
      console.error('Error rejecting stock item:', error)
      alert('Reddetme sırasında bir hata oluştu')
    } finally {
      setIsProcessing(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Bekliyor</Badge>
      case 'approved':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Onaylandı</Badge>
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Reddedildi</Badge>
      default:
        return <Badge variant="outline">Bilinmeyen</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Stok Yönetimi</h1>
          <p className="text-gray-600">
            Üretici stok taleplerini inceleyin ve onaylayın
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {stockItems.filter(item => item.status === 'pending').length}
            </div>
            <div className="text-sm text-gray-600">Bekleyen</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {stockItems.filter(item => item.status === 'approved').length}
            </div>
            <div className="text-sm text-gray-600">Onaylanan</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {stockItems.filter(item => item.status === 'rejected').length}
            </div>
            <div className="text-sm text-gray-600">Reddedilen</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {stockItems.length}
            </div>
            <div className="text-sm text-gray-600">Toplam</div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <button
          onClick={() => setSelectedTab('pending')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            selectedTab === 'pending'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Bekleyen ({pendingItems.length})
        </button>
        <button
          onClick={() => setSelectedTab('all')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            selectedTab === 'all'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Tümü ({stockItems.length})
        </button>
      </div>

      {/* Stock Items */}
      <div className="space-y-4">
        {displayItems.map((stockItem) => (
          <Card key={stockItem.id} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex gap-6">
                {/* Image */}
                <div className="w-32 h-32 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                  <img
                    src={stockItem.image?.url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'}
                    alt={stockItem.productName}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'
                    }}
                  />
                </div>

                {/* Content */}
                <div className="flex-1 space-y-4">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{stockItem.productName}</h3>
                      <p className="text-gray-600">{stockItem.producerName}</p>
                      <p className="text-sm text-gray-500">
                        Gönderilme: {stockItem.submittedAt.toLocaleDateString('tr-TR')}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(stockItem.status)}
                    </div>
                  </div>

                  {/* Details Table */}
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300 text-sm">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border border-gray-300 px-3 py-2 text-left">Metraj (m²)</th>
                          <th className="border border-gray-300 px-3 py-2 text-left">Kalınlık (cm)</th>
                          <th className="border border-gray-300 px-3 py-2 text-left">En (cm)</th>
                          <th className="border border-gray-300 px-3 py-2 text-left">Boy (cm)</th>
                          <th className="border border-gray-300 px-3 py-2 text-left">Fiyat</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td className="border border-gray-300 px-3 py-2">{stockItem.metraj}</td>
                          <td className="border border-gray-300 px-3 py-2">{stockItem.thickness}</td>
                          <td className="border border-gray-300 px-3 py-2">{stockItem.width}</td>
                          <td className="border border-gray-300 px-3 py-2">{stockItem.length}</td>
                          <td className="border border-gray-300 px-3 py-2">{stockItem.price} {stockItem.currency}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  {/* Rejection Reason */}
                  {stockItem.status === 'rejected' && stockItem.rejectionReason && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="text-sm font-medium text-red-800">Red Sebebi:</p>
                          <p className="text-sm text-red-700">{stockItem.rejectionReason}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  {stockItem.status === 'pending' && (
                    <div className="flex gap-3">
                      <Button
                        onClick={() => handleApprove(stockItem.id)}
                        disabled={isProcessing}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Onayla
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleRejectClick(stockItem.id)}
                        disabled={isProcessing}
                        className="text-red-600 hover:text-red-700 border-red-300"
                      >
                        <XCircle className="w-4 h-4 mr-2" />
                        Reddet
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {displayItems.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {selectedTab === 'pending' ? 'Bekleyen stok talebi yok' : 'Stok talebi bulunamadı'}
            </h3>
            <p className="text-gray-600">
              {selectedTab === 'pending' 
                ? 'Şu anda onay bekleyen stok talebi bulunmamaktadır.'
                : 'Henüz hiç stok talebi gönderilmemiş.'
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Rejection Modal */}
      {rejectingItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Stok Talebini Reddet</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Red Sebebi <span className="text-red-500">*</span>
                  </label>
                  <Textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    placeholder="Stok talebini neden reddediyorsunuz?"
                    rows={4}
                    className="w-full"
                  />
                </div>
              </div>
              <div className="flex justify-end gap-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setRejectingItem(null)
                    setRejectionReason('')
                  }}
                  disabled={isProcessing}
                >
                  İptal
                </Button>
                <Button
                  onClick={handleRejectConfirm}
                  disabled={isProcessing || !rejectionReason.trim()}
                  className="bg-red-600 hover:bg-red-700"
                >
                  {isProcessing ? 'Reddediliyor...' : 'Reddet'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
