import { Router } from 'express';

const router = Router();

/**
 * @swagger
 * /api/notifications:
 *   get:
 *     summary: Get user notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Notifications retrieved successfully
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Notifications endpoint - Coming soon'
  });
});

export default router;
