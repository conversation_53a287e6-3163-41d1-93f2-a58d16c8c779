'use client';

import React, { useState, useEffect, useRef } from 'react';
import { 
  Home, 
  Grid, 
  Palette, 
  <PERSON>ting<PERSON>, 
  RotateCw,
  Maximize,
  Eye,
  Save,
  Download,
  Share2
} from 'lucide-react';
import { 
  RoomSimulationConfiguration,
  RoomTemplate,
  TilingPattern,
  GroutOption,
  AdvancedProductConfig 
} from '../../types/3d';

interface RoomSimulatorProps {
  productId: string;
  configuration: RoomSimulationConfiguration;
  currentConfig: AdvancedProductConfig;
  onConfigChange: (config: Partial<AdvancedProductConfig>) => void;
  onRoomUpdate: (roomData: RoomVisualizationData) => void;
  className?: string;
}

interface RoomVisualizationData {
  room: RoomTemplate;
  pattern: TilingPattern;
  grout: GroutOption;
  coverage: number;
  tileCount: number;
  wastePercentage: number;
}

export const RoomSimulator: React.FC<RoomSimulatorProps> = ({
  productId,
  configuration,
  currentConfig,
  onConfigChange,
  onRoomUpdate,
  className = ''
}) => {
  const [selectedRoom, setSelectedRoom] = useState<RoomTemplate | null>(currentConfig.room || null);
  const [selectedPattern, setSelectedPattern] = useState<TilingPattern>(
    currentConfig.pattern || configuration.tilingPatterns[0]
  );
  const [selectedGrout, setSelectedGrout] = useState<GroutOption>(
    currentConfig.grout || configuration.groutOptions[0]
  );
  const [viewMode, setViewMode] = useState<'2d' | '3d'>('3d');
  const [isGenerating, setIsGenerating] = useState(false);
  const [roomData, setRoomData] = useState<RoomVisualizationData | null>(null);

  // Calculate room visualization data
  const calculateRoomData = (
    room: RoomTemplate, 
    pattern: TilingPattern, 
    grout: GroutOption,
    tileDimensions: { width: number; height: number; }
  ): RoomVisualizationData => {
    const roomArea = (room.dimensions.width * room.dimensions.depth) / 10000; // m²
    const tileArea = (tileDimensions.width * tileDimensions.height) / 10000; // m²
    
    let tileCount = 0;
    let coverage = 0;
    let wastePercentage = 10; // Default waste

    // Calculate based on pattern
    switch (pattern.algorithm) {
      case 'grid':
        tileCount = Math.ceil(roomArea / tileArea);
        coverage = (tileCount * tileArea) / roomArea;
        wastePercentage = 5;
        break;
      case 'diagonal':
        tileCount = Math.ceil(roomArea / tileArea * 1.1);
        coverage = (tileCount * tileArea) / roomArea;
        wastePercentage = 10;
        break;
      case 'herringbone':
        tileCount = Math.ceil(roomArea / tileArea * 1.15);
        coverage = (tileCount * tileArea) / roomArea;
        wastePercentage = 15;
        break;
      case 'random':
        tileCount = Math.ceil(roomArea / tileArea * 1.2);
        coverage = (tileCount * tileArea) / roomArea;
        wastePercentage = 20;
        break;
      default:
        tileCount = Math.ceil(roomArea / tileArea);
        coverage = (tileCount * tileArea) / roomArea;
    }

    return {
      room,
      pattern,
      grout,
      coverage: Math.min(coverage, 1),
      tileCount,
      wastePercentage
    };
  };

  // Handle room selection
  const handleRoomSelect = (room: RoomTemplate) => {
    setSelectedRoom(room);
    onConfigChange({ room });
    
    if (room) {
      const data = calculateRoomData(room, selectedPattern, selectedGrout, currentConfig.dimensions);
      setRoomData(data);
      onRoomUpdate(data);
    }
  };

  // Handle pattern selection
  const handlePatternSelect = (pattern: TilingPattern) => {
    setSelectedPattern(pattern);
    onConfigChange({ pattern });
    
    if (selectedRoom) {
      const data = calculateRoomData(selectedRoom, pattern, selectedGrout, currentConfig.dimensions);
      setRoomData(data);
      onRoomUpdate(data);
    }
  };

  // Handle grout selection
  const handleGroutSelect = (grout: GroutOption) => {
    setSelectedGrout(grout);
    onConfigChange({ grout });
    
    if (selectedRoom) {
      const data = calculateRoomData(selectedRoom, selectedPattern, grout, currentConfig.dimensions);
      setRoomData(data);
      onRoomUpdate(data);
    }
  };

  // Generate room visualization
  const generateVisualization = async () => {
    if (!selectedRoom) return;
    
    setIsGenerating(true);
    try {
      // Simulate generation process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const data = calculateRoomData(selectedRoom, selectedPattern, selectedGrout, currentConfig.dimensions);
      setRoomData(data);
      onRoomUpdate(data);
    } catch (error) {
      console.error('Room visualization generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Get room type icon
  const getRoomIcon = (roomType: string) => {
    const icons = {
      'bathroom': '🚿',
      'kitchen': '🍳',
      'living': '🛋️',
      'bedroom': '🛏️',
      'outdoor': '🌳'
    };
    return icons[roomType as keyof typeof icons] || '🏠';
  };

  // Get pattern preview
  const getPatternPreview = (pattern: TilingPattern) => {
    const previews = {
      'grid': '⬜⬜⬜\n⬜⬜⬜\n⬜⬜⬜',
      'checkerboard': '⬜⬛⬜\n⬛⬜⬛\n⬜⬛⬜',
      'herringbone': '▬▬╱\n╱▬▬\n▬╱▬',
      'diagonal': '╱⬜╱\n⬜╱⬜\n╱⬜╱',
      'brick': '▬▬▬\n ▬▬ \n▬▬▬',
      'random': '▬╱⬜\n⬜▬╱\n╱⬜▬'
    };
    return previews[pattern.algorithm] || '⬜⬜⬜';
  };

  useEffect(() => {
    if (selectedRoom) {
      const data = calculateRoomData(selectedRoom, selectedPattern, selectedGrout, currentConfig.dimensions);
      setRoomData(data);
      onRoomUpdate(data);
    }
  }, [currentConfig.dimensions]);

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Home className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">Mekan Simülasyonu</h3>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* View Mode Toggle */}
            <button
              onClick={() => setViewMode(prev => prev === '2d' ? '3d' : '2d')}
              className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                viewMode === '3d' 
                  ? 'bg-green-100 text-green-700' 
                  : 'bg-gray-100 text-gray-700'
              }`}
            >
              {viewMode === '3d' ? '3D' : '2D'}
            </button>
            
            {/* Generate Button */}
            <button
              onClick={generateVisualization}
              disabled={!selectedRoom || isGenerating}
              className="px-3 py-1 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGenerating ? 'Oluşturuluyor...' : 'Görselleştir'}
            </button>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Room Selection */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Mekan Seçimi</h4>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3">
            {configuration.roomTemplates.map((room) => (
              <button
                key={room.id}
                onClick={() => handleRoomSelect(room)}
                className={`p-3 rounded-lg border-2 transition-all text-center ${
                  selectedRoom?.id === room.id
                    ? 'border-green-500 bg-green-50 text-green-900'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <div className="text-2xl mb-1">{getRoomIcon(room.type)}</div>
                <div className="text-sm font-medium">{room.name}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {room.dimensions.width}×{room.dimensions.depth} cm
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Pattern Selection */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Döşeme Deseni</h4>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
            {configuration.tilingPatterns.map((pattern) => (
              <button
                key={pattern.name}
                onClick={() => handlePatternSelect(pattern)}
                className={`p-3 rounded-lg border-2 transition-all ${
                  selectedPattern.name === pattern.name
                    ? 'border-blue-500 bg-blue-50 text-blue-900'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <div className="text-xs font-mono mb-2 leading-tight whitespace-pre">
                  {getPatternPreview(pattern)}
                </div>
                <div className="text-sm font-medium">{pattern.name}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {pattern.description}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Grout Options */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Derz Seçenekleri</h4>
          <div className="grid grid-cols-3 sm:grid-cols-4 gap-3">
            {configuration.groutOptions.map((grout, index) => (
              <button
                key={index}
                onClick={() => handleGroutSelect(grout)}
                className={`p-3 rounded-lg border-2 transition-all text-center ${
                  selectedGrout === grout
                    ? 'border-purple-500 bg-purple-50 text-purple-900'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <div 
                  className="w-full h-4 rounded mb-2 border"
                  style={{ backgroundColor: grout.color }}
                />
                <div className="text-sm font-medium">{grout.width}mm</div>
                <div className="text-xs text-gray-500 capitalize">{grout.material}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Room Data Summary */}
        {roomData && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Hesaplama Özeti</h4>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">{roomData.tileCount}</div>
                <div className="text-xs text-gray-500">Karo Sayısı</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {(roomData.coverage * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-gray-500">Kaplama Oranı</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {roomData.wastePercentage}%
                </div>
                <div className="text-xs text-gray-500">Fire Oranı</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {((roomData.room.dimensions.width * roomData.room.dimensions.depth) / 10000).toFixed(1)}
                </div>
                <div className="text-xs text-gray-500">Alan (m²)</div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {selectedRoom && (
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-2">
              <Save className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                {selectedRoom.name} - {selectedPattern.name}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <button className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors">
                <Download className="w-4 h-4 text-gray-600" />
              </button>
              <button className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors">
                <Share2 className="w-4 h-4 text-gray-600" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
