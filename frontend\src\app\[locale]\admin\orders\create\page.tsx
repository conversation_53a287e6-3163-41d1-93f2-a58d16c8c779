'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft,
  ArrowRight,
  Check,
  User,
  Building2,
  Package,
  DollarSign,
  CreditCard,
  FileText,
  Save,
  X
} from 'lucide-react'
import { CustomerSelectionStep } from '@/components/ui/customer-selection-step'
import { ProducerSelectionStep } from '@/components/ui/producer-selection-step'
import { ProductSelectionStep } from '@/components/ui/product-selection-step'
import { PricingStep } from '@/components/ui/pricing-step'
import { PaymentStep } from '@/components/ui/payment-step'
import { OrderSummaryStep } from '@/components/ui/order-summary-step'

// Step definitions
const STEPS = [
  { id: 1, title: 'Müşteri Seçimi', icon: User, description: '<PERSON><PERSON>şteri seç veya yeni müşteri oluştur' },
  { id: 2, title: 'Üretici Seçimi', icon: Building2, description: '<PERSON><PERSON><PERSON>ş için üretici seç' },
  { id: 3, title: '<PERSON>r<PERSON><PERSON>im<PERSON>', icon: Package, description: 'Ürünler ve ebatları seç' },
  { id: 4, title: 'Fiyat & Koşullar', icon: DollarSign, description: 'Fiyatlandırma ve teslimat koşulları' },
  { id: 5, title: 'Ödeme Ayarları', icon: CreditCard, description: 'Ödeme yöntemi ve vadeleri' },
  { id: 6, title: 'Sipariş Özeti', icon: FileText, description: 'Sipariş detaylarını onayla' }
]

// Form data interface
interface OrderFormData {
  customer: any
  producer: any
  products: any[]
  pricing: any
  payment: any
  notes: string
}

export default function CreateOrderPage() {
  const [currentStep, setCurrentStep] = React.useState(1)
  const [formData, setFormData] = React.useState<OrderFormData>({
    customer: null,
    producer: null,
    products: [],
    pricing: null,
    payment: null,
    notes: ''
  })
  const [isDraft, setIsDraft] = React.useState(false)

  const handleNext = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSaveDraft = () => {
    setIsDraft(true)
    // Save draft logic here
    console.log('Saving draft...', formData)
    alert('Taslak kaydedildi!')
  }

  const handleCancel = () => {
    if (confirm('Sipariş oluşturmayı iptal etmek istediğinizden emin misiniz? Tüm veriler kaybolacak.')) {
      window.location.href = '/admin/orders'
    }
  }

  const isStepCompleted = (stepId: number) => {
    switch (stepId) {
      case 1: return formData.customer !== null
      case 2: return formData.producer !== null
      case 3: return formData.products.length > 0
      case 4: return formData.pricing !== null
      case 5: return formData.payment !== null
      case 6: return true // Summary step
      default: return false
    }
  }

  const canProceedToNext = () => {
    return isStepCompleted(currentStep)
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <CustomerSelectionStep formData={formData} setFormData={setFormData} />
      case 2:
        return <ProducerSelectionStep formData={formData} setFormData={setFormData} />
      case 3:
        return <ProductSelectionStep formData={formData} setFormData={setFormData} />
      case 4:
        return <PricingStep formData={formData} setFormData={setFormData} />
      case 5:
        return <PaymentStep formData={formData} setFormData={setFormData} />
      case 6:
        return <OrderSummaryStep formData={formData} setFormData={setFormData} />
      default:
        return <div>Bilinmeyen adım</div>
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.href = '/admin/orders'}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Sipariş Listesi
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Manuel Sipariş Oluştur</h1>
              <p className="text-gray-600">Yeni sipariş oluşturmak için adımları takip edin</p>
            </div>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-between mb-8">
            {STEPS.map((step, index) => {
              const Icon = step.icon
              const isActive = currentStep === step.id
              const isCompleted = isStepCompleted(step.id)
              const isAccessible = step.id <= currentStep || isCompleted

              return (
                <div key={step.id} className="flex items-center">
                  <div className="flex flex-col items-center">
                    <button
                      onClick={() => isAccessible && setCurrentStep(step.id)}
                      disabled={!isAccessible}
                      className={`
                        w-12 h-12 rounded-full flex items-center justify-center border-2 mb-2 transition-all
                        ${isActive ? 'border-blue-500 bg-blue-500 text-white' : 
                          isCompleted ? 'border-green-500 bg-green-500 text-white' :
                          isAccessible ? 'border-gray-300 bg-white text-gray-600 hover:border-blue-300' :
                          'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed'}
                      `}
                    >
                      {isCompleted ? <Check className="w-5 h-5" /> : <Icon className="w-5 h-5" />}
                    </button>
                    <div className="text-center">
                      <p className={`text-sm font-medium ${isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-600'}`}>
                        {step.title}
                      </p>
                      <p className="text-xs text-gray-500 max-w-24 leading-tight">
                        {step.description}
                      </p>
                    </div>
                  </div>
                  {index < STEPS.length - 1 && (
                    <div className={`w-16 h-0.5 mx-4 ${isCompleted ? 'bg-green-500' : 'bg-gray-300'}`} />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Step Content */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              {React.createElement(STEPS[currentStep - 1].icon, { className: "w-6 h-6" })}
              Adım {currentStep}: {STEPS[currentStep - 1].title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {renderStepContent()}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleCancel}
            >
              <X className="w-4 h-4 mr-2" />
              İptal
            </Button>
            <Button
              variant="outline"
              onClick={handleSaveDraft}
            >
              <Save className="w-4 h-4 mr-2" />
              Taslak Kaydet
            </Button>
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 1}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Önceki
            </Button>
            
            {currentStep === STEPS.length ? (
              <Button
                onClick={() => {
                  console.log('Creating order...', formData)
                  alert('Sipariş oluşturuldu! (Demo)')
                }}
                disabled={!canProceedToNext()}
              >
                <Check className="w-4 h-4 mr-2" />
                Siparişi Oluştur
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                disabled={!canProceedToNext()}
              >
                Sonraki
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            )}
          </div>
        </div>


      </div>
    </div>
  )
}

// All step components are now implemented
