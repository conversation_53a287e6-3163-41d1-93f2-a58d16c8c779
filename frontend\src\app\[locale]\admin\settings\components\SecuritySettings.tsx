'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Lock, Clock, AlertTriangle, CheckCircle } from 'lucide-react';
import { useSettings } from '../context/SettingsContext';

const SecuritySettings = () => {
  const { getSettingValue, updateSetting } = useSettings();

  // Security settings values
  const passwordMinLength = getSettingValue('security', 'passwordMinLength') || 8;
  const passwordRequireUppercase = getSettingValue('security', 'passwordRequireUppercase') || true;
  const passwordRequireLowercase = getSettingValue('security', 'passwordRequireLowercase') || true;
  const passwordRequireNumbers = getSettingValue('security', 'passwordRequireNumbers') || true;
  const passwordRequireSymbols = getSettingValue('security', 'passwordRequireSymbols') || false;
  const sessionTimeout = getSettingValue('security', 'sessionTimeout') || 3600;
  const maxConcurrentSessions = getSettingValue('security', 'maxConcurrentSessions') || 3;
  const require2FA = getSettingValue('security', 'require2FA') || false;
  const loginAttemptLimit = getSettingValue('security', 'loginAttemptLimit') || 5;
  const lockoutDuration = getSettingValue('security', 'lockoutDuration') || 900;

  // Calculate security score
  const calculateSecurityScore = () => {
    let score = 0;
    if (passwordMinLength >= 8) score += 20;
    if (passwordRequireUppercase) score += 15;
    if (passwordRequireLowercase) score += 15;
    if (passwordRequireNumbers) score += 15;
    if (passwordRequireSymbols) score += 15;
    if (require2FA) score += 20;
    return score;
  };

  const securityScore = calculateSecurityScore();
  const getSecurityLevel = (score: number) => {
    if (score >= 80) return { level: 'Yüksek', color: 'green', variant: 'default' as const };
    if (score >= 60) return { level: 'Orta', color: 'yellow', variant: 'secondary' as const };
    return { level: 'Düşük', color: 'red', variant: 'destructive' as const };
  };

  const securityLevel = getSecurityLevel(securityScore);

  return (
    <div className="space-y-6">
      {/* Security Score */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-blue-600" />
              <CardTitle>Güvenlik Skoru</CardTitle>
            </div>
            <Badge variant={securityLevel.variant}>
              {securityLevel.level} ({securityScore}/100)
            </Badge>
          </div>
          <CardDescription>
            Mevcut güvenlik ayarlarınızın genel değerlendirmesi
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-300 ${
                securityScore >= 80 ? 'bg-green-500' : 
                securityScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
              style={{ width: `${securityScore}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Güvenlik skorunuzu artırmak için 2FA'yı etkinleştirin ve güçlü şifre politikaları uygulayın.
          </p>
        </CardContent>
      </Card>

      {/* Password Policy */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Lock className="w-5 h-5 text-blue-600" />
            <CardTitle>Şifre Politikaları</CardTitle>
          </div>
          <CardDescription>
            Kullanıcı şifrelerine yönelik güvenlik kurallarını belirleyin
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="passwordMinLength">Minimum Şifre Uzunluğu</Label>
            <Input
              id="passwordMinLength"
              type="number"
              min="6"
              max="50"
              value={passwordMinLength}
              onChange={(e) => updateSetting('security', 'passwordMinLength', parseInt(e.target.value) || 8)}
            />
            <p className="text-xs text-gray-500">
              Kullanıcıların oluşturabileceği minimum şifre uzunluğu
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="passwordRequireUppercase">Büyük Harf Zorunlu</Label>
                <p className="text-sm text-gray-500">A-Z karakterleri gerekli</p>
              </div>
              <Switch
                id="passwordRequireUppercase"
                checked={passwordRequireUppercase}
                onCheckedChange={(checked) => updateSetting('security', 'passwordRequireUppercase', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="passwordRequireLowercase">Küçük Harf Zorunlu</Label>
                <p className="text-sm text-gray-500">a-z karakterleri gerekli</p>
              </div>
              <Switch
                id="passwordRequireLowercase"
                checked={passwordRequireLowercase}
                onCheckedChange={(checked) => updateSetting('security', 'passwordRequireLowercase', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="passwordRequireNumbers">Rakam Zorunlu</Label>
                <p className="text-sm text-gray-500">0-9 karakterleri gerekli</p>
              </div>
              <Switch
                id="passwordRequireNumbers"
                checked={passwordRequireNumbers}
                onCheckedChange={(checked) => updateSetting('security', 'passwordRequireNumbers', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="passwordRequireSymbols">Özel Karakter Zorunlu</Label>
                <p className="text-sm text-gray-500">!@#$%^&* gibi karakterler</p>
              </div>
              <Switch
                id="passwordRequireSymbols"
                checked={passwordRequireSymbols}
                onCheckedChange={(checked) => updateSetting('security', 'passwordRequireSymbols', checked)}
              />
            </div>
          </div>

          {/* Password Example */}
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Örnek geçerli şifre:</strong> 
              {passwordMinLength >= 8 ? 'MyPass123' : 'Pass123'}
              {passwordRequireSymbols ? '!' : ''}
              {' '}({passwordMinLength + (passwordRequireSymbols ? 1 : 0)} karakter)
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Session Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Clock className="w-5 h-5 text-blue-600" />
            <CardTitle>Oturum Yönetimi</CardTitle>
          </div>
          <CardDescription>
            Kullanıcı oturumları ve zaman aşımı ayarları
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="sessionTimeout">Oturum Zaman Aşımı (Saniye)</Label>
              <Input
                id="sessionTimeout"
                type="number"
                min="300"
                max="86400"
                value={sessionTimeout}
                onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value) || 3600)}
              />
              <p className="text-xs text-gray-500">
                {Math.floor(sessionTimeout / 60)} dakika ({Math.floor(sessionTimeout / 3600)} saat)
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxConcurrentSessions">Maksimum Eş Zamanlı Oturum</Label>
              <Input
                id="maxConcurrentSessions"
                type="number"
                min="1"
                max="10"
                value={maxConcurrentSessions}
                onChange={(e) => updateSetting('security', 'maxConcurrentSessions', parseInt(e.target.value) || 3)}
              />
              <p className="text-xs text-gray-500">
                Bir kullanıcının aynı anda açabileceği maksimum oturum sayısı
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Two-Factor Authentication */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Shield className="w-5 h-5 text-green-600" />
            <CardTitle>İki Faktörlü Kimlik Doğrulama (2FA)</CardTitle>
          </div>
          <CardDescription>
            Ek güvenlik katmanı için 2FA ayarlarını yapılandırın
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="require2FA">2FA Zorunluluğu</Label>
              <p className="text-sm text-gray-500">
                Tüm kullanıcılar için 2FA'yı zorunlu kıl
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="require2FA"
                checked={require2FA}
                onCheckedChange={(checked) => updateSetting('security', 'require2FA', checked)}
              />
              <Badge variant={require2FA ? "default" : "secondary"}>
                {require2FA ? "Zorunlu" : "İsteğe Bağlı"}
              </Badge>
            </div>
          </div>

          {require2FA && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                2FA zorunluluğu aktif. Tüm kullanıcılar giriş yaparken ikinci faktör doğrulaması yapacak.
              </AlertDescription>
            </Alert>
          )}

          {!require2FA && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Güvenlik Uyarısı:</strong> 2FA zorunluluğu devre dışı. 
                Hesap güvenliği için 2FA'yı etkinleştirmeniz önerilir.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Login Security */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-orange-600" />
            <CardTitle>Giriş Güvenliği</CardTitle>
          </div>
          <CardDescription>
            Başarısız giriş denemelerine karşı koruma ayarları
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="loginAttemptLimit">Maksimum Giriş Denemesi</Label>
              <Input
                id="loginAttemptLimit"
                type="number"
                min="3"
                max="20"
                value={loginAttemptLimit}
                onChange={(e) => updateSetting('security', 'loginAttemptLimit', parseInt(e.target.value) || 5)}
              />
              <p className="text-xs text-gray-500">
                Bu sayıda başarısız denemeden sonra hesap kilitlenir
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="lockoutDuration">Kilitleme Süresi (Saniye)</Label>
              <Input
                id="lockoutDuration"
                type="number"
                min="60"
                max="3600"
                value={lockoutDuration}
                onChange={(e) => updateSetting('security', 'lockoutDuration', parseInt(e.target.value) || 900)}
              />
              <p className="text-xs text-gray-500">
                {Math.floor(lockoutDuration / 60)} dakika kilitleme süresi
              </p>
            </div>
          </div>

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Brute Force Koruması:</strong> {loginAttemptLimit} başarısız denemeden sonra 
              hesap {Math.floor(lockoutDuration / 60)} dakika kilitlenecek.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Security Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Güvenlik Özeti</CardTitle>
          <CardDescription>
            Mevcut güvenlik ayarlarının özeti
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800">Şifre Politikası</h4>
              <p className="text-sm text-blue-600 mt-1">
                Min {passwordMinLength} karakter, {[
                  passwordRequireUppercase && 'büyük harf',
                  passwordRequireLowercase && 'küçük harf', 
                  passwordRequireNumbers && 'rakam',
                  passwordRequireSymbols && 'özel karakter'
                ].filter(Boolean).join(', ')}
              </p>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-800">2FA Durumu</h4>
              <p className="text-sm text-green-600 mt-1">
                {require2FA ? 'Tüm kullanıcılar için zorunlu' : 'İsteğe bağlı'}
              </p>
            </div>
            
            <div className="p-4 bg-orange-50 rounded-lg">
              <h4 className="font-medium text-orange-800">Oturum Süresi</h4>
              <p className="text-sm text-orange-600 mt-1">
                {Math.floor(sessionTimeout / 3600)} saat zaman aşımı
              </p>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-medium text-purple-800">Giriş Koruması</h4>
              <p className="text-sm text-purple-600 mt-1">
                {loginAttemptLimit} deneme, {Math.floor(lockoutDuration / 60)} dk kilitleme
              </p>
            </div>
            
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-800">Eş Zamanlı Oturum</h4>
              <p className="text-sm text-gray-600 mt-1">
                Maksimum {maxConcurrentSessions} oturum
              </p>
            </div>
            
            <div className={`p-4 rounded-lg ${
              securityScore >= 80 ? 'bg-green-50' : 
              securityScore >= 60 ? 'bg-yellow-50' : 'bg-red-50'
            }`}>
              <h4 className={`font-medium ${
                securityScore >= 80 ? 'text-green-800' : 
                securityScore >= 60 ? 'text-yellow-800' : 'text-red-800'
              }`}>
                Genel Güvenlik
              </h4>
              <p className={`text-sm mt-1 ${
                securityScore >= 80 ? 'text-green-600' : 
                securityScore >= 60 ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {securityLevel.level} seviye ({securityScore}/100)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SecuritySettings;
