'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Globe, Palette, Settings, AlertTriangle } from 'lucide-react';
import { useSettings } from '../context/SettingsContext';

const PlatformSettings = () => {
  const { getSettingValue, updateSetting } = useSettings();

  // Platform settings values
  const siteName = getSettingValue('platform', 'siteName') || '';
  const siteDescription = getSettingValue('platform', 'siteDescription') || '';
  const logoUrl = getSettingValue('platform', 'logoUrl') || '';
  const faviconUrl = getSettingValue('platform', 'faviconUrl') || '';
  const maintenanceMode = getSettingValue('platform', 'maintenanceMode') || false;
  const maintenanceMessage = getSettingValue('platform', 'maintenanceMessage') || '';
  const defaultLanguage = getSettingValue('platform', 'defaultLanguage') || 'tr';
  const timezone = getSettingValue('platform', 'timezone') || 'Europe/Istanbul';

  const languages = [
    { value: 'tr', label: 'Türkçe' },
    { value: 'en', label: 'English' },
    { value: 'ar', label: 'العربية' },
    { value: 'de', label: 'Deutsch' },
    { value: 'fr', label: 'Français' },
    { value: 'es', label: 'Español' },
    { value: 'it', label: 'Italiano' },
    { value: 'ru', label: 'Русский' },
    { value: 'zh', label: '中文' },
    { value: 'ja', label: '日本語' }
  ];

  const timezones = [
    { value: 'Europe/Istanbul', label: 'İstanbul (UTC+3)' },
    { value: 'Europe/London', label: 'Londra (UTC+0)' },
    { value: 'Europe/Berlin', label: 'Berlin (UTC+1)' },
    { value: 'America/New_York', label: 'New York (UTC-5)' },
    { value: 'Asia/Dubai', label: 'Dubai (UTC+4)' },
    { value: 'Asia/Shanghai', label: 'Şangay (UTC+8)' }
  ];

  return (
    <div className="space-y-6">
      {/* Maintenance Mode Alert */}
      {maintenanceMode && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Bakım Modu Aktif:</strong> Site şu anda bakım modunda. Ziyaretçiler bakım mesajını görecek.
          </AlertDescription>
        </Alert>
      )}

      {/* Site Information */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Settings className="w-5 h-5 text-blue-600" />
            <CardTitle>Site Bilgileri</CardTitle>
          </div>
          <CardDescription>
            Temel site bilgilerini ve görünüm ayarlarını yapılandırın
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="siteName">Site Adı</Label>
              <Input
                id="siteName"
                value={siteName}
                onChange={(e) => updateSetting('platform', 'siteName', e.target.value)}
                placeholder="Doğal Taş Pazaryeri"
              />
              <p className="text-xs text-gray-500">
                Bu ad tarayıcı başlığında ve site genelinde görünecek
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="logoUrl">Logo URL</Label>
              <Input
                id="logoUrl"
                value={logoUrl}
                onChange={(e) => updateSetting('platform', 'logoUrl', e.target.value)}
                placeholder="https://example.com/logo.png"
              />
              <p className="text-xs text-gray-500">
                Site logosunun URL adresi
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="siteDescription">Site Açıklaması</Label>
            <Textarea
              id="siteDescription"
              value={siteDescription}
              onChange={(e) => updateSetting('platform', 'siteDescription', e.target.value)}
              placeholder="Türkiye'nin en büyük doğal taş pazaryeri..."
              rows={3}
            />
            <p className="text-xs text-gray-500">
              SEO ve sosyal medya paylaşımları için kullanılır
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="faviconUrl">Favicon URL</Label>
            <Input
              id="faviconUrl"
              value={faviconUrl}
              onChange={(e) => updateSetting('platform', 'faviconUrl', e.target.value)}
              placeholder="https://example.com/favicon.ico"
            />
            <p className="text-xs text-gray-500">
              Tarayıcı sekmesinde görünecek küçük ikon
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Localization Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Globe className="w-5 h-5 text-blue-600" />
            <CardTitle>Yerelleştirme</CardTitle>
          </div>
          <CardDescription>
            Dil, zaman dilimi ve bölgesel ayarları yapılandırın
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="defaultLanguage">Varsayılan Dil</Label>
              <Select
                value={defaultLanguage}
                onValueChange={(value) => updateSetting('platform', 'defaultLanguage', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Dil seçin" />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      {lang.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                Yeni kullanıcılar için varsayılan dil
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="timezone">Zaman Dilimi</Label>
              <Select
                value={timezone}
                onValueChange={(value) => updateSetting('platform', 'timezone', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Zaman dilimi seçin" />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz.value} value={tz.value}>
                      {tz.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                Sistem genelinde kullanılacak zaman dilimi
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Maintenance Mode */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-orange-600" />
            <CardTitle>Bakım Modu</CardTitle>
          </div>
          <CardDescription>
            Site bakım modunu etkinleştirin ve bakım mesajını özelleştirin
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="maintenanceMode">Bakım Modu</Label>
              <p className="text-sm text-gray-500">
                Etkinleştirildiğinde site ziyaretçilere kapatılır
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="maintenanceMode"
                checked={maintenanceMode}
                onCheckedChange={(checked) => updateSetting('platform', 'maintenanceMode', checked)}
              />
              <Badge variant={maintenanceMode ? "destructive" : "secondary"}>
                {maintenanceMode ? "Aktif" : "Pasif"}
              </Badge>
            </div>
          </div>

          {maintenanceMode && (
            <div className="space-y-2">
              <Label htmlFor="maintenanceMessage">Bakım Mesajı</Label>
              <Textarea
                id="maintenanceMessage"
                value={maintenanceMessage}
                onChange={(e) => updateSetting('platform', 'maintenanceMessage', e.target.value)}
                placeholder="Site bakımda. Lütfen daha sonra tekrar deneyin."
                rows={3}
              />
              <p className="text-xs text-gray-500">
                Bakım modu sırasında ziyaretçilere gösterilecek mesaj
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Theme Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Palette className="w-5 h-5 text-blue-600" />
            <CardTitle>Tema Ayarları</CardTitle>
          </div>
          <CardDescription>
            Site görünümü ve tema ayarlarını yapılandırın
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Tema ayarları geliştirilme aşamasında. Yakında kullanıma sunulacak.
            </AlertDescription>
          </Alert>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 border rounded-lg text-center">
              <div className="w-full h-16 bg-blue-500 rounded mb-2"></div>
              <p className="text-sm font-medium">Mavi Tema</p>
              <Badge variant="outline" className="mt-1">Aktif</Badge>
            </div>
            <div className="p-4 border rounded-lg text-center opacity-50">
              <div className="w-full h-16 bg-green-500 rounded mb-2"></div>
              <p className="text-sm font-medium">Yeşil Tema</p>
              <Badge variant="secondary" className="mt-1">Yakında</Badge>
            </div>
            <div className="p-4 border rounded-lg text-center opacity-50">
              <div className="w-full h-16 bg-purple-500 rounded mb-2"></div>
              <p className="text-sm font-medium">Mor Tema</p>
              <Badge variant="secondary" className="mt-1">Yakında</Badge>
            </div>
            <div className="p-4 border rounded-lg text-center opacity-50">
              <div className="w-full h-16 bg-gray-800 rounded mb-2"></div>
              <p className="text-sm font-medium">Koyu Tema</p>
              <Badge variant="secondary" className="mt-1">Yakında</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PlatformSettings;
