import * as React from "react"
import { cn } from "@/lib/utils"

export interface GridProps extends React.HTMLAttributes<HTMLDivElement> {
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 12
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  responsive?: {
    sm?: 1 | 2 | 3 | 4 | 5 | 6 | 12
    md?: 1 | 2 | 3 | 4 | 5 | 6 | 12
    lg?: 1 | 2 | 3 | 4 | 5 | 6 | 12
    xl?: 1 | 2 | 3 | 4 | 5 | 6 | 12
  }
}

/**
 * Grid component following RFC-004 UI/UX Design System
 * Responsive CSS Grid with consistent spacing
 */
const Grid = React.forwardRef<HTMLDivElement, GridProps>(
  ({ className, cols = 1, gap = 'md', responsive, ...props }, ref) => {
    
    const getColsClass = (columns: number) => {
      const colsMap = {
        1: "grid-cols-1",
        2: "grid-cols-2", 
        3: "grid-cols-3",
        4: "grid-cols-4",
        5: "grid-cols-5",
        6: "grid-cols-6",
        12: "grid-cols-12"
      }
      return colsMap[columns as keyof typeof colsMap] || "grid-cols-1"
    }
    
    const getGapClass = () => {
      switch (gap) {
        case 'none':
          return "gap-0"
        case 'sm':
          return "gap-2"  // 8px
        case 'md':
          return "gap-4"  // 16px
        case 'lg':
          return "gap-6"  // 24px
        case 'xl':
          return "gap-8"  // 32px
        default:
          return "gap-4"
      }
    }
    
    const getResponsiveClasses = () => {
      if (!responsive) return []
      
      const classes = []
      
      if (responsive.sm) {
        classes.push(`sm:${getColsClass(responsive.sm)}`)
      }
      if (responsive.md) {
        classes.push(`md:${getColsClass(responsive.md)}`)
      }
      if (responsive.lg) {
        classes.push(`lg:${getColsClass(responsive.lg)}`)
      }
      if (responsive.xl) {
        classes.push(`xl:${getColsClass(responsive.xl)}`)
      }
      
      return classes
    }

    return (
      <div
        ref={ref}
        className={cn(
          "grid",
          getColsClass(cols),
          getGapClass(),
          ...getResponsiveClasses(),
          className
        )}
        {...props}
      />
    )
  }
)

Grid.displayName = "Grid"

export interface GridItemProps extends React.HTMLAttributes<HTMLDivElement> {
  span?: 1 | 2 | 3 | 4 | 5 | 6 | 12
  start?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12
  end?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13
  responsive?: {
    sm?: { span?: number; start?: number; end?: number }
    md?: { span?: number; start?: number; end?: number }
    lg?: { span?: number; start?: number; end?: number }
    xl?: { span?: number; start?: number; end?: number }
  }
}

/**
 * GridItem component for individual grid items
 */
const GridItem = React.forwardRef<HTMLDivElement, GridItemProps>(
  ({ className, span, start, end, responsive, ...props }, ref) => {
    
    const getSpanClass = (spanValue: number) => {
      const spanMap = {
        1: "col-span-1",
        2: "col-span-2",
        3: "col-span-3",
        4: "col-span-4",
        5: "col-span-5",
        6: "col-span-6",
        12: "col-span-12"
      }
      return spanMap[spanValue as keyof typeof spanMap]
    }
    
    const getStartClass = (startValue: number) => {
      const startMap = {
        1: "col-start-1",
        2: "col-start-2",
        3: "col-start-3",
        4: "col-start-4",
        5: "col-start-5",
        6: "col-start-6",
        7: "col-start-7",
        8: "col-start-8",
        9: "col-start-9",
        10: "col-start-10",
        11: "col-start-11",
        12: "col-start-12"
      }
      return startMap[startValue as keyof typeof startMap]
    }
    
    const getEndClass = (endValue: number) => {
      const endMap = {
        1: "col-end-1",
        2: "col-end-2",
        3: "col-end-3",
        4: "col-end-4",
        5: "col-end-5",
        6: "col-end-6",
        7: "col-end-7",
        8: "col-end-8",
        9: "col-end-9",
        10: "col-end-10",
        11: "col-end-11",
        12: "col-end-12",
        13: "col-end-13"
      }
      return endMap[endValue as keyof typeof endMap]
    }
    
    const getResponsiveClasses = () => {
      if (!responsive) return []
      
      const classes = []
      
      Object.entries(responsive).forEach(([breakpoint, config]) => {
        if (config.span) {
          classes.push(`${breakpoint}:${getSpanClass(config.span)}`)
        }
        if (config.start) {
          classes.push(`${breakpoint}:${getStartClass(config.start)}`)
        }
        if (config.end) {
          classes.push(`${breakpoint}:${getEndClass(config.end)}`)
        }
      })
      
      return classes
    }

    return (
      <div
        ref={ref}
        className={cn(
          span && getSpanClass(span),
          start && getStartClass(start),
          end && getEndClass(end),
          ...getResponsiveClasses(),
          className
        )}
        {...props}
      />
    )
  }
)

GridItem.displayName = "GridItem"

export { Grid, GridItem }
