// RFC-013: Approval System
// Admin onay sistemi

import { EventEmitter } from 'events';
import { MarketingTask, TaskResult } from '../types/ai-marketing.types';

interface ApprovalRequest {
  id: string;
  taskId: string;
  type: string;
  content: any;
  requestedAt: Date;
  status: 'pending' | 'approved' | 'rejected';
  reviewedBy?: string;
  reviewedAt?: Date;
  comments?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export class ApprovalSystem extends EventEmitter {
  private approvalQueue: Map<string, ApprovalRequest> = new Map();

  constructor() {
    super();
  }

  public async requestApproval(task: MarketingTask, result: TaskResult): Promise<void> {
    const approvalRequest: ApprovalRequest = {
      id: `approval-${Date.now()}`,
      taskId: task.id,
      type: task.type,
      content: result.data,
      requestedAt: new Date(),
      status: 'pending',
      priority: task.priority
    };

    this.approvalQueue.set(approvalRequest.id, approvalRequest);
    
    console.log(`Approval requested for task: ${task.id}`);
    this.emit('approvalRequested', approvalRequest);
  }

  public async approveRequest(requestId: string, reviewedBy: string, comments?: string): Promise<void> {
    const request = this.approvalQueue.get(requestId);
    if (!request) {
      throw new Error(`Approval request not found: ${requestId}`);
    }

    request.status = 'approved';
    request.reviewedBy = reviewedBy;
    request.reviewedAt = new Date();
    request.comments = comments;

    console.log(`Approval request approved: ${requestId}`);
    this.emit('approvalApproved', request);
  }

  public async rejectRequest(requestId: string, reviewedBy: string, comments: string): Promise<void> {
    const request = this.approvalQueue.get(requestId);
    if (!request) {
      throw new Error(`Approval request not found: ${requestId}`);
    }

    request.status = 'rejected';
    request.reviewedBy = reviewedBy;
    request.reviewedAt = new Date();
    request.comments = comments;

    console.log(`Approval request rejected: ${requestId}`);
    this.emit('approvalRejected', request);
  }

  public getPendingApprovals(): ApprovalRequest[] {
    return Array.from(this.approvalQueue.values())
      .filter(request => request.status === 'pending')
      .sort((a, b) => {
        // Sort by priority and date
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        const aPriority = priorityOrder[a.priority];
        const bPriority = priorityOrder[b.priority];
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority;
        }
        
        return a.requestedAt.getTime() - b.requestedAt.getTime();
      });
  }

  public getApprovalStats(): any {
    const requests = Array.from(this.approvalQueue.values());
    
    return {
      total: requests.length,
      pending: requests.filter(r => r.status === 'pending').length,
      approved: requests.filter(r => r.status === 'approved').length,
      rejected: requests.filter(r => r.status === 'rejected').length
    };
  }
}
