'use client';

import React, { useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

export type RoomType = 'living' | 'kitchen' | 'bathroom' | 'bedroom' | 'outdoor';

interface RoomTemplateProps {
  roomType: RoomType;
  visible?: boolean;
  opacity?: number;
  showEnvironment?: boolean;
}

interface RoomDimensions {
  width: number;
  depth: number;
  height: number;
}

interface RoomFurniture {
  type: string;
  position: [number, number, number];
  rotation: [number, number, number];
  scale: [number, number, number];
  color: string;
}

// Room configurations with more detailed furniture
const ROOM_CONFIGS: Record<RoomType, { dimensions: RoomDimensions; furniture: RoomFurniture[] }> = {
  living: {
    dimensions: { width: 10, depth: 8, height: 3 },
    furniture: [
      {
        type: 'sofa',
        position: [-3, 0.4, -2.5],
        rotation: [0, 0, 0],
        scale: [3, 0.8, 1.2],
        color: '#8b7355'
      },
      {
        type: 'coffee-table',
        position: [-1, 0.2, -1.5],
        rotation: [0, 0, 0],
        scale: [1.5, 0.4, 1],
        color: '#654321'
      },
      {
        type: 'tv-stand',
        position: [3.5, 0.3, -3],
        rotation: [0, Math.PI, 0],
        scale: [1.5, 0.6, 0.4],
        color: '#2d3748'
      }
    ]
  },
  kitchen: {
    dimensions: { width: 6, depth: 4, height: 3 },
    furniture: [
      {
        type: 'counter',
        position: [-2, 0.45, -1.5],
        rotation: [0, 0, 0],
        scale: [3, 0.9, 0.6],
        color: '#e2e8f0'
      },
      {
        type: 'island',
        position: [0.5, 0.45, 0],
        rotation: [0, 0, 0],
        scale: [1.5, 0.9, 1],
        color: '#f7fafc'
      },
      {
        type: 'cabinet',
        position: [-2, 1.2, -1.5],
        rotation: [0, 0, 0],
        scale: [3, 1.2, 0.4],
        color: '#8b7355'
      }
    ]
  },
  bathroom: {
    dimensions: { width: 3, depth: 3, height: 3 },
    furniture: [
      {
        type: 'bathtub',
        position: [-1, 0.3, -1],
        rotation: [0, 0, 0],
        scale: [1.8, 0.6, 0.8],
        color: '#ffffff'
      },
      {
        type: 'sink',
        position: [1, 0.4, -1],
        rotation: [0, 0, 0],
        scale: [0.6, 0.8, 0.4],
        color: '#f7fafc'
      },
      {
        type: 'toilet',
        position: [1, 0.2, 0.5],
        rotation: [0, -Math.PI/2, 0],
        scale: [0.4, 0.4, 0.6],
        color: '#ffffff'
      }
    ]
  },
  bedroom: {
    dimensions: { width: 6, depth: 5, height: 3 },
    furniture: [
      {
        type: 'bed',
        position: [0, 0.3, -1.5],
        rotation: [0, 0, 0],
        scale: [2, 0.6, 1.5],
        color: '#f7fafc'
      },
      {
        type: 'nightstand',
        position: [-1.5, 0.3, -1.5],
        rotation: [0, 0, 0],
        scale: [0.5, 0.6, 0.5],
        color: '#8b7355'
      },
      {
        type: 'wardrobe',
        position: [-2.5, 1, 1],
        rotation: [0, Math.PI/2, 0],
        scale: [1, 2, 0.6],
        color: '#654321'
      }
    ]
  },
  outdoor: {
    dimensions: { width: 10, depth: 8, height: 0 },
    furniture: [
      {
        type: 'table',
        position: [0, 0.4, 0],
        rotation: [0, 0, 0],
        scale: [1.5, 0.8, 1.5],
        color: '#8b7355'
      },
      {
        type: 'chair',
        position: [-1, 0.4, -1],
        rotation: [0, Math.PI/4, 0],
        scale: [0.5, 0.8, 0.5],
        color: '#654321'
      },
      {
        type: 'chair',
        position: [1, 0.4, 1],
        rotation: [0, -Math.PI/4, 0],
        scale: [0.5, 0.8, 0.5],
        color: '#654321'
      }
    ]
  }
};

// Furniture Component
const Furniture: React.FC<{ furniture: RoomFurniture }> = ({ furniture }) => {
  const geometry = useMemo(() => {
    switch (furniture.type) {
      case 'sofa':
        return new THREE.BoxGeometry(1, 1, 1);
      case 'coffee-table':
      case 'table':
        return new THREE.BoxGeometry(1, 1, 1);
      case 'tv-stand':
      case 'counter':
      case 'island':
        return new THREE.BoxGeometry(1, 1, 1);
      case 'cabinet':
      case 'wardrobe':
        return new THREE.BoxGeometry(1, 1, 1);
      case 'bathtub':
        return new THREE.BoxGeometry(1, 1, 1);
      case 'sink':
      case 'toilet':
        return new THREE.BoxGeometry(1, 1, 1);
      case 'bed':
        return new THREE.BoxGeometry(1, 1, 1);
      case 'nightstand':
      case 'chair':
        return new THREE.BoxGeometry(1, 1, 1);
      default:
        return new THREE.BoxGeometry(1, 1, 1);
    }
  }, [furniture.type]);

  const material = useMemo(() => {
    return new THREE.MeshStandardMaterial({
      color: furniture.color,
      roughness: 0.7,
      metalness: 0.1,
      transparent: true,
      opacity: 0.8
    });
  }, [furniture.color]);

  return (
    <mesh
      position={furniture.position}
      rotation={furniture.rotation}
      scale={furniture.scale}
      geometry={geometry}
      material={material}
      castShadow
      receiveShadow
    />
  );
};

// Room Walls Component
const RoomWalls: React.FC<{ dimensions: RoomDimensions; roomType: RoomType }> = ({ 
  dimensions, 
  roomType 
}) => {
  const wallMaterial = useMemo(() => {
    return new THREE.MeshStandardMaterial({
      color: roomType === 'outdoor' ? '#87ceeb' : '#f8f9fa',
      roughness: 0.9,
      metalness: 0.0,
      transparent: true,
      opacity: roomType === 'outdoor' ? 0.3 : 0.7,
      side: THREE.DoubleSide
    });
  }, [roomType]);

  if (roomType === 'outdoor') {
    // For outdoor, just show a sky dome
    return (
      <mesh>
        <sphereGeometry args={[20, 32, 16]} />
        <meshBasicMaterial 
          color="#87ceeb" 
          transparent 
          opacity={0.3} 
          side={THREE.BackSide} 
        />
      </mesh>
    );
  }

  return (
    <group>
      {/* Back Wall */}
      <mesh 
        position={[0, dimensions.height / 2, -dimensions.depth / 2]}
        material={wallMaterial}
        receiveShadow
      >
        <planeGeometry args={[dimensions.width, dimensions.height]} />
      </mesh>

      {/* Left Wall */}
      <mesh 
        position={[-dimensions.width / 2, dimensions.height / 2, 0]}
        rotation={[0, Math.PI / 2, 0]}
        material={wallMaterial}
        receiveShadow
      >
        <planeGeometry args={[dimensions.depth, dimensions.height]} />
      </mesh>

      {/* Right Wall */}
      <mesh 
        position={[dimensions.width / 2, dimensions.height / 2, 0]}
        rotation={[0, -Math.PI / 2, 0]}
        material={wallMaterial}
        receiveShadow
      >
        <planeGeometry args={[dimensions.depth, dimensions.height]} />
      </mesh>

      {/* Ceiling */}
      <mesh 
        position={[0, dimensions.height, 0]}
        rotation={[-Math.PI / 2, 0, 0]}
        material={wallMaterial}
        receiveShadow
      >
        <planeGeometry args={[dimensions.width, dimensions.depth]} />
      </mesh>
    </group>
  );
};

// Main RoomTemplate Component
export const RoomTemplate: React.FC<RoomTemplateProps> = ({
  roomType,
  visible = true,
  opacity = 1,
  showEnvironment = true
}) => {
  const config = ROOM_CONFIGS[roomType];

  if (!visible) return null;

  return (
    <group>
      {/* Room Walls */}
      <RoomWalls dimensions={config.dimensions} roomType={roomType} />

      {/* Furniture */}
      {config.furniture.map((furniture, index) => (
        <Furniture key={`${roomType}-furniture-${index}`} furniture={furniture} />
      ))}

      {/* Floor Base with realistic texture */}
      <mesh position={[0, -0.01, 0]} rotation={[-Math.PI / 2, 0, 0]} receiveShadow>
        <planeGeometry args={[config.dimensions.width, config.dimensions.depth]} />
        <meshStandardMaterial
          color="#f5f5f5"
          roughness={0.9}
          metalness={0.0}
        />
      </mesh>

      {/* Room Walls */}
      {showEnvironment && (
        <>
          {/* Back Wall */}
          <mesh position={[0, config.dimensions.height / 2, -config.dimensions.depth / 2]} receiveShadow>
            <planeGeometry args={[config.dimensions.width, config.dimensions.height]} />
            <meshStandardMaterial
              color="#fafafa"
              roughness={0.8}
              metalness={0.0}
            />
          </mesh>

          {/* Left Wall */}
          <mesh
            position={[-config.dimensions.width / 2, config.dimensions.height / 2, 0]}
            rotation={[0, Math.PI / 2, 0]}
            receiveShadow
          >
            <planeGeometry args={[config.dimensions.depth, config.dimensions.height]} />
            <meshStandardMaterial
              color="#fafafa"
              roughness={0.8}
              metalness={0.0}
            />
          </mesh>

          {/* Right Wall */}
          <mesh
            position={[config.dimensions.width / 2, config.dimensions.height / 2, 0]}
            rotation={[0, -Math.PI / 2, 0]}
            receiveShadow
          >
            <planeGeometry args={[config.dimensions.depth, config.dimensions.height]} />
            <meshStandardMaterial
              color="#fafafa"
              roughness={0.8}
              metalness={0.0}
            />
          </mesh>
        </>
      )}

      {/* Room Boundary Indicator */}
      <mesh position={[0, 0.01, 0]} rotation={[-Math.PI / 2, 0, 0]} receiveShadow>
        <ringGeometry args={[
          Math.max(config.dimensions.width, config.dimensions.depth) / 2 - 0.1,
          Math.max(config.dimensions.width, config.dimensions.depth) / 2,
          32
        ]} />
        <meshBasicMaterial
          color="#94a3b8"
          transparent
          opacity={0.3 * opacity}
        />
      </mesh>
    </group>
  );
};

export default RoomTemplate;
