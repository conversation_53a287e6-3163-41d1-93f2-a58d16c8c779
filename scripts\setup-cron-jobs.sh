#!/bin/bash

# Cron Jobs Setup Script
# Türkiye Doğal Taş Pazaryeri - Automated Tasks Setup

set -e

# Configuration
PROJECT_DIR=$(pwd)
USER=$(whoami)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "Setting up cron jobs for Natural Stone Marketplace"
log_info "Project directory: $PROJECT_DIR"
log_info "User: $USER"

# Function to check if cron is available
check_cron() {
    if ! command -v crontab &> /dev/null; then
        log_error "Crontab is not available on this system"
        return 1
    fi
    
    if ! systemctl is-active --quiet cron 2>/dev/null && ! systemctl is-active --quiet crond 2>/dev/null; then
        log_warning "Cron service may not be running"
    fi
    
    log_success "Cron is available"
}

# Function to backup existing crontab
backup_crontab() {
    log_info "Backing up existing crontab..."
    
    local backup_file="$PROJECT_DIR/logs/crontab-backup-$(date +%Y%m%d-%H%M%S).txt"
    mkdir -p "$(dirname "$backup_file")"
    
    if crontab -l > "$backup_file" 2>/dev/null; then
        log_success "Crontab backed up to: $backup_file"
    else
        log_info "No existing crontab found"
        touch "$backup_file"
    fi
}

# Function to create cron jobs
setup_cron_jobs() {
    log_info "Setting up cron jobs..."
    
    # Create temporary crontab file
    local temp_crontab="/tmp/natural-stone-crontab"
    
    # Get existing crontab (if any)
    crontab -l > "$temp_crontab" 2>/dev/null || echo "" > "$temp_crontab"
    
    # Remove existing Natural Stone Marketplace jobs
    grep -v "# Natural Stone Marketplace" "$temp_crontab" > "${temp_crontab}.clean" || touch "${temp_crontab}.clean"
    mv "${temp_crontab}.clean" "$temp_crontab"
    
    # Add new cron jobs
    cat >> "$temp_crontab" << EOF

# Natural Stone Marketplace - Automated Tasks
# Generated on $(date)

# Daily database backup at 2:00 AM
0 2 * * * cd $PROJECT_DIR/backend && node scripts/backup-database.js >> $PROJECT_DIR/logs/backup.log 2>&1 # Natural Stone Marketplace

# Backup monitoring every 6 hours
0 */6 * * * cd $PROJECT_DIR && bash scripts/backup-monitor.sh >> $PROJECT_DIR/logs/backup-monitor.log 2>&1 # Natural Stone Marketplace

# Clean old logs weekly (Sunday at 3:00 AM)
0 3 * * 0 find $PROJECT_DIR/logs -name "*.log" -mtime +30 -delete # Natural Stone Marketplace

# Database maintenance weekly (Sunday at 4:00 AM)
0 4 * * 0 cd $PROJECT_DIR/backend && npx prisma db execute --file scripts/maintenance.sql >> $PROJECT_DIR/logs/db-maintenance.log 2>&1 # Natural Stone Marketplace

# Performance monitoring every hour
0 * * * * curl -f http://localhost:8000/health > /dev/null 2>&1 || echo "\$(date): Health check failed" >> $PROJECT_DIR/logs/health-check.log # Natural Stone Marketplace

# SSL certificate renewal check daily at 1:00 AM
0 1 * * * certbot renew --quiet && systemctl reload nginx # Natural Stone Marketplace

# Clean old backup files monthly (1st day at 5:00 AM)
0 5 1 * * find $PROJECT_DIR/backups -name "*.sql.gz" -mtime +30 -delete # Natural Stone Marketplace

# System resource monitoring every 15 minutes
*/15 * * * * df -h | grep -E "/(|home|var)$" | awk '{if(\$5+0 > 85) print "Disk usage warning: " \$0}' >> $PROJECT_DIR/logs/disk-usage.log # Natural Stone Marketplace

# Application log rotation daily at midnight
0 0 * * * cd $PROJECT_DIR && find . -name "*.log" -size +100M -exec gzip {} \; # Natural Stone Marketplace

# Weekly security scan (Saturday at 2:00 AM)
0 2 * * 6 cd $PROJECT_DIR && bash scripts/security-scan.sh >> $PROJECT_DIR/logs/security-scan.log 2>&1 # Natural Stone Marketplace

EOF
    
    # Install new crontab
    if crontab "$temp_crontab"; then
        log_success "Cron jobs installed successfully"
    else
        log_error "Failed to install cron jobs"
        return 1
    fi
    
    # Clean up
    rm -f "$temp_crontab"
}

# Function to create log directories
create_log_directories() {
    log_info "Creating log directories..."
    
    local directories=(
        "$PROJECT_DIR/logs"
        "$PROJECT_DIR/backups"
        "$PROJECT_DIR/logs/archive"
    )
    
    for dir in "${directories[@]}"; do
        if mkdir -p "$dir"; then
            log_success "Created directory: $dir"
        else
            log_error "Failed to create directory: $dir"
            return 1
        fi
    done
}

# Function to create maintenance scripts
create_maintenance_scripts() {
    log_info "Creating maintenance scripts..."
    
    # Database maintenance script
    cat > "$PROJECT_DIR/backend/scripts/maintenance.sql" << 'EOF'
-- Database Maintenance Script
-- Türkiye Doğal Taş Pazaryeri

-- Clean old audit logs (older than 1 year)
DELETE FROM audit_logs WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 year';

-- Clean old performance logs (older than 3 months)
DELETE FROM performance_logs WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '3 months';

-- Clean old error logs (older than 6 months)
DELETE FROM error_logs WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '6 months';

-- Clean expired sessions
DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP;

-- Clean old rate limit records
DELETE FROM rate_limits WHERE window_start < CURRENT_TIMESTAMP - INTERVAL '1 day';

-- Clean sent notifications (older than 1 month)
DELETE FROM notification_queue WHERE status = 'SENT' AND sent_at < CURRENT_TIMESTAMP - INTERVAL '1 month';

-- Clean failed notifications (older than 1 week)
DELETE FROM notification_queue WHERE status = 'FAILED' AND created_at < CURRENT_TIMESTAMP - INTERVAL '1 week';

-- Update statistics
ANALYZE;

-- Vacuum tables
VACUUM;
EOF
    
    # Security scan script
    cat > "$PROJECT_DIR/scripts/security-scan.sh" << 'EOF'
#!/bin/bash

# Security Scan Script
# Basic security checks for Natural Stone Marketplace

LOG_FILE="./logs/security-scan-$(date +%Y%m%d).log"

echo "$(date): Starting security scan" >> "$LOG_FILE"

# Check for failed login attempts
echo "Checking failed login attempts..." >> "$LOG_FILE"
grep "authentication failed" ./logs/*.log | tail -10 >> "$LOG_FILE" 2>/dev/null || echo "No failed login attempts found" >> "$LOG_FILE"

# Check for suspicious IP addresses
echo "Checking for suspicious activities..." >> "$LOG_FILE"
grep -E "(DROP|REJECT|DENY)" /var/log/syslog | tail -5 >> "$LOG_FILE" 2>/dev/null || echo "No suspicious activities found" >> "$LOG_FILE"

# Check SSL certificate expiry
echo "Checking SSL certificate expiry..." >> "$LOG_FILE"
if command -v openssl &> /dev/null; then
    echo | openssl s_client -servername yourdomain.com -connect yourdomain.com:443 2>/dev/null | openssl x509 -noout -dates >> "$LOG_FILE" 2>/dev/null || echo "SSL check failed" >> "$LOG_FILE"
fi

# Check for outdated packages
echo "Checking for security updates..." >> "$LOG_FILE"
if command -v apt &> /dev/null; then
    apt list --upgradable 2>/dev/null | grep -i security | wc -l >> "$LOG_FILE"
elif command -v yum &> /dev/null; then
    yum check-update --security 2>/dev/null | wc -l >> "$LOG_FILE"
fi

echo "$(date): Security scan completed" >> "$LOG_FILE"
EOF
    
    chmod +x "$PROJECT_DIR/scripts/security-scan.sh"
    
    log_success "Maintenance scripts created"
}

# Function to test cron jobs
test_cron_jobs() {
    log_info "Testing cron job setup..."
    
    # List current cron jobs
    log_info "Current cron jobs:"
    crontab -l | grep "Natural Stone Marketplace" || log_warning "No Natural Stone Marketplace cron jobs found"
    
    # Test log directory permissions
    if [[ -w "$PROJECT_DIR/logs" ]]; then
        log_success "Log directory is writable"
    else
        log_error "Log directory is not writable"
        return 1
    fi
    
    # Test backup directory permissions
    if [[ -w "$PROJECT_DIR/backups" ]]; then
        log_success "Backup directory is writable"
    else
        log_error "Backup directory is not writable"
        return 1
    fi
    
    log_success "Cron job setup test completed"
}

# Function to show cron job status
show_status() {
    log_info "Cron Job Status:"
    echo "=================="
    
    echo "Active cron jobs for Natural Stone Marketplace:"
    crontab -l | grep "Natural Stone Marketplace" | while read -r job; do
        echo "  $job"
    done
    
    echo ""
    echo "Log files:"
    find "$PROJECT_DIR/logs" -name "*.log" -mtime -1 | while read -r logfile; do
        echo "  $(basename "$logfile") ($(stat -f%z "$logfile" 2>/dev/null || stat -c%s "$logfile") bytes)"
    done
    
    echo ""
    echo "Recent backups:"
    find "$PROJECT_DIR/backups" -name "*.sql.gz" -mtime -7 | sort -r | head -5 | while read -r backup; do
        echo "  $(basename "$backup")"
    done
}

# Main setup function
main() {
    log_info "=== CRON JOBS SETUP STARTED ==="
    
    check_cron || exit 1
    backup_crontab
    create_log_directories || exit 1
    create_maintenance_scripts
    setup_cron_jobs || exit 1
    test_cron_jobs || exit 1
    
    log_success "=== CRON JOBS SETUP COMPLETED ==="
    
    show_status
    
    echo ""
    log_info "Next steps:"
    echo "1. Review the installed cron jobs: crontab -l"
    echo "2. Monitor log files in: $PROJECT_DIR/logs"
    echo "3. Check backup files in: $PROJECT_DIR/backups"
    echo "4. Customize schedules as needed"
}

# Function to remove cron jobs
remove_cron_jobs() {
    log_info "Removing Natural Stone Marketplace cron jobs..."
    
    local temp_crontab="/tmp/natural-stone-crontab-remove"
    
    # Get existing crontab and remove our jobs
    if crontab -l > "$temp_crontab" 2>/dev/null; then
        grep -v "# Natural Stone Marketplace" "$temp_crontab" > "${temp_crontab}.clean"
        
        if crontab "${temp_crontab}.clean"; then
            log_success "Natural Stone Marketplace cron jobs removed"
        else
            log_error "Failed to remove cron jobs"
        fi
        
        rm -f "$temp_crontab" "${temp_crontab}.clean"
    else
        log_info "No existing crontab found"
    fi
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h      Show this help message"
        echo "  --remove        Remove Natural Stone Marketplace cron jobs"
        echo "  --status        Show current cron job status"
        echo ""
        echo "This script sets up automated tasks for:"
        echo "  - Daily database backups"
        echo "  - Backup monitoring"
        echo "  - Log cleanup"
        echo "  - Performance monitoring"
        echo "  - Security scans"
        echo "  - SSL certificate renewal"
        exit 0
        ;;
    --remove)
        remove_cron_jobs
        exit 0
        ;;
    --status)
        show_status
        exit 0
        ;;
esac

# Run main function
main "$@"
