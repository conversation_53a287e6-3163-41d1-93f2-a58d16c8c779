'use client';

import React, { useState, useCallback } from 'react';
import { 
  Plus, 
  Trash2, 
  <PERSON>, 
  <PERSON>Off, 
  Co<PERSON>, 
  Move, 
  RotateCw,
  Layers,
  Grid3X3,
  <PERSON><PERSON>,
  Settings2
} from 'lucide-react';
import { ProductPlacement } from '../core/ShowroomEngine';
import { PATTERN_CONFIGS } from '../utils/patterns';
import { GROUT_PRESETS } from '../utils/grout';

interface MultiProductManagerProps {
  products: ProductPlacement[];
  selectedProduct: string | null;
  onProductSelect: (productId: string) => void;
  onProductUpdate: (productId: string, updates: Partial<ProductPlacement>) => void;
  onProductRemove: (productId: string) => void;
  onProductDuplicate: (productId: string) => void;
  className?: string;
}

export const MultiProductManager: React.FC<MultiProductManagerProps> = ({
  products,
  selectedProduct,
  onProductSelect,
  onProductUpdate,
  onProductRemove,
  onProductDuplicate,
  className = ''
}) => {
  const [expandedProduct, setExpandedProduct] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'name' | 'area' | 'pattern'>('name');

  // Sort products
  const sortedProducts = [...products].sort((a, b) => {
    switch (sortBy) {
      case 'area':
        return b.area - a.area;
      case 'pattern':
        return a.pattern.localeCompare(b.pattern);
      default:
        return a.productId.localeCompare(b.productId);
    }
  });

  // Handle product property update
  const handlePropertyUpdate = useCallback((productId: string, property: string, value: any) => {
    onProductUpdate(productId, { [property]: value });
  }, [onProductUpdate]);

  // Handle grout settings update
  const handleGroutUpdate = useCallback((productId: string, groutUpdates: any) => {
    const product = products.find(p => p.id === productId);
    if (product) {
      onProductUpdate(productId, {
        groutSettings: { ...product.groutSettings, ...groutUpdates }
      });
    }
  }, [products, onProductUpdate]);

  // Calculate total area
  const totalArea = products.reduce((sum, product) => 
    product.visible ? sum + product.area : sum, 0
  );

  return (
    <div className={`bg-white border border-gray-200 rounded-lg ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <Layers size={16} />
            Çoklu Ürün Yönetimi
          </h3>
          
          <div className="flex items-center gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="text-xs px-2 py-1 border border-gray-300 rounded"
            >
              <option value="name">İsme Göre</option>
              <option value="area">Alana Göre</option>
              <option value="pattern">Pattern'e Göre</option>
            </select>
          </div>
        </div>

        <div className="text-xs text-gray-600 space-y-1">
          <div className="flex justify-between">
            <span>Toplam Ürün:</span>
            <span className="font-medium">{products.length}</span>
          </div>
          <div className="flex justify-between">
            <span>Görünür Ürün:</span>
            <span className="font-medium">{products.filter(p => p.visible).length}</span>
          </div>
          <div className="flex justify-between">
            <span>Toplam Alan:</span>
            <span className="font-medium">{totalArea.toFixed(2)} m²</span>
          </div>
        </div>
      </div>

      {/* Product List */}
      <div className="max-h-96 overflow-y-auto">
        {sortedProducts.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <Grid3X3 size={32} className="mx-auto mb-2 opacity-50" />
            <p className="text-sm">Henüz ürün eklenmemiş</p>
            <p className="text-xs">Ürünler sekmesinden ürün ekleyin</p>
          </div>
        ) : (
          <div className="space-y-1 p-2">
            {sortedProducts.map((product, index) => (
              <div
                key={product.id}
                className={`border rounded-lg transition-all duration-200 ${
                  selectedProduct === product.id
                    ? 'border-amber-500 bg-amber-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                {/* Product Header */}
                <div className="p-3">
                  <div className="flex items-center justify-between">
                    <div 
                      className="flex-1 cursor-pointer"
                      onClick={() => onProductSelect(product.id)}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                          #{index + 1}
                        </span>
                        <span className="text-sm font-medium text-gray-900 truncate">
                          {product.productId}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                        <span>{product.scale.width}×{product.scale.height} cm</span>
                        <span>{product.area.toFixed(2)} m²</span>
                        <span className="capitalize">
                          {PATTERN_CONFIGS[product.pattern]?.displayName}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-1">
                      <button
                        onClick={() => handlePropertyUpdate(product.id, 'visible', !product.visible)}
                        className={`p-1 rounded transition-colors ${
                          product.visible 
                            ? 'text-green-600 hover:bg-green-50' 
                            : 'text-gray-400 hover:bg-gray-50'
                        }`}
                        title={product.visible ? 'Gizle' : 'Göster'}
                      >
                        {product.visible ? <Eye size={14} /> : <EyeOff size={14} />}
                      </button>

                      <button
                        onClick={() => onProductDuplicate(product.id)}
                        className="p-1 text-blue-600 hover:bg-blue-50 rounded transition-colors"
                        title="Kopyala"
                      >
                        <Copy size={14} />
                      </button>

                      <button
                        onClick={() => setExpandedProduct(
                          expandedProduct === product.id ? null : product.id
                        )}
                        className="p-1 text-gray-600 hover:bg-gray-50 rounded transition-colors"
                        title="Ayarlar"
                      >
                        <Settings2 size={14} />
                      </button>

                      <button
                        onClick={() => onProductRemove(product.id)}
                        className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                        title="Kaldır"
                      >
                        <Trash2 size={14} />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Expanded Settings */}
                {expandedProduct === product.id && (
                  <div className="border-t border-gray-200 p-3 bg-gray-50 space-y-3">
                    {/* Opacity Control */}
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">
                        Opaklık: {Math.round(product.opacity * 100)}%
                      </label>
                      <input
                        type="range"
                        min="0.1"
                        max="1"
                        step="0.1"
                        value={product.opacity}
                        onChange={(e) => handlePropertyUpdate(product.id, 'opacity', parseFloat(e.target.value))}
                        className="w-full"
                      />
                    </div>

                    {/* Pattern Selection */}
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Döşeme Pattern</label>
                      <select
                        value={product.pattern}
                        onChange={(e) => handlePropertyUpdate(product.id, 'pattern', e.target.value)}
                        className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                      >
                        {Object.entries(PATTERN_CONFIGS).map(([key, config]) => (
                          <option key={key} value={key}>
                            {config.displayName}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Grout Settings */}
                    <div>
                      <label className="block text-xs text-gray-600 mb-2">Derz Ayarları</label>
                      
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">
                            Genişlik: {product.groutSettings.width}mm
                          </label>
                          <input
                            type="range"
                            min="0"
                            max="10"
                            step="0.5"
                            value={product.groutSettings.width}
                            onChange={(e) => handleGroutUpdate(product.id, { 
                              width: parseFloat(e.target.value) 
                            })}
                            className="w-full"
                          />
                        </div>

                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Renk</label>
                          <input
                            type="color"
                            value={product.groutSettings.color}
                            onChange={(e) => handleGroutUpdate(product.id, { 
                              color: e.target.value 
                            })}
                            className="w-full h-6 rounded border border-gray-300"
                          />
                        </div>
                      </div>

                      <div className="mt-2">
                        <label className="block text-xs text-gray-500 mb-1">Hazır Ayarlar</label>
                        <select
                          onChange={(e) => {
                            if (e.target.value && GROUT_PRESETS[e.target.value as keyof typeof GROUT_PRESETS]) {
                              handleGroutUpdate(product.id, 
                                GROUT_PRESETS[e.target.value as keyof typeof GROUT_PRESETS]
                              );
                            }
                          }}
                          className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                          defaultValue=""
                        >
                          <option value="">Seçin...</option>
                          <option value="minimal">Minimal</option>
                          <option value="standard">Standart</option>
                          <option value="bold">Kalın</option>
                          <option value="contrast">Kontrastlı</option>
                          <option value="invisible">Görünmez</option>
                        </select>
                      </div>
                    </div>

                    {/* Position Controls */}
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Pozisyon</label>
                      <div className="grid grid-cols-3 gap-1 text-xs">
                        <div>
                          <span className="text-gray-500">X:</span>
                          <span className="ml-1">{product.position.x.toFixed(1)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Y:</span>
                          <span className="ml-1">{product.position.y.toFixed(1)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Z:</span>
                          <span className="ml-1">{product.position.z.toFixed(1)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer Actions */}
      {products.length > 0 && (
        <div className="p-3 border-t border-gray-200 bg-gray-50">
          <div className="flex gap-2 text-xs">
            <button
              onClick={() => {
                products.forEach(product => {
                  handlePropertyUpdate(product.id, 'visible', true);
                });
              }}
              className="flex-1 px-3 py-2 bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
            >
              Tümünü Göster
            </button>
            
            <button
              onClick={() => {
                products.forEach(product => {
                  handlePropertyUpdate(product.id, 'visible', false);
                });
              }}
              className="flex-1 px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
            >
              Tümünü Gizle
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiProductManager;
