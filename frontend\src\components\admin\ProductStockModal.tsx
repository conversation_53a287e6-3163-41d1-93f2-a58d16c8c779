'use client'

import React from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  X,
  Warehouse,
  Building2,
  MapPin,
  Clock,
  DollarSign,
  Package,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Minus,
  Star,
  Phone,
  Mail
} from 'lucide-react'

interface ProductStockModalProps {
  isOpen: boolean
  onClose: () => void
  product: any
}

export function ProductStockModal({ isOpen, onClose, product }: ProductStockModalProps) {
  if (!isOpen) return null

  // Mock stok verileri - gerçek uygulamada API'den gelecek
  const mockStockData = {
    totalStock: 475,
    unit: 'm²',
    lastUpdate: '2 saat önce',
    producers: [
      {
        id: '1',
        name: 'Afyon Doğal Taş A.Ş.',
        location: 'Afyon',
        contact: { phone: '+90 272 123 45 67', email: '<EMAIL>' },
        totalStock: 250,
        lastUpdate: '2 saat önce',
        stockItems: [
          {
            dimension: '60x120x2cm',
            stock: 120,
            price: 115,
            currency: 'USD',
            surfaceFinish: 'Ham',
            packaging: 'Paletüstü',
            delivery: 'Fabrika',
            lastUpdate: '2 saat önce'
          },
          {
            dimension: '80x160x3cm',
            stock: 85,
            price: 125,
            currency: 'USD',
            surfaceFinish: 'Cilalı',
            packaging: 'Kasalı',
            delivery: 'Liman',
            lastUpdate: '1 gün önce'
          },
          {
            dimension: '40x80x2cm',
            stock: 45,
            price: 110,
            currency: 'USD',
            surfaceFinish: 'Honlu',
            packaging: 'Bandıllı',
            delivery: 'FOB',
            lastUpdate: '3 saat önce'
          }
        ]
      },
      {
        id: '2',
        name: 'Premium Stone Co.',
        location: 'İstanbul',
        contact: { phone: '+90 212 987 65 43', email: '<EMAIL>' },
        totalStock: 180,
        lastUpdate: '1 gün önce',
        stockItems: [
          {
            dimension: '60x120x2cm',
            stock: 95,
            price: 120,
            currency: 'USD',
            surfaceFinish: 'Ham',
            packaging: 'Paletüstü',
            delivery: 'Fabrika',
            lastUpdate: '1 gün önce'
          },
          {
            dimension: '80x160x3cm',
            stock: 65,
            price: 130,
            currency: 'USD',
            surfaceFinish: 'Cilalı',
            packaging: 'Kasalı',
            delivery: 'Liman',
            lastUpdate: '2 gün önce'
          },
          {
            dimension: '100x200x3cm',
            stock: 20,
            price: 140,
            currency: 'USD',
            surfaceFinish: 'Fırçalı',
            packaging: 'Kasalı',
            delivery: 'Liman',
            lastUpdate: '1 gün önce'
          }
        ]
      },
      {
        id: '3',
        name: 'Marmara Mermer Ltd.',
        location: 'Bursa',
        contact: { phone: '+90 224 555 12 34', email: '<EMAIL>' },
        totalStock: 45,
        lastUpdate: '3 gün önce',
        stockItems: [
          {
            dimension: '60x120x2cm',
            stock: 25,
            price: 110,
            currency: 'USD',
            surfaceFinish: 'Ham',
            packaging: 'Paletüstü',
            delivery: 'Fabrika',
            lastUpdate: '3 gün önce'
          },
          {
            dimension: '40x80x2cm',
            stock: 20,
            price: 105,
            currency: 'USD',
            surfaceFinish: 'Ham',
            packaging: 'Paletüstü',
            delivery: 'Fabrika',
            lastUpdate: '4 gün önce'
          }
        ]
      }
    ]
  }

  const getStockStatus = (stock: number) => {
    if (stock > 100) return { color: 'text-green-600', bg: 'bg-green-100', label: 'Yeterli', icon: CheckCircle }
    if (stock > 50) return { color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'Orta', icon: Minus }
    return { color: 'text-red-600', bg: 'bg-red-100', label: 'Düşük', icon: AlertTriangle }
  }

  const getUpdateStatus = (lastUpdate: string) => {
    const hours = lastUpdate.includes('saat') ? parseInt(lastUpdate) : 
                  lastUpdate.includes('gün') ? parseInt(lastUpdate) * 24 : 0
    
    if (hours <= 6) return { color: 'text-green-600', icon: TrendingUp, label: 'Güncel' }
    if (hours <= 24) return { color: 'text-yellow-600', icon: Minus, label: 'Normal' }
    return { color: 'text-red-600', icon: TrendingDown, label: 'Eski' }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-7xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-2xl font-bold text-gray-900">{product.name} - Stok Detayları</h3>
              <p className="text-gray-600">Üretici bazlı detaylı stok bilgileri ve fiyatlandırma</p>
            </div>
            <Button variant="outline" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Warehouse className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Toplam Stok</p>
                  <p className="text-xl font-bold text-gray-900">{mockStockData.totalStock} {mockStockData.unit}</p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Building2 className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Aktif Üretici</p>
                  <p className="text-xl font-bold text-gray-900">{mockStockData.producers.length}</p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-amber-100 rounded-lg">
                  <DollarSign className="w-5 h-5 text-amber-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Ortalama Fiyat</p>
                  <p className="text-xl font-bold text-gray-900">
                    ${Math.round(mockStockData.producers.reduce((sum, p) => 
                      sum + p.stockItems.reduce((itemSum, item) => itemSum + item.price, 0) / p.stockItems.length, 0
                    ) / mockStockData.producers.length)}/m²
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Clock className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Son Güncelleme</p>
                  <p className="text-xl font-bold text-gray-900">{mockStockData.lastUpdate}</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Producer Stock Details */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold">Üretici Bazlı Stok Detayları</h4>
            
            {mockStockData.producers.map((producer) => {
              const stockStatus = getStockStatus(producer.totalStock)
              const updateStatus = getUpdateStatus(producer.lastUpdate)
              const StatusIcon = stockStatus.icon
              const UpdateIcon = updateStatus.icon

              return (
                <Card key={producer.id} className="overflow-hidden">
                  {/* Producer Header */}
                  <div className="p-4 bg-gray-50 border-b">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                          <Building2 className="w-6 h-6 text-gray-600" />
                        </div>
                        <div>
                          <button className="text-lg font-semibold text-blue-600 hover:underline">
                            {producer.name}
                          </button>
                          <p className="text-sm text-gray-600 flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            {producer.location}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="flex items-center gap-2">
                            <StatusIcon className={`w-4 h-4 ${stockStatus.color}`} />
                            <span className="font-semibold">{producer.totalStock} m²</span>
                            <Badge variant="outline" className={stockStatus.bg}>
                              {stockStatus.label}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-1 mt-1">
                            <UpdateIcon className={`w-3 h-3 ${updateStatus.color}`} />
                            <span className="text-xs text-gray-500">
                              Son güncelleme: {producer.lastUpdate}
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Phone className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Mail className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Stock Items Table */}
                  <div className="p-4">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-2 px-3">Ebat</th>
                            <th className="text-left py-2 px-3">Stok</th>
                            <th className="text-left py-2 px-3">Fiyat</th>
                            <th className="text-left py-2 px-3">Yüzey İşlemi</th>
                            <th className="text-left py-2 px-3">Ambalaj</th>
                            <th className="text-left py-2 px-3">Teslimat</th>
                            <th className="text-left py-2 px-3">Son Güncelleme</th>
                            <th className="text-left py-2 px-3">Durum</th>
                          </tr>
                        </thead>
                        <tbody>
                          {producer.stockItems.map((item, index) => {
                            const itemStatus = getStockStatus(item.stock)
                            const ItemStatusIcon = itemStatus.icon
                            
                            return (
                              <tr key={index} className="border-b">
                                <td className="py-2 px-3 font-medium">{item.dimension}</td>
                                <td className="py-2 px-3">
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">{item.stock} m²</span>
                                  </div>
                                </td>
                                <td className="py-2 px-3">
                                  <span className="font-medium">${item.price}/{item.currency}</span>
                                </td>
                                <td className="py-2 px-3">
                                  <Badge variant="outline">{item.surfaceFinish}</Badge>
                                </td>
                                <td className="py-2 px-3">{item.packaging}</td>
                                <td className="py-2 px-3">{item.delivery}</td>
                                <td className="py-2 px-3 text-sm text-gray-600">{item.lastUpdate}</td>
                                <td className="py-2 px-3">
                                  <div className="flex items-center gap-1">
                                    <ItemStatusIcon className={`w-4 h-4 ${itemStatus.color}`} />
                                    <span className={`text-sm ${itemStatus.color}`}>
                                      {itemStatus.label}
                                    </span>
                                  </div>
                                </td>
                              </tr>
                            )
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>

          {/* Summary */}
          <Card className="p-6 mt-6">
            <h4 className="text-lg font-semibold mb-4">Stok Özeti</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h5 className="font-medium text-gray-900 mb-2">Ebat Bazlı Toplam Stok</h5>
                <div className="space-y-2">
                  {['60x120x2cm', '80x160x3cm', '40x80x2cm', '100x200x3cm'].map((dimension) => {
                    const totalForDimension = mockStockData.producers.reduce((sum, producer) => {
                      const item = producer.stockItems.find(item => item.dimension === dimension)
                      return sum + (item?.stock || 0)
                    }, 0)
                    
                    if (totalForDimension > 0) {
                      return (
                        <div key={dimension} className="flex justify-between">
                          <span className="text-sm text-gray-600">{dimension}:</span>
                          <span className="text-sm font-medium">{totalForDimension} m²</span>
                        </div>
                      )
                    }
                    return null
                  })}
                </div>
              </div>
              
              <div>
                <h5 className="font-medium text-gray-900 mb-2">Fiyat Aralığı</h5>
                <div className="space-y-2">
                  {['60x120x2cm', '80x160x3cm', '40x80x2cm'].map((dimension) => {
                    const prices = mockStockData.producers.reduce((acc: number[], producer) => {
                      const item = producer.stockItems.find(item => item.dimension === dimension)
                      if (item) acc.push(item.price)
                      return acc
                    }, [])
                    
                    if (prices.length > 0) {
                      const minPrice = Math.min(...prices)
                      const maxPrice = Math.max(...prices)
                      
                      return (
                        <div key={dimension} className="flex justify-between">
                          <span className="text-sm text-gray-600">{dimension}:</span>
                          <span className="text-sm font-medium">
                            ${minPrice}{minPrice !== maxPrice ? `-$${maxPrice}` : ''}
                          </span>
                        </div>
                      )
                    }
                    return null
                  })}
                </div>
              </div>
              
              <div>
                <h5 className="font-medium text-gray-900 mb-2">Stok Uyarıları</h5>
                <div className="space-y-2">
                  {mockStockData.producers.map((producer) => {
                    const lowStockItems = producer.stockItems.filter(item => item.stock < 50)
                    if (lowStockItems.length > 0) {
                      return (
                        <div key={producer.id} className="text-sm">
                          <span className="text-red-600 font-medium">{producer.name}:</span>
                          <span className="text-gray-600 ml-1">
                            {lowStockItems.length} ebat düşük stokta
                          </span>
                        </div>
                      )
                    }
                    return null
                  })}
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
