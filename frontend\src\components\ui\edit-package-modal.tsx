'use client'

import * as React from 'react'
import { <PERSON>alog, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Package, 
  Calendar, 
  DollarSign,
  Truck,
  AlertCircle,
  CheckCircle,
  Edit,
  Save,
  X
} from 'lucide-react'
import { DeliveryPackage } from '@/types/multi-delivery'

interface EditPackageModalProps {
  isOpen: boolean
  onClose: () => void
  package: DeliveryPackage | null
  onSavePackage: (packageData: Partial<DeliveryPackage>) => void
}

export function EditPackageModal({
  isOpen,
  onClose,
  package: pkg,
  onSavePackage
}: EditPackageModalProps) {
  const [formData, setFormData] = React.useState({
    quantity: 0,
    amount: 0,
    productionStartDate: '',
    productionEndDate: '',
    deliveryDate: '',
    deliveryMethod: 'delivery' as const,
    productionNotes: '',
    deliveryNotes: '',
    advancePercentage: 50,
    deliveryPercentage: 50,
    completionPercentage: 0
  })
  const [isLoading, setIsLoading] = React.useState(false)

  React.useEffect(() => {
    if (pkg) {
      setFormData({
        quantity: pkg.quantity,
        amount: pkg.amount,
        productionStartDate: pkg.productionStartDate || '',
        productionEndDate: pkg.productionEndDate || '',
        deliveryDate: pkg.deliveryDate || '',
        deliveryMethod: pkg.deliverySchedule?.deliveryMethod || 'delivery',
        productionNotes: pkg.productionNotes || '',
        deliveryNotes: pkg.deliveryNotes || '',
        advancePercentage: pkg.payments.find(p => p.paymentType === 'advance')?.amount || 50,
        deliveryPercentage: pkg.payments.find(p => p.paymentType === 'delivery')?.amount || 50,
        completionPercentage: pkg.payments.find(p => p.paymentType === 'completion')?.amount || 0
      })
    }
  }, [pkg])

  const handleSave = async () => {
    if (!pkg) return

    setIsLoading(true)
    try {
      await onSavePackage({
        id: pkg.id,
        quantity: formData.quantity,
        amount: formData.amount,
        productionStartDate: formData.productionStartDate,
        productionEndDate: formData.productionEndDate,
        deliveryDate: formData.deliveryDate,
        productionNotes: formData.productionNotes,
        deliveryNotes: formData.deliveryNotes,
        deliverySchedule: {
          ...pkg.deliverySchedule,
          deliveryMethod: formData.deliveryMethod,
          scheduledDate: formData.deliveryDate
        }
      })
      onClose()
    } catch (error) {
      console.error('Error saving package:', error)
      alert('Paket kaydedilirken hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'paused':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Tamamlandı'
      case 'in_progress':
        return 'Devam Ediyor'
      case 'paused':
        return 'Duraklatıldı'
      default:
        return 'Bekliyor'
    }
  }

  if (!pkg) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="w-5 h-5" />
            Paket #{pkg.packageNumber} Düzenle
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Package Status */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-blue-800">Paket Durumu</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <span className="text-sm font-medium text-blue-700">Üretim Durumu:</span>
                <Badge className={getStatusColor(pkg.productionStatus)}>
                  {getStatusText(pkg.productionStatus)}
                </Badge>
              </div>
              <div>
                <span className="text-sm font-medium text-blue-700">Teslimat Durumu:</span>
                <Badge className={getStatusColor(pkg.deliveryStatus)}>
                  {getStatusText(pkg.deliveryStatus)}
                </Badge>
              </div>
              <div>
                <span className="text-sm font-medium text-blue-700">Ödeme Durumu:</span>
                <Badge className={getStatusColor(pkg.paymentStatus)}>
                  {getStatusText(pkg.paymentStatus)}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                Temel Bilgiler
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="quantity">Miktar (m²)</Label>
                <Input
                  id="quantity"
                  type="number"
                  value={formData.quantity}
                  onChange={(e) => setFormData(prev => ({ ...prev, quantity: parseInt(e.target.value) || 0 }))}
                  disabled={pkg.productionStatus === 'completed'}
                />
                {pkg.productionStatus === 'completed' && (
                  <p className="text-xs text-gray-500 mt-1">Üretim tamamlandığı için değiştirilemez</p>
                )}
              </div>
              
              <div>
                <Label htmlFor="amount">Tutar ($)</Label>
                <Input
                  id="amount"
                  type="number"
                  value={formData.amount}
                  onChange={(e) => setFormData(prev => ({ ...prev, amount: parseInt(e.target.value) || 0 }))}
                  disabled={pkg.paymentStatus === 'paid'}
                />
                {pkg.paymentStatus === 'paid' && (
                  <p className="text-xs text-gray-500 mt-1">Ödeme alındığı için değiştirilemez</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Production Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Üretim Takvimi
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="productionStartDate">Üretim Başlangıç</Label>
                <Input
                  id="productionStartDate"
                  type="date"
                  value={formData.productionStartDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, productionStartDate: e.target.value }))}
                  disabled={pkg.productionStatus === 'completed'}
                />
              </div>
              
              <div>
                <Label htmlFor="productionEndDate">Üretim Bitiş</Label>
                <Input
                  id="productionEndDate"
                  type="date"
                  value={formData.productionEndDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, productionEndDate: e.target.value }))}
                  disabled={pkg.productionStatus === 'completed'}
                />
              </div>
              
              <div>
                <Label htmlFor="deliveryDate">Teslimat Tarihi</Label>
                <Input
                  id="deliveryDate"
                  type="date"
                  value={formData.deliveryDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, deliveryDate: e.target.value }))}
                  disabled={pkg.deliveryStatus === 'delivered'}
                />
              </div>
            </CardContent>
          </Card>

          {/* Delivery Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="w-5 h-5" />
                Teslimat Ayarları
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="deliveryMethod">Teslimat Yöntemi</Label>
                <Select
                  value={formData.deliveryMethod}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, deliveryMethod: value as any }))}
                  disabled={pkg.deliveryStatus === 'delivered'}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="factory_pickup">Fabrika Teslim (Müşteri Alır)</SelectItem>
                    <SelectItem value="delivery">Teslimat (Kargo/Nakliye)</SelectItem>
                    <SelectItem value="partial_delivery">Kısmi Teslimat</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Payment Terms */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="w-5 h-5" />
                Ödeme Koşulları
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="advancePercentage">Avans (%)</Label>
                <Input
                  id="advancePercentage"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.advancePercentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, advancePercentage: parseInt(e.target.value) || 0 }))}
                  disabled={pkg.paymentStatus === 'paid'}
                />
              </div>
              
              <div>
                <Label htmlFor="deliveryPercentage">Teslimat (%)</Label>
                <Input
                  id="deliveryPercentage"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.deliveryPercentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, deliveryPercentage: parseInt(e.target.value) || 0 }))}
                  disabled={pkg.paymentStatus === 'paid'}
                />
              </div>
              
              <div>
                <Label htmlFor="completionPercentage">Tamamlama (%)</Label>
                <Input
                  id="completionPercentage"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.completionPercentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, completionPercentage: parseInt(e.target.value) || 0 }))}
                  disabled={pkg.paymentStatus === 'paid'}
                />
              </div>
              
              <div className="col-span-3">
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Toplam:</span> {formData.advancePercentage + formData.deliveryPercentage + formData.completionPercentage}%
                  {(formData.advancePercentage + formData.deliveryPercentage + formData.completionPercentage) !== 100 && (
                    <span className="text-red-600 ml-2">
                      <AlertCircle className="w-4 h-4 inline mr-1" />
                      Toplam %100 olmalıdır
                    </span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Notlar</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="productionNotes">Üretim Notları</Label>
                <Textarea
                  id="productionNotes"
                  value={formData.productionNotes}
                  onChange={(e) => setFormData(prev => ({ ...prev, productionNotes: e.target.value }))}
                  rows={3}
                  placeholder="Üretim ile ilgili notlar..."
                />
              </div>
              
              <div>
                <Label htmlFor="deliveryNotes">Teslimat Notları</Label>
                <Textarea
                  id="deliveryNotes"
                  value={formData.deliveryNotes}
                  onChange={(e) => setFormData(prev => ({ ...prev, deliveryNotes: e.target.value }))}
                  rows={3}
                  placeholder="Teslimat ile ilgili notlar..."
                />
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              <X className="w-4 h-4 mr-2" />
              İptal
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading || (formData.advancePercentage + formData.deliveryPercentage + formData.completionPercentage) !== 100}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Kaydet
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
