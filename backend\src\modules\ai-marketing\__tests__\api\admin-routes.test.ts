// Admin API Routes Tests
// Tests for AI Marketing admin dashboard API endpoints

import request from 'supertest';
import express from 'express';
import adminRoutes from '../../routes/admin/ai-marketing';

// Mock dependencies
jest.mock('../../orchestrator/AIMarketingOrchestrator');
jest.mock('../../database/DatabaseManager');
jest.mock('../../integrations/APIIntegrationManager');

const app = express();
app.use(express.json());
app.use('/api/admin/ai-marketing', adminRoutes);

describe('AI Marketing Admin API Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/admin/ai-marketing/stats', () => {
    test('should return system statistics successfully', async () => {
      const response = await request(app)
        .get('/api/admin/ai-marketing/stats')
        .expect(200);

      expect(response.body).toHaveProperty('adaptiveLearning');
      expect(response.body).toHaveProperty('continuousResearch');
      expect(response.body).toHaveProperty('dynamicStrategy');
      expect(response.body).toHaveProperty('realTimeOptimizer');
      expect(response.body).toHaveProperty('knowledgeBase');
      expect(response.body).toHaveProperty('systemHealth');

      // Verify structure of adaptiveLearning stats
      expect(response.body.adaptiveLearning).toHaveProperty('learningCycles');
      expect(response.body.adaptiveLearning).toHaveProperty('patternsDiscovered');
      expect(response.body.adaptiveLearning).toHaveProperty('successRate');
      expect(response.body.adaptiveLearning).toHaveProperty('lastCycle');

      // Verify system health structure
      expect(response.body.systemHealth).toHaveProperty('overall');
      expect(response.body.systemHealth).toHaveProperty('tradeMap');
      expect(response.body.systemHealth).toHaveProperty('linkedIn');
      expect(response.body.systemHealth).toHaveProperty('googleAds');
      expect(response.body.systemHealth).toHaveProperty('database');
    });

    test('should handle initialization errors gracefully', async () => {
      // Mock initialization failure
      const mockError = new Error('Initialization failed');
      
      const response = await request(app)
        .get('/api/admin/ai-marketing/stats')
        .expect(500);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Failed to fetch system statistics');
    });
  });

  describe('GET /api/admin/ai-marketing/alerts', () => {
    test('should return active alerts', async () => {
      const response = await request(app)
        .get('/api/admin/ai-marketing/alerts')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      if (response.body.length > 0) {
        const alert = response.body[0];
        expect(alert).toHaveProperty('id');
        expect(alert).toHaveProperty('severity');
        expect(alert).toHaveProperty('message');
        expect(alert).toHaveProperty('timestamp');
        expect(alert).toHaveProperty('acknowledged');
        
        expect(['critical', 'warning', 'info']).toContain(alert.severity);
        expect(typeof alert.acknowledged).toBe('boolean');
      }
    });

    test('should handle database errors when fetching alerts', async () => {
      const response = await request(app)
        .get('/api/admin/ai-marketing/alerts')
        .expect(500);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Failed to fetch alerts');
    });
  });

  describe('POST /api/admin/ai-marketing/alerts/:id/acknowledge', () => {
    test('should acknowledge alert successfully', async () => {
      const alertId = 'test-alert-123';
      
      const response = await request(app)
        .post(`/api/admin/ai-marketing/alerts/${alertId}/acknowledge`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', 'Alert acknowledged');
    });

    test('should handle invalid alert ID', async () => {
      const invalidId = 'invalid-alert-id';
      
      const response = await request(app)
        .post(`/api/admin/ai-marketing/alerts/${invalidId}/acknowledge`)
        .expect(500);

      expect(response.body).toHaveProperty('error');
    });
  });

  describe('GET /api/admin/ai-marketing/learning/patterns', () => {
    test('should return learning patterns', async () => {
      const response = await request(app)
        .get('/api/admin/ai-marketing/learning/patterns')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      if (response.body.length > 0) {
        const pattern = response.body[0];
        expect(pattern).toHaveProperty('id');
        expect(pattern).toHaveProperty('pattern_data');
        expect(pattern).toHaveProperty('confidence');
        expect(pattern).toHaveProperty('success_rate');
        expect(pattern).toHaveProperty('context');
        expect(pattern).toHaveProperty('usage_count');
      }
    });

    test('should filter patterns by context', async () => {
      const context = 'email-marketing';
      
      const response = await request(app)
        .get(`/api/admin/ai-marketing/learning/patterns?context=${context}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      // If patterns exist, they should match the context
      response.body.forEach((pattern: any) => {
        expect(pattern.context).toBe(context);
      });
    });

    test('should limit number of patterns returned', async () => {
      const limit = 5;
      
      const response = await request(app)
        .get(`/api/admin/ai-marketing/learning/patterns?limit=${limit}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeLessThanOrEqual(limit);
    });
  });

  describe('GET /api/admin/ai-marketing/research/insights', () => {
    test('should return research insights', async () => {
      const response = await request(app)
        .get('/api/admin/ai-marketing/research/insights')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      if (response.body.length > 0) {
        const insight = response.body[0];
        expect(insight).toHaveProperty('id');
        expect(insight).toHaveProperty('insight_text');
        expect(insight).toHaveProperty('source');
        expect(insight).toHaveProperty('confidence');
        expect(insight).toHaveProperty('actionable');
        expect(insight).toHaveProperty('category');
      }
    });

    test('should filter insights by category', async () => {
      const category = 'market-trends';
      
      const response = await request(app)
        .get(`/api/admin/ai-marketing/research/insights?category=${category}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      response.body.forEach((insight: any) => {
        expect(insight.category).toBe(category);
      });
    });

    test('should filter actionable insights', async () => {
      const response = await request(app)
        .get('/api/admin/ai-marketing/research/insights?actionable=true')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      response.body.forEach((insight: any) => {
        expect(insight.actionable).toBe(true);
      });
    });
  });

  describe('GET /api/admin/ai-marketing/performance/metrics', () => {
    test('should return performance metrics', async () => {
      const response = await request(app)
        .get('/api/admin/ai-marketing/performance/metrics')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      if (response.body.length > 0) {
        const metric = response.body[0];
        expect(metric).toHaveProperty('id');
        expect(metric).toHaveProperty('metric_name');
        expect(metric).toHaveProperty('metric_value');
        expect(metric).toHaveProperty('module_name');
        expect(metric).toHaveProperty('recorded_at');
      }
    });

    test('should filter metrics by module', async () => {
      const module = 'learning';
      
      const response = await request(app)
        .get(`/api/admin/ai-marketing/performance/metrics?module=${module}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      response.body.forEach((metric: any) => {
        expect(metric.module_name).toBe(module);
      });
    });

    test('should filter metrics by time range', async () => {
      const days = 7;
      
      const response = await request(app)
        .get(`/api/admin/ai-marketing/performance/metrics?days=${days}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      
      response.body.forEach((metric: any) => {
        const recordedDate = new Date(metric.recorded_at);
        expect(recordedDate.getTime()).toBeGreaterThanOrEqual(cutoffDate.getTime());
      });
    });
  });

  describe('POST /api/admin/ai-marketing/campaigns/create', () => {
    test('should create integrated campaign successfully', async () => {
      const campaignData = {
        name: 'Test Campaign',
        targetMarkets: ['US', 'DE'],
        budget: 5000,
        duration: 30,
        productCategory: 'marble',
        objectives: ['lead-generation', 'brand-awareness']
      };

      const response = await request(app)
        .post('/api/admin/ai-marketing/campaigns/create')
        .send(campaignData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('campaign');
      expect(response.body).toHaveProperty('message');
    });

    test('should validate required fields', async () => {
      const incompleteCampaignData = {
        name: 'Test Campaign'
        // Missing targetMarkets and budget
      };

      const response = await request(app)
        .post('/api/admin/ai-marketing/campaigns/create')
        .send(incompleteCampaignData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Missing required fields');
    });

    test('should handle campaign creation errors', async () => {
      const campaignData = {
        name: 'Test Campaign',
        targetMarkets: ['US'],
        budget: 1000
      };

      const response = await request(app)
        .post('/api/admin/ai-marketing/campaigns/create')
        .send(campaignData)
        .expect(500);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Failed to create integrated campaign');
    });
  });

  describe('POST /api/admin/ai-marketing/research/start', () => {
    test('should start market research successfully', async () => {
      const researchData = {
        productCode: '2515',
        targetCountries: ['US', 'DE', 'GB'],
        keywords: ['natural stone', 'marble', 'construction']
      };

      const response = await request(app)
        .post('/api/admin/ai-marketing/research/start')
        .send(researchData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('research');
      expect(response.body).toHaveProperty('message');
    });

    test('should validate research parameters', async () => {
      const incompleteData = {
        productCode: '2515'
        // Missing targetCountries and keywords
      };

      const response = await request(app)
        .post('/api/admin/ai-marketing/research/start')
        .send(incompleteData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Missing required fields');
    });
  });

  describe('GET /api/admin/ai-marketing/system/health', () => {
    test('should return system health status', async () => {
      const response = await request(app)
        .get('/api/admin/ai-marketing/system/health')
        .expect(200);

      expect(response.body).toHaveProperty('health');
      expect(response.body).toHaveProperty('metrics');
      expect(response.body).toHaveProperty('timestamp');

      expect(response.body.health).toHaveProperty('overall');
      expect(response.body.health).toHaveProperty('tradeMap');
      expect(response.body.health).toHaveProperty('linkedIn');
      expect(response.body.health).toHaveProperty('googleAds');
    });
  });

  describe('POST /api/admin/ai-marketing/system/restart', () => {
    test('should restart system successfully', async () => {
      const response = await request(app)
        .post('/api/admin/ai-marketing/system/restart')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('restarted successfully');
    });

    test('should handle restart errors gracefully', async () => {
      const response = await request(app)
        .post('/api/admin/ai-marketing/system/restart')
        .expect(500);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Failed to restart system');
    });
  });

  describe('Error Handling', () => {
    test('should handle malformed JSON requests', async () => {
      const response = await request(app)
        .post('/api/admin/ai-marketing/campaigns/create')
        .send('invalid json')
        .set('Content-Type', 'application/json')
        .expect(400);
    });

    test('should handle missing request body', async () => {
      const response = await request(app)
        .post('/api/admin/ai-marketing/campaigns/create')
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    test('should handle database connection errors', async () => {
      // This would be tested with actual database connection issues
      // For now, we verify the error response structure
      const response = await request(app)
        .get('/api/admin/ai-marketing/stats')
        .expect(500);

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('details');
    });
  });

  describe('Rate Limiting and Security', () => {
    test('should handle concurrent requests', async () => {
      const promises = [];
      
      // Send 10 concurrent requests
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app).get('/api/admin/ai-marketing/stats')
        );
      }

      const responses = await Promise.all(promises);
      
      // All requests should succeed or fail gracefully
      responses.forEach(response => {
        expect([200, 500]).toContain(response.status);
      });
    });

    test('should validate input parameters', async () => {
      // Test SQL injection attempt
      const maliciousInput = "'; DROP TABLE learning_patterns; --";
      
      const response = await request(app)
        .get(`/api/admin/ai-marketing/learning/patterns?context=${encodeURIComponent(maliciousInput)}`)
        .expect(200); // Should handle gracefully, not crash

      expect(Array.isArray(response.body)).toBe(true);
    });
  });
});
