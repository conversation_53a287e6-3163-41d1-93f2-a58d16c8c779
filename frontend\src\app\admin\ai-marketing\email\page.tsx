'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Mail,
  Users,
  TrendingUp,
  Globe,
  Search,
  Plus,
  Eye,
  Edit,
  Trash2,
  Send,
  Clock,
  CheckCircle,
  XCircle,
  BarChart3,
  Wand2,
  Shield
} from 'lucide-react';

// Modal components
import CountryDetailModal from '@/components/admin/ai-marketing/CountryDetailModal';
import CampaignDetailModal from '@/components/admin/ai-marketing/CampaignDetailModal';
import EditCountryModal from '@/components/admin/ai-marketing/EditCountryModal';
import NewCampaignModal from '@/components/admin/ai-marketing/NewCampaignModal';
import CountrySelectionModal from '@/components/admin/ai-marketing/CountrySelectionModal';

interface CountryEmailList {
  id: string;
  countryCode: string;
  countryName: string;
  flag: string;
  totalSubscribers: number;
  activeSubscribers: number;
  lastUpdated: Date;
  segments: number;
}

interface EmailCampaign {
  id: string;
  name: string;
  subject: string;
  targetCountries: string[];
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'paused';
  scheduledFor?: Date;
  sentAt?: Date;
  recipients: number;
  openRate: number;
  clickRate: number;
  conversionRate: number;
  createdAt: Date;
}

export default function EmailMarketingPage() {
  const [countryLists, setCountryLists] = useState<CountryEmailList[]>([]);
  const [campaigns, setCampaigns] = useState<EmailCampaign[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  // Modal states
  const [selectedCountry, setSelectedCountry] = useState<CountryEmailList | null>(null);
  const [selectedCampaign, setSelectedCampaign] = useState<EmailCampaign | null>(null);
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [showCampaignModal, setShowCampaignModal] = useState(false);
  const [showEditCountryModal, setShowEditCountryModal] = useState(false);
  const [showNewCampaignModal, setShowNewCampaignModal] = useState(false);
  const [showCountrySelectionModal, setShowCountrySelectionModal] = useState(false);

  useEffect(() => {
    fetchEmailData();
  }, []);

  const fetchEmailData = async () => {
    setLoading(true);
    try {
      // Mock data - gerçek API'den gelecek
      const mockCountryLists: CountryEmailList[] = [
        {
          id: '1',
          countryCode: 'US',
          countryName: 'Amerika Birleşik Devletleri',
          flag: '🇺🇸',
          totalSubscribers: 2847,
          activeSubscribers: 2654,
          lastUpdated: new Date('2025-07-04'),
          segments: 8
        },
        {
          id: '2',
          countryCode: 'DE',
          countryName: 'Almanya',
          flag: '🇩🇪',
          totalSubscribers: 1923,
          activeSubscribers: 1834,
          lastUpdated: new Date('2025-07-04'),
          segments: 6
        },
        {
          id: '3',
          countryCode: 'IT',
          countryName: 'İtalya',
          flag: '🇮🇹',
          totalSubscribers: 1456,
          activeSubscribers: 1398,
          lastUpdated: new Date('2025-07-03'),
          segments: 5
        },
        {
          id: '4',
          countryCode: 'FR',
          countryName: 'Fransa',
          flag: '🇫🇷',
          totalSubscribers: 1234,
          activeSubscribers: 1187,
          lastUpdated: new Date('2025-07-03'),
          segments: 4
        },
        {
          id: '5',
          countryCode: 'ES',
          countryName: 'İspanya',
          flag: '🇪🇸',
          totalSubscribers: 987,
          activeSubscribers: 945,
          lastUpdated: new Date('2025-07-02'),
          segments: 3
        }
      ];

      const mockCampaigns: EmailCampaign[] = [
        {
          id: '1',
          name: 'Yeni Ürün Tanıtımı - Traverten',
          subject: 'Yeni Traverten Koleksiyonumuz Sizleri Bekliyor!',
          targetCountries: ['US', 'DE', 'IT'],
          status: 'sent',
          scheduledFor: new Date('2025-07-01'),
          recipients: 6224,
          openRate: 24.5,
          clickRate: 3.2,
          conversionRate: 1.8,
          createdAt: new Date('2025-06-28')
        },
        {
          id: '2',
          name: 'Özel İndirim Kampanyası',
          subject: 'Sadece Bu Hafta: %15 İndirim!',
          targetCountries: ['FR', 'ES'],
          status: 'sending',
          scheduledFor: new Date('2025-07-04'),
          recipients: 2221,
          openRate: 0,
          clickRate: 0,
          conversionRate: 0,
          createdAt: new Date('2025-07-03')
        },
        {
          id: '3',
          name: 'Mermer Trend Raporu',
          subject: '2025 Mermer Trendleri ve Pazar Analizi',
          targetCountries: ['US', 'DE', 'IT', 'FR'],
          status: 'scheduled',
          scheduledFor: new Date('2025-07-06'),
          recipients: 7457,
          openRate: 0,
          clickRate: 0,
          conversionRate: 0,
          createdAt: new Date('2025-07-04')
        }
      ];

      setCountryLists([]);
      setCampaigns([]);
    } catch (error) {
      console.error('Error fetching email data:', error);
    } finally {
      setLoading(false);
    }
  };

  // İşlem fonksiyonları
  const handleViewCountry = (country: CountryEmailList) => {
    setSelectedCountry(country);
    setShowCountryModal(true);
  };

  const handleEditCountry = (country: CountryEmailList) => {
    setSelectedCountry(country);
    setShowEditCountryModal(true);
  };

  const handleSendCampaignToCountry = (country: CountryEmailList) => {
    setSelectedCountry(country);
    setShowNewCampaignModal(true);
  };

  const handleViewCampaign = (campaign: EmailCampaign) => {
    setSelectedCampaign(campaign);
    setShowCampaignModal(true);
  };

  const handleEditCampaign = (campaign: EmailCampaign) => {
    setSelectedCampaign(campaign);
    // Edit campaign logic burada implementasyonu yapılacak
    console.log('Edit campaign:', campaign);
  };

  const handleCreateAICampaign = () => {
    // En yüksek abone sayısına sahip ülkeyi seç
    const topCountry = countryLists.reduce((prev, current) =>
      (prev.activeSubscribers > current.activeSubscribers) ? prev : current
    );

    if (topCountry) {
      setSelectedCountry(topCountry);
      setShowNewCampaignModal(true);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary">Taslak</Badge>;
      case 'scheduled':
        return <Badge variant="outline">Zamanlandı</Badge>;
      case 'sending':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Gönderiliyor</Badge>;
      case 'sent':
        return <Badge variant="default" className="bg-green-100 text-green-800">Gönderildi</Badge>;
      case 'paused':
        return <Badge variant="destructive">Duraklatıldı</Badge>;
      default:
        return <Badge variant="outline">Bilinmiyor</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit className="w-4 h-4 text-gray-500" />;
      case 'scheduled':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'sending':
        return <Send className="w-4 h-4 text-blue-600" />;
      case 'sent':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'paused':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const filteredCountryLists = countryLists.filter(list =>
    list.countryName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    list.countryCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalSubscribers = countryLists.reduce((sum, list) => sum + list.totalSubscribers, 0);
  const totalActiveSubscribers = countryLists.reduce((sum, list) => sum + list.activeSubscribers, 0);
  const activeCampaigns = campaigns.filter(c => c.status === 'sending' || c.status === 'scheduled').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Email Marketing AI</h1>
          <p className="text-gray-600 mt-1">
            Ülke bazlı email listeleri ve AI destekli kampanya yönetimi
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowCountrySelectionModal(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            Yeni Kampanya
          </Button>
          <Button
            onClick={() => handleCreateAICampaign()}
          >
            <Wand2 className="w-4 h-4 mr-2" />
            AI Kampanya Oluştur
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Toplam Abone</p>
                <p className="text-2xl font-bold">{totalSubscribers.toLocaleString()}</p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Aktif Abone</p>
                <p className="text-2xl font-bold">{totalActiveSubscribers.toLocaleString()}</p>
              </div>
              <Mail className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Ülke Sayısı</p>
                <p className="text-2xl font-bold">{countryLists.length}</p>
              </div>
              <Globe className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Aktif Kampanya</p>
                <p className="text-2xl font-bold">{activeCampaigns}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Tabs */}
      <Tabs defaultValue="lists" className="space-y-4">
        <TabsList>
          <TabsTrigger value="lists">Ülke Listeleri</TabsTrigger>
          <TabsTrigger value="campaigns">
            Kampanyalar
            {activeCampaigns > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {activeCampaigns}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="anti-spam">
            <div className="flex items-center space-x-1">
              <Shield className="w-4 h-4" />
              <span>Spam Önleme</span>
            </div>
          </TabsTrigger>
          <TabsTrigger value="analytics">Analitik</TabsTrigger>
          <TabsTrigger value="settings">AI Ayarları</TabsTrigger>
        </TabsList>

        <TabsContent value="lists" className="space-y-4">
          {/* Search */}
          <div className="flex items-center space-x-2">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Ülke ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Country Lists Table */}
          <Card>
            <CardHeader>
              <CardTitle>Ülke Bazlı Email Listeleri</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Ülke</TableHead>
                    <TableHead>Toplam Abone</TableHead>
                    <TableHead>Aktif Abone</TableHead>
                    <TableHead>Segment</TableHead>
                    <TableHead>Son Güncelleme</TableHead>
                    <TableHead>İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCountryLists.map((list) => (
                    <TableRow key={list.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="text-2xl">{list.flag}</span>
                          <div>
                            <p className="font-medium">{list.countryName}</p>
                            <p className="text-sm text-gray-500">{list.countryCode}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {list.totalSubscribers.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-green-600">
                            {list.activeSubscribers.toLocaleString()}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {((list.activeSubscribers / list.totalSubscribers) * 100).toFixed(1)}%
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>{list.segments}</TableCell>
                      <TableCell>
                        {list.lastUpdated.toLocaleDateString('tr-TR')}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewCountry(list)}
                            title="Detayları Görüntüle"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditCountry(list)}
                            title="Listeyi Düzenle"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSendCampaignToCountry(list)}
                            title="Kampanya Gönder"
                          >
                            <Send className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-4">
          {/* Campaigns Table */}
          <Card>
            <CardHeader>
              <CardTitle>Email Kampanyaları</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Kampanya</TableHead>
                    <TableHead>Hedef Ülkeler</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead>Alıcı</TableHead>
                    <TableHead>Açılma Oranı</TableHead>
                    <TableHead>Tıklama Oranı</TableHead>
                    <TableHead>İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {campaigns.map((campaign) => (
                    <TableRow key={campaign.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{campaign.name}</p>
                          <p className="text-sm text-gray-500">{campaign.subject}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          {campaign.targetCountries.map((country) => (
                            <Badge key={country} variant="outline" className="text-xs">
                              {country}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(campaign.status)}
                          {getStatusBadge(campaign.status)}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {campaign.recipients.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {campaign.openRate > 0 ? `${campaign.openRate}%` : '-'}
                      </TableCell>
                      <TableCell>
                        {campaign.clickRate > 0 ? `${campaign.clickRate}%` : '-'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewCampaign(campaign)}
                            title="Kampanya Detayları"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditCampaign(campaign)}
                            title="Kampanyayı Düzenle"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {/* Email Analytics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Bu Ay Gönderilen</p>
                    <p className="text-2xl font-bold">24</p>
                    <div className="flex items-center space-x-1 mt-1">
                      <TrendingUp className="w-3 h-3 text-green-500" />
                      <span className="text-xs text-green-600">+12%</span>
                    </div>
                  </div>
                  <Mail className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Ortalama Açılma</p>
                    <p className="text-2xl font-bold">24.8%</p>
                    <div className="flex items-center space-x-1 mt-1">
                      <TrendingUp className="w-3 h-3 text-green-500" />
                      <span className="text-xs text-green-600">+2.3%</span>
                    </div>
                  </div>
                  <Eye className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Ortalama Tıklama</p>
                    <p className="text-2xl font-bold">3.4%</p>
                    <div className="flex items-center space-x-1 mt-1">
                      <TrendingUp className="w-3 h-3 text-green-500" />
                      <span className="text-xs text-green-600">+0.8%</span>
                    </div>
                  </div>
                  <BarChart3 className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Toplam Dönüşüm</p>
                    <p className="text-2xl font-bold">89</p>
                    <div className="flex items-center space-x-1 mt-1">
                      <TrendingUp className="w-3 h-3 text-green-500" />
                      <span className="text-xs text-green-600">+15%</span>
                    </div>
                  </div>
                  <Users className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Aylık Performans Trendi</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-end justify-between space-x-2">
                  {[
                    { month: 'Oca', campaigns: 18, openRate: 22.1 },
                    { month: 'Şub', campaigns: 22, openRate: 23.5 },
                    { month: 'Mar', campaigns: 19, openRate: 21.8 },
                    { month: 'Nis', campaigns: 25, openRate: 24.2 },
                    { month: 'May', campaigns: 28, openRate: 25.1 },
                    { month: 'Haz', campaigns: 24, openRate: 24.8 }
                  ].map((data, index) => {
                    const maxCampaigns = 30;
                    const height = (data.campaigns / maxCampaigns) * 100;

                    return (
                      <div key={index} className="flex flex-col items-center">
                        <div
                          className="bg-blue-500 rounded-t w-8 min-h-[8px] transition-all hover:bg-blue-600"
                          style={{ height: `${height}%` }}
                          title={`${data.month}: ${data.campaigns} kampanya, ${data.openRate}% açılma`}
                        ></div>
                        <span className="text-xs text-gray-500 mt-2">{data.month}</span>
                      </div>
                    );
                  })}
                </div>
                <div className="mt-4 text-center">
                  <p className="text-sm text-gray-600">
                    Son 6 ayda toplam 136 kampanya gönderildi
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Country Performance */}
            <Card>
              <CardHeader>
                <CardTitle>Ülke Bazlı Performans</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { country: 'Amerika', flag: '🇺🇸', campaigns: 8, openRate: 26.2, clickRate: 3.8 },
                    { country: 'Almanya', flag: '🇩🇪', campaigns: 6, openRate: 23.8, clickRate: 3.2 },
                    { country: 'İtalya', flag: '🇮🇹', campaigns: 5, openRate: 21.5, clickRate: 2.9 },
                    { country: 'Fransa', flag: '🇫🇷', campaigns: 3, openRate: 24.1, clickRate: 3.5 },
                    { country: 'İspanya', flag: '🇪🇸', campaigns: 2, openRate: 22.7, clickRate: 3.1 }
                  ].map((country, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{country.flag}</span>
                        <div>
                          <p className="font-medium">{country.country}</p>
                          <p className="text-sm text-gray-500">{country.campaigns} kampanya</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-green-600">{country.openRate}% açılma</p>
                        <p className="text-sm text-gray-500">{country.clickRate}% tıklama</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Engagement Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Etkileşim Analizi</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-24 h-24 mx-auto mb-4 relative">
                    <div className="w-full h-full rounded-full border-8 border-gray-200"></div>
                    <div className="absolute inset-0 w-full h-full rounded-full border-8 border-green-500 border-t-transparent transform rotate-45"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-lg font-bold">24.8%</span>
                    </div>
                  </div>
                  <h3 className="font-medium">Açılma Oranı</h3>
                  <p className="text-sm text-gray-500">Sektör ortalaması: 21.3%</p>
                </div>
                <div className="text-center">
                  <div className="w-24 h-24 mx-auto mb-4 relative">
                    <div className="w-full h-full rounded-full border-8 border-gray-200"></div>
                    <div className="absolute inset-0 w-full h-full rounded-full border-8 border-blue-500 border-t-transparent transform rotate-12"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-lg font-bold">3.4%</span>
                    </div>
                  </div>
                  <h3 className="font-medium">Tıklama Oranı</h3>
                  <p className="text-sm text-gray-500">Sektör ortalaması: 2.8%</p>
                </div>
                <div className="text-center">
                  <div className="w-24 h-24 mx-auto mb-4 relative">
                    <div className="w-full h-full rounded-full border-8 border-gray-200"></div>
                    <div className="absolute inset-0 w-full h-full rounded-full border-8 border-purple-500 border-t-transparent transform rotate-6"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-lg font-bold">1.8%</span>
                    </div>
                  </div>
                  <h3 className="font-medium">Dönüşüm Oranı</h3>
                  <p className="text-sm text-gray-500">Sektör ortalaması: 1.2%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          {/* AI Settings */}
          <Card>
            <CardHeader>
              <CardTitle>AI Email Üretim Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Varsayılan İçerik Tonu
                  </label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="professional">Profesyonel</option>
                    <option value="friendly">Samimi</option>
                    <option value="formal">Resmi</option>
                    <option value="enthusiastic">Coşkulu</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    AI Model Seçimi
                  </label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="gpt-4">GPT-4 (Önerilen)</option>
                    <option value="claude">Claude</option>
                    <option value="gemini">Gemini</option>
                  </select>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Otomatik Konu Satırı Üretimi</p>
                    <p className="text-sm text-gray-500">AI ile otomatik konu satırı oluştur</p>
                  </div>
                  <input type="checkbox" defaultChecked className="w-4 h-4" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Kişiselleştirme</p>
                    <p className="text-sm text-gray-500">Alıcı bilgilerine göre içerik kişiselleştir</p>
                  </div>
                  <input type="checkbox" defaultChecked className="w-4 h-4" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">A/B Test Önerileri</p>
                    <p className="text-sm text-gray-500">AI ile A/B test varyantları oluştur</p>
                  </div>
                  <input type="checkbox" className="w-4 h-4" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Optimal Zamanlama</p>
                    <p className="text-sm text-gray-500">En uygun gönderim zamanını AI ile belirle</p>
                  </div>
                  <input type="checkbox" defaultChecked className="w-4 h-4" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Campaign Automation */}
          <Card>
            <CardHeader>
              <CardTitle>Kampanya Otomasyonu</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Otomatik Kampanya Sıklığı
                  </label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="weekly">Haftalık</option>
                    <option value="biweekly">İki Haftada Bir</option>
                    <option value="monthly">Aylık</option>
                    <option value="manual">Manuel</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Maksimum Günlük Kampanya
                  </label>
                  <input
                    type="number"
                    defaultValue="3"
                    min="1"
                    max="10"
                    className="w-full border rounded px-3 py-2"
                  />
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Otomatik Segmentasyon</p>
                    <p className="text-sm text-gray-500">Aboneleri otomatik olarak segmentlere ayır</p>
                  </div>
                  <input type="checkbox" defaultChecked className="w-4 h-4" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Performans Bazlı Optimizasyon</p>
                    <p className="text-sm text-gray-500">Düşük performanslı kampanyaları otomatik durdur</p>
                  </div>
                  <input type="checkbox" defaultChecked className="w-4 h-4" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Otomatik Yeniden Gönderim</p>
                    <p className="text-sm text-gray-500">Açmayanlara farklı konu ile tekrar gönder</p>
                  </div>
                  <input type="checkbox" className="w-4 h-4" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Content Templates */}
          <Card>
            <CardHeader>
              <CardTitle>İçerik Şablonları</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  { name: 'Ürün Tanıtımı', usage: '45%', lastUsed: '2 gün önce' },
                  { name: 'Özel İndirim', usage: '32%', lastUsed: '1 hafta önce' },
                  { name: 'Haber Bülteni', usage: '18%', lastUsed: '3 gün önce' },
                  { name: 'Hoş Geldin', usage: '5%', lastUsed: '1 gün önce' }
                ].map((template, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{template.name}</h4>
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Kullanım:</span>
                        <span className="font-medium">{template.usage}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Son kullanım:</span>
                        <span className="text-gray-500">{template.lastUsed}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button variant="outline" className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  Yeni Şablon Ekle
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="anti-spam" className="space-y-4">
          {/* Spam Score Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="md:col-span-2">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Spam Skoru</p>
                    <div className="flex items-center space-x-2">
                      <p className="text-4xl font-bold text-green-600">8.7/10</p>
                      <Badge className="bg-green-100 text-green-800">Mükemmel</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Yüksek skor = Düşük spam riski
                    </p>
                  </div>
                  <Shield className="w-16 h-16 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Teslimat Oranı</p>
                    <p className="text-2xl font-bold text-blue-600">98.5%</p>
                    <p className="text-xs text-green-600 mt-1">+2.1% bu ay</p>
                  </div>
                  <Mail className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Spam Oranı</p>
                    <p className="text-2xl font-bold text-red-600">0.3%</p>
                    <p className="text-xs text-green-600 mt-1">-0.2% bu ay</p>
                  </div>
                  <XCircle className="w-8 h-8 text-red-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Authentication Status */}
          <Card>
            <CardHeader>
              <CardTitle>Email Kimlik Doğrulama</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    <div>
                      <p className="font-medium">SPF</p>
                      <p className="text-sm text-gray-600">Sender Policy Framework</p>
                    </div>
                  </div>
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>

                <div className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    <div>
                      <p className="font-medium">DKIM</p>
                      <p className="text-sm text-gray-600">DomainKeys Identified Mail</p>
                    </div>
                  </div>
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>

                <div className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    <div>
                      <p className="font-medium">DMARC</p>
                      <p className="text-sm text-gray-600">Domain-based Message Auth</p>
                    </div>
                  </div>
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Deliverability Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Teslimat Metrikleri</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Teslimat Oranı</span>
                  <span className="font-semibold text-green-600">98.5%</span>
                </div>
                <div className="flex justify-between">
                  <span>Açılma Oranı</span>
                  <span className="font-semibold text-blue-600">24.3%</span>
                </div>
                <div className="flex justify-between">
                  <span>Tıklama Oranı</span>
                  <span className="font-semibold text-purple-600">3.8%</span>
                </div>
                <div className="flex justify-between">
                  <span>Bounce Oranı</span>
                  <span className="font-semibold text-orange-600">1.2%</span>
                </div>
                <div className="flex justify-between">
                  <span>Spam Oranı</span>
                  <span className="font-semibold text-red-600">0.3%</span>
                </div>
                <div className="flex justify-between">
                  <span>Unsubscribe Oranı</span>
                  <span className="font-semibold text-gray-600">0.8%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Spam Önleme Önerileri</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    <span>SPF kaydı aktif</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    <span>DKIM imzası aktif</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    <span>DMARC politikası aktif</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    <span>Dedicated IP kullanımı</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    <span>Liste hijyeni düzenli</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    <span>İçerik optimizasyonu</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Anti-Spam Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Spam Önleme Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Gönderim Hızı Limiti</label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="slow">Yavaş (10 email/dakika)</option>
                    <option value="medium" selected>Orta (50 email/dakika)</option>
                    <option value="fast">Hızlı (100 email/dakika)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Bounce Handling</label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="auto" selected>Otomatik</option>
                    <option value="manual">Manuel</option>
                    <option value="disabled">Devre Dışı</option>
                  </select>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Otomatik Liste Temizleme</p>
                    <p className="text-sm text-gray-500">Bounce olan adresleri otomatik kaldır</p>
                  </div>
                  <input type="checkbox" defaultChecked className="w-4 h-4" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Double Opt-in Zorunlu</p>
                    <p className="text-sm text-gray-500">Yeni aboneler için email doğrulaması</p>
                  </div>
                  <input type="checkbox" defaultChecked className="w-4 h-4" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Gerçek Zamanlı İzleme</p>
                    <p className="text-sm text-gray-500">Spam skorunu sürekli izle</p>
                  </div>
                  <input type="checkbox" defaultChecked className="w-4 h-4" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Spam Uyarıları</p>
                    <p className="text-sm text-gray-500">Yüksek spam oranında bildirim gönder</p>
                  </div>
                  <input type="checkbox" defaultChecked className="w-4 h-4" />
                </div>
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <Button variant="outline">Varsayılana Sıfırla</Button>
                <Button>
                  <Shield className="w-4 h-4 mr-2" />
                  Ayarları Kaydet
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      {showCountryModal && selectedCountry && (
        <CountryDetailModal
          country={selectedCountry}
          onClose={() => setShowCountryModal(false)}
        />
      )}

      {showCampaignModal && selectedCampaign && (
        <CampaignDetailModal
          campaign={selectedCampaign}
          onClose={() => setShowCampaignModal(false)}
        />
      )}

      {showEditCountryModal && selectedCountry && (
        <EditCountryModal
          country={selectedCountry}
          onClose={() => setShowEditCountryModal(false)}
          onSave={(updatedCountry) => {
            // Update country list
            setCountryLists(prev =>
              prev.map(c => c.id === updatedCountry.id ? updatedCountry : c)
            );
            setShowEditCountryModal(false);
          }}
        />
      )}

      {showNewCampaignModal && selectedCountry && (
        <NewCampaignModal
          targetCountry={selectedCountry}
          onClose={() => setShowNewCampaignModal(false)}
          onSave={(newCampaign) => {
            // Add new campaign
            setCampaigns(prev => [...prev, newCampaign]);
            setShowNewCampaignModal(false);
          }}
        />
      )}

      {showCountrySelectionModal && (
        <CountrySelectionModal
          countries={countryLists}
          onClose={() => setShowCountrySelectionModal(false)}
          onSelect={(country) => {
            setSelectedCountry(country);
            setShowNewCampaignModal(true);
          }}
        />
      )}
    </div>
  );
}
