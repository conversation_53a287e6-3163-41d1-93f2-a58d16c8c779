"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useQuote } from "@/contexts/quote-context"
import { 
  ClockIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  UserIcon,
  EnvelopeIcon,
  PlusIcon
} from "@heroicons/react/24/outline"

interface ProducerQuotePageProps {
  onNavigate?: (path: string) => void
}

export default function ProducerQuotePage({ onNavigate }: ProducerQuotePageProps) {
  const { 
    producerQuoteRequests, 
    createQuote,
    isLoading 
  } = useQuote()

  const [selectedRequestId, setSelectedRequestId] = React.useState<string | null>(null)
  const [quoteForm, setQuoteForm] = React.useState({
    unitPrice: '',
    deliveryTime: '',
    terms: '',
    notes: ''
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />
      case 'quoted':
        return <CurrencyDollarIcon className="h-5 w-5 text-blue-500" />
      case 'accepted':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'rejected':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'quoted':
        return 'Teklif Verildi'
      case 'accepted':
        return 'Kabul Edildi'
      case 'rejected':
        return 'Reddedildi'
      case 'completed':
        return 'Tamamlandı'
      default:
        return 'Bilinmiyor'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'quoted':
        return 'bg-blue-100 text-blue-800'
      case 'accepted':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleSubmitQuote = async (requestId: string) => {
    const request = producerQuoteRequests.find(r => r.id === requestId)
    if (!request) return

    try {
      // Calculate total for first product/spec (simplified)
      const firstProduct = request.products[0]
      const firstSpec = firstProduct.specifications[0]
      const quantity = parseFloat(firstSpec.area) || 100
      const unitPrice = parseFloat(quoteForm.unitPrice) || 0
      const totalPrice = quantity * unitPrice

      const quoteData = {
        quoteRequestId: requestId,
        producerId: "prod-1", // Mock producer ID
        producerName: "Demo Üretici",
        producerCompany: "Demo Taş Ltd.",
        producerEmail: "<EMAIL>",
        items: [{
          id: `item-${Date.now()}`,
          productId: firstProduct.productId,
          productName: firstProduct.productName,
          specificationId: firstSpec.id,
          quantity: quantity,
          unitPrice: unitPrice,
          totalPrice: totalPrice,
          currency: "USD",
          deliveryTime: quoteForm.deliveryTime,
          notes: quoteForm.notes
        }],
        totalAmount: totalPrice,
        currency: "USD",
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        terms: quoteForm.terms,
        status: 'pending' as const
      }

      await createQuote(quoteData)
      alert('Teklif başarıyla gönderildi!')
      
      // Reset form
      setQuoteForm({
        unitPrice: '',
        deliveryTime: '',
        terms: '',
        notes: ''
      })
      setSelectedRequestId(null)
      
    } catch (error) {
      console.error('Error creating quote:', error)
      alert('Teklif gönderilirken hata oluştu.')
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-stone-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Talepler yükleniyor...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gelen Talepler</h1>
          <p className="text-gray-600">Müşteri teklif taleplerini görüntüleyin ve teklif verin</p>
        </div>
        <div className="text-sm text-gray-500">
          Toplam {producerQuoteRequests.length} talep
        </div>
      </div>

      {/* Requests List */}
      {producerQuoteRequests.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz teklif talebi yok</h3>
            <p className="text-gray-600">
              Müşterilerden teklif talepleri geldiğinde burada görüntülenecektir.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {producerQuoteRequests.map((request) => {
            const isExpanded = selectedRequestId === request.id

            return (
              <Card key={request.id} className="overflow-hidden">
                <CardHeader 
                  className="cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => setSelectedRequestId(isExpanded ? null : request.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(request.status)}
                      <div>
                        <CardTitle className="text-lg">
                          Talep #{request.id.slice(-6).toUpperCase()}
                        </CardTitle>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <div className="flex items-center space-x-1">
                            <UserIcon className="h-4 w-4" />
                            <span>{request.customerName}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <CalendarIcon className="h-4 w-4" />
                            <span>{request.createdAt.toLocaleDateString('tr-TR')}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <span>{request.products.length} ürün</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Badge className={getStatusColor(request.status)}>
                        {getStatusText(request.status)}
                      </Badge>
                      <span className="text-gray-400">
                        {isExpanded ? '▼' : '▶'}
                      </span>
                    </div>
                  </div>
                </CardHeader>

                {isExpanded && (
                  <CardContent className="border-t bg-gray-50">
                    {/* Customer Info */}
                    <div className="mb-6">
                      <h4 className="font-medium text-gray-900 mb-2">Müşteri Bilgileri</h4>
                      <div className="bg-white p-4 rounded-lg border">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="flex items-center space-x-2">
                            <UserIcon className="h-4 w-4 text-gray-400" />
                            <span className="font-medium">{request.customerName}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <EnvelopeIcon className="h-4 w-4 text-gray-400" />
                            <span>{request.customerEmail}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Request Details */}
                    <div className="space-y-4 mb-6">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Talep Edilen Ürünler</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {request.products.map((product) => (
                            <div key={product.id} className="bg-white p-4 rounded-lg border">
                              <div className="flex items-center space-x-3 mb-3">
                                <img
                                  src={product.productImage}
                                  alt={product.productName}
                                  className="w-12 h-12 object-cover rounded"
                                />
                                <div>
                                  <h5 className="font-medium">{product.productName}</h5>
                                  <p className="text-sm text-gray-600">{product.productCategory}</p>
                                </div>
                              </div>
                              <div className="space-y-2">
                                {product.specifications.map((spec) => (
                                  <div key={spec.id} className="text-sm">
                                    <div className="grid grid-cols-2 gap-2">
                                      <span className="text-gray-600">Kalınlık:</span>
                                      <span>{spec.thickness} cm</span>
                                      {spec.type === 'sized' && (
                                        <>
                                          <span className="text-gray-600">Ebat:</span>
                                          <span>{spec.width}x{spec.length} cm</span>
                                        </>
                                      )}
                                      <span className="text-gray-600">Metraj:</span>
                                      <span className="font-medium">{spec.area} m²</span>
                                      <span className="text-gray-600">Yüzey:</span>
                                      <span>{spec.surface}</span>
                                      <span className="text-gray-600">Ambalaj:</span>
                                      <span>{spec.packaging}</span>
                                      <span className="text-gray-600">Teslimat:</span>
                                      <span>{spec.delivery}</span>
                                      {spec.targetPrice && (
                                        <>
                                          <span className="text-gray-600">Hedef Fiyat:</span>
                                          <span className="text-blue-600 font-medium">
                                            {spec.targetPrice} {spec.currency}
                                          </span>
                                        </>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {request.message && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Müşteri Notları</h4>
                          <p className="text-gray-700 bg-white p-3 rounded border">
                            {request.message}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Quote Form */}
                    {request.status === 'pending' && (
                      <div className="bg-white p-6 rounded-lg border">
                        <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                          <PlusIcon className="h-5 w-5 mr-2" />
                          Teklif Ver
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Birim Fiyat (USD/m²)
                            </label>
                            <input
                              type="number"
                              step="0.01"
                              value={quoteForm.unitPrice}
                              onChange={(e) => setQuoteForm(prev => ({ ...prev, unitPrice: e.target.value }))}
                              placeholder="Örn: 25.50"
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Teslimat Süresi
                            </label>
                            <input
                              type="text"
                              value={quoteForm.deliveryTime}
                              onChange={(e) => setQuoteForm(prev => ({ ...prev, deliveryTime: e.target.value }))}
                              placeholder="Örn: 15 gün"
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                            />
                          </div>
                        </div>
                        <div className="mb-4">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Ödeme Şartları
                          </label>
                          <input
                            type="text"
                            value={quoteForm.terms}
                            onChange={(e) => setQuoteForm(prev => ({ ...prev, terms: e.target.value }))}
                            placeholder="Örn: %50 peşin, %50 sevkiyatta"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                          />
                        </div>
                        <div className="mb-4">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Ek Notlar
                          </label>
                          <textarea
                            value={quoteForm.notes}
                            onChange={(e) => setQuoteForm(prev => ({ ...prev, notes: e.target.value }))}
                            rows={3}
                            placeholder="Kalite, özellikler, garanti vb. hakkında notlar..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                          />
                        </div>
                        <Button
                          onClick={() => handleSubmitQuote(request.id)}
                          disabled={!quoteForm.unitPrice || !quoteForm.deliveryTime}
                          className="w-full"
                        >
                          Teklif Gönder
                        </Button>
                      </div>
                    )}

                    {request.status === 'quoted' && (
                      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <p className="text-blue-800 text-center">
                          Bu talep için teklif verildi. Müşterinin yanıtı bekleniyor.
                        </p>
                      </div>
                    )}
                  </CardContent>
                )}
              </Card>
            )
          })}
        </div>
      )}
    </div>
  )
}
