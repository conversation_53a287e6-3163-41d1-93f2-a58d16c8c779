// RFC-501: Log Management Service
import fs from 'fs/promises';
import path from 'path';
import { createReadStream } from 'fs';
import { createInterface } from 'readline';

export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'debug' | 'info' | 'warning' | 'error';
  source: string;
  message: string;
  details?: any;
  userId?: string;
  ip?: string;
  userAgent?: string;
}

export interface LogFilter {
  level?: string;
  source?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface LogStats {
  totalLogs: number;
  errorCount: number;
  warningCount: number;
  infoCount: number;
  debugCount: number;
  sources: { [key: string]: number };
  hourlyDistribution: { [hour: string]: number };
}

export class LogManagementService {
  private logDirectory: string;
  private maxLogFileSize: number = 10 * 1024 * 1024; // 10MB
  private maxLogFiles: number = 10;

  constructor() {
    this.logDirectory = process.env.LOG_DIRECTORY || path.join(process.cwd(), 'logs');
    this.ensureLogDirectory();
  }

  /**
   * Ensure log directory exists
   */
  private async ensureLogDirectory(): Promise<void> {
    try {
      await fs.access(this.logDirectory);
    } catch (error) {
      await fs.mkdir(this.logDirectory, { recursive: true });
    }
  }

  /**
   * Write log entry to file
   */
  async writeLog(entry: Omit<LogEntry, 'id' | 'timestamp'>): Promise<void> {
    const logEntry: LogEntry = {
      id: this.generateLogId(),
      timestamp: new Date().toISOString(),
      ...entry
    };

    const logLine = JSON.stringify(logEntry) + '\n';
    const logFile = this.getCurrentLogFile();

    try {
      await fs.appendFile(logFile, logLine, 'utf8');
      
      // Check if log rotation is needed
      await this.checkLogRotation(logFile);
    } catch (error) {
      console.error('Failed to write log:', error);
    }
  }

  /**
   * Get logs with filtering
   */
  async getLogs(filter: LogFilter = {}): Promise<{ logs: LogEntry[]; total: number }> {
    const {
      level,
      source,
      startDate,
      endDate,
      search,
      limit = 100,
      offset = 0
    } = filter;

    const logs: LogEntry[] = [];
    const logFiles = await this.getLogFiles();

    for (const logFile of logFiles) {
      const fileLogs = await this.readLogFile(logFile);
      logs.push(...fileLogs);
    }

    // Filter logs
    let filteredLogs = logs;

    if (level && level !== 'all') {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }

    if (source && source !== 'all') {
      filteredLogs = filteredLogs.filter(log => log.source === source);
    }

    if (startDate) {
      const start = new Date(startDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= start);
    }

    if (endDate) {
      const end = new Date(endDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= end);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredLogs = filteredLogs.filter(log =>
        log.message.toLowerCase().includes(searchLower) ||
        log.source.toLowerCase().includes(searchLower) ||
        (log.details && JSON.stringify(log.details).toLowerCase().includes(searchLower))
      );
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    const total = filteredLogs.length;
    const paginatedLogs = filteredLogs.slice(offset, offset + limit);

    return { logs: paginatedLogs, total };
  }

  /**
   * Get log statistics
   */
  async getLogStats(filter: LogFilter = {}): Promise<LogStats> {
    const { logs } = await this.getLogs({ ...filter, limit: 10000 }); // Get more logs for stats

    const stats: LogStats = {
      totalLogs: logs.length,
      errorCount: 0,
      warningCount: 0,
      infoCount: 0,
      debugCount: 0,
      sources: {},
      hourlyDistribution: {}
    };

    logs.forEach(log => {
      // Count by level
      switch (log.level) {
        case 'error':
          stats.errorCount++;
          break;
        case 'warning':
          stats.warningCount++;
          break;
        case 'info':
          stats.infoCount++;
          break;
        case 'debug':
          stats.debugCount++;
          break;
      }

      // Count by source
      stats.sources[log.source] = (stats.sources[log.source] || 0) + 1;

      // Count by hour
      const hour = new Date(log.timestamp).getHours().toString().padStart(2, '0');
      stats.hourlyDistribution[hour] = (stats.hourlyDistribution[hour] || 0) + 1;
    });

    return stats;
  }

  /**
   * Clear old logs
   */
  async clearLogs(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const logFiles = await this.getLogFiles();
    let clearedCount = 0;

    for (const logFile of logFiles) {
      const stats = await fs.stat(logFile);
      if (stats.mtime < cutoffDate) {
        await fs.unlink(logFile);
        clearedCount++;
      }
    }

    return clearedCount;
  }

  /**
   * Export logs to file
   */
  async exportLogs(filter: LogFilter = {}, format: 'json' | 'csv' | 'txt' = 'json'): Promise<string> {
    const { logs } = await this.getLogs({ ...filter, limit: 10000 });
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `logs-export-${timestamp}.${format}`;
    const filepath = path.join(this.logDirectory, filename);

    let content: string;

    switch (format) {
      case 'json':
        content = JSON.stringify(logs, null, 2);
        break;
      case 'csv':
        content = this.logsToCSV(logs);
        break;
      case 'txt':
        content = logs.map(log => 
          `[${log.timestamp}] [${log.level.toUpperCase()}] [${log.source}] ${log.message}`
        ).join('\n');
        break;
      default:
        throw new Error('Unsupported export format');
    }

    await fs.writeFile(filepath, content, 'utf8');
    return filepath;
  }

  /**
   * Get unique sources from logs
   */
  async getLogSources(): Promise<string[]> {
    const { logs } = await this.getLogs({ limit: 1000 });
    const sources = new Set(logs.map(log => log.source));
    return Array.from(sources).sort();
  }

  /**
   * Private helper methods
   */
  private generateLogId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private getCurrentLogFile(): string {
    const date = new Date().toISOString().split('T')[0];
    return path.join(this.logDirectory, `app-${date}.log`);
  }

  private async getLogFiles(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.logDirectory);
      const logFiles = files
        .filter(file => file.endsWith('.log'))
        .map(file => path.join(this.logDirectory, file))
        .sort((a, b) => b.localeCompare(a)); // Newest first

      return logFiles;
    } catch (error) {
      return [];
    }
  }

  private async readLogFile(filepath: string): Promise<LogEntry[]> {
    const logs: LogEntry[] = [];

    try {
      const fileStream = createReadStream(filepath);
      const rl = createInterface({
        input: fileStream,
        crlfDelay: Infinity
      });

      for await (const line of rl) {
        if (line.trim()) {
          try {
            const logEntry = JSON.parse(line);
            logs.push(logEntry);
          } catch (error) {
            // Skip invalid JSON lines
          }
        }
      }
    } catch (error) {
      console.error(`Error reading log file ${filepath}:`, error);
    }

    return logs;
  }

  private async checkLogRotation(logFile: string): Promise<void> {
    try {
      const stats = await fs.stat(logFile);
      if (stats.size > this.maxLogFileSize) {
        await this.rotateLogFile(logFile);
      }
    } catch (error) {
      // File doesn't exist yet, no rotation needed
    }
  }

  private async rotateLogFile(logFile: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const rotatedFile = logFile.replace('.log', `-${timestamp}.log`);
    
    await fs.rename(logFile, rotatedFile);
    
    // Clean up old log files
    await this.cleanupOldLogFiles();
  }

  private async cleanupOldLogFiles(): Promise<void> {
    const logFiles = await this.getLogFiles();
    
    if (logFiles.length > this.maxLogFiles) {
      const filesToDelete = logFiles.slice(this.maxLogFiles);
      
      for (const file of filesToDelete) {
        try {
          await fs.unlink(file);
        } catch (error) {
          console.error(`Failed to delete old log file ${file}:`, error);
        }
      }
    }
  }

  private logsToCSV(logs: LogEntry[]): string {
    const headers = ['timestamp', 'level', 'source', 'message', 'details'];
    const csvLines = [headers.join(',')];

    logs.forEach(log => {
      const row = [
        log.timestamp,
        log.level,
        log.source,
        `"${log.message.replace(/"/g, '""')}"`,
        log.details ? `"${JSON.stringify(log.details).replace(/"/g, '""')}"` : ''
      ];
      csvLines.push(row.join(','));
    });

    return csvLines.join('\n');
  }
}
