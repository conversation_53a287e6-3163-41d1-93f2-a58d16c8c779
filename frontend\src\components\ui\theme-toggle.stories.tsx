import type { <PERSON>a, StoryObj } from '@storybook/react'
import { ThemeProvider } from './theme-provider'
import { ThemeToggle } from './theme-toggle'

/**
 * ThemeToggle component for switching between light, dark, and system themes
 * 
 * The ThemeToggle component provides an intuitive way for users to switch
 * between different theme modes in the Turkish Natural Stone Marketplace.
 */
const meta = {
  title: 'UI/ThemeToggle',
  component: ThemeToggle,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
The ThemeToggle component allows users to switch between light, dark, and system themes.
It integrates with the ThemeProvider to maintain theme state across the application.

## Features
- Three variants: button, switch, dropdown
- Three sizes: sm, md, lg
- Optional label display
- Smooth animations
- System theme detection
- Persistent theme storage
        `,
      },
    },
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <ThemeProvider defaultTheme="light">
        <Story />
      </ThemeProvider>
    ),
  ],
  argTypes: {
    variant: {
      control: 'select',
      options: ['button', 'switch', 'dropdown'],
      description: 'Visual style variant of the theme toggle',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the theme toggle',
    },
    showLabel: {
      control: 'boolean',
      description: 'Whether to show the theme label',
    },
  },
  args: {
    variant: 'button',
    size: 'md',
    showLabel: false,
  },
} satisfies Meta<typeof ThemeToggle>

export default meta
type Story = StoryObj<typeof meta>

/**
 * Default theme toggle button
 */
export const Default: Story = {}

/**
 * All variants of the theme toggle
 */
export const Variants: Story = {
  render: () => (
    <div className="flex flex-col gap-6">
      <div className="space-y-2">
        <h3 className="font-semibold">Button Variant</h3>
        <ThemeToggle variant="button" />
      </div>
      
      <div className="space-y-2">
        <h3 className="font-semibold">Switch Variant</h3>
        <ThemeToggle variant="switch" />
      </div>
      
      <div className="space-y-2">
        <h3 className="font-semibold">Dropdown Variant</h3>
        <ThemeToggle variant="dropdown" />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Different visual styles available for the theme toggle.',
      },
    },
  },
}

/**
 * Different sizes for each variant
 */
export const Sizes: Story = {
  render: () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="font-semibold">Button Sizes</h3>
        <div className="flex items-center gap-4">
          <ThemeToggle variant="button" size="sm" />
          <ThemeToggle variant="button" size="md" />
          <ThemeToggle variant="button" size="lg" />
        </div>
      </div>
      
      <div className="space-y-2">
        <h3 className="font-semibold">Switch Sizes</h3>
        <div className="flex items-center gap-4">
          <ThemeToggle variant="switch" size="sm" />
          <ThemeToggle variant="switch" size="md" />
          <ThemeToggle variant="switch" size="lg" />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Different sizes available for theme toggle variants.',
      },
    },
  },
}

/**
 * Theme toggle with labels
 */
export const WithLabels: Story = {
  render: () => (
    <div className="space-y-4">
      <ThemeToggle variant="button" showLabel />
      <ThemeToggle variant="switch" showLabel />
      <ThemeToggle variant="dropdown" showLabel />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Theme toggles with visible labels showing current theme.',
      },
    },
  },
}

/**
 * Navigation bar integration
 */
export const NavigationIntegration: Story = {
  render: () => (
    <div className="bg-white border-b border-gray-200 p-4">
      <div className="flex items-center justify-between max-w-6xl mx-auto">
        <div className="flex items-center gap-2">
          <span className="text-2xl font-bold text-[var(--primary-stone)]">
            🏛️ Doğal Taş Pazarı
          </span>
        </div>
        
        <div className="flex items-center gap-4">
          <nav className="hidden md:flex items-center gap-6">
            <a href="#" className="text-gray-700 hover:text-[var(--primary-stone)]">Ana Sayfa</a>
            <a href="#" className="text-gray-700 hover:text-[var(--primary-stone)]">Ürünler</a>
            <a href="#" className="text-gray-700 hover:text-[var(--primary-stone)]">Hakkımızda</a>
          </nav>
          
          <ThemeToggle variant="button" />
          
          <button className="bg-[var(--primary-stone)] text-white px-4 py-2 rounded-md">
            Giriş Yap
          </button>
        </div>
      </div>
    </div>
  ),
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'Theme toggle integrated into a navigation bar.',
      },
    },
  },
}

/**
 * Settings panel integration
 */
export const SettingsPanel: Story = {
  render: () => (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-md">
      <h2 className="text-lg font-semibold mb-4">Ayarlar</h2>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <label className="font-medium">Tema</label>
            <p className="text-sm text-gray-600">Görünüm tercihini seç</p>
          </div>
          <ThemeToggle variant="switch" showLabel />
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <label className="font-medium">Bildirimler</label>
            <p className="text-sm text-gray-600">Email bildirimleri</p>
          </div>
          <input type="checkbox" className="rounded" />
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <label className="font-medium">Dil</label>
            <p className="text-sm text-gray-600">Arayüz dili</p>
          </div>
          <select className="border border-gray-300 rounded px-2 py-1">
            <option>Türkçe</option>
            <option>English</option>
          </select>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Theme toggle integrated into a settings panel.',
      },
    },
  },
}

/**
 * Mobile responsive behavior
 */
export const MobileResponsive: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="block sm:hidden">
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <span className="font-bold">🏛️ Doğal Taş</span>
            <ThemeToggle variant="button" size="sm" />
          </div>
        </div>
      </div>
      
      <div className="hidden sm:block">
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <span className="text-xl font-bold">🏛️ Türkiye Doğal Taş Pazarı</span>
            <ThemeToggle variant="button" showLabel />
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Responsive behavior on different screen sizes.',
      },
    },
  },
}

/**
 * Accessibility demonstration
 */
export const Accessibility: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <h3 className="font-semibold">Keyboard Navigation</h3>
        <p className="text-sm text-gray-600">
          Use Tab to focus and Enter/Space to toggle
        </p>
        <ThemeToggle variant="button" showLabel />
      </div>
      
      <div className="space-y-2">
        <h3 className="font-semibold">Screen Reader Support</h3>
        <p className="text-sm text-gray-600">
          Announces current theme and provides clear labels
        </p>
        <ThemeToggle variant="switch" showLabel />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Accessibility features including keyboard navigation and screen reader support.',
      },
    },
  },
}
