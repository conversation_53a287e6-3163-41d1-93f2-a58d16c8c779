// RFC-501: Admin Dashboard Service Implementation
import { PrismaClient } from '@prisma/client';
import { createClient, RedisClientType } from 'redis';

export interface KPICard {
  id: string;
  title: string;
  value: number | string;
  previousValue?: number | string;
  changePercentage?: number;
  trend: 'up' | 'down' | 'stable';
  format: 'number' | 'currency' | 'percentage' | 'duration';
  color: 'green' | 'red' | 'blue' | 'yellow';
  icon: string;
  drillDownUrl?: string;
}

export interface DashboardOverview {
  kpiCards: KPICard[];
  realtimeMetrics: RealtimeMetric[];
  alertsPanel: AlertsPanel;
  quickActions: QuickAction[];
  systemHealth: SystemHealth;
}

export interface RealtimeMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  trend: number[];
}

export interface AlertsPanel {
  criticalAlerts: Alert[];
  warningAlerts: Alert[];
  infoAlerts: Alert[];
  totalCount: number;
}

export interface Alert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  actionUrl?: string;
}

export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  url: string;
  badge?: number;
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  responseTime: number;
  errorRate: number;
  activeUsers: number;
  databaseStatus: 'connected' | 'disconnected' | 'slow';
  redisStatus: 'connected' | 'disconnected' | 'slow';
}

export interface UserAnalytics {
  totalUsers: number;
  activeUsers: number;
  newRegistrations: number;
  usersByType: { [key: string]: number };
  usersByStatus: { [key: string]: number };
  usersByCountry: { [key: string]: number };
  registrationTrend: { date: string; count: number }[];
  engagementMetrics: EngagementMetrics;
}

export interface EngagementMetrics {
  averageSessionDuration: number;
  pageViewsPerSession: number;
  bounceRate: number;
  returnUserRate: number;
}

export interface OrderAnalytics {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  ordersByStatus: { [key: string]: number };
  revenueByPeriod: { date: string; revenue: number }[];
  topProducts: { productId: string; name: string; orders: number; revenue: number }[];
}

export interface PaymentAnalytics {
  totalRevenue: number;
  pendingPayments: number;
  escrowBalance: number;
  commissionEarned: number;
  refundsProcessed: number;
  paymentsByMethod: { [key: string]: number };
  revenueByRegion: { [key: string]: number };
  commissionTrend: { date: string; commission: number }[];
}

export class AdminDashboardService {
  private prisma: PrismaClient;
  private redis: RedisClientType;

  constructor() {
    this.prisma = new PrismaClient();
    this.redis = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    this.redis.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });
  }

  async connect(): Promise<void> {
    if (!this.redis.isOpen) {
      await this.redis.connect();
    }
  }

  async disconnect(): Promise<void> {
    if (this.redis.isOpen) {
      await this.redis.disconnect();
    }
    await this.prisma.$disconnect();
  }

  // Get comprehensive dashboard overview
  async getDashboardOverview(): Promise<DashboardOverview> {
    await this.connect();
    
    // Check cache first
    const cached = await this.redis.get('dashboard_overview');
    if (cached) {
      return JSON.parse(cached);
    }

    const [kpiCards, realtimeMetrics, alertsPanel, quickActions, systemHealth] = await Promise.all([
      this.getKPICards(),
      this.getRealtimeMetrics(),
      this.getAlertsPanel(),
      this.getQuickActions(),
      this.getSystemHealth()
    ]);

    const overview: DashboardOverview = {
      kpiCards,
      realtimeMetrics,
      alertsPanel,
      quickActions,
      systemHealth
    };

    // Cache for 2 minutes
    await this.redis.setEx('dashboard_overview', 120, JSON.stringify(overview));

    return overview;
  }

  // Get KPI cards with current and previous values
  async getKPICards(): Promise<KPICard[]> {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfPreviousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfPreviousMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    const [
      totalUsers,
      previousTotalUsers,
      activeUsers,
      totalOrders,
      previousTotalOrders,
      totalRevenue,
      previousTotalRevenue,
      pendingApprovals,
      systemUptime
    ] = await Promise.all([
      this.prisma.user.count(),
      this.prisma.user.count({
        where: { createdAt: { lt: startOfMonth } }
      }),
      this.prisma.user.count({
        where: { 
          lastLoginAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }
      }),
      this.prisma.order.count(),
      this.prisma.order.count({
        where: { createdAt: { lt: startOfMonth } }
      }),
      this.prisma.payment.aggregate({
        _sum: { amount: true },
        where: { status: 'COMPLETED' }
      }),
      this.prisma.payment.aggregate({
        _sum: { amount: true },
        where: { 
          status: 'COMPLETED',
          createdAt: { 
            gte: startOfPreviousMonth,
            lte: endOfPreviousMonth
          }
        }
      }),
      this.prisma.user.count({
        where: { 
          userType: 'producer',
          status: 'PENDING'
        }
      }),
      this.getSystemUptime()
    ]);

    const currentRevenue = Number(totalRevenue._sum.amount || 0);
    const prevRevenue = Number(previousTotalRevenue._sum.amount || 0);
    const revenueChange = prevRevenue > 0 ? ((currentRevenue - prevRevenue) / prevRevenue) * 100 : 0;

    const newUsers = totalUsers - previousTotalUsers;
    const userGrowth = previousTotalUsers > 0 ? ((newUsers) / previousTotalUsers) * 100 : 0;

    const newOrders = totalOrders - previousTotalOrders;
    const orderGrowth = previousTotalOrders > 0 ? ((newOrders) / previousTotalOrders) * 100 : 0;

    return [
      {
        id: 'total_users',
        title: 'Total Users',
        value: totalUsers,
        previousValue: previousTotalUsers,
        changePercentage: userGrowth,
        trend: userGrowth > 0 ? 'up' : userGrowth < 0 ? 'down' : 'stable',
        format: 'number',
        color: 'blue',
        icon: 'Users',
        drillDownUrl: '/admin/users'
      },
      {
        id: 'active_users',
        title: 'Active Users (30d)',
        value: activeUsers,
        format: 'number',
        color: 'green',
        icon: 'UserCheck',
        trend: 'stable'
      },
      {
        id: 'total_orders',
        title: 'Total Orders',
        value: totalOrders,
        previousValue: previousTotalOrders,
        changePercentage: orderGrowth,
        trend: orderGrowth > 0 ? 'up' : orderGrowth < 0 ? 'down' : 'stable',
        format: 'number',
        color: 'blue',
        icon: 'ShoppingCart',
        drillDownUrl: '/admin/orders'
      },
      {
        id: 'total_revenue',
        title: 'Total Revenue',
        value: currentRevenue,
        previousValue: prevRevenue,
        changePercentage: revenueChange,
        trend: revenueChange > 0 ? 'up' : revenueChange < 0 ? 'down' : 'stable',
        format: 'currency',
        color: 'green',
        icon: 'DollarSign',
        drillDownUrl: '/admin/payments'
      },
      {
        id: 'pending_approvals',
        title: 'Pending Approvals',
        value: pendingApprovals,
        format: 'number',
        color: pendingApprovals > 10 ? 'red' : pendingApprovals > 5 ? 'yellow' : 'green',
        icon: 'Clock',
        trend: 'stable',
        drillDownUrl: '/admin/approvals'
      },
      {
        id: 'system_uptime',
        title: 'System Uptime',
        value: `${systemUptime}%`,
        format: 'percentage',
        color: systemUptime > 99 ? 'green' : systemUptime > 95 ? 'yellow' : 'red',
        icon: 'Activity',
        trend: 'stable'
      }
    ];
  }

  // Get real-time metrics
  async getRealtimeMetrics(): Promise<RealtimeMetric[]> {
    const now = new Date();

    // Get current active users from Redis (would be updated by user activity)
    const activeUsers = await this.redis.get('active_users_count') || '0';

    // Get recent response times (would be collected by middleware)
    const avgResponseTime = await this.redis.get('avg_response_time') || '150';

    // Get current error rate
    const errorRate = await this.redis.get('error_rate') || '0.1';

    // Get database connections
    const dbConnections = await this.redis.get('db_connections') || '5';

    return [
      {
        id: 'active_users',
        name: 'Active Users',
        value: parseInt(activeUsers),
        unit: 'users',
        timestamp: now,
        trend: [45, 52, 48, 61, 55, 67, parseInt(activeUsers)]
      },
      {
        id: 'response_time',
        name: 'Avg Response Time',
        value: parseInt(avgResponseTime),
        unit: 'ms',
        timestamp: now,
        trend: [120, 135, 142, 128, 156, 148, parseInt(avgResponseTime)]
      },
      {
        id: 'error_rate',
        name: 'Error Rate',
        value: parseFloat(errorRate),
        unit: '%',
        timestamp: now,
        trend: [0.2, 0.1, 0.3, 0.1, 0.2, 0.1, parseFloat(errorRate)]
      },
      {
        id: 'db_connections',
        name: 'DB Connections',
        value: parseInt(dbConnections),
        unit: 'connections',
        timestamp: now,
        trend: [3, 4, 5, 6, 4, 5, parseInt(dbConnections)]
      }
    ];
  }

  // Get alerts panel
  async getAlertsPanel(): Promise<AlertsPanel> {
    // Get recent system alerts
    const criticalAlerts: Alert[] = [];
    const warningAlerts: Alert[] = [];
    const infoAlerts: Alert[] = [];

    // Check for critical conditions
    const pendingApprovals = await this.prisma.user.count({
      where: { userType: 'producer', status: 'PENDING' }
    });

    if (pendingApprovals > 10) {
      criticalAlerts.push({
        id: 'pending_approvals_high',
        type: 'critical',
        title: 'High Number of Pending Approvals',
        message: `${pendingApprovals} producer applications are waiting for approval`,
        timestamp: new Date(),
        acknowledged: false,
        actionUrl: '/admin/approvals'
      });
    }

    // Check for failed payments
    const failedPayments = await this.prisma.payment.count({
      where: {
        status: 'FAILED',
        createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }
    });

    if (failedPayments > 0) {
      warningAlerts.push({
        id: 'failed_payments',
        type: 'warning',
        title: 'Failed Payments Detected',
        message: `${failedPayments} payments failed in the last 24 hours`,
        timestamp: new Date(),
        acknowledged: false,
        actionUrl: '/admin/payments'
      });
    }

    // Check for new registrations
    const newRegistrations = await this.prisma.user.count({
      where: {
        createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }
    });

    if (newRegistrations > 0) {
      infoAlerts.push({
        id: 'new_registrations',
        type: 'info',
        title: 'New User Registrations',
        message: `${newRegistrations} new users registered in the last 24 hours`,
        timestamp: new Date(),
        acknowledged: false,
        actionUrl: '/admin/users'
      });
    }

    return {
      criticalAlerts,
      warningAlerts,
      infoAlerts,
      totalCount: criticalAlerts.length + warningAlerts.length + infoAlerts.length
    };
  }

  // Get quick actions
  async getQuickActions(): Promise<QuickAction[]> {
    const [pendingApprovals, pendingPayments, activeDisputes, systemIssues] = await Promise.all([
      this.prisma.user.count({
        where: { userType: 'producer', status: 'PENDING' }
      }),
      this.prisma.payment.count({
        where: { status: 'PENDING' }
      }),
      this.prisma.dispute.count({
        where: { status: 'ACTIVE' }
      }),
      0 // Would check system issues
    ]);

    return [
      {
        id: 'approve_producers',
        title: 'Approve Producers',
        description: 'Review and approve pending producer applications',
        icon: 'UserCheck',
        url: '/admin/approvals',
        badge: pendingApprovals
      },
      {
        id: 'process_payments',
        title: 'Process Payments',
        description: 'Review and process pending payments',
        icon: 'CreditCard',
        url: '/admin/payments',
        badge: pendingPayments
      },
      {
        id: 'resolve_disputes',
        title: 'Resolve Disputes',
        description: 'Handle active customer disputes',
        icon: 'AlertTriangle',
        url: '/admin/disputes',
        badge: activeDisputes
      },
      {
        id: 'system_maintenance',
        title: 'System Maintenance',
        description: 'Perform system maintenance tasks',
        icon: 'Settings',
        url: '/admin/system',
        badge: systemIssues
      },
      {
        id: 'generate_reports',
        title: 'Generate Reports',
        description: 'Create business and financial reports',
        icon: 'FileText',
        url: '/admin/reports'
      },
      {
        id: 'broadcast_message',
        title: 'Broadcast Message',
        description: 'Send announcements to all users',
        icon: 'MessageSquare',
        url: '/admin/communications'
      }
    ];
  }

  // Get system health
  async getSystemHealth(): Promise<SystemHealth> {
    const uptime = await this.getSystemUptime();

    // Test database connection
    let databaseStatus: 'connected' | 'disconnected' | 'slow' = 'connected';
    let responseTime = 0;

    try {
      const start = Date.now();
      await this.prisma.$queryRaw`SELECT 1`;
      responseTime = Date.now() - start;

      if (responseTime > 1000) {
        databaseStatus = 'slow';
      }
    } catch (error) {
      databaseStatus = 'disconnected';
      responseTime = -1;
    }

    // Test Redis connection
    let redisStatus: 'connected' | 'disconnected' | 'slow' = 'connected';
    try {
      const start = Date.now();
      await this.redis.ping();
      const redisResponseTime = Date.now() - start;

      if (redisResponseTime > 500) {
        redisStatus = 'slow';
      }
    } catch (error) {
      redisStatus = 'disconnected';
    }

    // Get active users count
    const activeUsers = parseInt(await this.redis.get('active_users_count') || '0');

    // Calculate error rate
    const errorRate = parseFloat(await this.redis.get('error_rate') || '0');

    // Determine overall status
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (databaseStatus === 'disconnected' || redisStatus === 'disconnected' || uptime < 95) {
      status = 'critical';
    } else if (databaseStatus === 'slow' || redisStatus === 'slow' || errorRate > 1 || uptime < 99) {
      status = 'warning';
    }

    return {
      status,
      uptime,
      responseTime,
      errorRate,
      activeUsers,
      databaseStatus,
      redisStatus
    };
  }

  // Get system uptime percentage
  private async getSystemUptime(): Promise<number> {
    // This would typically be calculated from monitoring data
    // For now, return a mock value
    return 99.8;
  }
}
