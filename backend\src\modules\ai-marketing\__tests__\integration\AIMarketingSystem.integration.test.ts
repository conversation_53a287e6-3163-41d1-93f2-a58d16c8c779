// AI Marketing System Integration Tests
// End-to-end integration tests for the complete AI marketing system

import { AIMarketingOrchestrator } from '../../orchestrator/AIMarketingOrchestrator';
import { DatabaseManager } from '../../database/DatabaseManager';
import { APIIntegrationManager } from '../../integrations/APIIntegrationManager';
import { MarketingTask } from '../../types/ai-marketing.types';

// Test database configuration
const testDbConfig = {
  host: process.env.TEST_DB_HOST || 'localhost',
  port: parseInt(process.env.TEST_DB_PORT || '5432'),
  database: process.env.TEST_DB_NAME || 'marketplace_test',
  username: process.env.TEST_DB_USER || 'postgres',
  password: process.env.TEST_DB_PASSWORD || 'password',
  ssl: false
};

// Mock API configurations
const mockApiConfig = {
  tradeMap: {
    apiKey: 'test-trademap-key',
    baseUrl: 'https://api.trademap.test',
    timeout: 5000,
    retryAttempts: 1
  },
  linkedIn: {
    clientId: 'test-linkedin-client',
    clientSecret: 'test-linkedin-secret',
    accessToken: 'test-linkedin-token',
    baseUrl: 'https://api.linkedin.test',
    timeout: 5000
  },
  googleAds: {
    clientId: 'test-google-client',
    clientSecret: 'test-google-secret',
    refreshToken: 'test-google-refresh',
    developerToken: 'test-google-dev',
    customerId: 'test-customer-id'
  }
};

describe('AI Marketing System Integration', () => {
  let database: DatabaseManager;
  let apiManager: APIIntegrationManager;
  let orchestrator: AIMarketingOrchestrator;

  beforeAll(async () => {
    // Initialize test database
    database = new DatabaseManager(testDbConfig);
    await database.initialize();

    // Clean test data
    await cleanTestData();
  });

  afterAll(async () => {
    // Cleanup
    if (orchestrator) {
      await orchestrator.cleanup();
    }
    if (apiManager) {
      await apiManager.cleanup();
    }
    if (database) {
      await database.cleanup();
    }
  });

  beforeEach(async () => {
    // Clean test data before each test
    await cleanTestData();
  });

  describe('System Initialization', () => {
    test('should initialize all components successfully', async () => {
      // Initialize API manager (with mocked APIs)
      apiManager = new APIIntegrationManager(mockApiConfig);
      
      // Mock API health checks to return true
      jest.spyOn(apiManager, 'performHealthCheck').mockResolvedValue({
        tradeMap: true,
        linkedIn: true,
        googleAds: true,
        overall: true,
        lastChecked: new Date()
      });

      await apiManager.initialize();

      // Initialize orchestrator
      orchestrator = new AIMarketingOrchestrator();
      await orchestrator.startMarketingCycle();

      // Verify initialization
      expect(database.isHealthy).toBeDefined();
      expect(orchestrator.isRunning).toBe(true);
    }, 30000);

    test('should handle database connection failures gracefully', async () => {
      const badDbConfig = { ...testDbConfig, port: 9999 }; // Invalid port
      const badDatabase = new DatabaseManager(badDbConfig);

      await expect(badDatabase.initialize()).rejects.toThrow();
    });
  });

  describe('Learning System Integration', () => {
    beforeEach(async () => {
      orchestrator = new AIMarketingOrchestrator();
      await orchestrator.startMarketingCycle();
    });

    test('should process learning tasks end-to-end', async () => {
      const learningTask: MarketingTask = {
        id: 'integration-learning-1',
        type: 'learning',
        priority: 'high',
        aiModel: 'learning',
        data: {
          action: 'analyze_performance',
          performanceData: {
            email: {
              openRate: 28,
              clickRate: 4.2,
              conversionRate: 1.8
            },
            social: {
              engagementRate: 3.5,
              reachGrowth: 15
            }
          }
        },
        requiresApproval: false,
        createdAt: new Date()
      };

      // Add task to orchestrator
      orchestrator.addTask(learningTask);

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Verify learning patterns were saved to database
      const patterns = await database.getLearningPatterns('email-marketing', 10);
      expect(patterns.length).toBeGreaterThanOrEqual(0);

      // Verify system stats include learning data
      const stats = await orchestrator.getSystemStats();
      expect(stats.adaptiveLearning).toBeDefined();
    }, 15000);

    test('should persist learning data across system restarts', async () => {
      // Create initial learning data
      const patternId = await database.saveLearningPattern({
        patternData: { type: 'email', timing: 'morning', success: true },
        confidence: 0.85,
        successRate: 0.9,
        context: 'email-marketing'
      });

      // Stop and restart orchestrator
      await orchestrator.cleanup();
      orchestrator = new AIMarketingOrchestrator();
      await orchestrator.startMarketingCycle();

      // Verify data persistence
      const patterns = await database.getLearningPatterns('email-marketing');
      const savedPattern = patterns.find(p => p.id === patternId);
      expect(savedPattern).toBeDefined();
      expect(savedPattern?.confidence).toBe(0.85);
    }, 20000);
  });

  describe('Research System Integration', () => {
    beforeEach(async () => {
      orchestrator = new AIMarketingOrchestrator();
      await orchestrator.startMarketingCycle();
    });

    test('should process research tasks and save insights', async () => {
      const researchTask: MarketingTask = {
        id: 'integration-research-1',
        type: 'research',
        priority: 'medium',
        aiModel: 'research',
        data: {
          action: 'research_market_trends',
          keywords: ['natural stone', 'marble market', 'construction trends'],
          targetMarkets: ['US', 'DE', 'GB']
        },
        requiresApproval: false,
        createdAt: new Date()
      };

      orchestrator.addTask(researchTask);

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 8000));

      // Verify insights were saved
      const insights = await database.getMarketInsights({ 
        category: 'market-trends',
        limit: 10 
      });
      expect(insights.length).toBeGreaterThanOrEqual(0);
    }, 20000);
  });

  describe('Database Operations Integration', () => {
    test('should handle concurrent database operations', async () => {
      const promises = [];

      // Create multiple concurrent operations
      for (let i = 0; i < 10; i++) {
        promises.push(
          database.saveLearningPattern({
            patternData: { test: `pattern-${i}` },
            confidence: 0.5 + (i * 0.05),
            successRate: 0.6 + (i * 0.04),
            context: `test-context-${i % 3}`
          })
        );

        promises.push(
          database.saveMarketInsight({
            insightText: `Test insight ${i}`,
            source: 'integration-test',
            confidence: 0.7,
            actionable: i % 2 === 0,
            category: 'test',
            impactLevel: 'medium'
          })
        );
      }

      // Wait for all operations to complete
      const results = await Promise.all(promises);
      expect(results).toHaveLength(20);
      expect(results.every(id => typeof id === 'string')).toBe(true);
    }, 15000);

    test('should handle database transactions correctly', async () => {
      await database.transaction(async (client) => {
        // Insert test data within transaction
        await client.query(
          'INSERT INTO learning_patterns (pattern_data, confidence, success_rate, context) VALUES ($1, $2, $3, $4)',
          [JSON.stringify({ test: 'transaction' }), 0.8, 0.9, 'transaction-test']
        );

        await client.query(
          'INSERT INTO market_insights (insight_text, source, confidence, actionable, category, impact_level) VALUES ($1, $2, $3, $4, $5, $6)',
          ['Transaction test insight', 'test', 0.7, true, 'test', 'low']
        );

        return 'success';
      });

      // Verify data was committed
      const patterns = await database.getLearningPatterns('transaction-test');
      expect(patterns.length).toBe(1);

      const insights = await database.getMarketInsights({ category: 'test' });
      expect(insights.some(i => i.insight_text === 'Transaction test insight')).toBe(true);
    });

    test('should rollback failed transactions', async () => {
      try {
        await database.transaction(async (client) => {
          await client.query(
            'INSERT INTO learning_patterns (pattern_data, confidence, success_rate, context) VALUES ($1, $2, $3, $4)',
            [JSON.stringify({ test: 'rollback' }), 0.8, 0.9, 'rollback-test']
          );

          // Force an error
          throw new Error('Intentional error for rollback test');
        });
      } catch (error) {
        expect(error.message).toBe('Intentional error for rollback test');
      }

      // Verify data was not committed
      const patterns = await database.getLearningPatterns('rollback-test');
      expect(patterns.length).toBe(0);
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle high-volume task processing', async () => {
      orchestrator = new AIMarketingOrchestrator();
      await orchestrator.startMarketingCycle();

      const startTime = Date.now();
      const taskCount = 50;

      // Add multiple tasks rapidly
      for (let i = 0; i < taskCount; i++) {
        orchestrator.addTask({
          type: 'learning',
          priority: 'low',
          aiModel: 'learning',
          data: {
            action: 'analyze_performance',
            performanceData: {
              email: { openRate: 20 + i, clickRate: 2 + (i * 0.1) }
            }
          },
          requiresApproval: false
        });
      }

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 15000));

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Verify reasonable processing time (should be under 20 seconds)
      expect(processingTime).toBeLessThan(20000);

      // Verify system stats reflect the processing
      const stats = await orchestrator.getSystemStats();
      expect(stats).toBeDefined();
    }, 30000);

    test('should maintain system stability under load', async () => {
      orchestrator = new AIMarketingOrchestrator();
      await orchestrator.startMarketingCycle();

      // Simulate sustained load
      const loadDuration = 10000; // 10 seconds
      const startTime = Date.now();
      let taskCounter = 0;

      const loadInterval = setInterval(() => {
        if (Date.now() - startTime > loadDuration) {
          clearInterval(loadInterval);
          return;
        }

        orchestrator.addTask({
          type: 'learning',
          priority: 'medium',
          aiModel: 'learning',
          data: {
            action: 'analyze_performance',
            performanceData: {
              email: { openRate: Math.random() * 40 + 10 }
            }
          },
          requiresApproval: false
        });

        taskCounter++;
      }, 100); // Add task every 100ms

      // Wait for load test to complete
      await new Promise(resolve => setTimeout(resolve, loadDuration + 5000));

      // Verify system is still healthy
      const stats = await orchestrator.getSystemStats();
      expect(stats).toBeDefined();
      expect(taskCounter).toBeGreaterThan(50);
    }, 20000);
  });

  describe('Error Recovery Integration', () => {
    test('should recover from temporary database disconnection', async () => {
      orchestrator = new AIMarketingOrchestrator();
      await orchestrator.startMarketingCycle();

      // Simulate database error
      const originalQuery = database.query;
      database.query = jest.fn().mockRejectedValue(new Error('Connection lost'));

      // Add task during "disconnection"
      orchestrator.addTask({
        type: 'learning',
        priority: 'high',
        aiModel: 'learning',
        data: {
          action: 'analyze_performance',
          performanceData: { email: { openRate: 25 } }
        },
        requiresApproval: false
      });

      await new Promise(resolve => setTimeout(resolve, 2000));

      // Restore database connection
      database.query = originalQuery;

      // Add another task after "recovery"
      orchestrator.addTask({
        type: 'learning',
        priority: 'high',
        aiModel: 'learning',
        data: {
          action: 'analyze_performance',
          performanceData: { email: { openRate: 30 } }
        },
        requiresApproval: false
      });

      await new Promise(resolve => setTimeout(resolve, 3000));

      // Verify system recovered
      const stats = await orchestrator.getSystemStats();
      expect(stats).toBeDefined();
    }, 15000);
  });

  // Helper function to clean test data
  async function cleanTestData(): Promise<void> {
    try {
      await database.query('DELETE FROM learning_patterns WHERE context LIKE $1', ['%test%']);
      await database.query('DELETE FROM market_insights WHERE source LIKE $1', ['%test%']);
      await database.query('DELETE FROM performance_metrics WHERE module_name LIKE $1', ['%test%']);
      await database.query('DELETE FROM system_events WHERE module_name LIKE $1', ['%test%']);
    } catch (error) {
      console.warn('Failed to clean test data:', error);
    }
  }
});
