"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "./button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle } from "./card"
import { Badge } from "./badge"
import { 
  FileText,
  Download,
  Upload,
  Eye,
  Trash2,
  Plus,
  File,
  Image,
  FileSpreadsheet,
  Calendar,
  User,
  AlertTriangle
} from "lucide-react"
import { OrderDocument } from "@/types/orders"

interface OrderDocumentsProps {
  documents: OrderDocument[]
  onUpload?: (file: File, type: string) => void
  onDownload?: (document: OrderDocument) => void
  onDelete?: (documentId: string) => void
  onView?: (document: OrderDocument) => void
  className?: string
  readOnly?: boolean
}

export function OrderDocuments({ 
  documents, 
  onUpload,
  onDownload,
  onDelete,
  onView,
  className = "",
  readOnly = false
}: OrderDocumentsProps) {
  const [isUploadModalOpen, setIsUploadModalOpen] = React.useState(false)
  const [selectedDocumentType, setSelectedDocumentType] = React.useState<string>('other')
  const fileInputRef = React.useRef<HTMLInputElement>(null)

  const documentTypes = [
    { value: 'invoice', label: 'Fatura', icon: FileText },
    { value: 'receipt', label: 'Makbuz', icon: FileText },
    { value: 'shipping-label', label: 'Kargo Etiketi', icon: FileText },
    { value: 'delivery-note', label: 'İrsaliye', icon: FileText },
    { value: 'contract', label: 'Sözleşme', icon: FileText },
    { value: 'other', label: 'Diğer', icon: File }
  ]

  const getDocumentIcon = (type: string, fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase()
    
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) {
      return Image
    }
    if (['xls', 'xlsx', 'csv'].includes(extension || '')) {
      return FileSpreadsheet
    }
    
    switch (type) {
      case 'invoice':
      case 'receipt':
      case 'shipping-label':
      case 'delivery-note':
      case 'contract':
        return FileText
      default:
        return File
    }
  }

  const getDocumentTypeLabel = (type: string) => {
    const docType = documentTypes.find(dt => dt.value === type)
    return docType?.label || 'Bilinmeyen'
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('tr-TR', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && onUpload) {
      onUpload(file, selectedDocumentType)
      setIsUploadModalOpen(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const groupedDocuments = documents.reduce((acc, doc) => {
    if (!acc[doc.type]) {
      acc[doc.type] = []
    }
    acc[doc.type].push(doc)
    return acc
  }, {} as Record<string, OrderDocument[]>)

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Belgeler ({documents.length})
          </CardTitle>
          {!readOnly && (
            <Button 
              size="sm" 
              onClick={() => setIsUploadModalOpen(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Belge Ekle
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {documents.length > 0 ? (
          <div className="space-y-6">
            {Object.entries(groupedDocuments).map(([type, docs]) => (
              <div key={type}>
                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
                  {React.createElement(documentTypes.find(dt => dt.value === type)?.icon || File, { 
                    className: "w-4 h-4" 
                  })}
                  {getDocumentTypeLabel(type)} ({docs.length})
                </h4>
                <div className="space-y-2">
                  {docs.map((document) => {
                    const Icon = getDocumentIcon(document.type, document.name)
                    
                    return (
                      <div 
                        key={document.id} 
                        className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center gap-3 flex-1 min-w-0">
                          <Icon className="w-5 h-5 text-gray-600 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {document.name}
                            </p>
                            <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                              <div className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                <span>{document.uploadedBy}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                <span>{formatDate(document.uploadedAt)}</span>
                              </div>
                              <span>{formatFileSize(document.size)}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-1 ml-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onView && onView(document)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onDownload && onDownload(document)}
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                          {!readOnly && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => onDelete && onDelete(document.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-600 mb-4">Henüz belge eklenmemiş.</p>
            {!readOnly && (
              <Button onClick={() => setIsUploadModalOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                İlk Belgeyi Ekle
              </Button>
            )}
          </div>
        )}
      </CardContent>

      {/* Upload Modal */}
      {isUploadModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold">Belge Yükle</h3>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsUploadModalOpen(false)}
              >
                <AlertTriangle className="w-5 h-5" />
              </Button>
            </div>
            
            <div className="p-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Belge Türü
                </label>
                <select
                  value={selectedDocumentType}
                  onChange={(e) => setSelectedDocumentType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {documentTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dosya Seç
                </label>
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={handleFileUpload}
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Desteklenen formatlar: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG
                </p>
              </div>
            </div>
            
            <div className="flex justify-end gap-2 p-4 border-t">
              <Button
                variant="outline"
                onClick={() => setIsUploadModalOpen(false)}
              >
                İptal
              </Button>
              <Button onClick={() => fileInputRef.current?.click()}>
                <Upload className="w-4 h-4 mr-2" />
                Yükle
              </Button>
            </div>
          </div>
        </div>
      )}
    </Card>
  )
}
