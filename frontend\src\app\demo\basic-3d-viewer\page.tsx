'use client';

import React, { useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment, ContactShadows } from '@react-three/drei';
import * as THREE from 'three';
import {
  DimensionConfiguration,
  SurfaceFinishConfiguration,
  RoomSimulationConfiguration,
  AdvancedProductConfig,
  SurfaceFinishName
} from '../../../types/3d';
import { DimensionConfigurator } from '../../../components/3d/DimensionConfigurator';
import { SurfaceFinishSimulator } from '../../../components/3d/SurfaceFinishSimulator';

// Demo configuration data (simplified)
const DEMO_DIMENSION_CONFIG: DimensionConfiguration = {
  standardSizes: [
    { width: 30, height: 60, thickness: 2, label: '30x60x2', category: 'floor', applications: ['banyo', 'mutfak'] },
    { width: 60, height: 60, thickness: 2, label: '60x60x2', category: 'floor', applications: ['salon', 'yatak odası'] },
    { width: 80, height: 80, thickness: 3, label: '80x80x3', category: 'floor', applications: ['salon', 'dış mekan'] }
  ],
  customSize: {
    enabled: true,
    minWidth: 10,
    maxWidth: 300,
    minHeight: 10,
    maxHeight: 300,
    thicknessOptions: [1, 1.5, 2, 3, 4, 5],
    validation: {
      aspectRatio: { min: 0.2, max: 5.0 },
      structuralLimits: true,
      productionFeasibility: true
    }
  },
  pricing: {
    baseCalculation: 'area',
    wastageMultiplier: 1.1,
    cuttingCost: 5,
    customSizePremium: 0.15
  }
};

const DEMO_SURFACE_FINISH_CONFIG: SurfaceFinishConfiguration = {
  availableFinishes: [
    {
      name: 'ham' as SurfaceFinishName,
      description: 'Doğal yüzey, işlenmemiş',
      roughness: 0.9,
      metallic: 0.0,
      normalIntensity: 1.0,
      priceMultiplier: 1.0,
      shaderUniforms: {}
    },
    {
      name: 'honlu' as SurfaceFinishName,
      description: 'Mat yüzey, pürüzsüz',
      roughness: 0.6,
      metallic: 0.0,
      normalIntensity: 0.3,
      priceMultiplier: 1.2,
      shaderUniforms: {}
    },
    {
      name: 'cilali' as SurfaceFinishName,
      description: 'Parlak yüzey, ayna gibi',
      roughness: 0.1,
      metallic: 0.0,
      normalIntensity: 0.1,
      priceMultiplier: 1.5,
      shaderUniforms: {}
    }
  ],
  defaultFinish: 'ham' as SurfaceFinishName,
  transitionAnimation: {
    duration: 1000,
    easing: 'ease-in-out',
    steps: 30
  }
};

// Simple Product Mesh Component
function ProductMesh({ dimensions, surfaceFinish }: { 
  dimensions: { width: number; height: number; thickness: number };
  surfaceFinish: SurfaceFinishName;
}) {
  const geometry = React.useMemo(() => {
    return new THREE.BoxGeometry(
      dimensions.width / 100, // Convert cm to meters
      dimensions.thickness / 100,
      dimensions.height / 100
    );
  }, [dimensions]);

  const material = React.useMemo(() => {
    const finishConfig = DEMO_SURFACE_FINISH_CONFIG.availableFinishes.find(f => f.name === surfaceFinish);
    return new THREE.MeshStandardMaterial({
      color: surfaceFinish === 'cilali' ? '#f0f0f0' : surfaceFinish === 'honlu' ? '#e0e0e0' : '#d0d0d0',
      roughness: finishConfig?.roughness || 0.9,
      metalness: finishConfig?.metallic || 0.0
    });
  }, [surfaceFinish]);

  return (
    <mesh geometry={geometry} material={material} castShadow receiveShadow />
  );
}

export default function Basic3DViewer() {
  const [config, setConfig] = useState<AdvancedProductConfig>({
    productId: 'demo-marble-basic',
    dimensions: { width: 60, height: 60, thickness: 2 },
    surfaceFinish: 'ham' as SurfaceFinishName
  });



  const handleConfigChange = (newConfig: Partial<AdvancedProductConfig>) => {
    const updatedConfig = { ...config, ...newConfig };
    setConfig(updatedConfig);
  };

  const handleMaterialUpdate = () => {
    // Basic implementation - just for interface compatibility
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <h1 className="text-xl font-bold text-gray-900">
              Basit 3D Görüntüleyici
            </h1>
            <div className="text-sm text-gray-500">
              RFC-801 Basit Demo
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* 3D Viewer */}
          <div className="xl:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">3D Önizleme</h2>
              
              <div className="w-full h-96 bg-gray-100 rounded-lg overflow-hidden">
                <Canvas
                  camera={{ position: [3, 3, 3], fov: 50 }}
                  shadows
                >
                  {/* Lighting */}
                  <ambientLight intensity={0.4} />
                  <directionalLight 
                    position={[10, 10, 5]} 
                    intensity={1}
                    castShadow
                  />

                  {/* Environment */}
                  <Environment preset="studio" />

                  {/* Product */}
                  <ProductMesh 
                    dimensions={config.dimensions}
                    surfaceFinish={config.surfaceFinish}
                  />

                  {/* Ground */}
                  <ContactShadows 
                    position={[0, -1, 0]} 
                    opacity={0.4} 
                    scale={5} 
                    blur={2} 
                  />

                  {/* Controls */}
                  <OrbitControls
                    enablePan={true}
                    enableZoom={true}
                    enableRotate={true}
                    minDistance={2}
                    maxDistance={10}
                  />
                </Canvas>
              </div>

              {/* Current Selection */}
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-700">
                  <strong>Seçili:</strong> {config.dimensions.width} × {config.dimensions.height} × {config.dimensions.thickness} cm
                  <span className="ml-4 capitalize"><strong>Yüzey:</strong> {config.surfaceFinish}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Configuration Panels */}
          <div className="xl:col-span-1 space-y-6">
            {/* Dimension Configurator */}
            <DimensionConfigurator
              productId="demo-marble-basic"
              configuration={DEMO_DIMENSION_CONFIG}
              currentConfig={config}
              onConfigChange={handleConfigChange}

            />

            {/* Surface Finish Simulator */}
            <SurfaceFinishSimulator
              productId="demo-marble-basic"
              configuration={DEMO_SURFACE_FINISH_CONFIG}
              currentConfig={config}
              onConfigChange={handleConfigChange}
              onMaterialUpdate={handleMaterialUpdate}
            />

            {/* Removed price display */}
          </div>
        </div>
      </div>
    </div>
  );
}
