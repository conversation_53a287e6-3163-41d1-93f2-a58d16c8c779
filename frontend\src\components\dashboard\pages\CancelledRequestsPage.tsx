'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  ClipboardDocumentListIcon,
  XCircleIcon,
  EyeIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { useQuote, QuoteRequest } from '@/contexts/quote-context';

interface CancelledRequestsPageProps {
  onNavigate?: (route: string) => void;
}

const CancelledRequestsPage: React.FC<CancelledRequestsPageProps> = ({ onNavigate }) => {
  const { customerQuoteRequests, isLoading } = useQuote();

  // Sadece iptal edilen talepleri filtrele
  const cancelledRequests = customerQuoteRequests.filter(request =>
    ['rejected', 'cancelled'].includes(request.status)
  );

  const getStatusIcon = (status: string) => {
    return <XCircleIcon className="h-5 w-5 text-red-500" />;
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'rejected':
        return 'Reddedildi';
      case 'cancelled':
        return 'İptal Edildi';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    return 'bg-red-100 text-red-800';
  };

  const handleReactivateRequest = async (request: QuoteRequest) => {
    // Talebi yeniden aktif hale getir
    try {
      // Bu işlev backend'e istek gönderecek
      console.log('Reactivating request:', request.id);
      // Şimdilik sadece sayfa yönlendirmesi yapalım
      onNavigate?.('/customer/requests/active');
    } catch (error) {
      console.error('Error reactivating request:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-stone-600 mx-auto mb-4"></div>
          <p className="text-gray-600">İptal edilen talepler yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">İptal Edilen Talepler</h1>
          <p className="text-gray-600 mt-1">Reddedilen ve iptal edilen talepleriniz</p>
        </div>
        <div className="bg-red-50 px-4 py-2 rounded-lg">
          <p className="text-sm text-red-600 font-medium">
            {cancelledRequests.length} iptal edilen talep
          </p>
        </div>
      </div>

      {/* Navigation Links */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => onNavigate?.('/customer/requests/active')}
            className="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm whitespace-nowrap"
          >
            Aktif Talepler
            {customerQuoteRequests.filter(r => ['pending', 'quoted'].includes(r.status)).length > 0 && (
              <span className="ml-2 py-0.5 px-2 rounded-full text-xs bg-gray-100 text-gray-600">
                {customerQuoteRequests.filter(r => ['pending', 'quoted'].includes(r.status)).length}
              </span>
            )}
          </button>
          <button
            onClick={() => onNavigate?.('/customer/requests/cancelled')}
            className="py-2 px-1 border-b-2 border-red-500 text-red-600 font-medium text-sm whitespace-nowrap"
          >
            İptal Edilen
            {cancelledRequests.length > 0 && (
              <span className="ml-2 py-0.5 px-2 rounded-full text-xs bg-red-100 text-red-600">
                {cancelledRequests.length}
              </span>
            )}
          </button>
        </nav>
      </div>

      {/* Requests List */}
      {cancelledRequests.length > 0 ? (
        <div className="space-y-6">
          {cancelledRequests.map((request, index) => (
            <motion.div
              key={request.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200"
            >
              {/* Request Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(request.status)}
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(request.status)}`}>
                      {getStatusLabel(request.status)}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {request.id}. Talep
                    </h3>
                    <p className="text-sm text-gray-600">
                      {request.createdAt && new Date(request.createdAt).toLocaleDateString('tr-TR')}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Alınan Teklif</p>
                  <p className="text-lg font-semibold text-red-600">
                    {request.quotes?.length || 0}
                  </p>
                </div>
              </div>

              {/* Products */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Ürünler</h4>
                <div className="space-y-2">
                  {request.products.map((product) => (
                    <div key={product.id} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                      <div>
                        <p className="font-medium text-gray-900">{product.productName}</p>
                        <p className="text-sm text-gray-600">{product.productCategory}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {product.specifications?.length || 0} farklı ebat
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {product.specifications?.[0]?.area ? `${product.specifications[0].area} m²` : 'Belirtilmemiş'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {product.specifications?.[0]?.thickness ? `${product.specifications[0].thickness} cm` : ''}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Message */}
              {request.message && (
                <div className="mb-4 bg-blue-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-900 mb-2">Mesaj</h4>
                  <p className="text-sm text-blue-800">{request.message}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => onNavigate?.(`/customer/requests/detail/${request.id}`)}
                    className="flex items-center space-x-2 text-blue-600 hover:text-blue-700"
                  >
                    <EyeIcon className="h-4 w-4" />
                    <span className="text-sm font-medium">Detay</span>
                  </button>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleReactivateRequest(request)}
                    className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <PlusIcon className="h-4 w-4" />
                    <span className="text-sm font-medium">Tekrar Teklif İste</span>
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <ClipboardDocumentListIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            İptal edilen talebiniz bulunmuyor
          </h3>
          <p className="text-gray-600 mb-6">
            İptal ettiğiniz talepler burada görünecek.
          </p>
          <button
            onClick={() => onNavigate?.('/customer/requests/active')}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Aktif Taleplere Dön
          </button>
        </div>
      )}
    </div>
  );
};

export default CancelledRequestsPage;
