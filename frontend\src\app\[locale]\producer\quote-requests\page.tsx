'use client'

import * as React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  XCircle,
  DollarSign,
  Calendar,
  User,
  Package,
  MessageSquare,
  TrendingUp
} from 'lucide-react'

// Mock data for quote requests
const mockQuoteRequests = [
  {
    id: '1',
    customerName: 'ABC İnşaat Ltd.',
    customerEmail: '<EMAIL>',
    productName: 'Beyaz Mermer',
    quantity: 500,
    unit: 'm²',
    requestDate: '2025-06-28',
    deadline: '2025-06-30',
    status: 'pending',
    description: 'Otel projesi için kaliteli beyaz mermer ihtiyacımız var.',
    deliveryLocation: 'İstanbul, Türkiye',
    targetPrice: 40
  },
  {
    id: '2',
    customerName: 'XYZ Mimarlık',
    customerEmail: '<EMAIL>',
    productName: 'Traverten Plaka',
    quantity: 200,
    unit: 'm²',
    requestDate: '2025-06-27',
    deadline: '2025-06-29',
    status: 'quoted',
    description: 'Villa projesi için traverten plaka.',
    deliveryLocation: 'Ankara, Türkiye',
    targetPrice: 30,
    myQuote: {
      price: 35,
      currency: 'USD',
      deliveryTime: '15 gün',
      notes: 'Kaliteli traverten, hızlı teslimat',
      quotedDate: '2025-06-28'
    }
  },
  {
    id: '3',
    customerName: 'DEF Yapı',
    customerEmail: '<EMAIL>',
    productName: 'Granit Blok',
    quantity: 50,
    unit: 'ton',
    requestDate: '2025-06-26',
    deadline: '2025-06-28',
    status: 'accepted',
    description: 'Anıt projesi için yüksek kaliteli granit blok.',
    deliveryLocation: 'Afyon, Türkiye',
    targetPrice: 750,
    myQuote: {
      price: 800,
      currency: 'USD',
      deliveryTime: '20 gün',
      notes: 'Premium kalite granit',
      quotedDate: '2025-06-27',
      acceptedDate: '2025-06-28'
    }
  },
  {
    id: '5',
    customerName: 'JKL İnşaat',
    customerEmail: '<EMAIL>',
    productName: 'Siyah Granit',
    quantity: 150,
    unit: 'm²',
    requestDate: '2025-06-24',
    deadline: '2025-06-26',
    status: 'rejected',
    description: 'Mutfak tezgahı için siyah granit.',
    deliveryLocation: 'Bursa, Türkiye',
    targetPrice: 60
  }
]

export default function ProducerQuoteRequests() {
  const { producer } = useProducerAuth()
  const pathname = usePathname()

  const menuItems = [
    { 
      id: 'pending', 
      label: 'Bekleyen Talepler', 
      href: '/producer/quote-requests/pending',
      count: mockQuoteRequests.filter(r => r.status === 'pending').length,
      icon: Clock,
      color: 'text-yellow-600 bg-yellow-100'
    },
    { 
      id: 'quoted', 
      label: 'Teklif Verildi', 
      href: '/producer/quote-requests/quoted',
      count: mockQuoteRequests.filter(r => r.status === 'quoted').length,
      icon: FileText,
      color: 'text-blue-600 bg-blue-100'
    },
    { 
      id: 'accepted', 
      label: 'Kabul Edildi', 
      href: '/producer/quote-requests/accepted',
      count: mockQuoteRequests.filter(r => r.status === 'accepted').length,
      icon: CheckCircle,
      color: 'text-green-600 bg-green-100'
    },
    { 
      id: 'rejected', 
      label: 'Reddedildi', 
      href: '/producer/quote-requests/rejected',
      count: mockQuoteRequests.filter(r => r.status === 'rejected').length,
      icon: XCircle,
      color: 'text-red-600 bg-red-100'
    }
  ]

  // Summary stats
  const stats = {
    total: mockQuoteRequests.length,
    pending: mockQuoteRequests.filter(r => r.status === 'pending').length,
    quoted: mockQuoteRequests.filter(r => r.status === 'quoted').length,
    accepted: mockQuoteRequests.filter(r => r.status === 'accepted').length,
    rejected: mockQuoteRequests.filter(r => r.status === 'rejected').length,
    conversionRate: mockQuoteRequests.length > 0 ? 
      Math.round((mockQuoteRequests.filter(r => r.status === 'accepted').length / mockQuoteRequests.length) * 100) : 0
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100'
      case 'quoted':
        return 'text-blue-600 bg-blue-100'
      case 'accepted':
        return 'text-green-600 bg-green-100'
      case 'rejected':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'quoted':
        return 'Teklif Verildi'
      case 'accepted':
        return 'Kabul Edildi'
      case 'rejected':
        return 'Reddedildi'
      default:
        return status
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'quoted':
        return <FileText className="w-4 h-4" />
      case 'accepted':
        return <CheckCircle className="w-4 h-4" />
      case 'rejected':
        return <XCircle className="w-4 h-4" />
      default:
        return <FileText className="w-4 h-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Teklif Talepleri</h1>
          <p className="text-gray-600">
            Teklif taleplerini yönetin ve performansınızı takip edin
          </p>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Talep</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Tüm zamanlar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">
              Teklif bekliyor
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Kabul Edildi</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.accepted}</div>
            <p className="text-xs text-muted-foreground">
              Siparişe dönüştü
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dönüşüm Oranı</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.conversionRate}%</div>
            <p className="text-xs text-muted-foreground">
              Kabul oranı
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Menu Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {menuItems.map((item) => {
          const Icon = item.icon
          return (
            <Link key={item.id} href={item.href}>
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <div className={`p-2 rounded-lg ${item.color}`}>
                          <Icon className="w-5 h-5" />
                        </div>
                      </div>
                      <h3 className="font-semibold text-gray-900">{item.label}</h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {item.count} talep
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">{item.count}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          )
        })}
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Son Aktiviteler</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockQuoteRequests.slice(0, 3).map((request) => (
              <div key={request.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${getStatusColor(request.status)}`}>
                    {getStatusIcon(request.status)}
                  </div>
                  <div>
                    <p className="font-medium text-sm">{request.customerName}</p>
                    <p className="text-sm text-gray-600">{request.productName} - {request.quantity} {request.unit}</p>
                    <p className="text-xs text-gray-500">{request.requestDate}</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                    {getStatusText(request.status)}
                  </span>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <Link href="/producer/quote-requests/pending">
              <Button variant="outline" size="sm">
                Tüm Talepleri Görüntüle
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
