'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from './card'
import { Button } from './button'
import { Badge } from './badge'
import { 
  FileText,
  User,
  Building2,
  Package,
  DollarSign,
  CreditCard,
  Truck,
  Calendar,
  Check,
  AlertTriangle,
  Mail,
  Phone,
  MapPin
} from 'lucide-react'

interface OrderSummaryStepProps {
  formData: any
  setFormData: (data: any) => void
}

export function OrderSummaryStep({ formData, setFormData }: OrderSummaryStepProps) {
  const [orderNotes, setOrderNotes] = React.useState(formData.notes || '')
  const [isCreating, setIsCreating] = React.useState(false)

  React.useEffect(() => {
    setFormData({ ...formData, notes: orderNotes })
  }, [orderNotes])

  const formatCurrency = (amount: number) => {
    const currency = formData.pricing?.currency || 'USD'
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount)
  }

  const generateOrderNumber = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `ORD-${year}${month}${day}-${random}`
  }

  const getDeliveryMethodLabel = (method: string) => {
    const methods = {
      'factory': 'Fabrika Teslim',
      'port': 'Liman Teslim',
      'door-to-door': 'Kapıya Teslim'
    }
    return methods[method as keyof typeof methods] || method
  }

  const getPaymentMethodLabel = (method: string) => {
    const methods = {
      'bank-transfer': 'Banka Havalesi',
      'credit-card': 'Kredi Kartı',
      'cash': 'Nakit',
      'check': 'Çek'
    }
    return methods[method as keyof typeof methods] || method
  }

  const getTotalQuantity = () => {
    if (!formData.products) return 0
    return formData.products.reduce((total: number, product: any) => 
      total + product.specifications.reduce((productTotal: number, spec: any) => 
        productTotal + spec.quantity, 0
      ), 0
    )
  }

  const getTotalSpecCount = () => {
    if (!formData.products) return 0
    return formData.products.reduce((total: number, product: any) => 
      total + product.specifications.length, 0
    )
  }

  const isFormComplete = () => {
    return formData.customer && 
           formData.producer && 
           formData.products && 
           formData.products.length > 0 &&
           formData.pricing &&
           formData.payment
  }

  const handleCreateOrder = async () => {
    if (!isFormComplete()) {
      alert('Lütfen tüm adımları tamamlayın.')
      return
    }

    setIsCreating(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const orderNumber = generateOrderNumber()
      
      // Here you would normally send the data to your API
      console.log('Creating order:', {
        orderNumber,
        ...formData,
        createdAt: new Date(),
        createdBy: 'admin-001' // Current admin user
      })
      
      alert(`Sipariş başarıyla oluşturuldu!\nSipariş No: ${orderNumber}`)
      
      // Redirect to orders list
      window.location.href = '/admin/orders'
      
    } catch (error) {
      console.error('Order creation failed:', error)
      alert('Sipariş oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.')
    } finally {
      setIsCreating(false)
    }
  }

  if (!isFormComplete()) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Eksik Bilgiler</h3>
        <p className="text-gray-600 mb-6">Sipariş özeti görüntülemek için tüm adımları tamamlayın.</p>
        <div className="space-y-2 text-sm text-gray-600">
          {!formData.customer && <p>• Müşteri seçimi yapılmadı</p>}
          {!formData.producer && <p>• Üretici seçimi yapılmadı</p>}
          {(!formData.products || formData.products.length === 0) && <p>• Ürün seçimi yapılmadı</p>}
          {!formData.pricing && <p>• Fiyatlandırma tamamlanmadı</p>}
          {!formData.payment && <p>• Ödeme ayarları yapılmadı</p>}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Order Overview */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-900">
            <FileText className="w-5 h-5" />
            Sipariş Özeti
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-blue-900">{formData.products.length}</p>
              <p className="text-sm text-blue-700">Ürün Çeşidi</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-900">{getTotalSpecCount()}</p>
              <p className="text-sm text-blue-700">Toplam Ebat</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-900">{getTotalQuantity()}</p>
              <p className="text-sm text-blue-700">Toplam Miktar (m²)</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-900">{formatCurrency(formData.pricing.totalAmount)}</p>
              <p className="text-sm text-blue-700">Toplam Tutar</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customer & Producer Info */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Müşteri Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <h4 className="font-semibold text-gray-900">{formData.customer.companyName}</h4>
              <p className="text-gray-600">{formData.customer.contactPerson}</p>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <span>{formData.customer.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-gray-400" />
                <span>{formData.customer.phone}</span>
              </div>
              {formData.customer.address && (
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-gray-400" />
                  <span>{formData.customer.address.city}, {formData.customer.address.country}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="w-5 h-5" />
              Üretici Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <h4 className="font-semibold text-gray-900">{formData.producer.companyName}</h4>
              <p className="text-gray-600">{formData.producer.contactPerson}</p>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <span>{formData.producer.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-gray-400" />
                <span>{formData.producer.phone}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-400" />
                <span>{formData.producer.factoryAddress.city}, {formData.producer.factoryAddress.country}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Products Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Sipariş Edilen Ürünler
          </CardTitle>
        </CardHeader>
        <CardContent>
          {formData.products.map((product: any, index: number) => (
            <div key={index} className="mb-6 last:mb-0">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                  <Package className="w-6 h-6 text-gray-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{product.productName}</h4>
                  <Badge variant="outline">{product.category}</Badge>
                </div>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full text-sm border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-3 py-2 text-left">Ebat (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-left">Yüzey</th>
                      <th className="border border-gray-300 px-3 py-2 text-left">Miktar</th>
                      <th className="border border-gray-300 px-3 py-2 text-left">Birim Fiyat</th>
                      <th className="border border-gray-300 px-3 py-2 text-left">Toplam</th>
                    </tr>
                  </thead>
                  <tbody>
                    {product.specifications.map((spec: any, specIndex: number) => (
                      <tr key={specIndex}>
                        <td className="border border-gray-300 px-3 py-2">
                          {spec.thickness}×{spec.width}×{spec.length}
                        </td>
                        <td className="border border-gray-300 px-3 py-2">{spec.surface}</td>
                        <td className="border border-gray-300 px-3 py-2">{spec.quantity} m²</td>
                        <td className="border border-gray-300 px-3 py-2">{formatCurrency(spec.unitPrice)}</td>
                        <td className="border border-gray-300 px-3 py-2 font-semibold text-green-600">
                          {formatCurrency(spec.totalPrice)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Pricing & Payment Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Fiyat Detayları
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span>Ara Toplam:</span>
              <span>{formatCurrency(formData.pricing.subtotal)}</span>
            </div>
            <div className="flex justify-between">
              <span>KDV ({(formData.pricing.taxRate * 100).toFixed(0)}%):</span>
              <span>{formatCurrency(formData.pricing.taxAmount)}</span>
            </div>
            {formData.pricing.shippingCost > 0 && (
              <div className="flex justify-between">
                <span>Kargo/Nakliye:</span>
                <span>{formatCurrency(formData.pricing.shippingCost)}</span>
              </div>
            )}
            {formData.pricing.discountAmount > 0 && (
              <div className="flex justify-between text-red-600">
                <span>İndirim:</span>
                <span>-{formatCurrency(formData.pricing.discountAmount)}</span>
              </div>
            )}
            <hr />
            <div className="flex justify-between text-lg font-bold">
              <span>Genel Toplam:</span>
              <span className="text-green-600">{formatCurrency(formData.pricing.totalAmount)}</span>
            </div>
            <div className="text-sm text-gray-600">
              <p>Teslimat: {getDeliveryMethodLabel(formData.pricing.deliveryMethod)}</p>
              {formData.pricing.estimatedDeliveryDate && (
                <p>Tahmini Teslimat: {new Date(formData.pricing.estimatedDeliveryDate).toLocaleDateString('tr-TR')}</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Ödeme Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <p className="text-sm text-gray-600">Ödeme Yöntemi</p>
              <p className="font-medium">{getPaymentMethodLabel(formData.payment.paymentMethod)}</p>
            </div>
            <div className="flex justify-between">
              <span>Ön Ödeme ({formData.payment.advancePayment.percentage}%):</span>
              <span className="font-semibold">{formatCurrency(formData.payment.advancePayment.amount)}</span>
            </div>
            <div className="flex justify-between">
              <span>Kalan Ödeme:</span>
              <span className="font-semibold">{formatCurrency(formData.payment.remainingPayment.amount)}</span>
            </div>
            {formData.payment.advancePayment.dueDate && (
              <div className="text-sm text-gray-600">
                <p>Ön Ödeme Tarihi: {new Date(formData.payment.advancePayment.dueDate).toLocaleDateString('tr-TR')}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Order Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Sipariş Notları</CardTitle>
        </CardHeader>
        <CardContent>
          <textarea
            value={orderNotes}
            onChange={(e) => setOrderNotes(e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Sipariş hakkında özel notlar, talimatlar veya açıklamalar..."
          />
        </CardContent>
      </Card>

      {/* Final Actions */}
      <Card className="bg-green-50 border-green-200">
        <CardContent className="p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-green-900 mb-2">Sipariş Oluşturmaya Hazır</h3>
            <p className="text-green-700 mb-6">
              Tüm bilgileri kontrol ettiniz mi? Sipariş oluşturulduktan sonra müşteri ve üreticiye otomatik bildirim gönderilecektir.
            </p>
            
            <div className="flex justify-center gap-4">
              <Button
                variant="outline"
                onClick={() => window.print()}
              >
                <FileText className="w-4 h-4 mr-2" />
                Önizleme Yazdır
              </Button>
              
              <Button
                onClick={handleCreateOrder}
                disabled={isCreating}
                className="bg-green-600 hover:bg-green-700"
              >
                {isCreating ? (
                  <>
                    <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    Sipariş Oluşturuluyor...
                  </>
                ) : (
                  <>
                    <Check className="w-4 h-4 mr-2" />
                    Siparişi Oluştur
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
