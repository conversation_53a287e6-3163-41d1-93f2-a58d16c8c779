/**
 * Chatbot Types
 * Type definitions for the frontend chatbot components
 */

export enum ChatbotIntent {
  // Product Related
  PRODUCT_INQUIRY = 'product_inquiry',
  PRODUCT_SPECIFICATIONS = 'product_specifications',
  PRODUCT_PRICING = 'product_pricing',
  PRODUCT_AVAILABILITY = 'product_availability',
  
  // Order Related
  ORDER_STATUS = 'order_status',
  ORDER_TRACKING = 'order_tracking',
  ORDER_MODIFICATION = 'order_modification',
  ORDER_CANCELLATION = 'order_cancellation',
  
  // Bidding Related
  BID_PROCESS = 'bid_process',
  BID_STATUS = 'bid_status',
  BID_REQUIREMENTS = 'bid_requirements',
  
  // Account Related
  ACCOUNT_SETUP = 'account_setup',
  PROFILE_UPDATE = 'profile_update',
  VERIFICATION_STATUS = 'verification_status',
  
  // Payment Related
  PAYMENT_METHODS = 'payment_methods',
  PAYMENT_STATUS = 'payment_status',
  REFUND_REQUEST = 'refund_request',
  
  // Technical Support
  TECHNICAL_ISSUE = 'technical_issue',
  PLATFORM_USAGE = 'platform_usage',
  
  // General
  GREETING = 'greeting',
  GOODBYE = 'goodbye',
  HUMAN_HANDOFF = 'human_handoff',
  COMPLAINT = 'complaint',
  UNKNOWN = 'unknown'
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  language?: string;
  intent?: ChatbotIntent;
  confidence?: number;
  entities?: ExtractedEntity[];
}

export interface ExtractedEntity {
  type: string;
  value: string;
  confidence: number;
  start: number;
  end: number;
}

export interface ChatbotResponse {
  success: boolean;
  sessionId: string;
  response: string;
  confidence: number;
  intent: ChatbotIntent;
  entities: ExtractedEntity[];
  suggestions: string[];
  escalated?: boolean;
  agentId?: string;
  estimatedWaitTime?: number;
}

export interface ConversationHistory {
  sessionId: string;
  messages: ChatMessage[];
  count: number;
}

export interface UserFeedback {
  sessionId: string;
  messageId: string;
  rating: number;
  feedback?: string;
  timestamp: Date;
}

export interface ChatbotAnalytics {
  responseAccuracy: number;
  resolutionRate: number;
  averageResponseTime: number;
  userSatisfactionScore: number;
  totalConversations: number;
  averageConversationLength: number;
  mostCommonIntents: IntentFrequency[];
  languageDistribution: LanguageStats[];
  escalationRate: number;
  escalationReasons: EscalationReason[];
  humanResolutionRate: number;
  newKnowledgeGaps: KnowledgeGap[];
  improvementOpportunities: Improvement[];
}

export interface IntentFrequency {
  intent: ChatbotIntent;
  count: number;
  percentage: number;
}

export interface LanguageStats {
  language: string;
  count: number;
  percentage: number;
}

export interface EscalationReason {
  reason: string;
  count: number;
  percentage: number;
}

export interface KnowledgeGap {
  topic: string;
  frequency: number;
  examples: string[];
}

export interface Improvement {
  area: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  estimatedImpact: string;
}

export interface Agent {
  id: string;
  name: string;
  languages: string[];
  expertise: string[];
  currentLoad: number;
  available: boolean;
}

export interface EscalationCriteria {
  lowConfidenceThreshold: number;
  mediumConfidenceThreshold: number;
  alwaysEscalate: ChatbotIntent[];
  neverEscalate: ChatbotIntent[];
  negativeSentimentThreshold: number;
  multipleUnresolvedIssues: number;
  conversationLength: number;
  repeatedQuestions: number;
  vipCustomers: string[];
  highValueOrders: number;
}

export interface ChatWidgetConfig {
  userId?: string;
  language?: string;
  position?: 'bottom-right' | 'bottom-left';
  theme?: 'light' | 'dark';
  autoStart?: boolean;
  showSuggestions?: boolean;
  enableFeedback?: boolean;
  enableVoice?: boolean;
}

export interface ChatSession {
  sessionId: string;
  userId?: string;
  language: string;
  messageCount: number;
  lastActivity: Date;
  escalationLevel: number;
  status: 'active' | 'escalated' | 'closed';
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: any[];
}

export interface StartConversationResponse {
  sessionId: string;
  greeting: string;
}

export interface ConversationStatsResponse {
  messageCount: number;
  duration: number;
  intents: Record<string, number>;
  averageConfidence: number;
  escalationLevel: number;
}

// Localization Types
export interface ChatbotTranslations {
  [language: string]: {
    greeting: string;
    placeholder: string;
    sendButton: string;
    closeButton: string;
    minimizeButton: string;
    maximizeButton: string;
    errorMessage: string;
    typingIndicator: string;
    escalationMessage: string;
    feedbackPrompt: string;
    suggestions: {
      productInquiry: string;
      pricing: string;
      bidding: string;
      delivery: string;
      support: string;
    };
  };
}

// Voice Interface Types (for future implementation)
export interface VoiceConfig {
  enabled: boolean;
  language: string;
  voice?: string;
  rate?: number;
  pitch?: number;
  volume?: number;
}

export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
}

// Admin Dashboard Types
export interface ChatbotDashboardData {
  analytics: ChatbotAnalytics;
  activeSessions: ChatSession[];
  agents: Agent[];
  escalationCriteria: EscalationCriteria;
  recentFeedback: UserFeedback[];
}

export interface ChatbotSettings {
  enabled: boolean;
  defaultLanguage: string;
  supportedLanguages: string[];
  escalationCriteria: EscalationCriteria;
  autoResponses: Record<string, string>;
  businessHours: {
    enabled: boolean;
    timezone: string;
    schedule: {
      [day: string]: {
        start: string;
        end: string;
        enabled: boolean;
      };
    };
  };
}

// Error Types
export class ChatbotError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ChatbotError';
  }
}

export enum ChatbotErrorCode {
  SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',
  INVALID_MESSAGE = 'INVALID_MESSAGE',
  API_ERROR = 'API_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'
}

// Event Types for real-time updates
export interface ChatbotEvent {
  type: 'message' | 'typing' | 'escalation' | 'agent_assigned' | 'session_ended';
  sessionId: string;
  data: any;
  timestamp: Date;
}

export interface TypingEvent extends ChatbotEvent {
  type: 'typing';
  data: {
    isTyping: boolean;
    userId?: string;
  };
}

export interface MessageEvent extends ChatbotEvent {
  type: 'message';
  data: ChatMessage;
}

export interface EscalationEvent extends ChatbotEvent {
  type: 'escalation';
  data: {
    agentId: string;
    estimatedWaitTime?: number;
    reason: string;
  };
}

// Hooks Types
export interface UseChatbotReturn {
  messages: ChatMessage[];
  isLoading: boolean;
  isTyping: boolean;
  sessionId: string | null;
  suggestions: string[];
  sendMessage: (message: string) => Promise<void>;
  startConversation: () => Promise<void>;
  endConversation: () => Promise<void>;
  submitFeedback: (messageId: string, rating: number, feedback?: string) => Promise<void>;
  error: ChatbotError | null;
  clearError: () => void;
}

export interface UseChatbotAnalyticsReturn {
  analytics: ChatbotAnalytics | null;
  isLoading: boolean;
  error: ChatbotError | null;
  refresh: () => Promise<void>;
}

export interface UseChatbotAdminReturn {
  dashboardData: ChatbotDashboardData | null;
  isLoading: boolean;
  error: ChatbotError | null;
  updateEscalationCriteria: (criteria: Partial<EscalationCriteria>) => Promise<void>;
  updateAgentAvailability: (agentId: string, available: boolean) => Promise<void>;
  refresh: () => Promise<void>;
}
