'use client';

import React from 'react';
import { SampleProvider } from '@/contexts/sample-context';
import AdminSampleRequestsSubPage from '@/components/admin/pages/AdminSampleRequestsSubPage';

export default function AdminSampleRequestsRejectedPage() {
  return (
    <SampleProvider>
      <AdminSampleRequestsSubPage 
        status="rejected"
        title="Reddedilen Numune Talepleri"
        description="Reddedilmiş numune taleplerini görüntüleyin"
      />
    </SampleProvider>
  );
}
