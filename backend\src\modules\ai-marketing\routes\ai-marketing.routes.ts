// RFC-013: AI Marketing API Routes
import { Router, Request, Response } from 'express';
import { AIMarketingOrchestrator } from '../orchestrator/AIMarketingOrchestrator';
import { EmailMarketingAI } from '../email-marketing/EmailMarketingAI';
import { SocialMediaAI } from '../social-media/SocialMediaAI';

const router = Router();

// Initialize AI Marketing services
const orchestrator = new AIMarketingOrchestrator();
const emailAI = new EmailMarketingAI();
const socialAI = new SocialMediaAI();

/**
 * @swagger
 * /api/admin/ai-marketing/status:
 *   get:
 *     summary: Get AI Marketing system status
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System status retrieved successfully
 */
router.get('/status', async (req: Request, res: Response) => {
  try {
    const status = await orchestrator.getSystemStatus();
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('Error getting AI marketing status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get system status'
    });
  }
});

/**
 * @swagger
 * /api/admin/ai-marketing/stats:
 *   get:
 *     summary: Get comprehensive AI Marketing statistics
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 */
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const stats = await orchestrator.getSystemStats();
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error getting AI marketing stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get statistics'
    });
  }
});

/**
 * @swagger
 * /api/admin/ai-marketing/start:
 *   post:
 *     summary: Start AI Marketing automation cycle
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: AI Marketing cycle started successfully
 */
router.post('/start', async (req: Request, res: Response) => {
  try {
    await orchestrator.startMarketingCycle();
    res.json({
      success: true,
      message: 'AI Marketing cycle started successfully'
    });
  } catch (error) {
    console.error('Error starting AI marketing cycle:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start AI marketing cycle'
    });
  }
});

/**
 * @swagger
 * /api/admin/ai-marketing/stop:
 *   post:
 *     summary: Stop AI Marketing automation cycle
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: AI Marketing cycle stopped successfully
 */
router.post('/stop', async (req: Request, res: Response) => {
  try {
    orchestrator.stopMarketingCycle();
    res.json({
      success: true,
      message: 'AI Marketing cycle stopped successfully'
    });
  } catch (error) {
    console.error('Error stopping AI marketing cycle:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to stop AI marketing cycle'
    });
  }
});

// Email Marketing Routes
/**
 * @swagger
 * /api/admin/ai-marketing/email/lists:
 *   get:
 *     summary: Get country-based email lists
 *     tags: [Email Marketing]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Email lists retrieved successfully
 */
router.get('/email/lists', async (req: Request, res: Response) => {
  try {
    // Mock data - gerçek implementasyonda EmailMarketingAI'dan gelecek
    const mockLists = [
      {
        id: '1',
        countryCode: 'US',
        countryName: 'Amerika Birleşik Devletleri',
        flag: '🇺🇸',
        totalSubscribers: 2847,
        activeSubscribers: 2654,
        lastUpdated: new Date(),
        segments: 8
      },
      {
        id: '2',
        countryCode: 'DE',
        countryName: 'Almanya',
        flag: '🇩🇪',
        totalSubscribers: 1923,
        activeSubscribers: 1834,
        lastUpdated: new Date(),
        segments: 6
      },
      {
        id: '3',
        countryCode: 'IT',
        countryName: 'İtalya',
        flag: '🇮🇹',
        totalSubscribers: 1456,
        activeSubscribers: 1398,
        lastUpdated: new Date(),
        segments: 5
      }
    ];

    res.json({
      success: true,
      data: mockLists
    });
  } catch (error) {
    console.error('Error getting email lists:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get email lists'
    });
  }
});

/**
 * @swagger
 * /api/admin/ai-marketing/email/campaigns:
 *   get:
 *     summary: Get email campaigns
 *     tags: [Email Marketing]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Email campaigns retrieved successfully
 */
router.get('/email/campaigns', async (req: Request, res: Response) => {
  try {
    // Mock data - gerçek implementasyonda EmailMarketingAI'dan gelecek
    const mockCampaigns = [
      {
        id: '1',
        name: 'Yeni Ürün Tanıtımı - Traverten',
        subject: 'Yeni Traverten Koleksiyonumuz Sizleri Bekliyor!',
        targetCountries: ['US', 'DE', 'IT'],
        status: 'sent',
        scheduledFor: new Date('2025-07-01'),
        recipients: 6224,
        openRate: 24.5,
        clickRate: 3.2,
        conversionRate: 1.8,
        createdAt: new Date('2025-06-28')
      },
      {
        id: '2',
        name: 'Özel İndirim Kampanyası',
        subject: 'Sadece Bu Hafta: %15 İndirim!',
        targetCountries: ['FR', 'ES'],
        status: 'sending',
        scheduledFor: new Date('2025-07-04'),
        recipients: 2221,
        openRate: 0,
        clickRate: 0,
        conversionRate: 0,
        createdAt: new Date('2025-07-03')
      }
    ];

    res.json({
      success: true,
      data: mockCampaigns
    });
  } catch (error) {
    console.error('Error getting email campaigns:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get email campaigns'
    });
  }
});

/**
 * @swagger
 * /api/admin/ai-marketing/email/add-subscriber:
 *   post:
 *     summary: Add new subscriber to country list
 *     tags: [Email Marketing]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               customerId:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               company:
 *                 type: string
 *               industry:
 *                 type: string
 *               country:
 *                 type: string
 *     responses:
 *       200:
 *         description: Subscriber added successfully
 */
router.post('/email/add-subscriber', async (req: Request, res: Response) => {
  try {
    const subscriberData = req.body;
    
    // EmailMarketingAI'ya task olarak gönder
    orchestrator.addTask({
      type: 'email_campaign',
      priority: 'medium',
      aiModel: 'email',
      data: {
        action: 'add_subscriber',
        ...subscriberData
      },
      requiresApproval: false
    });

    res.json({
      success: true,
      message: 'Subscriber addition task queued successfully'
    });
  } catch (error) {
    console.error('Error adding subscriber:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add subscriber'
    });
  }
});

/**
 * @swagger
 * /api/admin/ai-marketing/email/generate-campaign:
 *   post:
 *     summary: Generate AI email campaign
 *     tags: [Email Marketing]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               targetCountries:
 *                 type: array
 *                 items:
 *                   type: string
 *               productCategory:
 *                 type: string
 *               campaignType:
 *                 type: string
 *     responses:
 *       200:
 *         description: Campaign generation started
 */
router.post('/email/generate-campaign', async (req: Request, res: Response) => {
  try {
    const { targetCountries, productCategory, campaignType } = req.body;
    
    orchestrator.addTask({
      type: 'email_campaign',
      priority: 'high',
      aiModel: 'email',
      data: {
        action: 'generate_campaign',
        targetCountries,
        productCategory,
        campaignType
      },
      requiresApproval: true
    });

    res.json({
      success: true,
      message: 'AI campaign generation started'
    });
  } catch (error) {
    console.error('Error generating campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate campaign'
    });
  }
});

// Social Media Routes
/**
 * @swagger
 * /api/admin/ai-marketing/social/accounts:
 *   get:
 *     summary: Get connected social media accounts
 *     tags: [Social Media]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Social media accounts retrieved successfully
 */
router.get('/social/accounts', async (req: Request, res: Response) => {
  try {
    // Mock data
    const mockAccounts = [
      {
        id: '1',
        platform: 'facebook',
        accountName: 'Doğal Taş Pazaryeri',
        isActive: true,
        followers: 15420,
        engagement: 4.2,
        lastSync: new Date()
      },
      {
        id: '2',
        platform: 'instagram',
        accountName: 'naturalstone_marketplace',
        isActive: true,
        followers: 8930,
        engagement: 6.8,
        lastSync: new Date()
      },
      {
        id: '3',
        platform: 'linkedin',
        accountName: 'Natural Stone Marketplace',
        isActive: true,
        followers: 3240,
        engagement: 3.1,
        lastSync: new Date()
      }
    ];

    res.json({
      success: true,
      data: mockAccounts
    });
  } catch (error) {
    console.error('Error getting social accounts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get social media accounts'
    });
  }
});

/**
 * @swagger
 * /api/admin/ai-marketing/social/generate-content:
 *   post:
 *     summary: Generate AI social media content
 *     tags: [Social Media]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               platforms:
 *                 type: array
 *                 items:
 *                   type: string
 *               contentTheme:
 *                 type: string
 *               productCategory:
 *                 type: string
 *     responses:
 *       200:
 *         description: Content generation started
 */
router.post('/social/generate-content', async (req: Request, res: Response) => {
  try {
    const { platforms, contentTheme, productCategory } = req.body;
    
    orchestrator.addTask({
      type: 'social_content',
      priority: 'medium',
      aiModel: 'social',
      data: {
        action: 'generate_content',
        platforms,
        contentTheme,
        productCategory
      },
      requiresApproval: true
    });

    res.json({
      success: true,
      message: 'AI content generation started'
    });
  } catch (error) {
    console.error('Error generating social content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate social content'
    });
  }
});

export default router;
