import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { authMiddleware, AuthenticatedRequest } from '../../middleware/authMiddleware';
import { asyncHandler } from '../../middleware/errorHandler';
import { z } from 'zod';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const createQuoteRequestSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  specifications: z.array(z.object({
    type: z.enum(['sized', 'area']),
    thickness: z.string().optional(),
    width: z.string().optional(),
    length: z.string().optional(),
    area: z.number().optional(),
    unit: z.string().optional(),
    quantity: z.number().min(1),
    surfaceFinish: z.string().optional(),
    packaging: z.string().optional()
  })),
  deliveryAddress: z.object({
    street: z.string().min(1),
    city: z.string().min(1),
    state: z.string().optional(),
    postalCode: z.string().min(1),
    country: z.string().min(1)
  }),
  notes: z.string().optional(),
  urgency: z.enum(['LOW', 'MEDIUM', 'HIGH']).default('MEDIUM')
});

const createQuoteSchema = z.object({
  quoteRequestId: z.string().min(1),
  unitPrice: z.number().min(0),
  totalPrice: z.number().min(0),
  currency: z.string().default('TRY'),
  validUntil: z.string(),
  deliveryTime: z.string(),
  paymentTerms: z.string(),
  notes: z.string().optional(),
  items: z.array(z.object({
    specificationId: z.string(),
    unitPrice: z.number(),
    quantity: z.number(),
    totalPrice: z.number()
  }))
});

/**
 * @swagger
 * /api/quotes/requests:
 *   get:
 *     summary: Get quote requests
 *     tags: [Quotes]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Quote requests retrieved successfully
 */
router.get('/requests', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const status = req.query.status as string;

  const skip = (page - 1) * limit;
  const where: any = {};

  // Filter based on user role
  if (user.userType === 'customer') {
    where.customerId = user.id;
  } else if (user.userType === 'producer') {
    where.product = {
      producerId: user.id
    };
  }
  // Admin can see all

  if (status) {
    where.status = status;
  }

  const [quoteRequests, total] = await Promise.all([
    prisma.quoteRequest.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        product: {
          include: {
            images: {
              where: { isPrimary: true },
              take: 1
            },
            producer: {
              select: {
                id: true,
                companyName: true
              }
            }
          }
        },
        quotes: {
          include: {
            producer: {
              select: {
                id: true,
                companyName: true
              }
            }
          }
        }
      }
    }),
    prisma.quoteRequest.count({ where })
  ]);

  const totalPages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      quoteRequests,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @swagger
 * /api/quotes/requests:
 *   post:
 *     summary: Create new quote request
 *     tags: [Quotes]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               productId:
 *                 type: string
 *               specifications:
 *                 type: array
 *               deliveryAddress:
 *                 type: object
 *     responses:
 *       201:
 *         description: Quote request created successfully
 */
router.post('/requests', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;

  if (user.userType !== 'customer') {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Only customers can create quote requests',
        statusCode: 403
      }
    });
  }

  const requestData = createQuoteRequestSchema.parse(req.body);

  // Verify product exists
  const product = await prisma.product.findUnique({
    where: { id: requestData.productId },
    include: {
      producer: {
        select: { id: true, companyName: true }
      }
    }
  });

  if (!product) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Product not found',
        statusCode: 404
      }
    });
  }

  // Get user's request count for numbering
  const userRequestCount = await prisma.quoteRequest.count({
    where: { customerId: user.id }
  });

  const requestNumber = `${user.id.slice(-4)}-${(userRequestCount + 1).toString().padStart(3, '0')}`;

  // Create quote request
  const quoteRequest = await prisma.quoteRequest.create({
    data: {
      customerId: user.id,
      productId: requestData.productId,
      requestNumber,
      specifications: requestData.specifications,
      quantity: requestData.specifications[0]?.quantity || 1,
      unit: requestData.specifications[0]?.type === 'area' ? 'm2' : 'pieces',
      deliveryAddress: requestData.deliveryAddress,
      notes: requestData.notes,
      urgency: requestData.urgency,
      status: 'PENDING'
    },
    include: {
      product: {
        include: {
          images: {
            where: { isPrimary: true },
            take: 1
          }
        }
      }
    }
  });

  // Send notification using notification service
  const notificationService = req.app.get('notificationService');
  if (notificationService) {
    await notificationService.notifyQuoteRequest(
      quoteRequest.id,
      requestData.productId,
      user.id
    );
  }

  res.status(201).json({
    success: true,
    data: quoteRequest,
    message: 'Quote request created successfully'
  });
}));

/**
 * @swagger
 * /api/quotes/requests/{id}:
 *   get:
 *     summary: Get quote request by ID
 *     tags: [Quotes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Quote request retrieved successfully
 */
router.get('/requests/:id', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const user = req.user!;

  const where: any = { id };

  // Apply access control
  if (user.userType === 'customer') {
    where.customerId = user.id;
  } else if (user.userType === 'producer') {
    where.product = {
      producerId: user.id
    };
  }

  const quoteRequest = await prisma.quoteRequest.findFirst({
    where,
    include: {
      customer: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          profile: true
        }
      },
      product: {
        include: {
          images: true,
          producer: {
            select: {
              id: true,
              companyName: true,
              profile: true
            }
          }
        }
      },
      quotes: {
        include: {
          producer: {
            select: {
              id: true,
              companyName: true,
              profile: {
                select: {
                  city: true,
                  country: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }
    }
  });

  if (!quoteRequest) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Quote request not found',
        statusCode: 404
      }
    });
  }

  res.json({
    success: true,
    data: quoteRequest
  });
}));

/**
 * @swagger
 * /api/quotes:
 *   post:
 *     summary: Create quote for a quote request (Producer only)
 *     tags: [Quotes]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               quoteRequestId:
 *                 type: string
 *               unitPrice:
 *                 type: number
 *               totalPrice:
 *                 type: number
 *     responses:
 *       201:
 *         description: Quote created successfully
 */
router.post('/', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;

  if (user.userType !== 'producer') {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Only producers can create quotes',
        statusCode: 403
      }
    });
  }

  const quoteData = createQuoteSchema.parse(req.body);

  // Verify quote request exists and belongs to producer's product
  const quoteRequest = await prisma.quoteRequest.findFirst({
    where: {
      id: quoteData.quoteRequestId,
      product: {
        producerId: user.id
      }
    },
    include: {
      customer: true,
      product: true
    }
  });

  if (!quoteRequest) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Quote request not found or not authorized',
        statusCode: 404
      }
    });
  }

  // Check if quote already exists
  const existingQuote = await prisma.quote.findFirst({
    where: {
      quoteRequestId: quoteData.quoteRequestId,
      producerId: user.id
    }
  });

  if (existingQuote) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Quote already exists for this request',
        statusCode: 400
      }
    });
  }

  // Create quote
  const quote = await prisma.quote.create({
    data: {
      quoteNumber: `QUO-${Date.now()}`,
      quoteRequestId: quoteData.quoteRequestId,
      producerId: user.id,
      customerId: quoteRequest.customerId,
      productId: quoteRequest.productId,
      unitPrice: quoteData.unitPrice,
      totalPrice: quoteData.totalPrice,
      currency: quoteData.currency,
      validUntil: new Date(quoteData.validUntil),
      deliveryTimeDays: parseInt(quoteData.deliveryTime) || 30,
      paymentTerms: quoteData.paymentTerms,
      notes: quoteData.notes,
      items: quoteData.items,
      status: 'PENDING'
    },
    include: {
      product: true,
      producer: {
        select: {
          id: true,
          companyName: true
        }
      }
    }
  });

  // Send notification using notification service
  const notificationService = req.app.get('notificationService');
  if (notificationService) {
    await notificationService.notifyQuoteResponse(
      quote.id,
      quoteRequest.customerId,
      user.id
    );
  }

  res.status(201).json({
    success: true,
    data: quote,
    message: 'Quote created successfully'
  });
}));

/**
 * @swagger
 * /api/quotes/{id}/accept:
 *   put:
 *     summary: Accept quote (Customer only)
 *     tags: [Quotes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Quote accepted successfully
 */
router.put('/:id/accept', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const user = req.user!;

  if (user.userType !== 'customer') {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Only customers can accept quotes',
        statusCode: 403
      }
    });
  }

  const quote = await prisma.quote.findFirst({
    where: {
      id,
      customerId: user.id
    },
    include: {
      product: true,
      producer: true
    }
  });

  if (!quote) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Quote not found',
        statusCode: 404
      }
    });
  }

  if (quote.status !== 'PENDING') {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Quote is not in pending status',
        statusCode: 400
      }
    });
  }

  // Check if quote is still valid
  if (quote.validUntil && new Date() > quote.validUntil) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Quote has expired',
        statusCode: 400
      }
    });
  }

  // Accept quote
  const acceptedQuote = await prisma.quote.update({
    where: { id },
    data: { status: 'ACCEPTED' }
  });

  // Create notification for producer
  await prisma.notification.create({
    data: {
      userId: quote.producerId,
      title: 'Quote Accepted',
      message: `Your quote for ${quote.product.name} has been accepted`,
      notificationType: 'QUOTE_ACCEPTED',
      relatedEntityId: quote.id,
      relatedEntityType: 'QUOTE'
    }
  });

  res.json({
    success: true,
    data: acceptedQuote,
    message: 'Quote accepted successfully'
  });
}));

/**
 * @swagger
 * /api/quotes/{id}/reject:
 *   put:
 *     summary: Reject quote (Customer only)
 *     tags: [Quotes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Quote rejected successfully
 */
router.put('/:id/reject', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const user = req.user!;
  const { reason } = req.body;

  if (user.userType !== 'customer') {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Only customers can reject quotes',
        statusCode: 403
      }
    });
  }

  const quote = await prisma.quote.findFirst({
    where: {
      id,
      customerId: user.id
    },
    include: {
      product: true,
      producer: true
    }
  });

  if (!quote) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Quote not found',
        statusCode: 404
      }
    });
  }

  if (quote.status !== 'PENDING') {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Quote is not in pending status',
        statusCode: 400
      }
    });
  }

  // Reject quote
  const rejectedQuote = await prisma.quote.update({
    where: { id },
    data: {
      status: 'REJECTED',
      notes: `${quote.notes || ''}\nRejected: ${reason || 'No reason provided'}`
    }
  });

  // Create notification for producer
  await prisma.notification.create({
    data: {
      userId: quote.producerId,
      title: 'Quote Rejected',
      message: `Your quote for ${quote.product.name} has been rejected`,
      notificationType: 'QUOTE_REJECTED',
      relatedEntityId: quote.id,
      relatedEntityType: 'QUOTE'
    }
  });

  res.json({
    success: true,
    data: rejectedQuote,
    message: 'Quote rejected successfully'
  });
}));

export default router;
