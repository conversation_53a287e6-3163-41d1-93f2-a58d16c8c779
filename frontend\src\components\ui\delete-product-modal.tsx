'use client'

import * as React from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { AlertTriangle, X } from 'lucide-react'

interface DeleteProductModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (reason: string) => void
  productName: string
  isLoading?: boolean
}

export function DeleteProductModal({
  isOpen,
  onClose,
  onConfirm,
  productName,
  isLoading = false
}: DeleteProductModalProps) {
  const [reason, setReason] = React.useState('')
  const [error, setError] = React.useState('')

  const handleSubmit = () => {
    if (!reason.trim()) {
      setError('Silme sebebi zorunludur')
      return
    }

    if (reason.trim().length < 10) {
      setError('Silme sebebi en az 10 karakter olmalıdır')
      return
    }

    onConfirm(reason.trim())
  }

  const handleClose = () => {
    setReason('')
    setError('')
    onClose()
  }

  React.useEffect(() => {
    if (isOpen) {
      setReason('')
      setError('')
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Ürünü Sil</h2>
              <p className="text-sm text-gray-600">Bu işlem geri alınamaz</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isLoading}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-sm text-red-800">
              <strong>"{productName}"</strong> adlı ürünü silmek üzeresiniz. 
              Bu işlem geri alınamaz ve ürün kalıcı olarak silinecektir.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Silme Sebebi <span className="text-red-500">*</span>
            </label>
            <Textarea
              value={reason}
              onChange={(e) => {
                setReason(e.target.value)
                if (error) setError('')
              }}
              placeholder="Ürünü neden siliyorsunuz? (En az 10 karakter)"
              rows={4}
              className="w-full"
              disabled={isLoading}
            />
            {error && (
              <p className="text-sm text-red-600 mt-1">{error}</p>
            )}
          </div>

          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
            <p className="text-sm text-amber-800">
              <strong>Not:</strong> Silme sebebiniz admin paneline gönderilecek ve kayıt altına alınacaktır.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            İptal
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {isLoading ? 'Siliniyor...' : 'Ürünü Sil'}
          </Button>
        </div>
      </div>
    </div>
  )
}
