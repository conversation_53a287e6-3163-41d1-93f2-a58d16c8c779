/**
 * use3DViewer Hook Tests
 * Test suite for the 3D viewer custom hook
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { use3DViewer } from '../use3DViewer';
import { threeDApi } from '../../services/3dApi';
import { 
  AssetQuality, 
  ViewerConfiguration, 
  Asset3D, 
  MaterialDefinition,
  AssetType,
  AssetFormat
} from '../../types/3d';

// Mock the 3D API service
jest.mock('../../services/3dApi', () => ({
  threeDApi: {
    getViewerConfiguration: jest.fn(),
    getProductAssets: jest.fn(),
    getProductMaterials: jest.fn(),
    startViewerSession: jest.fn(),
    updateViewerSession: jest.fn(),
    endViewerSession: jest.fn(),
    getDeviceType: jest.fn(() => 'desktop'),
    getScreenResolution: jest.fn(() => '1920x1080'),
    detectViewerCapabilities: jest.fn(() => ({
      webgl: true,
      webgl2: true,
      webgpu: false,
      maxTextureSize: 4096,
      maxVertexAttributes: 16,
      supportedFormats: ['GLB', 'GLTF', 'JPG', 'PNG', 'WEBP'],
      deviceMemory: 8,
      hardwareConcurrency: 8
    })),
    isWebGLSupported: jest.fn(() => true),
    estimateMemoryUsage: jest.fn(() => 50),
    canLoadAsset: jest.fn(() => true),
    preloadAsset: jest.fn(() => Promise.resolve())
  }
}));

// Mock data
const mockConfiguration: ViewerConfiguration = {
  id: 'config-1',
  productId: 'product-1',
  cameraPosition: { x: 5, y: 5, z: 5 },
  cameraTarget: { x: 0, y: 0, z: 0 },
  cameraFov: 75,
  ambientLightColor: '#ffffff',
  ambientLightIntensity: 0.4,
  directionalLightColor: '#ffffff',
  directionalLightIntensity: 1.0,
  directionalLightPosition: { x: 10, y: 10, z: 5 },
  backgroundType: 'color',
  backgroundColor: '#f0f0f0',
  enableOrbitControls: true,
  enableZoom: true,
  enablePan: true,
  enableRotate: true,
  autoRotate: false,
  autoRotateSpeed: 2.0,
  enableShadows: true,
  shadowMapSize: 1024,
  enableAntialiasing: true,
  pixelRatio: 1.0,
  enableAnnotations: true,
  annotations: [],
  createdAt: new Date(),
  updatedAt: new Date()
};

const mockAssets: Asset3D[] = [
  {
    id: 'asset-1',
    productId: 'product-1',
    name: 'Test Model',
    type: AssetType.MODEL_3D,
    format: AssetFormat.GLB,
    quality: AssetQuality.HIGH,
    fileName: 'test.glb',
    filePath: '/models/test.glb',
    fileSize: 1024000,
    mimeType: 'model/gltf-binary',
    vertices: 10000,
    faces: 3333,
    tags: ['test'],
    downloadCount: 0,
    viewCount: 0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const mockMaterials: MaterialDefinition[] = [
  {
    id: 'material-1',
    name: 'Test Material',
    baseColor: '#ffffff',
    metallic: 0.0,
    roughness: 0.5,
    tilingU: 1.0,
    tilingV: 1.0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const mockSession = {
  id: 'session-1',
  sessionId: 'session-uuid-1',
  productId: 'product-1',
  userId: 'user-1',
  viewDuration: 0,
  interactionCount: 0,
  zoomCount: 0,
  rotationCount: 0,
  annotationViews: 0,
  startedAt: new Date()
};

describe('use3DViewer', () => {
  const mockThreeDApi = threeDApi as jest.Mocked<typeof threeDApi>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock responses
    mockThreeDApi.getViewerConfiguration.mockResolvedValue({
      success: true,
      configuration: mockConfiguration,
      assets: mockAssets,
      materials: mockMaterials
    });
    
    mockThreeDApi.getProductAssets.mockResolvedValue(mockAssets);
    mockThreeDApi.getProductMaterials.mockResolvedValue(mockMaterials);
    
    mockThreeDApi.startViewerSession.mockResolvedValue({
      success: true,
      sessionId: 'session-uuid-1',
      configuration: mockConfiguration,
      capabilities: {
        webgl: true,
        webgl2: true,
        webgpu: false,
        maxTextureSize: 4096,
        maxVertexAttributes: 16,
        supportedFormats: ['GLB', 'GLTF'],
        deviceMemory: 8,
        hardwareConcurrency: 8
      }
    });
    
    mockThreeDApi.updateViewerSession.mockResolvedValue();
    mockThreeDApi.endViewerSession.mockResolvedValue();
  });

  test('should initialize with default state', () => {
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    expect(result.current.configuration).toBeNull();
    expect(result.current.assets).toEqual([]);
    expect(result.current.materials).toEqual([]);
    expect(result.current.session).toBeNull();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  test('should load assets successfully', async () => {
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    await act(async () => {
      await result.current.loadAssets();
    });

    expect(result.current.configuration).toEqual(mockConfiguration);
    expect(result.current.assets).toEqual(mockAssets);
    expect(result.current.materials).toEqual(mockMaterials);
    expect(result.current.error).toBeNull();
  });

  test('should handle load assets error', async () => {
    mockThreeDApi.getViewerConfiguration.mockRejectedValue(
      new Error('Failed to load configuration')
    );

    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    await act(async () => {
      await result.current.loadAssets();
    });

    expect(result.current.error).toBeDefined();
    expect(result.current.error?.message).toBe('Failed to load configuration');
  });

  test('should start session successfully', async () => {
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        userId: 'user-1',
        autoStart: false 
      })
    );

    await act(async () => {
      await result.current.startSession();
    });

    expect(result.current.session).toBeDefined();
    expect(result.current.session?.sessionId).toBe('session-uuid-1');
    expect(result.current.session?.productId).toBe('product-1');
    expect(result.current.session?.userId).toBe('user-1');
  });

  test('should handle start session error', async () => {
    mockThreeDApi.startViewerSession.mockRejectedValue(
      new Error('Failed to start session')
    );

    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    await act(async () => {
      await result.current.startSession();
    });

    expect(result.current.error).toBeDefined();
    expect(result.current.error?.message).toBe('Failed to start session');
  });

  test('should update session successfully', async () => {
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    // First start a session
    await act(async () => {
      await result.current.startSession();
    });

    // Then update it
    await act(async () => {
      await result.current.updateSession({
        viewDuration: 120,
        interactionCount: 5
      });
    });

    expect(mockThreeDApi.updateViewerSession).toHaveBeenCalledWith(
      'session-uuid-1',
      {
        viewDuration: 120,
        interactionCount: 5
      }
    );
  });

  test('should end session successfully', async () => {
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    // First start a session
    await act(async () => {
      await result.current.startSession();
    });

    // Then end it
    await act(async () => {
      await result.current.endSession();
    });

    expect(mockThreeDApi.endViewerSession).toHaveBeenCalledWith('session-uuid-1');
    expect(result.current.session).toBeNull();
  });

  test('should clear error', () => {
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    // Manually set an error for testing
    act(() => {
      (result.current as any).setError({ 
        code: 'TEST_ERROR', 
        message: 'Test error' 
      });
    });

    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBeNull();
  });

  test('should auto-start when enabled', async () => {
    const onLoad = jest.fn();
    
    renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: true,
        onLoad
      })
    );

    await waitFor(() => {
      expect(mockThreeDApi.getViewerConfiguration).toHaveBeenCalled();
      expect(mockThreeDApi.startViewerSession).toHaveBeenCalled();
    });
  });

  test('should call onLoad callback', async () => {
    const onLoad = jest.fn();
    
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false,
        onLoad
      })
    );

    await act(async () => {
      await result.current.loadAssets();
    });

    expect(onLoad).toHaveBeenCalled();
  });

  test('should call onError callback', async () => {
    const onError = jest.fn();
    
    mockThreeDApi.getViewerConfiguration.mockRejectedValue(
      new Error('Test error')
    );

    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false,
        onError
      })
    );

    await act(async () => {
      await result.current.loadAssets();
    });

    expect(onError).toHaveBeenCalledWith(
      expect.objectContaining({
        message: 'Test error'
      })
    );
  });

  test('should call onSessionUpdate callback', async () => {
    const onSessionUpdate = jest.fn();
    
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false,
        onSessionUpdate
      })
    );

    await act(async () => {
      await result.current.startSession();
    });

    await act(async () => {
      await result.current.updateSession({
        viewDuration: 60
      });
    });

    expect(onSessionUpdate).toHaveBeenCalled();
  });

  test('should get optimal quality based on device', () => {
    mockThreeDApi.getDeviceType.mockReturnValue('mobile');
    
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    const optimalQuality = (result.current as any).getOptimalQuality();
    expect(optimalQuality).toBe(AssetQuality.LOW);
  });

  test('should check if assets can be loaded', () => {
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    // Set some assets first
    act(() => {
      (result.current as any).setAssets(mockAssets);
    });

    const canLoad = (result.current as any).canLoadAssets();
    expect(canLoad).toBe(true);
  });

  test('should preload assets', async () => {
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    // Set some assets first
    act(() => {
      (result.current as any).setAssets(mockAssets);
    });

    await act(async () => {
      await (result.current as any).preloadAssets(AssetQuality.HIGH);
    });

    expect(mockThreeDApi.preloadAsset).toHaveBeenCalledWith(
      'asset-1',
      AssetQuality.HIGH
    );
  });

  test('should track interactions', () => {
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    act(() => {
      (result.current as any).trackInteraction('zoom');
    });

    act(() => {
      (result.current as any).trackInteraction('rotation');
    });

    act(() => {
      (result.current as any).trackInteraction('annotation');
    });

    // Interactions are tracked internally and would be sent during session updates
    expect(true).toBe(true); // Placeholder assertion
  });

  test('should handle WebGL not supported', () => {
    mockThreeDApi.isWebGLSupported.mockReturnValue(false);
    
    const { result } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    const canLoad = (result.current as any).canLoadAssets();
    expect(canLoad).toBe(false);
    expect(result.current.error?.code).toBe('WEBGL_NOT_SUPPORTED');
  });

  test('should cleanup on unmount', () => {
    const { result, unmount } = renderHook(() => 
      use3DViewer({ 
        productId: 'product-1',
        autoStart: false 
      })
    );

    // Start a session
    act(async () => {
      await result.current.startSession();
    });

    // Unmount the hook
    unmount();

    // Session should be ended (this would be called in the cleanup effect)
    expect(true).toBe(true); // Placeholder assertion
  });
});
