'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import ReportExportModal from '@/components/admin/ReportExportModal';
import ReportChart, { generateSampleLineData, generateSampleBarData, generateSamplePieData } from '@/components/admin/ReportChart';
import ReportBuilder from '@/components/admin/ReportBuilder';
import ReportTemplates from '@/components/admin/ReportTemplates';
import { useRealtimeKPIs, useSystemHealth } from '@/hooks/useRealtimeReports';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  ShoppingCart, 
  Activity,
  Download,
  RefreshCw,
  Calendar,
  Filter,
  FileText,
  PieChart,
  LineChart,
  BarChart,
  Settings,
  Share,
  Mail,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';

// Types for reports
interface KPIMetric {
  id: string;
  title: string;
  value: number | string;
  previousValue?: number | string;
  changePercentage?: number;
  trend: 'up' | 'down' | 'stable';
  format: 'number' | 'currency' | 'percentage';
  color: 'green' | 'red' | 'blue' | 'yellow';
  icon: string;
}

interface ReportFilter {
  dateRange: string;
  category: string;
  userType: string;
  customStartDate?: string;
  customEndDate?: string;
}

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string;
    borderWidth?: number;
  }[];
}

export default function AdminReportsPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [filters, setFilters] = useState<ReportFilter>({
    dateRange: 'last30days',
    category: 'all',
    userType: 'all'
  });
  const [activeTab, setActiveTab] = useState('financial');
  const [kpiMetrics, setKpiMetrics] = useState<KPIMetric[]>([]);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showReportBuilder, setShowReportBuilder] = useState(false);

  // Realtime data hooks - disabled for now to prevent infinite loops
  // const { kpis, isConnected, lastUpdate } = useRealtimeKPIs();
  // const { health } = useSystemHealth();

  // Mock data for demonstration
  const [isConnected] = useState(true);
  const [lastUpdate] = useState(new Date());
  const [kpis] = useState({});
  const [health] = useState({
    overall: 'healthy' as const,
    details: {
      response_time: { value: 142, status: 'healthy' as const },
      cpu_usage: { value: 45, status: 'healthy' as const },
      memory_usage: { value: 67, status: 'warning' as const }
    }
  });

  // Initialize KPI metrics with static data
  useEffect(() => {
    setKpiMetrics([
      {
        id: 'total_revenue',
        title: 'Toplam Gelir',
        value: 2847500,
        previousValue: 2456000,
        changePercentage: 15.9,
        trend: 'up',
        format: 'currency',
        color: 'green',
        icon: 'DollarSign'
      },
      {
        id: 'commission_earned',
        title: 'Komisyon Geliri',
        value: 284750,
        previousValue: 245600,
        changePercentage: 15.9,
        trend: 'up',
        format: 'currency',
        color: 'blue',
        icon: 'TrendingUp'
      },
      {
        id: 'active_orders',
        title: 'Aktif Siparişler',
        value: 156,
        previousValue: 142,
        changePercentage: 9.9,
        trend: 'up',
        format: 'number',
        color: 'yellow',
        icon: 'ShoppingCart'
      },
      {
        id: 'platform_users',
        title: 'Platform Kullanıcıları',
        value: 1247,
        previousValue: 1189,
        changePercentage: 4.9,
        trend: 'up',
        format: 'number',
        color: 'green',
        icon: 'Users'
      }
    ]);
  }, []); // Empty dependency array to run only once

  const iconMap: { [key: string]: React.ComponentType<any> } = {
    DollarSign,
    TrendingUp,
    TrendingDown,
    ShoppingCart,
    Users,
    Activity,
    BarChart3,
    PieChart,
    LineChart,
    BarChart
  };

  const formatValue = (value: number | string, format: string): string => {
    if (typeof value === 'string') return value;
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('tr-TR', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        }).format(value);
      case 'percentage':
        return `${value}%`;
      case 'number':
        return new Intl.NumberFormat('tr-TR').format(value);
      default:
        return value.toString();
    }
  };

  const getTrendIcon = (trend: string, changePercentage?: number) => {
    if (!changePercentage) return null;
    
    if (trend === 'up') {
      return <TrendingUp className="w-4 h-4 text-green-600" />;
    } else if (trend === 'down') {
      return <TrendingDown className="w-4 h-4 text-red-600" />;
    }
    return <Activity className="w-4 h-4 text-gray-600" />;
  };

  const handleFilterChange = (key: keyof ReportFilter, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleRefreshData = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const handleExportReport = (format: 'pdf' | 'excel' | 'csv') => {
    // Export functionality will be implemented
    console.log(`Exporting report as ${format}`);
  };

  const handleSaveCustomReport = (config: any) => {
    console.log('Saving custom report:', config);
    // Here you would save the report configuration to the backend
  };

  const handlePreviewCustomReport = (config: any) => {
    console.log('Previewing custom report:', config);
    // Here you would generate a preview of the custom report
  };

  const handleCreateReportFromTemplate = (template: any) => {
    console.log('Creating report from template:', template);
    // Here you would generate a report from the template
  };

  const handleEditTemplate = (template: any) => {
    console.log('Editing template:', template);
    // Here you would open the template editor
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Raporlar</h1>
          <p className="text-gray-600 mt-1">
            Platform performansını analiz edin ve detaylı raporlar oluşturun
          </p>
          <div className="flex items-center gap-2 mt-2">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm text-gray-500">
              {isConnected ? 'Canlı veri aktif' : 'Canlı veri bağlantısı yok'}
            </span>
            {lastUpdate && (
              <span className="text-xs text-gray-400">
                • Son güncelleme: {lastUpdate.toLocaleTimeString('tr-TR')}
              </span>
            )}
          </div>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" onClick={handleRefreshData} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Yenileniyor...' : 'Yenile'}
          </Button>
          <Button variant="outline" onClick={() => setShowExportModal(true)}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button onClick={() => setShowExportModal(true)}>
            <Share className="w-4 h-4 mr-2" />
            Paylaş
          </Button>
          <Button onClick={() => setShowReportBuilder(true)}>
            <Settings className="w-4 h-4 mr-2" />
            Özel Rapor
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {kpiMetrics.map((kpi) => {
          const IconComponent = iconMap[kpi.icon];
          return (
            <Card key={kpi.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">{kpi.title}</p>
                    <p className="text-2xl font-bold">
                      {formatValue(kpi.value, kpi.format)}
                    </p>
                    {kpi.changePercentage !== undefined && (
                      <div className="flex items-center space-x-1 text-sm">
                        {getTrendIcon(kpi.trend, kpi.changePercentage)}
                        <span className={
                          kpi.changePercentage > 0 ? 'text-green-600' : 
                          kpi.changePercentage < 0 ? 'text-red-600' : 'text-gray-600'
                        }>
                          {kpi.changePercentage > 0 ? '+' : ''}{kpi.changePercentage}%
                        </span>
                      </div>
                    )}
                  </div>
                  {IconComponent && (
                    <div className={`p-3 rounded-full ${
                      kpi.color === 'green' ? 'bg-green-100 text-green-600' :
                      kpi.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                      kpi.color === 'yellow' ? 'bg-yellow-100 text-yellow-600' :
                      'bg-red-100 text-red-600'
                    }`}>
                      <IconComponent className="w-6 h-6" />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filtreler
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="dateRange">Tarih Aralığı</Label>
              <Select value={filters.dateRange} onValueChange={(value) => handleFilterChange('dateRange', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Tarih seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Bugün</SelectItem>
                  <SelectItem value="yesterday">Dün</SelectItem>
                  <SelectItem value="last7days">Son 7 Gün</SelectItem>
                  <SelectItem value="last30days">Son 30 Gün</SelectItem>
                  <SelectItem value="last90days">Son 90 Gün</SelectItem>
                  <SelectItem value="thisMonth">Bu Ay</SelectItem>
                  <SelectItem value="lastMonth">Geçen Ay</SelectItem>
                  <SelectItem value="thisYear">Bu Yıl</SelectItem>
                  <SelectItem value="custom">Özel Aralık</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Kategori</Label>
              <Select value={filters.category} onValueChange={(value) => handleFilterChange('category', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Kategori seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Kategoriler</SelectItem>
                  <SelectItem value="mermer">Mermer</SelectItem>
                  <SelectItem value="traverten">Traverten</SelectItem>
                  <SelectItem value="granit">Granit</SelectItem>
                  <SelectItem value="oniks">Oniks</SelectItem>
                  <SelectItem value="plaka">Plaka</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="userType">Kullanıcı Tipi</Label>
              <Select value={filters.userType} onValueChange={(value) => handleFilterChange('userType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Kullanıcı tipi seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Kullanıcılar</SelectItem>
                  <SelectItem value="customers">Müşteriler</SelectItem>
                  <SelectItem value="producers">Üreticiler</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button className="w-full" onClick={handleRefreshData}>
                <BarChart3 className="w-4 h-4 mr-2" />
                Rapor Oluştur
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="financial" className="flex items-center gap-2">
            <DollarSign className="w-4 h-4" />
            Finansal
          </TabsTrigger>
          <TabsTrigger value="business" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            İş Performansı
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Kullanıcılar
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Sistem
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            Şablonlar
          </TabsTrigger>
        </TabsList>

        {/* Financial Reports Tab */}
        <TabsContent value="financial" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="w-5 h-5" />
                  Gelir Trendi
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ReportChart
                  type="line"
                  data={generateSampleLineData()}
                  height={250}
                />
              </CardContent>
            </Card>

            {/* Commission Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="w-5 h-5" />
                  Komisyon Dağılımı
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ReportChart
                  type="doughnut"
                  data={{
                    labels: ['m² Komisyonu', 'Ton Komisyonu', 'Premium Üyelik'],
                    datasets: [{
                      label: 'Komisyon Türü',
                      data: [180000, 85000, 19750],
                      backgroundColor: ['#3B82F6', '#10B981', '#F59E0B']
                    }]
                  }}
                  height={250}
                />
              </CardContent>
            </Card>

            {/* Payment Methods */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart className="w-5 h-5" />
                  Ödeme Yöntemleri
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Banka Havalesi</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                      </div>
                      <span className="text-sm text-gray-600">75%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Kredi Kartı</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '20%' }}></div>
                      </div>
                      <span className="text-sm text-gray-600">20%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Akreditif</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '5%' }}></div>
                      </div>
                      <span className="text-sm text-gray-600">5%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Top Earning Products */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  En Karlı Ürünler
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { name: 'Beyaz Mermer', revenue: 125000, orders: 45 },
                    { name: 'Traverten Klasik', revenue: 98000, orders: 38 },
                    { name: 'Granit Siyah', revenue: 87000, orders: 29 },
                    { name: 'Oniks Yeşil', revenue: 76000, orders: 22 },
                    { name: 'Plaka Mermer', revenue: 65000, orders: 18 }
                  ].map((product, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{product.name}</p>
                        <p className="text-sm text-gray-600">{product.orders} sipariş</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-green-600">
                          {formatValue(product.revenue, 'currency')}
                        </p>
                        <Badge variant="secondary" className="text-xs">
                          #{index + 1}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Business Performance Tab */}
        <TabsContent value="business" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quote to Order Conversion */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Teklif-Sipariş Dönüşümü
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ReportChart
                  type="line"
                  data={{
                    labels: ['1. Hafta', '2. Hafta', '3. Hafta', '4. Hafta', '5. Hafta', '6. Hafta'],
                    datasets: [{
                      label: 'Dönüşüm Oranı (%)',
                      data: [23.5, 28.2, 31.8, 29.4, 35.1, 38.7],
                      borderColor: '#10B981',
                      backgroundColor: 'rgba(16, 185, 129, 0.1)',
                      fill: true,
                      tension: 0.4
                    }]
                  }}
                  height={250}
                />
              </CardContent>
            </Card>

            {/* Geographic Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart className="w-5 h-5" />
                  Coğrafi Dağılım
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { country: 'Türkiye', percentage: 35, orders: 156 },
                    { country: 'Almanya', percentage: 22, orders: 98 },
                    { country: 'İtalya', percentage: 18, orders: 78 },
                    { country: 'Fransa', percentage: 12, orders: 54 },
                    { country: 'İspanya', percentage: 8, orders: 36 },
                    { country: 'Diğer', percentage: 5, orders: 23 }
                  ].map((country, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{country.country}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${country.percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600 w-12">{country.percentage}%</span>
                        <span className="text-xs text-gray-500">({country.orders})</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Response Times */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Yanıt Süreleri
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">2.4h</p>
                    <p className="text-sm text-gray-600">Ortalama Teklif Süresi</p>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">18m</p>
                    <p className="text-sm text-gray-600">Ortalama Yanıt Süresi</p>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <p className="text-2xl font-bold text-yellow-600">5.2d</p>
                    <p className="text-sm text-gray-600">Ortalama Teslimat</p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <p className="text-2xl font-bold text-purple-600">94%</p>
                    <p className="text-sm text-gray-600">Müşteri Memnuniyeti</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Producer Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Üretici Performansı
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { name: 'Marmara Taş A.Ş.', score: 98, orders: 45, rating: 4.9 },
                    { name: 'Anadolu Mermer Ltd.', score: 95, orders: 38, rating: 4.8 },
                    { name: 'Ege Doğaltaş San.', score: 92, orders: 32, rating: 4.7 },
                    { name: 'Kapadokya Taş Tic.', score: 89, orders: 28, rating: 4.6 },
                    { name: 'Akdeniz Mermer A.Ş.', score: 87, orders: 25, rating: 4.5 }
                  ].map((producer, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{producer.name}</p>
                        <p className="text-sm text-gray-600">{producer.orders} sipariş • ⭐ {producer.rating}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-green-600">{producer.score}/100</p>
                        <Badge variant="secondary" className="text-xs">
                          #{index + 1}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* User Growth */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Kullanıcı Büyümesi
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ReportChart
                  type="bar"
                  data={{
                    labels: ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran'],
                    datasets: [
                      {
                        label: 'Yeni Müşteriler',
                        data: [45, 52, 48, 61, 58, 65],
                        backgroundColor: '#3B82F6'
                      },
                      {
                        label: 'Yeni Üreticiler',
                        data: [18, 22, 19, 25, 23, 28],
                        backgroundColor: '#10B981'
                      }
                    ]
                  }}
                  height={250}
                />
              </CardContent>
            </Card>

            {/* User Segmentation */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="w-5 h-5" />
                  Kullanıcı Segmentasyonu
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600">847</p>
                      <p className="text-sm text-gray-600">Müşteriler</p>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <p className="text-2xl font-bold text-green-600">400</p>
                      <p className="text-sm text-gray-600">Üreticiler</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Aktif Kullanıcılar (30 gün)</span>
                      <span className="font-medium">892 (71.5%)</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Yeni Kayıtlar (7 gün)</span>
                      <span className="font-medium">58</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Churn Rate</span>
                      <span className="font-medium text-red-600">3.2%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Top Customers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  En Aktif Müşteriler
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { name: 'Global Stone Import Ltd.', orders: 28, volume: 2450, country: 'Almanya' },
                    { name: 'Mediterranean Marble Co.', orders: 24, volume: 2100, country: 'İtalya' },
                    { name: 'European Stone Trading', orders: 22, volume: 1950, country: 'Fransa' },
                    { name: 'Atlantic Natural Stone', orders: 19, volume: 1680, country: 'İspanya' },
                    { name: 'Nordic Stone Solutions', orders: 16, volume: 1420, country: 'Norveç' }
                  ].map((customer, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{customer.name}</p>
                        <p className="text-sm text-gray-600">{customer.country} • {customer.orders} sipariş</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-blue-600">{customer.volume}m²</p>
                        <Badge variant="secondary" className="text-xs">
                          #{index + 1}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* User Activity Heatmap */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Aktivite Haritası
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-7 gap-1 text-xs text-center">
                    <div>Pzt</div><div>Sal</div><div>Çar</div><div>Per</div><div>Cum</div><div>Cmt</div><div>Paz</div>
                  </div>
                  <div className="grid grid-cols-7 gap-1">
                    {Array.from({ length: 28 }, (_, i) => (
                      <div
                        key={i}
                        className={`h-8 rounded ${
                          Math.random() > 0.7 ? 'bg-green-500' :
                          Math.random() > 0.4 ? 'bg-green-300' :
                          Math.random() > 0.2 ? 'bg-green-100' : 'bg-gray-100'
                        }`}
                        title={`${Math.floor(Math.random() * 100)} aktivite`}
                      ></div>
                    ))}
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Az</span>
                    <span>Çok</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* System Tab */}
        <TabsContent value="system" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* System Health */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Sistem Sağlığı
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">API Yanıt Süresi</span>
                    <div className="flex items-center gap-2">
                      {health.details.response_time.status === 'healthy' ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : health.details.response_time.status === 'warning' ? (
                        <AlertCircle className="w-4 h-4 text-yellow-600" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-600" />
                      )}
                      <span className={`text-sm ${
                        health.details.response_time.status === 'healthy' ? 'text-green-600' :
                        health.details.response_time.status === 'warning' ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {Math.round(health.details.response_time.value)}ms
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Uptime</span>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm text-green-600">99.97%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Hata Oranı</span>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm text-green-600">0.03%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Veritabanı Bağlantısı</span>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm text-green-600">Sağlıklı</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Genel Durum</span>
                    <div className="flex items-center gap-2">
                      {health.overall === 'healthy' ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : health.overall === 'warning' ? (
                        <AlertCircle className="w-4 h-4 text-yellow-600" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-600" />
                      )}
                      <span className={`text-sm capitalize ${
                        health.overall === 'healthy' ? 'text-green-600' :
                        health.overall === 'warning' ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {health.overall === 'healthy' ? 'Sağlıklı' :
                         health.overall === 'warning' ? 'Uyarı' : 'Kritik'}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Resource Usage */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart className="w-5 h-5" />
                  Kaynak Kullanımı
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>CPU Kullanımı</span>
                      <span>{Math.round(health.details.cpu_usage.value)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          health.details.cpu_usage.status === 'healthy' ? 'bg-green-600' :
                          health.details.cpu_usage.status === 'warning' ? 'bg-yellow-600' : 'bg-red-600'
                        }`}
                        style={{ width: `${Math.round(health.details.cpu_usage.value)}%` }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Memory Kullanımı</span>
                      <span>{Math.round(health.details.memory_usage.value)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          health.details.memory_usage.status === 'healthy' ? 'bg-green-600' :
                          health.details.memory_usage.status === 'warning' ? 'bg-yellow-600' : 'bg-red-600'
                        }`}
                        style={{ width: `${Math.round(health.details.memory_usage.value)}%` }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Disk Kullanımı</span>
                      <span>23%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '23%' }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Network I/O</span>
                      <span>89%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-red-600 h-2 rounded-full" style={{ width: '89%' }}></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Security Alerts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="w-5 h-5" />
                  Güvenlik Uyarıları
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { type: 'warning', message: '5 başarısız giriş denemesi tespit edildi', time: '2 saat önce' },
                    { type: 'info', message: 'SSL sertifikası 30 gün içinde yenilenecek', time: '1 gün önce' },
                    { type: 'success', message: 'Güvenlik taraması tamamlandı', time: '2 gün önce' },
                    { type: 'warning', message: 'Şüpheli API çağrısı tespit edildi', time: '3 gün önce' }
                  ].map((alert, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                      {alert.type === 'warning' && <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5" />}
                      {alert.type === 'info' && <Activity className="w-4 h-4 text-blue-600 mt-0.5" />}
                      {alert.type === 'success' && <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />}
                      <div className="flex-1">
                        <p className="text-sm font-medium">{alert.message}</p>
                        <p className="text-xs text-gray-500">{alert.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* API Usage */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  API Kullanım İstatistikleri
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { endpoint: '/api/products', calls: 15420, avgTime: '145ms' },
                    { endpoint: '/api/quotes', calls: 8930, avgTime: '267ms' },
                    { endpoint: '/api/orders', calls: 6540, avgTime: '198ms' },
                    { endpoint: '/api/users', calls: 4320, avgTime: '89ms' },
                    { endpoint: '/api/admin', calls: 2180, avgTime: '312ms' }
                  ].map((api, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-mono text-sm">{api.endpoint}</p>
                        <p className="text-xs text-gray-600">{api.calls.toLocaleString()} çağrı</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{api.avgTime}</p>
                        <Badge variant="secondary" className="text-xs">
                          Ort. Süre
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Templates Tab */}
        <TabsContent value="templates" className="space-y-6">
          <ReportTemplates
            onCreateReport={handleCreateReportFromTemplate}
            onEditTemplate={handleEditTemplate}
          />
        </TabsContent>
      </Tabs>

      {/* Export Modal */}
      <ReportExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        reportType={activeTab}
      />

      {/* Report Builder Modal */}
      <ReportBuilder
        isOpen={showReportBuilder}
        onClose={() => setShowReportBuilder(false)}
        onSave={handleSaveCustomReport}
        onPreview={handlePreviewCustomReport}
      />
    </div>
  );
}
