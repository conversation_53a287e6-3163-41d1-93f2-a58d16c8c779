// RFC-301: Anonymous Bidding API Routes
import express from 'express';
import { BiddingRedisService } from '../../services/redis/BiddingRedisService';
import { AnonymousIdService } from '../../services/redis/AnonymousIdService';
import { BiddingNotificationService } from '../../services/redis/BiddingNotificationService';
import { EscrowPaymentService } from '../../services/payment/EscrowPaymentService';
import { CommissionCalculationService } from '../../services/payment/CommissionCalculationService';

const router = express.Router();

// Initialize services
const biddingService = new BiddingRedisService();
const anonymousIdService = new AnonymousIdService();
const notificationService = new BiddingNotificationService();
const escrowService = new EscrowPaymentService();
const commissionService = new CommissionCalculationService();

// Create anonymous bid request
router.post('/requests', async (req, res) => {
  try {
    const { customerId, title, description, specifications, quantity, unit, deliveryAddress, bidDeadline } = req.body;

    // Generate anonymous ID for bid request
    const anonymousId = `REQ_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create bid request in database (would use Prisma)
    const bidRequest = {
      id: `bid-request-${Date.now()}`,
      customerId,
      anonymousId,
      title,
      description,
      specifications,
      quantity,
      unit,
      deliveryAddress,
      bidDeadline: new Date(bidDeadline),
      status: 'ACTIVE'
    };

    // Add to Redis for tracking
    await biddingService.addActiveBidRequest(bidRequest.id, bidRequest.bidDeadline);

    // Notify matching producers
    await notificationService.notifyProducersOfNewRequest(bidRequest as any);

    res.status(201).json({
      success: true,
      data: {
        id: bidRequest.anonymousId,
        title: bidRequest.title,
        deadline: bidRequest.bidDeadline,
        status: bidRequest.status
      }
    });
  } catch (error) {
    console.error('Error creating bid request:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Submit anonymous bid
router.post('/requests/:requestId/bids', async (req, res) => {
  try {
    const { requestId } = req.params;
    const { producerId, price, specifications, deliveryTime, notes } = req.body;

    // Generate anonymous bid ID
    const bidId = `bid-${Date.now()}`;
    const anonymousBidId = await anonymousIdService.generateAnonymousBidId(bidId);
    const anonymousProducerId = await anonymousIdService.generateAnonymousProducerId(producerId, requestId);

    // Create bid in database (would use Prisma)
    const bid = {
      id: bidId,
      anonymousId: anonymousBidId,
      bidRequestId: requestId,
      producerId,
      anonymousProducerId,
      price,
      specifications,
      deliveryTime,
      notes,
      status: 'SUBMITTED',
      submittedAt: new Date()
    };

    // Update competition data
    await biddingService.updateBidCompetition(requestId, {
      totalBids: 1, // Would get actual count from database
      lowestPrice: price,
      highestPrice: price,
      averagePrice: price
    });

    // Notify customer of new bid
    const customerId = 'customer-123'; // Would get from bid request
    await notificationService.notifyCustomerOfNewBid(requestId, customerId);

    res.status(201).json({
      success: true,
      data: {
        id: anonymousBidId,
        anonymousProducerId,
        price,
        deliveryTime,
        submittedAt: bid.submittedAt
      }
    });
  } catch (error) {
    console.error('Error submitting bid:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get anonymous bids for a request
router.get('/requests/:requestId/bids', async (req, res) => {
  try {
    const { requestId } = req.params;

    // Get competition data
    const competition = await biddingService.getBidCompetition(requestId);

    // Get anonymous bids (would query database)
    const anonymousBids = [
      {
        id: 'BID_12345678',
        anonymousProducerId: 'PRODUCER_ABC123',
        price: 1250,
        deliveryTime: 30,
        submittedAt: new Date(),
        ranking: 1
      }
    ];

    res.json({
      success: true,
      data: {
        competition,
        bids: anonymousBids
      }
    });
  } catch (error) {
    console.error('Error getting bids:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Select winning bid and initiate escrow
router.post('/requests/:requestId/select-bid', async (req, res) => {
  try {
    const { requestId } = req.params;
    const { selectedBidId, customerId } = req.body;

    // Calculate commission
    const orderData = {
      items: [
        { id: '1', productId: 'prod-1', quantity: 100, unit: 'm2' as const, pricePerUnit: 50, totalPrice: 5000 }
      ],
      unit: 'm2' as const,
      totalQuantity: 100
    };

    const commission = commissionService.calculateCommission(orderData);

    // Initialize escrow payment
    const escrowPayment = await escrowService.initializeEscrowPayment({
      bidRequestId: requestId,
      selectedBidId,
      customerId,
      totalAmount: 5000
    });

    res.json({
      success: true,
      data: {
        escrowPayment: {
          id: escrowPayment.id,
          amount: escrowPayment.amount,
          totalAmount: escrowPayment.totalAmount,
          expiresAt: escrowPayment.expiresAt
        },
        commission
      }
    });
  } catch (error) {
    console.error('Error selecting bid:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Upload bank transfer receipt
router.post('/escrow/:escrowId/receipt', async (req, res) => {
  try {
    const { escrowId } = req.params;
    const { receiptUrl, bankReference, uploadedBy } = req.body;

    await escrowService.processBankTransferReceipt(escrowId, {
      receiptUrl,
      bankReference,
      uploadedBy
    });

    res.json({
      success: true,
      message: 'Receipt uploaded successfully. Awaiting admin verification.'
    });
  } catch (error) {
    console.error('Error uploading receipt:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Admin verify payment
router.post('/escrow/:escrowId/verify', async (req, res) => {
  try {
    const { escrowId } = req.params;
    const { approved, adminId } = req.body;

    await escrowService.adminVerifyPayment(escrowId, adminId, approved);

    res.json({
      success: true,
      message: approved ? 'Payment verified successfully' : 'Payment rejected'
    });
  } catch (error) {
    console.error('Error verifying payment:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get user notifications
router.get('/notifications/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 50 } = req.query;

    const notifications = await notificationService.getUserNotifications(userId, Number(limit));

    res.json({
      success: true,
      data: notifications
    });
  } catch (error) {
    console.error('Error getting notifications:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Mark notifications as read
router.post('/notifications/:userId/read', async (req, res) => {
  try {
    const { userId } = req.params;
    const { count } = req.body;

    await notificationService.markNotificationsAsRead(userId, count);

    res.json({
      success: true,
      message: 'Notifications marked as read'
    });
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get commission statistics
router.get('/commission/stats', async (req, res) => {
  try {
    const stats = await commissionService.getCommissionStatistics();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error getting commission stats:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

export default router;
