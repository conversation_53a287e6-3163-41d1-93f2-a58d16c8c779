/**
 * Viewer Controller
 * Handles HTTP requests for 3D viewer functionality
 */

import { Request, Response } from 'express';
import { ViewerService } from '../services/ViewerService';

const viewerService = new ViewerService();

export class ViewerController {
  /**
   * Get viewer configuration
   */
  static async getConfiguration(req: Request, res: Response) {
    try {
      const { productId } = req.params;
      const configuration = await viewerService.getViewerConfiguration(productId);

      res.json({
        success: true,
        data: configuration
      });
    } catch (error) {
      console.error('Get configuration error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get viewer configuration'
      });
    }
  }

  /**
   * Update viewer configuration
   */
  static async updateConfiguration(req: Request, res: Response) {
    try {
      const { productId } = req.params;
      const updates = req.body;

      const configuration = await viewerService.updateViewerConfiguration(productId, updates);

      res.json({
        success: true,
        data: configuration
      });
    } catch (error) {
      console.error('Update configuration error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update viewer configuration'
      });
    }
  }

  /**
   * Start viewer session
   */
  static async startSession(req: Request, res: Response) {
    try {
      const { productId, deviceInfo } = req.body;
      const userId = req.user?.id; // Assuming auth middleware sets req.user

      const session = await viewerService.startViewerSession(productId, userId, deviceInfo);

      res.json({
        success: true,
        data: {
          sessionId: session.sessionId,
          session
        }
      });
    } catch (error) {
      console.error('Start session error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to start viewer session'
      });
    }
  }

  /**
   * Update viewer session
   */
  static async updateSession(req: Request, res: Response) {
    try {
      const { sessionId } = req.params;
      const updates = req.body;

      await viewerService.updateViewerSession(sessionId, updates);

      res.json({
        success: true,
        message: 'Session updated successfully'
      });
    } catch (error) {
      console.error('Update session error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update session'
      });
    }
  }

  /**
   * End viewer session
   */
  static async endSession(req: Request, res: Response) {
    try {
      const { sessionId } = req.params;

      await viewerService.endViewerSession(sessionId);

      res.json({
        success: true,
        message: 'Session ended successfully'
      });
    } catch (error) {
      console.error('End session error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to end session'
      });
    }
  }

  /**
   * Get viewer analytics
   */
  static async getAnalytics(req: Request, res: Response) {
    try {
      const { productId, startDate, endDate } = req.query;

      const analytics = await viewerService.getViewerAnalytics(
        productId as string,
        startDate ? new Date(startDate as string) : undefined,
        endDate ? new Date(endDate as string) : undefined
      );

      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      console.error('Get analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get analytics'
      });
    }
  }

  /**
   * Add annotation
   */
  static async addAnnotation(req: Request, res: Response) {
    try {
      const { productId } = req.params;
      const annotation = req.body;

      const newAnnotation = await viewerService.addAnnotation(productId, annotation);

      res.json({
        success: true,
        data: newAnnotation
      });
    } catch (error) {
      console.error('Add annotation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to add annotation'
      });
    }
  }

  /**
   * Update annotation
   */
  static async updateAnnotation(req: Request, res: Response) {
    try {
      const { productId, annotationId } = req.params;
      const updates = req.body;

      const annotation = await viewerService.updateAnnotation(productId, annotationId, updates);

      res.json({
        success: true,
        data: annotation
      });
    } catch (error) {
      console.error('Update annotation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update annotation'
      });
    }
  }

  /**
   * Delete annotation
   */
  static async deleteAnnotation(req: Request, res: Response) {
    try {
      const { productId, annotationId } = req.params;

      await viewerService.deleteAnnotation(productId, annotationId);

      res.json({
        success: true,
        message: 'Annotation deleted successfully'
      });
    } catch (error) {
      console.error('Delete annotation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete annotation'
      });
    }
  }
}

export default ViewerController;
