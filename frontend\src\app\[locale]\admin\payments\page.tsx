'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DollarSign,
  Clock,
  Shield,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  CreditCard,
  Banknote,
  PieChart,
  BarChart3,
  Users,
  Building,
  Package,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react'

// Mock data - gerçek API'den gelecek
const mockPaymentStats = {
  totalRevenue: 125000,
  pendingPayments: 15,
  escrowBalance: 45000,
  commissionEarned: 8500,
  refundsProcessed: 3,
  fraudAlerts: 2,
  revenueGrowth: 12.5,
  pendingGrowth: -8.3,
  escrowGrowth: 15.7,
  commissionGrowth: 18.2
}

const mockRecentTransactions = [
  {
    id: 'pay_001',
    type: 'bank_transfer',
    amount: 15000,
    status: 'pending_verification',
    customer: 'Yapı İnşaat Ltd.',
    date: '2024-01-15',
    receiptUrl: '/receipts/receipt_001.pdf'
  },
  {
    id: 'pay_002',
    type: 'escrow_release',
    amount: 8500,
    status: 'completed',
    customer: 'Mimar Yapı A.Ş.',
    date: '2024-01-14',
    producer: 'Afyon Mermer A.Ş.'
  },
  {
    id: 'pay_003',
    type: 'commission',
    amount: 450,
    status: 'completed',
    customer: 'Dekor İnşaat',
    date: '2024-01-14',
    commission: true
  }
]

const mockFraudAlerts = [
  {
    id: 'fraud_001',
    type: 'suspicious_amount',
    severity: 'high',
    description: 'Olağandışı yüksek tutar: $50,000',
    paymentId: 'pay_045',
    customer: 'Şüpheli Firma Ltd.',
    date: '2024-01-15'
  },
  {
    id: 'fraud_002',
    type: 'duplicate_transaction',
    severity: 'medium',
    description: 'Aynı makbuz 2 kez yüklendi',
    paymentId: 'pay_046',
    customer: 'Test İnşaat A.Ş.',
    date: '2024-01-14'
  }
]

const quickActions = [
  {
    title: 'Bekleyen Makbuzlar',
    description: 'Onay bekleyen banka havalesi makbuzları',
    count: 15,
    href: '/admin/payments/bank-transfers',
    icon: Banknote,
    color: 'bg-orange-500',
    urgent: true
  },
  {
    title: 'Escrow Yönetimi',
    description: 'Emanet hesap işlemleri',
    count: 8,
    href: '/admin/payments/escrow',
    icon: Shield,
    color: 'bg-blue-500',
    urgent: false
  },
  {
    title: 'Fraud Uyarıları',
    description: 'Şüpheli işlem bildirimleri',
    count: 2,
    href: '/admin/payments/fraud-detection',
    icon: AlertTriangle,
    color: 'bg-red-500',
    urgent: true
  },
  {
    title: 'Komisyon Takibi',
    description: 'Platform komisyon analitiği',
    count: null,
    href: '/admin/payments/commission',
    icon: TrendingUp,
    color: 'bg-green-500',
    urgent: false
  }
]

export default function AdminPaymentsPage() {
  const [isLoading, setIsLoading] = useState(false)

  const handleRefresh = async () => {
    setIsLoading(true)
    // API çağrısı simülasyonu
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsLoading(false)
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Ödeme Yönetimi</h1>
          <p className="text-gray-600 mt-2">
            Platform ödemelerini, escrow hesaplarını ve komisyonları yönetin
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
            className="flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Yenile</span>
          </Button>
          <Button className="flex items-center space-x-2">
            <Download className="h-4 w-4" />
            <span>Rapor İndir</span>
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Toplam Gelir</p>
              <p className="text-2xl font-bold text-gray-900">
                ${mockPaymentStats.totalRevenue.toLocaleString()}
              </p>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-600 ml-1">
                  +{mockPaymentStats.revenueGrowth}%
                </span>
              </div>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Bekleyen Ödemeler</p>
              <p className="text-2xl font-bold text-gray-900">
                {mockPaymentStats.pendingPayments}
              </p>
              <div className="flex items-center mt-2">
                <ArrowDownRight className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-600 ml-1">
                  {mockPaymentStats.pendingGrowth}%
                </span>
              </div>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <Clock className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Escrow Bakiyesi</p>
              <p className="text-2xl font-bold text-gray-900">
                ${mockPaymentStats.escrowBalance.toLocaleString()}
              </p>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-600 ml-1">
                  +{mockPaymentStats.escrowGrowth}%
                </span>
              </div>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Kazanılan Komisyon</p>
              <p className="text-2xl font-bold text-gray-900">
                ${mockPaymentStats.commissionEarned.toLocaleString()}
              </p>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-600 ml-1">
                  +{mockPaymentStats.commissionGrowth}%
                </span>
              </div>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickActions.map((action) => (
          <Link key={action.title} href={action.href}>
            <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer group">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 ${action.color} rounded-lg`}>
                      <action.icon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 group-hover:text-blue-600">
                        {action.title}
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {action.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between mt-4">
                    {action.count !== null && (
                      <div className="flex items-center space-x-2">
                        <span className="text-2xl font-bold text-gray-900">
                          {action.count}
                        </span>
                        {action.urgent && (
                          <Badge variant="destructive" className="text-xs">
                            Acil
                          </Badge>
                        )}
                      </div>
                    )}
                    <ArrowUpRight className="h-4 w-4 text-gray-400 group-hover:text-blue-600" />
                  </div>
                </div>
              </div>
            </Card>
          </Link>
        ))}
      </div>

      {/* Recent Transactions & Fraud Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Transactions */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Son İşlemler</h2>
            <Link href="/admin/payments/history">
              <Button variant="outline" size="sm">
                Tümünü Gör
              </Button>
            </Link>
          </div>
          <div className="space-y-4">
            {mockRecentTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${
                    transaction.type === 'bank_transfer' ? 'bg-blue-100' :
                    transaction.type === 'escrow_release' ? 'bg-green-100' :
                    'bg-purple-100'
                  }`}>
                    {transaction.type === 'bank_transfer' ? (
                      <Banknote className={`h-4 w-4 ${
                        transaction.type === 'bank_transfer' ? 'text-blue-600' :
                        transaction.type === 'escrow_release' ? 'text-green-600' :
                        'text-purple-600'
                      }`} />
                    ) : transaction.type === 'escrow_release' ? (
                      <Shield className="h-4 w-4 text-green-600" />
                    ) : (
                      <TrendingUp className="h-4 w-4 text-purple-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{transaction.customer}</p>
                    <p className="text-sm text-gray-600">
                      {transaction.type === 'bank_transfer' ? 'Banka Havalesi' :
                       transaction.type === 'escrow_release' ? 'Escrow Serbest Bırakma' :
                       'Platform Komisyonu'}
                    </p>
                    <p className="text-xs text-gray-500">{transaction.date}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">
                    ${transaction.amount.toLocaleString()}
                  </p>
                  <Badge
                    variant={transaction.status === 'completed' ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {transaction.status === 'completed' ? 'Tamamlandı' :
                     transaction.status === 'pending_verification' ? 'Onay Bekliyor' :
                     'İşleniyor'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Fraud Alerts */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Fraud Uyarıları</h2>
            <Link href="/admin/payments/fraud-detection">
              <Button variant="outline" size="sm">
                Tümünü Gör
              </Button>
            </Link>
          </div>
          <div className="space-y-4">
            {mockFraudAlerts.length > 0 ? (
              mockFraudAlerts.map((alert) => (
                <div key={alert.id} className="flex items-start space-x-3 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className={`p-2 rounded-full ${
                    alert.severity === 'high' ? 'bg-red-100' :
                    alert.severity === 'medium' ? 'bg-yellow-100' :
                    'bg-gray-100'
                  }`}>
                    <AlertTriangle className={`h-4 w-4 ${
                      alert.severity === 'high' ? 'text-red-600' :
                      alert.severity === 'medium' ? 'text-yellow-600' :
                      'text-gray-600'
                    }`} />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <p className="font-medium text-gray-900">{alert.customer}</p>
                      <Badge
                        variant={alert.severity === 'high' ? 'destructive' : 'secondary'}
                        className="text-xs"
                      >
                        {alert.severity === 'high' ? 'Yüksek Risk' :
                         alert.severity === 'medium' ? 'Orta Risk' :
                         'Düşük Risk'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{alert.description}</p>
                    <p className="text-xs text-gray-500 mt-2">{alert.date}</p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-3" />
                <p className="text-gray-600">Şu anda aktif fraud uyarısı bulunmuyor</p>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Payment Methods Overview */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Ödeme Yöntemleri Dağılımı</h2>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filtrele
            </Button>
            <Button variant="outline" size="sm">
              <Calendar className="h-4 w-4 mr-2" />
              Bu Ay
            </Button>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <CreditCard className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">65%</p>
            <p className="text-sm text-gray-600">Banka Havalesi</p>
            <p className="text-xs text-gray-500 mt-1">$81,250</p>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <Shield className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">30%</p>
            <p className="text-sm text-gray-600">Escrow</p>
            <p className="text-xs text-gray-500 mt-1">$37,500</p>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <DollarSign className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">5%</p>
            <p className="text-sm text-gray-600">Diğer</p>
            <p className="text-xs text-gray-500 mt-1">$6,250</p>
          </div>
        </div>
      </Card>
    </div>
  )
}