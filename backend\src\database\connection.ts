import { PrismaClient } from '@prisma/client';
import { redisManager } from '../services/redis/RedisManager';

// Prisma Client instance
export const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  errorFormat: 'pretty',
});

// Redis Manager instance
export const redis = redisManager;

// Database connection function
export async function connectDatabase(): Promise<void> {
  try {
    // Test Prisma connection
    await prisma.$connect();
    console.log('✅ PostgreSQL connected successfully');

    // Try Redis connection (optional)
    await redisManager.connect();
    if (redisManager.isRedisConnected()) {
      console.log('✅ Redis connected successfully');
    } else {
      console.log('⚠️ Redis disabled or connection failed - using fallback mode');
    }

  } catch (error) {
    console.error('❌ Database connection failed:', error);
    console.log('⚠️ Continuing without database - API will have limited functionality');
    // Don't throw error - continue without database for testing
  }
}

// Graceful shutdown function
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    console.log('✅ PostgreSQL disconnected');

    redis.disconnect();
    console.log('✅ Redis disconnected');
  } catch (error) {
    console.error('❌ Error during database disconnection:', error);
    throw error;
  }
}

// Health check function
export async function checkDatabaseHealth(): Promise<{
  postgres: boolean;
  redis: boolean;
}> {
  const health = {
    postgres: false,
    redis: false // Always false since Redis is disabled
  };

  try {
    // Check PostgreSQL
    await prisma.$queryRaw`SELECT 1`;
    health.postgres = true;
  } catch (error) {
    console.error('PostgreSQL health check failed:', error);
  }

  // Redis disabled - skip check
  console.log('Redis health check skipped (disabled)');

  return health;
}

// Cache helper functions - Redis disabled
export class CacheService {
  static async get<T>(key: string): Promise<T | null> {
    // Redis disabled - return null
    return null;
  }

  static async set(key: string, value: any, ttlSeconds?: number): Promise<boolean> {
    // Redis disabled - return false
    return false;
  }

  static async del(key: string): Promise<boolean> {
    // Redis disabled - return false
    return false;
  }

  static async exists(key: string): Promise<boolean> {
    // Redis disabled - return false
    return false;
  }

  static async flush(): Promise<boolean> {
    // Redis disabled - return false
    return false;
  }
}

// Session management
export class SessionService {
  static async createSession(userId: string, sessionData: any, ttlSeconds: number = 86400): Promise<string> {
    const sessionId = `session:${userId}:${Date.now()}`;
    await CacheService.set(sessionId, sessionData, ttlSeconds);
    return sessionId;
  }

  static async getSession<T>(sessionId: string): Promise<T | null> {
    return await CacheService.get<T>(sessionId);
  }

  static async destroySession(sessionId: string): Promise<boolean> {
    return await CacheService.del(sessionId);
  }

  static async refreshSession(sessionId: string, ttlSeconds: number = 86400): Promise<boolean> {
    try {
      // Redis session refresh temporarily disabled
      // await redis.safeExpire(sessionId, ttlSeconds);
      return true;
    } catch (error) {
      console.error('Session refresh error:', error);
      return false;
    }
  }
}
