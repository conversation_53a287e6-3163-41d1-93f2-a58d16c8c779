#!/bin/bash

# Natural Stone Marketplace - Security Hardening Script
# Implements security best practices for production deployment

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔒 Starting security hardening for Natural Stone Marketplace${NC}"

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 1. Update system packages
print_status "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# 2. Install security tools
print_status "Installing security tools..."
sudo apt install -y fail2ban ufw rkhunter chkrootkit logwatch

# 3. Configure firewall (UFW)
print_status "Configuring firewall..."
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH (change port if needed)
sudo ufw allow 22/tcp

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow MySQL (only from localhost)
sudo ufw allow from 127.0.0.1 to any port 3306

# Allow Redis (only from localhost)
sudo ufw allow from 127.0.0.1 to any port 6379

# Enable firewall
sudo ufw --force enable

print_status "Firewall configured and enabled"

# 4. Configure Fail2Ban
print_status "Configuring Fail2Ban..."
sudo tee /etc/fail2ban/jail.local > /dev/null << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
backend = systemd

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 2

[natural-stone-marketplace]
enabled = true
filter = natural-stone-marketplace
port = http,https
logpath = /var/log/natural-stone-marketplace/error.log
maxretry = 5
EOF

# Create custom filter for application
sudo tee /etc/fail2ban/filter.d/natural-stone-marketplace.conf > /dev/null << EOF
[Definition]
failregex = ^.*\[error\].*client: <HOST>.*$
            ^.*authentication failed.*client: <HOST>.*$
            ^.*invalid credentials.*client: <HOST>.*$
ignoreregex =
EOF

sudo systemctl enable fail2ban
sudo systemctl restart fail2ban

print_status "Fail2Ban configured and started"

# 5. Secure SSH configuration
print_status "Hardening SSH configuration..."
sudo cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

sudo tee -a /etc/ssh/sshd_config > /dev/null << EOF

# Security hardening
Protocol 2
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
PermitEmptyPasswords no
ChallengeResponseAuthentication no
UsePAM yes
X11Forwarding no
PrintMotd no
ClientAliveInterval 300
ClientAliveCountMax 2
MaxAuthTries 3
MaxSessions 2
LoginGraceTime 60
EOF

sudo systemctl restart sshd
print_status "SSH hardened and restarted"

# 6. Secure MySQL/MariaDB
print_status "Hardening MySQL configuration..."
sudo tee -a /etc/mysql/mysql.conf.d/mysqld.cnf > /dev/null << EOF

# Security settings
bind-address = 127.0.0.1
skip-networking = 0
local-infile = 0
symbolic-links = 0

# Performance and security
max_connections = 100
connect_timeout = 10
wait_timeout = 600
max_allowed_packet = 64M
thread_cache_size = 128
sort_buffer_size = 4M
bulk_insert_buffer_size = 16M
tmp_table_size = 32M
max_heap_table_size = 32M

# Logging
log_error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
EOF

sudo systemctl restart mysql
print_status "MySQL hardened and restarted"

# 7. Set up file permissions
print_status "Setting secure file permissions..."
sudo chown -R deploy:deploy /var/www/natural-stone-marketplace
sudo find /var/www/natural-stone-marketplace -type d -exec chmod 755 {} \;
sudo find /var/www/natural-stone-marketplace -type f -exec chmod 644 {} \;
sudo chmod 600 /var/www/natural-stone-marketplace/backend/.env
sudo chmod +x /var/www/natural-stone-marketplace/scripts/*.sh

# Secure log files
sudo chown -R deploy:deploy /var/log/natural-stone-marketplace
sudo chmod 750 /var/log/natural-stone-marketplace
sudo find /var/log/natural-stone-marketplace -type f -exec chmod 640 {} \;

print_status "File permissions secured"

# 8. Configure automatic security updates
print_status "Configuring automatic security updates..."
sudo apt install -y unattended-upgrades

sudo tee /etc/apt/apt.conf.d/50unattended-upgrades > /dev/null << EOF
Unattended-Upgrade::Allowed-Origins {
    "\${distro_id}:\${distro_codename}-security";
    "\${distro_id}ESMApps:\${distro_codename}-apps-security";
    "\${distro_id}ESM:\${distro_codename}-infra-security";
};

Unattended-Upgrade::Package-Blacklist {
};

Unattended-Upgrade::DevRelease "false";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
Unattended-Upgrade::Automatic-Reboot-Time "02:00";
EOF

sudo systemctl enable unattended-upgrades
print_status "Automatic security updates configured"

# 9. Install and configure intrusion detection
print_status "Setting up intrusion detection..."
sudo tee /etc/rkhunter.conf.local > /dev/null << EOF
UPDATE_MIRRORS=1
MIRRORS_MODE=0
WEB_CMD=""
DISABLE_TESTS="suspscan hidden_procs deleted_files packet_cap_apps apps"
EOF

# Schedule rkhunter
(crontab -l 2>/dev/null; echo "0 3 * * * /usr/bin/rkhunter --update --quiet") | crontab -
(crontab -l 2>/dev/null; echo "0 4 * * * /usr/bin/rkhunter --check --quiet --report-warnings-only") | crontab -

print_status "Intrusion detection configured"

# 10. Set up log monitoring
print_status "Setting up log monitoring..."
sudo tee /etc/logwatch/conf/logwatch.conf > /dev/null << EOF
LogDir = /var/log
TmpDir = /var/cache/logwatch
MailTo = <EMAIL>
MailFrom = <EMAIL>
Print = No
Save = /var/cache/logwatch
Range = yesterday
Detail = Med
Service = All
mailer = "/usr/sbin/sendmail -t"
EOF

# Schedule logwatch
(crontab -l 2>/dev/null; echo "0 6 * * * /usr/sbin/logwatch --output mail") | crontab -

print_status "Log monitoring configured"

# 11. Secure kernel parameters
print_status "Hardening kernel parameters..."
sudo tee -a /etc/sysctl.conf > /dev/null << EOF

# Security hardening
net.ipv4.ip_forward = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.secure_redirects = 0
net.ipv4.conf.default.secure_redirects = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0
net.ipv4.conf.all.log_martians = 1
net.ipv4.conf.default.log_martians = 1
net.ipv4.icmp_echo_ignore_broadcasts = 1
net.ipv4.icmp_ignore_bogus_error_responses = 1
net.ipv4.tcp_syncookies = 1
kernel.dmesg_restrict = 1
kernel.kptr_restrict = 2
fs.suid_dumpable = 0
EOF

sudo sysctl -p
print_status "Kernel parameters hardened"

# 12. Create security audit script
print_status "Creating security audit script..."
cat > /var/www/natural-stone-marketplace/scripts/security-audit.sh << 'EOF'
#!/bin/bash

# Security audit script
AUDIT_LOG="/var/log/natural-stone-marketplace/security-audit.log"
DATE=$(date)

echo "=== Security Audit Report - $DATE ===" > $AUDIT_LOG

# Check for failed login attempts
echo "Failed login attempts in last 24 hours:" >> $AUDIT_LOG
grep "Failed password" /var/log/auth.log | grep "$(date +%b\ %d)" | wc -l >> $AUDIT_LOG

# Check for suspicious network connections
echo "Suspicious network connections:" >> $AUDIT_LOG
netstat -tuln | grep -E ":22|:80|:443|:3000|:3001|:3306" >> $AUDIT_LOG

# Check file permissions on sensitive files
echo "Checking sensitive file permissions:" >> $AUDIT_LOG
ls -la /var/www/natural-stone-marketplace/backend/.env >> $AUDIT_LOG
ls -la /etc/ssh/sshd_config >> $AUDIT_LOG

# Check for world-writable files
echo "World-writable files:" >> $AUDIT_LOG
find /var/www/natural-stone-marketplace -type f -perm -002 >> $AUDIT_LOG

# Check running processes
echo "Running processes:" >> $AUDIT_LOG
ps aux | grep -E "node|nginx|mysql" >> $AUDIT_LOG

# Check disk usage
echo "Disk usage:" >> $AUDIT_LOG
df -h >> $AUDIT_LOG

# Check memory usage
echo "Memory usage:" >> $AUDIT_LOG
free -h >> $AUDIT_LOG

# Check for updates
echo "Available updates:" >> $AUDIT_LOG
apt list --upgradable 2>/dev/null | wc -l >> $AUDIT_LOG

echo "=== End of Security Audit ===" >> $AUDIT_LOG
EOF

chmod +x /var/www/natural-stone-marketplace/scripts/security-audit.sh

# Schedule security audit
(crontab -l 2>/dev/null; echo "0 2 * * * /var/www/natural-stone-marketplace/scripts/security-audit.sh") | crontab -

print_status "Security audit script created and scheduled"

# 13. Final security check
print_status "Running final security check..."
sudo ufw status
sudo fail2ban-client status
sudo systemctl status ssh --no-pager
sudo systemctl status mysql --no-pager

echo -e "${GREEN}"
echo "=================================================="
echo "🔒 SECURITY HARDENING COMPLETED!"
echo "=================================================="
echo "✅ Firewall configured and enabled"
echo "✅ Fail2Ban configured for intrusion prevention"
echo "✅ SSH hardened"
echo "✅ MySQL secured"
echo "✅ File permissions set"
echo "✅ Automatic updates enabled"
echo "✅ Intrusion detection configured"
echo "✅ Log monitoring set up"
echo "✅ Kernel parameters hardened"
echo "✅ Security audit scheduled"
echo "=================================================="
echo "🔍 Next steps:"
echo "1. Change default passwords"
echo "2. Set up SSL certificates"
echo "3. Configure backup encryption"
echo "4. Review and test all security measures"
echo "=================================================="
echo -e "${NC}"
