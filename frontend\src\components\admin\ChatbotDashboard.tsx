'use client';

import React, { useState, useEffect } from 'react';
import { 
  MessageCircle, 
  Users, 
  TrendingUp, 
  AlertTriangle, 
  Clock, 
  Star,
  Settings,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';
import { 
  ChatbotAnalytics, 
  Agent, 
  EscalationCriteria,
  ChatSession 
} from '../../types/chatbot';
import { useChatbotAdmin } from '../../hooks/useChatbot';

interface ChatbotDashboardProps {
  token?: string;
}

export const ChatbotDashboard: React.FC<ChatbotDashboardProps> = ({ token }) => {
  const { dashboardData, isLoading, error, updateEscalationCriteria, updateAgentAvailability, refresh } = useChatbotAdmin(token);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'agents' | 'settings'>('overview');
  const [editingCriteria, setEditingCriteria] = useState(false);

  useEffect(() => {
    // Refresh data every 30 seconds
    const interval = setInterval(refresh, 30000);
    return () => clearInterval(interval);
  }, [refresh]);

  if (isLoading && !dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading chatbot dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
          <span className="text-red-800">Error loading dashboard: {error.message}</span>
        </div>
      </div>
    );
  }

  const analytics = dashboardData?.analytics;
  const agents = dashboardData?.agents || [];
  const activeSessions = dashboardData?.activeSessions || [];
  const escalationCriteria = dashboardData?.escalationCriteria;

  const handleAgentToggle = async (agentId: string, available: boolean) => {
    try {
      await updateAgentAvailability(agentId, available);
    } catch (error) {
      console.error('Failed to update agent availability:', error);
    }
  };

  const handleCriteriaUpdate = async (newCriteria: Partial<EscalationCriteria>) => {
    try {
      await updateEscalationCriteria(newCriteria);
      setEditingCriteria(false);
    } catch (error) {
      console.error('Failed to update escalation criteria:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI Chatbot Dashboard</h1>
          <p className="text-gray-600">Monitor and manage your AI assistant</p>
        </div>
        <button
          onClick={refresh}
          disabled={isLoading}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: TrendingUp },
            { id: 'agents', label: 'Agents', icon: Users },
            { id: 'settings', label: 'Settings', icon: Settings }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setSelectedTab(id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Icon className="h-4 w-4 mr-2" />
              {label}
            </button>
          ))}
        </nav>
      </div>

      {/* Overview Tab */}
      {selectedTab === 'overview' && analytics && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Total Conversations"
              value={analytics.totalConversations.toString()}
              icon={MessageCircle}
              color="blue"
            />
            <MetricCard
              title="Resolution Rate"
              value={`${(analytics.resolutionRate * 100).toFixed(1)}%`}
              icon={TrendingUp}
              color="green"
            />
            <MetricCard
              title="Avg Response Time"
              value={`${analytics.averageResponseTime.toFixed(1)}s`}
              icon={Clock}
              color="yellow"
            />
            <MetricCard
              title="User Satisfaction"
              value={`${analytics.userSatisfactionScore.toFixed(1)}/5`}
              icon={Star}
              color="purple"
            />
          </div>

          {/* Charts and Stats */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Intent Distribution */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Most Common Intents</h3>
              <div className="space-y-3">
                {analytics.mostCommonIntents.slice(0, 5).map((intent, index) => (
                  <div key={intent.intent} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{intent.intent.replace('_', ' ')}</span>
                    <div className="flex items-center">
                      <div className="w-24 bg-gray-200 rounded-full h-2 mr-3">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${intent.percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-gray-900">{intent.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Language Distribution */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Language Distribution</h3>
              <div className="space-y-3">
                {analytics.languageDistribution.map((lang, index) => (
                  <div key={lang.language} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{lang.language.toUpperCase()}</span>
                    <div className="flex items-center">
                      <div className="w-24 bg-gray-200 rounded-full h-2 mr-3">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${lang.percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-gray-900">{lang.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Active Sessions */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Active Conversations</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Session ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Language
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Messages
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Activity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {activeSessions.slice(0, 10).map((session) => (
                    <tr key={session.sessionId}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                        {session.sessionId.substring(0, 8)}...
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {session.language.toUpperCase()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {session.messageCount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(session.lastActivity).toLocaleTimeString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          session.escalationLevel > 0
                            ? 'bg-red-100 text-red-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {session.escalationLevel > 0 ? 'Escalated' : 'Active'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Agents Tab */}
      {selectedTab === 'agents' && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Agent Management</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {agents.map((agent) => (
                <div key={agent.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900">{agent.name}</h4>
                    <button
                      onClick={() => handleAgentToggle(agent.id, !agent.available)}
                      className={`p-1 rounded ${
                        agent.available ? 'text-green-600 hover:bg-green-50' : 'text-gray-400 hover:bg-gray-50'
                      }`}
                    >
                      {agent.available ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                    </button>
                  </div>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div>Languages: {agent.languages.join(', ')}</div>
                    <div>Expertise: {agent.expertise.join(', ')}</div>
                    <div>Current Load: {agent.currentLoad}</div>
                    <div className={`font-medium ${
                      agent.available ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {agent.available ? 'Available' : 'Offline'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Settings Tab */}
      {selectedTab === 'settings' && escalationCriteria && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Escalation Criteria</h3>
              <button
                onClick={() => setEditingCriteria(!editingCriteria)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                {editingCriteria ? 'Cancel' : 'Edit'}
              </button>
            </div>
            
            {editingCriteria ? (
              <EscalationCriteriaForm
                criteria={escalationCriteria}
                onSave={handleCriteriaUpdate}
                onCancel={() => setEditingCriteria(false)}
              />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Low Confidence Threshold</label>
                  <div className="mt-1 text-sm text-gray-900">{escalationCriteria.lowConfidenceThreshold}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Negative Sentiment Threshold</label>
                  <div className="mt-1 text-sm text-gray-900">{escalationCriteria.negativeSentimentThreshold}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Max Conversation Length</label>
                  <div className="mt-1 text-sm text-gray-900">{escalationCriteria.conversationLength}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Max Unresolved Issues</label>
                  <div className="mt-1 text-sm text-gray-900">{escalationCriteria.multipleUnresolvedIssues}</div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Helper Components
interface MetricCardProps {
  title: string;
  value: string;
  icon: React.ComponentType<any>;
  color: 'blue' | 'green' | 'yellow' | 'purple';
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon: Icon, color }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    green: 'bg-green-50 text-green-600',
    yellow: 'bg-yellow-50 text-yellow-600',
    purple: 'bg-purple-50 text-purple-600'
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center">
        <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
        </div>
      </div>
    </div>
  );
};

interface EscalationCriteriaFormProps {
  criteria: EscalationCriteria;
  onSave: (criteria: Partial<EscalationCriteria>) => void;
  onCancel: () => void;
}

const EscalationCriteriaForm: React.FC<EscalationCriteriaFormProps> = ({ criteria, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    lowConfidenceThreshold: criteria.lowConfidenceThreshold,
    negativeSentimentThreshold: criteria.negativeSentimentThreshold,
    conversationLength: criteria.conversationLength,
    multipleUnresolvedIssues: criteria.multipleUnresolvedIssues
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Low Confidence Threshold</label>
          <input
            type="number"
            step="0.1"
            min="0"
            max="1"
            value={formData.lowConfidenceThreshold}
            onChange={(e) => setFormData({ ...formData, lowConfidenceThreshold: parseFloat(e.target.value) })}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Negative Sentiment Threshold</label>
          <input
            type="number"
            step="0.1"
            min="-1"
            max="1"
            value={formData.negativeSentimentThreshold}
            onChange={(e) => setFormData({ ...formData, negativeSentimentThreshold: parseFloat(e.target.value) })}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Max Conversation Length</label>
          <input
            type="number"
            min="1"
            value={formData.conversationLength}
            onChange={(e) => setFormData({ ...formData, conversationLength: parseInt(e.target.value) })}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Max Unresolved Issues</label>
          <input
            type="number"
            min="1"
            value={formData.multipleUnresolvedIssues}
            onChange={(e) => setFormData({ ...formData, multipleUnresolvedIssues: parseInt(e.target.value) })}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
};
