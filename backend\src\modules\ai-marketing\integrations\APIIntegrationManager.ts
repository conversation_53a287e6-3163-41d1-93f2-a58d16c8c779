// API Integration Manager
// Tüm API entegrasyonlarını yöneten merkezi sistem

import { EventEmitter } from 'events';
import { TradeMapAPI, TradeMapConfig } from './TradeMapAPI';
import { LinkedInAPI, LinkedInConfig } from './LinkedInAPI';
import { GoogleAdsAPI, GoogleAdsConfig } from './GoogleAdsAPI';

export interface APIIntegrationConfig {
  tradeMap: TradeMapConfig;
  linkedIn: LinkedInConfig;
  googleAds: GoogleAdsConfig;
}

export interface APIHealthStatus {
  tradeMap: boolean;
  linkedIn: boolean;
  googleAds: boolean;
  overall: boolean;
  lastChecked: Date;
}

export interface IntegrationMetrics {
  tradeMap: {
    requestsToday: number;
    dataPointsCollected: number;
    lastSync: Date;
    errorRate: number;
  };
  linkedIn: {
    campaignsActive: number;
    leadsGenerated: number;
    lastCampaignUpdate: Date;
    apiCallsRemaining: number;
  };
  googleAds: {
    campaignsRunning: number;
    totalSpend: number;
    conversions: number;
    lastOptimization: Date;
  };
}

export class APIIntegrationManager extends EventEmitter {
  private tradeMapAPI: TradeMapAPI;
  private linkedInAPI: LinkedInAPI;
  private googleAdsAPI: GoogleAdsAPI;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private isInitialized: boolean = false;

  constructor(config: APIIntegrationConfig) {
    super();
    
    // API istemcilerini başlat
    this.tradeMapAPI = new TradeMapAPI(config.tradeMap);
    this.linkedInAPI = new LinkedInAPI(config.linkedIn);
    this.googleAdsAPI = new GoogleAdsAPI(config.googleAds);
    
    this.setupEventListeners();
  }

  /**
   * Tüm API entegrasyonlarını başlat
   */
  public async initialize(): Promise<void> {
    console.log('🔌 Initializing API integrations...');
    
    try {
      // Health check başlat
      await this.performHealthCheck();
      
      // Periyodik health check
      this.startHealthCheckInterval();
      
      this.isInitialized = true;
      console.log('✅ API integrations initialized successfully');
      this.emit('initialized');
      
    } catch (error) {
      console.error('❌ API integration initialization failed:', error);
      this.emit('initializationError', error);
      throw error;
    }
  }

  /**
   * Trade Map API işlemleri
   */
  public getTradeMapAPI(): TradeMapAPI {
    this.ensureInitialized();
    return this.tradeMapAPI;
  }

  /**
   * LinkedIn API işlemleri
   */
  public getLinkedInAPI(): LinkedInAPI {
    this.ensureInitialized();
    return this.linkedInAPI;
  }

  /**
   * Google Ads API işlemleri
   */
  public getGoogleAdsAPI(): GoogleAdsAPI {
    this.ensureInitialized();
    return this.googleAdsAPI;
  }

  /**
   * Kapsamlı pazar araştırması
   */
  public async performMarketResearch(params: {
    productCode: string;
    targetCountries: string[];
    keywords: string[];
  }): Promise<{
    tradeData: any;
    linkedInInsights: any;
    keywordData: any;
    marketOpportunities: any[];
  }> {
    console.log('🔍 Performing comprehensive market research...');
    
    try {
      // Trade Map verilerini al
      const tradeDataPromises = params.targetCountries.map(country =>
        this.tradeMapAPI.getImportData(params.productCode, country)
      );
      const tradeData = await Promise.all(tradeDataPromises);

      // İhracat fırsatlarını analiz et
      const exportOpportunities = await this.tradeMapAPI.analyzeExportOpportunities(params.productCode);

      // LinkedIn'de şirket araştırması
      const linkedInInsights = await this.linkedInAPI.searchCompanies({
        keywords: params.keywords,
        location: params.targetCountries,
        industry: ['Construction', 'Architecture', 'Building Materials'],
        limit: 50
      });

      // Google Ads keyword araştırması
      const keywordData = await this.googleAdsAPI.researchKeywords(
        params.keywords,
        params.targetCountries
      );

      // Pazar fırsatlarını birleştir ve analiz et
      const marketOpportunities = this.analyzeMarketOpportunities(
        tradeData,
        exportOpportunities,
        linkedInInsights,
        keywordData
      );

      const result = {
        tradeData,
        linkedInInsights,
        keywordData,
        marketOpportunities
      };

      this.emit('marketResearchCompleted', {
        productCode: params.productCode,
        targetCountries: params.targetCountries,
        opportunitiesFound: marketOpportunities.length
      });

      return result;

    } catch (error) {
      this.emit('marketResearchError', { error, params });
      throw new Error(`Market research failed: ${error}`);
    }
  }

  /**
   * Entegre kampanya oluşturma
   */
  public async createIntegratedCampaign(campaignData: {
    name: string;
    targetMarkets: string[];
    budget: number;
    duration: number; // days
    productCategory: string;
    objectives: string[];
  }): Promise<{
    linkedInCampaign?: any;
    googleAdsCampaign?: any;
    marketInsights: any;
    campaignId: string;
  }> {
    console.log('🚀 Creating integrated marketing campaign...');
    
    try {
      const campaignId = `integrated-${Date.now()}`;

      // Pazar araştırması yap
      const marketInsights = await this.performMarketResearch({
        productCode: '2515', // Natural stone HS code
        targetCountries: campaignData.targetMarkets,
        keywords: [campaignData.productCategory, 'natural stone', 'marble', 'granite']
      });

      // LinkedIn kampanyası oluştur
      let linkedInCampaign;
      if (campaignData.budget >= 1000) { // Minimum budget check
        linkedInCampaign = await this.linkedInAPI.createSponsoredContentCampaign({
          name: `${campaignData.name} - LinkedIn`,
          targeting: {
            geography: campaignData.targetMarkets,
            industries: ['Construction', 'Architecture', 'Building Materials'],
            jobTitles: ['CEO', 'Procurement Manager', 'Project Manager'],
            companySizes: ['SIZE_51_TO_200', 'SIZE_201_TO_500', 'SIZE_501_TO_1000'],
            seniorityLevels: ['MANAGER', 'DIRECTOR', 'VP'],
            skills: ['Construction Management', 'Procurement', 'Architecture'],
            interests: [],
            languages: ['en']
          },
          budget: {
            dailyBudget: Math.floor(campaignData.budget * 0.4 / campaignData.duration), // 40% of budget
            totalBudget: campaignData.budget * 0.4,
            bidType: 'CPC',
            bidAmount: 3.5
          },
          creative: {
            type: 'SINGLE_IMAGE',
            headline: `Premium ${campaignData.productCategory} from Turkey`,
            description: 'Discover high-quality natural stone products for your construction projects.',
            callToAction: 'Learn More',
            landingPageUrl: 'https://your-website.com/products'
          }
        });
      }

      // Google Ads kampanyası oluştur
      let googleAdsCampaign;
      if (campaignData.budget >= 500) { // Minimum budget check
        googleAdsCampaign = await this.googleAdsAPI.createSearchCampaign({
          name: `${campaignData.name} - Google Search`,
          budget: {
            dailyBudget: Math.floor(campaignData.budget * 0.6 / campaignData.duration), // 60% of budget
            deliveryMethod: 'STANDARD'
          },
          targeting: {
            geoTargeting: campaignData.targetMarkets,
            languageTargeting: ['en'],
            deviceTargeting: ['DESKTOP', 'MOBILE', 'TABLET']
          },
          biddingStrategy: {
            type: 'TARGET_CPA',
            targetCpa: 25
          },
          keywords: marketInsights.keywordData.slice(0, 20).map((kw: any) => kw.keyword),
          ads: [{
            type: 'RESPONSIVE_SEARCH_AD',
            headlines: [
              `Premium ${campaignData.productCategory}`,
              'Turkish Natural Stone',
              'High Quality Materials'
            ],
            descriptions: [
              'Discover premium natural stone products from Turkey.',
              'Perfect for construction and architectural projects.'
            ],
            finalUrls: ['https://your-website.com/products'],
            images: [],
            videos: []
          }]
        });
      }

      const result = {
        linkedInCampaign,
        googleAdsCampaign,
        marketInsights,
        campaignId
      };

      this.emit('integratedCampaignCreated', {
        campaignId,
        name: campaignData.name,
        targetMarkets: campaignData.targetMarkets,
        budget: campaignData.budget
      });

      return result;

    } catch (error) {
      this.emit('integratedCampaignError', { error, campaignData });
      throw new Error(`Integrated campaign creation failed: ${error}`);
    }
  }

  /**
   * API sağlık durumu kontrolü
   */
  public async performHealthCheck(): Promise<APIHealthStatus> {
    console.log('🏥 Performing API health check...');
    
    const healthStatus: APIHealthStatus = {
      tradeMap: false,
      linkedIn: false,
      googleAds: false,
      overall: false,
      lastChecked: new Date()
    };

    try {
      // Trade Map health check
      healthStatus.tradeMap = await this.tradeMapAPI.healthCheck();
    } catch (error) {
      console.warn('Trade Map health check failed:', error);
    }

    try {
      // LinkedIn health check
      healthStatus.linkedIn = await this.linkedInAPI.healthCheck();
    } catch (error) {
      console.warn('LinkedIn health check failed:', error);
    }

    try {
      // Google Ads health check
      healthStatus.googleAds = await this.googleAdsAPI.healthCheck();
    } catch (error) {
      console.warn('Google Ads health check failed:', error);
    }

    // Overall health
    healthStatus.overall = healthStatus.tradeMap && healthStatus.linkedIn && healthStatus.googleAds;

    this.emit('healthCheckCompleted', healthStatus);
    
    if (!healthStatus.overall) {
      this.emit('healthCheckWarning', healthStatus);
    }

    return healthStatus;
  }

  /**
   * Entegrasyon metriklerini getir
   */
  public async getIntegrationMetrics(): Promise<IntegrationMetrics> {
    const metrics: IntegrationMetrics = {
      tradeMap: {
        requestsToday: 0, // Bu veriler gerçek implementasyonda cache'den gelecek
        dataPointsCollected: 0,
        lastSync: new Date(),
        errorRate: 0
      },
      linkedIn: {
        campaignsActive: 0,
        leadsGenerated: 0,
        lastCampaignUpdate: new Date(),
        apiCallsRemaining: 1000
      },
      googleAds: {
        campaignsRunning: 0,
        totalSpend: 0,
        conversions: 0,
        lastOptimization: new Date()
      }
    };

    this.emit('metricsCollected', metrics);
    return metrics;
  }

  // Private methods
  private setupEventListeners(): void {
    // Trade Map events
    this.tradeMapAPI.on('dataFetched', (data) => {
      this.emit('tradeMapDataFetched', data);
    });

    this.tradeMapAPI.on('error', (error) => {
      this.emit('tradeMapError', error);
    });

    // LinkedIn events
    this.linkedInAPI.on('campaignCreated', (data) => {
      this.emit('linkedInCampaignCreated', data);
    });

    this.linkedInAPI.on('error', (error) => {
      this.emit('linkedInError', error);
    });

    // Google Ads events
    this.googleAdsAPI.on('campaignCreated', (data) => {
      this.emit('googleAdsCampaignCreated', data);
    });

    this.googleAdsAPI.on('error', (error) => {
      this.emit('googleAdsError', error);
    });
  }

  private startHealthCheckInterval(): void {
    // Her 30 dakikada bir health check
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.error('Scheduled health check failed:', error);
      }
    }, 30 * 60 * 1000);
  }

  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('API Integration Manager not initialized. Call initialize() first.');
    }
  }

  private analyzeMarketOpportunities(
    tradeData: any[],
    exportOpportunities: any[],
    linkedInInsights: any[],
    keywordData: any[]
  ): any[] {
    const opportunities = [];

    // Trade data'dan fırsatları çıkar
    for (const trade of tradeData) {
      if (trade.growthRate > 10) {
        opportunities.push({
          type: 'trade_growth',
          country: trade.importerCountry,
          opportunity: `${trade.growthRate}% growth in imports`,
          score: Math.min(100, trade.growthRate * 2),
          source: 'trade_map'
        });
      }
    }

    // Export opportunities'den en iyileri al
    for (const opportunity of exportOpportunities.slice(0, 5)) {
      opportunities.push({
        type: 'export_potential',
        country: opportunity.targetCountry,
        opportunity: `${opportunity.opportunityScore} opportunity score`,
        score: opportunity.opportunityScore,
        source: 'trade_map'
      });
    }

    // LinkedIn'den şirket yoğunluğu analizi
    const companyCountByCountry = linkedInInsights.reduce((acc: any, company: any) => {
      acc[company.location] = (acc[company.location] || 0) + 1;
      return acc;
    }, {});

    for (const [country, count] of Object.entries(companyCountByCountry)) {
      if ((count as number) > 5) {
        opportunities.push({
          type: 'linkedin_density',
          country,
          opportunity: `${count} potential companies found`,
          score: Math.min(100, (count as number) * 5),
          source: 'linkedin'
        });
      }
    }

    // Keyword data'dan search volume fırsatları
    for (const keyword of keywordData.slice(0, 10)) {
      if (keyword.searchVolume > 1000 && keyword.competition === 'LOW') {
        opportunities.push({
          type: 'keyword_opportunity',
          keyword: keyword.keyword,
          opportunity: `${keyword.searchVolume} monthly searches, low competition`,
          score: Math.min(100, keyword.searchVolume / 100),
          source: 'google_ads'
        });
      }
    }

    // Score'a göre sırala ve en iyi 20'yi döndür
    return opportunities
      .sort((a, b) => b.score - a.score)
      .slice(0, 20);
  }

  /**
   * Temizlik işlemleri
   */
  public async cleanup(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // API cache'lerini temizle
    this.tradeMapAPI.clearCache();

    console.log('🧹 API Integration Manager cleaned up');
    this.emit('cleanup');
  }
}
