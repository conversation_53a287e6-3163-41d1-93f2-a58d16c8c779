const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createAdmin() {
  try {
    // Admin bilgileri
    const adminData = {
      email: '<EMAIL>',
      password: 'Admin123456', // Güçlü şifre
      name: '<PERSON>ste<PERSON> Yöneticisi',
      role: 'super_admin'
    };

    // Şifreyi hash'le
    const hashedPassword = await bcrypt.hash(adminData.password, 12);

    // Mevcut admin'i kontrol et
    const existingAdmin = await prisma.admin.findUnique({
      where: { email: adminData.email }
    });

    if (existingAdmin) {
      console.log('❌ Bu email ile admin zaten mevcut:', adminData.email);
      return;
    }

    // Admin oluştur
    const admin = await prisma.admin.create({
      data: {
        email: adminData.email,
        password: hashedPassword,
        name: adminData.name,
        role: adminData.role,
        isActive: true
      }
    });

    console.log('✅ Admin başarıyla oluşturuldu!');
    console.log('📧 Email:', adminData.email);
    console.log('🔑 Şifre:', adminData.password);
    console.log('👤 İsim:', adminData.name);
    console.log('🎭 Rol:', adminData.role);
    console.log('🆔 ID:', admin.id);
    console.log('\n🔗 Giriş URL: http://localhost:3001/admin/login');

  } catch (error) {
    console.error('❌ Admin oluşturma hatası:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin();
