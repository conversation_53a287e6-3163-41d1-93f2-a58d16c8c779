import { Router } from 'express';
import { authMiddleware, AuthenticatedRequest } from '../../middleware/authMiddleware';
import { AIMarketingController } from '../../controllers/AIMarketingController';

const router = Router();
const aiMarketingController = new AIMarketingController();

/**
 * @swagger
 * components:
 *   schemas:
 *     AICampaign:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         type:
 *           type: string
 *           enum: [lead_generation, email_campaign, market_analysis, retargeting]
 *         status:
 *           type: string
 *           enum: [draft, active, paused, completed, failed]
 *         target_countries:
 *           type: array
 *           items:
 *             type: string
 *         target_industries:
 *           type: array
 *           items:
 *             type: string
 *         performance_metrics:
 *           type: object
 *         created_at:
 *           type: string
 *           format: date-time
 *     
 *     AILead:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         campaign_id:
 *           type: string
 *         company_name:
 *           type: string
 *         contact_person:
 *           type: string
 *         email:
 *           type: string
 *         country:
 *           type: string
 *         industry:
 *           type: string
 *         ai_confidence_score:
 *           type: number
 *           minimum: 0
 *           maximum: 1
 *         status:
 *           type: string
 *           enum: [new, contacted, qualified, converted, rejected]
 *         estimated_value:
 *           type: number
 *         created_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/ai-marketing/campaigns:
 *   post:
 *     summary: Create new AI marketing campaign
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - type
 *               - target_countries
 *             properties:
 *               name:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [lead_generation, email_campaign, market_analysis, retargeting]
 *               target_countries:
 *                 type: array
 *                 items:
 *                   type: string
 *               target_industries:
 *                 type: array
 *                 items:
 *                   type: string
 *               target_keywords:
 *                 type: array
 *                 items:
 *                   type: string
 *               budget_limit:
 *                 type: number
 *     responses:
 *       201:
 *         description: Campaign created successfully
 */
router.post('/campaigns', authMiddleware, aiMarketingController.createCampaign);

/**
 * @swagger
 * /api/ai-marketing/campaigns:
 *   get:
 *     summary: Get AI marketing campaigns
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, active, paused, completed, failed]
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [lead_generation, email_campaign, market_analysis, retargeting]
 *     responses:
 *       200:
 *         description: Campaigns retrieved successfully
 */
router.get('/campaigns', authMiddleware, aiMarketingController.getCampaigns);

/**
 * @swagger
 * /api/ai-marketing/campaigns/{campaignId}/leads/generate:
 *   post:
 *     summary: Start AI lead generation for campaign
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: campaignId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - target_countries
 *               - target_industries
 *               - keywords
 *             properties:
 *               target_countries:
 *                 type: array
 *                 items:
 *                   type: string
 *               target_industries:
 *                 type: array
 *                 items:
 *                   type: string
 *               keywords:
 *                 type: array
 *                 items:
 *                   type: string
 *               max_leads:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 1000
 *                 default: 100
 *               confidence_threshold:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 1
 *                 default: 0.7
 *     responses:
 *       200:
 *         description: Lead generation started successfully
 */
router.post('/campaigns/:campaignId/leads/generate', authMiddleware, aiMarketingController.startLeadGeneration);

/**
 * @swagger
 * /api/ai-marketing/campaigns/{campaignId}/leads:
 *   get:
 *     summary: Get leads for specific campaign
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: campaignId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [new, contacted, qualified, converted, rejected]
 *       - in: query
 *         name: country
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Leads retrieved successfully
 */
router.get('/campaigns/:campaignId/leads', authMiddleware, aiMarketingController.getCampaignLeads);

/**
 * @swagger
 * /api/ai-marketing/leads/{leadId}/status:
 *   put:
 *     summary: Update lead status
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: leadId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [new, contacted, qualified, converted, rejected]
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Lead status updated successfully
 */
router.put('/leads/:leadId/status', authMiddleware, aiMarketingController.updateLeadStatus);

/**
 * @swagger
 * /api/ai-marketing/market-analysis:
 *   post:
 *     summary: Start AI market analysis
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - country
 *               - product_category
 *               - analysis_type
 *             properties:
 *               country:
 *                 type: string
 *               product_category:
 *                 type: string
 *               analysis_type:
 *                 type: string
 *                 enum: [import_potential, export_opportunity, competition_analysis, price_analysis]
 *     responses:
 *       200:
 *         description: Market analysis completed successfully
 */
router.post('/market-analysis', authMiddleware, aiMarketingController.startMarketAnalysis);

/**
 * @swagger
 * /api/ai-marketing/market-analysis:
 *   get:
 *     summary: Get market analysis results
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: country
 *         schema:
 *           type: string
 *       - in: query
 *         name: analysis_type
 *         schema:
 *           type: string
 *           enum: [import_potential, export_opportunity, competition_analysis, price_analysis]
 *     responses:
 *       200:
 *         description: Market analysis retrieved successfully
 */
router.get('/market-analysis', authMiddleware, aiMarketingController.getMarketAnalysis);

/**
 * @swagger
 * /api/ai-marketing/dashboard:
 *   get:
 *     summary: Get AI marketing dashboard data
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 */
router.get('/dashboard', authMiddleware, aiMarketingController.getDashboard);

/**
 * @swagger
 * /api/ai-marketing/countries/{country}/emails/export:
 *   get:
 *     summary: Export country email lists
 *     tags: [AI Marketing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: country
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, csv]
 *           default: json
 *     responses:
 *       200:
 *         description: Email list exported successfully
 */
router.get('/countries/:country/emails/export', authMiddleware, aiMarketingController.exportCountryEmails);

export default router;
