'use client'

import * as React from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Dialog<PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  Phone, 
  Mail, 
  MessageSquare, 
  User,
  Calendar,
  Package,
  Send,
  ExternalLink,
  Clock,
  CheckCircle
} from 'lucide-react'

interface CustomerContactModalProps {
  isOpen: boolean
  onClose: () => void
  request: any
  onSendMessage: (messageData: any) => Promise<boolean>
}

export function CustomerContactModal({
  isOpen,
  onClose,
  request,
  onSendMessage
}: CustomerContactModalProps) {
  const [contactMethod, setContactMethod] = React.useState<'message' | 'phone' | 'email'>('message')
  const [messageData, setMessageData] = React.useState({
    subject: '',
    message: '',
    priority: 'normal',
    followUpDate: ''
  })
  const [isLoading, setIsLoading] = React.useState(false)
  const [step, setStep] = React.useState(1) // 1: Contact Method, 2: Message Form, 3: Confirmation

  React.useEffect(() => {
    if (isOpen && request) {
      // Reset form when modal opens
      setMessageData({
        subject: `${request.productName} Teklifi Hakkında`,
        message: '',
        priority: 'normal',
        followUpDate: ''
      })
      setContactMethod('message')
      setStep(1)
    }
  }, [isOpen, request])

  const handleSendMessage = async () => {
    if (!messageData.message.trim()) {
      alert('Lütfen mesaj içeriğini yazın!')
      return
    }

    setIsLoading(true)
    try {
      const success = await onSendMessage({
        ...messageData,
        requestId: request.id,
        customerId: request.customerId,
        customerName: request.customerName,
        customerEmail: request.customerEmail,
        contactMethod,
        timestamp: new Date().toISOString()
      })
      
      if (success) {
        setStep(3)
        setTimeout(() => {
          onClose()
        }, 2000)
      }
    } catch (error) {
      console.error('Error sending message:', error)
      alert('Mesaj gönderilirken hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePhoneCall = () => {
    // In a real app, this could integrate with a calling system
    window.open(`tel:${request.customerPhone}`, '_self')
  }

  const handleEmailSend = () => {
    const subject = encodeURIComponent(messageData.subject)
    const body = encodeURIComponent(messageData.message)
    window.open(`mailto:${request.customerEmail}?subject=${subject}&body=${body}`, '_blank')
  }

  const getContactHistory = () => {
    // Mock contact history - in real app, this would come from API
    return [
      {
        id: 1,
        type: 'message',
        date: '2024-12-22',
        content: 'Teklif reddedildi: Hedef fiyat çok düşük',
        direction: 'incoming'
      },
      {
        id: 2,
        type: 'email',
        date: '2024-12-20',
        content: 'İlk teklif gönderildi',
        direction: 'outgoing'
      }
    ]
  }

  if (!request) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            Müşteriyle İletişim - {request.customerName}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Customer Info */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-blue-800">Müşteri Bilgileri</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-blue-600" />
                  <div>
                    <span className="font-medium text-blue-700">Ad:</span>
                    <p className="text-blue-900">{request.customerName}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-blue-600" />
                  <div>
                    <span className="font-medium text-blue-700">E-posta:</span>
                    <p className="text-blue-900">{request.customerEmail}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-blue-600" />
                  <div>
                    <span className="font-medium text-blue-700">Telefon:</span>
                    <p className="text-blue-900">{request.customerPhone}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Package className="w-4 h-4 text-blue-600" />
                  <div>
                    <span className="font-medium text-blue-700">Talep:</span>
                    <p className="text-blue-900">{request.productName} - {request.quantity}m²</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Step 1: Contact Method Selection */}
          {step === 1 && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>İletişim Yöntemi Seçin</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    variant={contactMethod === 'message' ? 'default' : 'outline'}
                    onClick={() => setContactMethod('message')}
                    className="h-20 flex flex-col items-center justify-center"
                  >
                    <MessageSquare className="w-6 h-6 mb-2" />
                    <span>Platform Mesajı</span>
                  </Button>
                  
                  <Button
                    variant={contactMethod === 'email' ? 'default' : 'outline'}
                    onClick={() => setContactMethod('email')}
                    className="h-20 flex flex-col items-center justify-center"
                  >
                    <Mail className="w-6 h-6 mb-2" />
                    <span>E-posta Gönder</span>
                  </Button>
                  
                  <Button
                    variant={contactMethod === 'phone' ? 'default' : 'outline'}
                    onClick={() => setContactMethod('phone')}
                    className="h-20 flex flex-col items-center justify-center"
                  >
                    <Phone className="w-6 h-6 mb-2" />
                    <span>Telefon Ara</span>
                  </Button>
                </CardContent>
              </Card>

              {/* Contact History */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    İletişim Geçmişi
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {getContactHistory().map((contact) => (
                      <div key={contact.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                        <div className={`p-2 rounded-full ${
                          contact.direction === 'incoming' ? 'bg-blue-100' : 'bg-green-100'
                        }`}>
                          {contact.type === 'message' && <MessageSquare className="w-4 h-4" />}
                          {contact.type === 'email' && <Mail className="w-4 h-4" />}
                          {contact.type === 'phone' && <Phone className="w-4 h-4" />}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge variant={contact.direction === 'incoming' ? 'secondary' : 'default'}>
                              {contact.direction === 'incoming' ? 'Gelen' : 'Giden'}
                            </Badge>
                            <span className="text-sm text-gray-500">{contact.date}</span>
                          </div>
                          <p className="text-sm text-gray-700">{contact.content}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={onClose}>
                  İptal
                </Button>
                <Button onClick={() => {
                  if (contactMethod === 'phone') {
                    handlePhoneCall()
                  } else {
                    setStep(2)
                  }
                }}>
                  {contactMethod === 'phone' ? 'Ara' : 'Devam Et'}
                </Button>
              </div>
            </>
          )}

          {/* Step 2: Message Form */}
          {step === 2 && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>
                    {contactMethod === 'message' && 'Platform Mesajı Gönder'}
                    {contactMethod === 'email' && 'E-posta Gönder'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="subject">Konu</Label>
                    <Input
                      id="subject"
                      value={messageData.subject}
                      onChange={(e) => setMessageData(prev => ({ ...prev, subject: e.target.value }))}
                      placeholder="Mesaj konusu"
                    />
                  </div>

                  <div>
                    <Label htmlFor="priority">Öncelik</Label>
                    <Select 
                      value={messageData.priority} 
                      onValueChange={(value) => setMessageData(prev => ({ ...prev, priority: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Düşük</SelectItem>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="high">Yüksek</SelectItem>
                        <SelectItem value="urgent">Acil</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="message">Mesaj İçeriği</Label>
                    <Textarea
                      id="message"
                      value={messageData.message}
                      onChange={(e) => setMessageData(prev => ({ ...prev, message: e.target.value }))}
                      rows={6}
                      placeholder="Müşteriye göndermek istediğiniz mesajı yazın..."
                    />
                  </div>

                  <div>
                    <Label htmlFor="followUpDate">Takip Tarihi (İsteğe Bağlı)</Label>
                    <Input
                      id="followUpDate"
                      type="date"
                      value={messageData.followUpDate}
                      onChange={(e) => setMessageData(prev => ({ ...prev, followUpDate: e.target.value }))}
                    />
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>
                  Geri Dön
                </Button>
                <Button 
                  onClick={() => {
                    if (contactMethod === 'email') {
                      handleEmailSend()
                    } else {
                      handleSendMessage()
                    }
                  }}
                  disabled={!messageData.message.trim() || isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Gönderiliyor...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      {contactMethod === 'email' ? 'E-posta Aç' : 'Mesaj Gönder'}
                    </>
                  )}
                </Button>
              </div>
            </>
          )}

          {/* Step 3: Confirmation */}
          {step === 3 && (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-green-800 mb-2">
                Mesaj Başarıyla Gönderildi!
              </h3>
              <p className="text-green-600">
                Mesajınız {request.customerName} müşterisine iletildi.
              </p>
              {messageData.followUpDate && (
                <p className="text-sm text-gray-600 mt-2">
                  Takip tarihi: {new Date(messageData.followUpDate).toLocaleDateString('tr-TR')}
                </p>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
