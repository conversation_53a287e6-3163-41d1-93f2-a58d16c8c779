"use client"

import * as React from "react"
import { Navigation } from "@/components/ui/navigation"
import { Container } from "@/components/ui/container"
import { Grid } from "@/components/ui/grid"
import { Button } from "@/components/ui/button"
import ProductCard from "@/components/ui/product-card"
import { StoneTexture } from "@/components/ui/stone-texture"
// import { ThemeToggle } from "@/components/ui/theme-toggle"
import { VisuallyHidden } from "@/components/ui/visually-hidden"
import { useAuth } from "@/contexts/auth-context"
import { OrganizationStructuredData, WebsiteStructuredData } from "@/components/seo/structured-data"
import { useProducerAuth } from "@/contexts/producer-auth-context"
import { useSearchParams } from "next/navigation"
import { useProducts } from "@/contexts/products-context"


// Test verileri kaldırıldı - gerçek veriler products context'inden gelecek

// Navigation links will be created inside component with translations

/**
 * Home page following RFC-004 UI/UX Design System
 * Natural Stone Marketplace landing page with modern design
 */
export default function Home() {
  const { isAuthenticated, user, showLoginModal, showCustomerRegisterModal, logout } = useAuth()
  const { showLoginModal: showProducerLoginModal, showRegisterModal: showProducerRegisterModal } = useProducerAuth()
  const { getApprovedProducts } = useProducts()
  const searchParams = useSearchParams()

  // Onaylanmış ürünlerden ilk 4'ünü öne çıkan ürünler olarak göster
  const featuredProducts = React.useMemo(() => {
    const approvedProducts = getApprovedProducts()
    return approvedProducts.slice(0, 4)
  }, [getApprovedProducts])

  // Geçici translation function
  const t = (key: string) => {
    const translations: Record<string, string> = {
      'nav.register': 'Üye Ol',
      'common.welcome': 'Hoş geldiniz',
      'dashboard.menu.logout': 'Çıkış Yap',
      'home.hero.title_part1': 'Türkiye\'nin En Büyük',
      'home.hero.title_part2': 'Doğal Taş Pazarı',
      'home.hero.subtitle': 'Kaliteli doğal taş ürünleri için güvenilir adres. Üreticilerden direkt, rekabetçi fiyatlarla.',
      'home.featured.title': 'Öne Çıkan Ürünler',
      'home.featured.subtitle': 'En popüler ve kaliteli doğal taş ürünlerimizi keşfedin',
      'home.featured.view_all': 'Tüm Ürünleri Görüntüle',
      'home.loading.title': 'Ürünler Yükleniyor',
      'home.loading.subtitle': 'En iyi doğal taş ürünlerini sizin için hazırlıyoruz...',
      'home.how_it_works.title': 'Nasıl Çalışır?',
      'home.how_it_works.subtitle': 'Doğal taş siparişinizi 6 kolay adımda tamamlayın',
      'home.how_it_works.step1.title': 'Ürün Arayın',
      'home.how_it_works.step1.description': 'İhtiyacınıza uygun doğal taş ürünlerini keşfedin',
      'home.how_it_works.step2.title': 'Teklif İsteyin',
      'home.how_it_works.step2.description': 'Seçtiğiniz ürünler için üreticilerden teklif alın',
      'home.how_it_works.step3.title': 'Teklifleri Karşılaştırın',
      'home.how_it_works.step3.description': 'Gelen teklifleri karşılaştırıp en uygununu seçin',
      'home.how_it_works.step4.title': 'Sipariş Verin',
      'home.how_it_works.step4.description': 'Seçtiğiniz teklifi onaylayıp siparişinizi oluşturun',
      'home.how_it_works.step5.title': 'Ödeme Yapın',
      'home.how_it_works.step5.description': 'Güvenli ödeme sistemi ile ödemenizi gerçekleştirin',
      'home.how_it_works.step6.title': 'Teslimat Alın',
      'home.how_it_works.step6.description': 'Ürününüz güvenli şekilde adresinize teslim edilir'
    }
    return translations[key] || key
  }
  // Create navigation links
  const navigationLinks = [
    { name: 'Ana Sayfa', href: "/", active: true },
    { name: 'Ürünler', href: "/products" },
    { name: '3D Showroom', href: "/3d-showroom" },
    { name: 'Haberler', href: "/news" },
    { name: 'Hakkımızda', href: "/about" },
    { name: 'İletişim', href: "/contact" }
  ]

  // Check if login is required (from middleware redirect)
  React.useEffect(() => {
    const loginRequired = searchParams.get('login')
    if (loginRequired === 'required' && !isAuthenticated) {
      showLoginModal()
    }
  }, [searchParams, isAuthenticated, showLoginModal])

  return (
    <div className="min-h-screen bg-[var(--bg-primary)]">
      {/* Navigation */}
      <Navigation
        brand={{
          name: "Türkiye Doğal Taş Pazarı",
          href: "/"
        }}
        links={navigationLinks}
        actions={
          <div className="flex items-center gap-2">
            {!isAuthenticated ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={showLoginModal}
                >
                  Giriş Yap
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={showCustomerRegisterModal}
                >
                  {t('nav.register')}
                </Button>
              </>
            ) : (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 max-w-[200px] truncate">
                  {t('common.welcome')}, {user?.company || user?.name}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={logout}
                >
                  {t('dashboard.menu.logout')}
                </Button>
              </div>
            )}
          </div>
        }
      />

      {/* Main Content */}
      <main id="main-content" role="main">
        {/* Hero Section */}
        <section
          className="relative py-24 lg:py-32 overflow-hidden"
          aria-labelledby="hero-heading"
        >
          <StoneTexture
            type="marble"
            intensity="light"
            className="absolute inset-0 opacity-30"
          />

          <Container className="relative z-10">
            <div className="text-center max-w-4xl mx-auto">
              <h1
                id="hero-heading"
                className="text-4xl lg:text-6xl font-bold text-[var(--text-primary)] mb-6"
              >
                {t('home.hero.title_part1')}{" "}
                <span className="text-[var(--primary-stone)]">
                  {t('home.hero.title_part2')}
                </span>
              </h1>

              <p className="text-xl lg:text-2xl text-[var(--text-secondary)] mb-8 leading-relaxed">
                {t('home.hero.subtitle')}
              </p>


            </div>
          </Container>
        </section>



        {/* Featured Products Section */}
        <section
          className="py-16"
          aria-labelledby="featured-heading"
        >
          <Container>
            <div className="text-center mb-12">
              <h2
                id="featured-heading"
                className="text-3xl lg:text-4xl font-bold text-[var(--text-primary)] mb-4"
              >
                {t('home.featured.title')}
              </h2>
              <p className="text-lg text-[var(--text-secondary)] max-w-2xl mx-auto">
                {t('home.featured.subtitle')}
              </p>
            </div>

            <Grid
              cols={1}
              responsive={{ sm: 2, lg: 3, xl: 4 }}
              gap="lg"
            >
              {featuredProducts && featuredProducts.length > 0 ? (
                featuredProducts.map((product) => {
                  // Products context'indeki veriyi ProductCard'ın beklediği formata dönüştür
                  const productForCard = {
                    id: product.id,
                    name: product.name,
                    category: product.category,
                    price: {
                      min: product.basePrice || 0,
                      max: (product.basePrice || 0) * 1.5, // Örnek max fiyat
                      currency: product.currency || "$",
                      unit: product.unit || "m²"
                    },
                    image: product.image || "/api/placeholder/300/200",
                    producer: product.producer || "Üretici Bilgisi Yok"
                  }

                  return (
                    <ProductCard
                      key={product.id}
                      product={productForCard}
                      onViewDetails={(id) => {
                        console.log("View details:", id)
                        window.location.href = `/products/${id}`
                      }}
                      onRequestQuote={(id) => console.log("Request quote:", id)}
                      onToggleFavorite={(id) => console.log("Toggle favorite:", id)}
                      onView3D={(id) => console.log("View 3D:", id)}
                      show3DViewer={true}
                    />
                  )
                })
              ) : (
                <div className="col-span-full text-center py-12">
                  <div className="text-6xl mb-4">🏗️</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {t('home.loading.title')}
                  </h3>
                  <p className="text-gray-600">
                    {t('home.loading.subtitle')}
                  </p>
                </div>
              )}
            </Grid>

            <div className="text-center mt-12">
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  window.location.href = '/products'
                }}
              >
                {t('home.featured.view_all')}
              </Button>
            </div>
          </Container>
        </section>

        {/* How It Works Section */}
        <section
          className="py-16 bg-[var(--bg-secondary)]"
          aria-labelledby="how-it-works-heading"
        >
          <Container>
            <div className="text-center mb-12">
              <h2
                id="how-it-works-heading"
                className="text-3xl lg:text-4xl font-bold text-[var(--text-primary)] mb-4"
              >
                {t('home.how_it_works.title')}
              </h2>
              <p className="text-lg text-[var(--text-secondary)] max-w-2xl mx-auto">
                {t('home.how_it_works.subtitle')}
              </p>
            </div>

            <Grid cols={1} responsive={{ md: 2, lg: 3 }} gap="lg">
              {[
                {
                  step: "1",
                  title: t('home.how_it_works.step1.title'),
                  description: t('home.how_it_works.step1.description'),
                  icon: "🔍"
                },
                {
                  step: "2",
                  title: t('home.how_it_works.step2.title'),
                  description: t('home.how_it_works.step2.description'),
                  icon: "📋"
                },
                {
                  step: "3",
                  title: t('home.how_it_works.step3.title'),
                  description: t('home.how_it_works.step3.description'),
                  icon: "⚖️"
                },
                {
                  step: "4",
                  title: t('home.how_it_works.step4.title'),
                  description: t('home.how_it_works.step4.description'),
                  icon: "✅"
                },
                {
                  step: "5",
                  title: t('home.how_it_works.step5.title'),
                  description: t('home.how_it_works.step5.description'),
                  icon: "💳"
                },
                {
                  step: "6",
                  title: t('home.how_it_works.step6.title'),
                  description: t('home.how_it_works.step6.description'),
                  icon: "🚚"
                }
              ].map((item) => (
                <div
                  key={item.step}
                  className="bg-white rounded-[var(--radius-lg)] p-6 shadow-[var(--shadow)] text-center hover:shadow-[var(--shadow-lg)] transition-shadow duration-300"
                >
                  <div className="w-16 h-16 bg-[var(--primary-stone)] rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                    {item.step}
                  </div>
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-3">
                    {item.title}
                  </h3>
                  <p className="text-[var(--text-secondary)] leading-relaxed">
                    {item.description}
                  </p>
                </div>
              ))}
            </Grid>
          </Container>
        </section>

        {/* Statistics Section */}
        <section
          className="py-16"
          aria-labelledby="stats-heading"
        >
          <Container>
            <VisuallyHidden>
              <h2 id="stats-heading">Platform İstatistikleri</h2>
            </VisuallyHidden>

            <Grid cols={2} responsive={{ md: 4 }} gap="lg">
              {[
                { number: "500+", label: "Aktif Üretici", icon: "🏭" },
                { number: "10,000+", label: "Ürün Çeşidi", icon: "🏛️" },
                { number: "50,000+", label: "Mutlu Müşteri", icon: "😊" },
                { number: "1M+", label: "Tamamlanan Sipariş", icon: "📦" }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-4xl mb-2">{stat.icon}</div>
                  <div className="text-3xl lg:text-4xl font-bold text-[var(--primary-stone)] mb-2">
                    {stat.number}
                  </div>
                  <div className="text-[var(--text-secondary)] font-medium">
                    {stat.label}
                  </div>
                </div>
              ))}
            </Grid>
          </Container>
        </section>
      </main>

      {/* Footer */}
      <footer
        className="bg-[var(--gray-900)] text-white py-12"
        role="contentinfo"
      >
        <Container>
          <Grid cols={1} responsive={{ md: 2, lg: 4 }} gap="lg">
            <div>
              <h3 className="text-xl font-bold mb-4 text-[var(--primary-light)]">
                Türkiye Doğal Taş Pazarı
              </h3>
              <p className="text-gray-300 mb-4">
                Türkiye'nin en büyük doğal taş pazarında güvenli alışveriş yapın.
              </p>
              <div className="flex gap-4">
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  📧 İletişim
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  📞 Destek
                </a>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Ürünler</h4>
              <ul className="space-y-2 text-gray-300">
                <li><a href="#" className="hover:text-white transition-colors">Mermer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Granit</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Traverten</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Oniks</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Hizmetler</h4>
              <ul className="space-y-2 text-gray-300">
                <li><a href="#" className="hover:text-white transition-colors">3D Görüntüleme</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Güvenli Ödeme</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Lojistik Takip</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Destek</h4>
              <ul className="space-y-2 text-gray-300">
                <li><a href="#" className="hover:text-white transition-colors">Yardım Merkezi</a></li>
                <li><a href="#" className="hover:text-white transition-colors">SSS</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Gizlilik</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Şartlar</a></li>
              </ul>
            </div>
          </Grid>

          <div className="border-t border-gray-700 mt-8 pt-8">
            {/* Üretici Giriş/Kayıt Bölümü */}
            <div className="text-center mb-6">
              <h4 className="text-lg font-semibold text-white mb-3">Üretici misiniz?</h4>
              <p className="text-gray-300 mb-4">
                Ürünlerinizi satmak ve müşterilere ulaşmak için üretici panelimize katılın.
              </p>
              <div className="flex justify-center gap-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-500 text-gray-300 hover:bg-gray-700 hover:text-white"
                  onClick={showProducerLoginModal}
                >
                  Üretici Giriş Yap
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  className="bg-amber-600 hover:bg-amber-700 text-white"
                  onClick={showProducerRegisterModal}
                >
                  Üretici Üye Ol
                </Button>
              </div>
            </div>

            <div className="text-center text-gray-400">
              <p>&copy; 2025 Türkiye Doğal Taş Pazarı. Tüm hakları saklıdır.</p>
            </div>
          </div>
        </Container>
      </footer>

      {/* SEO Structured Data */}
      <OrganizationStructuredData />
      <WebsiteStructuredData />
    </div>
  )
}
