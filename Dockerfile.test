# Test Runner Dockerfile
FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache \
    curl \
    bash \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# Set working directory
WORKDIR /app

# Set Puppeteer to use installed Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY frontend/package*.json ./frontend/

# Install dependencies
RUN npm ci --only=production

# Copy test files and configurations
COPY tests ./tests/
COPY jest.config.js ./
COPY playwright.config.ts ./

# Copy source code for testing
COPY backend/src ./backend/src/
COPY backend/prisma ./backend/prisma/
COPY frontend/src ./frontend/src/
COPY frontend/public ./frontend/public/

# Create test results directory
RUN mkdir -p test-results

# Install test dependencies
RUN npm install --save-dev \
    jest \
    @types/jest \
    supertest \
    @types/supertest \
    playwright \
    @playwright/test

# Install Playwright browsers
RUN npx playwright install chromium

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Default command runs all tests
CMD ["npm", "run", "test:all"]
