# Production Docker Compose for Natural Stone Marketplace
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: natural-stone-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: natural_stone_marketplace_prod
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - natural-stone-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d natural_stone_marketplace_prod"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: natural-stone-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - natural-stone-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI Marketing Backend
  ai-marketing-backend:
    build:
      context: .
      dockerfile: Dockerfile.ai-marketing
      target: production
    container_name: marketplace-ai-backend-prod
    restart: unless-stopped
    environment:
      # Database
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-marketplace}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD}
      
      # Redis
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      
      # API Keys
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      TRADEMAP_API_KEY: ${TRADEMAP_API_KEY}
      LINKEDIN_CLIENT_ID: ${LINKEDIN_CLIENT_ID}
      LINKEDIN_CLIENT_SECRET: ${LINKEDIN_CLIENT_SECRET}
      LINKEDIN_ACCESS_TOKEN: ${LINKEDIN_ACCESS_TOKEN}
      GOOGLE_ADS_CLIENT_ID: ${GOOGLE_ADS_CLIENT_ID}
      GOOGLE_ADS_CLIENT_SECRET: ${GOOGLE_ADS_CLIENT_SECRET}
      GOOGLE_ADS_REFRESH_TOKEN: ${GOOGLE_ADS_REFRESH_TOKEN}
      GOOGLE_ADS_DEVELOPER_TOKEN: ${GOOGLE_ADS_DEVELOPER_TOKEN}
      GOOGLE_ADS_CUSTOMER_ID: ${GOOGLE_ADS_CUSTOMER_ID}
      
      # Application
      NODE_ENV: production
      PORT: 3001
      JWT_SECRET: ${JWT_SECRET}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000}
      
      # AI Marketing Specific
      AI_MARKETING_ENABLED: true
      LEARNING_CYCLE_INTERVAL: 900000  # 15 minutes
      RESEARCH_CYCLE_INTERVAL: 21600000  # 6 hours
      OPTIMIZATION_CYCLE_INTERVAL: 300000  # 5 minutes
      
      # Logging
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_FORMAT: json
      
    volumes:
      - app_logs:/app/logs
      - app_data:/app/data
      - app_uploads:/app/uploads
    ports:
      - "3001:3001"
    networks:
      - marketplace-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
    container_name: marketplace-frontend-prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:3001}
      NEXT_PUBLIC_FRONTEND_URL: ${NEXT_PUBLIC_FRONTEND_URL:-http://localhost:3000}
    ports:
      - "3000:3000"
    networks:
      - marketplace-network
    depends_on:
      ai-marketing-backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: marketplace-nginx-prod
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - marketplace-network
    depends_on:
      - frontend
      - ai-marketing-backend
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: marketplace-prometheus-prod
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - marketplace-network

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: marketplace-grafana-prod
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "3003:3000"
    networks:
      - marketplace-network
    depends_on:
      - prometheus

  # Log Management - Loki
  loki:
    image: grafana/loki:latest
    container_name: marketplace-loki-prod
    restart: unless-stopped
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./monitoring/loki-config.yaml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    ports:
      - "3100:3100"
    networks:
      - marketplace-network

  # Log Collection - Promtail
  promtail:
    image: grafana/promtail:latest
    container_name: marketplace-promtail-prod
    restart: unless-stopped
    command: -config.file=/etc/promtail/config.yml
    volumes:
      - ./monitoring/promtail-config.yml:/etc/promtail/config.yml:ro
      - app_logs:/var/log/app:ro
      - nginx_logs:/var/log/nginx:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    networks:
      - marketplace-network
    depends_on:
      - loki

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: marketplace-backup-prod
    restart: "no"
    environment:
      PGPASSWORD: ${DB_PASSWORD}
    volumes:
      - ./scripts/backup.sh:/backup.sh:ro
      - backup_data:/backups
    networks:
      - marketplace-network
    depends_on:
      - postgres
    command: /bin/sh -c "chmod +x /backup.sh && crond -f"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  app_data:
    driver: local
  app_uploads:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
  backup_data:
    driver: local

networks:
  marketplace-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
