import { PrismaClient, EscrowStatus, PaymentStatus, OrderStatus } from '@prisma/client';
import { EventEmitter } from 'events';

const prisma = new PrismaClient();

export interface EscrowAccount {
  id: string;
  orderId: string;
  customerId: string;
  producerId: string;
  totalAmount: number;
  platformCommission: number;
  producerAmount: number;
  status: EscrowStatus;
  customerPaidAt?: Date;
  producerNotifiedAt?: Date;
  customerApprovedAt?: Date;
  producerPaidAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface BankTransferInfo {
  bankName: string;
  iban: string;
  accountHolder: string;
  transferAmount: number;
  referenceCode: string;
  transferDeadline: Date;
}

export interface PaymentWorkflowStep {
  step: string;
  status: 'pending' | 'completed' | 'failed';
  completedAt?: Date;
  description: string;
  nextAction?: string;
}

export class EscrowService extends EventEmitter {
  private platformIban: string;
  private platformCommissionRate: number;
  private commissionPerM2: number;
  private commissionCurrency: string;
  private commissionMethod: string;

  constructor() {
    super();
    this.platformIban = process.env.PLATFORM_IBAN || '**************************';
    this.platformCommissionRate = parseFloat(process.env.PLATFORM_COMMISSION_RATE || '0.05'); // 5% default
    this.commissionPerM2 = parseFloat(process.env.PLATFORM_COMMISSION_PER_M2 || '1.00'); // $1 per m²
    this.commissionCurrency = process.env.PLATFORM_COMMISSION_CURRENCY || 'USD';
    this.commissionMethod = process.env.COMMISSION_CALCULATION_METHOD || 'PER_M2';
  }

  /**
   * Calculate commission based on method (percentage or per m²)
   */
  private calculateCommission(totalAmount: number, totalM2?: number): number {
    if (this.commissionMethod === 'PER_M2' && totalM2) {
      // M² bazlı komisyon: $1 per m²
      return totalM2 * this.commissionPerM2;
    } else {
      // Yüzde bazlı komisyon (fallback)
      return totalAmount * this.platformCommissionRate;
    }
  }

  /**
   * Create escrow account for order
   */
  async createEscrowAccount(orderId: string, customerId: string, producerId: string, totalAmount: number, totalM2?: number): Promise<EscrowAccount> {
    const platformCommission = this.calculateCommission(totalAmount, totalM2);
    const producerAmount = totalAmount - platformCommission;

    const escrowAccount = await prisma.escrowAccount.create({
      data: {
        orderId,
        customerId,
        producerId,
        totalAmount,
        platformCommission,
        producerAmount,
        status: EscrowStatus.PENDING,
        referenceCode: this.generateReferenceCode(orderId),
      },
    });

    // Emit event for notifications
    this.emit('escrowCreated', {
      escrowId: escrowAccount.id,
      orderId,
      customerId,
      producerId,
      totalAmount,
    });

    return escrowAccount as EscrowAccount;
  }

  /**
   * Get bank transfer information for customer
   */
  getBankTransferInfo(escrowAccount: EscrowAccount): BankTransferInfo {
    const transferDeadline = new Date();
    transferDeadline.setDate(transferDeadline.getDate() + 3); // 3 days to pay

    return {
      bankName: process.env.PLATFORM_BANK_NAME || 'Türkiye İş Bankası',
      iban: this.platformIban,
      accountHolder: process.env.PLATFORM_ACCOUNT_HOLDER || 'Doğal Taş Pazaryeri Ltd. Şti.',
      transferAmount: escrowAccount.totalAmount,
      referenceCode: escrowAccount.referenceCode,
      transferDeadline,
    };
  }

  /**
   * Confirm customer payment received
   */
  async confirmCustomerPayment(escrowId: string, bankTransferReceipt?: any): Promise<void> {
    const escrowAccount = await prisma.escrowAccount.update({
      where: { id: escrowId },
      data: {
        status: EscrowStatus.HELD,
        customerPaidAt: new Date(),
        bankTransferReceipt,
      },
      include: {
        order: true,
      },
    });

    // Update order status
    await prisma.order.update({
      where: { id: escrowAccount.orderId },
      data: { status: OrderStatus.CONFIRMED },
    });

    // Notify producer that payment is secured
    await this.notifyProducerPaymentSecured(escrowAccount);

    this.emit('customerPaymentConfirmed', {
      escrowId,
      orderId: escrowAccount.orderId,
      customerId: escrowAccount.customerId,
      producerId: escrowAccount.producerId,
    });
  }

  /**
   * Producer notifies that goods are ready
   */
  async notifyGoodsReady(escrowId: string, producerId: string, productionProof?: any): Promise<void> {
    const escrowAccount = await prisma.escrowAccount.findUnique({
      where: { id: escrowId },
      include: { order: true },
    });

    if (!escrowAccount || escrowAccount.producerId !== producerId) {
      throw new Error('Unauthorized or escrow not found');
    }

    if (escrowAccount.status !== EscrowStatus.HELD) {
      throw new Error('Payment not yet secured');
    }

    await prisma.escrowAccount.update({
      where: { id: escrowId },
      data: {
        producerNotifiedAt: new Date(),
        productionProof,
      },
    });

    // Update order status
    await prisma.order.update({
      where: { id: escrowAccount.orderId },
      data: { status: OrderStatus.READY_FOR_SHIPMENT },
    });

    // Notify customer for approval
    await this.notifyCustomerForApproval(escrowAccount);

    this.emit('goodsReady', {
      escrowId,
      orderId: escrowAccount.orderId,
      customerId: escrowAccount.customerId,
      producerId: escrowAccount.producerId,
    });
  }

  /**
   * Customer approves and authorizes payment to producer
   */
  async customerApprovePayment(escrowId: string, customerId: string): Promise<void> {
    const escrowAccount = await prisma.escrowAccount.findUnique({
      where: { id: escrowId },
      include: { order: true },
    });

    if (!escrowAccount || escrowAccount.customerId !== customerId) {
      throw new Error('Unauthorized or escrow not found');
    }

    if (!escrowAccount.producerNotifiedAt) {
      throw new Error('Producer has not notified goods ready yet');
    }

    await prisma.escrowAccount.update({
      where: { id: escrowId },
      data: {
        customerApprovedAt: new Date(),
      },
    });

    // Automatically trigger payment to producer
    await this.payProducer(escrowId);

    this.emit('customerApproved', {
      escrowId,
      orderId: escrowAccount.orderId,
      customerId: escrowAccount.customerId,
      producerId: escrowAccount.producerId,
    });
  }

  /**
   * Pay producer (manual bank transfer)
   */
  async payProducer(escrowId: string): Promise<void> {
    const escrowAccount = await prisma.escrowAccount.findUnique({
      where: { id: escrowId },
      include: { 
        order: true,
        producer: {
          include: { profile: true }
        }
      },
    });

    if (!escrowAccount || !escrowAccount.customerApprovedAt) {
      throw new Error('Customer approval required');
    }

    // Create payment record
    const payment = await prisma.payment.create({
      data: {
        orderId: escrowAccount.orderId,
        userId: escrowAccount.producerId,
        amount: escrowAccount.producerAmount,
        method: 'BANK_TRANSFER',
        status: PaymentStatus.PENDING,
        description: `Payment for order ${escrowAccount.order.orderNumber}`,
      },
    });

    // Update escrow status
    await prisma.escrowAccount.update({
      where: { id: escrowId },
      data: {
        status: EscrowStatus.RELEASED,
        producerPaidAt: new Date(),
      },
    });

    // Update order status
    await prisma.order.update({
      where: { id: escrowAccount.orderId },
      data: { status: OrderStatus.DELIVERED },
    });

    this.emit('producerPaid', {
      escrowId,
      paymentId: payment.id,
      orderId: escrowAccount.orderId,
      producerId: escrowAccount.producerId,
      amount: escrowAccount.producerAmount,
    });
  }

  /**
   * Handle dispute - refund customer
   */
  async handleDispute(escrowId: string, reason: string, adminId: string): Promise<void> {
    const escrowAccount = await prisma.escrowAccount.findUnique({
      where: { id: escrowId },
      include: { order: true },
    });

    if (!escrowAccount) {
      throw new Error('Escrow not found');
    }

    // Create refund payment
    const refund = await prisma.payment.create({
      data: {
        orderId: escrowAccount.orderId,
        userId: escrowAccount.customerId,
        amount: escrowAccount.totalAmount,
        method: 'BANK_TRANSFER',
        status: PaymentStatus.PENDING,
        description: `Refund for order ${escrowAccount.order.orderNumber} - ${reason}`,
      },
    });

    // Update escrow status
    await prisma.escrowAccount.update({
      where: { id: escrowId },
      data: {
        status: EscrowStatus.REFUNDED,
        disputeReason: reason,
        resolvedBy: adminId,
        resolvedAt: new Date(),
      },
    });

    // Update order status
    await prisma.order.update({
      where: { id: escrowAccount.orderId },
      data: { status: OrderStatus.CANCELLED },
    });

    this.emit('disputeResolved', {
      escrowId,
      refundId: refund.id,
      orderId: escrowAccount.orderId,
      customerId: escrowAccount.customerId,
      reason,
    });
  }

  /**
   * Get payment workflow status
   */
  async getPaymentWorkflow(escrowId: string): Promise<PaymentWorkflowStep[]> {
    const escrowAccount = await prisma.escrowAccount.findUnique({
      where: { id: escrowId },
    });

    if (!escrowAccount) {
      throw new Error('Escrow not found');
    }

    const workflow: PaymentWorkflowStep[] = [
      {
        step: 'customer_payment',
        status: escrowAccount.customerPaidAt ? 'completed' : 'pending',
        completedAt: escrowAccount.customerPaidAt,
        description: 'Müşteri ödeme yapması bekleniyor',
        nextAction: escrowAccount.customerPaidAt ? undefined : 'Banka havalesi ile ödeme yapın',
      },
      {
        step: 'production',
        status: escrowAccount.producerNotifiedAt ? 'completed' : escrowAccount.customerPaidAt ? 'pending' : 'pending',
        completedAt: escrowAccount.producerNotifiedAt,
        description: 'Üretici malı hazırlaması bekleniyor',
        nextAction: escrowAccount.producerNotifiedAt ? undefined : 'Üretici malı hazırlayıp bildirecek',
      },
      {
        step: 'customer_approval',
        status: escrowAccount.customerApprovedAt ? 'completed' : escrowAccount.producerNotifiedAt ? 'pending' : 'pending',
        completedAt: escrowAccount.customerApprovedAt,
        description: 'Müşteri onayı bekleniyor',
        nextAction: escrowAccount.customerApprovedAt ? undefined : 'Müşteri malı onaylayıp ödeme talimatı verecek',
      },
      {
        step: 'producer_payment',
        status: escrowAccount.producerPaidAt ? 'completed' : escrowAccount.customerApprovedAt ? 'pending' : 'pending',
        completedAt: escrowAccount.producerPaidAt,
        description: 'Üreticiye ödeme yapılması',
        nextAction: escrowAccount.producerPaidAt ? undefined : 'Platform üreticiye ödeme yapacak',
      },
    ];

    return workflow;
  }

  /**
   * Generate unique reference code for bank transfer
   */
  private generateReferenceCode(orderId: string): string {
    const timestamp = Date.now().toString().slice(-6);
    const orderSuffix = orderId.slice(-4);
    return `DTP${timestamp}${orderSuffix}`.toUpperCase();
  }

  /**
   * Notify producer that payment is secured
   */
  private async notifyProducerPaymentSecured(escrowAccount: any): Promise<void> {
    // This will be implemented with notification service
    console.log(`Notifying producer ${escrowAccount.producerId} that payment is secured for order ${escrowAccount.orderId}`);
  }

  /**
   * Notify customer for approval
   */
  private async notifyCustomerForApproval(escrowAccount: any): Promise<void> {
    // This will be implemented with notification service
    console.log(`Notifying customer ${escrowAccount.customerId} for approval of order ${escrowAccount.orderId}`);
  }
}
