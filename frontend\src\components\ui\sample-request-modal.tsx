'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  BeakerIcon,
  TruckIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { useSample, CreateSampleRequestData } from '@/contexts/sample-context';

interface SampleRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  quoteRequestId: string;
  quoteId: string;
  customerId: string;
  producerId: string;
  products: {
    id: string;
    name: string;
    category?: string;
  }[];
}

const SampleRequestModal: React.FC<SampleRequestModalProps> = ({
  isOpen,
  onClose,
  quoteRequestId,
  quoteId,
  customerId,
  producerId,
  products
}) => {
  const { createSampleRequest, isLoading } = useSample();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    selectedProducts: [] as string[],
    deliveryAddress: {
      name: '',
      address: '',
      city: '',
      country: 'Türkiye',
      phone: ''
    },
    specialRequirements: ''
  });

  const handleProductSelection = (productId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedProducts: prev.selectedProducts.includes(productId)
        ? prev.selectedProducts.filter(id => id !== productId)
        : [...prev.selectedProducts, productId]
    }));
  };

  const handleAddressChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      deliveryAddress: {
        ...prev.deliveryAddress,
        [field]: value
      }
    }));
  };

  const handleSubmit = async () => {
    try {
      if (formData.selectedProducts.length === 0) {
        alert('Lütfen en az bir ürün seçin.');
        return;
      }

      if (!formData.deliveryAddress.name || !formData.deliveryAddress.address || !formData.deliveryAddress.phone) {
        alert('Lütfen teslimat bilgilerini eksiksiz doldurun.');
        return;
      }

      const requestData: CreateSampleRequestData = {
        quoteRequestId,
        quoteId,
        customerId,
        producerId,
        requestedProducts: formData.selectedProducts.map(productId => {
          const product = products.find(p => p.id === productId);
          return {
            productId,
            productName: product?.name || '',
            sampleSize: '10x10 cm',
            specifications: 'Standart numune'
          };
        }),
        deliveryAddress: formData.deliveryAddress,
        specialRequirements: formData.specialRequirements || undefined
      };

      await createSampleRequest(requestData);
      alert('Numune talebi başarıyla oluşturuldu!');
      onClose();
      
      // Reset form
      setCurrentStep(1);
      setFormData({
        selectedProducts: [],
        deliveryAddress: {
          name: '',
          address: '',
          city: '',
          country: 'Türkiye',
          phone: ''
        },
        specialRequirements: ''
      });
    } catch (error) {
      console.error('Error creating sample request:', error);
      alert('Numune talebi oluşturulurken hata oluştu.');
    }
  };

  const nextStep = () => {
    if (currentStep === 1 && formData.selectedProducts.length === 0) {
      alert('Lütfen en az bir ürün seçin.');
      return;
    }
    setCurrentStep(prev => Math.min(prev + 1, 3));
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <BeakerIcon className="h-6 w-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">Numune Talep Et</h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Progress Steps */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step <= currentStep 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {step}
                  </div>
                  {step < 3 && (
                    <div className={`w-16 h-1 mx-2 ${
                      step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-600">
              <span>Ürün Seçimi</span>
              <span>Teslimat Bilgileri</span>
              <span>Özel Gereksinimler</span>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Step 1: Product Selection */}
            {currentStep === 1 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Numune İstediğiniz Ürünleri Seçin</h3>
                <div className="space-y-3">
                  {products.map((product) => (
                    <div
                      key={product.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        formData.selectedProducts.includes(product.id)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleProductSelection(product.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900">{product.name}</h4>
                          {product.category && (
                            <p className="text-sm text-gray-600">{product.category}</p>
                          )}
                        </div>
                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                          formData.selectedProducts.includes(product.id)
                            ? 'border-blue-500 bg-blue-500'
                            : 'border-gray-300'
                        }`}>
                          {formData.selectedProducts.includes(product.id) && (
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Not:</strong> Numuneler 10x10 cm boyutunda gönderilecektir. Kargo ücreti tarafınıza aittir.
                  </p>
                </div>
              </div>
            )}

            {/* Step 2: Delivery Address */}
            {currentStep === 2 && (
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <TruckIcon className="h-5 w-5 text-gray-600" />
                  <h3 className="text-lg font-medium text-gray-900">Teslimat Bilgileri</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ad Soyad / Firma Adı *
                    </label>
                    <input
                      type="text"
                      value={formData.deliveryAddress.name}
                      onChange={(e) => handleAddressChange('name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Teslimat alacak kişi/firma"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Telefon *
                    </label>
                    <input
                      type="tel"
                      value={formData.deliveryAddress.phone}
                      onChange={(e) => handleAddressChange('phone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="+90 ************"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Adres *
                    </label>
                    <textarea
                      value={formData.deliveryAddress.address}
                      onChange={(e) => handleAddressChange('address', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Detaylı teslimat adresi"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Şehir
                    </label>
                    <input
                      type="text"
                      value={formData.deliveryAddress.city}
                      onChange={(e) => handleAddressChange('city', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Şehir"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ülke
                    </label>
                    <input
                      type="text"
                      value={formData.deliveryAddress.country}
                      onChange={(e) => handleAddressChange('country', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Special Requirements */}
            {currentStep === 3 && (
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <DocumentTextIcon className="h-5 w-5 text-gray-600" />
                  <h3 className="text-lg font-medium text-gray-900">Özel Gereksinimler</h3>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Özel İstekleriniz (İsteğe Bağlı)
                  </label>
                  <textarea
                    value={formData.specialRequirements}
                    onChange={(e) => setFormData(prev => ({ ...prev, specialRequirements: e.target.value }))}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Numune ile ilgili özel isteklerinizi belirtebilirsiniz..."
                  />
                </div>
                
                {/* Summary */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Talep Özeti</h4>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p><strong>Seçilen Ürünler:</strong> {formData.selectedProducts.length} adet</p>
                    <p><strong>Teslimat:</strong> {formData.deliveryAddress.name}</p>
                    <p><strong>Adres:</strong> {formData.deliveryAddress.city}, {formData.deliveryAddress.country}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Geri
            </button>
            
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
              >
                İptal
              </button>
              
              {currentStep < 3 ? (
                <button
                  onClick={nextStep}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                >
                  İleri
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  disabled={isLoading}
                  className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Gönderiliyor...' : 'Numune Talep Et'}
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default SampleRequestModal;
