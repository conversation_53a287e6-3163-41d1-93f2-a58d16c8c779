'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Card } from '@/components/ui/card'
import {
  CheckCircle,
  XCircle,
  FileText,
  User,
  Building,
  Calendar,
  DollarSign,
  CreditCard,
  Package,
  Download,
  Eye,
  AlertTriangle
} from 'lucide-react'

interface BankTransferApprovalModalProps {
  transfer: any
  isOpen: boolean
  onClose: () => void
  onApprove: (transferId: string, reason?: string) => void
  onReject: (transferId: string, reason: string) => void
}

export function BankTransferApprovalModal({
  transfer,
  isOpen,
  onClose,
  onApprove,
  onReject
}: BankTransferApprovalModalProps) {
  const [action, setAction] = useState<'approve' | 'reject' | null>(null)
  const [reason, setReason] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)

  const handleSubmit = async () => {
    if (!action) return

    setIsProcessing(true)
    try {
      if (action === 'approve') {
        await onApprove(transfer.id, reason)
      } else {
        if (!reason.trim()) {
          alert('Red sebebi zorunludur')
          return
        }
        await onReject(transfer.id, reason)
      }
      onClose()
    } catch (error) {
      console.error('Error processing transfer:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!transfer) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Banka Havalesi Onay - {transfer.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Basic Info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge 
                variant={transfer.status === 'pending' ? 'secondary' : 'outline'}
                className={
                  transfer.status === 'pending' 
                    ? 'bg-yellow-100 text-yellow-800' 
                    : transfer.status === 'verified'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }
              >
                {transfer.status === 'pending' ? 'Onay Bekliyor' : 
                 transfer.status === 'verified' ? 'Onaylandı' : 'Reddedildi'}
              </Badge>
              <span className="text-sm text-gray-500">
                Yükleme: {formatDate(transfer.uploadedAt)}
              </span>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-1" />
                Makbuzu İndir
              </Button>
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4 mr-1" />
                Makbuzu Görüntüle
              </Button>
            </div>
          </div>

          {/* Customer and Order Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <User className="w-5 h-5" />
                Müşteri Bilgileri
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Firma Adı</label>
                  <p className="text-gray-900">{transfer.customerName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">E-posta</label>
                  <p className="text-gray-900">{transfer.customerEmail}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Ödeme ID</label>
                  <p className="text-gray-900 font-mono text-sm">{transfer.paymentId}</p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Package className="w-5 h-5" />
                Sipariş Bilgileri
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Sipariş No</label>
                  <p className="text-gray-900">{transfer.orderInfo.orderId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Ürün</label>
                  <p className="text-gray-900">{transfer.orderInfo.productName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Miktar</label>
                  <p className="text-gray-900">{transfer.orderInfo.quantity}</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Payment Amount */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Ödeme Tutarı
            </h3>
            <div className="flex items-center gap-4">
              <div className="text-3xl font-bold text-gray-900">
                ${transfer.amount.toLocaleString()} {transfer.currency}
              </div>
              <Badge variant="outline" className="text-sm">
                Ön Ödeme (%30)
              </Badge>
            </div>
          </Card>

          {/* Bank Transfer Details */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Banka Havalesi Detayları
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Gönderen Hesap</label>
                  <p className="text-gray-900 font-mono text-sm">{transfer.bankDetails.senderAccount}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Gönderen Adı</label>
                  <p className="text-gray-900">{transfer.bankDetails.senderName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Havale Tarihi</label>
                  <p className="text-gray-900">{transfer.bankDetails.transferDate}</p>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Alıcı Hesap</label>
                  <p className="text-gray-900 font-mono text-sm">{transfer.bankDetails.receiverAccount}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Referans No</label>
                  <p className="text-gray-900 font-mono text-sm">{transfer.bankDetails.referenceNumber}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Havale Tutarı</label>
                  <p className="text-gray-900 font-semibold">
                    ${transfer.bankDetails.amount.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </Card>

          {/* Receipt Preview */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Makbuz Önizleme
            </h3>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-2">Makbuz dosyası: {transfer.receiptUrl}</p>
              <p className="text-sm text-gray-500">
                Makbuzu görüntülemek için "Makbuzu Görüntüle" butonuna tıklayın
              </p>
            </div>
          </Card>

          {/* Verification Actions */}
          {transfer.status === 'pending' && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Onay İşlemi
              </h3>
              
              {!action && (
                <div className="flex gap-4">
                  <Button
                    onClick={() => setAction('approve')}
                    className="flex-1 bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Onayla
                  </Button>
                  <Button
                    onClick={() => setAction('reject')}
                    variant="destructive"
                    className="flex-1"
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    Reddet
                  </Button>
                </div>
              )}

              {action && (
                <div className="space-y-4">
                  <div className="flex items-center gap-2 p-3 rounded-lg bg-gray-50">
                    {action === 'approve' ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                    <span className="font-medium">
                      {action === 'approve' ? 'Ödemeyi Onaylıyorsunuz' : 'Ödemeyi Reddediyorsunuz'}
                    </span>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {action === 'approve' ? 'Onay Notu (İsteğe bağlı)' : 'Red Sebebi (Zorunlu)'}
                    </label>
                    <Textarea
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      placeholder={
                        action === 'approve' 
                          ? 'Onay ile ilgili notlarınızı yazabilirsiniz...'
                          : 'Red sebebini detaylı olarak açıklayın...'
                      }
                      rows={3}
                      className="w-full"
                    />
                    {action === 'reject' && !reason.trim() && (
                      <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                        <AlertTriangle className="w-4 h-4" />
                        Red sebebi zorunludur
                      </p>
                    )}
                  </div>

                  <div className="flex gap-3">
                    <Button
                      onClick={handleSubmit}
                      disabled={isProcessing || (action === 'reject' && !reason.trim())}
                      className={action === 'approve' ? 'bg-green-600 hover:bg-green-700' : ''}
                      variant={action === 'approve' ? 'default' : 'destructive'}
                    >
                      {isProcessing ? 'İşleniyor...' : 
                       action === 'approve' ? 'Onayı Tamamla' : 'Reddi Tamamla'}
                    </Button>
                    <Button
                      onClick={() => {
                        setAction(null)
                        setReason('')
                      }}
                      variant="outline"
                      disabled={isProcessing}
                    >
                      İptal
                    </Button>
                  </div>
                </div>
              )}
            </Card>
          )}

          {/* Already processed info */}
          {transfer.status !== 'pending' && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                İşlem Geçmişi
              </h3>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  <strong>Durum:</strong> {transfer.status === 'verified' ? 'Onaylandı' : 'Reddedildi'}
                </p>
                {transfer.verifiedAt && (
                  <p className="text-sm text-gray-600">
                    <strong>İşlem Tarihi:</strong> {formatDate(transfer.verifiedAt)}
                  </p>
                )}
                {transfer.verifiedBy && (
                  <p className="text-sm text-gray-600">
                    <strong>İşlem Yapan:</strong> {transfer.verifiedBy}
                  </p>
                )}
              </div>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
