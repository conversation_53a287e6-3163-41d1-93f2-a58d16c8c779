'use client'

import * as React from 'react'
import { useRouter } from 'next/navigation'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Package,
  FileText,
  ShoppingCart,
  TrendingUp,
  DollarSign,
  Users,
  AlertCircle,
  CheckCircle
} from 'lucide-react'

// Mock data for demonstration
const mockStats = {
  totalProducts: 24,
  activeQuoteRequests: 8,
  totalOrders: 156,
  monthlyRevenue: 45000,
  pendingApproval: 3,
  completedOrders: 142
}

const mockRecentQuoteRequests = [
  {
    id: '1',
    customerName: 'ABC İnşaat Ltd.',
    productName: 'Beyaz Mermer',
    quantity: '500 m²',
    requestDate: '2025-06-28',
    status: 'pending'
  },
  {
    id: '2',
    customerName: 'XYZ Mimarlık',
    productName: 'Traverten Plaka',
    quantity: '200 m²',
    requestDate: '2025-06-27',
    status: 'quoted'
  },
  {
    id: '3',
    customerName: 'DEF Yapı',
    productName: 'Granit Blok',
    quantity: '50 ton',
    requestDate: '2025-06-26',
    status: 'accepted'
  }
]

const mockRecentOrders = [
  {
    id: '1',
    customerName: 'ABC İnşaat Ltd.',
    productName: 'Beyaz Mermer',
    quantity: '300 m²',
    value: 15000,
    status: 'production',
    orderDate: '2025-06-25'
  },
  {
    id: '2',
    customerName: 'GHI Dekorasyon',
    productName: 'Oniks Plaka',
    quantity: '100 m²',
    value: 8000,
    status: 'shipped',
    orderDate: '2025-06-24'
  }
]

export default function ProducerDashboard() {
  const { producer } = useProducerAuth()
  const router = useRouter()

  // Navigation handlers
  const handleViewAllQuoteRequests = () => {
    router.push('/producer/quote-requests')
  }

  const handleViewAllOrders = () => {
    router.push('/producer/orders')
  }

  const handleAddNewProduct = () => {
    router.push('/producer/products/add')
  }

  const handleViewQuoteRequests = () => {
    router.push('/producer/quote-requests')
  }

  const handleViewAnalytics = () => {
    router.push('/producer/analytics')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100'
      case 'quoted':
        return 'text-blue-600 bg-blue-100'
      case 'accepted':
        return 'text-green-600 bg-green-100'
      case 'production':
        return 'text-orange-600 bg-orange-100'
      case 'shipped':
        return 'text-purple-600 bg-purple-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'quoted':
        return 'Teklif Verildi'
      case 'accepted':
        return 'Kabul Edildi'
      case 'production':
        return 'Üretimde'
      case 'shipped':
        return 'Gönderildi'
      default:
        return status
    }
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-amber-500 to-orange-500 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Hoş geldiniz, {producer?.name}!
        </h1>
        <p className="text-amber-100">
          {producer?.companyName} - Üretici Dashboard
        </p>
        {!producer?.isApproved && (
          <div className="mt-4 p-3 bg-yellow-500 bg-opacity-20 rounded-lg border border-yellow-300">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              <span className="text-sm">
                Hesabınız henüz onaylanmamıştır. Onay sürecinin tamamlanması için lütfen bekleyiniz.
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Ürün</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              Aktif ürün sayısı
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Teklif Talepleri</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.activeQuoteRequests}</div>
            <p className="text-xs text-muted-foreground">
              Bekleyen talepler
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Sipariş</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              Tüm zamanlar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aylık Gelir</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockStats.monthlyRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Bu ay
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Quote Requests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Son Teklif Talepleri
              <Button variant="outline" size="sm" onClick={handleViewAllQuoteRequests}>
                Tümünü Gör
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockRecentQuoteRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-sm">{request.customerName}</p>
                    <p className="text-sm text-gray-600">{request.productName}</p>
                    <p className="text-xs text-gray-500">{request.quantity} - {request.requestDate}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                    {getStatusText(request.status)}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Son Siparişler
              <Button variant="outline" size="sm" onClick={handleViewAllOrders}>
                Tümünü Gör
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockRecentOrders.map((order) => (
                <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-sm">{order.customerName}</p>
                    <p className="text-sm text-gray-600">{order.productName}</p>
                    <p className="text-xs text-gray-500">{order.quantity} - ${order.value.toLocaleString()}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                    {getStatusText(order.status)}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Hızlı İşlemler</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={handleAddNewProduct}
            >
              <Package className="w-6 h-6" />
              <span>Yeni Ürün Ekle</span>
            </Button>
            <Button
              className="h-20 flex flex-col items-center justify-center space-y-2 bg-orange-600 hover:bg-orange-700"
              onClick={() => {
                // Ocak sahipliği kontrolü
                const storedProducer = localStorage.getItem('producer')
                if (storedProducer) {
                  const producer = JSON.parse(storedProducer)
                  if (producer.hasQuarry) {
                    router.push('/producer/blocks/add')
                  } else {
                    router.push('/producer/blocks')
                  }
                } else {
                  router.push('/producer/blocks')
                }
              }}
            >
              <div className="text-2xl">🧱</div>
              <span>Blok Ekle</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={handleViewQuoteRequests}
            >
              <FileText className="w-6 h-6" />
              <span>Teklif Talepleri</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={handleViewAnalytics}
            >
              <TrendingUp className="w-6 h-6" />
              <span>Satış Analizi</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
