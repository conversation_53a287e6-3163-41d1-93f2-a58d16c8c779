'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/utils';
import {
  Bot,
  Mail,
  Share2,
  Users,
  TrendingUp,
  Settings,
  BarChart3,
  CheckCircle
} from 'lucide-react';

const aiMarketingNavItems = [
  {
    name: '<PERSON><PERSON>',
    href: '/admin/ai-marketing',
    icon: Bot,
    description: 'AI pazarlama sistem durumu'
  },
  {
    name: 'Email Marketing',
    href: '/admin/ai-marketing/email',
    icon: Mail,
    description: 'Ülke bazlı email listeleri ve kampanyalar'
  },
  {
    name: 'Sosyal Medya',
    href: '/admin/ai-marketing/social',
    icon: Share2,
    description: 'Platform yönetimi ve içerik üretimi'
  },
  {
    name: '<PERSON><PERSON>ş<PERSON>i Arama',
    href: '/admin/ai-marketing/customers',
    icon: Users,
    description: '<PERSON>tan<PERSON><PERSON>l müşteri bulma ve iletişim'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    href: '/admin/ai-marketing/ads',
    icon: TrendingUp,
    description: 'Google ve sosyal medya reklamlar<PERSON>'
  },
  {
    name: 'Onay Sistemi',
    href: '/admin/ai-marketing/approvals',
    icon: CheckCircle,
    description: 'İçerik onay ve yönetimi'
  },
  {
    name: 'Analitik',
    href: '/admin/ai-marketing/analytics',
    icon: BarChart3,
    description: 'Performans raporları ve istatistikler'
  },
  {
    name: 'AI Ayarları',
    href: '/admin/ai-marketing/settings',
    icon: Settings,
    description: 'AI model ve sistem ayarları'
  }
];

interface AIMarketingLayoutProps {
  children: React.ReactNode;
}

export default function AIMarketingLayout({ children }: AIMarketingLayoutProps) {
  const pathname = usePathname();

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-80 bg-white shadow-sm border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Bot className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">AI Pazarlama</h1>
              <p className="text-sm text-gray-500">Dijital pazarlama otomasyonu</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {aiMarketingNavItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-start space-x-3 p-3 rounded-lg transition-colors duration-200',
                  isActive
                    ? 'bg-blue-50 border border-blue-200 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                )}
              >
                <Icon className={cn(
                  'w-5 h-5 mt-0.5 flex-shrink-0',
                  isActive ? 'text-blue-600' : 'text-gray-400'
                )} />
                <div className="flex-1 min-w-0">
                  <p className={cn(
                    'text-sm font-medium',
                    isActive ? 'text-blue-700' : 'text-gray-900'
                  )}>
                    {item.name}
                  </p>
                  <p className={cn(
                    'text-xs mt-1',
                    isActive ? 'text-blue-600' : 'text-gray-500'
                  )}>
                    {item.description}
                  </p>
                </div>
              </Link>
            );
          })}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span>AI Sistemleri Aktif</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto">
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
