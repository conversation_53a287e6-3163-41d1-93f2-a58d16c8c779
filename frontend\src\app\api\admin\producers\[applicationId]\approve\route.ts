import { NextRequest, NextResponse } from 'next/server'

export async function POST(
  request: NextRequest,
  { params }: { params: { applicationId: string } }
) {
  try {
    // Admin token'ını cookie'den al
    const adminToken = request.cookies.get('admin_token')

    if (!adminToken?.value) {
      return NextResponse.json(
        { success: false, message: 'Admin token not found' },
        { status: 401 }
      )
    }

    // Request body'yi al
    const body = await request.json()

    // Backend API'ye istek yap - cookie ile
    const response = await fetch(`http://localhost:8000/api/admin/producers/${params.applicationId}/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `admin_token=${adminToken.value}`
      },
      body: JSON.stringify(body)
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || 'API request failed' },
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('API proxy error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
