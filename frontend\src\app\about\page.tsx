'use client';


import { Navigation } from "@/components/ui/navigation";
import { Button } from "@/components/ui/button";
import { useAuth } from '@/contexts/auth-context';

// Metadata moved to layout.tsx since this is a client component

const navigationLinks = [
  { name: "<PERSON>", href: "/" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/products" },
  { name: "3D Sanal Showroom", href: "/3d-showroom" },
  { name: "<PERSON><PERSON><PERSON>", href: "/news" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/about", active: true },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/contact" }
];

const stats = [
  { number: "500+", label: "Aktif Üretici" },
  { number: "10,000+", label: "Ürün Çeşidi" },
  { number: "50+", label: "Ülkeye İhracat" },
  { number: "1M+", label: "<PERSON><PERSON><PERSON>" }
];

const values = [
  {
    title: "Güvenilirlik",
    description: "Tüm işlemlerimizde şeffaflık ve güven önceliğimizdir.",
    icon: "🛡️"
  },
  {
    title: "Kali<PERSON>",
    description: "Yalnızca en kaliteli doğal taş ürünlerini platformumuzda bulundururuz.",
    icon: "⭐"
  },
  {
    title: "İnovasyon",
    description: "3D teknolojisi ve yapay zeka ile sektöre yenilik getiriyoruz.",
    icon: "🚀"
  },
  {
    title: "Sürdürülebilirlik",
    description: "Çevre dostu üretim ve sürdürülebilir madencilik uygulamalarını destekliyoruz.",
    icon: "🌱"
  }
];

export default function AboutPage() {
  const { user, isAuthenticated, logout, showLoginModal, showCustomerRegisterModal } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50">
      <Navigation
        brand={{
          name: "Türkiye Doğal Taş Pazarı",
          href: "/"
        }}
        links={navigationLinks}
        actions={
          <div className="flex items-center gap-2">
            {!isAuthenticated ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={showLoginModal}
                >
                  Giriş Yap
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={showCustomerRegisterModal}
                >
                  Kayıt Ol
                </Button>
              </>
            ) : (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 max-w-[200px] truncate">
                  Hoş geldiniz, {user?.company || user?.name}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={logout}
                >
                  Çıkış Yap
                </Button>
              </div>
            )}
          </div>
        }
      />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-stone-900 mb-6">
            Türkiye'nin En Büyük
            <br />
            <span className="text-amber-600">Doğal Taş Pazarı</span>
          </h1>
          <p className="text-xl text-stone-600 max-w-3xl mx-auto">
            2020 yılından bu yana doğal taş sektöründe üreticiler ve alıcıları buluşturan, 
            güvenilir ve yenilikçi B2B platformuyuz.
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center bg-white rounded-lg p-6 shadow-sm border border-stone-200">
              <div className="text-3xl font-bold text-amber-600 mb-2">
                {stat.number}
              </div>
              <div className="text-stone-600 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div className="bg-white rounded-lg p-8 shadow-sm border border-stone-200">
            <div className="text-4xl mb-4">🎯</div>
            <h2 className="text-2xl font-bold text-stone-900 mb-4">Misyonumuz</h2>
            <p className="text-stone-600 leading-relaxed">
              Türkiye'nin zengin doğal taş kaynaklarını dünya pazarlarına tanıtmak, 
              üreticiler ve alıcılar arasında güvenilir bir köprü kurmak. 
              Teknoloji ile geleneksel sektörü buluşturarak, herkes için adil ve 
              şeffaf bir ticaret ortamı yaratmak.
            </p>
          </div>

          <div className="bg-white rounded-lg p-8 shadow-sm border border-stone-200">
            <div className="text-4xl mb-4">🔮</div>
            <h2 className="text-2xl font-bold text-stone-900 mb-4">Vizyonumuz</h2>
            <p className="text-stone-600 leading-relaxed">
              Doğal taş ticaretinde küresel lider platform olmak. 
              3D teknolojisi, yapay zeka ve sürdürülebilir uygulamalarla 
              sektörün dijital dönüşümüne öncülük etmek. 
              Türk doğal taşını dünya markası haline getirmek.
            </p>
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-stone-900 mb-12">
            Değerlerimiz
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm border border-stone-200 text-center">
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-semibold text-stone-900 mb-3">
                  {value.title}
                </h3>
                <p className="text-stone-600 text-sm">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Story Section */}
        <div className="bg-white rounded-lg p-8 shadow-sm border border-stone-200 mb-16">
          <h2 className="text-3xl font-bold text-stone-900 mb-6 text-center">
            Hikayemiz
          </h2>
          <div className="prose prose-stone max-w-none">
            <p className="text-stone-600 leading-relaxed mb-4">
              Türkiye Doğal Taş Pazarı, doğal taş sektöründeki deneyimli profesyoneller tarafından 
              2020 yılında kuruldu. Sektördeki geleneksel ticaret yöntemlerinin dijital çağın 
              gereksinimlerini karşılamadığını gören ekibimiz, modern teknoloji ile 
              geleneksel sektörü buluşturmaya karar verdi.
            </p>
            <p className="text-stone-600 leading-relaxed mb-4">
              İlk günden itibaren amacımız, Türkiye'nin zengin doğal taş kaynaklarını 
              dünya pazarlarına en etkili şekilde tanıtmak ve üreticilerimizin 
              küresel pazarlarda rekabet edebilmelerini sağlamaktı. 
            </p>
            <p className="text-stone-600 leading-relaxed">
              Bugün 500'den fazla üretici ve binlerce alıcının güvendiği platformumuz, 
              sürekli yenilik ve gelişim ile doğal taş ticaretinin geleceğini şekillendirmeye 
              devam ediyor. 3D sanal showroom teknolojimiz ve yapay zeka destekli 
              eşleştirme sistemimizle sektöre öncülük ediyoruz.
            </p>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-stone-900 text-white rounded-lg p-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Bizimle İş Yapmaya Hazır mısınız?
          </h2>
          <p className="text-stone-300 mb-6 max-w-2xl mx-auto">
            Doğal taş sektöründe güvenilir iş ortağınız olmaya hazırız. 
            Hemen başlayın ve farkı yaşayın.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="primary" size="lg">
              Üretici Olarak Katıl
            </Button>
            <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-stone-900">
              Alıcı Olarak Katıl
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
}
