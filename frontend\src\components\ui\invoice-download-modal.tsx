'use client'

import * as React from 'react'
import { <PERSON>alog, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Download, 
  FileText, 
  Calendar,
  DollarSign,
  User,
  Package,
  CheckCircle,
  Printer,
  Mail,
  Eye
} from 'lucide-react'

interface InvoiceDownloadModalProps {
  isOpen: boolean
  onClose: () => void
  order: any
  onDownload: (format: string) => void
  onSendEmail: () => void
  onPrint: () => void
}

export function InvoiceDownloadModal({
  isOpen,
  onClose,
  order,
  onDownload,
  onSendEmail,
  onPrint
}: InvoiceDownloadModalProps) {
  const [selectedFormat, setSelectedFormat] = React.useState('pdf')
  const [isLoading, setIsLoading] = React.useState(false)

  const handleDownload = async (format: string) => {
    setIsLoading(true)
    try {
      await onDownload(format)
      alert(`Fatura ${format.toUpperCase()} formatında indirildi!`)
    } catch (error) {
      console.error('Error downloading invoice:', error)
      alert('Fatura indirilirken hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSendEmail = async () => {
    setIsLoading(true)
    try {
      await onSendEmail()
      alert('Fatura müşteriye e-posta ile gönderildi!')
    } catch (error) {
      console.error('Error sending email:', error)
      alert('E-posta gönderilirken hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePrint = async () => {
    setIsLoading(true)
    try {
      await onPrint()
      alert('Fatura yazdırma işlemi başlatıldı!')
    } catch (error) {
      console.error('Error printing invoice:', error)
      alert('Yazdırma işlemi sırasında hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  const calculateTax = (amount: number) => {
    return (amount * 0.18).toFixed(2) // %18 KDV
  }

  const calculateTotal = (amount: number) => {
    const tax = parseFloat(calculateTax(amount))
    return (amount + tax).toFixed(2)
  }

  if (!order) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Fatura İşlemleri - {order.invoiceNumber}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Invoice Preview */}
          <Card className="bg-gray-50 border-gray-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-gray-800">Fatura Önizleme</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Invoice Header */}
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-xl font-bold text-gray-900">FATURA</h3>
                  <p className="text-gray-600">#{order.invoiceNumber}</p>
                </div>
                <Badge className="bg-green-100 text-green-800">
                  ÖDENDİ
                </Badge>
              </div>

              {/* Company and Customer Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">Satıcı Bilgileri</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p className="font-medium">Taş Üretim A.Ş.</p>
                    <p>Sanayi Mah. Fabrika Cad. No:123</p>
                    <p>Afyon / Türkiye</p>
                    <p>Tel: +90 272 123 45 67</p>
                    <p>Vergi No: 1234567890</p>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">Alıcı Bilgileri</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p className="font-medium">{order.customerName}</p>
                    <p>{order.customerEmail}</p>
                    <p>{order.customerPhone}</p>
                    <p>Proje: {order.projectType}</p>
                  </div>
                </div>
              </div>

              {/* Invoice Details */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Fatura Tarihi:</span>
                  <p className="text-gray-900">{formatDate(order.completedDate)}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Vade Tarihi:</span>
                  <p className="text-gray-900">{formatDate(order.deliveredDate)}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Sipariş Tarihi:</span>
                  <p className="text-gray-900">{formatDate(order.orderDate)}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Teslimat:</span>
                  <p className="text-gray-900">{order.deliveryMethod}</p>
                </div>
              </div>

              {/* Product Details */}
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="text-left p-3 text-sm font-medium text-gray-700">Ürün</th>
                      <th className="text-right p-3 text-sm font-medium text-gray-700">Miktar</th>
                      <th className="text-right p-3 text-sm font-medium text-gray-700">Birim Fiyat</th>
                      <th className="text-right p-3 text-sm font-medium text-gray-700">Toplam</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t">
                      <td className="p-3">
                        <div>
                          <p className="font-medium">{order.productName}</p>
                          <p className="text-sm text-gray-500">{order.projectType}</p>
                        </div>
                      </td>
                      <td className="p-3 text-right">{order.quantity} {order.unit}</td>
                      <td className="p-3 text-right">
                        {(order.totalValue / order.quantity).toFixed(2)} {order.currency}
                      </td>
                      <td className="p-3 text-right font-medium">
                        {order.totalValue.toLocaleString()} {order.currency}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              {/* Totals */}
              <div className="border-t pt-4">
                <div className="flex justify-end">
                  <div className="w-64 space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Ara Toplam:</span>
                      <span>{order.totalValue.toLocaleString()} {order.currency}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>KDV (%18):</span>
                      <span>{calculateTax(order.totalValue)} {order.currency}</span>
                    </div>
                    <div className="flex justify-between font-bold text-lg border-t pt-2">
                      <span>Genel Toplam:</span>
                      <span>{calculateTotal(order.totalValue)} {order.currency}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Status */}
              <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-800">Ödeme Durumu: Tamamlandı</span>
                </div>
                <p className="text-sm text-green-700 mt-1">
                  Ödeme Tarihi: {formatDate(order.deliveredDate)} | 
                  Tutar: {order.paidAmount.toLocaleString()} {order.currency}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Download Options */}
          <Card>
            <CardHeader>
              <CardTitle>İndirme Seçenekleri</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  onClick={() => handleDownload('pdf')}
                  disabled={isLoading}
                  className="h-20 flex flex-col items-center justify-center bg-red-600 hover:bg-red-700"
                >
                  <FileText className="w-6 h-6 mb-2" />
                  <span>PDF İndir</span>
                </Button>
                
                <Button
                  onClick={() => handleDownload('excel')}
                  disabled={isLoading}
                  className="h-20 flex flex-col items-center justify-center bg-green-600 hover:bg-green-700"
                >
                  <Download className="w-6 h-6 mb-2" />
                  <span>Excel İndir</span>
                </Button>
                
                <Button
                  onClick={() => handleDownload('word')}
                  disabled={isLoading}
                  className="h-20 flex flex-col items-center justify-center bg-blue-600 hover:bg-blue-700"
                >
                  <FileText className="w-6 h-6 mb-2" />
                  <span>Word İndir</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Additional Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Ek İşlemler</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  onClick={handleSendEmail}
                  disabled={isLoading}
                  className="h-16 flex flex-col items-center justify-center"
                >
                  <Mail className="w-5 h-5 mb-1" />
                  <span>Müşteriye E-posta Gönder</span>
                </Button>
                
                <Button
                  variant="outline"
                  onClick={handlePrint}
                  disabled={isLoading}
                  className="h-16 flex flex-col items-center justify-center"
                >
                  <Printer className="w-5 h-5 mb-1" />
                  <span>Yazdır</span>
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => alert('Fatura detayları görüntüleniyor...')}
                  disabled={isLoading}
                  className="h-16 flex flex-col items-center justify-center"
                >
                  <Eye className="w-5 h-5 mb-1" />
                  <span>Tam Ekran Görünüm</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end">
            <Button variant="outline" onClick={onClose}>
              Kapat
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
