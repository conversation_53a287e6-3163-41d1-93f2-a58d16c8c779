'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useSettings } from '@/contexts/settings-context';
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  ShieldCheck,
  Save,
  RefreshCcw
} from 'lucide-react';
import toast from 'react-hot-toast';

interface AdminSettingsSubPageProps {
  type: 'contact' | 'whatsapp' | 'email' | 'security';
  title: string;
  description: string;
}

const AdminSettingsSubPage: React.FC<AdminSettingsSubPageProps> = ({
  type,
  title,
  description
}) => {
  const { settings, isLoading, updateContactSettings, updateWhatsAppSettings, updateEmailSettings, updateSecuritySettings, refreshSettings } = useSettings();
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState<any>({});

  React.useEffect(() => {
    // Initialize form data based on type
    switch (type) {
      case 'contact':
        setFormData(settings.contact);
        break;
      case 'whatsapp':
        setFormData(settings.whatsapp);
        break;
      case 'email':
        setFormData(settings.email);
        break;
      case 'security':
        setFormData(settings.security);
        break;
    }
  }, [settings, type]);

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      switch (type) {
        case 'contact':
          await updateContactSettings(formData);
          break;
        case 'whatsapp':
          await updateWhatsAppSettings(formData);
          break;
        case 'email':
          await updateEmailSettings(formData);
          break;
        case 'security':
          await updateSecuritySettings(formData);
          break;
      }
      toast.success('Ayarlar başarıyla kaydedildi!');
    } catch (error) {
      toast.error('Ayarlar kaydedilirken hata oluştu!');
    } finally {
      setIsSaving(false);
    }
  };

  const getTypeIcon = () => {
    switch (type) {
      case 'contact':
        return <Phone className="h-6 w-6 text-blue-600" />;
      case 'whatsapp':
        return <Phone className="h-6 w-6 text-green-600" />;
      case 'email':
        return <Mail className="h-6 w-6 text-purple-600" />;
      case 'security':
        return <ShieldCheck className="h-6 w-6 text-red-600" />;
      default:
        return <Phone className="h-6 w-6 text-gray-600" />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Ayarlar yükleniyor...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getTypeIcon()}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
            <p className="text-gray-600 mt-1">{description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={refreshSettings}
            className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            <RefreshCcw className="h-4 w-4" />
            <span>Yenile</span>
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <Save className="h-4 w-4" />
            <span>{isSaving ? 'Kaydediliyor...' : 'Kaydet'}</span>
          </button>
        </div>
      </div>

      {/* Settings Form */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        {type === 'contact' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Telefon Numarası
                </label>
                <input
                  type="text"
                  value={formData.phone || ''}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="+90 555 123 45 67"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Adresi
                </label>
                <input
                  type="email"
                  value={formData.email || ''}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Adres
              </label>
              <textarea
                value={formData.address || ''}
                onChange={(e) => handleInputChange('address', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="İstanbul, Türkiye"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Çalışma Saatleri
                </label>
                <input
                  type="text"
                  value={formData.workingHours || ''}
                  onChange={(e) => handleInputChange('workingHours', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Pazartesi - Cuma: 09:00 - 18:00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  WhatsApp Numarası
                </label>
                <input
                  type="text"
                  value={formData.whatsappNumber || ''}
                  onChange={(e) => handleInputChange('whatsappNumber', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="+90 555 123 45 67"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fax Numarası (Opsiyonel)
              </label>
              <input
                type="text"
                value={formData.faxNumber || ''}
                onChange={(e) => handleInputChange('faxNumber', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="+90 212 123 45 67"
              />
            </div>
          </div>
        )}

        {type === 'whatsapp' && (
          <div className="space-y-6">
            <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg">
              <input
                type="checkbox"
                checked={formData.isEnabled || false}
                onChange={(e) => handleInputChange('isEnabled', e.target.checked)}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="text-sm font-medium text-green-800">
                WhatsApp Widget'ını Etkinleştir
              </label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  WhatsApp Numarası
                </label>
                <input
                  type="text"
                  value={formData.phoneNumber || ''}
                  onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="+905551234567"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Ülke kodu ile birlikte, boşluk olmadan yazın
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Widget Konumu
                </label>
                <select
                  value={formData.position || 'bottom-right'}
                  onChange={(e) => handleInputChange('position', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="bottom-right">Sağ Alt</option>
                  <option value="bottom-left">Sol Alt</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Varsayılan Mesaj
              </label>
              <textarea
                value={formData.defaultMessage || ''}
                onChange={(e) => handleInputChange('defaultMessage', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Merhaba! Doğal taş ürünleri hakkında bilgi almak istiyorum."
              />
              <p className="text-xs text-gray-500 mt-1">
                Kullanıcılar WhatsApp'a tıkladığında bu mesaj otomatik olarak yazılacak
              </p>
            </div>
          </div>
        )}

        {type === 'email' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP Host
                </label>
                <input
                  type="text"
                  value={formData.smtpHost || ''}
                  onChange={(e) => handleInputChange('smtpHost', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="smtp.gmail.com"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP Port
                </label>
                <input
                  type="number"
                  value={formData.smtpPort || ''}
                  onChange={(e) => handleInputChange('smtpPort', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="587"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP Kullanıcı Adı
                </label>
                <input
                  type="text"
                  value={formData.smtpUser || ''}
                  onChange={(e) => handleInputChange('smtpUser', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP Şifre
                </label>
                <input
                  type="password"
                  value={formData.smtpPassword || ''}
                  onChange={(e) => handleInputChange('smtpPassword', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="••••••••"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gönderen Email
                </label>
                <input
                  type="email"
                  value={formData.fromEmail || ''}
                  onChange={(e) => handleInputChange('fromEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gönderen İsim
                </label>
                <input
                  type="text"
                  value={formData.fromName || ''}
                  onChange={(e) => handleInputChange('fromName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Doğal Taş Pazarı"
                />
              </div>
            </div>
          </div>
        )}

        {type === 'security' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maksimum Giriş Denemesi
                </label>
                <input
                  type="number"
                  value={formData.maxLoginAttempts || ''}
                  onChange={(e) => handleInputChange('maxLoginAttempts', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="5"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Oturum Zaman Aşımı (dakika)
                </label>
                <input
                  type="number"
                  value={formData.sessionTimeout || ''}
                  onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="30"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Şifre Uzunluğu
                </label>
                <input
                  type="number"
                  value={formData.passwordMinLength || ''}
                  onChange={(e) => handleInputChange('passwordMinLength', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="8"
                />
              </div>
              <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg">
                <input
                  type="checkbox"
                  checked={formData.requireTwoFactor || false}
                  onChange={(e) => handleInputChange('requireTwoFactor', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="text-sm font-medium text-blue-800">
                  İki Faktörlü Kimlik Doğrulama Zorunlu
                </label>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminSettingsSubPage;
