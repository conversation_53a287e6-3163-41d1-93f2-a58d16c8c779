import * as React from "react"
import { cn } from "@/lib/utils"

export interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  centered?: boolean
  padding?: boolean
}

/**
 * Container component following RFC-004 UI/UX Design System
 * Responsive container with consistent max-widths and padding
 */
const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ className, size = 'lg', centered = true, padding = true, ...props }, ref) => {
    
    const getSizeStyles = () => {
      switch (size) {
        case 'sm':
          return "max-w-screen-sm"  // 640px
        case 'md':
          return "max-w-screen-md"  // 768px
        case 'lg':
          return "max-w-screen-lg"  // 1024px
        case 'xl':
          return "max-w-screen-xl"  // 1280px
        case 'full':
          return "max-w-full"
        default:
          return "max-w-screen-lg"
      }
    }

    return (
      <div
        ref={ref}
        className={cn(
          "w-full",
          getSizeStyles(),
          centered && "mx-auto",
          padding && [
            "px-4",           // Mobile: 16px
            "sm:px-6",        // Small: 24px
            "lg:px-8"         // Large: 32px
          ],
          className
        )}
        {...props}
      />
    )
  }
)

Container.displayName = "Container"

export { Container }
