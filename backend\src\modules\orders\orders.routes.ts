import { Router } from 'express';
import { authMiddleware } from '@/middleware/authMiddleware';

const router = Router();

/**
 * @swagger
 * /api/orders:
 *   get:
 *     summary: Get user orders
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 */
router.get('/', authMiddleware, (req, res) => {
  res.json({
    success: true,
    message: 'Orders list endpoint - Coming soon',
    data: {
      orders: []
    }
  });
});

export default router;
