// Database Manager
// AI Marketing sistemi için veritabanı yönetimi

import { Pool, PoolClient, QueryResult } from 'pg';
import { EventEmitter } from 'events';
import fs from 'fs/promises';
import path from 'path';

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
  maxConnections?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
}

export interface QueryOptions {
  timeout?: number;
  retries?: number;
  transaction?: boolean;
}

export interface TransactionCallback<T> {
  (client: PoolClient): Promise<T>;
}

export class DatabaseManager extends EventEmitter {
  private pool: Pool;
  private config: DatabaseConfig;
  private isInitialized: boolean = false;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor(config: DatabaseConfig) {
    super();
    this.config = config;
    
    this.pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl ? { rejectUnauthorized: false } : false,
      max: config.maxConnections || 20,
      idleTimeoutMillis: config.idleTimeoutMillis || 30000,
      connectionTimeoutMillis: config.connectionTimeoutMillis || 10000
    });

    this.setupEventListeners();
  }

  /**
   * Veritabanı bağlantısını başlat
   */
  public async initialize(): Promise<void> {
    console.log('🗄️ Initializing database connection...');
    
    try {
      // Bağlantı testi
      await this.testConnection();
      
      // Schema kontrolü ve oluşturma
      await this.ensureSchema();
      
      // Health check başlat
      this.startHealthCheck();
      
      this.isInitialized = true;
      console.log('✅ Database initialized successfully');
      this.emit('initialized');
      
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      this.emit('initializationError', error);
      throw error;
    }
  }

  /**
   * Query çalıştır
   */
  public async query<T = any>(
    text: string,
    params?: any[],
    options: QueryOptions = {}
  ): Promise<QueryResult<any>> {
    this.ensureInitialized();
    
    const startTime = Date.now();
    let retries = options.retries || 0;
    
    while (retries >= 0) {
      try {
        const client = await this.pool.connect();
        
        try {
          if (options.timeout) {
            // Query timeout ayarla
            await client.query(`SET statement_timeout = ${options.timeout}`);
          }
          
          const result = await client.query(text, params);
          
          this.emit('queryExecuted', {
            query: text,
            params,
            duration: Date.now() - startTime,
            rowCount: result.rowCount
          });
          
          return result;
          
        } finally {
          client.release();
        }
        
      } catch (error) {
        if (retries > 0) {
          retries--;
          console.warn(`Query failed, retrying... (${retries} attempts left)`);
          await this.delay(1000); // 1 saniye bekle
          continue;
        }
        
        this.emit('queryError', {
          query: text,
          params,
          error,
          duration: Date.now() - startTime
        });
        
        throw error;
      }
    }
    
    throw new Error('Query failed after all retries');
  }

  /**
   * Transaction içinde işlem yap
   */
  public async transaction<T>(
    callback: TransactionCallback<T>,
    options: QueryOptions = {}
  ): Promise<T> {
    this.ensureInitialized();
    
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      
      if (options.timeout) {
        await client.query(`SET statement_timeout = ${options.timeout}`);
      }
      
      const result = await callback(client);
      
      await client.query('COMMIT');
      
      this.emit('transactionCompleted', {
        success: true
      });
      
      return result;
      
    } catch (error) {
      await client.query('ROLLBACK');
      
      this.emit('transactionError', {
        error,
        rolledBack: true
      });
      
      throw error;
      
    } finally {
      client.release();
    }
  }

  /**
   * Learning pattern kaydet
   */
  public async saveLearningPattern(pattern: {
    patternData: any;
    confidence: number;
    successRate: number;
    context: string;
  }): Promise<string> {
    const result = await this.query(
      `INSERT INTO learning_patterns (pattern_data, confidence, success_rate, context)
       VALUES ($1, $2, $3, $4)
       RETURNING id`,
      [JSON.stringify(pattern.patternData), pattern.confidence, pattern.successRate, pattern.context]
    );
    
    return result.rows[0].id;
  }

  /**
   * Learning pattern'leri getir
   */
  public async getLearningPatterns(context?: string, limit: number = 100): Promise<any[]> {
    let query = `
      SELECT id, pattern_data, confidence, success_rate, context, usage_count, created_at, last_used
      FROM learning_patterns
    `;
    const params: any[] = [];
    
    if (context) {
      query += ' WHERE context = $1';
      params.push(context);
    }
    
    query += ' ORDER BY confidence DESC, success_rate DESC LIMIT $' + (params.length + 1);
    params.push(limit);
    
    const result = await this.query(query, params);
    return result.rows;
  }

  /**
   * Market insight kaydet
   */
  public async saveMarketInsight(insight: {
    insightText: string;
    source: string;
    confidence: number;
    actionable: boolean;
    category: string;
    impactLevel: 'high' | 'medium' | 'low';
    relatedData?: any;
    expiresAt?: Date;
  }): Promise<string> {
    const result = await this.query(
      `INSERT INTO market_insights (insight_text, source, confidence, actionable, category, impact_level, related_data, expires_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       RETURNING id`,
      [
        insight.insightText,
        insight.source,
        insight.confidence,
        insight.actionable,
        insight.category,
        insight.impactLevel,
        insight.relatedData ? JSON.stringify(insight.relatedData) : null,
        insight.expiresAt
      ]
    );
    
    return result.rows[0].id;
  }

  /**
   * Market insight'ları getir
   */
  public async getMarketInsights(filters: {
    category?: string;
    actionable?: boolean;
    minConfidence?: number;
    limit?: number;
  } = {}): Promise<any[]> {
    let query = `
      SELECT id, insight_text, source, confidence, actionable, category, impact_level, related_data, created_at
      FROM market_insights
      WHERE (expires_at IS NULL OR expires_at > NOW())
    `;
    const params: any[] = [];
    let paramIndex = 1;
    
    if (filters.category) {
      query += ` AND category = $${paramIndex}`;
      params.push(filters.category);
      paramIndex++;
    }
    
    if (filters.actionable !== undefined) {
      query += ` AND actionable = $${paramIndex}`;
      params.push(filters.actionable);
      paramIndex++;
    }
    
    if (filters.minConfidence !== undefined) {
      query += ` AND confidence >= $${paramIndex}`;
      params.push(filters.minConfidence);
      paramIndex++;
    }
    
    query += ` ORDER BY confidence DESC, created_at DESC LIMIT $${paramIndex}`;
    params.push(filters.limit || 50);
    
    const result = await this.query(query, params);
    return result.rows;
  }

  /**
   * Knowledge entry kaydet
   */
  public async saveKnowledgeEntry(entry: {
    category: string;
    topic: string;
    content: string;
    source: string;
    confidence: number;
    relevance: number;
    tags: string[];
    relatedEntries?: string[];
  }): Promise<string> {
    const result = await this.query(
      `INSERT INTO knowledge_entries (category, topic, content, source, confidence, relevance, tags, related_entries)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       RETURNING id`,
      [
        entry.category,
        entry.topic,
        entry.content,
        entry.source,
        entry.confidence,
        entry.relevance,
        entry.tags,
        entry.relatedEntries || []
      ]
    );
    
    return result.rows[0].id;
  }

  /**
   * Knowledge entry'leri ara
   */
  public async searchKnowledgeEntries(searchTerm: string, category?: string, limit: number = 20): Promise<any[]> {
    let query = `
      SELECT id, category, topic, content, source, confidence, relevance, tags, usage_count, created_at
      FROM knowledge_entries
      WHERE (
        topic ILIKE $1 OR 
        content ILIKE $1 OR 
        $2 = ANY(tags)
      )
    `;
    const params: any[] = [`%${searchTerm}%`, searchTerm];
    
    if (category) {
      query += ' AND category = $3';
      params.push(category);
    }
    
    query += ' ORDER BY relevance DESC, confidence DESC LIMIT $' + (params.length + 1);
    params.push(limit);
    
    const result = await this.query(query, params);
    return result.rows;
  }

  /**
   * Performance metric kaydet
   */
  public async savePerformanceMetric(metric: {
    metricName: string;
    metricValue: number;
    targetValue?: number;
    thresholdValue?: number;
    trend?: 'up' | 'down' | 'stable';
    moduleName: string;
    campaignId?: string;
  }): Promise<string> {
    const result = await this.query(
      `INSERT INTO performance_metrics (metric_name, metric_value, target_value, threshold_value, trend, module_name, campaign_id)
       VALUES ($1, $2, $3, $4, $5, $6, $7)
       RETURNING id`,
      [
        metric.metricName,
        metric.metricValue,
        metric.targetValue,
        metric.thresholdValue,
        metric.trend,
        metric.moduleName,
        metric.campaignId
      ]
    );
    
    return result.rows[0].id;
  }

  /**
   * Performance metric'leri getir
   */
  public async getPerformanceMetrics(filters: {
    moduleName?: string;
    campaignId?: string;
    metricName?: string;
    fromDate?: Date;
    limit?: number;
  } = {}): Promise<any[]> {
    let query = `
      SELECT id, metric_name, metric_value, target_value, threshold_value, trend, module_name, campaign_id, recorded_at
      FROM performance_metrics
      WHERE 1=1
    `;
    const params: any[] = [];
    let paramIndex = 1;
    
    if (filters.moduleName) {
      query += ` AND module_name = $${paramIndex}`;
      params.push(filters.moduleName);
      paramIndex++;
    }
    
    if (filters.campaignId) {
      query += ` AND campaign_id = $${paramIndex}`;
      params.push(filters.campaignId);
      paramIndex++;
    }
    
    if (filters.metricName) {
      query += ` AND metric_name = $${paramIndex}`;
      params.push(filters.metricName);
      paramIndex++;
    }
    
    if (filters.fromDate) {
      query += ` AND recorded_at >= $${paramIndex}`;
      params.push(filters.fromDate);
      paramIndex++;
    }
    
    query += ` ORDER BY recorded_at DESC LIMIT $${paramIndex}`;
    params.push(filters.limit || 100);
    
    const result = await this.query(query, params);
    return result.rows;
  }

  /**
   * Cache'e veri kaydet
   */
  public async setCache(key: string, data: any, source: string, ttlSeconds: number = 3600): Promise<void> {
    const expiresAt = new Date(Date.now() + ttlSeconds * 1000);
    
    await this.query(
      `INSERT INTO external_data_cache (cache_key, data, source, expires_at)
       VALUES ($1, $2, $3, $4)
       ON CONFLICT (cache_key) 
       DO UPDATE SET data = $2, expires_at = $4, created_at = NOW()`,
      [key, JSON.stringify(data), source, expiresAt]
    );
  }

  /**
   * Cache'den veri getir
   */
  public async getCache(key: string): Promise<any | null> {
    const result = await this.query(
      `SELECT data FROM external_data_cache 
       WHERE cache_key = $1 AND expires_at > NOW()`,
      [key]
    );
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return result.rows[0].data;
  }

  /**
   * System event kaydet
   */
  public async logSystemEvent(event: {
    eventType: string;
    eventData?: any;
    severity?: 'critical' | 'warning' | 'info' | 'debug';
    moduleName: string;
    message?: string;
  }): Promise<void> {
    await this.query(
      `INSERT INTO system_events (event_type, event_data, severity, module_name, message)
       VALUES ($1, $2, $3, $4, $5)`,
      [
        event.eventType,
        event.eventData ? JSON.stringify(event.eventData) : null,
        event.severity || 'info',
        event.moduleName,
        event.message
      ]
    );
  }

  /**
   * Veritabanı istatistikleri
   */
  public async getStatistics(): Promise<any> {
    const stats = await this.transaction(async (client) => {
      const learningPatterns = await client.query('SELECT COUNT(*) as count FROM learning_patterns');
      const marketInsights = await client.query('SELECT COUNT(*) as count FROM market_insights WHERE expires_at IS NULL OR expires_at > NOW()');
      const knowledgeEntries = await client.query('SELECT COUNT(*) as count FROM knowledge_entries');
      const performanceMetrics = await client.query('SELECT COUNT(*) as count FROM performance_metrics WHERE recorded_at > NOW() - INTERVAL \'24 hours\'');
      const systemEvents = await client.query('SELECT COUNT(*) as count FROM system_events WHERE created_at > NOW() - INTERVAL \'24 hours\'');
      
      return {
        learningPatterns: parseInt(learningPatterns.rows[0].count),
        marketInsights: parseInt(marketInsights.rows[0].count),
        knowledgeEntries: parseInt(knowledgeEntries.rows[0].count),
        performanceMetrics24h: parseInt(performanceMetrics.rows[0].count),
        systemEvents24h: parseInt(systemEvents.rows[0].count),
        lastUpdated: new Date()
      };
    });
    
    return stats;
  }

  // Private methods
  private async testConnection(): Promise<void> {
    const client = await this.pool.connect();
    try {
      await client.query('SELECT NOW()');
      console.log('✅ Database connection test successful');
    } finally {
      client.release();
    }
  }

  private async ensureSchema(): Promise<void> {
    console.log('🔧 Checking database schema...');
    
    try {
      // Schema dosyasını oku
      const schemaPath = path.join(__dirname, 'schema.sql');
      const schemaSQL = await fs.readFile(schemaPath, 'utf-8');
      
      // Schema'yı çalıştır
      await this.query(schemaSQL);
      
      console.log('✅ Database schema ensured');
      
    } catch (error) {
      console.error('❌ Schema creation failed:', error);
      throw error;
    }
  }

  private setupEventListeners(): void {
    this.pool.on('connect', () => {
      this.emit('connectionCreated');
    });

    this.pool.on('error', (error) => {
      console.error('Database pool error:', error);
      this.emit('poolError', error);
    });
  }

  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.testConnection();
        this.emit('healthCheckPassed');
      } catch (error) {
        console.error('Database health check failed:', error);
        this.emit('healthCheckFailed', error);
      }
    }, 60000); // Her dakika
  }

  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Temizlik işlemleri
   */
  public async cleanup(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    await this.pool.end();
    console.log('🗄️ Database connections closed');
    this.emit('cleanup');
  }
}
