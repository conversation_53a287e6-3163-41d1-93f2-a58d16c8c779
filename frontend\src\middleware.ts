import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Admin sayfaları için koruma
  if (pathname.startsWith('/admin')) {
    // Login ve forgot-password sayfalarına erişime izin ver
    if (pathname === '/admin/login' || pathname === '/admin/forgot-password') {
      return NextResponse.next()
    }

    // Admin token kontrolü - JWT token olup olmadığını kontrol et
    const adminToken = request.cookies.get('admin_token')

    if (!adminToken?.value) {
      // Admin token yoksa login sayfasına yönlendir
      return NextResponse.redirect(new URL('/admin/login', request.url))
    }

    // JWT token format kontrolü (basit kontrol)
    try {
      const tokenParts = adminToken.value.split('.')
      if (tokenParts.length !== 3) {
        return NextResponse.redirect(new URL('/admin/login', request.url))
      }
    } catch (error) {
      return NextResponse.redirect(new URL('/admin/login', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Admin sayfaları için matcher
     * - /admin (ana sayfa)
     * - /admin/... (tüm alt sayfalar)
     * Ancak /admin/login ve /admin/forgot-password hariç
     */
    '/admin/((?!login|forgot-password).*)',
    '/admin'
  ]
}
