'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Ruler, 
  Calculator, 
  Grid3X3, 
  Settings, 
  Info,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { 
  DimensionConfiguration, 
  StandardSize, 
  CustomSizeConfig, 
  AdvancedProductConfig,
  PriceBreakdown 
} from '../../types/3d';

interface DimensionConfiguratorProps {
  productId: string;
  configuration: DimensionConfiguration;
  currentConfig: AdvancedProductConfig;
  onConfigChange: (config: Partial<AdvancedProductConfig>) => void;
  className?: string;
}

export const DimensionConfigurator: React.FC<DimensionConfiguratorProps> = ({
  productId,
  configuration,
  currentConfig,
  onConfigChange,
  className = ''
}) => {
  const [selectedTab, setSelectedTab] = useState<'standard' | 'custom'>('standard');
  const [customDimensions, setCustomDimensions] = useState({
    width: currentConfig.dimensions.width,
    height: currentConfig.dimensions.height,
    thickness: currentConfig.dimensions.thickness
  });
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Filter standard sizes by category
  const getStandardSizesByCategory = (category: string) => {
    return configuration.standardSizes.filter(size => size.category === category);
  };

  // Validate custom dimensions
  const validateCustomDimensions = useCallback((dimensions: typeof customDimensions) => {
    const errors: string[] = [];
    const { validation } = configuration.customSize;

    // Size limits
    if (dimensions.width < configuration.customSize.minWidth) {
      errors.push(`En minimum ${configuration.customSize.minWidth} cm olmalıdır`);
    }
    if (dimensions.width > configuration.customSize.maxWidth) {
      errors.push(`En maksimum ${configuration.customSize.maxWidth} cm olmalıdır`);
    }
    if (dimensions.height < configuration.customSize.minHeight) {
      errors.push(`Boy minimum ${configuration.customSize.minHeight} cm olmalıdır`);
    }
    if (dimensions.height > configuration.customSize.maxHeight) {
      errors.push(`Boy maksimum ${configuration.customSize.maxHeight} cm olmalıdır`);
    }

    // Thickness validation
    if (!configuration.customSize.thicknessOptions.includes(dimensions.thickness)) {
      errors.push(`Kalınlık ${configuration.customSize.thicknessOptions.join(', ')} cm değerlerinden biri olmalıdır`);
    }

    // Aspect ratio validation
    if (validation.aspectRatio) {
      const aspectRatio = dimensions.width / dimensions.height;
      if (aspectRatio < validation.aspectRatio.min || aspectRatio > validation.aspectRatio.max) {
        errors.push(`En/Boy oranı ${validation.aspectRatio.min} - ${validation.aspectRatio.max} arasında olmalıdır`);
      }
    }

    return errors;
  }, [configuration]);

  // Removed price calculation functionality

  // Check if dimensions match a standard size
  const isStandardSize = (dimensions: typeof customDimensions) => {
    return configuration.standardSizes.some(size => 
      size.width === dimensions.width && 
      size.height === dimensions.height && 
      size.thickness === dimensions.thickness
    );
  };

  // Removed surface finish multiplier

  // Handle standard size selection
  const handleStandardSizeSelect = (size: StandardSize) => {
    const newDimensions = {
      width: size.width,
      height: size.height,
      thickness: size.thickness
    };

    onConfigChange({
      dimensions: newDimensions
    });

    setCustomDimensions(newDimensions);
    setValidationErrors([]);
  };

  // Handle custom dimension change
  const handleCustomDimensionChange = (field: keyof typeof customDimensions, value: number) => {
    const newDimensions = {
      ...customDimensions,
      [field]: value
    };

    setCustomDimensions(newDimensions);

    const errors = validateCustomDimensions(newDimensions);
    setValidationErrors(errors);

    if (errors.length === 0) {
      onConfigChange({
        dimensions: newDimensions
      });
    }
  };

  // Effect to validate on mount
  useEffect(() => {
    const errors = validateCustomDimensions(customDimensions);
    setValidationErrors(errors);
  }, [customDimensions, validateCustomDimensions]);

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Ruler className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Ebat Seçimi</h3>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => setSelectedTab('standard')}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            selectedTab === 'standard'
              ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <Grid3X3 className="w-4 h-4 inline mr-2" />
          Standart Ebatlar
        </button>
        <button
          onClick={() => setSelectedTab('custom')}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            selectedTab === 'custom'
              ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          disabled={!configuration.customSize.enabled}
        >
          <Settings className="w-4 h-4 inline mr-2" />
          Özel Ebat
        </button>
      </div>

      {/* Content */}
      <div className="p-4">
        {selectedTab === 'standard' && (
          <StandardSizeSelector
            sizes={configuration.standardSizes}
            currentDimensions={currentConfig.dimensions}
            onSizeSelect={handleStandardSizeSelect}
          />
        )}

        {selectedTab === 'custom' && configuration.customSize.enabled && (
          <CustomSizeEditor
            config={configuration.customSize}
            dimensions={customDimensions}
            validationErrors={validationErrors}
            isCalculating={isCalculating}
            onChange={handleCustomDimensionChange}
          />
        )}
      </div>

      {/* Validation Messages */}
      {validationErrors.length > 0 && (
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-red-800">Doğrulama Hataları:</p>
              <ul className="mt-1 text-sm text-red-700 list-disc list-inside">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Current Selection Info */}
      <div className="p-4 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span className="text-sm text-gray-700">
              Seçili: {currentConfig.dimensions.width} × {currentConfig.dimensions.height} × {currentConfig.dimensions.thickness} cm
            </span>
          </div>

        </div>
      </div>
    </div>
  );
};

// Standard Size Selector Component
interface StandardSizeSelectorProps {
  sizes: StandardSize[];
  currentDimensions: { width: number; height: number; thickness: number; };
  onSizeSelect: (size: StandardSize) => void;
}

const StandardSizeSelector: React.FC<StandardSizeSelectorProps> = ({
  sizes,
  currentDimensions,
  onSizeSelect
}) => {
  const categories = ['floor', 'wall', 'countertop'] as const;
  
  return (
    <div className="space-y-6">
      {categories.map(category => {
        const categorySizes = sizes.filter(size => size.category === category);
        if (categorySizes.length === 0) return null;
        
        return (
          <div key={category}>
            <h4 className="text-sm font-medium text-gray-900 mb-3 capitalize">
              {category === 'floor' ? 'Döşeme' : category === 'wall' ? 'Duvar' : 'Tezgah'}
            </h4>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
              {categorySizes.map((size, index) => {
                const isSelected = 
                  size.width === currentDimensions.width &&
                  size.height === currentDimensions.height &&
                  size.thickness === currentDimensions.thickness;
                
                return (
                  <button
                    key={index}
                    onClick={() => onSizeSelect(size)}
                    className={`p-3 rounded-lg border-2 transition-all text-left ${
                      isSelected
                        ? 'border-blue-500 bg-blue-50 text-blue-900'
                        : 'border-gray-200 hover:border-gray-300 text-gray-700'
                    }`}
                  >
                    <div className="font-medium">{size.label}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {size.applications.join(', ')}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
};

// Custom Size Editor Component
interface CustomSizeEditorProps {
  config: CustomSizeConfig;
  dimensions: { width: number; height: number; thickness: number; };
  validationErrors: string[];
  isCalculating: boolean;
  onChange: (field: keyof typeof dimensions, value: number) => void;
}

const CustomSizeEditor: React.FC<CustomSizeEditorProps> = ({
  config,
  dimensions,
  validationErrors,
  isCalculating,
  onChange
}) => {
  return (
    <div className="space-y-4">
      {/* Width */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          En (cm)
        </label>
        <input
          type="number"
          min={config.minWidth}
          max={config.maxWidth}
          value={dimensions.width}
          onChange={(e) => onChange('width', Number(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder={`${config.minWidth} - ${config.maxWidth}`}
        />
        <p className="text-xs text-gray-500 mt-1">
          {config.minWidth} - {config.maxWidth} cm arası
        </p>
      </div>

      {/* Height */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Boy (cm)
        </label>
        <input
          type="number"
          min={config.minHeight}
          max={config.maxHeight}
          value={dimensions.height}
          onChange={(e) => onChange('height', Number(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder={`${config.minHeight} - ${config.maxHeight}`}
        />
        <p className="text-xs text-gray-500 mt-1">
          {config.minHeight} - {config.maxHeight} cm arası
        </p>
      </div>

      {/* Thickness */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Kalınlık (cm)
        </label>
        <select
          value={dimensions.thickness}
          onChange={(e) => onChange('thickness', Number(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {config.thicknessOptions.map(thickness => (
            <option key={thickness} value={thickness}>
              {thickness} cm
            </option>
          ))}
        </select>
      </div>

      {/* Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div className="flex items-start space-x-2">
          <Info className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-blue-700">
            <p className="font-medium">Özel Ebat Bilgileri:</p>
            <ul className="mt-1 space-y-1 text-xs">
              <li>• Özel ebatlarda ek işlem süresi gerekir</li>
              <li>• Kesim işlemi uygulanır</li>
              <li>• Üretim süresi 3-5 iş günü uzar</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
