'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Package,
  Eye,
  Calendar,
  DollarSign,
  Building,
  Truck,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import toast from 'react-hot-toast';

interface OngoingOrder {
  id: string;
  orderNumber: string;
  productName: string;
  totalQuantity: number;
  unit: string;
  totalAmount: number;
  currency: string;
  status: string;
  createdAt: string;
  producer: {
    name: string;
    company: string;
  };
  deliveryProgress: {
    completed: number;
    total: number;
    percentage: number;
  };
  paymentProgress: {
    paid: number;
    total: number;
    percentage: number;
  };
  nextDeliveryDate?: string;
  nextPaymentDue?: string;
}

export default function CustomerOngoingOrdersPage() {
  const router = useRouter();
  const [orders, setOrders] = useState<OngoingOrder[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchOngoingOrders();
  }, []);

  const fetchOngoingOrders = async () => {
    try {
      setLoading(true);
      // Gerçek veriler API'den gelecek - şimdilik boş array
      const mockOrders: OngoingOrder[] = [];
      setOrders(mockOrders);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Siparişler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleNavigate = (route: string) => {
    router.push(route);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      confirmed: { label: 'Onaylandı', color: 'bg-blue-100 text-blue-800', icon: CheckCircle },
      in_production: { label: 'Üretimde', color: 'bg-purple-100 text-purple-800', icon: Package },
      partially_delivered: { label: 'Kısmi Teslim', color: 'bg-orange-100 text-orange-800', icon: Truck },
      pending_payment: { label: 'Ödeme Bekliyor', color: 'bg-yellow-100 text-yellow-800', icon: Clock }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.confirmed;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Devam eden siparişler yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Devam Eden Siparişler</h1>
          <p className="text-gray-600 mt-1">Üretim ve teslimat aşamasındaki siparişleriniz</p>
        </div>
      </div>

      {/* Orders List */}
      <div className="grid gap-6">
        {orders.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Devam eden sipariş bulunamadı</h3>
              <p className="text-gray-600">Şu anda üretim veya teslimat aşamasında olan siparişiniz bulunmuyor.</p>
            </CardContent>
          </Card>
        ) : (
          orders.map((order) => (
            <Card key={order.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{order.orderNumber}</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">{order.productName}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-gray-900 mb-1">
                      {formatCurrency(order.totalAmount, order.currency)}
                    </div>
                    {getStatusBadge(order.status)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <p className="text-sm font-medium text-gray-900 mb-1">Üretici</p>
                    <p className="text-sm text-gray-600 flex items-center gap-1">
                      <Building className="w-4 h-4" />
                      {order.producer.company}
                    </p>
                    <p className="text-sm text-gray-600">{order.producer.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 mb-1">Miktar</p>
                    <p className="text-sm text-gray-600">
                      {order.totalQuantity} {order.unit}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 mb-1">Sipariş Tarihi</p>
                    <p className="text-sm text-gray-600 flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {formatDate(order.createdAt)}
                    </p>
                  </div>
                </div>

                {/* Progress Bars */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">Teslimat İlerlemesi</span>
                      <span className="text-sm text-gray-600">
                        {order.deliveryProgress.completed}/{order.deliveryProgress.total}
                      </span>
                    </div>
                    <Progress value={order.deliveryProgress.percentage} className="h-2" />
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">Ödeme İlerlemesi</span>
                      <span className="text-sm text-gray-600">
                        {formatCurrency(order.paymentProgress.paid, order.currency)} / {formatCurrency(order.paymentProgress.total, order.currency)}
                      </span>
                    </div>
                    <Progress value={order.paymentProgress.percentage} className="h-2" />
                  </div>
                </div>

                {/* Next Actions */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {order.nextDeliveryDate && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <p className="text-sm font-medium text-blue-900 flex items-center gap-1">
                        <Truck className="w-4 h-4" />
                        Sonraki Teslimat: {formatDate(order.nextDeliveryDate)}
                      </p>
                    </div>
                  )}

                  {order.nextPaymentDue && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <p className="text-sm font-medium text-yellow-900 flex items-center gap-1">
                        <DollarSign className="w-4 h-4" />
                        Ödeme Vadesi: {formatDate(order.nextPaymentDue)}
                      </p>
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleNavigate(`/customer/orders/${order.id}`)}
                    className="flex-1"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Sipariş Detayları
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
