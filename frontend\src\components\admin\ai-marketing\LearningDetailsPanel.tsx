'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  TrendingUp, 
  Target, 
  Clock, 
  BarChart3,
  CheckCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react';

interface LearningPattern {
  id: string;
  pattern_data: any;
  confidence: number;
  success_rate: number;
  context: string;
  usage_count: number;
  created_at: string;
  last_used: string;
}

interface PerformanceMetric {
  id: string;
  metric_name: string;
  metric_value: number;
  target_value?: number;
  trend: 'up' | 'down' | 'stable';
  module_name: string;
  recorded_at: string;
}

export default function LearningDetailsPanel() {
  const [patterns, setPatterns] = useState<LearningPattern[]>([]);
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedContext, setSelectedContext] = useState<string>('all');

  useEffect(() => {
    fetchLearningData();
  }, [selectedContext]);

  const fetchLearningData = async () => {
    setLoading(true);
    try {
      // Fetch learning patterns
      const patternsResponse = await fetch(
        `/api/admin/ai-marketing/learning/patterns?${selectedContext !== 'all' ? `context=${selectedContext}` : ''}&limit=20`
      );
      const patternsData = await patternsResponse.json();
      setPatterns(patternsData);

      // Fetch performance metrics
      const metricsResponse = await fetch(
        '/api/admin/ai-marketing/performance/metrics?module=learning&days=7'
      );
      const metricsData = await metricsResponse.json();
      setMetrics(metricsData);

    } catch (error) {
      console.error('Failed to fetch learning data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getContexts = () => {
    const contexts = [...new Set(patterns.map(p => p.context))];
    return ['all', ...contexts];
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 80) return 'bg-green-500';
    if (rate >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down': return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />;
      default: return <Target className="h-4 w-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        <span>Loading learning data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center">
            <Brain className="h-6 w-6 mr-2" />
            Adaptive Learning Engine
          </h2>
          <p className="text-muted-foreground">
            Detailed analysis of learning patterns and performance
          </p>
        </div>
        <Button onClick={fetchLearningData} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Patterns</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{patterns.length}</div>
            <p className="text-xs text-muted-foreground">
              Active learning patterns
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Confidence</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {patterns.length > 0 
                ? Math.round((patterns.reduce((sum, p) => sum + p.confidence, 0) / patterns.length) * 100)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Pattern confidence
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {patterns.length > 0 
                ? Math.round((patterns.reduce((sum, p) => sum + p.success_rate, 0) / patterns.length) * 100)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Average success rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {patterns.reduce((sum, p) => sum + p.usage_count, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Pattern applications
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="patterns" className="space-y-4">
        <TabsList>
          <TabsTrigger value="patterns">Learning Patterns</TabsTrigger>
          <TabsTrigger value="performance">Performance Metrics</TabsTrigger>
          <TabsTrigger value="insights">Learning Insights</TabsTrigger>
        </TabsList>

        {/* Learning Patterns Tab */}
        <TabsContent value="patterns" className="space-y-4">
          {/* Context Filter */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Context:</span>
            <div className="flex flex-wrap gap-2">
              {getContexts().map((context) => (
                <Button
                  key={context}
                  variant={selectedContext === context ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedContext(context)}
                >
                  {context}
                </Button>
              ))}
            </div>
          </div>

          {/* Patterns List */}
          <div className="grid gap-4">
            {patterns.map((pattern) => (
              <Card key={pattern.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">
                      Pattern #{pattern.id.slice(-8)}
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">{pattern.context}</Badge>
                      <Badge 
                        variant={pattern.confidence >= 0.8 ? "default" : "secondary"}
                      >
                        {Math.round(pattern.confidence * 100)}% confidence
                      </Badge>
                    </div>
                  </div>
                  <CardDescription>
                    Created: {new Date(pattern.created_at).toLocaleDateString()} | 
                    Last used: {new Date(pattern.last_used).toLocaleDateString()} | 
                    Used {pattern.usage_count} times
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Success Rate Progress */}
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Success Rate</span>
                        <span>{Math.round(pattern.success_rate * 100)}%</span>
                      </div>
                      <Progress 
                        value={pattern.success_rate * 100} 
                        className="h-2"
                      />
                    </div>

                    {/* Pattern Data Preview */}
                    <div className="bg-muted p-3 rounded-md">
                      <div className="text-sm font-medium mb-2">Pattern Data:</div>
                      <pre className="text-xs overflow-x-auto">
                        {JSON.stringify(pattern.pattern_data, null, 2).slice(0, 200)}
                        {JSON.stringify(pattern.pattern_data, null, 2).length > 200 && '...'}
                      </pre>
                    </div>

                    {/* Actions */}
                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                      <Button variant="outline" size="sm">
                        Apply Pattern
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {patterns.length === 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  No learning patterns found for the selected context.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Performance Metrics Tab */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4">
            {metrics.map((metric) => (
              <Card key={metric.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{metric.metric_name}</CardTitle>
                    <div className="flex items-center space-x-2">
                      {getTrendIcon(metric.trend)}
                      <Badge variant="outline">{metric.trend}</Badge>
                    </div>
                  </div>
                  <CardDescription>
                    Module: {metric.module_name} | 
                    Recorded: {new Date(metric.recorded_at).toLocaleString()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold">{metric.metric_value}</div>
                      {metric.target_value && (
                        <div className="text-sm text-muted-foreground">
                          Target: {metric.target_value}
                        </div>
                      )}
                    </div>
                    {metric.target_value && (
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {metric.metric_value >= metric.target_value ? 'Above' : 'Below'} Target
                        </div>
                        <div className={`text-sm ${
                          metric.metric_value >= metric.target_value 
                            ? 'text-green-600' 
                            : 'text-red-600'
                        }`}>
                          {Math.abs(((metric.metric_value - metric.target_value) / metric.target_value) * 100).toFixed(1)}%
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {metrics.length === 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  No performance metrics available for the selected period.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Learning Insights Tab */}
        <TabsContent value="insights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Learning Insights & Recommendations</CardTitle>
              <CardDescription>
                AI-generated insights from learning patterns analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-start space-x-3">
                    <Brain className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-blue-900">Pattern Optimization</div>
                      <div className="text-sm text-blue-700 mt-1">
                        High-confidence patterns in 'email-marketing' context show 23% better performance. 
                        Consider applying these patterns to other marketing channels.
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-green-900">Success Pattern Identified</div>
                      <div className="text-sm text-green-700 mt-1">
                        Patterns with usage count > 10 show 89% success rate. 
                        These proven patterns should be prioritized for new campaigns.
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-yellow-900">Learning Opportunity</div>
                      <div className="text-sm text-yellow-700 mt-1">
                        Low-usage patterns need more data for reliable confidence scores. 
                        Consider running A/B tests to validate these patterns.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
