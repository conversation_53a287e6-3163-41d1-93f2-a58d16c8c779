'use client'

import React from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Building2,
  MapPin, 
  Eye,
  TrendingUp,
  DollarSign,
  Calendar,
  Search,
  Filter,
  Phone,
  Mail,
  ShoppingCart,
  Target,
  Star,
  Clock
} from 'lucide-react'

export default function AdminCustomersPage() {
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedSector, setSelectedSector] = React.useState('all')

  // Customer data will be loaded from API
  const mockCustomers: any[] = []

  const sectors = ['all', 'İnşaat', 'Dekorasyon', 'Konut', 'Otelcilik']

  const filteredCustomers = mockCustomers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.contact.person.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesSector = selectedSector === 'all' || customer.sector === selectedSector
    
    return matchesSearch && matchesSector
  })

  const totalCustomers = mockCustomers.length
  const totalRevenue = mockCustomers.reduce((sum, customer) => sum + customer.stats.totalValue, 0)
  const avgOrderValue = mockCustomers.reduce((sum, customer) => sum + customer.stats.avgOrderValue, 0) / mockCustomers.length
  const avgConversionRate = mockCustomers.reduce((sum, customer) => sum + customer.stats.conversionRate, 0) / mockCustomers.length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Müşteri Yönetimi</h1>
          <p className="text-gray-600 mt-1">
            Müşteri profillerini yönetin ve performanslarını analiz edin
          </p>
        </div>
        <Button>
          <Users className="w-4 h-4 mr-2" />
          Yeni Müşteri Ekle
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Müşteri</p>
              <p className="text-xl font-bold text-gray-900">{totalCustomers}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Gelir</p>
              <p className="text-xl font-bold text-gray-900">₺{(totalRevenue / 1000000).toFixed(1)}M</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 rounded-lg">
              <ShoppingCart className="w-5 h-5 text-amber-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Ort. Sipariş Değeri</p>
              <p className="text-xl font-bold text-gray-900">₺{(avgOrderValue / 1000).toFixed(0)}K</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Target className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Ort. Dönüşüm</p>
              <p className="text-xl font-bold text-gray-900">{avgConversionRate.toFixed(1)}%</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Müşteri adı veya kişi adı ile ara..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
          />
        </div>
        
        <select
          value={selectedSector}
          onChange={(e) => setSelectedSector(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
        >
          {sectors.map(sector => (
            <option key={sector} value={sector}>
              {sector === 'all' ? 'Tüm Sektörler' : sector}
            </option>
          ))}
        </select>
      </div>

      {/* Customers Grid */}
      <div className="grid grid-cols-1 gap-6">
        {filteredCustomers.map((customer) => (
          <Card key={customer.id} className="overflow-hidden">
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {/* Customer Info */}
                <div className="lg:col-span-1">
                  <div className="flex items-start gap-3">
                    <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                      <Building2 className="w-6 h-6 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <button
                        onClick={() => window.location.href = `/admin/customers/${customer.id}`}
                        className="text-lg font-semibold text-blue-600 hover:underline text-left"
                      >
                        {customer.name}
                      </button>
                      <p className="text-sm text-gray-600 flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        {customer.location}
                      </p>
                      <Badge variant="outline" className="mt-1">{customer.sector}</Badge>
                      
                      <div className="mt-2 space-y-1">
                        <p className="text-xs text-gray-600">{customer.contact.person}</p>
                        <div className="flex items-center gap-2">
                          <Phone className="w-3 h-3 text-gray-400" />
                          <span className="text-xs text-gray-600">{customer.contact.phone}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Stats */}
                <div className="lg:col-span-2">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Toplam Sipariş</p>
                      <p className="text-lg font-bold text-gray-900">{customer.stats.totalOrders}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Toplam Değer</p>
                      <p className="text-lg font-bold text-gray-900">₺{(customer.stats.totalValue / 1000).toFixed(0)}K</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Dönüşüm</p>
                      <p className="text-lg font-bold text-gray-900">{customer.stats.conversionRate.toFixed(1)}%</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Son Aktivite</p>
                      <p className="text-sm text-gray-600">{customer.stats.lastActivity}</p>
                    </div>
                  </div>

                  {/* Recent Orders */}
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Son Siparişler</h4>
                    <div className="space-y-1">
                      {customer.recentOrders.slice(0, 2).map((order, index) => (
                        <div key={index} className="text-xs text-gray-600 flex justify-between">
                          <span>{order.productName} ({order.dimension})</span>
                          <span>{order.quantity} m² - ${order.price}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="lg:col-span-1">
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => window.location.href = `/admin/customers/${customer.id}`}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Profili Görüntüle
                    </Button>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Phone className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Mail className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <TrendingUp className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {filteredCustomers.length === 0 && (
        <div className="text-center py-12">
          <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Müşteri bulunamadı</h3>
          <p className="text-gray-600">Arama kriterlerinize uygun müşteri bulunmuyor.</p>
        </div>
      )}
    </div>
  )
}
