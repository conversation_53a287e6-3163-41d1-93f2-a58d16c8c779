import { Router, Request, Response } from 'express';
import { requireAdmin } from '@/middleware/authMiddleware';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    userType: 'producer' | 'customer' | 'admin';
    status: string;
  };
}
import { AdminDashboardService } from '@/services/admin/AdminDashboardService';
import { UserManagementService } from '@/services/admin/UserManagementService';
import { OrderManagementService } from '@/services/admin/OrderManagementService';
import { PaymentManagementService } from '@/services/admin/PaymentManagementService';
import { ProducerApprovalService } from '@/services/admin/ProducerApprovalService';

const router = Router();

// Initialize services
const dashboardService = new AdminDashboardService();
const userService = new UserManagementService();
const orderService = new OrderManagementService();
const paymentService = new PaymentManagementService();
const approvalService = new ProducerApprovalService();

/**
 * @swagger
 * /api/admin/dashboard/overview:
 *   get:
 *     summary: Get comprehensive admin dashboard overview
 *     tags: [Admin Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard overview retrieved successfully
 *       403:
 *         description: Admin access required
 */
router.get('/dashboard/overview', requireAdmin, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const overview = await dashboardService.getDashboardOverview();
    res.json({
      success: true,
      data: overview
    });
  } catch (error) {
    console.error('Error fetching dashboard overview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard overview'
    });
  } finally {
    await dashboardService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/users:
 *   get:
 *     summary: Search and list users with advanced criteria
 *     tags: [Admin User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: query
 *         schema:
 *           type: string
 *         description: Search query for email, company name, or contact person
 *       - in: query
 *         name: userType
 *         schema:
 *           type: string
 *           enum: [PRODUCER, CUSTOMER, ADMIN]
 *         description: Filter by user type
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, ACTIVE, SUSPENDED, BANNED]
 *         description: Filter by user status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 */
router.get('/users', requireAdmin, async (req, res) => {
  try {
    const criteria = {
      query: req.query.query as string,
      userType: req.query.userType as any,
      status: req.query.status as any,
      countryCode: req.query.countryCode as string,
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20
    };

    const result = await userService.searchUsers(criteria);
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error searching users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search users'
    });
  } finally {
    await userService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/users/{userId}:
 *   get:
 *     summary: Get detailed user information
 *     tags: [Admin User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: User details retrieved successfully
 *       404:
 *         description: User not found
 */
router.get('/users/:userId', requireAdmin, async (req, res) => {
  try {
    const userDetails = await userService.getUserDetails(req.params.userId);

    if (!userDetails) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: userDetails
    });
  } catch (error) {
    console.error('Error fetching user details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user details'
    });
  } finally {
    await userService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/users/{userId}/status:
 *   post:
 *     summary: Update user status
 *     tags: [Admin User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [PENDING, ACTIVE, SUSPENDED, BANNED]
 *               reason:
 *                 type: string
 *             required:
 *               - status
 *     responses:
 *       200:
 *         description: User status updated successfully
 */
router.post('/users/:userId/status', requireAdmin, async (req, res) => {
  try {
    const { status, reason } = req.body;
    const adminId = req.user?.id;

    if (!adminId) {
      return res.status(401).json({
        success: false,
        message: 'Admin ID not found'
      });
    }

    await userService.updateUserStatus(req.params.userId, status, adminId, reason);

    res.json({
      success: true,
      message: 'User status updated successfully'
    });
  } catch (error) {
    console.error('Error updating user status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user status'
    });
  } finally {
    await userService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/orders:
 *   get:
 *     summary: Search and list orders with advanced criteria
 *     tags: [Admin Order Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: query
 *         schema:
 *           type: string
 *         description: Search query for order number, customer, or producer
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, CONFIRMED, PRODUCTION, SHIPPED, DELIVERED, CANCELLED]
 *         description: Filter by order status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 */
router.get('/orders', requireAdmin, async (req, res) => {
  try {
    const criteria = {
      query: req.query.query as string,
      status: req.query.status as any,
      customerId: req.query.customerId as string,
      producerId: req.query.producerId as string,
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20
    };

    const result = await orderService.searchOrders(criteria);
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error searching orders:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search orders'
    });
  } finally {
    await orderService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/orders/{orderId}:
 *   get:
 *     summary: Get detailed order information
 *     tags: [Admin Order Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ID
 *     responses:
 *       200:
 *         description: Order details retrieved successfully
 *       404:
 *         description: Order not found
 */
router.get('/orders/:orderId', requireAdmin, async (req, res) => {
  try {
    const orderDetails = await orderService.getOrderDetails(req.params.orderId);

    if (!orderDetails) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: orderDetails
    });
  } catch (error) {
    console.error('Error fetching order details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order details'
    });
  } finally {
    await orderService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/orders/{orderId}/status:
 *   post:
 *     summary: Update order status
 *     tags: [Admin Order Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [PENDING, CONFIRMED, PRODUCTION, SHIPPED, DELIVERED, CANCELLED]
 *               notes:
 *                 type: string
 *             required:
 *               - status
 *     responses:
 *       200:
 *         description: Order status updated successfully
 */
router.post('/orders/:orderId/status', requireAdmin, async (req, res) => {
  try {
    const { status, notes } = req.body;
    const adminId = req.user?.id;

    if (!adminId) {
      return res.status(401).json({
        success: false,
        message: 'Admin ID not found'
      });
    }

    await orderService.updateOrderStatus(req.params.orderId, status, adminId, notes);

    res.json({
      success: true,
      message: 'Order status updated successfully'
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order status'
    });
  } finally {
    await orderService.disconnect();
  }
});

export default router;
