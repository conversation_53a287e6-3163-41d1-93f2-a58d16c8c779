/**
 * Main Chatbot Service
 * Orchestrates all AI chatbot components
 */

import { v4 as uuidv4 } from 'uuid';
import { OpenAIService } from './OpenAIService';
import { DatabaseConversationManager } from './DatabaseConversationManager';
import { KnowledgeBaseService } from './KnowledgeBaseService';
import { DatabaseEscalationManager } from './DatabaseEscalationManager';
import { AnalyticsService } from './AnalyticsService';
import { 
  ChatbotResponse, 
  ConversationContext, 
  ChatbotIntent, 
  ExtractedEntity,
  UserFeedback,
  ChatbotAnalytics 
} from '../types';

export class ChatbotService {
  private openAIService: OpenAIService;
  public conversationManager: DatabaseConversationManager;
  private knowledgeBaseService: KnowledgeBaseService;
  public escalationManager: DatabaseEscalationManager;
  private analyticsService: AnalyticsService;

  constructor() {
    this.openAIService = new OpenAIService();
    this.conversationManager = new DatabaseConversationManager();
    this.knowledgeBaseService = new KnowledgeBaseService();
    this.escalationManager = new DatabaseEscalationManager();
    this.analyticsService = new AnalyticsService();
  }

  /**
   * Start new conversation
   */
  async startConversation(userId?: string, language: string = 'en'): Promise<{
    sessionId: string;
    greeting: string;
  }> {
    const sessionId = await this.conversationManager.createSession(userId, language);
    
    const greeting = this.getGreeting(language);
    
    // Add greeting message to conversation
    await this.conversationManager.addMessage(
      sessionId,
      'assistant',
      greeting,
      ChatbotIntent.GREETING,
      1.0
    );

    return { sessionId, greeting };
  }

  /**
   * Process user message and generate response
   */
  async processMessage(
    sessionId: string,
    message: string,
    userId?: string
  ): Promise<ChatbotResponse> {
    try {
      // Get conversation context
      let context = await this.conversationManager.getContext(sessionId);
      if (!context) {
        throw new Error('Conversation not found');
      }

      // Detect language if not set
      if (!context.language || context.language === 'auto') {
        context.language = await this.openAIService.detectLanguage(message);
        await this.conversationManager.updateContext(sessionId, { language: context.language });
      }

      // Classify intent
      const { intent, confidence } = await this.openAIService.classifyIntent(message, context.language);
      
      // Extract entities
      const entities = await this.openAIService.extractEntities(message, intent);
      
      // Analyze sentiment
      const sentiment = await this.openAIService.analyzeSentiment(message);
      
      // Add user message to conversation
      await this.conversationManager.addMessage(
        sessionId,
        'user',
        message,
        intent,
        confidence,
        entities
      );

      // Update context with new information
      await this.conversationManager.updateContext(sessionId, {
        currentIntent: intent,
        confidence,
        sentiment,
        entities: [...context.entities, ...entities]
      });

      // Get updated context
      context = await this.conversationManager.getContext(sessionId);
      if (!context) {
        throw new Error('Failed to update context');
      }

      // Check if escalation is needed
      if (this.escalationManager.shouldEscalate(context)) {
        return await this.handleEscalation(context);
      }

      // Generate AI response
      const response = await this.generateResponse(context, intent, entities);
      
      // Add assistant response to conversation
      await this.conversationManager.addMessage(
        sessionId,
        'assistant',
        response.response,
        intent,
        response.confidence
      );

      return response;

    } catch (error) {
      console.error('Error processing message:', error);
      return this.getErrorResponse(sessionId, message);
    }
  }

  /**
   * Generate AI response based on context and intent
   */
  private async generateResponse(
    context: ConversationContext,
    intent: ChatbotIntent,
    entities: ExtractedEntity[]
  ): Promise<ChatbotResponse> {
    const knowledgeBase = this.knowledgeBaseService.getKnowledgeBase();
    
    // Try to find specific knowledge for the intent
    let response = '';
    let suggestions: string[] = [];

    switch (intent) {
      case ChatbotIntent.PRODUCT_INQUIRY:
        response = await this.handleProductInquiry(context, entities);
        suggestions = this.getProductSuggestions(context.language);
        break;

      case ChatbotIntent.PRODUCT_SPECIFICATIONS:
        response = await this.handleProductSpecifications(context, entities);
        suggestions = this.getSpecificationSuggestions(context.language);
        break;

      case ChatbotIntent.PRODUCT_PRICING:
        response = await this.handlePricingInquiry(context, entities);
        suggestions = this.getPricingSuggestions(context.language);
        break;

      case ChatbotIntent.BID_PROCESS:
        response = await this.handleBiddingInquiry(context);
        suggestions = this.getBiddingSuggestions(context.language);
        break;

      case ChatbotIntent.ORDER_STATUS:
        response = await this.handleOrderInquiry(context, entities);
        suggestions = this.getOrderSuggestions(context.language);
        break;

      case ChatbotIntent.PAYMENT_METHODS:
        response = await this.handlePaymentInquiry(context);
        suggestions = this.getPaymentSuggestions(context.language);
        break;

      case ChatbotIntent.GREETING:
        response = this.getGreeting(context.language);
        suggestions = this.getGeneralSuggestions(context.language);
        break;

      case ChatbotIntent.GOODBYE:
        response = this.getGoodbye(context.language);
        suggestions = [];
        break;

      default:
        // Use OpenAI for general responses
        response = await this.openAIService.generateResponse(context, knowledgeBase);
        suggestions = this.getGeneralSuggestions(context.language);
    }

    return {
      success: true,
      sessionId: context.sessionId,
      response,
      confidence: context.confidence,
      intent,
      entities,
      suggestions
    };
  }

  /**
   * Handle escalation to human agent
   */
  private async handleEscalation(context: ConversationContext): Promise<ChatbotResponse> {
    const escalationResult = await this.escalationManager.escalateToHuman(context);
    
    // Update conversation context
    await this.conversationManager.updateContext(context.sessionId, {
      escalationLevel: 1,
      escalationReason: escalationResult.reason,
      humanAgentId: escalationResult.agentId
    });

    const response = this.getEscalationMessage(context.language, escalationResult);

    return {
      success: true,
      sessionId: context.sessionId,
      response,
      confidence: 1.0,
      intent: ChatbotIntent.HUMAN_HANDOFF,
      entities: [],
      suggestions: [],
      escalated: true,
      agentId: escalationResult.agentId,
      estimatedWaitTime: escalationResult.estimatedWaitTime
    };
  }

  /**
   * Handle product inquiry
   */
  private async handleProductInquiry(
    context: ConversationContext, 
    entities: ExtractedEntity[]
  ): Promise<string> {
    const stoneType = entities.find(e => e.type === 'stone_type')?.value;
    
    if (stoneType) {
      const stoneInfo = this.knowledgeBaseService.getStoneTypeInfo(stoneType);
      if (stoneInfo) {
        return this.formatStoneTypeResponse(stoneInfo, context.language);
      }
    }

    // Search FAQ for relevant information
    const lastMessage = context.conversationHistory[context.conversationHistory.length - 1];
    const faqs = this.knowledgeBaseService.searchFAQ(lastMessage.content, context.language);
    
    if (faqs.length > 0) {
      return faqs[0].answer;
    }

    // Fallback to OpenAI
    return await this.openAIService.generateResponse(
      context, 
      this.knowledgeBaseService.getKnowledgeBase()
    );
  }

  /**
   * Handle product specifications inquiry
   */
  private async handleProductSpecifications(
    context: ConversationContext,
    entities: ExtractedEntity[]
  ): Promise<string> {
    const stoneType = entities.find(e => e.type === 'stone_type')?.value;
    
    if (stoneType) {
      const stoneInfo = this.knowledgeBaseService.getStoneTypeInfo(stoneType);
      if (stoneInfo) {
        return this.formatSpecificationsResponse(stoneInfo, context.language);
      }
    }

    return await this.openAIService.generateResponse(
      context,
      this.knowledgeBaseService.getKnowledgeBase()
    );
  }

  /**
   * Handle pricing inquiry
   */
  private async handlePricingInquiry(
    context: ConversationContext,
    entities: ExtractedEntity[]
  ): Promise<string> {
    const stoneType = entities.find(e => e.type === 'stone_type')?.value;
    const guidelines = this.knowledgeBaseService.getPricingGuidelines(stoneType);
    
    return this.formatPricingResponse(guidelines, context.language, stoneType);
  }

  /**
   * Handle bidding process inquiry
   */
  private async handleBiddingInquiry(context: ConversationContext): Promise<string> {
    const processInfo = this.knowledgeBaseService.getProcessInfo(ChatbotIntent.BID_PROCESS);
    return this.formatProcessResponse(processInfo, context.language);
  }

  /**
   * Handle order status inquiry
   */
  private async handleOrderInquiry(
    context: ConversationContext,
    entities: ExtractedEntity[]
  ): Promise<string> {
    const orderId = entities.find(e => e.type === 'order_id')?.value;
    
    if (orderId) {
      // In a real implementation, this would query the order database
      return this.getOrderStatusMessage(context.language, orderId);
    }

    const processInfo = this.knowledgeBaseService.getProcessInfo(ChatbotIntent.ORDER_STATUS);
    return this.formatProcessResponse(processInfo, context.language);
  }

  /**
   * Handle payment methods inquiry
   */
  private async handlePaymentInquiry(context: ConversationContext): Promise<string> {
    const faqs = this.knowledgeBaseService.searchFAQ('payment methods', context.language);
    
    if (faqs.length > 0) {
      return faqs[0].answer;
    }

    return this.getPaymentMethodsMessage(context.language);
  }

  /**
   * Submit user feedback
   */
  async submitFeedback(feedback: UserFeedback): Promise<void> {
    try {
      // Store feedback in database
      await this.conversationManager.submitFeedback(
        feedback.sessionId,
        feedback.messageId,
        feedback.rating,
        feedback.feedback
      );

      // Update conversation context if negative feedback
      if (feedback.rating < 3) {
        await this.conversationManager.markIssueUnresolved(
          feedback.sessionId,
          `Low rating: ${feedback.rating}`
        );
      } else {
        await this.conversationManager.markIssueResolved(
          feedback.sessionId,
          'User satisfied with response'
        );
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  }

  /**
   * Get conversation analytics
   */
  async getAnalytics(): Promise<ChatbotAnalytics> {
    try {
      return await this.analyticsService.getAnalytics();
    } catch (error) {
      console.error('Error getting analytics:', error);
      throw error;
    }
  }

  /**
   * Get error response
   */
  private getErrorResponse(sessionId: string, message: string): ChatbotResponse {
    return {
      success: false,
      sessionId,
      response: 'I apologize, but I encountered an error processing your message. Please try again.',
      confidence: 0.0,
      intent: ChatbotIntent.UNKNOWN,
      entities: [],
      suggestions: ['Try rephrasing your question', 'Contact human support']
    };
  }

  // Helper methods for formatting responses and getting localized messages
  private getGreeting(language: string): string {
    const greetings = {
      'en': 'Hello! Welcome to our natural stone marketplace. How can I help you today?',
      'tr': 'Merhaba! Doğal taş pazaryerimize hoş geldiniz. Size nasıl yardımcı olabilirim?',
      'ar': 'مرحبا! أهلاً بك في سوق الحجر الطبيعي. كيف يمكنني مساعدتك اليوم؟',
      'de': 'Hallo! Willkommen auf unserem Naturstein-Marktplatz. Wie kann ich Ihnen heute helfen?',
      'fr': 'Bonjour! Bienvenue sur notre marché de pierre naturelle. Comment puis-je vous aider aujourd\'hui?',
      'es': '¡Hola! Bienvenido a nuestro mercado de piedra natural. ¿Cómo puedo ayudarte hoy?',
      'it': 'Ciao! Benvenuto nel nostro mercato della pietra naturale. Come posso aiutarti oggi?',
      'ru': 'Привет! Добро пожаловать на наш рынок натурального камня. Как я могу помочь вам сегодня?',
      'zh': '你好！欢迎来到我们的天然石材市场。今天我能为您做些什么？',
      'ja': 'こんにちは！天然石マーケットプレイスへようこそ。今日はどのようにお手伝いできますか？'
    };
    
    return (greetings as any)[language] || greetings['en'];
  }

  private getGoodbye(language: string): string {
    const goodbyes = {
      'en': 'Thank you for using our service. Have a great day!',
      'tr': 'Hizmetimizi kullandığınız için teşekkür ederiz. İyi günler!',
      'ar': 'شكراً لاستخدام خدمتنا. أتمنى لك يوماً سعيداً!',
      'de': 'Vielen Dank für die Nutzung unseres Services. Haben Sie einen schönen Tag!',
      'fr': 'Merci d\'utiliser notre service. Passez une excellente journée!',
      'es': 'Gracias por usar nuestro servicio. ¡Que tengas un gran día!',
      'it': 'Grazie per aver utilizzato il nostro servizio. Buona giornata!',
      'ru': 'Спасибо за использование нашего сервиса. Хорошего дня!',
      'zh': '感谢您使用我们的服务。祝您有美好的一天！',
      'ja': 'サービスをご利用いただきありがとうございます。良い一日をお過ごしください！'
    };
    
    return (goodbyes as any)[language] || goodbyes['en'];
  }

  private getGeneralSuggestions(language: string): string[] {
    const suggestions = {
      'en': [
        'Tell me about marble types',
        'What are your delivery terms?',
        'How does bidding work?',
        'What payment methods do you accept?'
      ],
      'tr': [
        'Mermer çeşitleri hakkında bilgi ver',
        'Teslimat koşullarınız nelerdir?',
        'Teklif sistemi nasıl çalışır?',
        'Hangi ödeme yöntemlerini kabul ediyorsunuz?'
      ]
    };
    
    return (suggestions as any)[language] || suggestions['en'];
  }

  private getProductSuggestions(language: string): string[] {
    const suggestions = {
      'en': [
        'Show me marble specifications',
        'What finishes are available?',
        'Tell me about travertine',
        'What sizes do you offer?'
      ],
      'tr': [
        'Mermer özelliklerini göster',
        'Hangi yüzey işlemleri mevcut?',
        'Traverten hakkında bilgi ver',
        'Hangi boyutları sunuyorsunuz?'
      ]
    };
    
    return suggestions[language as keyof typeof suggestions] || suggestions['en'];
  }

  private getSpecificationSuggestions(language: string): string[] {
    const suggestions = {
      'en': [
        'Water absorption rates',
        'Density specifications',
        'Available thicknesses',
        'Quality standards'
      ],
      'tr': [
        'Su emme oranları',
        'Yoğunluk özellikleri',
        'Mevcut kalınlıklar',
        'Kalite standartları'
      ]
    };

    return (suggestions as any)[language] || suggestions['en'];
  }

  private getPricingSuggestions(language: string): string[] {
    const suggestions = {
      'en': [
        'Get a price quote',
        'Bulk pricing discounts',
        'Shipping costs',
        'Payment terms'
      ],
      'tr': [
        'Fiyat teklifi al',
        'Toplu alım indirimleri',
        'Nakliye maliyetleri',
        'Ödeme koşulları'
      ]
    };
    
    return (suggestions as any)[language] || suggestions['en'];
  }

  private getBiddingSuggestions(language: string): string[] {
    const suggestions = {
      'en': [
        'How to create a bid request',
        'Bidding timeline',
        'Anonymous bidding benefits',
        'Payment after bid selection'
      ],
      'tr': [
        'Teklif talebi nasıl oluşturulur',
        'Teklif zaman çizelgesi',
        'Anonim teklif avantajları',
        'Teklif seçimi sonrası ödeme'
      ]
    };
    
    return (suggestions as any)[language] || suggestions['en'];
  }

  private getOrderSuggestions(language: string): string[] {
    const suggestions = {
      'en': [
        'Track my order',
        'Order modification',
        'Delivery schedule',
        'Quality control process'
      ],
      'tr': [
        'Siparişimi takip et',
        'Sipariş değişikliği',
        'Teslimat programı',
        'Kalite kontrol süreci'
      ]
    };
    
    return (suggestions as any)[language] || suggestions['en'];
  }

  private getPaymentSuggestions(language: string): string[] {
    const suggestions = {
      'en': [
        'Escrow payment protection',
        'Bank transfer details',
        'Letter of credit process',
        'Payment timeline'
      ],
      'tr': [
        'Emanet ödeme koruması',
        'Banka havalesi detayları',
        'Akreditif süreci',
        'Ödeme zaman çizelgesi'
      ]
    };
    
    return (suggestions as any)[language] || suggestions['en'];
  }

  private formatStoneTypeResponse(stoneInfo: any, language: string): string {
    // Implementation would format stone type information based on language
    return `Information about ${stoneInfo.name}: ${JSON.stringify(stoneInfo.properties)}`;
  }

  private formatSpecificationsResponse(stoneInfo: any, language: string): string {
    // Implementation would format specifications based on language
    return `Technical specifications for ${stoneInfo.name}: ${JSON.stringify(stoneInfo.properties)}`;
  }

  private formatPricingResponse(guidelines: any, language: string, stoneType?: string): string {
    // Implementation would format pricing information based on language
    return `Pricing information${stoneType ? ` for ${stoneType}` : ''}: Please contact us for detailed quotes.`;
  }

  private formatProcessResponse(processInfo: any, language: string): string {
    // Implementation would format process information based on language
    return `Process information: ${processInfo ? JSON.stringify(processInfo.steps) : 'Information not available'}`;
  }

  private getOrderStatusMessage(language: string, orderId: string): string {
    const messages = {
      'en': `I can help you check the status of order ${orderId}. Please contact our support team for detailed order information.`,
      'tr': `${orderId} numaralı siparişinizin durumunu kontrol etmenize yardımcı olabilirim. Detaylı sipariş bilgisi için destek ekibimizle iletişime geçin.`
    };
    
    return (messages as any)[language] || messages['en'];
  }

  private getPaymentMethodsMessage(language: string): string {
    const messages = {
      'en': 'We accept bank transfers, letters of credit (L/C), and credit cards. All payments are processed through our secure escrow system.',
      'tr': 'Banka havalesi, akreditif (L/C) ve kredi kartı kabul ediyoruz. Tüm ödemeler güvenli emanet sistemimiz üzerinden işlenir.'
    };

    return (messages as any)[language] || messages['en'];
  }

  private getEscalationMessage(language: string, escalationResult: any): string {
    const messages = {
      'en': `I'm connecting you with a human agent who can better assist you. ${escalationResult.estimatedWaitTime ? `Estimated wait time: ${escalationResult.estimatedWaitTime} minutes.` : ''}`,
      'tr': `Size daha iyi yardımcı olabilecek bir insan operatörle bağlantı kuruyorum. ${escalationResult.estimatedWaitTime ? `Tahmini bekleme süresi: ${escalationResult.estimatedWaitTime} dakika.` : ''}`
    };

    return (messages as any)[language] || messages['en'];
  }
}
