import React from 'react';
import {
  BeakerIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  TruckIcon,
  StarIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline';
import { SampleStatusUtilsConfig } from '@/types/sample-request';

export const getSampleStatusIcon = (status: string): React.ReactElement => {
  switch (status) {
    case 'pending':
      return <ClockIcon className="h-5 w-5 text-yellow-500" />;
    case 'approved':
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
    case 'approved_pending_payment':
    case 'payment_required':
      return <CreditCardIcon className="h-5 w-5 text-orange-500" />;
    case 'rejected':
      return <XCircleIcon className="h-5 w-5 text-red-500" />;
    case 'preparing':
      return <BeakerIcon className="h-5 w-5 text-blue-500" />;
    case 'shipped':
      return <TruckIcon className="h-5 w-5 text-purple-500" />;
    case 'delivered':
      return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
    case 'evaluated':
      return <StarIcon className="h-5 w-5 text-yellow-600" />;
    default:
      return <ClockIcon className="h-5 w-5 text-gray-500" />;
  }
};

export const getSampleStatusLabel = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'Onay Bekliyor';
    case 'approved':
      return 'Onaylandı';
    case 'approved_pending_payment':
    case 'payment_required':
      return 'Ödeme Bekleniyor';
    case 'rejected':
      return 'Reddedildi';
    case 'preparing':
      return 'Hazırlanıyor';
    case 'shipped':
      return 'Gönderildi';
    case 'delivered':
      return 'Teslim Edildi';
    case 'evaluated':
      return 'Değerlendirildi';
    default:
      return status;
  }
};

export const getSampleStatusColor = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'approved_pending_payment':
    case 'payment_required':
      return 'bg-orange-100 text-orange-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'preparing':
      return 'bg-blue-100 text-blue-800';
    case 'shipped':
      return 'bg-purple-100 text-purple-800';
    case 'delivered':
      return 'bg-green-100 text-green-800';
    case 'evaluated':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getSampleStatusConfig = (status: string): SampleStatusUtilsConfig => {
  return {
    status,
    icon: getSampleStatusIcon(status).type,
    label: getSampleStatusLabel(status),
    color: getSampleStatusColor(status).split(' ')[1], // Extract text color
    bgColor: getSampleStatusColor(status)
  };
};

export const getAllSampleStatuses = (): string[] => {
  return [
    'pending',
    'approved',
    'payment_required',
    'preparing',
    'shipped',
    'delivered',
    'evaluated',
    'rejected'
  ];
};

export const getStatusPriority = (status: string): number => {
  const priorities: Record<string, number> = {
    'payment_required': 1,
    'approved_pending_payment': 1,
    'pending': 2,
    'preparing': 3,
    'shipped': 4,
    'delivered': 5,
    'approved': 6,
    'evaluated': 7,
    'rejected': 8
  };
  
  return priorities[status] || 9;
};
