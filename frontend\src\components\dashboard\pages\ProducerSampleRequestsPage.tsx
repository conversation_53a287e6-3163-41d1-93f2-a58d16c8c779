'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BeakerIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  TruckIcon,
  EyeIcon,
  CheckIcon,
  XMarkIcon,
  UserIcon as User,
  CubeIcon as Package
} from '@heroicons/react/24/outline';
import { useSample } from '@/contexts/sample-context';

interface ProducerSampleRequestsPageProps {
  onNavigate?: (route: string) => void;
}

const ProducerSampleRequestsPage: React.FC<ProducerSampleRequestsPageProps> = ({ onNavigate }) => {
  const { getSampleRequestsByProducer, approveSampleRequestByProducer, updateSampleStatus, isLoading } = useSample();
  const [sampleRequests, setSampleRequests] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<'pending' | 'approved' | 'preparing' | 'shipped' | 'all'>('pending');
  const [selectedSample, setSelectedSample] = useState<any>(null);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [approvalData, setApprovalData] = useState({
    approved: true,
    notes: '',
    rejectionReason: '',
    preparationDays: 3,
    shippingCost: 0,
    carrier: 'aras'
  });
  const [statusData, setStatusData] = useState({
    status: 'preparing',
    notes: '',
    shippingInfo: {
      carrier: '',
      trackingNumber: '',
      estimatedDelivery: ''
    }
  });

  const producerId = '1'; // Should be actual producer ID

  useEffect(() => {
    loadSampleRequests();
  }, [activeTab]);

  // Calculate shipping cost automatically
  const calculateShippingCost = async (sampleRequest: any) => {
    try {
      const response = await fetch('http://localhost:8001/api/samples/shipping/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fromAddress: { city: 'İstanbul', country: 'Türkiye' }, // Producer location
          toAddress: {
            city: sampleRequest.deliveryAddress.city,
            country: sampleRequest.deliveryAddress.country
          },
          productCount: sampleRequest.requestedProducts.length,
          carrier: approvalData.carrier
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setApprovalData(prev => ({
          ...prev,
          shippingCost: result.data.shippingRate.baseRate
        }));
      }
    } catch (error) {
      console.error('Error calculating shipping cost:', error);
    }
  };

  const loadSampleRequests = async () => {
    try {
      const data = await getSampleRequestsByProducer(
        producerId, 
        activeTab === 'all' ? undefined : activeTab
      );
      setSampleRequests(data);
    } catch (error) {
      console.error('Error loading sample requests:', error);
    }
  };

  const handleApproval = async () => {
    if (!selectedSample) return;

    try {
      await approveSampleRequestByProducer(
        selectedSample.id,
        approvalData.approved,
        approvalData.notes,
        approvalData.rejectionReason,
        approvalData.preparationDays
      );

      setShowApprovalModal(false);
      setSelectedSample(null);
      setApprovalData({ approved: true, notes: '', rejectionReason: '', preparationDays: 3, shippingCost: 0, carrier: 'aras' });
      loadSampleRequests();
      alert(approvalData.approved ? 'Numune talebi onaylandı!' : 'Numune talebi reddedildi!');
    } catch (error) {
      console.error('Error processing approval:', error);
      alert('İşlem sırasında hata oluştu.');
    }
  };

  const handleStatusUpdate = async () => {
    if (!selectedSample) return;

    try {
      await updateSampleStatus(selectedSample.id, statusData.status, statusData);
      setShowStatusModal(false);
      setSelectedSample(null);
      setStatusData({
        status: 'preparing',
        notes: '',
        shippingInfo: { carrier: '', trackingNumber: '', estimatedDelivery: '' }
      });
      loadSampleRequests();
      alert('Durum başarıyla güncellendi!');
    } catch (error) {
      console.error('Error updating status:', error);
      alert('Durum güncellenirken hata oluştu.');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'approved':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'preparing':
        return <BeakerIcon className="h-5 w-5 text-blue-500" />;
      case 'shipped':
        return <TruckIcon className="h-5 w-5 text-purple-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Onay Bekliyor';
      case 'approved':
        return 'Onaylandı';
      case 'rejected':
        return 'Reddedildi';
      case 'preparing':
        return 'Hazırlanıyor';
      case 'shipped':
        return 'Gönderildi';
      case 'delivered':
        return 'Teslim Edildi';
      case 'evaluated':
        return 'Değerlendirildi';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'preparing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'evaluated':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const tabs = [
    { key: 'pending', label: 'Onay Bekleyen', count: sampleRequests.filter(r => r.status === 'pending').length },
    { key: 'approved', label: 'Onaylanan', count: sampleRequests.filter(r => r.status === 'approved').length },
    { key: 'preparing', label: 'Hazırlanan', count: sampleRequests.filter(r => r.status === 'preparing').length },
    { key: 'shipped', label: 'Gönderilen', count: sampleRequests.filter(r => r.status === 'shipped').length },
    { key: 'all', label: 'Tümü', count: sampleRequests.length }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Numune talepleri yükleniyor...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Numune Talepleri</h1>
          <p className="text-gray-600 mt-1">Müşteri numune taleplerini onaylayın ve yönetin</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                  activeTab === tab.key
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Sample Requests List */}
      {sampleRequests.length > 0 ? (
        <div className="space-y-4">
          {sampleRequests.map((request, index) => (
            <motion.div
              key={request.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200"
            >
              {/* Request Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(request.status)}
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(request.status)}`}>
                      {getStatusLabel(request.status)}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Numune Talebi #{request.id.slice(-6)}
                    </h3>
                    <p className="text-sm text-gray-600">
                      Müşteri: {request.deliveryAddress.name} - {new Date(request.createdAt).toLocaleDateString('tr-TR')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Products */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Talep Edilen Ürünler</h4>
                <div className="space-y-2">
                  {request.requestedProducts.map((product: any, idx: number) => (
                    <div key={idx} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                      <div>
                        <p className="font-medium text-gray-900">{product.productName}</p>
                        <p className="text-sm text-gray-600">{product.specifications}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">{product.sampleSize}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => {
                      setSelectedSample(request);
                      setShowDetailModal(true);
                    }}
                    className="flex items-center space-x-2 text-blue-600 hover:text-blue-700"
                  >
                    <EyeIcon className="h-4 w-4" />
                    <span className="text-sm font-medium">Detay</span>
                  </button>
                </div>
                
                <div className="flex items-center space-x-3">
                  {request.status === 'pending' && (
                    <>
                      <button
                        onClick={() => {
                          setSelectedSample(request);
                          setApprovalData(prev => ({ ...prev, approved: false }));
                          setShowApprovalModal(true);
                        }}
                        className="px-4 py-2 text-sm font-medium text-red-700 bg-red-100 rounded-lg hover:bg-red-200 flex items-center space-x-1"
                      >
                        <XMarkIcon className="h-4 w-4" />
                        <span>Reddet</span>
                      </button>
                      <button
                        onClick={() => {
                          setSelectedSample(request);
                          setApprovalData(prev => ({ ...prev, approved: true }));
                          setShowApprovalModal(true);
                          // Auto-calculate shipping cost
                          setTimeout(() => calculateShippingCost(request), 100);
                        }}
                        className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 flex items-center space-x-1"
                      >
                        <CheckIcon className="h-4 w-4" />
                        <span>Onayla</span>
                      </button>
                    </>
                  )}
                  
                  {(request.status === 'approved' || request.status === 'preparing') && (
                    <button
                      onClick={() => {
                        setSelectedSample(request);
                        setShowStatusModal(true);
                      }}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                    >
                      Durum Güncelle
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <BeakerIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {activeTab === 'pending' ? 'Onay bekleyen numune talebi bulunmuyor' : 'Numune talebi bulunmuyor'}
          </h3>
          <p className="text-gray-600">
            Müşteriler sizin ürünlerinizden numune talep ettiğinde burada görünecektir.
          </p>
        </div>
      )}

      {/* Approval Modal */}
      {showApprovalModal && selectedSample && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Numune Talebi #{selectedSample.id.slice(-6)}
              </h3>
              
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Müşteri Bilgileri</h4>
                  <p className="text-sm text-gray-600">{selectedSample.deliveryAddress.name}</p>
                  <p className="text-sm text-gray-600">{selectedSample.deliveryAddress.city}, {selectedSample.deliveryAddress.country}</p>
                </div>

                {approvalData.approved ? (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Hazırlık Süresi (Gün)
                      </label>
                      <input
                        type="number"
                        value={approvalData.preparationDays}
                        onChange={(e) => setApprovalData(prev => ({ ...prev, preparationDays: Number(e.target.value) }))}
                        min="1"
                        max="30"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Kargo Firması
                        </label>
                        <select
                          value={approvalData.carrier}
                          onChange={(e) => {
                            setApprovalData(prev => ({ ...prev, carrier: e.target.value }));
                            if (selectedSample) calculateShippingCost(selectedSample);
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="aras">Aras Kargo</option>
                          <option value="mng">MNG Kargo</option>
                          <option value="ups">UPS</option>
                          <option value="dhl">DHL</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Kargo Ücreti (TL) *
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            value={approvalData.shippingCost}
                            onChange={(e) => setApprovalData(prev => ({ ...prev, shippingCost: Number(e.target.value) }))}
                            min="0"
                            step="5"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="0"
                          />
                          <button
                            type="button"
                            onClick={() => selectedSample && calculateShippingCost(selectedSample)}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-blue-600 hover:text-blue-700"
                          >
                            Hesapla
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <p className="text-sm text-yellow-800">
                        <strong>Önemli:</strong> Kargo ücreti müşteri tarafından ödenecektir.
                        Onay verdikten sonra müşteriye ödeme talebi gönderilecek ve ödeme yapıldıktan sonra numune hazırlığı başlayacaktır.
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Notlar
                      </label>
                      <textarea
                        value={approvalData.notes}
                        onChange={(e) => setApprovalData(prev => ({ ...prev, notes: e.target.value }))}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Onay notları..."
                      />
                    </div>
                  </>
                ) : (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Red Sebebi *
                    </label>
                    <textarea
                      value={approvalData.rejectionReason}
                      onChange={(e) => setApprovalData(prev => ({ ...prev, rejectionReason: e.target.value }))}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Red sebebini belirtin..."
                      required
                    />
                  </div>
                )}
              </div>

              <div className="flex items-center justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowApprovalModal(false);
                    setApprovalData({ approved: true, notes: '', rejectionReason: '', preparationDays: 3, shippingCost: 0, carrier: 'aras' });
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  İptal
                </button>
                <button
                  onClick={handleApproval}
                  disabled={
                    (!approvalData.approved && !approvalData.rejectionReason.trim()) ||
                    (approvalData.approved && approvalData.shippingCost <= 0)
                  }
                  className={`px-4 py-2 text-sm font-medium text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed ${
                    approvalData.approved
                      ? 'bg-green-600 hover:bg-green-700'
                      : 'bg-red-600 hover:bg-red-700'
                  }`}
                >
                  {approvalData.approved ? `Onayla (${approvalData.shippingCost} TL Kargo)` : 'Reddet'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Status Update Modal */}
      {showStatusModal && selectedSample && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Durum Güncelle #{selectedSample.id.slice(-6)}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Yeni Durum
                  </label>
                  <select
                    value={statusData.status}
                    onChange={(e) => setStatusData(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="preparing">Hazırlanıyor</option>
                    <option value="shipped">Gönderildi</option>
                    <option value="delivered">Teslim Edildi</option>
                  </select>
                </div>

                {statusData.status === 'shipped' && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Kargo Firması
                      </label>
                      <input
                        type="text"
                        value={statusData.shippingInfo.carrier}
                        onChange={(e) => setStatusData(prev => ({
                          ...prev,
                          shippingInfo: { ...prev.shippingInfo, carrier: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Aras Kargo, MNG, vb."
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Takip Numarası
                      </label>
                      <input
                        type="text"
                        value={statusData.shippingInfo.trackingNumber}
                        onChange={(e) => setStatusData(prev => ({
                          ...prev,
                          shippingInfo: { ...prev.shippingInfo, trackingNumber: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Kargo takip numarası"
                      />
                    </div>
                  </>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notlar
                  </label>
                  <textarea
                    value={statusData.notes}
                    onChange={(e) => setStatusData(prev => ({ ...prev, notes: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Durum güncelleme notları..."
                  />
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowStatusModal(false);
                    setStatusData({
                      status: 'preparing',
                      notes: '',
                      shippingInfo: { carrier: '', trackingNumber: '', estimatedDelivery: '' }
                    });
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  İptal
                </button>
                <button
                  onClick={handleStatusUpdate}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                >
                  Güncelle
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Detail Modal */}
      {showDetailModal && selectedSample && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    Numune Talebi Detayları #{selectedSample.id.slice(-6)}
                  </h3>
                  <div className="flex items-center space-x-2 mt-2">
                    {getStatusIcon(selectedSample.status)}
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedSample.status)}`}>
                      {getStatusLabel(selectedSample.status)}
                    </span>
                    <span className="text-sm text-gray-500">
                      {new Date(selectedSample.createdAt).toLocaleDateString('tr-TR')}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShowDetailModal(false);
                    setSelectedSample(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column */}
                <div className="space-y-6">
                  {/* Customer Information */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                      <User className="h-5 w-5 mr-2" />
                      Müşteri Bilgileri
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Ad Soyad:</span>
                        <span className="ml-2 text-gray-900">{selectedSample.deliveryAddress.name}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Telefon:</span>
                        <span className="ml-2 text-gray-900">{selectedSample.deliveryAddress.phone}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Adres:</span>
                        <span className="ml-2 text-gray-900">
                          {selectedSample.deliveryAddress.address}, {selectedSample.deliveryAddress.city}, {selectedSample.deliveryAddress.country}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Requested Products */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                      <Package className="h-5 w-5 mr-2" />
                      Talep Edilen Ürünler
                    </h4>
                    <div className="space-y-3">
                      {selectedSample.requestedProducts.map((product: any, idx: number) => (
                        <div key={idx} className="bg-white rounded-lg p-3 border">
                          <div className="flex items-start justify-between">
                            <div>
                              <h5 className="font-medium text-gray-900">{product.productName}</h5>
                              <p className="text-sm text-gray-600 mt-1">{product.specifications}</p>
                            </div>
                            <div className="text-right">
                              <span className="text-sm font-medium text-blue-600">{product.sampleSize}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Sample Specifications */}
                  {selectedSample.sampleSpecifications && (
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900 mb-3">Özel Gereksinimler</h4>
                      <p className="text-sm text-gray-700">{selectedSample.sampleSpecifications}</p>
                    </div>
                  )}
                </div>

                {/* Right Column */}
                <div className="space-y-6">
                  {/* Producer Response */}
                  {selectedSample.producerResponse && (
                    <div className="bg-green-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <CheckCircleIcon className="h-5 w-5 mr-2 text-green-600" />
                        Üretici Yanıtı
                      </h4>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Durum:</span>
                          <span className={`ml-2 ${selectedSample.producerResponse.approved ? 'text-green-600' : 'text-red-600'}`}>
                            {selectedSample.producerResponse.approved ? 'Onaylandı' : 'Reddedildi'}
                          </span>
                        </div>
                        {selectedSample.producerResponse.preparationDays && (
                          <div>
                            <span className="font-medium text-gray-700">Hazırlık Süresi:</span>
                            <span className="ml-2 text-gray-900">{selectedSample.producerResponse.preparationDays} gün</span>
                          </div>
                        )}
                        {selectedSample.producerResponse.notes && (
                          <div>
                            <span className="font-medium text-gray-700">Notlar:</span>
                            <p className="mt-1 text-gray-900">{selectedSample.producerResponse.notes}</p>
                          </div>
                        )}
                        <div>
                          <span className="font-medium text-gray-700">Yanıt Tarihi:</span>
                          <span className="ml-2 text-gray-900">
                            {new Date(selectedSample.producerResponse.responseDate).toLocaleDateString('tr-TR')}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Shipping Information */}
                  {selectedSample.shippingInfo && (
                    <div className="bg-blue-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <TruckIcon className="h-5 w-5 mr-2 text-blue-600" />
                        Kargo Bilgileri
                      </h4>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Kargo Firması:</span>
                          <span className="ml-2 text-gray-900">{selectedSample.shippingInfo.carrier}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Takip Numarası:</span>
                          <span className="ml-2 text-gray-900 font-mono">{selectedSample.shippingInfo.trackingNumber}</span>
                        </div>
                        {selectedSample.shippingInfo.estimatedDelivery && (
                          <div>
                            <span className="font-medium text-gray-700">Tahmini Teslimat:</span>
                            <span className="ml-2 text-gray-900">
                              {new Date(selectedSample.shippingInfo.estimatedDelivery).toLocaleDateString('tr-TR')}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Status Timeline */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                      <ClockIcon className="h-5 w-5 mr-2" />
                      Durum Geçmişi
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <div className="text-sm">
                          <span className="font-medium text-gray-900">Talep Oluşturuldu</span>
                          <span className="text-gray-500 ml-2">
                            {new Date(selectedSample.createdAt).toLocaleDateString('tr-TR')}
                          </span>
                        </div>
                      </div>

                      {selectedSample.approvedAt && (
                        <div className="flex items-center space-x-3">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <div className="text-sm">
                            <span className="font-medium text-gray-900">Onaylandı</span>
                            <span className="text-gray-500 ml-2">
                              {new Date(selectedSample.approvedAt).toLocaleDateString('tr-TR')}
                            </span>
                          </div>
                        </div>
                      )}

                      {selectedSample.shippedAt && (
                        <div className="flex items-center space-x-3">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          <div className="text-sm">
                            <span className="font-medium text-gray-900">Gönderildi</span>
                            <span className="text-gray-500 ml-2">
                              {new Date(selectedSample.shippedAt).toLocaleDateString('tr-TR')}
                            </span>
                          </div>
                        </div>
                      )}

                      {selectedSample.deliveredAt && (
                        <div className="flex items-center space-x-3">
                          <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                          <div className="text-sm">
                            <span className="font-medium text-gray-900">Teslim Edildi</span>
                            <span className="text-gray-500 ml-2">
                              {new Date(selectedSample.deliveredAt).toLocaleDateString('tr-TR')}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer Actions */}
              <div className="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={() => {
                    setShowDetailModal(false);
                    setSelectedSample(null);
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  Kapat
                </button>

                {selectedSample.status === 'pending' && (
                  <button
                    onClick={() => {
                      setShowDetailModal(false);
                      setShowApprovalModal(true);
                    }}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                  >
                    Onay Ver / Reddet
                  </button>
                )}

                {(selectedSample.status === 'approved' || selectedSample.status === 'preparing') && (
                  <button
                    onClick={() => {
                      setShowDetailModal(false);
                      setShowStatusModal(true);
                    }}
                    className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700"
                  >
                    Durum Güncelle
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProducerSampleRequestsPage;
