// Çoklu Teslimat Sistemi için Type Definitions

export interface DeliveryPackage {
  id: string
  orderId: string
  packageNumber: number
  quantity: number
  amount: number
  
  // Durumlar
  productionStatus: 'pending' | 'in_progress' | 'completed' | 'paused'
  deliveryStatus: 'pending' | 'ready' | 'shipped' | 'delivered'
  paymentStatus: 'pending' | 'paid' | 'overdue'
  
  // Tarihler
  productionStartDate?: string
  productionEndDate?: string
  deliveryDate?: string
  actualDeliveryDate?: string
  
  // Notlar
  productionNotes?: string
  deliveryNotes?: string
  
  // İlişkili veriler
  productionSchedules: ProductionSchedule[]
  deliverySchedule?: DeliverySchedule
  payments: PackagePayment[]
  
  createdAt: string
  updatedAt: string
}

export interface ProductionSchedule {
  id: string
  deliveryPackageId: string
  stageName: string
  stageOrder: number
  
  // Durumlar
  status: 'pending' | 'in_progress' | 'completed' | 'paused'
  
  // Tarihler
  plannedStartDate?: string
  plannedEndDate?: string
  actualStartDate?: string
  actualEndDate?: string
  
  // Detaylar
  assignedWorker?: string
  notes?: string
  
  createdAt: string
  updatedAt: string
}

export interface DeliverySchedule {
  id: string
  deliveryPackageId: string
  
  // Teslimat detayları
  deliveryMethod: 'factory_pickup' | 'delivery' | 'partial_delivery'
  deliveryAddress?: string
  
  // Tarihler
  scheduledDate: string
  actualDate?: string
  
  // Kargo bilgileri
  trackingNumber?: string
  carrierCompany?: string
  
  // Durumlar
  status: 'scheduled' | 'in_transit' | 'delivered' | 'failed'
  
  // Notlar
  notes?: string
  
  createdAt: string
  updatedAt: string
}

export interface PackagePayment {
  id: string
  deliveryPackageId: string
  
  // Ödeme detayları
  amount: number
  paymentType: 'advance' | 'delivery' | 'completion'
  
  // Durumlar
  status: 'pending' | 'paid' | 'overdue' | 'cancelled'
  
  // Tarihler
  dueDate: string
  paidDate?: string
  
  // Ödeme bilgileri
  paymentMethod?: string
  transactionId?: string
  
  // Notlar
  notes?: string
  
  createdAt: string
  updatedAt: string
}

export interface MultiDeliveryOrder {
  id: string
  customerId: string
  producerId: string
  productId: string
  
  // Toplam sipariş bilgileri
  totalQuantity: number
  totalAmount: number
  
  // Teslimat tipi
  deliveryType: 'single' | 'multiple'
  
  // Genel sipariş durumu
  status: 'pending' | 'confirmed' | 'in_production' | 'partially_delivered' | 'completed' | 'cancelled'
  
  // Tarihler
  orderDate: string
  estimatedCompletionDate?: string
  
  // Diğer alanlar
  notes?: string
  
  // İlişkili veriler
  deliveryPackages: DeliveryPackage[]
  
  createdAt: string
  updatedAt: string
}

// Utility Types
export interface ProductionProgress {
  packageId: string
  packageNumber: number
  totalStages: number
  completedStages: number
  currentStage?: ProductionSchedule
  progressPercentage: number
}

export interface DeliveryProgress {
  packageId: string
  packageNumber: number
  status: DeliveryPackage['deliveryStatus']
  scheduledDate?: string
  actualDate?: string
  trackingNumber?: string
}

export interface PaymentProgress {
  packageId: string
  packageNumber: number
  totalAmount: number
  paidAmount: number
  pendingAmount: number
  overdueAmount: number
  nextPaymentDue?: string
}

// API Response Types
export interface MultiDeliveryOrderResponse {
  success: boolean
  data: MultiDeliveryOrder
  message?: string
}

export interface DeliveryPackagesResponse {
  success: boolean
  data: DeliveryPackage[]
  message?: string
}

export interface ProductionScheduleResponse {
  success: boolean
  data: ProductionSchedule[]
  message?: string
}
