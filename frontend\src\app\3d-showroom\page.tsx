'use client';

import React, { useState } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/auth-context';

export default function ThreeDShowroomPage() {
  const [viewMode, setViewMode] = useState<'room' | 'product'>('room');
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const { user, isAuthenticated, logout, showLoginModal, showCustomerRegisterModal } = useAuth();

  // Navigation links
  const navigationLinks = [
    { name: "<PERSON> Say<PERSON>", href: "/" },
    { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/products" },
    { name: "3D Sanal Showroom", href: "/3d-showroom", active: true },
    { name: "Ha<PERSON>ler", href: "/news" },
    { name: "Hakkımızda", href: "/about" },
    { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/contact" }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Navigation
        brand={{
          name: "Türkiye Doğal Taş Pazarı",
          href: "/"
        }}
        links={navigationLinks}
        actions={
          <div className="flex items-center gap-2">
            {!isAuthenticated ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={showLoginModal}
                >
                  Giriş Yap
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={showCustomerRegisterModal}
                >
                  Kayıt Ol
                </Button>
              </>
            ) : (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 max-w-[200px] truncate">
                  Hoş geldiniz, {user?.company || user?.name}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={logout}
                >
                  Çıkış Yap
                </Button>
              </div>
            )}
          </div>
        }
      />

      {/* 3D Showroom Header */}
      <div className="bg-white shadow border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-blue-600 rounded"></div>
              <h1 className="text-2xl font-bold text-gray-900">3D Showroom</h1>
              <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-sm">Beta</span>
            </div>

            <div className="flex space-x-2">
              <button
                className="px-3 py-1 border rounded text-sm hover:bg-gray-50"
                onClick={() => alert('Paylaşım özelliği yakında aktif olacak')}
              >
                Paylaş
              </button>
              <button
                className="px-3 py-1 border rounded text-sm hover:bg-gray-50"
                onClick={() => alert('İndirme özelliği yakında aktif olacak')}
              >
                İndir
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">

          {/* Sol Panel */}
          <div className="lg:col-span-1 space-y-4">

            {/* Görünüm Modu */}
            <div className="bg-white rounded shadow p-4">
              <h3 className="text-lg font-semibold mb-3">Görünüm Modu</h3>
              <div className="grid grid-cols-2 gap-2">
                <button
                  className={`px-3 py-2 rounded ${viewMode === 'room' ? 'bg-blue-600 text-white' : 'border'}`}
                  onClick={() => setViewMode('room')}
                >
                  Oda
                </button>
                <button
                  className={`px-3 py-2 rounded ${viewMode === 'product' ? 'bg-blue-600 text-white' : 'border'}`}
                  onClick={() => setViewMode('product')}
                >
                  Ürün
                </button>
              </div>
            </div>

            {/* Ürün Seçimi */}
            <div className="bg-white rounded shadow p-4">
              <h3 className="text-lg font-semibold mb-3">Ürün Seçimi</h3>
              <div className="space-y-2">
                <button
                  className={`w-full p-3 text-left rounded ${selectedProduct === 'marble' ? 'bg-blue-50 border-blue-500 border-2' : 'border'}`}
                  onClick={() => setSelectedProduct('marble')}
                >
                  <div className="font-medium">Beyaz Mermer</div>
                  <div className="text-sm text-gray-500">Mermer - 60x60 cm</div>
                </button>
                <button
                  className={`w-full p-3 text-left rounded ${selectedProduct === 'granite' ? 'bg-blue-50 border-blue-500 border-2' : 'border'}`}
                  onClick={() => setSelectedProduct('granite')}
                >
                  <div className="font-medium">Siyah Granit</div>
                  <div className="text-sm text-gray-500">Granit - 80x80 cm</div>
                </button>
                <button
                  className={`w-full p-3 text-left rounded ${selectedProduct === 'travertine' ? 'bg-blue-50 border-blue-500 border-2' : 'border'}`}
                  onClick={() => setSelectedProduct('travertine')}
                >
                  <div className="font-medium">Traverten</div>
                  <div className="text-sm text-gray-500">Traverten - 40x40 cm</div>
                </button>
              </div>
            </div>

            {/* 3D Kontroller */}
            <div className="bg-white rounded shadow p-4">
              <h3 className="text-lg font-semibold mb-3">3D Kontroller</h3>
              <div className="grid grid-cols-2 gap-2">
                <button
                  className="px-3 py-2 border rounded text-sm hover:bg-gray-50"
                  onClick={() => alert('3D görünüm sıfırlandı')}
                >
                  Sıfırla
                </button>
                <button
                  className="px-3 py-2 border rounded text-sm hover:bg-gray-50"
                  onClick={() => alert('Yakınlaştırıldı')}
                >
                  Yakınlaştır
                </button>
                <button
                  className="px-3 py-2 border rounded text-sm hover:bg-gray-50"
                  onClick={() => alert('Uzaklaştırıldı')}
                >
                  Uzaklaştır
                </button>
                <button
                  className="px-3 py-2 border rounded text-sm hover:bg-gray-50"
                  onClick={() => alert('Hareket modu aktif')}
                >
                  Hareket
                </button>
              </div>
            </div>

          </div>

          {/* Ana 3D Görüntüleyici */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded shadow h-96 relative">
              <div className="h-full bg-gray-100 rounded flex items-center justify-center">

                {/* 3D Viewer Placeholder */}
                <div className="text-center">
                  <div className="w-24 h-24 bg-blue-600 rounded mx-auto mb-4"></div>
                  <h3 className="text-xl font-semibold text-gray-600 mb-2">3D Görüntüleyici</h3>
                  <p className="text-gray-500 mb-4">
                    {selectedProduct ?
                      `${selectedProduct === 'marble' ? 'Beyaz Mermer' :
                        selectedProduct === 'granite' ? 'Siyah Granit' : 'Traverten'} görüntüleniyor` :
                      'Bir ürün seçin'
                    }
                  </p>
                  <p className="text-sm text-gray-400 mb-2">Görünüm Modu: {viewMode === 'room' ? 'Oda' : 'Ürün'}</p>
                  <span className="border text-gray-600 px-3 py-1 rounded text-sm">
                    Three.js entegrasyonu yakında
                  </span>
                </div>

              </div>
            </div>

            {/* Alt Bilgi */}
            <div className="mt-4 text-center text-sm text-gray-500">
              <p>💡 İpucu: Ürünleri seçin ve farklı odalarda nasıl göründüğünü görün</p>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
}