'use client'

import * as React from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Package, 
  Plus, 
  Trash2, 
  Calendar, 
  DollarSign,
  AlertCircle,
  CheckCircle,
  Calculator
} from 'lucide-react'

interface CreateMultiDeliveryModalProps {
  isOpen: boolean
  onClose: () => void
  order: any
  onCreateMultiDelivery: (packages: DeliveryPackageConfig[]) => void
}

interface DeliveryPackageConfig {
  packageNumber: number
  quantity: number
  amount: number
  productionStartDate: string
  productionEndDate: string
  deliveryDate: string
  deliveryMethod: 'factory_pickup' | 'delivery' | 'partial_delivery'
  paymentTerms: {
    advancePercentage: number
    deliveryPercentage: number
    completionPercentage: number
  }
  notes: string
}

export function CreateMultiDeliveryModal({
  isOpen,
  onClose,
  order,
  onCreateMultiDelivery
}: CreateMultiDeliveryModalProps) {
  const [packages, setPackages] = React.useState<DeliveryPackageConfig[]>([])
  const [autoSplit, setAutoSplit] = React.useState(true)
  const [packageCount, setPackageCount] = React.useState(5)
  const [isLoading, setIsLoading] = React.useState(false)

  React.useEffect(() => {
    if (isOpen && order && autoSplit) {
      generateAutoPackages()
    }
  }, [isOpen, order, autoSplit, packageCount])

  const generateAutoPackages = () => {
    if (!order || !order.quantity || !order.totalValue) return

    const quantityPerPackage = Math.ceil(order.quantity / packageCount)
    const amountPerPackage = Math.ceil(order.totalValue / packageCount)
    
    const newPackages: DeliveryPackageConfig[] = []
    let remainingQuantity = order.quantity
    let remainingAmount = order.totalValue

    for (let i = 1; i <= packageCount; i++) {
      const isLastPackage = i === packageCount
      const packageQuantity = isLastPackage ? remainingQuantity : Math.min(quantityPerPackage, remainingQuantity)
      const packageAmount = isLastPackage ? remainingAmount : Math.min(amountPerPackage, remainingAmount)

      // Calculate dates with 2-week intervals
      const startDate = new Date()
      startDate.setDate(startDate.getDate() + (i - 1) * 14)
      
      const endDate = new Date(startDate)
      endDate.setDate(endDate.getDate() + 10)
      
      const deliveryDate = new Date(endDate)
      deliveryDate.setDate(deliveryDate.getDate() + 3)

      newPackages.push({
        packageNumber: i,
        quantity: packageQuantity,
        amount: packageAmount,
        productionStartDate: startDate.toISOString().split('T')[0],
        productionEndDate: endDate.toISOString().split('T')[0],
        deliveryDate: deliveryDate.toISOString().split('T')[0],
        deliveryMethod: 'delivery',
        paymentTerms: {
          advancePercentage: 50,
          deliveryPercentage: 50,
          completionPercentage: 0
        },
        notes: `Paket ${i} - Otomatik oluşturuldu`
      })

      remainingQuantity -= packageQuantity
      remainingAmount -= packageAmount
    }

    setPackages(newPackages)
  }

  const addPackage = () => {
    const newPackage: DeliveryPackageConfig = {
      packageNumber: packages.length + 1,
      quantity: 0,
      amount: 0,
      productionStartDate: '',
      productionEndDate: '',
      deliveryDate: '',
      deliveryMethod: 'delivery',
      paymentTerms: {
        advancePercentage: 50,
        deliveryPercentage: 50,
        completionPercentage: 0
      },
      notes: ''
    }
    setPackages([...packages, newPackage])
  }

  const removePackage = (index: number) => {
    const newPackages = packages.filter((_, i) => i !== index)
    // Renumber packages
    newPackages.forEach((pkg, i) => {
      pkg.packageNumber = i + 1
    })
    setPackages(newPackages)
  }

  const updatePackage = (index: number, field: keyof DeliveryPackageConfig, value: any) => {
    const newPackages = [...packages]
    if (field === 'paymentTerms') {
      newPackages[index].paymentTerms = { ...newPackages[index].paymentTerms, ...value }
    } else {
      (newPackages[index] as any)[field] = value
    }
    setPackages(newPackages)
  }

  const validatePackages = () => {
    if (!order) {
      return {
        isValid: false,
        totalQuantity: 0,
        totalAmount: 0,
        quantityDiff: 0,
        amountDiff: 0
      }
    }

    const totalQuantity = packages.reduce((sum, pkg) => sum + pkg.quantity, 0)
    const totalAmount = packages.reduce((sum, pkg) => sum + pkg.amount, 0)

    return {
      isValid: totalQuantity === order.quantity && totalAmount === order.totalValue,
      totalQuantity,
      totalAmount,
      quantityDiff: totalQuantity - order.quantity,
      amountDiff: totalAmount - order.totalValue
    }
  }

  const handleCreateMultiDelivery = async () => {
    if (!order) {
      alert('Sipariş bilgisi bulunamadı!')
      return
    }

    const validation = validatePackages()
    if (!validation.isValid) {
      alert('Paket toplamları sipariş toplamıyla eşleşmiyor!')
      return
    }

    if (packages.length === 0) {
      alert('En az bir paket oluşturmalısınız!')
      return
    }

    setIsLoading(true)
    try {
      await onCreateMultiDelivery(packages)
      onClose()
    } catch (error) {
      console.error('Error creating multi-delivery:', error)
      alert('Çoklu teslimat oluşturulurken hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const validation = validatePackages()

  if (!order) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[1000px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Çoklu Teslimat Oluştur - Sipariş #{order.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Summary */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-blue-800">Sipariş Özeti</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-blue-700">Müşteri:</span>
                <p className="text-blue-900">{order.customerName}</p>
              </div>
              <div>
                <span className="font-medium text-blue-700">Ürün:</span>
                <p className="text-blue-900">{order.productName}</p>
              </div>
              <div>
                <span className="font-medium text-blue-700">Toplam Miktar:</span>
                <p className="text-blue-900">{order.quantity} m²</p>
              </div>
              <div>
                <span className="font-medium text-blue-700">Toplam Tutar:</span>
                <p className="text-blue-900">${order.totalValue?.toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>

          {/* Auto Split Options */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Calculator className="w-5 h-5" />
                Otomatik Bölme
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={autoSplit}
                    onChange={(e) => setAutoSplit(e.target.checked)}
                    className="rounded"
                  />
                  <span>Otomatik eşit bölme</span>
                </label>
                
                {autoSplit && (
                  <div className="flex items-center gap-2">
                    <Label htmlFor="packageCount">Paket Sayısı:</Label>
                    <Input
                      id="packageCount"
                      type="number"
                      min="2"
                      max="20"
                      value={packageCount}
                      onChange={(e) => setPackageCount(parseInt(e.target.value) || 5)}
                      className="w-20"
                    />
                    <Button
                      size="sm"
                      onClick={generateAutoPackages}
                      variant="outline"
                    >
                      Yeniden Oluştur
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Validation Summary */}
          <Card className={validation.isValid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                {validation.isValid ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-600" />
                )}
                <div className="flex-1">
                  <p className={`font-medium ${validation.isValid ? 'text-green-800' : 'text-red-800'}`}>
                    {validation.isValid ? 'Paket toplamları doğru' : 'Paket toplamları hatalı'}
                  </p>
                  <div className="text-sm mt-1 grid grid-cols-2 gap-4">
                    <div>
                      <span className="font-medium">Miktar:</span> {validation.totalQuantity} m² 
                      {validation.quantityDiff !== 0 && (
                        <span className="text-red-600"> ({validation.quantityDiff > 0 ? '+' : ''}{validation.quantityDiff})</span>
                      )}
                    </div>
                    <div>
                      <span className="font-medium">Tutar:</span> ${validation.totalAmount.toLocaleString()}
                      {validation.amountDiff !== 0 && (
                        <span className="text-red-600"> ({validation.amountDiff > 0 ? '+' : ''}${validation.amountDiff.toLocaleString()})</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Package List */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Teslimat Paketleri ({packages.length})</h3>
              <Button onClick={addPackage} size="sm">
                <Plus className="w-4 h-4 mr-1" />
                Paket Ekle
              </Button>
            </div>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {packages.map((pkg, index) => (
                <Card key={index} className="border-gray-200">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">Paket #{pkg.packageNumber}</CardTitle>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => removePackage(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Basic Info */}
                    <div className="space-y-3">
                      <div>
                        <Label>Miktar (m²)</Label>
                        <Input
                          type="number"
                          value={pkg.quantity}
                          onChange={(e) => updatePackage(index, 'quantity', parseInt(e.target.value) || 0)}
                        />
                      </div>
                      <div>
                        <Label>Tutar ($)</Label>
                        <Input
                          type="number"
                          value={pkg.amount}
                          onChange={(e) => updatePackage(index, 'amount', parseInt(e.target.value) || 0)}
                        />
                      </div>
                    </div>

                    {/* Dates */}
                    <div className="space-y-3">
                      <div>
                        <Label>Üretim Başlangıç</Label>
                        <Input
                          type="date"
                          value={pkg.productionStartDate}
                          onChange={(e) => updatePackage(index, 'productionStartDate', e.target.value)}
                        />
                      </div>
                      <div>
                        <Label>Üretim Bitiş</Label>
                        <Input
                          type="date"
                          value={pkg.productionEndDate}
                          onChange={(e) => updatePackage(index, 'productionEndDate', e.target.value)}
                        />
                      </div>
                      <div>
                        <Label>Teslimat Tarihi</Label>
                        <Input
                          type="date"
                          value={pkg.deliveryDate}
                          onChange={(e) => updatePackage(index, 'deliveryDate', e.target.value)}
                        />
                      </div>
                    </div>

                    {/* Payment & Delivery */}
                    <div className="space-y-3">
                      <div>
                        <Label>Teslimat Yöntemi</Label>
                        <Select
                          value={pkg.deliveryMethod}
                          onValueChange={(value) => updatePackage(index, 'deliveryMethod', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="factory_pickup">Fabrika Teslim</SelectItem>
                            <SelectItem value="delivery">Teslimat</SelectItem>
                            <SelectItem value="partial_delivery">Kısmi Teslimat</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label>Avans %</Label>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            value={pkg.paymentTerms.advancePercentage}
                            onChange={(e) => updatePackage(index, 'paymentTerms', {
                              advancePercentage: parseInt(e.target.value) || 0
                            })}
                          />
                        </div>
                        <div>
                          <Label>Teslimat %</Label>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            value={pkg.paymentTerms.deliveryPercentage}
                            onChange={(e) => updatePackage(index, 'paymentTerms', {
                              deliveryPercentage: parseInt(e.target.value) || 0
                            })}
                          />
                        </div>
                      </div>
                      
                      <div>
                        <Label>Notlar</Label>
                        <Textarea
                          value={pkg.notes}
                          onChange={(e) => updatePackage(index, 'notes', e.target.value)}
                          rows={2}
                          placeholder="Paket notları..."
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              İptal
            </Button>
            <Button
              onClick={handleCreateMultiDelivery}
              disabled={!validation.isValid || packages.length === 0 || isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Oluşturuluyor...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Çoklu Teslimat Oluştur
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
