import React, { useState, useEffect } from 'react';
import { GetServerSideProps } from 'next';
import { getSession } from 'next-auth/react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';
import { useNotifications, Notification } from '../../hooks/useNotifications';
import { Bell, Check, CheckCheck, X, Clock, AlertCircle, Filter, Search } from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { tr } from 'date-fns/locale';

const NotificationsPage: React.FC = () => {
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);

  const {
    notifications,
    unreadCount,
    isLoading,
    mark<PERSON>Read,
    markAllAsRead,
    fetchNotifications
  } = useNotifications();

  // Filter notifications based on current filters
  const filteredNotifications = notifications.filter(notification => {
    // Status filter
    if (filter === 'unread' && notification.status === 'read') return false;
    if (filter === 'read' && notification.status === 'UNREAD') return false;

    // Type filter
    if (typeFilter !== 'all' && notification.type !== typeFilter) return false;

    // Search filter
    if (searchTerm && !notification.message.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !notification.title.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    return true;
  });

  const getNotificationIcon = (type: string, priority: string) => {
    const iconClass = `w-5 h-5 ${
      priority === 'URGENT' ? 'text-red-500' :
      priority === 'HIGH' ? 'text-orange-500' :
      priority === 'MEDIUM' ? 'text-blue-500' :
      'text-gray-500'
    }`;

    switch (type) {
      case 'QUOTE_REQUEST_RECEIVED':
      case 'QUOTE_RECEIVED':
        return <Clock className={iconClass} />;
      case 'ORDER_CREATED':
      case 'ORDER_STATUS_UPDATED':
        return <Check className={iconClass} />;
      case 'PRODUCT_APPROVED':
        return <CheckCheck className={`${iconClass} text-green-500`} />;
      case 'PRODUCT_REJECTED':
        return <X className={`${iconClass} text-red-500`} />;
      case 'SYSTEM_MAINTENANCE':
        return <AlertCircle className={iconClass} />;
      default:
        return <Bell className={iconClass} />;
    }
  };

  const getNotificationTypeText = (type: string) => {
    switch (type) {
      case 'QUOTE_REQUEST_RECEIVED':
        return 'Teklif Talebi';
      case 'QUOTE_RECEIVED':
        return 'Teklif Alındı';
      case 'ORDER_CREATED':
        return 'Yeni Sipariş';
      case 'ORDER_STATUS_UPDATED':
        return 'Sipariş Güncellendi';
      case 'PRODUCT_APPROVED':
        return 'Ürün Onaylandı';
      case 'PRODUCT_REJECTED':
        return 'Ürün Reddedildi';
      case 'SYSTEM_MAINTENANCE':
        return 'Sistem Bakımı';
      default:
        return 'Bildirim';
    }
  };

  const getPriorityBadge = (priority: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    
    switch (priority) {
      case 'URGENT':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'HIGH':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      case 'MEDIUM':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'LOW':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const handleNotificationClick = async (notification: Notification) => {
    if (notification.status === 'UNREAD') {
      await markAsRead(notification.id);
    }
  };

  const loadMore = () => {
    setPage(prev => prev + 1);
    fetchNotifications(page + 1);
  };

  // Get unique notification types for filter
  const notificationTypes = Array.from(new Set(notifications.map(n => n.type)));

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Bildirimler</h1>
            <p className="text-gray-600">
              {unreadCount > 0 ? `${unreadCount} okunmamış bildirim` : 'Tüm bildirimler okundu'}
            </p>
          </div>
          
          {unreadCount > 0 && (
            <button
              onClick={markAllAsRead}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <CheckCheck className="w-4 h-4" />
              <span>Tümünü Okundu İşaretle</span>
            </button>
          )}
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Bildirimlerde ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-4">
              {/* Status Filter */}
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as 'all' | 'unread' | 'read')}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tüm Bildirimler</option>
                <option value="unread">Okunmamış</option>
                <option value="read">Okunmuş</option>
              </select>

              {/* Type Filter */}
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tüm Türler</option>
                {notificationTypes.map(type => (
                  <option key={type} value={type}>
                    {getNotificationTypeText(type)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Notifications List */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          {isLoading && filteredNotifications.length === 0 ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-4 text-gray-500">Bildirimler yükleniyor...</p>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="p-8 text-center">
              <Bell className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Bildirim bulunamadı</h3>
              <p className="text-gray-500">
                {searchTerm || filter !== 'all' || typeFilter !== 'all'
                  ? 'Arama kriterlerinize uygun bildirim bulunamadı.'
                  : 'Henüz bildiriminiz yok.'
                }
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  onClick={() => handleNotificationClick(notification)}
                  className={`p-6 hover:bg-gray-50 cursor-pointer transition-colors ${
                    notification.status === 'UNREAD' ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type, notification.priority)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-sm font-medium text-gray-900">
                            {getNotificationTypeText(notification.type)}
                          </h3>
                          <span className={getPriorityBadge(notification.priority)}>
                            {notification.priority}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <span>{format(new Date(notification.createdAt), 'dd MMM yyyy HH:mm', { locale: tr })}</span>
                          <span>•</span>
                          <span>{formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true, locale: tr })}</span>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 mb-3">
                        {notification.message}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        {notification.status === 'UNREAD' && (
                          <div className="flex items-center">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                            <span className="text-xs text-blue-600 font-medium">Okunmadı</span>
                          </div>
                        )}
                        
                        {notification.relatedEntityType && notification.relatedEntityId && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              // Handle navigation to related entity
                              const { relatedEntityType, relatedEntityId } = notification;
                              switch (relatedEntityType) {
                                case 'QUOTE_REQUEST':
                                  window.location.href = `/dashboard/quotes/requests/${relatedEntityId}`;
                                  break;
                                case 'QUOTE':
                                  window.location.href = `/dashboard/quotes/${relatedEntityId}`;
                                  break;
                                case 'ORDER':
                                  window.location.href = `/dashboard/orders/${relatedEntityId}`;
                                  break;
                                case 'PRODUCT':
                                  window.location.href = `/dashboard/products/${relatedEntityId}`;
                                  break;
                              }
                            }}
                            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                          >
                            Detayları Görüntüle →
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {/* Load More Button */}
          {filteredNotifications.length > 0 && filteredNotifications.length >= 20 && (
            <div className="p-4 border-t border-gray-200 text-center">
              <button
                onClick={loadMore}
                disabled={isLoading}
                className="px-6 py-2 text-blue-600 hover:text-blue-800 font-medium disabled:opacity-50"
              >
                {isLoading ? 'Yükleniyor...' : 'Daha Fazla Yükle'}
              </button>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const session = await getSession(context);

  if (!session) {
    return {
      redirect: {
        destination: '/auth/login',
        permanent: false,
      },
    };
  }

  return {
    props: {
      session,
    },
  };
};

export default NotificationsPage;
