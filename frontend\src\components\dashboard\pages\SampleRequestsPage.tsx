'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSample } from '@/contexts/sample-context';
import {
  SampleRequestCard,
  SampleRequestFilter
} from '@/components/sample-requests';
import {
  getSampleStatusLabel,
  getSampleStatusIcon,
  getSampleStatusColor
} from '@/utils/sample-status-utils';

interface SampleRequestsPageProps {
  onNavigate?: (route: string) => void;
}

const SampleRequestsPage: React.FC<SampleRequestsPageProps> = ({ onNavigate }) => {
  const router = useRouter();
  const { sampleRequests, getSampleRequestsByCustomer, isLoading } = useSample();
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<{ start: Date | null; end: Date | null }>({ start: null, end: null });

  useEffect(() => {
    // Load customer sample requests
    getSampleRequestsByCustomer('1'); // Should be actual customer ID
  }, []);

  // Filter requests based on search term and date range
  const filteredRequests = React.useMemo(() => {
    let filtered = sampleRequests;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(request =>
        request.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.requestedProducts.some((product: any) =>
          product.productName.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Filter by date range
    if (dateRange.start || dateRange.end) {
      filtered = filtered.filter(request => {
        const requestDate = new Date(request.createdAt);
        if (dateRange.start && requestDate < dateRange.start) return false;
        if (dateRange.end && requestDate > dateRange.end) return false;
        return true;
      });
    }

    return filtered;
  }, [sampleRequests, searchTerm, dateRange]);

  // Get status summary
  const statusSummary = React.useMemo(() => {
    const summary = [
      {
        key: 'pending',
        label: 'Bekleyen',
        count: sampleRequests.filter(r => r.status === 'pending').length,
        route: '/customer/requests/samples/pending'
      },
      {
        key: 'payment_required',
        label: 'Ödeme Bekleniyor',
        count: sampleRequests.filter(r => r.status === 'approved_pending_payment' || r.status === 'payment_required').length,
        route: '/customer/requests/samples/payment'
      },
      {
        key: 'approved',
        label: 'Onaylanan',
        count: sampleRequests.filter(r => r.status === 'approved').length,
        route: '/customer/requests/samples/approved'
      },
      {
        key: 'shipped',
        label: 'Gönderilen',
        count: sampleRequests.filter(r => r.status === 'shipped').length,
        route: '/customer/requests/samples/shipped'
      },
      {
        key: 'delivered',
        label: 'Teslim Edilen',
        count: sampleRequests.filter(r => r.status === 'delivered').length,
        route: '/customer/requests/samples/delivered'
      },
      {
        key: 'evaluated',
        label: 'Değerlendirilen',
        count: sampleRequests.filter(r => r.status === 'evaluated').length,
        route: '/customer/requests/samples/evaluated'
      }
    ];
    return summary;
  }, [sampleRequests]);

  const handleNavigate = (route: string) => {
    if (onNavigate) {
      onNavigate(route);
    } else {
      router.push(route);
    }
  };

  const handleViewDetail = (requestId: string) => {
    console.log('View detail for request:', requestId);
  };

  const handlePayment = (requestId: string) => {
    console.log('Handle payment for request:', requestId);
  };

  const handleEvaluate = (requestId: string) => {
    console.log('Handle evaluate for request:', requestId);
  };

  const handleOrder = (requestId: string) => {
    console.log('Handle order for request:', requestId);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Numune talepleri yükleniyor...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Numune Taleplerim</h1>
          <p className="text-gray-600 mt-1">Tüm numune taleplerini görüntüleyin ve yönetin</p>
        </div>
      </div>

      {/* Status Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {statusSummary.map((status) => (
          <div
            key={status.key}
            onClick={() => handleNavigate(status.route)}
            className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-all duration-200 cursor-pointer"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getSampleStatusIcon(status.key)}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{status.label}</h3>
                  <p className="text-sm text-gray-600">Numune talepleri</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">{status.count}</div>
                <div className={`text-xs px-2 py-1 rounded-full ${getSampleStatusColor(status.key)}`}>
                  {getSampleStatusLabel(status.key)}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Filter Component */}
      <SampleRequestFilter
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        dateRange={dateRange}
        onDateRangeChange={setDateRange}
      />

      {/* Recent Sample Requests */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Son Numune Talepleri</h2>
          {filteredRequests.length > 5 && (
            <button
              onClick={() => handleNavigate('/customer/requests/samples/pending')}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Tümünü Görüntüle
            </button>
          )}
        </div>

        {filteredRequests.length > 0 ? (
          <div className="space-y-4">
            {filteredRequests.slice(0, 5).map((request, index) => (
              <SampleRequestCard
                key={request.id}
                request={request}
                onNavigate={handleNavigate}
                onViewDetail={handleViewDetail}
                onPayment={handlePayment}
                onEvaluate={handleEvaluate}
                onOrder={handleOrder}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <div className="text-gray-400 mb-4">
              <svg className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz numune talebiniz yok</h3>
            <p className="text-gray-600 mb-6">Teklif aldığınız ürünlerden numune talep edebilirsiniz.</p>
            <button
              onClick={() => handleNavigate('/customer/requests/active')}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Aktif Taleplerime Git
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default SampleRequestsPage;
