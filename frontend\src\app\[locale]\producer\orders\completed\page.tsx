'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { InvoiceDownloadModal } from '@/components/ui/invoice-download-modal'
import { CustomerContactModal } from '@/components/ui/customer-contact-modal'
import { OrderDetailsModal } from '@/components/ui/order-details-modal'
import { ReviewRequestModal } from '@/components/ui/review-request-modal'
import {
  CheckCircle,
  User,
  Calendar,
  Package,
  DollarSign,
  Star,
  MessageSquare,
  Eye,

  Download,
  TrendingUp,
  Search,
  Filter,
  Award,
  ThumbsUp,
  FileText
} from 'lucide-react'

// Mock data for completed orders
const mockCompletedOrders = [
  {
    id: 'ORD-COMP-001',
    customerId: '1',
    customerName: 'Ahmet Yılmaz',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Traverten Klasik',
    quantity: 150,
    unit: 'm²',
    orderDate: '2024-11-15',
    completedDate: '2024-12-20',
    deliveredDate: '2024-12-22',
    totalValue: 7500,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 7500,
    customerRating: 5,
    customerReview: 'Mükemmel kalite ve zamanında teslimat. Kesinlikle tavsiye ederim!',
    invoiceNumber: 'INV-2024-001',
    deliveryMethod: 'Adrese Teslimat',
    notes: 'Müşteri çok memnun kaldı, benzer projeler için teklif verilebilir',
    projectType: 'Villa Projesi'
  },
  {
    id: 'ORD-COMP-002',
    customerId: '2',
    customerName: 'Fatma Demir',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Mermer Beyaz Carrara',
    quantity: 80,
    unit: 'm²',
    orderDate: '2024-11-20',
    completedDate: '2024-12-18',
    deliveredDate: '2024-12-19',
    totalValue: 9600,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 9600,
    customerRating: 4,
    customerReview: 'Kalite çok iyi, sadece teslimat biraz gecikti.',
    invoiceNumber: 'INV-2024-002',
    deliveryMethod: 'Fabrika Teslim',
    notes: 'Küçük teslimat gecikmesi yaşandı, müşteri anlayışlı',
    projectType: 'Mutfak Renovasyonu'
  },
  {
    id: 'ORD-COMP-003',
    customerId: '3',
    customerName: 'Mehmet Özkan',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Granit Siyah',
    quantity: 200,
    unit: 'm²',
    orderDate: '2024-10-10',
    completedDate: '2024-12-15',
    deliveredDate: '2024-12-16',
    totalValue: 16000,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 16000,
    customerRating: 5,
    customerReview: 'Profesyonel hizmet, kaliteli ürün. Çok teşekkürler!',
    invoiceNumber: 'INV-2024-003',
    deliveryMethod: 'Şantiye Teslim',
    notes: 'Büyük proje başarıyla tamamlandı, referans müşteri',
    projectType: 'Otel Projesi'
  },
  {
    id: 'ORD-COMP-004',
    customerId: '4',
    customerName: 'Ayşe Kaya',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Oniks Honey',
    quantity: 45,
    unit: 'm²',
    orderDate: '2024-11-01',
    completedDate: '2024-12-10',
    deliveredDate: '2024-12-12',
    totalValue: 6750,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 6750,
    customerRating: null,
    customerReview: null,
    invoiceNumber: 'INV-2024-004',
    deliveryMethod: 'Adrese Teslimat',
    notes: 'Müşteri değerlendirmesi bekleniyor',
    canReorder: true,
    projectType: 'Banyo Renovasyonu'
  }
]

export default function CompletedOrdersPage() {
  const { producer } = useProducerAuth()
  const [orders, setOrders] = React.useState(mockCompletedOrders)
  const [searchTerm, setSearchTerm] = React.useState('')
  const [sortBy, setSortBy] = React.useState('completedDate')
  const [ratingFilter, setRatingFilter] = React.useState('all')
  const [selectedOrder, setSelectedOrder] = React.useState<any>(null)

  const [isInvoiceModalOpen, setIsInvoiceModalOpen] = React.useState(false)
  const [isContactModalOpen, setIsContactModalOpen] = React.useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = React.useState(false)
  const [isReviewRequestModalOpen, setIsReviewRequestModalOpen] = React.useState(false)

  const filteredOrders = React.useMemo(() => {
    let filtered = orders.filter(order => {
      const matchesSearch = 
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.productName.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesRating = ratingFilter === 'all' || 
        (ratingFilter === 'rated' && order.customerRating) ||
        (ratingFilter === 'unrated' && !order.customerRating) ||
        (ratingFilter === '5' && order.customerRating === 5) ||
        (ratingFilter === '4' && order.customerRating === 4)
      
      return matchesSearch && matchesRating
    })

    // Sort orders
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'completedDate':
          return new Date(b.completedDate).getTime() - new Date(a.completedDate).getTime()
        case 'customerName':
          return a.customerName.localeCompare(b.customerName)
        case 'totalValue':
          return b.totalValue - a.totalValue
        case 'rating':
          return (b.customerRating || 0) - (a.customerRating || 0)
        default:
          return 0
      }
    })

    return filtered
  }, [orders, searchTerm, sortBy, ratingFilter])

  const handleViewDetails = (order: any) => {
    setSelectedOrder(order)
    setIsDetailsModalOpen(true)
  }

  const handleSendMessage = (order: any) => {
    setSelectedOrder(order)
    setIsContactModalOpen(true)
  }



  const handleDownloadInvoice = (order: any) => {
    setSelectedOrder(order)
    setIsInvoiceModalOpen(true)
  }

  const handleRequestReview = (order: any) => {
    if (order.customerRating) {
      alert('Bu sipariş zaten değerlendirilmiş.')
      return
    }

    setSelectedOrder(order)
    setIsReviewRequestModalOpen(true)
  }

  // Modal handlers

  const handleInvoiceDownload = async (format: string) => {
    try {
      console.log('Downloading invoice in format:', format)
      // Simulate download
      setTimeout(() => {
        const link = document.createElement('a')
        link.href = '#'
        link.download = `fatura-${selectedOrder?.invoiceNumber}.${format}`
        link.click()
      }, 1000)
    } catch (error) {
      console.error('Error downloading invoice:', error)
      throw error
    }
  }

  const handleInvoiceEmail = async () => {
    try {
      console.log('Sending invoice email to:', selectedOrder?.customerEmail)
      // Simulate email sending
      await new Promise(resolve => setTimeout(resolve, 1000))
    } catch (error) {
      console.error('Error sending invoice email:', error)
      throw error
    }
  }

  const handleInvoicePrint = async () => {
    try {
      console.log('Printing invoice:', selectedOrder?.invoiceNumber)
      // Simulate printing
      window.print()
    } catch (error) {
      console.error('Error printing invoice:', error)
      throw error
    }
  }

  const handleSendCustomerMessage = async (messageData: any) => {
    try {
      console.log('Sending message to customer:', messageData)
      alert('Mesaj başarıyla gönderildi!')
      return true
    } catch (error) {
      console.error('Error sending message:', error)
      alert('Mesaj gönderilirken hata oluştu.')
      return false
    }
  }

  const handleSendReviewRequest = async (requestData: any) => {
    try {
      console.log('Sending review request:', requestData)
      alert('Değerlendirme talebi başarıyla gönderildi!')
      return true
    } catch (error) {
      console.error('Error sending review request:', error)
      alert('Değerlendirme talebi gönderilirken hata oluştu.')
      return false
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  const getDaysAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getAverageRating = () => {
    const ratedOrders = orders.filter(o => o.customerRating)
    if (ratedOrders.length === 0) return 0
    return (ratedOrders.reduce((sum, o) => sum + o.customerRating, 0) / ratedOrders.length).toFixed(1)
  }

  const getTotalRevenue = () => {
    return orders.reduce((sum, o) => sum + o.totalValue, 0)
  }

  const renderStars = (rating: number | null) => {
    if (!rating) return <span className="text-gray-400">Değerlendirilmedi</span>
    
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm font-medium">{rating}/5</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Tamamlanan Siparişler</h1>
        <p className="text-gray-600">
          Başarıyla teslim edilen siparişler ve müşteri değerlendirmeleri
        </p>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Sipariş ID, müşteri adı veya ürün ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="w-full md:w-48">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Sırala" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="completedDate">Tamamlanma Tarihi</SelectItem>
                  <SelectItem value="customerName">Müşteri Adı</SelectItem>
                  <SelectItem value="totalValue">Sipariş Tutarı</SelectItem>
                  <SelectItem value="rating">Müşteri Puanı</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="w-full md:w-48">
              <Select value={ratingFilter} onValueChange={setRatingFilter}>
                <SelectTrigger>
                  <Star className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Puan filtrele" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Siparişler</SelectItem>
                  <SelectItem value="rated">Puanlanmış</SelectItem>
                  <SelectItem value="unrated">Puanlanmamış</SelectItem>
                  <SelectItem value="5">5 Yıldız</SelectItem>
                  <SelectItem value="4">4+ Yıldız</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Toplam Tamamlanan</p>
                <p className="text-2xl font-bold text-green-900">{orders.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Ortalama Puan</p>
                <p className="text-2xl font-bold text-yellow-900">{getAverageRating()}/5</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <DollarSign className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Toplam Gelir</p>
                <p className="text-2xl font-bold text-blue-900">${getTotalRevenue().toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <ThumbsUp className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Memnuniyet</p>
                <p className="text-2xl font-bold text-purple-900">
                  {Math.round((orders.filter(o => o.customerRating && o.customerRating >= 4).length / orders.filter(o => o.customerRating).length) * 100) || 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Completed Orders List */}
      <div className="space-y-4">
        {filteredOrders.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <CheckCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm || ratingFilter !== 'all' ? 'Arama kriterlerine uygun sipariş bulunamadı' : 'Tamamlanan sipariş bulunmuyor'}
              </h3>
              <p className="text-gray-600">
                {searchTerm || ratingFilter !== 'all' ? 'Farklı arama terimleri deneyin' : 'Henüz tamamlanmış siparişiniz bulunmuyor'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredOrders.map((order) => (
            <Card key={order.id} className="overflow-hidden border-green-200 bg-green-50">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center gap-6">
                  {/* Order Info */}
                  <div className="flex-1 space-y-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          Sipariş #{order.id}
                        </h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className="bg-green-100 text-green-800">
                            Tamamlandı
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {getDaysAgo(order.completedDate)} gün önce
                          </span>
                          {order.customerRating && (
                            <Badge className="bg-yellow-100 text-yellow-800">
                              <Star className="w-3 h-3 mr-1" />
                              {order.customerRating}/5
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">{order.customerName}</div>
                          <div className="text-gray-500">{order.projectType}</div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Package className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">{order.productName}</div>
                          <div className="text-gray-500">{order.quantity} {order.unit}</div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">
                            {order.totalValue.toLocaleString()} {order.currency}
                          </div>
                          <div className="text-gray-500">
                            Fatura: {order.invoiceNumber}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">
                            {formatDate(order.deliveredDate)}
                          </div>
                          <div className="text-gray-500">Teslim tarihi</div>
                        </div>
                      </div>
                    </div>

                    {/* Customer Rating and Review */}
                    <div className="bg-white p-4 rounded-lg border">
                      <div className="flex items-center gap-2 mb-2">
                        <Award className="w-5 h-5 text-yellow-600" />
                        <span className="font-medium text-gray-800">Müşteri Değerlendirmesi</span>
                      </div>
                      
                      {order.customerRating ? (
                        <div className="space-y-2">
                          {renderStars(order.customerRating)}
                          {order.customerReview && (
                            <p className="text-gray-700 italic">"{order.customerReview}"</p>
                          )}
                        </div>
                      ) : (
                        <div className="flex items-center justify-between">
                          <span className="text-gray-500">Henüz değerlendirilmedi</span>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRequestReview(order)}
                            className="text-blue-600 hover:text-blue-700"
                          >
                            Değerlendirme İste
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Notes */}
                    {order.notes && (
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Notlar:</span>
                        <p className="text-gray-600 mt-1">{order.notes}</p>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col gap-2 lg:ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownloadInvoice(order)}
                    >
                      <Download className="w-4 h-4 mr-1" />
                      Fatura İndir
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSendMessage(order)}
                    >
                      <MessageSquare className="w-4 h-4 mr-1" />
                      Müşteriyle İletişim
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewDetails(order)}
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      Detayları Gör
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Success Tips */}
      <Card className="bg-green-50 border-green-200">
        <CardHeader>
          <CardTitle className="text-green-800">🎉 Başarılı Siparişler İçin İpuçları</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-700">
            <div>
              <h4 className="font-medium mb-2">Müşteri Memnuniyeti İçin:</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>Zamanında teslimat yapın</li>
                <li>Kalite kontrolünü ihmal etmeyin</li>
                <li>Müşteri ile düzenli iletişim kurun</li>
                <li>Satış sonrası destek sağlayın</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Tekrar Satış İçin:</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>Müşteri değerlendirmelerini takip edin</li>
                <li>Yeni ürünler hakkında bilgi verin</li>
                <li>Özel indirimler sunun</li>
                <li>Referans müşteri olarak değerlendirin</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Development Info */}
      <div className="bg-green-50 p-4 rounded-lg border border-green-200">
        <h3 className="font-semibold text-green-800 mb-2">✅ Tamamlanan Siparişler</h3>
        <p className="text-sm text-green-700">
          Bu sayfa başarıyla teslim edilen siparişlerin yönetimi ve müşteri ilişkileri için oluşturulmuştur.
        </p>
        <div className="mt-3 text-xs text-green-600">
          <p><strong>Özellikler:</strong></p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Müşteri değerlendirmeleri ve puanları</li>
            <li>Tekrar sipariş alma sistemi</li>
            <li>Fatura indirme ve mali takip</li>
            <li>Müşteri iletişim sistemi</li>
            <li>Başarı istatistikleri</li>
          </ul>
        </div>
      </div>

      {/* Modals */}
      {isInvoiceModalOpen && selectedOrder && (
        <InvoiceDownloadModal
          isOpen={isInvoiceModalOpen}
          onClose={() => {
            setIsInvoiceModalOpen(false)
            setSelectedOrder(null)
          }}
          order={selectedOrder}
          onDownload={handleInvoiceDownload}
          onSendEmail={handleInvoiceEmail}
          onPrint={handleInvoicePrint}
        />
      )}

      {isContactModalOpen && selectedOrder && (
        <CustomerContactModal
          isOpen={isContactModalOpen}
          onClose={() => {
            setIsContactModalOpen(false)
            setSelectedOrder(null)
          }}
          request={selectedOrder}
          onSendMessage={handleSendCustomerMessage}
        />
      )}

      {isDetailsModalOpen && selectedOrder && (
        <OrderDetailsModal
          isOpen={isDetailsModalOpen}
          onClose={() => {
            setIsDetailsModalOpen(false)
            setSelectedOrder(null)
          }}
          order={selectedOrder}
        />
      )}

      {isReviewRequestModalOpen && selectedOrder && (
        <ReviewRequestModal
          isOpen={isReviewRequestModalOpen}
          onClose={() => {
            setIsReviewRequestModalOpen(false)
            setSelectedOrder(null)
          }}
          order={selectedOrder}
          onSendRequest={handleSendReviewRequest}
        />
      )}
    </div>
  )
}
