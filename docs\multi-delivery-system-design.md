# Çoklu Teslimat Sistemi Tasarımı

## 1. Sistem Genel Bakış

### Problem
- Büyük metrajlı siparişler tek seferde teslim edilemez
- Örnek: 1000m² sipariş → 10 farklı yükleme ile teslimat
- Her yükleme için ayrı üretim takvimi gerekli
- Her yükleme için ayrı ödeme sistemi gerekli

### Çözüm
Metraj bazlı çoklu teslimat sistemi ile:
- Siparişi alt teslimat paketlerine böl
- Her paket için ayrı üretim takvimi
- Her paket için ayrı teslimat takvimi
- Her paket için ayrı ödeme takibi

## 2. Veritabanı Şeması

### 2.1 Orders Tablosu (Güncellenmiş)
```sql
CREATE TABLE orders (
  id VARCHAR(50) PRIMARY KEY,
  customer_id VARCHAR(50) NOT NULL,
  producer_id VARCHAR(50) NOT NULL,
  product_id VARCHAR(50) NOT NULL,
  
  -- Toplam sipariş bilgileri
  total_quantity DECIMAL(10,2) NOT NULL,
  total_amount DECIMAL(12,2) NOT NULL,
  
  -- Teslimat tipi
  delivery_type ENUM('single', 'multiple') DEFAULT 'single',
  
  -- Genel sipariş durumu
  status ENUM('pending', 'confirmed', 'in_production', 'partially_delivered', 'completed', 'cancelled') DEFAULT 'pending',
  
  -- Tarihler
  order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  estimated_completion_date DATE,
  
  -- Diğer alanlar
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2.2 Delivery_Packages Tablosu (Yeni)
```sql
CREATE TABLE delivery_packages (
  id VARCHAR(50) PRIMARY KEY,
  order_id VARCHAR(50) NOT NULL,
  package_number INT NOT NULL, -- 1, 2, 3, ...
  
  -- Paket detayları
  quantity DECIMAL(10,2) NOT NULL,
  amount DECIMAL(12,2) NOT NULL,
  
  -- Durumlar
  production_status ENUM('pending', 'in_progress', 'completed', 'paused') DEFAULT 'pending',
  delivery_status ENUM('pending', 'ready', 'shipped', 'delivered') DEFAULT 'pending',
  payment_status ENUM('pending', 'paid', 'overdue') DEFAULT 'pending',
  
  -- Tarihler
  production_start_date DATE,
  production_end_date DATE,
  delivery_date DATE,
  actual_delivery_date DATE,
  
  -- Notlar
  production_notes TEXT,
  delivery_notes TEXT,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
  UNIQUE KEY unique_package (order_id, package_number)
);
```

### 2.3 Production_Schedules Tablosu (Yeni)
```sql
CREATE TABLE production_schedules (
  id VARCHAR(50) PRIMARY KEY,
  delivery_package_id VARCHAR(50) NOT NULL,
  
  -- Üretim aşamaları
  stage_name VARCHAR(100) NOT NULL,
  stage_order INT NOT NULL,
  
  -- Durumlar
  status ENUM('pending', 'in_progress', 'completed', 'paused') DEFAULT 'pending',
  
  -- Tarihler
  planned_start_date DATE,
  planned_end_date DATE,
  actual_start_date DATE,
  actual_end_date DATE,
  
  -- Detaylar
  assigned_worker VARCHAR(100),
  notes TEXT,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (delivery_package_id) REFERENCES delivery_packages(id) ON DELETE CASCADE
);
```

### 2.4 Delivery_Schedules Tablosu (Yeni)
```sql
CREATE TABLE delivery_schedules (
  id VARCHAR(50) PRIMARY KEY,
  delivery_package_id VARCHAR(50) NOT NULL,
  
  -- Teslimat detayları
  delivery_method ENUM('factory_pickup', 'delivery', 'partial_delivery') NOT NULL,
  delivery_address TEXT,
  
  -- Tarihler
  scheduled_date DATE NOT NULL,
  actual_date DATE,
  
  -- Kargo bilgileri
  tracking_number VARCHAR(100),
  carrier_company VARCHAR(100),
  
  -- Durumlar
  status ENUM('scheduled', 'in_transit', 'delivered', 'failed') DEFAULT 'scheduled',
  
  -- Notlar
  notes TEXT,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (delivery_package_id) REFERENCES delivery_packages(id) ON DELETE CASCADE
);
```

### 2.5 Package_Payments Tablosu (Yeni)
```sql
CREATE TABLE package_payments (
  id VARCHAR(50) PRIMARY KEY,
  delivery_package_id VARCHAR(50) NOT NULL,
  
  -- Ödeme detayları
  amount DECIMAL(12,2) NOT NULL,
  payment_type ENUM('advance', 'delivery', 'completion') NOT NULL,
  
  -- Durumlar
  status ENUM('pending', 'paid', 'overdue', 'cancelled') DEFAULT 'pending',
  
  -- Tarihler
  due_date DATE NOT NULL,
  paid_date DATE,
  
  -- Ödeme bilgileri
  payment_method VARCHAR(50),
  transaction_id VARCHAR(100),
  
  -- Notlar
  notes TEXT,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (delivery_package_id) REFERENCES delivery_packages(id) ON DELETE CASCADE
);
```

## 3. İş Akışı (Workflow)

### 3.1 Sipariş Oluşturma
1. Müşteri büyük metrajlı sipariş verir
2. Sistem otomatik olarak teslimat paketlerine böler
3. Her paket için üretim ve teslimat takvimi oluşturulur
4. Her paket için ödeme planı belirlenir

### 3.2 Üretim Süreci
1. İlk paket üretimi başlar
2. Her aşama için ayrı takip
3. Paket tamamlandığında teslimat hazırlığı
4. Sonraki paket üretimi başlar

### 3.3 Teslimat Süreci
1. Paket hazır olduğunda teslimat planlanır
2. Müşteri bilgilendirilir
3. Teslimat gerçekleştirilir
4. Teslimat onayı alınır

### 3.4 Ödeme Süreci
1. Her paket için ayrı fatura
2. Teslimat öncesi ödeme kontrolü
3. Ödeme alındıktan sonra teslimat
4. Geciken ödemeler için hatırlatma

## 4. API Endpoints

### 4.1 Sipariş Yönetimi
- `POST /api/orders` - Yeni sipariş oluştur
- `POST /api/orders/{id}/split` - Siparişi paketlere böl
- `GET /api/orders/{id}/packages` - Sipariş paketlerini listele

### 4.2 Üretim Yönetimi
- `GET /api/packages/{id}/production` - Üretim takvimini getir
- `PUT /api/packages/{id}/production/stage` - Üretim aşamasını güncelle
- `POST /api/packages/{id}/production/pause` - Üretimi duraklat

### 4.3 Teslimat Yönetimi
- `GET /api/packages/{id}/delivery` - Teslimat bilgilerini getir
- `PUT /api/packages/{id}/delivery/schedule` - Teslimat tarihini güncelle
- `POST /api/packages/{id}/delivery/complete` - Teslimatı tamamla

### 4.4 Ödeme Yönetimi
- `GET /api/packages/{id}/payments` - Ödeme planını getir
- `POST /api/packages/{id}/payments` - Ödeme kaydet
- `PUT /api/packages/{id}/payments/{paymentId}` - Ödeme durumunu güncelle

## 5. Frontend Bileşenleri

### 5.1 Sipariş Detay Sayfası
- Genel sipariş bilgileri
- Paket listesi ve durumları
- Genel ilerleme çubuğu

### 5.2 Paket Yönetim Sayfası
- Her paket için ayrı kart
- Üretim durumu
- Teslimat durumu
- Ödeme durumu

### 5.3 Üretim Takvimi
- Gantt chart görünümü
- Aşama bazlı takip
- Kaynak planlaması

### 5.4 Teslimat Takvimi
- Takvim görünümü
- Teslimat rotaları
- Kargo takibi

## 6. Bildirim Sistemi

### 6.1 Müşteri Bildirimleri
- Üretim başlangıcı
- Paket hazır
- Teslimat tarihi
- Ödeme hatırlatması

### 6.2 Üretici Bildirimleri
- Yeni sipariş
- Üretim aşaması tamamlandı
- Teslimat zamanı
- Ödeme alındı

## 7. Raporlama

### 7.1 Üretici Raporları
- Paket bazlı ilerleme
- Üretim verimliliği
- Teslimat performansı
- Ödeme takibi

### 7.2 Müşteri Raporları
- Sipariş durumu
- Teslimat takvimi
- Ödeme planı
- Genel ilerleme

Bu tasarım, büyük metrajlı siparişlerin etkin bir şekilde yönetilmesini sağlar ve her paket için ayrı takip imkanı sunar.
