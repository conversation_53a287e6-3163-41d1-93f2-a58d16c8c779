'use client'

import * as React from 'react'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  Star, 
  Send, 
  User,
  Package,
  Calendar,
  Mail,
  MessageSquare,
  CheckCircle,
  Gift,
  Award
} from 'lucide-react'

interface ReviewRequestModalProps {
  isOpen: boolean
  onClose: () => void
  order: any
  onSendRequest: (requestData: any) => Promise<boolean>
}

export function ReviewRequestModal({
  isOpen,
  onClose,
  order,
  onSendRequest
}: ReviewRequestModalProps) {
  const [requestData, setRequestData] = React.useState({
    emailTemplate: 'standard',
    personalMessage: '',
    incentive: 'none',
    reminderDate: '',
    includeOrderDetails: true
  })
  const [isLoading, setIsLoading] = React.useState(false)
  const [step, setStep] = React.useState(1) // 1: Template Selection, 2: Customization, 3: Confirmation

  React.useEffect(() => {
    if (isOpen && order) {
      // Reset form
      setRequestData({
        emailTemplate: 'standard',
        personalMessage: '',
        incentive: 'none',
        reminderDate: '',
        includeOrderDetails: true
      })
      setStep(1)
    }
  }, [isOpen, order])

  const handleSendRequest = async () => {
    setIsLoading(true)
    try {
      const success = await onSendRequest({
        ...requestData,
        orderId: order.id,
        customerId: order.customerId,
        customerName: order.customerName,
        customerEmail: order.customerEmail,
        productName: order.productName
      })
      
      if (success) {
        setStep(3)
        setTimeout(() => {
          onClose()
        }, 2000)
      }
    } catch (error) {
      console.error('Error sending review request:', error)
      alert('Değerlendirme talebi gönderilirken hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const getEmailTemplates = () => {
    return [
      {
        id: 'standard',
        name: 'Standart Şablon',
        description: 'Genel değerlendirme talebi',
        preview: `Merhaba ${order?.customerName},\n\n${order?.productName} siparişiniz başarıyla tamamlandı. Deneyiminizi değerlendirmenizi rica ederiz.`
      },
      {
        id: 'friendly',
        name: 'Samimi Şablon',
        description: 'Daha kişisel ve samimi ton',
        preview: `Sevgili ${order?.customerName},\n\nSiparişinizden memnun kaldığınızı umuyoruz! Görüşlerinizi bizimle paylaşır mısınız?`
      },
      {
        id: 'professional',
        name: 'Profesyonel Şablon',
        description: 'Kurumsal ve resmi ton',
        preview: `Sayın ${order?.customerName},\n\nHizmet kalitemizi artırmak için değerlendirmenizi bekliyoruz.`
      }
    ]
  }

  const getIncentiveOptions = () => {
    return [
      { id: 'none', name: 'Teşvik Yok', description: 'Sadece değerlendirme talebi' },
      { id: 'discount', name: '%5 İndirim', description: 'Sonraki sipariş için %5 indirim' },
      { id: 'gift', name: 'Hediye Ürün', description: 'Küçük hediye ürün gönderimi' },
      { id: 'priority', name: 'Öncelikli Hizmet', description: 'Gelecek siparişlerde öncelik' }
    ]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  if (!order) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Star className="w-5 h-5" />
            Değerlendirme Talebi Gönder - #{order.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step 1: Template Selection */}
          {step === 1 && (
            <>
              {/* Order Summary */}
              <Card className="bg-blue-50 border-blue-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg text-blue-800">Sipariş Bilgileri</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-blue-700">Müşteri:</span>
                    <p className="text-blue-900">{order.customerName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">Ürün:</span>
                    <p className="text-blue-900">{order.productName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">Teslim Tarihi:</span>
                    <p className="text-blue-900">{formatDate(order.deliveredDate)}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">E-posta:</span>
                    <p className="text-blue-900">{order.customerEmail}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Template Selection */}
              <Card>
                <CardHeader>
                  <CardTitle>E-posta Şablonu Seçin</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {getEmailTemplates().map((template) => (
                    <div
                      key={template.id}
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        requestData.emailTemplate === template.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setRequestData(prev => ({ ...prev, emailTemplate: template.id }))}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{template.name}</h4>
                        <input
                          type="radio"
                          checked={requestData.emailTemplate === template.id}
                          onChange={() => setRequestData(prev => ({ ...prev, emailTemplate: template.id }))}
                          className="text-blue-600"
                        />
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                      <div className="bg-gray-50 p-3 rounded text-sm">
                        <strong>Önizleme:</strong>
                        <p className="mt-1 whitespace-pre-line">{template.preview}</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={onClose}>
                  İptal
                </Button>
                <Button onClick={() => setStep(2)}>
                  Devam Et
                </Button>
              </div>
            </>
          )}

          {/* Step 2: Customization */}
          {step === 2 && (
            <>
              {/* Personal Message */}
              <Card>
                <CardHeader>
                  <CardTitle>Kişisel Mesaj Ekle</CardTitle>
                </CardHeader>
                <CardContent>
                  <Label htmlFor="personalMessage">Ek Mesaj (İsteğe Bağlı)</Label>
                  <Textarea
                    id="personalMessage"
                    value={requestData.personalMessage}
                    onChange={(e) => setRequestData(prev => ({ ...prev, personalMessage: e.target.value }))}
                    rows={4}
                    placeholder="Müşteriye özel bir mesaj eklemek istiyorsanız buraya yazın..."
                    className="mt-1"
                  />
                </CardContent>
              </Card>

              {/* Incentive Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Gift className="w-5 h-5" />
                    Teşvik Seçeneği
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {getIncentiveOptions().map((incentive) => (
                      <div
                        key={incentive.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-all ${
                          requestData.incentive === incentive.id
                            ? 'border-green-500 bg-green-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setRequestData(prev => ({ ...prev, incentive: incentive.id }))}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">{incentive.name}</h4>
                            <p className="text-sm text-gray-600">{incentive.description}</p>
                          </div>
                          <input
                            type="radio"
                            checked={requestData.incentive === incentive.id}
                            onChange={() => setRequestData(prev => ({ ...prev, incentive: incentive.id }))}
                            className="text-green-600"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Reminder Settings */}
              <Card>
                <CardHeader>
                  <CardTitle>Hatırlatma Ayarları</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="reminderDate">Hatırlatma Tarihi (İsteğe Bağlı)</Label>
                    <input
                      id="reminderDate"
                      type="date"
                      value={requestData.reminderDate}
                      onChange={(e) => setRequestData(prev => ({ ...prev, reminderDate: e.target.value }))}
                      className="mt-1 w-full p-2 border rounded-md"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Müşteri değerlendirme yapmazsa bu tarihte hatırlatma gönderilir
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="includeOrderDetails"
                      checked={requestData.includeOrderDetails}
                      onChange={(e) => setRequestData(prev => ({ ...prev, includeOrderDetails: e.target.checked }))}
                      className="text-blue-600"
                    />
                    <Label htmlFor="includeOrderDetails">Sipariş detaylarını e-postaya dahil et</Label>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>
                  Geri Dön
                </Button>
                <Button 
                  onClick={handleSendRequest}
                  disabled={isLoading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Gönderiliyor...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      Değerlendirme Talebi Gönder
                    </>
                  )}
                </Button>
              </div>
            </>
          )}

          {/* Step 3: Confirmation */}
          {step === 3 && (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-green-800 mb-2">
                Değerlendirme Talebi Gönderildi!
              </h3>
              <p className="text-green-600 mb-4">
                {order.customerName} müşterisine değerlendirme talebi e-postası gönderildi.
              </p>
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <div className="text-sm text-green-700">
                  <p><strong>E-posta:</strong> {order.customerEmail}</p>
                  <p><strong>Şablon:</strong> {getEmailTemplates().find(t => t.id === requestData.emailTemplate)?.name}</p>
                  {requestData.incentive !== 'none' && (
                    <p><strong>Teşvik:</strong> {getIncentiveOptions().find(i => i.id === requestData.incentive)?.name}</p>
                  )}
                  {requestData.reminderDate && (
                    <p><strong>Hatırlatma:</strong> {formatDate(requestData.reminderDate)}</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
