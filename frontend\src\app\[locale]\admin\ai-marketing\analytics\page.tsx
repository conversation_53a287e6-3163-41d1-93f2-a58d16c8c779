'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  BarChart3,
  TrendingUp,
  Mail,
  Share2,
  Users,
  Target,
  DollarSign,
  Download,
  RefreshCw,
  Eye,
  MousePointer,
  MessageSquare,
  Activity,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface AnalyticsData {
  overview: {
    totalROI: number;
    totalReach: number;
    totalEngagement: number;
    totalConversions: number;
    performanceScore: number;
  };
  emailMarketing: {
    campaignsSent: number;
    totalSent: number;
    deliveryRate: number;
    openRate: number;
    clickRate: number;
    conversionRate: number;
    unsubscribeRate: number;
    bounceRate: number;
  };
  socialMedia: {
    totalPosts: number;
    totalReach: number;
    totalEngagement: number;
    avgEngagementRate: number;
    platforms: {
      facebook: { posts: number; reach: number; engagement: number };
      instagram: { posts: number; reach: number; engagement: number };
      linkedin: { posts: number; reach: number; engagement: number };
      twitter: { posts: number; reach: number; engagement: number };
    };
  };
  customerAcquisition: {
    prospectsFound: number;
    contactsInitiated: number;
    responseRate: number;
    qualifiedLeads: number;
    conversions: number;
    avgLeadScore: number;
  };
  adManagement: {
    totalSpend: number;
    totalImpressions: number;
    totalClicks: number;
    avgCPC: number;
    avgCTR: number;
    conversions: number;
    roas: number;
  };
  aiSystemPerformance: {
    totalTasks: number;
    successRate: number;
    averageExecutionTime: number;
    errorCount: number;
    aiModelPerformance: {
      [key: string]: {
        totalTasks: number;
        successRate: number;
        averageExecutionTime: number;
        lastExecution: number;
      };
    };
  };
}

export default function AIMarketingAnalyticsPage() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30d');

  useEffect(() => {
    fetchAnalyticsData();
  }, [dateRange]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      // Mock analytics data
      const mockData: AnalyticsData = {
        overview: {
          totalROI: 3.2,
          totalReach: 125000,
          totalEngagement: 8500,
          totalConversions: 145,
          performanceScore: 87
        },
        emailMarketing: {
          campaignsSent: 24,
          totalSent: 15420,
          deliveryRate: 97.8,
          openRate: 24.5,
          clickRate: 3.2,
          conversionRate: 1.8,
          unsubscribeRate: 0.3,
          bounceRate: 2.2
        },
        socialMedia: {
          totalPosts: 156,
          totalReach: 89000,
          totalEngagement: 5200,
          avgEngagementRate: 5.8,
          platforms: {
            facebook: { posts: 45, reach: 25000, engagement: 1200 },
            instagram: { posts: 38, reach: 22000, engagement: 1800 },
            linkedin: { posts: 42, reach: 28000, engagement: 1400 },
            twitter: { posts: 31, reach: 14000, engagement: 800 }
          }
        },
        customerAcquisition: {
          prospectsFound: 342,
          contactsInitiated: 89,
          responseRate: 12.4,
          qualifiedLeads: 23,
          conversions: 8,
          avgLeadScore: 78.5
        },
        adManagement: {
          totalSpend: 8500,
          totalImpressions: 450000,
          totalClicks: 2800,
          avgCPC: 3.04,
          avgCTR: 0.62,
          conversions: 45,
          roas: 4.2
        },
        aiSystemPerformance: {
          totalTasks: 1247,
          successRate: 94.2,
          averageExecutionTime: 2.3,
          errorCount: 23,
          aiModelPerformance: {
            'OpenAI GPT-4': {
              totalTasks: 456,
              successRate: 96.1,
              averageExecutionTime: 2.1,
              lastExecution: Date.now() - 300000
            },
            'Claude': {
              totalTasks: 389,
              successRate: 93.8,
              averageExecutionTime: 2.4,
              lastExecution: Date.now() - 180000
            },
            'Gemini': {
              totalTasks: 402,
              successRate: 92.5,
              averageExecutionTime: 2.5,
              lastExecution: Date.now() - 120000
            }
          }
        }
      };

      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <span className="text-lg">Analitik verileri yükleniyor...</span>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Analitik verileri yüklenemedi.</p>
        <Button onClick={fetchAnalyticsData} className="mt-4">
          <RefreshCw className="w-4 h-4 mr-2" />
          Yeniden Dene
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI Marketing Analitik</h1>
          <p className="text-gray-600 mt-1">
            Kapsamlı performans analizi ve raporlama
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Son 7 Gün</SelectItem>
              <SelectItem value="30d">Son 30 Gün</SelectItem>
              <SelectItem value="90d">Son 3 Ay</SelectItem>
              <SelectItem value="1y">Son 1 Yıl</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchAnalyticsData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Yenile
          </Button>
          <Button>
            <Download className="w-4 h-4 mr-2" />
            Rapor İndir
          </Button>
        </div>
      </div>

      {/* Overview KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Performans Skoru</p>
                <p className="text-2xl font-bold text-blue-600">{analyticsData.overview.performanceScore}</p>
              </div>
              <BarChart3 className="w-8 h-8 text-blue-600" />
            </div>
            <div className="mt-2">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${analyticsData.overview.performanceScore}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">ROI</p>
                <p className="text-2xl font-bold text-green-600">{analyticsData.overview.totalROI}x</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
            <p className="text-xs text-green-600 mt-1">+12% bu ay</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Toplam Erişim</p>
                <p className="text-2xl font-bold">{formatNumber(analyticsData.overview.totalReach)}</p>
              </div>
              <Eye className="w-8 h-8 text-purple-600" />
            </div>
            <p className="text-xs text-purple-600 mt-1">+8% bu ay</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Etkileşim</p>
                <p className="text-2xl font-bold">{formatNumber(analyticsData.overview.totalEngagement)}</p>
              </div>
              <MessageSquare className="w-8 h-8 text-orange-600" />
            </div>
            <p className="text-xs text-orange-600 mt-1">+15% bu ay</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Dönüşümler</p>
                <p className="text-2xl font-bold text-green-600">{analyticsData.overview.totalConversions}</p>
              </div>
              <Target className="w-8 h-8 text-green-600" />
            </div>
            <p className="text-xs text-green-600 mt-1">+22% bu ay</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
