'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Package, 
  User, 
  Calendar, 
  DollarSign, 
  MapPin, 
  Truck,
  CheckCircle,
  Eye,
  MessageSquare,
  Search,
  Filter
} from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

// Mock data for shipped orders
const mockShippedOrders = [
  {
    id: 'ORD-SHIP-001',
    customerName: 'Mehmet Özkan',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Traverten Doğal Taş',
    quantity: 250,
    unit: 'm²',
    orderDate: '2024-12-15',
    shippedDate: '2025-01-02',
    deliveryDate: '2025-01-05',
    actualDeliveryDate: '2025-01-04',
    status: 'delivered',
    totalValue: 12500,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 12500,
    shippingInfo: {
      trackingNumber: 'TRK-2025-001',
      carrier: 'Aras Kargo',
      shippingMethod: 'Express Teslimat'
    },
    deliveryAddress: 'Atatürk Mah. İnönü Cad. No:45 Kadıköy/İstanbul',
    notes: 'Zamanında teslim edildi, müşteri memnun'
  },
  {
    id: 'ORD-SHIP-002',
    customerName: 'Ayşe Demir',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Mermer Beyaz',
    quantity: 180,
    unit: 'm²',
    orderDate: '2024-12-20',
    shippedDate: '2025-01-03',
    deliveryDate: '2025-01-07',
    actualDeliveryDate: null,
    status: 'shipped',
    totalValue: 18000,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 18000,
    shippingInfo: {
      trackingNumber: 'TRK-2025-002',
      carrier: 'Yurtiçi Kargo',
      shippingMethod: 'Standart Teslimat'
    },
    deliveryAddress: 'Çankaya Mah. Kızılay Cad. No:123 Çankaya/Ankara',
    notes: 'Kargoda, takip edilebilir'
  },
  {
    id: 'ORD-SHIP-003',
    customerName: 'Ali Yılmaz',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Granit Siyah',
    quantity: 320,
    unit: 'm²',
    orderDate: '2024-12-10',
    shippedDate: '2024-12-28',
    deliveryDate: '2025-01-02',
    actualDeliveryDate: '2025-01-02',
    status: 'delivered',
    totalValue: 24000,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 24000,
    shippingInfo: {
      trackingNumber: 'TRK-2024-045',
      carrier: 'MNG Kargo',
      shippingMethod: 'Hızlı Teslimat'
    },
    deliveryAddress: 'Konak Mah. Alsancak Cad. No:67 Konak/İzmir',
    notes: 'Başarılı teslimat, müşteri teşekkür etti'
  }
]

export default function ShippedOrdersPage() {
  const { producer } = useProducerAuth()
  const [orders, setOrders] = React.useState(mockShippedOrders)
  const [searchTerm, setSearchTerm] = React.useState('')
  const [statusFilter, setStatusFilter] = React.useState('all')
  const [selectedOrder, setSelectedOrder] = React.useState<any>(null)

  const filteredOrders = React.useMemo(() => {
    return orders.filter(order => {
      const matchesSearch = 
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.productName.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesStatus = statusFilter === 'all' || order.status === statusFilter
      
      return matchesSearch && matchesStatus
    })
  }, [orders, searchTerm, statusFilter])

  const handleViewDetails = (order: any) => {
    setSelectedOrder(order)
    alert(`Sipariş Detayları:\n\nSipariş: ${order.id}\nMüşteri: ${order.customerName}\nÜrün: ${order.productName}\nMiktar: ${order.quantity} ${order.unit}\nDurum: ${order.status}\nKargo: ${order.shippingInfo.trackingNumber}`)
  }

  const handleSendMessage = (order: any) => {
    alert(`${order.customerName} ile iletişim kurulacak.\nTelefon: ${order.customerPhone}\nE-posta: ${order.customerEmail}`)
  }

  const handleTrackShipment = (order: any) => {
    alert(`Kargo Takip Bilgileri:\n\nTakip No: ${order.shippingInfo.trackingNumber}\nKargo Şirketi: ${order.shippingInfo.carrier}\nDurum: ${order.status === 'shipped' ? 'Kargoda' : 'Teslim Edildi'}`)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800'
      case 'shipped':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'Teslim Edildi'
      case 'shipped':
        return 'Kargoda'
      default:
        return status
    }
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Sevk Edilmiş Siparişler</h1>
        <p className="text-gray-600">
          Kargoya verilmiş ve teslim edilmiş siparişler
        </p>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Sipariş ID, müşteri adı veya ürün ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="w-full md:w-48">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Durum filtrele" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Durumlar</SelectItem>
                  <SelectItem value="shipped">Kargoda</SelectItem>
                  <SelectItem value="delivered">Teslim Edildi</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Truck className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Kargoda</p>
                <p className="text-2xl font-bold text-blue-900">
                  {orders.filter(o => o.status === 'shipped').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Teslim Edildi</p>
                <p className="text-2xl font-bold text-green-900">
                  {orders.filter(o => o.status === 'delivered').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <DollarSign className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Toplam Gelir</p>
                <p className="text-2xl font-bold text-purple-900">
                  ${orders.reduce((sum, o) => sum + o.totalValue, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Orders List */}
      <div className="space-y-4">
        {filteredOrders.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm || statusFilter !== 'all' ? 'Arama kriterlerine uygun sipariş bulunamadı' : 'Sevk edilmiş sipariş bulunmuyor'}
              </h3>
              <p className="text-gray-600">
                {searchTerm || statusFilter !== 'all' ? 'Farklı arama terimleri deneyin' : 'Henüz sevk edilmiş siparişiniz bulunmuyor'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredOrders.map((order) => (
            <Card key={order.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center gap-6">
                  {/* Order Info */}
                  <div className="flex-1 space-y-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          Sipariş #{order.id}
                        </h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className={getStatusColor(order.status)}>
                            {getStatusText(order.status)}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            Sevk: {formatDate(order.shippedDate)}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">{order.customerName}</div>
                          <div className="text-gray-500">{order.customerPhone}</div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Package className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">{order.productName}</div>
                          <div className="text-gray-500">{order.quantity} {order.unit}</div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">
                            {order.totalValue.toLocaleString()} {order.currency}
                          </div>
                          <div className="text-gray-500">
                            Ödendi: {order.paidAmount.toLocaleString()} {order.currency}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">
                            {order.actualDeliveryDate ? formatDate(order.actualDeliveryDate) : formatDate(order.deliveryDate)}
                          </div>
                          <div className="text-gray-500">
                            {order.actualDeliveryDate ? 'Teslim edildi' : 'Tahmini teslimat'}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Shipping Info */}
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Truck className="w-4 h-4 text-blue-600" />
                        <span className="font-medium text-blue-800">Kargo Bilgileri</span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                        <div>
                          <span className="font-medium text-blue-700">Takip No:</span>
                          <p className="text-blue-900 font-mono">{order.shippingInfo.trackingNumber}</p>
                        </div>
                        <div>
                          <span className="font-medium text-blue-700">Kargo:</span>
                          <p className="text-blue-900">{order.shippingInfo.carrier}</p>
                        </div>
                        <div>
                          <span className="font-medium text-blue-700">Yöntem:</span>
                          <p className="text-blue-900">{order.shippingInfo.shippingMethod}</p>
                        </div>
                      </div>
                    </div>

                    {/* Delivery Address */}
                    <div className="flex items-start gap-2 text-sm">
                      <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                      <div>
                        <span className="font-medium text-gray-700">Teslimat Adresi:</span>
                        <p className="text-gray-600">{order.deliveryAddress}</p>
                      </div>
                    </div>

                    {/* Notes */}
                    {order.notes && (
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Notlar:</span>
                        <p className="text-gray-600 mt-1">{order.notes}</p>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col gap-2 lg:ml-4">
                    <Button
                      onClick={() => handleTrackShipment(order)}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Truck className="w-4 h-4 mr-1" />
                      Kargo Takip
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSendMessage(order)}
                    >
                      <MessageSquare className="w-4 h-4 mr-1" />
                      Müşteriyle İletişim
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewDetails(order)}
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      Detayları Gör
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Development Info */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h3 className="font-semibold text-blue-800 mb-2">📦 Sevk Edilmiş Siparişler</h3>
        <p className="text-sm text-blue-700">
          Bu sayfa kargoya verilmiş ve teslim edilmiş siparişlerin takibi için oluşturulmuştur.
        </p>
        <div className="mt-3 text-xs text-blue-600">
          <p><strong>Özellikler:</strong></p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Kargo takip sistemi</li>
            <li>Teslimat durumu takibi</li>
            <li>Müşteri iletişim sistemi</li>
            <li>Arama ve filtreleme</li>
            <li>Detaylı sipariş bilgileri</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
