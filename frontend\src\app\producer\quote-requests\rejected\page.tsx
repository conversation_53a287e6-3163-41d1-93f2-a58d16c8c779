'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { QuoteSubmissionModal } from '@/components/ui/quote-submission-modal'
import { CustomerContactModal } from '@/components/ui/customer-contact-modal'
import { RequestDetailsModal } from '@/components/ui/request-details-modal'
import {
  XCircle,
  User,
  Calendar,
  Package,
  MessageSquare,
  Search,
  Filter,
  Eye,
  RefreshCw,
  AlertTriangle
} from 'lucide-react'

// Mock data for rejected quote requests
const mockRejectedRequests = [
  {
    id: 'REJ-001',
    customerId: '1',
    customerName: 'Ah<PERSON> Yılmaz',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Traverten Klasik',
    quantity: 150,
    targetPrice: 45,
    currency: 'USD',
    requestDate: '2024-12-20',
    rejectedDate: '2024-12-22',
    rejectionReason: 'Hedef fiyat çok düşük. Minimum 55 USD/m² olmalı.',
    message: 'Villa projesi için traverten ihtiyacımız var.',
    status: 'rejected',
    canResubmit: true,
    lastContactDate: '2024-12-22',
    products: [
      {
        id: 'prod-rej-1',
        productId: '1',
        productName: 'Traverten Klasik',
        productCategory: 'Traverten',
        productImage: '/api/placeholder/300/200',
        specifications: [
          {
            id: 'spec-rej-1-1',
            type: 'sized',
            thickness: '2',
            width: '60',
            length: '120',
            surface: 'Cilalı',
            packaging: 'Kasalı',
            delivery: 'Fabrika',
            area: '150',
            targetPrice: '45',
            currency: 'USD'
          }
        ]
      }
    ]
  },
  {
    id: 'REJ-002',
    customerId: '2',
    customerName: 'Fatma Demir',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Mermer Beyaz Carrara',
    quantity: 80,
    targetPrice: 120,
    currency: 'USD',
    requestDate: '2024-12-18',
    rejectedDate: '2024-12-19',
    rejectionReason: 'Bu ürün stokta yok. Alternatif ürünler önerebiliriz.',
    message: 'Mutfak tezgahı için beyaz mermer arıyorum.',
    status: 'rejected',
    canResubmit: true,
    lastContactDate: '2024-12-19',
    products: [
      {
        id: 'prod-rej-2',
        productId: '2',
        productName: 'Mermer Beyaz Carrara',
        productCategory: 'Mermer',
        productImage: '/api/placeholder/300/200',
        specifications: [
          {
            id: 'spec-rej-2-1',
            type: 'sized',
            thickness: '3',
            width: '40',
            length: '80',
            surface: 'Cilalı',
            packaging: 'Kasalı',
            delivery: 'Adrese Teslimat',
            area: '80',
            targetPrice: '120',
            currency: 'USD'
          }
        ]
      }
    ]
  },
  {
    id: 'REJ-003',
    customerId: '3',
    customerName: 'Mehmet Özkan',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Granit Siyah',
    quantity: 200,
    targetPrice: 80,
    currency: 'USD',
    requestDate: '2024-12-15',
    rejectedDate: '2024-12-16',
    rejectionReason: 'Teslimat tarihi uygun değil. En erken 3 ay sonra teslimat yapabiliriz.',
    message: 'Acil proje için granit gerekiyor.',
    status: 'rejected',
    canResubmit: false,
    lastContactDate: '2024-12-20',
    products: [
      {
        id: 'prod-rej-3',
        productId: '3',
        productName: 'Granit Siyah',
        productCategory: 'Granit',
        productImage: '/api/placeholder/300/200',
        specifications: [
          {
            id: 'spec-rej-3-1',
            type: 'sized',
            thickness: '2',
            width: '50',
            length: '100',
            surface: 'Honlu',
            packaging: 'Kasalı',
            delivery: 'Şantiye',
            area: '200',
            targetPrice: '80',
            currency: 'USD'
          }
        ]
      }
    ]
  }
]

export default function RejectedQuoteRequestsPage() {
  const { producer } = useProducerAuth()
  const [requests, setRequests] = React.useState(mockRejectedRequests)
  const [searchTerm, setSearchTerm] = React.useState('')
  const [sortBy, setSortBy] = React.useState('rejectedDate')
  const [selectedRequest, setSelectedRequest] = React.useState<any>(null)
  const [isQuoteModalOpen, setIsQuoteModalOpen] = React.useState(false)
  const [isContactModalOpen, setIsContactModalOpen] = React.useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = React.useState(false)

  const filteredRequests = React.useMemo(() => {
    let filtered = requests.filter(request =>
      request.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.productName.toLowerCase().includes(searchTerm.toLowerCase())
    )

    // Sort requests
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'rejectedDate':
          return new Date(b.rejectedDate).getTime() - new Date(a.rejectedDate).getTime()
        case 'customerName':
          return a.customerName.localeCompare(b.customerName)
        case 'quantity':
          return b.quantity - a.quantity
        default:
          return 0
      }
    })

    return filtered
  }, [requests, searchTerm, sortBy])

  const handleViewDetails = (request: any) => {
    setSelectedRequest(request)
    setIsDetailsModalOpen(true)
  }

  const handleSendMessage = (request: any) => {
    setSelectedRequest(request)
    setIsContactModalOpen(true)
  }

  const handleResubmitQuote = (request: any) => {
    if (!request.canResubmit) {
      alert('Bu talep için yeniden teklif verilemez.')
      return
    }

    setSelectedRequest(request)
    setIsQuoteModalOpen(true)
  }

  const handleQuoteSubmit = async (quoteData: any) => {
    try {
      console.log('Resubmitting quote for request:', selectedRequest?.id, quoteData)
      // Here you would send the quote to your API
      alert('Yeniden teklif başarıyla gönderildi!')
      setIsQuoteModalOpen(false)
      setSelectedRequest(null)
      return true
    } catch (error) {
      console.error('Error resubmitting quote:', error)
      alert('Teklif gönderilirken hata oluştu.')
      return false
    }
  }

  const handleSendCustomerMessage = async (messageData: any) => {
    try {
      console.log('Sending message to customer:', messageData)
      // Here you would send the message to your API
      alert('Mesaj başarıyla gönderildi!')
      return true
    } catch (error) {
      console.error('Error sending message:', error)
      alert('Mesaj gönderilirken hata oluştu.')
      return false
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  const getDaysAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Reddedilen Teklif İstekleri</h1>
        <p className="text-gray-600">
          Reddettiğiniz teklif istekleri ve yeniden değerlendirme fırsatları
        </p>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Talep ID, müşteri adı veya ürün ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="w-full md:w-48">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Sırala" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="rejectedDate">Red Tarihi</SelectItem>
                  <SelectItem value="customerName">Müşteri Adı</SelectItem>
                  <SelectItem value="quantity">Miktar</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <XCircle className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Toplam Reddedilen</p>
                <p className="text-2xl font-bold text-red-900">{requests.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <RefreshCw className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Yeniden Teklif Verilebilir</p>
                <p className="text-2xl font-bold text-green-900">
                  {requests.filter(r => r.canResubmit).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Calendar className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Son 7 Gün</p>
                <p className="text-2xl font-bold text-orange-900">
                  {requests.filter(r => getDaysAgo(r.rejectedDate) <= 7).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Rejected Requests List */}
      <div className="space-y-4">
        {filteredRequests.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <XCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm ? 'Arama kriterlerine uygun reddedilen talep bulunamadı' : 'Reddedilen teklif isteği bulunmuyor'}
              </h3>
              <p className="text-gray-600">
                {searchTerm ? 'Farklı arama terimleri deneyin' : 'Henüz hiç teklif isteği reddetmediniz'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredRequests.map((request) => (
            <Card key={request.id} className="overflow-hidden border-red-200 bg-red-50">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center gap-6">
                  {/* Request Info */}
                  <div className="flex-1 space-y-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          Talep #{request.id}
                        </h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className="bg-red-100 text-red-800">
                            Reddedildi
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {getDaysAgo(request.rejectedDate)} gün önce
                          </span>
                          {request.canResubmit && (
                            <Badge className="bg-green-100 text-green-800">
                              Yeniden Teklif Verilebilir
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">{request.customerName}</div>
                          <div className="text-gray-500">{request.customerPhone}</div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Package className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">{request.productName}</div>
                          <div className="text-gray-500">{request.quantity} m²</div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">
                            {formatDate(request.rejectedDate)}
                          </div>
                          <div className="text-gray-500">Red tarihi</div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <MessageSquare className="w-4 h-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-gray-900">
                            {formatDate(request.lastContactDate)}
                          </div>
                          <div className="text-gray-500">Son iletişim</div>
                        </div>
                      </div>
                    </div>

                    {/* Rejection Reason */}
                    <div className="bg-red-100 p-3 rounded-lg border border-red-200">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="font-medium text-red-800 mb-1">Red Sebebi</h4>
                          <p className="text-red-700 text-sm">{request.rejectionReason}</p>
                        </div>
                      </div>
                    </div>

                    {/* Original Message */}
                    {request.message && (
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Müşteri Mesajı:</span>
                        <p className="text-gray-600 mt-1">{request.message}</p>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col gap-2 lg:ml-4">
                    {request.canResubmit && (
                      <Button
                        onClick={() => handleResubmitQuote(request)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <RefreshCw className="w-4 h-4 mr-1" />
                        Yeniden Teklif Ver
                      </Button>
                    )}
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSendMessage(request)}
                    >
                      <MessageSquare className="w-4 h-4 mr-1" />
                      Müşteriyle İletişim
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewDetails(request)}
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      Detayları Gör
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Tips for Resubmission */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-800">💡 Yeniden Teklif Verme İpuçları</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
            <div>
              <h4 className="font-medium mb-2">Fiyat Sorunları İçin:</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>Alternatif ürünler önerin</li>
                <li>Farklı kalite seçenekleri sunun</li>
                <li>Toplu alım indirimleri teklif edin</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Teslimat Sorunları İçin:</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>Kısmi teslimat seçenekleri sunun</li>
                <li>Güncel teslimat takvimini paylaşın</li>
                <li>Acil teslimat ücretleri belirtin</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Development Info */}
      <div className="bg-red-50 p-4 rounded-lg border border-red-200">
        <h3 className="font-semibold text-red-800 mb-2">❌ Reddedilen Teklif İstekleri</h3>
        <p className="text-sm text-red-700">
          Bu sayfa reddedilen teklif isteklerinin yönetimi ve yeniden değerlendirme fırsatları için oluşturulmuştur.
        </p>
        <div className="mt-3 text-xs text-red-600">
          <p><strong>Özellikler:</strong></p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Red sebeplerini görüntüleme</li>
            <li>Yeniden teklif verme imkanı</li>
            <li>Müşteri iletişim sistemi</li>
            <li>Arama ve sıralama</li>
            <li>İstatistiksel görünüm</li>
          </ul>
        </div>
      </div>

      {/* Modals */}
      {isQuoteModalOpen && selectedRequest && (
        <QuoteSubmissionModal
          isOpen={isQuoteModalOpen}
          onClose={() => {
            setIsQuoteModalOpen(false)
            setSelectedRequest(null)
          }}
          request={selectedRequest}
          onSubmitQuote={handleQuoteSubmit}
        />
      )}

      {isContactModalOpen && selectedRequest && (
        <CustomerContactModal
          isOpen={isContactModalOpen}
          onClose={() => {
            setIsContactModalOpen(false)
            setSelectedRequest(null)
          }}
          request={selectedRequest}
          onSendMessage={handleSendCustomerMessage}
        />
      )}

      {isDetailsModalOpen && selectedRequest && (
        <RequestDetailsModal
          isOpen={isDetailsModalOpen}
          onClose={() => {
            setIsDetailsModalOpen(false)
            setSelectedRequest(null)
          }}
          request={selectedRequest}
        />
      )}
    </div>
  )
}
