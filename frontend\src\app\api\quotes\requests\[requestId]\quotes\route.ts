import { NextRequest, NextResponse } from 'next/server'

// Mock database - in real app, this would be a database
let quotes: any[] = []
let quoteRequests: any[] = [
  {
    id: '1',
    customerName: 'ABC İnşaat Ltd.',
    customerEmail: '<EMAIL>',
    status: 'pending',
    createdAt: new Date('2025-06-28'),
    products: []
  }
]

export async function POST(
  request: NextRequest,
  { params }: { params: { requestId: string } }
) {
  try {
    const requestId = params.requestId
    const body = await request.json()

    // Validate request data
    if (!body.producerId || !body.items || !Array.isArray(body.items)) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data' },
        { status: 400 }
      )
    }

    // Check if quote request exists
    const quoteRequest = quoteRequests.find(req => req.id === requestId)
    if (!quoteRequest) {
      return NextResponse.json(
        { success: false, error: 'Quote request not found' },
        { status: 404 }
      )
    }

    // Check if producer already submitted a quote for this request
    const existingQuote = quotes.find(
      quote => quote.quoteRequestId === requestId && quote.producerId === body.producerId
    )

    if (existingQuote) {
      return NextResponse.json(
        { success: false, error: 'Quote already submitted for this request' },
        { status: 409 }
      )
    }

    // Create new quote
    const newQuote = {
      id: `quote-${Date.now()}`,
      quoteRequestId: requestId,
      producerId: body.producerId,
      producerName: body.producerName,
      producerCompany: body.producerCompany,
      producerEmail: body.producerEmail,
      items: body.items,
      totalAmount: body.totalAmount,
      currency: body.currency,
      validUntil: new Date(body.validUntil),
      terms: body.terms,
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    // Add quote to mock database
    quotes.push(newQuote)

    // Update quote request status to 'quoted'
    const requestIndex = quoteRequests.findIndex(req => req.id === requestId)
    if (requestIndex !== -1) {
      quoteRequests[requestIndex].status = 'quoted'
      quoteRequests[requestIndex].updatedAt = new Date()
    }

    console.log('Quote submitted successfully:', {
      quoteId: newQuote.id,
      requestId,
      producerId: body.producerId,
      totalAmount: body.totalAmount,
      currency: body.currency
    })

    return NextResponse.json({
      success: true,
      data: {
        quote: newQuote,
        message: 'Quote submitted successfully'
      }
    })

  } catch (error) {
    console.error('Error submitting quote:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { requestId: string } }
) {
  try {
    const requestId = params.requestId

    // Get all quotes for this request
    const requestQuotes = quotes.filter(quote => quote.quoteRequestId === requestId)

    return NextResponse.json({
      success: true,
      data: requestQuotes
    })

  } catch (error) {
    console.error('Error fetching quotes:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
