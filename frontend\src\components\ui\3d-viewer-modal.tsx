"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Button } from "./button"

interface Product3DViewerModalProps {
  isOpen: boolean
  onClose: () => void
  product: {
    id: string
    name: string
    category: string
    image: string
  } | null
}

export function Product3DViewerModal({ isOpen, onClose, product }: Product3DViewerModalProps) {
  if (!isOpen || !product) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{product.name}</h2>
            <p className="text-gray-600">{product.category}</p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </Button>
        </div>

        {/* 3D Viewer Content */}
        <div className="p-6">
          <div className="bg-gray-100 rounded-lg h-96 flex items-center justify-center mb-6">
            <div className="text-center">
              <div className="text-6xl mb-4">🪨</div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">3D Görüntüleyici</h3>
              <p className="text-gray-600 mb-4">
                {product.name} için 3D görüntüleyici yükleniyor...
              </p>
              <div className="animate-spin w-8 h-8 border-4 border-stone-300 border-t-stone-600 rounded-full mx-auto"></div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Boyut:</label>
              <select className="px-3 py-1 border border-gray-300 rounded text-sm">
                <option>30x60cm</option>
                <option>60x60cm</option>
                <option>80x80cm</option>
                <option>100x100cm</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Kalınlık:</label>
              <select className="px-3 py-1 border border-gray-300 rounded text-sm">
                <option>2cm</option>
                <option>3cm</option>
                <option>4cm</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Yüzey:</label>
              <select className="px-3 py-1 border border-gray-300 rounded text-sm">
                <option>Cilalı</option>
                <option>Mat</option>
                <option>Doğal</option>
              </select>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button variant="primary" className="flex-1">
              📋 Teklif Al
            </Button>
            <Button variant="outline" className="flex-1">
              💾 Kaydet
            </Button>
            <Button variant="outline" className="flex-1">
              📤 Paylaş
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
