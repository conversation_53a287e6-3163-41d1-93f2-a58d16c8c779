import { Request, Response, NextFunction } from 'express';
import { FileUploadService, UploadedFile } from '../services/FileUploadService';
import { 
  fileUploadConfigs, 
  imageProcessingPresets, 
  fileValidation, 
  storagePaths 
} from '../config/fileUpload';
import { asyncHandler } from '../middleware/errorHandler';
import { z } from 'zod';

// Validation schemas
const uploadProductImageSchema = z.object({
  productId: z.string().cuid(),
  category: z.enum(['main', 'gallery', 'technical']).default('gallery'),
  processImage: z.boolean().default(true),
});

const uploadDocumentSchema = z.object({
  entityId: z.string().cuid(),
  entityType: z.enum(['product', 'order', 'analysis-report', 'sample']),
  documentType: z.string().optional(),
});

const uploadAvatarSchema = z.object({
  userId: z.string().cuid(),
});

export class FileUploadController {
  private productImageService: FileUploadService;
  private documentService: FileUploadService;
  private avatarService: FileUploadService;
  private asset3DService: FileUploadService;

  constructor() {
    this.productImageService = new FileUploadService(fileUploadConfigs.productImages());
    this.documentService = new FileUploadService(fileUploadConfigs.documents());
    this.avatarService = new FileUploadService(fileUploadConfigs.avatars());
    this.asset3DService = new FileUploadService(fileUploadConfigs.assets3D());
  }

  private getProductImageUpload() {
    return this.productImageService.getMulterConfig({
      fieldName: 'images',
      maxCount: 10,
    }).array('images');
  }

  private getDocumentUpload() {
    return this.documentService.getMulterConfig({
      fieldName: 'documents',
      maxCount: 5,
    }).array('documents');
  }

  private getAvatarUpload() {
    return this.avatarService.getMulterConfig({
      fieldName: 'avatar',
      maxCount: 1,
    }).single('avatar');
  }

  private getAsset3DUpload() {
    return this.asset3DService.getMulterConfig({
      fieldName: 'assets',
      maxCount: 10,
    }).array('assets');
  }

  /**
   * Upload product images
   */
  uploadProductImages = [
    (req: Request, res: Response, next: NextFunction) => {
      this.getProductImageUpload()(req, res, next);
    },
    asyncHandler(async (req: Request, res: Response) => {
      const { productId, category, processImage } = uploadProductImageSchema.parse(req.body);
      const files = req.files as Express.Multer.File[];

      if (!files || files.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No files uploaded'
        });
      }

      const results: UploadedFile[] = [];

      for (const file of files) {
        try {
          if (fileValidation.isImage(file.mimetype) && processImage) {
            // Process image with optimization - temporarily disabled
            // const processedFiles = await this.productImageService.processImage(
            //   file,
            //   imageProcessingPresets.productImage
            // );
            // results.push(...processedFiles);

            // Use original file for now
            const uploadedFile: UploadedFile = {
              filename: file.filename,
              originalName: file.originalname,
              mimetype: file.mimetype,
              size: file.size,
              url: `/uploads/${file.filename}`,
              path: file.path
            };
            results.push(uploadedFile);
          } else {
            // Use original file
            const uploadedFile: UploadedFile = {
              filename: file.filename,
              originalName: file.originalname,
              mimetype: file.mimetype,
              size: file.size,
              url: (file as any).location || `/uploads/${file.filename}`,
              key: (file as any).key,
              path: file.path,
            };
            results.push(uploadedFile);
          }
        } catch (error) {
          console.error(`Error processing file ${file.originalname}:`, error);
          // Continue with other files
        }
      }

      res.json({
        success: true,
        data: {
          files: results,
          productId,
          category,
        },
        message: `${results.length} file(s) uploaded successfully`
      });
    })
  ];

  /**
   * Upload single product image
   */
  uploadProductImage = [
    (req: Request, res: Response, next: NextFunction) => {
      this.productImageService.getMulterConfig({
        fieldName: 'image',
        maxCount: 1,
      }).single('image')(req, res, next);
    },
    asyncHandler(async (req: Request, res: Response) => {
      const { productId, category, processImage } = uploadProductImageSchema.parse(req.body);
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'No file uploaded'
        });
      }

      let result: UploadedFile;

      if (fileValidation.isImage(file.mimetype) && processImage) {
        // Process image with optimization - temporarily disabled
        // const processedFiles = await this.productImageService.processImage(
        //   file,
        //   imageProcessingPresets.productImage
        // );
        // result = processedFiles[0]; // Main processed image

        // Use original file for now
        result = {
          filename: file.filename,
          originalName: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          url: `/uploads/${file.filename}`,
          path: file.path
        };
      } else {
        // Use original file
        result = {
          filename: file.filename,
          originalName: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          url: (file as any).location || `/uploads/${file.filename}`,
          key: (file as any).key,
          path: file.path,
        };
      }

      res.json({
        success: true,
        data: {
          file: result,
          productId,
          category,
        },
        message: 'File uploaded successfully'
      });
    })
  ];

  /**
   * Upload documents
   */
  uploadDocument = [
    (req: Request, res: Response, next: NextFunction) => {
      this.getDocumentUpload()(req, res, next);
    },
    asyncHandler(async (req: Request, res: Response) => {
      const { entityId, entityType, documentType } = uploadDocumentSchema.parse(req.body);
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'No file uploaded'
        });
      }

      const result: UploadedFile = {
        filename: file.filename,
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        url: (file as any).location || `/uploads/${file.filename}`,
        key: (file as any).key,
        path: file.path,
      };

      res.json({
        success: true,
        data: {
          file: result,
          entityId,
          entityType,
          documentType,
        },
        message: 'Document uploaded successfully'
      });
    })
  ];

  /**
   * Upload user avatar
   */
  uploadAvatar = [
    (req: Request, res: Response, next: NextFunction) => {
      this.getAvatarUpload()(req, res, next);
    },
    asyncHandler(async (req: Request, res: Response) => {
      const { userId } = uploadAvatarSchema.parse(req.body);
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'No file uploaded'
        });
      }

      // Process avatar image - temporarily disabled
      // const processedFiles = await this.avatarService.processImage(
      //   file,
      //   imageProcessingPresets.avatar
      // );

      const result = {
        filename: file.filename,
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        url: `/uploads/${file.filename}`,
        path: file.path
      };

      res.json({
        success: true,
        data: {
          file: result,
          userId,
        },
        message: 'Avatar uploaded successfully'
      });
    })
  ];

  /**
   * Upload 3D assets
   */
  upload3DAsset = [
    (req: Request, res: Response, next: NextFunction) => {
      this.asset3DService.getMulterConfig({
        fieldName: 'asset',
        maxCount: 1,
      }).single('asset')(req, res, next);
    },
    asyncHandler(async (req: Request, res: Response) => {
      const { productId, assetType, name, description } = req.body;
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'No file uploaded'
        });
      }

      const result: UploadedFile = {
        filename: file.filename,
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        url: (file as any).location || `/uploads/${file.filename}`,
        key: (file as any).key,
        path: file.path,
      };

      res.json({
        success: true,
        data: {
          file: result,
          productId,
          assetType,
          name,
          description,
        },
        message: '3D asset uploaded successfully'
      });
    })
  ];

  /**
   * Delete file
   */
  deleteFile = asyncHandler(async (req: Request, res: Response) => {
    const { fileKey, fileType } = req.body;

    if (!fileKey) {
      return res.status(400).json({
        success: false,
        error: 'File key is required'
      });
    }

    let service: FileUploadService;
    
    switch (fileType) {
      case 'product-image':
        service = this.productImageService;
        break;
      case 'document':
        service = this.documentService;
        break;
      case 'avatar':
        service = this.avatarService;
        break;
      case '3d-asset':
        service = this.asset3DService;
        break;
      default:
        service = this.productImageService; // Default
    }

    try {
      // await service.deleteFile(fileKey); // Temporarily disabled

      res.json({
        success: true,
        message: 'File deletion temporarily disabled'
      });
    } catch (error) {
      console.error('File deletion error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete file'
      });
    }
  });

  /**
   * Get signed URL for private files (S3 only)
   */
  getSignedUrl = asyncHandler(async (req: Request, res: Response) => {
    const { fileKey, fileType, expiresIn } = req.query;

    if (!fileKey) {
      return res.status(400).json({
        success: false,
        error: 'File key is required'
      });
    }

    let service: FileUploadService;
    
    switch (fileType) {
      case 'product-image':
        service = this.productImageService;
        break;
      case 'document':
        service = this.documentService;
        break;
      case 'avatar':
        service = this.avatarService;
        break;
      case '3d-asset':
        service = this.asset3DService;
        break;
      default:
        service = this.productImageService; // Default
    }

    try {
      // const signedUrl = await service.getSignedUrl(
      //   fileKey as string,
      //   parseInt(expiresIn as string) || 3600
      // );

      const signedUrl = `/uploads/${fileKey}`; // Direct URL for local storage

      res.json({
        success: true,
        data: {
          signedUrl,
          expiresIn: parseInt(expiresIn as string) || 3600,
        }
      });
    } catch (error) {
      console.error('Signed URL generation error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate signed URL'
      });
    }
  });
}
