'use client';

import React from 'react';
import { BeakerIcon } from '@heroicons/react/24/outline';
import { SampleRequestEmptyStateProps } from '@/types/sample-request';

const SampleRequestEmptyState: React.FC<SampleRequestEmptyStateProps> = ({
  activeTab,
  tabLabel,
  onNavigate
}) => {
  const getEmptyStateMessage = () => {
    if (activeTab === 'all') {
      return {
        title: 'Numune talebiniz bulunmuyor',
        description: '<PERSON><PERSON><PERSON><PERSON> aldı<PERSON>ınız ürünlerden numune talep edebilirsiniz.'
      };
    }
    
    return {
      title: `${tabLabel} numune talebi bulunmuyor`,
      description: '<PERSON><PERSON><PERSON><PERSON> ald<PERSON>z ürünlerden numune talep edebilirsiniz.'
    };
  };

  const { title, description } = getEmptyStateMessage();

  return (
    <div className="text-center py-12">
      <BeakerIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {title}
      </h3>
      <p className="text-gray-600 mb-6">
        {description}
      </p>
      <button
        onClick={() => onNavigate?.('/customer/requests/active')}
        className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
      >
        Aktif Taleplerime Git
      </button>
    </div>
  );
};

export default SampleRequestEmptyState;
