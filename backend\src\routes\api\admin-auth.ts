import express from 'express'
import { body } from 'express-validator'
import { adminLogin, adminLogout, forgotPassword, verifyAuth } from '../../modules/admin/admin-auth.controller'

const router = express.Router()

// Admin login validation
const adminLoginValidation = [
  body('email')
    .isEmail()
    .withMessage('Geçerli bir email adresi girin')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Şifre en az 6 karakter olmalıdır'),
  body('rememberMe')
    .optional()
    .isBoolean()
    .withMessage('Beni hatırla seçeneği boolean olmalıdır')
]

// Forgot password validation
const forgotPasswordValidation = [
  body('email')
    .isEmail()
    .withMessage('Geçerli bir email adresi girin')
    .normalizeEmail()
]

// Routes
router.post('/login', adminLoginValidation, adminLogin)
router.post('/logout', adminLogout)
router.get('/verify', verifyAuth)
router.post('/forgot-password', forgotPasswordValidation, forgotPassword)

export default router
