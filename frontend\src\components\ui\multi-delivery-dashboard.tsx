'use client'

import * as React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Package, 
  Calendar, 
  DollarSign, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  Truck
} from 'lucide-react'
import { MultiDeliveryOrder } from '@/types/multi-delivery'
import { getOverallProgress } from '@/data/mock-multi-delivery'
import { ProductionScheduleCard } from './production-schedule-card'
import { EditPackageModal } from './edit-package-modal'

interface MultiDeliveryDashboardProps {
  order: MultiDeliveryOrder
  onUpdateStage: (packageId: string, stageId: string, status: any) => void
  onPauseProduction: (packageId: string) => void
  onViewPackageDetails: (packageId: string) => void
  onEditPackage?: (packageId: string) => void
}

export function MultiDeliveryDashboard({
  order,
  onUpdateStage,
  onPauseProduction,
  onViewPackageDetails,
  onEditPackage
}: MultiDeliveryDashboardProps) {
  const [selectedPackage, setSelectedPackage] = React.useState<any>(null)
  const [isEditPackageModalOpen, setIsEditPackageModalOpen] = React.useState(false)
  const overallProgress = getOverallProgress(order)
  
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  const getStatusColor = (status: MultiDeliveryOrder['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in_production':
        return 'bg-blue-100 text-blue-800'
      case 'partially_delivered':
        return 'bg-purple-100 text-purple-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: MultiDeliveryOrder['status']) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'confirmed':
        return 'Onaylandı'
      case 'in_production':
        return 'Üretimde'
      case 'partially_delivered':
        return 'Kısmi Teslim'
      case 'completed':
        return 'Tamamlandı'
      case 'cancelled':
        return 'İptal Edildi'
      default:
        return status
    }
  }

  const handleEditPackage = (pkg: any) => {
    setSelectedPackage(pkg)
    setIsEditPackageModalOpen(true)
  }

  const handleSavePackage = async (packageData: any) => {
    console.log('Saving package:', packageData)
    // Here you would update the package in your backend
    alert('Paket başarıyla güncellendi!')
    setIsEditPackageModalOpen(false)
  }

  return (
    <div className="space-y-6">
      {/* Order Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl flex items-center gap-2">
              <Package className="w-6 h-6" />
              Çoklu Teslimat Siparişi #{order.id}
            </CardTitle>
            <Badge className={getStatusColor(order.status)}>
              {getStatusText(order.status)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Package className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-blue-800">Toplam Miktar</span>
              </div>
              <p className="text-2xl font-bold text-blue-900">{order.totalQuantity} m²</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="w-5 h-5 text-green-600" />
                <span className="font-medium text-green-800">Toplam Tutar</span>
              </div>
              <p className="text-2xl font-bold text-green-900">${order.totalAmount.toLocaleString()}</p>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Truck className="w-5 h-5 text-purple-600" />
                <span className="font-medium text-purple-800">Teslimat Paketleri</span>
              </div>
              <p className="text-2xl font-bold text-purple-900">{order.deliveryPackages.length}</p>
            </div>
            
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="w-5 h-5 text-orange-600" />
                <span className="font-medium text-orange-800">Tahmini Bitiş</span>
              </div>
              <p className="text-lg font-bold text-orange-900">{formatDate(order.estimatedCompletionDate)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Genel İlerleme
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Tamamlanan Paketler</span>
                <span className="text-sm text-gray-600">
                  {overallProgress.completedPackages}/{overallProgress.totalPackages} paket
                </span>
              </div>
              <Progress value={overallProgress.overallPercentage} className="h-3" />
              <div className="text-sm text-gray-500 mt-1">
                %{overallProgress.overallPercentage} tamamlandı
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-sm">
                  <span className="font-medium">{overallProgress.completedPackages}</span> Tamamlandı
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-blue-600" />
                <span className="text-sm">
                  <span className="font-medium">{overallProgress.inProgressPackages}</span> Üretimde
                </span>
              </div>
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-gray-600" />
                <span className="text-sm">
                  <span className="font-medium">{overallProgress.pendingPackages}</span> Bekliyor
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Notes */}
      {order.notes && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Sipariş Notları</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">{order.notes}</p>
          </CardContent>
        </Card>
      )}

      {/* Navigation Tabs */}
      <div className="flex gap-4 border-b">
        <Button
          variant="ghost"
          className="border-b-2 border-blue-600 text-blue-600"
        >
          Üretim Takibi
        </Button>
        <Button
          variant="ghost"
          onClick={() => window.location.href = '/producer/orders/multi-delivery/delivery'}
        >
          Teslimat Takvimi
        </Button>
        <Button
          variant="ghost"
          onClick={() => window.location.href = '/producer/orders/multi-delivery/payments'}
        >
          Ödeme Takibi
        </Button>
      </div>

      {/* Delivery Packages */}
      <div>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Teslimat Paketleri - Üretim Durumu</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {order.deliveryPackages
            .sort((a, b) => a.packageNumber - b.packageNumber)
            .map((pkg) => (
              <ProductionScheduleCard
                key={pkg.id}
                deliveryPackage={pkg}
                onUpdateStage={onUpdateStage}
                onPauseProduction={onPauseProduction}
                onViewDetails={onViewPackageDetails}
                onEditPackage={handleEditPackage}
              />
            ))}
        </div>
      </div>

      {/* Edit Package Modal */}
      <EditPackageModal
        isOpen={isEditPackageModalOpen}
        onClose={() => setIsEditPackageModalOpen(false)}
        package={selectedPackage}
        onSavePackage={handleSavePackage}
      />
    </div>
  )
}
