'use client'

import * as React from 'react'
import { Dialog, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Pause, AlertTriangle, Clock } from 'lucide-react'

interface ProductionPauseModalProps {
  isOpen: boolean
  onClose: () => void
  order: any
}

const pauseReasons = [
  { value: 'material_shortage', label: 'Malzeme Eksikliği' },
  { value: 'equipment_maintenance', label: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: 'quality_issue', label: 'Kalite Sorunu' },
  { value: 'customer_request', label: 'Müşteri Talebi' },
  { value: 'technical_problem', label: 'Teknik Sorun' },
  { value: 'other', label: 'Diğer' }
]

export function ProductionPauseModal({
  isOpen,
  onClose,
  order
}: ProductionPauseModalProps) {
  const [reason, setReason] = React.useState('')
  const [notes, setNotes] = React.useState('')
  const [estimatedResumeDays, setEstimatedResumeDays] = React.useState('')
  const [isLoading, setIsLoading] = React.useState(false)

  const handlePauseProduction = async () => {
    if (!reason) {
      alert('Lütfen duraklama sebebini seçin')
      return
    }

    if (reason === 'other' && !notes.trim()) {
      alert('Diğer seçeneği için açıklama zorunludur')
      return
    }

    setIsLoading(true)
    try {
      // Here you would pause the production in your backend
      console.log('Pausing production:', {
        orderId: order?.id,
        reason,
        notes,
        estimatedResumeDays
      })

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Show success message
      alert('Üretim başarıyla duraklatıldı!')
      
      // Reset form and close modal
      setReason('')
      setNotes('')
      setEstimatedResumeDays('')
      onClose()
    } catch (error) {
      console.error('Error pausing production:', error)
      alert('Üretim duraklatılırken bir hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setReason('')
    setNotes('')
    setEstimatedResumeDays('')
    onClose()
  }

  if (!order) return null

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Pause className="w-5 h-5 text-orange-600" />
            Üretimi Duraklat
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Order Info */}
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <span className="font-medium">Sipariş:</span>
              <span>#{order.id}</span>
              <span className="text-gray-500">-</span>
              <span>{order.customerName}</span>
            </div>
            <div className="text-sm text-gray-600 mt-1">
              {order.productName}
            </div>
          </div>

          {/* Warning */}
          <div className="flex items-start gap-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-orange-800">Dikkat!</p>
              <p className="text-orange-700 mt-1">
                Üretimi duraklatmak teslimat süresini etkileyebilir. Müşteri otomatik olarak bilgilendirilecektir.
              </p>
            </div>
          </div>

          {/* Pause Reason */}
          <div>
            <Label htmlFor="reason" className="text-sm font-medium text-gray-700">
              Duraklama Sebebi *
            </Label>
            <Select value={reason} onValueChange={setReason}>
              <SelectTrigger className="mt-1 h-12 text-sm">
                <SelectValue placeholder="Duraklama sebebini seçin" />
              </SelectTrigger>
              <SelectContent className="z-[100] max-h-60 overflow-y-auto" position="popper" sideOffset={4}>
                {pauseReasons.map((pauseReason) => (
                  <SelectItem
                    key={pauseReason.value}
                    value={pauseReason.value}
                    className="py-3 px-3 text-sm cursor-pointer hover:bg-gray-100 focus:bg-gray-100"
                  >
                    {pauseReason.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Estimated Resume Days */}
          <div>
            <Label htmlFor="estimatedDays" className="text-sm font-medium text-gray-700">
              Tahmini Duraklatma Günü
            </Label>
            <Select value={estimatedResumeDays} onValueChange={setEstimatedResumeDays}>
              <SelectTrigger className="mt-1 h-12 text-sm">
                <SelectValue placeholder="Tahmini süre seçin" />
              </SelectTrigger>
              <SelectContent className="z-[100] max-h-60 overflow-y-auto" position="popper" sideOffset={4}>
                <SelectItem value="1" className="py-3 px-3 text-sm cursor-pointer hover:bg-gray-100 focus:bg-gray-100">1 Gün</SelectItem>
                <SelectItem value="2" className="py-3 px-3 text-sm cursor-pointer hover:bg-gray-100 focus:bg-gray-100">2 Gün</SelectItem>
                <SelectItem value="3" className="py-3 px-3 text-sm cursor-pointer hover:bg-gray-100 focus:bg-gray-100">3 Gün</SelectItem>
                <SelectItem value="5" className="py-3 px-3 text-sm cursor-pointer hover:bg-gray-100 focus:bg-gray-100">5 Gün</SelectItem>
                <SelectItem value="7" className="py-3 px-3 text-sm cursor-pointer hover:bg-gray-100 focus:bg-gray-100">1 Hafta</SelectItem>
                <SelectItem value="14" className="py-3 px-3 text-sm cursor-pointer hover:bg-gray-100 focus:bg-gray-100">2 Hafta</SelectItem>
                <SelectItem value="30" className="py-3 px-3 text-sm cursor-pointer hover:bg-gray-100 focus:bg-gray-100">1 Ay</SelectItem>
                <SelectItem value="unknown" className="py-3 px-3 text-sm cursor-pointer hover:bg-gray-100 focus:bg-gray-100">Belirsiz</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Additional Notes */}
          <div>
            <Label htmlFor="notes" className="text-sm font-medium text-gray-700">
              {reason === 'other' ? 'Açıklama *' : 'Ek Açıklama'}
            </Label>
            <Textarea
              id="notes"
              placeholder={reason === 'other' ? 'Duraklama sebebini açıklayın...' : 'Duraklama ile ilgili ek bilgiler...'}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              className={`mt-1 ${reason === 'other' ? 'border-orange-300 focus:border-orange-500' : ''}`}
              disabled={isLoading}
            />
            {reason === 'other' && (
              <p className="text-xs text-orange-600 mt-1">
                Diğer seçeneği için açıklama zorunludur
              </p>
            )}
          </div>

          <div className="flex items-center justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              İptal
            </Button>
            <Button
              onClick={handlePauseProduction}
              disabled={!reason || (reason === 'other' && !notes.trim()) || isLoading}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Duraklatılıyor...
                </>
              ) : (
                <>
                  <Pause className="w-4 h-4 mr-2" />
                  Üretimi Duraklat
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
