'use client';

import React, { useState } from 'react';
import { AdvancedProductViewer } from '../../../components/3d/AdvancedProductViewer';
import {
  DimensionConfiguration,
  SurfaceFinishConfiguration,
  RoomSimulationConfiguration,
  AdvancedProductConfig,
  SurfaceFinishName
} from '../../../types/3d';
import { 
  Sparkles, 
  Zap, 
  Info,
  Settings,
  Eye,
  Home
} from 'lucide-react';

// Demo configuration data
const DEMO_DIMENSION_CONFIG: DimensionConfiguration = {
  standardSizes: [
    { width: 30, height: 60, thickness: 2, label: '30x60x2', category: 'floor', applications: ['banyo', 'mutfak'] },
    { width: 60, height: 60, thickness: 2, label: '60x60x2', category: 'floor', applications: ['salon', 'yatak odası'] },
    { width: 80, height: 80, thickness: 3, label: '80x80x3', category: 'floor', applications: ['salon', 'dış mekan'] },
    { width: 25, height: 40, thickness: 1, label: '25x40x1', category: 'wall', applications: ['banyo', 'mutfak'] },
    { width: 30, height: 60, thickness: 1.5, label: '30x60x1.5', category: 'wall', applications: ['banyo', 'salon'] },
    { width: 200, height: 100, thickness: 3, label: '200x100x3', category: 'countertop', applications: ['mutfak', 'banyo'] }
  ],
  customSize: {
    enabled: true,
    minWidth: 10,
    maxWidth: 300,
    minHeight: 10,
    maxHeight: 300,
    thicknessOptions: [1, 1.5, 2, 3, 4, 5],
    validation: {
      aspectRatio: { min: 0.2, max: 5.0 },
      structuralLimits: true,
      productionFeasibility: true
    }
  },
  pricing: {
    baseCalculation: 'area',
    wastageMultiplier: 1.1,
    cuttingCost: 5,
    customSizePremium: 0.15
  }
};

const DEMO_SURFACE_FINISH_CONFIG: SurfaceFinishConfiguration = {
  availableFinishes: [
    {
      name: 'ham' as SurfaceFinishName,
      description: 'Doğal yüzey, işlenmemiş',
      roughness: 0.9,
      metallic: 0.0,
      normalIntensity: 1.0,
      priceMultiplier: 1.0,
      shaderUniforms: {}
    },
    {
      name: 'honlu' as SurfaceFinishName,
      description: 'Mat yüzey, pürüzsüz',
      roughness: 0.6,
      metallic: 0.0,
      normalIntensity: 0.3,
      priceMultiplier: 1.2,
      shaderUniforms: {}
    },
    {
      name: 'cilali' as SurfaceFinishName,
      description: 'Parlak yüzey, ayna gibi',
      roughness: 0.1,
      metallic: 0.0,
      normalIntensity: 0.1,
      priceMultiplier: 1.5,
      shaderUniforms: {}
    },
    {
      name: 'fircinlanmis' as SurfaceFinishName,
      description: 'Fırçalanmış doku',
      roughness: 0.7,
      metallic: 0.0,
      normalIntensity: 0.8,
      priceMultiplier: 1.3,
      shaderUniforms: {}
    },
    {
      name: 'yakma' as SurfaceFinishName,
      description: 'Alev ile yakılmış yüzey',
      roughness: 0.8,
      metallic: 0.0,
      normalIntensity: 1.2,
      priceMultiplier: 1.4,
      shaderUniforms: {}
    },
    {
      name: 'eskitme' as SurfaceFinishName,
      description: 'Antik görünüm',
      roughness: 0.9,
      metallic: 0.0,
      normalIntensity: 1.1,
      priceMultiplier: 1.6,
      shaderUniforms: {}
    }
  ],
  defaultFinish: 'ham' as SurfaceFinishName,
  transitionAnimation: {
    duration: 1000,
    easing: 'ease-in-out',
    steps: 30
  }
};

const DEMO_ROOM_CONFIG: RoomSimulationConfiguration = {
  roomTemplates: [
    {
      id: 'modern-bathroom',
      name: 'Modern Banyo',
      type: 'bathroom',
      style: 'modern',
      dimensions: { width: 250, height: 300, depth: 200 },
      fixtures: ['shower', 'toilet', 'sink', 'mirror'],
      lighting: 'bright_white',
      humidity: 'high',
      applications: ['floor', 'wall', 'shower_wall']
    },
    {
      id: 'modern-kitchen',
      name: 'Modern Mutfak',
      type: 'kitchen',
      style: 'modern',
      dimensions: { width: 400, height: 300, depth: 600 },
      fixtures: ['island', 'cabinets', 'appliances', 'window'],
      lighting: 'task_lighting',
      humidity: 'medium',
      applications: ['floor', 'backsplash', 'countertop']
    },
    {
      id: 'contemporary-living',
      name: 'Çağdaş Salon',
      type: 'living',
      style: 'modern',
      dimensions: { width: 500, height: 300, depth: 600 },
      fixtures: ['sofa', 'tv_unit', 'coffee_table', 'fireplace'],
      lighting: 'ambient',
      humidity: 'low',
      applications: ['floor', 'accent_wall', 'fireplace']
    }
  ],
  tilingPatterns: [
    {
      name: 'Standart',
      description: 'Düz sıralı döşeme',
      algorithm: 'grid',
      parameters: { offsetX: 0, offsetY: 0, rotation: 0 }
    },
    {
      name: 'Satranç',
      description: 'Alternatif renk/desen',
      algorithm: 'checkerboard',
      parameters: { alternatePattern: true }
    },
    {
      name: 'Balık Kılçığı',
      description: '45° açılı zigzag',
      algorithm: 'herringbone',
      parameters: { angle: 45, spacing: 2 }
    },
    {
      name: 'Çapraz',
      description: '45° açılı döşeme',
      algorithm: 'diagonal',
      parameters: { angle: 45, offsetX: 0.5, offsetY: 0.5 }
    }
  ],
  groutOptions: [
    { width: 1, color: '#f5f5f5', material: 'standard' },
    { width: 2, color: '#e5e5e5', material: 'standard' },
    { width: 3, color: '#d5d5d5', material: 'epoxy' },
    { width: 1, color: '#8b4513', material: 'epoxy' },
    { width: 2, color: '#2c2c2c', material: 'urethane' }
  ]
};

export default function AdvancedViewerDemo() {
  const [currentConfig, setCurrentConfig] = useState<AdvancedProductConfig>({
    productId: 'demo-marble-carrara',
    dimensions: { width: 60, height: 60, thickness: 2 },
    surfaceFinish: 'ham' as SurfaceFinishName
  });


  const [showInfo, setShowInfo] = useState(true);

  const handleConfigChange = (config: AdvancedProductConfig) => {
    setCurrentConfig(config);
    console.log('Configuration changed:', config);
  };

  // Removed price update functionality

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Sparkles className="w-8 h-8 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900">
                  Gelişmiş 3D Görselleştirme
                </h1>
              </div>
              <div className="hidden sm:flex items-center space-x-2 text-sm text-gray-500">
                <span>RFC-801 Demo</span>
                <span>•</span>
                <span>Stoneline + AGT Özellikleri</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowInfo(!showInfo)}
                className={`p-2 rounded-lg transition-colors ${
                  showInfo 
                    ? 'bg-blue-100 text-blue-600' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                title="Bilgi Paneli"
              >
                <Info className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Info Panel */}
          {showInfo && (
            <div className="xl:col-span-1">
              <InfoPanel
                config={currentConfig}
              />
            </div>
          )}

          {/* Main Viewer */}
          <div className={showInfo ? 'xl:col-span-3' : 'xl:col-span-4'}>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <AdvancedProductViewer
                productId="demo-marble-carrara"
                initialConfig={currentConfig}
                dimensionConfig={DEMO_DIMENSION_CONFIG}
                surfaceFinishConfig={DEMO_SURFACE_FINISH_CONFIG}
                roomConfig={DEMO_ROOM_CONFIG}
                onConfigChange={handleConfigChange}
                width={showInfo ? 800 : 1000}
                height={600}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Info Panel Component
interface InfoPanelProps {
  config: AdvancedProductConfig;
}

const InfoPanel: React.FC<InfoPanelProps> = ({ config }) => {
  return (
    <div className="space-y-6">
      {/* Current Configuration */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Settings className="w-5 h-5 mr-2 text-blue-600" />
          Mevcut Konfigürasyon
        </h3>
        
        <div className="space-y-3">
          <div>
            <span className="text-sm font-medium text-gray-700">Ürün ID:</span>
            <p className="text-sm text-gray-600">{config.productId}</p>
          </div>
          
          <div>
            <span className="text-sm font-medium text-gray-700">Ebat:</span>
            <p className="text-sm text-gray-600">
              {config.dimensions.width} × {config.dimensions.height} × {config.dimensions.thickness} cm
            </p>
          </div>
          
          <div>
            <span className="text-sm font-medium text-gray-700">Yüzey İşlemi:</span>
            <p className="text-sm text-gray-600 capitalize">{config.surfaceFinish}</p>
          </div>
          
          {config.room && (
            <div>
              <span className="text-sm font-medium text-gray-700">Mekan:</span>
              <p className="text-sm text-gray-600">{config.room.name}</p>
            </div>
          )}
          
          {config.pattern && (
            <div>
              <span className="text-sm font-medium text-gray-700">Desen:</span>
              <p className="text-sm text-gray-600">{config.pattern.name}</p>
            </div>
          )}
        </div>
      </div>

      {/* Removed price information */}

      {/* Features */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Eye className="w-5 h-5 mr-2 text-purple-600" />
          Özellikler
        </h3>
        
        <div className="space-y-3">
          <FeatureItem 
            title="Ebat Konfigüratörü"
            description="Standart ve özel ebat seçimi"
            status="active"
          />
          <FeatureItem 
            title="Yüzey İşlemi Simülatörü"
            description="8 farklı yüzey işlemi"
            status="active"
          />
          <FeatureItem 
            title="Sanal Mekan Simülasyonu"
            description="Banyo, mutfak, salon şablonları"
            status="active"
          />
          <FeatureItem 
            title="PBR Rendering"
            description="Gerçekçi materyal gösterimi"
            status="active"
          />
          <FeatureItem 
            title="Dinamik Fiyatlandırma"
            description="Gerçek zamanlı fiyat hesaplama"
            status="active"
          />
          <FeatureItem 
            title="Performans Optimizasyonu"
            description="LOD sistemi ve adaptif kalite"
            status="active"
          />
        </div>
      </div>
    </div>
  );
};

// Feature Item Component
interface FeatureItemProps {
  title: string;
  description: string;
  status: 'active' | 'inactive' | 'coming-soon';
}

const FeatureItem: React.FC<FeatureItemProps> = ({ title, description, status }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'coming-soon': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'inactive': return 'Pasif';
      case 'coming-soon': return 'Yakında';
      default: return 'Bilinmiyor';
    }
  };

  return (
    <div className="flex items-start space-x-3">
      <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor()}`}>
        {getStatusText()}
      </div>
      <div className="flex-1">
        <h4 className="text-sm font-medium text-gray-900">{title}</h4>
        <p className="text-xs text-gray-500">{description}</p>
      </div>
    </div>
  );
};
