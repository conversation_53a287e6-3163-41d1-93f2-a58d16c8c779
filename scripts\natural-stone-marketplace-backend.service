[Unit]
Description=Natural Stone Marketplace Backend API
Documentation=https://github.com/your-org/natural-stone-marketplace
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=simple
User=deploy
Group=deploy
WorkingDirectory=/var/www/natural-stone-marketplace/backend
Environment=NODE_ENV=production
Environment=PORT=3000
EnvironmentFile=/var/www/natural-stone-marketplace/backend/.env
ExecStart=/usr/bin/node dist/index.js
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=nsm-backend

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/natural-stone-marketplace/backend/uploads
ReadWritePaths=/var/log/natural-stone-marketplace
ReadWritePaths=/tmp

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Health check
TimeoutStartSec=60
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
