import { SettingsModule } from '../core/SettingsModule';
import { SettingsCategory } from '../types/settings.types';

export class BusinessRulesModule extends SettingsModule {
  protected category: SettingsCategory = 'BUSINESS';

  protected getDefaultSettings() {
    return {
      commissionRate: {
        key: 'commission_rate',
        value: 0.05, // 5%
        type: 'number',
        category: this.category,
        description: 'Platform commission rate (decimal)'
      },
      minimumOrderAmount: {
        key: 'minimum_order_amount',
        value: 100,
        type: 'number',
        category: this.category,
        description: 'Minimum order amount in USD'
      },
      maxOrderAmount: {
        key: 'max_order_amount',
        value: 1000000,
        type: 'number',
        category: this.category,
        description: 'Maximum order amount in USD'
      },
      autoApproveOrders: {
        key: 'auto_approve_orders',
        value: false,
        type: 'boolean',
        category: this.category,
        description: 'Automatically approve orders'
      },
      requireProducerVerification: {
        key: 'require_producer_verification',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Require producer verification before listing products'
      },
      allowGuestOrders: {
        key: 'allow_guest_orders',
        value: false,
        type: 'boolean',
        category: this.category,
        description: 'Allow guest users to place orders'
      },
      defaultPaymentTerms: {
        key: 'default_payment_terms',
        value: 'NET_30',
        type: 'string',
        category: this.category,
        description: 'Default payment terms'
      },
      escrowEnabled: {
        key: 'escrow_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable escrow service'
      },
      escrowFeeRate: {
        key: 'escrow_fee_rate',
        value: 0.02, // 2%
        type: 'number',
        category: this.category,
        description: 'Escrow service fee rate (decimal)'
      },
      disputeResolutionDays: {
        key: 'dispute_resolution_days',
        value: 14,
        type: 'number',
        category: this.category,
        description: 'Days to resolve disputes'
      },
      returnPolicyDays: {
        key: 'return_policy_days',
        value: 30,
        type: 'number',
        category: this.category,
        description: 'Return policy period in days'
      },
      qualityGuaranteeEnabled: {
        key: 'quality_guarantee_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable quality guarantee'
      },
      maxProductsPerProducer: {
        key: 'max_products_per_producer',
        value: 1000,
        type: 'number',
        category: this.category,
        description: 'Maximum products per producer'
      },
      productApprovalRequired: {
        key: 'product_approval_required',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Require admin approval for new products'
      },
      allowBulkOrders: {
        key: 'allow_bulk_orders',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Allow bulk order requests'
      },
      bulkOrderMinQuantity: {
        key: 'bulk_order_min_quantity',
        value: 100,
        type: 'number',
        category: this.category,
        description: 'Minimum quantity for bulk orders'
      }
    };
  }

  async validateSetting(key: string, value: any): Promise<boolean> {
    switch (key) {
      case 'commission_rate':
      case 'escrow_fee_rate':
        return typeof value === 'number' && value >= 0 && value <= 1;
      case 'minimum_order_amount':
      case 'max_order_amount':
      case 'dispute_resolution_days':
      case 'return_policy_days':
      case 'max_products_per_producer':
      case 'bulk_order_min_quantity':
        return typeof value === 'number' && value > 0;
      case 'auto_approve_orders':
      case 'require_producer_verification':
      case 'allow_guest_orders':
      case 'escrow_enabled':
      case 'quality_guarantee_enabled':
      case 'product_approval_required':
      case 'allow_bulk_orders':
        return typeof value === 'boolean';
      case 'default_payment_terms':
        return typeof value === 'string' && ['NET_30', 'NET_60', 'IMMEDIATE', 'COD'].includes(value);
      default:
        return true;
    }
  }
}
