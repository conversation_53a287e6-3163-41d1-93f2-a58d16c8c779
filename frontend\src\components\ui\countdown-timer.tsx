import * as React from "react"
import { cn } from "@/lib/utils"

export interface CountdownTimerProps extends React.HTMLAttributes<HTMLDivElement> {
  targetDate: Date | string
  onExpire?: () => void
  showLabels?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'urgent' | 'minimal'
}

interface TimeLeft {
  days: number
  hours: number
  minutes: number
  seconds: number
}

/**
 * CountdownTimer component following RFC-004 UI/UX Design System
 * Countdown timer for bidding deadlines and time-sensitive actions
 */
const CountdownTimer = React.forwardRef<HTMLDivElement, CountdownTimerProps>(
  ({ 
    className, 
    targetDate,
    onExpire,
    showLabels = true,
    size = 'md',
    variant = 'default',
    ...props 
  }, ref) => {
    
    const [timeLeft, setTimeLeft] = React.useState<TimeLeft>({
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0
    })
    
    const [isExpired, setIsExpired] = React.useState(false)
    
    const calculateTimeLeft = React.useCallback(() => {
      const target = new Date(targetDate).getTime()
      const now = new Date().getTime()
      const difference = target - now
      
      if (difference <= 0) {
        setIsExpired(true)
        onExpire?.()
        return {
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0
        }
      }
      
      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
        minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((difference % (1000 * 60)) / 1000)
      }
    }, [targetDate, onExpire])
    
    React.useEffect(() => {
      const timer = setInterval(() => {
        setTimeLeft(calculateTimeLeft())
      }, 1000)
      
      // Initial calculation
      setTimeLeft(calculateTimeLeft())
      
      return () => clearInterval(timer)
    }, [calculateTimeLeft])
    
    const getSizeStyles = () => {
      switch (size) {
        case 'sm':
          return {
            container: "gap-2 p-2",
            number: "text-lg",
            label: "text-xs",
            separator: "text-base"
          }
        case 'lg':
          return {
            container: "gap-6 p-6",
            number: "text-3xl",
            label: "text-sm",
            separator: "text-2xl"
          }
        default:
          return {
            container: "gap-4 p-4",
            number: "text-2xl",
            label: "text-xs",
            separator: "text-xl"
          }
      }
    }
    
    const getVariantStyles = () => {
      switch (variant) {
        case 'urgent':
          return {
            background: "bg-[var(--error)] text-white",
            border: "border-red-600"
          }
        case 'minimal':
          return {
            background: "bg-transparent text-[var(--text-primary)]",
            border: "border-transparent"
          }
        default:
          return {
            background: "bg-[var(--gray-900)] text-white",
            border: "border-[var(--gray-700)]"
          }
      }
    }
    
    const styles = getSizeStyles()
    const variantStyles = getVariantStyles()
    
    const isUrgent = timeLeft.days === 0 && timeLeft.hours < 2
    
    if (isExpired) {
      return (
        <div
          ref={ref}
          className={cn(
            "flex items-center justify-center rounded-[var(--radius-lg)]",
            "bg-[var(--error)] text-white",
            styles.container,
            className
          )}
          {...props}
        >
          <span className={cn("font-bold", styles.number)}>
            Süre Doldu
          </span>
        </div>
      )
    }
    
    const timeUnits = [
      { value: timeLeft.days, label: 'Gün', show: timeLeft.days > 0 },
      { value: timeLeft.hours, label: 'Saat', show: true },
      { value: timeLeft.minutes, label: 'Dakika', show: true },
      { value: timeLeft.seconds, label: 'Saniye', show: true }
    ].filter(unit => unit.show)

    return (
      <div
        ref={ref}
        className={cn(
          "flex items-center justify-center rounded-[var(--radius-lg)]",
          "font-mono border",
          variantStyles.background,
          variantStyles.border,
          isUrgent && variant === 'default' && "bg-[var(--error)] border-red-600 animate-pulse",
          styles.container,
          className
        )}
        {...props}
      >
        {timeUnits.map((unit, index) => (
          <React.Fragment key={unit.label}>
            <div className="flex flex-col items-center min-w-0">
              <span className={cn(
                "font-bold leading-none tabular-nums",
                styles.number
              )}>
                {unit.value.toString().padStart(2, '0')}
              </span>
              {showLabels && (
                <span className={cn(
                  "uppercase tracking-wide opacity-70 leading-none mt-1",
                  styles.label
                )}>
                  {unit.label}
                </span>
              )}
            </div>
            
            {index < timeUnits.length - 1 && (
              <span className={cn(
                "font-bold opacity-50 animate-blink",
                styles.separator
              )}>
                :
              </span>
            )}
          </React.Fragment>
        ))}
      </div>
    )
  }
)

CountdownTimer.displayName = "CountdownTimer"

// Blink animation for separators
const blinkKeyframes = `
  @keyframes blink {
    0%, 50% { opacity: 0.5; }
    51%, 100% { opacity: 0; }
  }
  .animate-blink {
    animation: blink 1s infinite;
  }
`

// Inject keyframes into document head
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = blinkKeyframes
  document.head.appendChild(style)
}

export { CountdownTimer }
