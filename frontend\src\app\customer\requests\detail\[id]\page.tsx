'use client';

import React from 'react';
import { useParams, useRouter } from 'next/navigation';
import RequestDetailPage from '@/components/dashboard/pages/RequestDetailPage';

export default function RequestDetailRoute() {
  const params = useParams();
  const router = useRouter();
  const requestId = params.id as string;

  const handleNavigate = (route: string) => {
    router.push(route);
  };

  return (
    <RequestDetailPage 
      requestId={requestId} 
      onNavigate={handleNavigate}
    />
  );
}
