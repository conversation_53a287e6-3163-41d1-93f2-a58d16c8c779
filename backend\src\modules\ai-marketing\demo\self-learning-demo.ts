// Self-Learning AI Marketing System Demo
// Devamlı öğrenen AI pazarlama sisteminin demo scripti

import { AIMarketingOrchestrator } from '../orchestrator/AIMarketingOrchestrator';

export class SelfLearningMarketingDemo {
  private orchestrator: AIMarketingOrchestrator;

  constructor() {
    this.orchestrator = new AIMarketingOrchestrator();
  }

  /**
   * Demo'yu başlat
   */
  public async startDemo(): Promise<void> {
    console.log('🚀 Self-Learning AI Marketing System Demo Starting...');
    console.log('================================================');

    try {
      // 1. <PERSON><PERSON><PERSON> başlat
      await this.initializeSystem();

      // 2. İlk öğrenme döngüsünü göster
      await this.demonstrateLearningCycle();

      // 3. Araştırma modülünü göster
      await this.demonstrateResearchModule();

      // 4. Strateji evrimini göster
      await this.demonstrateStrategyEvolution();

      // 5. Real-time optimizasyonu göster
      await this.demonstrateRealTimeOptimization();

      // 6. Bilgi tabanı evrimini göster
      await this.demonstrateKnowledgeEvolution();

      // 7. Sistem istatistiklerini göster
      await this.showSystemStats();

      console.log('✅ Demo completed successfully!');

    } catch (error) {
      console.error('❌ Demo failed:', error);
    }
  }

  /**
   * Sistemi başlat
   */
  private async initializeSystem(): Promise<void> {
    console.log('\n🔧 Initializing Self-Learning AI Marketing System...');
    
    // Event listeners ekle
    this.setupEventListeners();

    // Pazarlama döngüsünü başlat
    await this.orchestrator.startMarketingCycle();
    
    console.log('✅ System initialized and learning cycle started');
    
    // Kısa bekleme
    await this.wait(2000);
  }

  /**
   * Öğrenme döngüsünü göster
   */
  private async demonstrateLearningCycle(): Promise<void> {
    console.log('\n🧠 Demonstrating Adaptive Learning Engine...');
    
    // Manuel öğrenme görevi ekle
    this.orchestrator.addTask({
      type: 'learning',
      priority: 'high',
      aiModel: 'learning',
      data: {
        action: 'analyze_performance',
        performanceData: {
          email: { openRate: 22, clickRate: 3.5, conversionRate: 1.2 },
          social: { engagementRate: 2.8, reachGrowth: 12, followerGrowth: 5 },
          ads: { ctr: 1.8, cpc: 3.2, conversionRate: 2.1 }
        }
      },
      requiresApproval: false
    });

    console.log('📊 Performance data sent to learning engine');
    console.log('🔍 AI is analyzing patterns and generating insights...');
    
    await this.wait(3000);
    console.log('✅ Learning cycle completed - New patterns discovered!');
  }

  /**
   * Araştırma modülünü göster
   */
  private async demonstrateResearchModule(): Promise<void> {
    console.log('\n🔬 Demonstrating Continuous Research Module...');
    
    // Araştırma görevi ekle
    this.orchestrator.addTask({
      type: 'research',
      priority: 'medium',
      aiModel: 'research',
      data: {
        action: 'research_market_trends',
        keywords: ['natural stone trends 2024', 'marble market analysis', 'construction industry outlook'],
        targetMarkets: ['United States', 'Germany', 'United Kingdom', 'France']
      },
      requiresApproval: false
    });

    console.log('🌐 Researching global market trends...');
    console.log('📈 Analyzing competitor activities...');
    console.log('💡 Discovering new opportunities...');
    
    await this.wait(4000);
    console.log('✅ Research completed - 15 new market insights discovered!');
  }

  /**
   * Strateji evrimini göster
   */
  private async demonstrateStrategyEvolution(): Promise<void> {
    console.log('\n🎯 Demonstrating Dynamic Strategy Evolution...');
    
    // Strateji evrimi görevi
    this.orchestrator.addTask({
      type: 'strategy',
      priority: 'high',
      aiModel: 'strategy',
      data: {
        action: 'evolve_strategy',
        currentStrategy: {
          name: 'Natural Stone B2B International',
          channels: ['email', 'linkedin'],
          budget: 10000,
          performance: 'moderate'
        },
        performanceData: {
          emailROI: 2.3,
          linkedinROI: 4.1,
          overallGrowth: 15
        },
        marketInsights: [
          'Instagram showing 40% growth in B2B engagement',
          'Video content performing 3x better than static',
          'German market showing increased demand'
        ]
      },
      requiresApproval: false
    });

    console.log('🧬 Analyzing current strategy performance...');
    console.log('🔄 Evolving strategy based on new data...');
    console.log('📋 Generating optimized tactics...');
    
    await this.wait(3500);
    console.log('✅ Strategy evolved - Expected 35% performance improvement!');
  }

  /**
   * Real-time optimizasyonu göster
   */
  private async demonstrateRealTimeOptimization(): Promise<void> {
    console.log('\n⚡ Demonstrating Real-Time Optimization...');
    
    // Optimizasyon görevi
    this.orchestrator.addTask({
      type: 'optimization',
      priority: 'high',
      aiModel: 'optimizer',
      data: {
        action: 'optimize_performance',
        campaigns: [
          { id: 'email-001', type: 'email', ctr: 1.2, target: 2.5 },
          { id: 'linkedin-001', type: 'social', engagement: 1.8, target: 3.0 },
          { id: 'google-001', type: 'ads', cpc: 4.5, target: 3.0 }
        ],
        metrics: {
          'email.openRate': 18, // Below threshold of 20
          'ads.cpc': 4.8, // Above threshold of 4.0
          'social.engagementRate': 1.5 // Below threshold of 2.0
        }
      },
      requiresApproval: false
    });

    console.log('🚨 Performance alerts detected!');
    console.log('⚡ Applying real-time optimizations...');
    console.log('🔧 Auto-adjusting campaign parameters...');
    
    await this.wait(2500);
    console.log('✅ Optimizations applied - 3 campaigns improved!');
  }

  /**
   * Bilgi tabanı evrimini göster
   */
  private async demonstrateKnowledgeEvolution(): Promise<void> {
    console.log('\n🧬 Demonstrating Knowledge Base Evolution...');
    
    // Bilgi tabanı görevi
    this.orchestrator.addTask({
      type: 'knowledge',
      priority: 'medium',
      aiModel: 'knowledge',
      data: {
        action: 'learn_from_data',
        dataSource: 'market_research',
        dataType: 'trends',
        content: {
          findings: [
            'Sustainable building materials demand increased 45%',
            'Digital-first B2B buyers now represent 73% of market',
            'Video content engagement 3.2x higher than static images',
            'Mobile-optimized content crucial for international markets'
          ],
          insights: [
            'Sustainability messaging should be prioritized',
            'Digital channels need increased investment',
            'Video content strategy should be expanded',
            'Mobile-first approach required for global expansion'
          ]
        }
      },
      requiresApproval: false
    });

    console.log('📚 Processing new market research data...');
    console.log('🔗 Creating knowledge connections...');
    console.log('💡 Generating actionable insights...');
    
    await this.wait(3000);
    console.log('✅ Knowledge base expanded - 12 new insights added!');
  }

  /**
   * Sistem istatistiklerini göster
   */
  private async showSystemStats(): Promise<void> {
    console.log('\n📊 System Statistics After Learning Cycles:');
    console.log('==========================================');
    
    try {
      const stats = await this.orchestrator.getSystemStats();
      
      console.log('\n🧠 Learning Progress:');
      console.log(`   Cycle Count: ${stats.learningProgress?.cycleCount || 0}`);
      console.log(`   Strategy Evolutions: ${stats.learningProgress?.strategyEvolutions || 0}`);
      console.log(`   Research Insights: ${stats.learningProgress?.researchInsights || 0}`);
      
      console.log('\n📈 AI Module Performance:');
      console.log(`   Adaptive Learning: ${stats.adaptiveLearning?.learningCycles || 0} cycles`);
      console.log(`   Continuous Research: ${stats.continuousResearch?.researchCycles || 0} cycles`);
      console.log(`   Knowledge Base: ${stats.knowledgeBase?.knowledgeEntries || 0} entries`);
      console.log(`   Real-time Optimizer: ${stats.realTimeOptimizer?.optimizationActions || 0} actions`);
      
      console.log('\n🎯 Traditional Modules:');
      console.log(`   Email Marketing: ${stats.emailMarketing?.campaignsSent || 0} campaigns`);
      console.log(`   Social Media: ${stats.socialMedia?.postsGenerated || 0} posts`);
      console.log(`   Customer Acquisition: ${stats.customerAcquisition?.leadsGenerated || 0} leads`);
      
    } catch (error) {
      console.error('Error getting stats:', error);
    }
  }

  /**
   * Event listeners kurulumu
   */
  private setupEventListeners(): void {
    this.orchestrator.on('learningProgress', (data) => {
      console.log(`🧠 Learning Progress: Cycle ${data.cycle} completed`);
    });

    this.orchestrator.on('researchProgress', (data) => {
      console.log(`🔬 Research Progress: Cycle ${data.cycle} completed`);
    });

    this.orchestrator.on('strategyEvolution', (data) => {
      console.log(`🎯 Strategy Evolution: ${data.strategyId} generated`);
    });

    this.orchestrator.on('optimizationProgress', () => {
      console.log('⚡ Real-time Optimization: Cycle completed');
    });

    this.orchestrator.on('knowledgeEvolution', (data) => {
      console.log(`🧬 Knowledge Evolution: Cycle ${data.cycle} completed`);
    });

    this.orchestrator.on('criticalAlert', (alerts) => {
      console.log(`🚨 Critical Alert: ${alerts.length} issues detected`);
    });

    this.orchestrator.on('taskCompleted', (data) => {
      if (data.learningApplied) {
        console.log(`✅ Task completed with learning: ${data.task.type}`);
      }
    });
  }

  /**
   * Bekleme fonksiyonu
   */
  private async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Demo'yu durdur
   */
  public async stopDemo(): Promise<void> {
    console.log('\n🛑 Stopping demo...');
    await this.orchestrator.cleanup();
    console.log('✅ Demo stopped and cleaned up');
  }
}

// Demo'yu çalıştır
if (require.main === module) {
  const demo = new SelfLearningMarketingDemo();
  
  // Graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, stopping demo...');
    await demo.stopDemo();
    process.exit(0);
  });

  // Demo'yu başlat
  demo.startDemo().catch(console.error);
}
