'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { useQuote } from '@/contexts/quote-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import {
  Clock,
  DollarSign,
  Calendar,
  User,
  Package,
  MessageSquare
} from 'lucide-react'
import { QuoteSubmissionModal } from '@/components/ui/quote-submission-modal'
import { MessageModal } from '@/components/ui/message-modal'
import { RequestDetailsModal } from '@/components/ui/request-details-modal'
import { MultiDeliveryQuoteModal } from '@/components/ui/multi-delivery-quote-modal'

// Pending requests are now loaded from quote context

export default function PendingQuoteRequests() {
  const { producer } = useProducerAuth()
  const { quoteRequests, quotes, isLoading } = useQuote()
  const [selectedRequest, setSelectedRequest] = React.useState<any>(null)
  const [isQuoteModalOpen, setIsQuoteModalOpen] = React.useState(false)
  const [isMessageModalOpen, setIsMessageModalOpen] = React.useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = React.useState(false)
  const [isMultiDeliveryQuoteModalOpen, setIsMultiDeliveryQuoteModalOpen] = React.useState(false)

  // Get pending requests (requests without quotes from this producer)
  const pendingRequests = React.useMemo(() => {
    return quoteRequests.filter(request => {
      // Check if this producer has NOT submitted a quote for this request
      const hasQuote = quotes.some(quote =>
        quote.quoteRequestId === request.id &&
        quote.producerId === producer?.id
      )
      return !hasQuote && request.status === 'pending'
    })
  }, [quoteRequests, quotes, producer?.id])

  const handleQuoteSubmit = async (quoteData: any) => {
    try {
      console.log('Submitting quote for request:', selectedRequest?.id, quoteData)
      // Here you would submit the quote to your API
      setIsQuoteModalOpen(false)
      setSelectedRequest(null)
      return true
    } catch (error) {
      console.error('Error submitting quote:', error)
      return false
    }
  }

  const handleSendMessage = (request: any) => {
    setSelectedRequest(request)
    setIsMessageModalOpen(true)
  }

  const handleViewDetails = (request: any) => {
    setSelectedRequest(request)
    setIsDetailsModalOpen(true)
  }

  const handleMultiDeliveryQuote = (request: any) => {
    setSelectedRequest(request)
    setIsMultiDeliveryQuoteModalOpen(true)
  }

  const handleSendMultiDeliveryQuote = async (quoteData: any) => {
    try {
      console.log('Sending multi-delivery quote:', quoteData)
      // Here you would send the quote to your API
      alert('Çoklu teslimat teklifi başarıyla gönderildi!')
      setIsMultiDeliveryQuoteModalOpen(false)
      setSelectedRequest(null)
      return true
    } catch (error) {
      console.error('Error sending multi-delivery quote:', error)
      alert('Teklif gönderilirken hata oluştu.')
      return false
    }
  }

  const shouldShowMultiDeliveryOption = (request: any) => {
    // Show multi-delivery option for large orders
    return request.quantity > 500 || (request.targetPrice * request.quantity) > 25000
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Bekleyen Teklif Talepleri</h1>
        <p className="text-gray-600">
          Henüz teklif vermediğiniz talepler
        </p>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
        </div>
      )}

      {/* Pending Requests List */}
      <div className="space-y-4">
        {pendingRequests.map((request) => (
          <Card key={request.id} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-yellow-600 bg-yellow-100">
                      <Clock className="w-4 h-4" />
                      Bekliyor
                    </div>
                    <span className="text-sm text-gray-500">#{request.id}</span>
                    <span className="text-xs text-red-600 font-medium">
                      Son tarih: {request.deadline}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <User className="w-4 h-4" />
                        Müşteri
                      </div>
                      <div className="font-medium">{request.customerName}</div>
                      <div className="text-sm text-gray-500">{request.customerEmail}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <Package className="w-4 h-4" />
                        Ürün Sayısı
                      </div>
                      <div className="font-medium">{request.products?.length || 0} ürün</div>
                      <div className="text-sm text-gray-500">
                        {request.products?.reduce((total: number, product: any) =>
                          total + product.specifications.reduce((sum: number, spec: any) => sum + parseFloat(spec.area || 0), 0), 0
                        ).toFixed(2) || 0} m² toplam
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <Calendar className="w-4 h-4" />
                        Tarihler
                      </div>
                      <div className="font-medium">Talep: {request.createdAt.toLocaleDateString('tr-TR')}</div>
                      <div className="text-sm text-red-600 font-medium">Son: {request.updatedAt.toLocaleDateString('tr-TR')}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <DollarSign className="w-4 h-4" />
                        Ortalama Hedef Fiyat
                      </div>
                      <div className="font-medium">
                        ${request.products?.reduce((total: number, product: any) => {
                          const productTotal = product.specifications.reduce((sum: number, spec: any) =>
                            sum + (parseFloat(spec.targetPrice || 0) * parseFloat(spec.area || 0)), 0
                          )
                          return total + productTotal
                        }, 0).toFixed(0) || 0}
                      </div>
                      <div className="text-sm text-gray-500">toplam bütçe</div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-2">Açıklama:</p>
                    <p className="text-sm">{request.description}</p>
                    {request.message && (
                      <div className="mt-2 p-2 bg-blue-50 rounded border-l-4 border-blue-400">
                        <p className="text-sm text-blue-800"><strong>Müşteri Mesajı:</strong> {request.message}</p>
                      </div>
                    )}
                  </div>

                  {/* Product Summary */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Talep Edilen Ürünler:</h4>
                    <div className="space-y-2">
                      {request.products?.map((product: any, productIndex: number) => (
                        <div key={productIndex} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="font-medium text-gray-900">{product.productName}</span>
                            <span className="text-xs bg-gray-200 px-2 py-1 rounded">{product.productCategory}</span>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                            {product.specifications?.map((spec: any, specIndex: number) => (
                              <div key={specIndex} className="text-gray-600">
                                <span className="font-medium">{spec.area}m²</span> -
                                {spec.thickness}cm × {spec.width}cm × {spec.length}cm -
                                {spec.surface} - ${spec.targetPrice}/m²
                              </div>
                            ))}
                          </div>
                        </div>
                      )) || (
                        <div className="text-sm text-gray-500">Ürün detayları yükleniyor...</div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col gap-2 ml-4">
                  <Button
                    onClick={() => {
                      setSelectedRequest(request)
                      setIsQuoteModalOpen(true)
                    }}
                    className="bg-amber-600 hover:bg-amber-700"
                  >
                    Teklif Ver
                  </Button>

                  {shouldShowMultiDeliveryOption(request) && (
                    <Button
                      onClick={() => handleMultiDeliveryQuote(request)}
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      <Package className="w-4 h-4 mr-1" />
                      Çoklu Teslimat Teklifi
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSendMessage(request)}
                  >
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Mesaj Gönder
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetails(request)}
                  >
                    Detayları Gör
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {!isLoading && pendingRequests.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Bekleyen teklif talebi bulunamadı
            </h3>
            <p className="text-gray-600">
              Şu anda bekleyen teklif talebiniz bulunmuyor.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Quote Submission Modal */}
      {isQuoteModalOpen && selectedRequest && (
        <QuoteSubmissionModal
          isOpen={isQuoteModalOpen}
          onClose={() => {
            setIsQuoteModalOpen(false)
            setSelectedRequest(null)
          }}
          request={selectedRequest}
        />
      )}

      {/* Message Modal */}
      {isMessageModalOpen && selectedRequest && (
        <MessageModal
          isOpen={isMessageModalOpen}
          onClose={() => {
            setIsMessageModalOpen(false)
            setSelectedRequest(null)
          }}
          request={selectedRequest}
        />
      )}

      {/* Details Modal */}
      {isDetailsModalOpen && selectedRequest && (
        <RequestDetailsModal
          isOpen={isDetailsModalOpen}
          onClose={() => {
            setIsDetailsModalOpen(false)
            setSelectedRequest(null)
          }}
          request={selectedRequest}
        />
      )}

      {/* Multi Delivery Quote Modal */}
      {isMultiDeliveryQuoteModalOpen && selectedRequest && (
        <MultiDeliveryQuoteModal
          isOpen={isMultiDeliveryQuoteModalOpen}
          onClose={() => {
            setIsMultiDeliveryQuoteModalOpen(false)
            setSelectedRequest(null)
          }}
          quoteRequest={selectedRequest}
          onSendQuote={handleSendMultiDeliveryQuote}
        />
      )}
    </div>
  )
}
