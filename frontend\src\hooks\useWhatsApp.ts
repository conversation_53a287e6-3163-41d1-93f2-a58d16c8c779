import { useState, useEffect } from 'react';

interface WhatsAppConfig {
  enabled: boolean;
  businessNumber: string;
  businessURL: string;
  autoReply: boolean;
  isWithinBusinessHours: boolean;
}

interface UseWhatsAppReturn {
  config: WhatsAppConfig | null;
  loading: boolean;
  error: string | null;
  generateSupportURL: (customerInfo?: {
    name?: string;
    orderNumber?: string;
    issue?: string;
  }) => Promise<string>;
  generateBusinessURL: (message?: string) => Promise<string>;
  sendPaymentInstructions: (data: {
    customerPhone: string;
    customerName: string;
    orderNumber: string;
    amount: number;
    currency: string;
    referenceCode: string;
    bankInfo: {
      bankName: string;
      iban: string;
      accountHolder: string;
    };
  }) => Promise<{ whatsappUrl: string; message: string }>;
  sendOrderStatus: (data: {
    customerPhone: string;
    customerName: string;
    orderNumber: string;
    status: string;
    message: string;
  }) => Promise<{ whatsappUrl: string; message: string }>;
  sendEscrowNotification: (data: {
    customerPhone: string;
    customerName: string;
    orderNumber: string;
    type: 'payment_confirmed' | 'goods_ready' | 'payment_released';
    amount?: number;
    currency?: string;
  }) => Promise<{ whatsappUrl: string; message: string }>;
}

export const useWhatsApp = (): UseWhatsAppReturn => {
  const [config, setConfig] = useState<WhatsAppConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/api/whatsapp/widget-config`);
      
      if (response.ok) {
        const data = await response.json();
        setConfig(data.data);
        setError(null);
      } else {
        throw new Error('Failed to load WhatsApp configuration');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('WhatsApp config error:', err);
    } finally {
      setLoading(false);
    }
  };

  const generateSupportURL = async (customerInfo?: {
    name?: string;
    orderNumber?: string;
    issue?: string;
  }): Promise<string> => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/api/whatsapp/support-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(customerInfo || {}),
      });

      if (response.ok) {
        const data = await response.json();
        return data.data.supportURL;
      } else {
        throw new Error('Failed to generate support URL');
      }
    } catch (err) {
      console.error('Support URL generation error:', err);
      // Fallback to business URL
      return config?.businessURL || `https://wa.me/${config?.businessNumber?.replace(/[^\d]/g, '')}`;
    }
  };

  const generateBusinessURL = async (message?: string): Promise<string> => {
    try {
      const queryParam = message ? `?message=${encodeURIComponent(message)}` : '';
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/api/whatsapp/business-url${queryParam}`);

      if (response.ok) {
        const data = await response.json();
        return data.data.businessURL;
      } else {
        throw new Error('Failed to generate business URL');
      }
    } catch (err) {
      console.error('Business URL generation error:', err);
      // Fallback
      const cleanNumber = config?.businessNumber?.replace(/[^\d]/g, '') || '';
      const encodedMessage = message ? encodeURIComponent(message) : '';
      return `https://wa.me/${cleanNumber}${message ? `?text=${encodedMessage}` : ''}`;
    }
  };

  const sendPaymentInstructions = async (data: {
    customerPhone: string;
    customerName: string;
    orderNumber: string;
    amount: number;
    currency: string;
    referenceCode: string;
    bankInfo: {
      bankName: string;
      iban: string;
      accountHolder: string;
    };
  }): Promise<{ whatsappUrl: string; message: string }> => {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/api/whatsapp/payment-instructions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (response.ok) {
      const result = await response.json();
      return result.data;
    } else {
      throw new Error('Failed to send payment instructions');
    }
  };

  const sendOrderStatus = async (data: {
    customerPhone: string;
    customerName: string;
    orderNumber: string;
    status: string;
    message: string;
  }): Promise<{ whatsappUrl: string; message: string }> => {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/api/whatsapp/order-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (response.ok) {
      const result = await response.json();
      return result.data;
    } else {
      throw new Error('Failed to send order status');
    }
  };

  const sendEscrowNotification = async (data: {
    customerPhone: string;
    customerName: string;
    orderNumber: string;
    type: 'payment_confirmed' | 'goods_ready' | 'payment_released';
    amount?: number;
    currency?: string;
  }): Promise<{ whatsappUrl: string; message: string }> => {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/api/whatsapp/escrow-notification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (response.ok) {
      const result = await response.json();
      return result.data;
    } else {
      throw new Error('Failed to send escrow notification');
    }
  };

  return {
    config,
    loading,
    error,
    generateSupportURL,
    generateBusinessURL,
    sendPaymentInstructions,
    sendOrderStatus,
    sendEscrowNotification,
  };
};

export default useWhatsApp;
