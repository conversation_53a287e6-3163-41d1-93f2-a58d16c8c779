'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSample } from '@/contexts/sample-context';
import {
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  TruckIcon
} from '@heroicons/react/24/outline';

interface AdminSampleRequestsSubPageProps {
  status: 'pending' | 'approved' | 'rejected' | 'shipped';
  title: string;
  description: string;
}

const AdminSampleRequestsSubPage: React.FC<AdminSampleRequestsSubPageProps> = ({
  status,
  title,
  description
}) => {
  const { getAllSampleRequests, isLoading } = useSample();
  const [sampleData, setSampleData] = useState<any>(null);
  const [selectedSample, setSelectedSample] = useState<any>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  useEffect(() => {
    loadSampleRequests();
  }, [status]);

  const loadSampleRequests = async () => {
    try {
      const data = await getAllSampleRequests({
        status: status,
        page: 1,
        limit: 50
      });
      setSampleData(data);
    } catch (error) {
      console.error('Error loading sample requests:', error);
    }
  };

  const handleViewDetail = (sample: any) => {
    setSelectedSample(sample);
    setShowDetailModal(true);
  };

  const getStatusIcon = (sampleStatus: string) => {
    switch (sampleStatus) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'approved':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'shipped':
        return <TruckIcon className="h-5 w-5 text-blue-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusLabel = (sampleStatus: string) => {
    switch (sampleStatus) {
      case 'pending':
        return 'Bekliyor';
      case 'approved':
        return 'Onaylandı';
      case 'rejected':
        return 'Reddedildi';
      case 'shipped':
        return 'Gönderildi';
      default:
        return sampleStatus;
    }
  };

  const getStatusColor = (sampleStatus: string) => {
    switch (sampleStatus) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'shipped':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Numune talepleri yükleniyor...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          <p className="text-gray-600 mt-1">{description}</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={loadSampleRequests}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            Yenile
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon(status)}
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {getStatusLabel(status)} Talepler
              </h3>
              <p className="text-sm text-gray-600">
                Toplam {sampleData?.samples?.length || 0} talep
              </p>
            </div>
          </div>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status)}`}>
            {getStatusLabel(status)}
          </div>
        </div>
      </div>

      {/* Sample Requests Table */}
      {sampleData?.samples && sampleData.samples.length > 0 ? (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Talep ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Müşteri
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ürünler
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Durum
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tarih
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sampleData.samples.map((sample: any, index: number) => (
                  <motion.tr
                    key={sample.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      #{sample.id.slice(-6)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {sample.customer?.name || 'Bilinmeyen Müşteri'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {sample.customer?.email}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">
                        {sample.requestedProducts?.map((product: any, idx: number) => (
                          <div key={idx} className="mb-1">
                            {product.productName}
                          </div>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(sample.status)}
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(sample.status)}`}>
                          {getStatusLabel(sample.status)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(sample.createdAt).toLocaleDateString('tr-TR')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => handleViewDetail(sample)}
                        className="text-blue-600 hover:text-blue-900 flex items-center space-x-1"
                      >
                        <EyeIcon className="h-4 w-4" />
                        <span>Detay</span>
                      </button>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-xl shadow-sm border border-gray-200">
          {getStatusIcon(status)}
          <h3 className="mt-4 text-lg font-medium text-gray-900">
            {getStatusLabel(status)} numune talebi bulunmuyor
          </h3>
          <p className="mt-2 text-gray-600">
            Bu durumda henüz numune talebi bulunmamaktadır.
          </p>
        </div>
      )}
    </div>
  );
};

export default AdminSampleRequestsSubPage;
