'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Bot,
  Mail,
  Share2,
  Users,
  TrendingUp,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Settings,
  Play,
  Pause,
  RefreshCw,
  ArrowRight,
  Activity
} from 'lucide-react';

interface AIMarketingStats {
  emailMarketing: {
    totalLists: number;
    totalSubscribers: number;
    campaignsActive: number;
    openRate: number;
    clickRate: number;
    conversionRate: number;
  };
  socialMedia: {
    platformsConnected: number;
    postsScheduled: number;
    postsPublished: number;
    totalReach: number;
    engagement: number;
    pendingApproval: number;
  };
  customerAcquisition: {
    prospectsFound: number;
    contactsInitiated: number;
    responseRate: number;
    leadsGenerated: number;
    conversionRate: number;
  };
  adManagement: {
    activeCampaigns: number;
    totalSpend: number;
    impressions: number;
    clicks: number;
    conversions: number;
    roas: number;
  };
}

interface AISystemStatus {
  orchestrator: 'active' | 'inactive' | 'error';
  emailAI: 'active' | 'inactive' | 'error';
  socialAI: 'active' | 'inactive' | 'error';
  customerAI: 'active' | 'inactive' | 'error';
  adsAI: 'active' | 'inactive' | 'error';
}

export default function AIMarketingDashboard() {
  const [stats, setStats] = useState<AIMarketingStats | null>(null);
  const [systemStatus, setSystemStatus] = useState<AISystemStatus | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAIMarketingData();
  }, []);

  const fetchAIMarketingData = async () => {
    setLoading(true);
    try {
      // Mock data - gerçek API'den gelecek
      const mockStats: AIMarketingStats = {
        emailMarketing: {
          totalLists: 45,
          totalSubscribers: 12847,
          campaignsActive: 8,
          openRate: 24.5,
          clickRate: 3.2,
          conversionRate: 1.8
        },
        socialMedia: {
          platformsConnected: 6,
          postsScheduled: 24,
          postsPublished: 156,
          totalReach: 89432,
          engagement: 4.7,
          pendingApproval: 12
        },
        customerAcquisition: {
          prospectsFound: 342,
          contactsInitiated: 89,
          responseRate: 12.4,
          leadsGenerated: 23,
          conversionRate: 8.7
        },
        adManagement: {
          activeCampaigns: 15,
          totalSpend: 8450,
          impressions: 234567,
          clicks: 3421,
          conversions: 89,
          roas: 3.2
        }
      };

      const mockSystemStatus: AISystemStatus = {
        orchestrator: 'active',
        emailAI: 'active',
        socialAI: 'active',
        customerAI: 'active',
        adsAI: 'active'
      };

      setStats(mockStats);
      setSystemStatus(mockSystemStatus);
    } catch (error) {
      console.error('Error fetching AI marketing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'inactive':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Aktif</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Pasif</Badge>;
      case 'error':
        return <Badge variant="destructive">Hata</Badge>;
      default:
        return <Badge variant="outline">Bilinmiyor</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-2 text-lg">AI Marketing verileri yükleniyor...</span>
      </div>
    );
  }

  if (!stats || !systemStatus) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Veri Yüklenemedi</h3>
        <p className="text-gray-600 mb-4">AI Marketing verileri yüklenirken bir hata oluştu.</p>
        <Button onClick={fetchAIMarketingData}>
          <RefreshCw className="w-4 h-4 mr-2" />
          Tekrar Dene
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI Pazarlama Genel Bakış</h1>
          <p className="text-gray-600 mt-1">
            Tüm AI pazarlama sistemlerinin durumu ve performansı
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchAIMarketingData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Yenile
          </Button>
          <Link href="/admin/ai-marketing/settings">
            <Button>
              <Settings className="w-4 h-4 mr-2" />
              AI Ayarları
            </Button>
          </Link>
        </div>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bot className="w-5 h-5 mr-2" />
            AI Sistem Durumu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-2">
                {getStatusIcon(systemStatus.orchestrator)}
                <span className="font-medium">Orchestrator</span>
              </div>
              {getStatusBadge(systemStatus.orchestrator)}
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-2">
                {getStatusIcon(systemStatus.emailAI)}
                <span className="font-medium">Email AI</span>
              </div>
              {getStatusBadge(systemStatus.emailAI)}
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-2">
                {getStatusIcon(systemStatus.socialAI)}
                <span className="font-medium">Social AI</span>
              </div>
              {getStatusBadge(systemStatus.socialAI)}
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-2">
                {getStatusIcon(systemStatus.customerAI)}
                <span className="font-medium">Customer AI</span>
              </div>
              {getStatusBadge(systemStatus.customerAI)}
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-2">
                {getStatusIcon(systemStatus.adsAI)}
                <span className="font-medium">Ads AI</span>
              </div>
              {getStatusBadge(systemStatus.adsAI)}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* AI Modülleri */}
      <div className="space-y-6">
        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Toplam Abone</p>
                    <p className="text-2xl font-bold">{stats.emailMarketing.totalSubscribers.toLocaleString()}</p>
                  </div>
                  <Mail className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Sosyal Medya Erişim</p>
                    <p className="text-2xl font-bold">{stats.socialMedia.totalReach.toLocaleString()}</p>
                  </div>
                  <Share2 className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Potansiyel Müşteri</p>
                    <p className="text-2xl font-bold">{stats.customerAcquisition.prospectsFound}</p>
                  </div>
                  <Users className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Reklam ROAS</p>
                    <p className="text-2xl font-bold">{stats.adManagement.roas.toFixed(1)}x</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

        {/* AI Modül Kartları */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Email Marketing Modülü */}
          <Link href="/admin/ai-marketing/email">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-l-blue-500">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Mail className="w-5 h-5 mr-2 text-blue-600" />
                    Email Marketing
                  </div>
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Aktif Kampanyalar</span>
                  <Badge variant="default">{stats.emailMarketing.campaignsActive}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Toplam Abone</span>
                  <span className="font-semibold">{stats.emailMarketing.totalSubscribers.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Açılma Oranı</span>
                  <span className="font-semibold text-green-600">{stats.emailMarketing.openRate}%</span>
                </div>
                <div className="pt-2">
                  <div className="flex items-center space-x-2">
                    <Activity className="w-4 h-4 text-green-500" />
                    <span className="text-xs text-green-600">Aktif</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          {/* Sosyal Medya Modülü */}
          <Link href="/admin/ai-marketing/social">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-l-purple-500">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Share2 className="w-5 h-5 mr-2 text-purple-600" />
                    Sosyal Medya
                  </div>
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Bağlı Platform</span>
                  <Badge variant="default">{stats.socialMedia.platformsConnected}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Yayınlanan Post</span>
                  <span className="font-semibold">{stats.socialMedia.postsPublished}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Onay Bekleyen</span>
                  <Badge variant="destructive">{stats.socialMedia.pendingApproval}</Badge>
                </div>
                <div className="pt-2">
                  <div className="flex items-center space-x-2">
                    <Activity className="w-4 h-4 text-green-500" />
                    <span className="text-xs text-green-600">Aktif</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          {/* Müşteri Arama Modülü */}
          <Link href="/admin/ai-marketing/customers">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-l-green-500">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Users className="w-5 h-5 mr-2 text-green-600" />
                    Müşteri Arama
                  </div>
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Bulunan Potansiyel</span>
                  <span className="font-semibold">{stats.customerAcquisition.prospectsFound}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">İletişim Kurulan</span>
                  <span className="font-semibold">{stats.customerAcquisition.contactsInitiated}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Yanıt Oranı</span>
                  <span className="font-semibold text-green-600">{stats.customerAcquisition.responseRate}%</span>
                </div>
                <div className="pt-2">
                  <div className="flex items-center space-x-2">
                    <Activity className="w-4 h-4 text-green-500" />
                    <span className="text-xs text-green-600">Aktif</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          {/* Reklam Yönetimi Modülü */}
          <Link href="/admin/ai-marketing/ads">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-l-orange-500">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-orange-600" />
                    Reklam Yönetimi
                  </div>
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Aktif Kampanya</span>
                  <Badge variant="default">{stats.adManagement.activeCampaigns}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Toplam Harcama</span>
                  <span className="font-semibold">${stats.adManagement.totalSpend.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">ROAS</span>
                  <span className="font-semibold text-green-600">{stats.adManagement.roas}x</span>
                </div>
                <div className="pt-2">
                  <div className="flex items-center space-x-2">
                    <Activity className="w-4 h-4 text-green-500" />
                    <span className="text-xs text-green-600">Aktif</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          {/* Onay Sistemi Modülü */}
          <Link href="/admin/ai-marketing/approvals">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-l-red-500">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <CheckCircle className="w-5 h-5 mr-2 text-red-600" />
                    Onay Sistemi
                  </div>
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Onay Bekleyen</span>
                  <Badge variant="destructive">12</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Bugün Onaylanan</span>
                  <span className="font-semibold">8</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Reddedilen</span>
                  <span className="font-semibold text-red-600">2</span>
                </div>
                <div className="pt-2">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="w-4 h-4 text-yellow-500" />
                    <span className="text-xs text-yellow-600">Dikkat Gerekli</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          {/* Analitik Modülü */}
          <Link href="/admin/ai-marketing/analytics">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-l-indigo-500">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-indigo-600" />
                    Analitik & Raporlar
                  </div>
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Toplam Görev</span>
                  <span className="font-semibold">1,247</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Başarı Oranı</span>
                  <span className="font-semibold text-green-600">94.2%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Ortalama Süre</span>
                  <span className="font-semibold">2.3s</span>
                </div>
                <div className="pt-2">
                  <div className="flex items-center space-x-2">
                    <Activity className="w-4 h-4 text-green-500" />
                    <span className="text-xs text-green-600">Aktif</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  );
}
