/**
 * Ocak (Quarry) ve Ürün Yönetimi için Type Definitions
 * RFC-011: Quarry-Based Product Management System
 */

export interface QuarryLocation {
  city: string
  district: string
  coordinates: {
    lat: number
    lng: number
  }
  address: string
  googleMapsLink?: string
}

export interface Quarry {
  id: string
  name: string // "Afyon Beyaz Mermer Ocağı"
  location: QuarryLocation
  owner: string // Ocak sahibi firma
  establishedYear?: number
  capacity?: string
  certifications: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface TechnicalSpecs {
  density?: number // Yoğunluk (kg/m³)
  hardness?: number // Sertlik (Mohs)
  waterAbsorption?: number // Su emme oranı (%)
  freezeThawResistance?: string // Donma-çözülme direnci
  compressiveStrength?: number // Basınç dayanımı (MPa)
  flexuralStrength?: number // Eğilme dayanımı (MPa)
  abrasionResistance?: number // Aşınma direnci
  thermalConductivity?: number // Isı iletkenliği
  chemicalResistance?: string // Kimyasal direnç
  color?: string // Renk
  pattern?: string // Desen
  origin?: string // Menşei
}

export interface ProductMedia {
  coverImage: string
  images: string[]
  videos?: string[]
  documents?: string[] // Analiz raporları, sertifikalar
  mockups?: string[] // Mokap resimleri
}

export interface DimensionPrice {
  width: number
  height: number
  thickness: number
  price: number
  currency: string
  unit: string
}

export interface SlabPrice {
  size: string
  price: number
  currency: string
  unit: string
}

export interface PriceList {
  dimensions: DimensionPrice[]
  slabs: SlabPrice[]
  surfaceFinishes: Array<{
    type: string
    additionalCost: number
    currency: string
  }>
  packaging: Array<{
    type: string
    cost: number
    currency: string
  }>
  delivery: Array<{
    type: string
    cost: number
    currency: string
  }>
}

export interface QuarryProduct {
  id: string
  quarryId: string // Hangi ocaktan geldiği
  name: string // "Afyon Beyaz Mermer"
  category: string
  description: string
  technicalSpecs: TechnicalSpecs
  media: ProductMedia
  producers: string[] // Bu ürünü üreten üretici ID'leri
  isActive: boolean
  approvalStatus: 'pending' | 'approved' | 'rejected'
  rejectionReason?: string
  createdAt: Date
  updatedAt: Date
  createdBy: string // İlk ekleyen üretici
  reviewedBy?: string
  reviewedAt?: Date
}

export interface ProducerProduct {
  id: string
  producerId: string
  productId: string
  quarryId: string
  priceList: PriceList
  stock: number
  minOrder: number
  productionCapacity: string
  leadTime: string // Teslimat süresi
  qualityGrade: string // Kalite sınıfı (A, B, C)
  isActive: boolean
  joinedAt: Date
  lastUpdated: Date
}

export interface QuarryProductWithProducers extends QuarryProduct {
  quarry: Quarry
  producerDetails: Array<{
    producerId: string
    producerName: string
    companyName: string
    joinedAt: Date
    isActive: boolean
    priceRange: {
      min: number
      max: number
      currency: string
    }
    stock: number
    productionCapacity: string
  }>
}

// Form için kullanılacak interface'ler
export interface QuarryFormData {
  name: string
  location: QuarryLocation
  owner: string
  establishedYear?: number
  capacity?: string
  certifications: string[]
}

export interface ProductFormData {
  quarryId: string
  name: string
  category: string
  description: string
  technicalSpecs: TechnicalSpecs
  media: ProductMedia
  priceList: PriceList
  stock: number
  minOrder: number
  productionCapacity: string
  leadTime: string
  qualityGrade: string
}

// API Response types
export interface QuarrySearchResponse {
  quarries: Quarry[]
  total: number
  page: number
  limit: number
}

export interface ProductSearchResponse {
  products: QuarryProductWithProducers[]
  total: number
  page: number
  limit: number
}

export interface ProducerProductResponse {
  producerProducts: ProducerProduct[]
  total: number
  page: number
  limit: number
}
