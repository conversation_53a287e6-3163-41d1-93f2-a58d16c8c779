'use client';

import React from 'react';
import {
  ShoppingBagIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ChartBarIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import {
  CalculatorIcon
} from '@heroicons/react/24/solid';
import { motion } from 'framer-motion';

interface KPICardProps {
  title: string;
  value: number;
  currency?: 'USD' | 'EUR' | 'TRY';
  unit?: string;
  change?: {
    percentage: number;
    trend: 'up' | 'down' | 'stable';
    period: 'month' | 'quarter' | 'year';
  };
  icon: 'shopping-bag' | 'trending-up' | 'percent' | 'clock' | 'file-text' | 'check-circle' | 'dollar-sign';
  color: 'blue' | 'green' | 'purple' | 'orange';
}

const KPICard: React.FC<KPICardProps> = ({
  title,
  value,
  currency,
  unit,
  change,
  icon,
  color
}) => {
  const iconMap = {
    'shopping-bag': ShoppingBagIcon,
    'trending-up': ChartBarIcon,
    'percent': CalculatorIcon,
    'clock': ClockIcon,
    'file-text': DocumentTextIcon,
    'check-circle': CheckCircleIcon,
    'dollar-sign': CurrencyDollarIcon
  };

  const colorMap = {
    blue: {
      bg: 'bg-blue-50',
      icon: 'text-blue-600',
      accent: 'border-blue-200'
    },
    green: {
      bg: 'bg-green-50',
      icon: 'text-green-600',
      accent: 'border-green-200'
    },
    purple: {
      bg: 'bg-purple-50',
      icon: 'text-purple-600',
      accent: 'border-purple-200'
    },
    orange: {
      bg: 'bg-orange-50',
      icon: 'text-orange-600',
      accent: 'border-orange-200'
    }
  };

  const IconComponent = iconMap[icon];
  const colors = colorMap[color];

  const formatValue = (val: number) => {
    if (currency) {
      const currencySymbols = {
        USD: '$',
        EUR: '€',
        TRY: '₺'
      };
      
      if (val >= 1000000) {
        return `${currencySymbols[currency]}${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `${currencySymbols[currency]}${(val / 1000).toFixed(0)}K`;
      } else {
        return `${currencySymbols[currency]}${val.toLocaleString()}`;
      }
    }
    
    if (unit === '%') {
      return `${val.toFixed(1)}%`;
    }
    
    return `${val.toLocaleString()}${unit ? ` ${unit}` : ''}`;
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-600 bg-green-100';
      case 'down':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return ArrowUpIcon;
      case 'down':
        return ArrowDownIcon;
      default:
        return null;
    }
  };

  const periodLabels = {
    month: 'bu ay',
    quarter: 'bu çeyrek',
    year: 'bu yıl'
  };

  return (
    <motion.div
      whileHover={{ y: -2 }}
      className={`bg-white rounded-xl border ${colors.accent} p-6 shadow-sm hover:shadow-md transition-all duration-200`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-lg ${colors.bg}`}>
          <IconComponent className={`h-6 w-6 ${colors.icon}`} />
        </div>
        
        {change && (
          <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getTrendColor(change.trend)}`}>
            {getTrendIcon(change.trend) && (
              <span className="h-3 w-3">
                {React.createElement(getTrendIcon(change.trend)!, { className: 'h-3 w-3' })}
              </span>
            )}
            <span>{change.percentage}%</span>
          </div>
        )}
      </div>

      {/* Value */}
      <div className="mb-2">
        <h3 className="text-2xl font-bold text-gray-900">
          {formatValue(value)}
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          {title}
        </p>
      </div>

      {/* Change Description */}
      {change && (
        <p className="text-xs text-gray-500">
          {change.trend === 'up' ? '+' : change.trend === 'down' ? '-' : ''}
          {change.percentage}% {periodLabels[change.period]}
        </p>
      )}
    </motion.div>
  );
};

export default KPICard;
