'use client'

import * as React from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Package, 
  Upload, 
  Plus, 
  Trash2, 
  DollarSign,
  Ruler,
  Palette,
  Box,
  Truck
} from 'lucide-react'

interface ProductFormData {
  // Temel Bilgiler
  name: string
  category: string
  description: string
  origin: string
  status: 'active' | 'draft' | 'inactive'

  // Admin Onay Sistemi
  approvalStatus?: 'pending' | 'approved' | 'rejected'
  rejectionReason?: string
  submittedAt?: Date
  reviewedAt?: Date
  reviewedBy?: string

  // Ürün Medya Yükleme Alanı
  media: {
    coverImage?: { file?: File; url?: string } // Kapak resmi (zorunlu)
    slabImages: Array<{ file?: File; url?: string }> // 3 adet plaka resmi
    mockupImages: Array<{ file?: File; url?: string }> // 3 adet mokap yere serili resim
    video?: { file?: File; url?: string } // 10 sn'lik sessiz video
  }

  // Teknik Özellikler (Mevcut yapı korunuyor)
  technicalSpecs: {
    density?: string
    waterAbsorption?: string
    compressiveStrength?: string
    flexuralStrength?: string
    frostResistance?: string
    abrasionResistance?: string
    customSpecs: Array<{
      name: string
      value: string
      unit: string
    }>
  }

  // Ebatlı Ürün Fiyat Listesi (Excel Görünümlü)
  dimensionPrices: Array<{
    id: string
    thickness: number
    width: number
    length: number
    surfaceFinish: string // Ham (sabit)
    packaging: string // Paletüstü (sabit)
    delivery: string // Fabrika teslim (sabit)
    price: number
    currency: string
  }>

  // Ebatlı Ürün Yüzey İşlemi Fiyat Listesi
  dimensionSurfaceFinishPrices: Array<{
    id: string
    finish: string // honlu, cilalı, fırçalı, kumlama, yakma, eskitme, dolgu
    available: boolean // Yapabilirim/Yapamam
    price: number
    currency: string
  }>

  // Ebatlı Ürün Ambalaj Listesi
  dimensionPackagingPrices: Array<{
    id: string
    packaging: string // kasalı, bandıllı
    price: number
    currency: string
  }>

  // Plaka Ürün Fiyat Listesi (Excel Görünümlü)
  slabPrices: Array<{
    id: string
    thickness: number
    width: number
    length: number
    surfaceFinish: string // Ham (sabit)
    packaging: string // Bandılsız (sabit)
    delivery: string // Fabrika teslim (sabit)
    price: number
    currency: string
  }>

  // Plaka Yüzey İşlemi Fiyat Listesi
  slabSurfaceFinishPrices: Array<{
    id: string
    finish: string
    available: boolean // Yapabilirim/Yapamam
    price: number
    currency: string
  }>

  // Plaka Ambalaj Listesi
  slabPackagingPrices: Array<{
    id: string
    packaging: string // bandıllı
    price: number
    currency: string
  }>

  // Taş Analiz Raporları
  analysisReports: Array<{
    id: string
    reportName: string
    value: string
    unit: string
    file?: { file?: File; url?: string }
  }>


}

interface ProductFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: ProductFormData) => Promise<boolean>
  product?: ProductFormData // For editing
  mode: 'create' | 'edit'
}

const CATEGORIES = [
  'Mermer', 'Traverten', 'Granit', 'Oniks', 'Limestone', 'Bazalt', 'Andezit'
]

const SURFACE_FINISHES = [
  'Ham', 'Honlu', 'Cilalı', 'Fırçalı', 'Kumlama', 'Yakma', 'Eskitme', 'Dolgu'
]

const DIMENSION_PACKAGING_OPTIONS = [
  'Kasalı', 'Bandıllı'
]

const SLAB_PACKAGING_OPTIONS = [
  'Bandıllı'
]

const CURRENCIES = ['USD', 'EUR', 'TL']

// Helper function to generate unique IDs
const generateId = () => Math.random().toString(36).substr(2, 9)

export function ProductFormModal({ 
  isOpen, 
  onClose, 
  onSave, 
  product, 
  mode 
}: ProductFormModalProps) {
  const [currentStep, setCurrentStep] = React.useState(1)
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState('')

  // Reset step when modal opens/closes
  React.useEffect(() => {
    if (isOpen) {
      setCurrentStep(1)
      setError('')
    }
  }, [isOpen])

  const [formData, setFormData] = React.useState<ProductFormData>({
    // Temel Bilgiler
    name: '',
    category: '',
    description: '',
    origin: 'Türkiye',
    status: 'draft',

    // Ürün Medya Yükleme Alanı
    media: {
      slabImages: [{}, {}, {}], // 3 adet plaka resmi
      mockupImages: [{}, {}, {}] // 3 adet mokap yere serili resim
    },

    // Teknik Özellikler (Mevcut yapı korunuyor)
    technicalSpecs: {
      customSpecs: []
    },

    // Ebatlı Ürün Fiyat Listesi
    dimensionPrices: [{
      id: generateId(),
      thickness: 2,
      width: 60,
      length: 60,
      surfaceFinish: 'Ham',
      packaging: 'Paletüstü',
      delivery: 'Fabrika Teslim',
      price: 0,
      currency: 'USD'
    }],

    // Ebatlı Ürün Yüzey İşlemi Fiyat Listesi
    dimensionSurfaceFinishPrices: SURFACE_FINISHES.filter(f => f !== 'Ham').map(finish => ({
      id: generateId(),
      finish,
      available: false,
      price: 0,
      currency: 'USD'
    })),

    // Ebatlı Ürün Ambalaj Listesi
    dimensionPackagingPrices: DIMENSION_PACKAGING_OPTIONS.map(packaging => ({
      id: generateId(),
      packaging,
      price: 0,
      currency: 'USD'
    })),

    // Plaka Ürün Fiyat Listesi
    slabPrices: [{
      id: generateId(),
      thickness: 2,
      width: 60,
      length: 60,
      surfaceFinish: 'Ham',
      packaging: 'Bandılsız',
      delivery: 'Fabrika Teslim',
      price: 0,
      currency: 'USD'
    }],

    // Plaka Yüzey İşlemi Fiyat Listesi
    slabSurfaceFinishPrices: SURFACE_FINISHES.filter(f => f !== 'Ham').map(finish => ({
      id: generateId(),
      finish,
      available: false,
      price: 0,
      currency: 'USD'
    })),

    // Plaka Ambalaj Listesi
    slabPackagingPrices: SLAB_PACKAGING_OPTIONS.map(packaging => ({
      id: generateId(),
      packaging,
      price: 0,
      currency: 'USD'
    })),

    // Taş Analiz Raporları
    analysisReports: []
  })

  const totalSteps = 8 // Artık 8 adım var

  // Initialize form data if editing
  React.useEffect(() => {
    if (mode === 'edit' && product) {
      // Ensure all required fields are present with defaults
      const editFormData = {
        // Temel Bilgiler
        name: product.name || '',
        category: product.category || '',
        description: product.description || '',
        origin: product.origin || 'Türkiye',
        status: product.status || 'draft',

        // Admin Onay Sistemi
        approvalStatus: product.approvalStatus,
        rejectionReason: product.rejectionReason,
        submittedAt: product.submittedAt,
        reviewedAt: product.reviewedAt,
        reviewedBy: product.reviewedBy,

        // Ürün Medya Yükleme Alanı
        media: {
          coverImage: product.media?.coverImage || (product.image ? { url: product.image } : undefined),
          slabImages: product.media?.slabImages || [{}, {}, {}],
          mockupImages: product.media?.mockupImages || [{}, {}, {}],
          video: product.media?.video
        },

        // Teknik Özellikler
        technicalSpecs: {
          customSpecs: product.media?.technicalSpecs?.customSpecs || []
        },

        // Ebatlı Ürün Fiyat Listesi
        dimensionPrices: product.media?.dimensionPrices || [{
          id: generateId(),
          thickness: 2,
          width: 60,
          length: 60,
          surfaceFinish: 'Ham',
          packaging: 'Paletüstü',
          delivery: 'Fabrika Teslim',
          price: 0,
          currency: 'USD'
        }],

        // Plaka Ürün Fiyat Listesi
        slabPrices: product.media?.slabPrices || [{
          id: generateId(),
          thickness: 2,
          surfaceFinish: 'Ham',
          packaging: 'Paletüstü',
          delivery: 'Fabrika Teslim',
          price: 0,
          currency: 'USD'
        }],

        // Plaka Yüzey İşlemi Fiyat Listesi
        slabSurfaceFinishPrices: product.media?.slabSurfaceFinishPrices || SURFACE_FINISHES.filter(f => f !== 'Ham').map(finish => ({
          id: generateId(),
          finish,
          available: false,
          price: 0,
          currency: 'USD'
        })),

        // Plaka Ambalaj Listesi
        slabPackagingPrices: product.media?.slabPackagingPrices || SLAB_PACKAGING_OPTIONS.map(packaging => ({
          id: generateId(),
          packaging,
          price: 0,
          currency: 'USD'
        })),

        // Taş Analiz Raporları
        analysisReports: product.media?.analysisReports || []
      }

      setFormData(editFormData)
    } else if (mode === 'create') {
      // Reset to initial state for create mode
      setFormData({
        // Temel Bilgiler
        name: '',
        category: '',
        description: '',
        origin: 'Türkiye',
        status: 'draft',

        // Ürün Medya Yükleme Alanı
        media: {
          slabImages: [{}, {}, {}], // 3 adet plaka resmi
          mockupImages: [{}, {}, {}] // 3 adet mokap yere serili resim
        },

        // Teknik Özellikler (Mevcut yapı korunuyor)
        technicalSpecs: {
          customSpecs: []
        },

        // Ebatlı Ürün Fiyat Listesi
        dimensionPrices: [{
          id: generateId(),
          thickness: 2,
          width: 60,
          length: 60,
          surfaceFinish: 'Ham',
          packaging: 'Paletüstü',
          delivery: 'Fabrika Teslim',
          price: 0,
          currency: 'USD'
        }],

        // Plaka Ürün Fiyat Listesi
        slabPrices: [{
          id: generateId(),
          thickness: 2,
          surfaceFinish: 'Ham',
          packaging: 'Paletüstü',
          delivery: 'Fabrika Teslim',
          price: 0,
          currency: 'USD'
        }],

        // Plaka Yüzey İşlemi Fiyat Listesi
        slabSurfaceFinishPrices: SURFACE_FINISHES.filter(f => f !== 'Ham').map(finish => ({
          id: generateId(),
          finish,
          available: false,
          price: 0,
          currency: 'USD'
        })),

        // Plaka Ambalaj Listesi
        slabPackagingPrices: SLAB_PACKAGING_OPTIONS.map(packaging => ({
          id: generateId(),
          packaging,
          price: 0,
          currency: 'USD'
        })),

        // Taş Analiz Raporları
        analysisReports: []
      })
    }
  }, [mode, product])

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const updateNestedFormData = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof ProductFormData],
        [field]: value
      }
    }))
  }

  // Ebatlı Ürün Fiyat Listesi Fonksiyonları
  const addDimensionPrice = () => {
    setFormData(prev => ({
      ...prev,
      dimensionPrices: [...prev.dimensionPrices, {
        id: generateId(),
        thickness: 2,
        width: 60,
        length: 60,
        surfaceFinish: 'Ham',
        packaging: 'Paletüstü',
        delivery: 'Fabrika Teslim',
        price: 0,
        currency: 'USD'
      }]
    }))
  }

  const removeDimensionPrice = (index: number) => {
    setFormData(prev => ({
      ...prev,
      dimensionPrices: prev.dimensionPrices.filter((_, i) => i !== index)
    }))
  }

  const updateDimensionPrice = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      dimensionPrices: prev.dimensionPrices.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  // Plaka Ürün Fiyat Listesi Fonksiyonları
  const addSlabPrice = () => {
    setFormData(prev => ({
      ...prev,
      slabPrices: [...prev.slabPrices, {
        id: generateId(),
        thickness: 2,
        width: 60,
        length: 60,
        surfaceFinish: 'Ham',
        packaging: 'Bandılsız',
        delivery: 'Fabrika Teslim',
        price: 0,
        currency: 'USD'
      }]
    }))
  }

  const removeSlabPrice = (index: number) => {
    setFormData(prev => ({
      ...prev,
      slabPrices: prev.slabPrices.filter((_, i) => i !== index)
    }))
  }

  const updateSlabPrice = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      slabPrices: prev.slabPrices.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  // Analiz Raporu Fonksiyonları
  const addAnalysisReport = () => {
    setFormData(prev => ({
      ...prev,
      analysisReports: [...prev.analysisReports, {
        id: generateId(),
        reportName: '',
        value: '',
        unit: '',
        file: undefined
      }]
    }))
  }

  const removeAnalysisReport = (index: number) => {
    setFormData(prev => ({
      ...prev,
      analysisReports: prev.analysisReports.filter((_, i) => i !== index)
    }))
  }

  const updateAnalysisReport = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      analysisReports: prev.analysisReports.map((report, i) =>
        i === index ? { ...report, [field]: value } : report
      )
    }))
  }

  // Yüzey İşlemi Fiyat Güncelleme Fonksiyonları
  const updateDimensionSurfaceFinishPrice = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      dimensionSurfaceFinishPrices: prev.dimensionSurfaceFinishPrices.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const updateSlabSurfaceFinishPrice = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      slabSurfaceFinishPrices: prev.slabSurfaceFinishPrices.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  // Ambalaj Fiyat Güncelleme Fonksiyonları
  const updateDimensionPackagingPrice = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      dimensionPackagingPrices: prev.dimensionPackagingPrices.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const updateSlabPackagingPrice = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      slabPackagingPrices: prev.slabPackagingPrices.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const addCustomSpec = () => {
    setFormData(prev => ({
      ...prev,
      technicalSpecs: {
        ...prev.technicalSpecs,
        customSpecs: [...prev.technicalSpecs.customSpecs, { name: '', value: '', unit: '' }]
      }
    }))
  }

  const removeCustomSpec = (index: number) => {
    setFormData(prev => ({
      ...prev,
      technicalSpecs: {
        ...prev.technicalSpecs,
        customSpecs: prev.technicalSpecs.customSpecs.filter((_, i) => i !== index)
      }
    }))
  }

  const updateCustomSpec = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      technicalSpecs: {
        ...prev.technicalSpecs,
        customSpecs: prev.technicalSpecs.customSpecs.map((spec, i) => 
          i === index ? { ...spec, [field]: value } : spec
        )
      }
    }))
  }

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    setError('')

    try {
      // Validation
      if (!formData.name.trim()) {
        setError('Ürün adı gereklidir')
        return
      }

      if (!formData.category) {
        setError('Kategori seçimi gereklidir')
        return
      }

      if (!formData.media.coverImage?.url && !formData.media.coverImage?.file) {
        setError('Kapak resmi zorunludur')
        return
      }

      if (formData.dimensionPrices.length === 0 && formData.slabPrices.length === 0) {
        setError('En az bir fiyat listesi eklemelisiniz')
        return
      }

      // Durum kontrolü - Aktif ise admin onayına gönder
      const finalFormData = {
        ...formData,
        approvalStatus: formData.status === 'active' ? 'pending' : undefined,
        submittedAt: formData.status === 'active' ? new Date() : undefined
      }

      const success = await onSave(finalFormData)
      if (!success) {
        setError('Ürün kaydetme işlemi başarısız oldu')
      } else {
        // Show success message based on status
        if (formData.status === 'active') {
          alert('Ürün admin onayına gönderildi! Onaylandıktan sonra müşteriler tarafından görülebilir olacaktır.')
        } else {
          alert('Ürün başarıyla kaydedildi!')
        }
        onClose()
      }
    } catch (error) {
      setError('Ürün kaydetme sırasında bir hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Package className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Temel Bilgiler</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ürün Adı *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Kategori *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => updateFormData('category', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                >
                  <option value="">Seçiniz</option>
                  {CATEGORIES.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Menşei
                </label>
                <input
                  type="text"
                  value={formData.origin}
                  onChange={(e) => updateFormData('origin', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Durum
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => updateFormData('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                >
                  <option value="draft">Taslak</option>
                  <option value="active">Aktif</option>
                  <option value="inactive">Pasif</option>
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ürün Açıklaması
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => updateFormData('description', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  placeholder="Ürününüzün özelliklerini, kullanım alanlarını ve avantajlarını açıklayın"
                />
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <Upload className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Ürün Medya Yükleme</h3>
            </div>

            {/* Kapak Resmi */}
            <Card className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-red-600">Kapak Resmi (Zorunlu) *</h4>
              </div>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                {formData.media.coverImage?.url ? (
                  <div className="space-y-2">
                    <img
                      src={formData.media.coverImage.url}
                      alt="Kapak resmi"
                      className="max-h-32 mx-auto rounded"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const input = document.createElement('input')
                        input.type = 'file'
                        input.accept = 'image/*'
                        input.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0]
                          if (file) {
                            const url = URL.createObjectURL(file)
                            updateNestedFormData('media', 'coverImage', { file, url })
                          }
                        }
                        input.click()
                      }}
                    >
                      Değiştir
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                    <p className="text-sm text-gray-600">
                      Kapak resmi yüklemek için tıklayın
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const input = document.createElement('input')
                        input.type = 'file'
                        input.accept = 'image/*'
                        input.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0]
                          if (file) {
                            const url = URL.createObjectURL(file)
                            updateNestedFormData('media', 'coverImage', { file, url })
                          }
                        }
                        input.click()
                      }}
                    >
                      Kapak Resmi Seç
                    </Button>
                  </div>
                )}
              </div>
            </Card>

            {/* Plaka Resimleri */}
            <Card className="p-4">
              <h4 className="font-medium mb-3">Plaka Resimleri (3 Adet)</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {formData.media.slabImages.map((image, index) => (
                  <div key={index} className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                    {image.url ? (
                      <div className="space-y-2">
                        <img
                          src={image.url}
                          alt={`Plaka resmi ${index + 1}`}
                          className="max-h-24 mx-auto rounded"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const input = document.createElement('input')
                            input.type = 'file'
                            input.accept = 'image/*'
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0]
                              if (file) {
                                const url = URL.createObjectURL(file)
                                const newSlabImages = [...formData.media.slabImages]
                                newSlabImages[index] = { file, url }
                                updateNestedFormData('media', 'slabImages', newSlabImages)
                              }
                            }
                            input.click()
                          }}
                        >
                          Değiştir
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Upload className="w-6 h-6 text-gray-400 mx-auto" />
                        <p className="text-xs text-gray-600">Plaka {index + 1}</p>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const input = document.createElement('input')
                            input.type = 'file'
                            input.accept = 'image/*'
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0]
                              if (file) {
                                const url = URL.createObjectURL(file)
                                const newSlabImages = [...formData.media.slabImages]
                                newSlabImages[index] = { file, url }
                                updateNestedFormData('media', 'slabImages', newSlabImages)
                              }
                            }
                            input.click()
                          }}
                        >
                          Seç
                        </Button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </Card>

            {/* Mokap Resimleri */}
            <Card className="p-4">
              <h4 className="font-medium mb-3">Mokap Yere Serili Resimler (3 Adet)</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {formData.media.mockupImages.map((image, index) => (
                  <div key={index} className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                    {image.url ? (
                      <div className="space-y-2">
                        <img
                          src={image.url}
                          alt={`Mokap resmi ${index + 1}`}
                          className="max-h-24 mx-auto rounded"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const input = document.createElement('input')
                            input.type = 'file'
                            input.accept = 'image/*'
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0]
                              if (file) {
                                const url = URL.createObjectURL(file)
                                const newMockupImages = [...formData.media.mockupImages]
                                newMockupImages[index] = { file, url }
                                updateNestedFormData('media', 'mockupImages', newMockupImages)
                              }
                            }
                            input.click()
                          }}
                        >
                          Değiştir
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Upload className="w-6 h-6 text-gray-400 mx-auto" />
                        <p className="text-xs text-gray-600">Mokap {index + 1}</p>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const input = document.createElement('input')
                            input.type = 'file'
                            input.accept = 'image/*'
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0]
                              if (file) {
                                const url = URL.createObjectURL(file)
                                const newMockupImages = [...formData.media.mockupImages]
                                newMockupImages[index] = { file, url }
                                updateNestedFormData('media', 'mockupImages', newMockupImages)
                              }
                            }
                            input.click()
                          }}
                        >
                          Seç
                        </Button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </Card>

            {/* Video */}
            <Card className="p-4">
              <h4 className="font-medium mb-3">Sessiz Video (10 saniye)</h4>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                {formData.media.video?.url ? (
                  <div className="space-y-2">
                    <video
                      src={formData.media.video.url}
                      className="max-h-32 mx-auto rounded"
                      controls
                      muted
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const input = document.createElement('input')
                        input.type = 'file'
                        input.accept = 'video/*'
                        input.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0]
                          if (file) {
                            const url = URL.createObjectURL(file)
                            updateNestedFormData('media', 'video', { file, url })
                          }
                        }
                        input.click()
                      }}
                    >
                      Değiştir
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                    <p className="text-sm text-gray-600">
                      10 saniyelik sessiz video yükleyin
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const input = document.createElement('input')
                        input.type = 'file'
                        input.accept = 'video/*'
                        input.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0]
                          if (file) {
                            const url = URL.createObjectURL(file)
                            updateNestedFormData('media', 'video', { file, url })
                          }
                        }
                        input.click()
                      }}
                    >
                      Video Seç
                    </Button>
                  </div>
                )}
              </div>
            </Card>
          </div>
        )

      case 3:
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Ruler className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Teknik Özellikler</h3>
            </div>

            <div className="space-y-6">
              {/* Standart Teknik Özellikler */}
              <div>
                <h4 className="font-medium mb-3">Standart Özellikler</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Yoğunluk (kg/m³)
                    </label>
                    <input
                      type="text"
                      value={formData.technicalSpecs.density || ''}
                      onChange={(e) => updateNestedFormData('technicalSpecs', 'density', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Su Emme Oranı (%)
                    </label>
                    <input
                      type="text"
                      value={formData.technicalSpecs.waterAbsorption || ''}
                      onChange={(e) => updateNestedFormData('technicalSpecs', 'waterAbsorption', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Basınç Dayanımı (MPa)
                    </label>
                    <input
                      type="text"
                      value={formData.technicalSpecs.compressiveStrength || ''}
                      onChange={(e) => updateNestedFormData('technicalSpecs', 'compressiveStrength', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Eğilme Dayanımı (MPa)
                    </label>
                    <input
                      type="text"
                      value={formData.technicalSpecs.flexuralStrength || ''}
                      onChange={(e) => updateNestedFormData('technicalSpecs', 'flexuralStrength', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Don Direnci
                    </label>
                    <input
                      type="text"
                      value={formData.technicalSpecs.frostResistance || ''}
                      onChange={(e) => updateNestedFormData('technicalSpecs', 'frostResistance', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Aşınma Direnci
                    </label>
                    <input
                      type="text"
                      value={formData.technicalSpecs.abrasionResistance || ''}
                      onChange={(e) => updateNestedFormData('technicalSpecs', 'abrasionResistance', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>
                </div>
              </div>

              {/* Özel Özellikler */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium">Özel Özellikler</h4>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addCustomSpec}
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Özellik Ekle
                  </Button>
                </div>

                {formData.technicalSpecs.customSpecs.map((spec, index) => (
                  <Card key={index} className="p-3 mb-3">
                    <div className="flex items-center gap-3">
                      <div className="flex-1">
                        <input
                          type="text"
                          value={spec.name}
                          onChange={(e) => updateCustomSpec(index, 'name', e.target.value)}
                          placeholder="Özellik adı"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                        />
                      </div>
                      <div className="flex-1">
                        <input
                          type="text"
                          value={spec.value}
                          onChange={(e) => updateCustomSpec(index, 'value', e.target.value)}
                          placeholder="Değer"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                        />
                      </div>
                      <div className="w-24">
                        <input
                          type="text"
                          value={spec.unit}
                          onChange={(e) => updateCustomSpec(index, 'unit', e.target.value)}
                          placeholder="Birim"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                        />
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeCustomSpec(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <DollarSign className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Ebatlı Ürün Fiyat Listesi</h3>
            </div>

            {/* Ebatlı Ürün Fiyat Tablosu */}
            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium">Ebat Fiyat Listesi</h4>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addDimensionPrice}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Ebat Ekle
                </Button>
              </div>

              <div className="overflow-x-auto max-w-full">
                <table className="min-w-full border-collapse border border-gray-300 text-sm">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[50px]">Sıra</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[100px]">Kalınlık (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[80px]">En (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[80px]">Boy (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[100px]">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[100px]">Ambalaj</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[120px]">Teslimat</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[100px]">Fiyat (m²)</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[100px]">Para Birimi</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[80px]">İşlem</th>
                    </tr>
                  </thead>
                  <tbody>
                    {formData.dimensionPrices.map((item, index) => (
                      <tr key={item.id}>
                        <td className="border border-gray-300 px-2 py-1 text-center text-sm">{index + 1}</td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.thickness}
                            onChange={(e) => updateDimensionPrice(index, 'thickness', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            step="0.1"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.width}
                            onChange={(e) => updateDimensionPrice(index, 'width', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.length}
                            onChange={(e) => updateDimensionPrice(index, 'length', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1 text-center text-sm bg-gray-50">Ham</td>
                        <td className="border border-gray-300 px-1 py-1 text-center text-sm bg-gray-50">Paletüstü</td>
                        <td className="border border-gray-300 px-1 py-1 text-center text-sm bg-gray-50">Fabrika Teslim</td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.price}
                            onChange={(e) => updateDimensionPrice(index, 'price', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            step="0.01"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <select
                            value={item.currency}
                            onChange={(e) => updateDimensionPrice(index, 'currency', e.target.value)}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                          >
                            {CURRENCIES.map(currency => (
                              <option key={currency} value={currency}>{currency}</option>
                            ))}
                          </select>
                        </td>
                        <td className="border border-gray-300 px-1 py-1 text-center">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeDimensionPrice(index)}
                            className="text-red-600 hover:text-red-700 p-1"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <Palette className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Ebatlı Ürün Yüzey İşlemi Fiyat Listesi</h3>
            </div>

            <Card className="p-4">
              <h4 className="font-medium mb-4">Yüzey İşlemi Fiyat Listesi</h4>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Sıra</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Yapabilirim/Yapamam</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Fiyat (m²)</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Para Birimi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {formData.dimensionSurfaceFinishPrices.map((item, index) => (
                      <tr key={item.id}>
                        <td className="border border-gray-300 px-2 py-1 text-center text-sm">{index + 1}</td>
                        <td className="border border-gray-300 px-2 py-1 text-sm bg-gray-50">{item.finish}</td>
                        <td className="border border-gray-300 px-1 py-1">
                          <select
                            value={item.available ? 'true' : 'false'}
                            onChange={(e) => updateDimensionSurfaceFinishPrice(index, 'available', e.target.value === 'true')}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                          >
                            <option value="false">Yapamam</option>
                            <option value="true">Yapabilirim</option>
                          </select>
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.price}
                            onChange={(e) => updateDimensionSurfaceFinishPrice(index, 'price', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            step="0.01"
                            disabled={!item.available}
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <select
                            value={item.currency}
                            onChange={(e) => updateDimensionSurfaceFinishPrice(index, 'currency', e.target.value)}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            disabled={!item.available}
                          >
                            {CURRENCIES.map(currency => (
                              <option key={currency} value={currency}>{currency}</option>
                            ))}
                          </select>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>

            {/* Ambalaj Fiyat Listesi */}
            <Card className="p-4">
              <h4 className="font-medium mb-4">Ambalaj Fiyat Listesi</h4>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Sıra</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Ambalaj</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Fiyat (m²)</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Para Birimi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {formData.dimensionPackagingPrices.map((item, index) => (
                      <tr key={item.id}>
                        <td className="border border-gray-300 px-2 py-1 text-center text-sm">{index + 1}</td>
                        <td className="border border-gray-300 px-2 py-1 text-sm bg-gray-50">{item.packaging}</td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.price}
                            onChange={(e) => updateDimensionPackagingPrice(index, 'price', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            step="0.01"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <select
                            value={item.currency}
                            onChange={(e) => updateDimensionPackagingPrice(index, 'currency', e.target.value)}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                          >
                            {CURRENCIES.map(currency => (
                              <option key={currency} value={currency}>{currency}</option>
                            ))}
                          </select>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>
        )

      case 6:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <Box className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Plaka Ürün Fiyat Listesi</h3>
            </div>

            {/* Plaka Ürün Fiyat Tablosu */}
            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium">Plaka Fiyat Listesi</h4>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addSlabPrice}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Ebat Ekle
                </Button>
              </div>

              <div className="overflow-x-auto max-w-full">
                <table className="min-w-full border-collapse border border-gray-300 text-sm">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[50px]">Sıra</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[100px]">Kalınlık (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[80px]">En (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[80px]">Boy (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[100px]">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[100px]">Ambalaj</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[120px]">Teslimat</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[100px]">Fiyat (m²)</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[100px]">Para Birimi</th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium min-w-[80px]">İşlem</th>
                    </tr>
                  </thead>
                  <tbody>
                    {formData.slabPrices.map((item, index) => (
                      <tr key={item.id}>
                        <td className="border border-gray-300 px-2 py-1 text-center text-sm">{index + 1}</td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.thickness}
                            onChange={(e) => updateSlabPrice(index, 'thickness', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            step="0.1"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.width}
                            onChange={(e) => updateSlabPrice(index, 'width', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.length}
                            onChange={(e) => updateSlabPrice(index, 'length', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1 text-center text-sm bg-gray-50">Ham</td>
                        <td className="border border-gray-300 px-1 py-1 text-center text-sm bg-gray-50">Bandılsız</td>
                        <td className="border border-gray-300 px-1 py-1 text-center text-sm bg-gray-50">Fabrika Teslim</td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.price}
                            onChange={(e) => updateSlabPrice(index, 'price', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            step="0.01"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <select
                            value={item.currency}
                            onChange={(e) => updateSlabPrice(index, 'currency', e.target.value)}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                          >
                            {CURRENCIES.map(currency => (
                              <option key={currency} value={currency}>{currency}</option>
                            ))}
                          </select>
                        </td>
                        <td className="border border-gray-300 px-1 py-1 text-center">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeSlabPrice(index)}
                            className="text-red-600 hover:text-red-700 p-1"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>
        )

      case 7:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <Palette className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Plaka Yüzey İşlemi ve Ambalaj Fiyat Listesi</h3>
            </div>

            {/* Plaka Yüzey İşlemi Fiyat Listesi */}
            <Card className="p-4">
              <h4 className="font-medium mb-4">Plaka Yüzey İşlemi Fiyat Listesi</h4>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Sıra</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Yapabilirim/Yapamam</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Fiyat (m²)</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Para Birimi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {formData.slabSurfaceFinishPrices.map((item, index) => (
                      <tr key={item.id}>
                        <td className="border border-gray-300 px-2 py-1 text-center text-sm">{index + 1}</td>
                        <td className="border border-gray-300 px-2 py-1 text-sm bg-gray-50">{item.finish}</td>
                        <td className="border border-gray-300 px-1 py-1">
                          <select
                            value={item.available ? 'true' : 'false'}
                            onChange={(e) => updateSlabSurfaceFinishPrice(index, 'available', e.target.value === 'true')}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                          >
                            <option value="false">Yapamam</option>
                            <option value="true">Yapabilirim</option>
                          </select>
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.price}
                            onChange={(e) => updateSlabSurfaceFinishPrice(index, 'price', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            step="0.01"
                            disabled={!item.available}
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <select
                            value={item.currency}
                            onChange={(e) => updateSlabSurfaceFinishPrice(index, 'currency', e.target.value)}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            disabled={!item.available}
                          >
                            {CURRENCIES.map(currency => (
                              <option key={currency} value={currency}>{currency}</option>
                            ))}
                          </select>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>

            {/* Plaka Ambalaj Fiyat Listesi */}
            <Card className="p-4">
              <h4 className="font-medium mb-4">Plaka Ambalaj Fiyat Listesi</h4>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Sıra</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Ambalaj</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Fiyat</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Para Birimi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {formData.slabPackagingPrices.map((item, index) => (
                      <tr key={item.id}>
                        <td className="border border-gray-300 px-2 py-1 text-center text-sm">{index + 1}</td>
                        <td className="border border-gray-300 px-2 py-1 text-sm bg-gray-50">{item.packaging}</td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="number"
                            value={item.price}
                            onChange={(e) => updateSlabPackagingPrice(index, 'price', Number(e.target.value))}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            step="0.01"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <select
                            value={item.currency}
                            onChange={(e) => updateSlabPackagingPrice(index, 'currency', e.target.value)}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                          >
                            {CURRENCIES.map(currency => (
                              <option key={currency} value={currency}>{currency}</option>
                            ))}
                          </select>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>
        )

      case 8:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <Ruler className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Taş Analiz Raporları</h3>
            </div>

            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium">Analiz Raporları</h4>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addAnalysisReport}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Rapor Ekle
                </Button>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Sıra</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Rapor Adı</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Değer</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Birim</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">Rapor Dosyası</th>
                      <th className="border border-gray-300 px-2 py-2 text-xs font-medium">İşlem</th>
                    </tr>
                  </thead>
                  <tbody>
                    {formData.analysisReports.map((report, index) => (
                      <tr key={report.id}>
                        <td className="border border-gray-300 px-2 py-1 text-center text-sm">{index + 1}</td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="text"
                            value={report.reportName}
                            onChange={(e) => updateAnalysisReport(index, 'reportName', e.target.value)}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            placeholder="Rapor adı"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="text"
                            value={report.value}
                            onChange={(e) => updateAnalysisReport(index, 'value', e.target.value)}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            placeholder="Değer"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1">
                          <input
                            type="text"
                            value={report.unit}
                            onChange={(e) => updateAnalysisReport(index, 'unit', e.target.value)}
                            className="w-full px-1 py-1 text-sm border-0 focus:ring-1 focus:ring-amber-500"
                            placeholder="Birim"
                          />
                        </td>
                        <td className="border border-gray-300 px-1 py-1 text-center">
                          {report.file?.url ? (
                            <div className="flex items-center gap-1">
                              <span className="text-xs text-green-600">✓</span>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  const input = document.createElement('input')
                                  input.type = 'file'
                                  input.accept = '.pdf,.jpg,.jpeg,.png'
                                  input.onchange = (e) => {
                                    const file = (e.target as HTMLInputElement).files?.[0]
                                    if (file) {
                                      const url = URL.createObjectURL(file)
                                      updateAnalysisReport(index, 'file', { file, url })
                                    }
                                  }
                                  input.click()
                                }}
                                className="text-xs p-1"
                              >
                                Değiştir
                              </Button>
                            </div>
                          ) : (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const input = document.createElement('input')
                                input.type = 'file'
                                input.accept = '.pdf,.jpg,.jpeg,.png'
                                input.onchange = (e) => {
                                  const file = (e.target as HTMLInputElement).files?.[0]
                                  if (file) {
                                    const url = URL.createObjectURL(file)
                                    updateAnalysisReport(index, 'file', { file, url })
                                  }
                                }
                                input.click()
                              }}
                              className="text-xs p-1"
                            >
                              Dosya Seç
                            </Button>
                          )}
                        </td>
                        <td className="border border-gray-300 px-1 py-1 text-center">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeAnalysisReport(index)}
                            className="text-red-600 hover:text-red-700 p-1"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {formData.analysisReports.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>Henüz analiz raporu eklenmemiş.</p>
                  <p className="text-sm">Yukarıdaki "Rapor Ekle" butonunu kullanarak rapor ekleyebilirsiniz.</p>
                </div>
              )}
            </Card>


          </div>
        )

      // Diğer adımlar için placeholder
      default:
        return (
          <div className="text-center py-8">
            <p className="text-gray-600">Adım {currentStep} içeriği yakında eklenecektir.</p>
          </div>
        )
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-900">
              {mode === 'create' ? 'Yeni Ürün Ekle' : 'Ürün Düzenle'}
            </h2>
            <p className="text-sm text-gray-600">
              Adım {currentStep} / {totalSteps}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-amber-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {renderStepContent()}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-white">
          <div className="flex gap-3">
            {currentStep > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
              >
                Geri
              </Button>
            )}
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              İptal
            </Button>
          </div>

          <div className="flex gap-3">
            {currentStep < totalSteps ? (
              <Button
                type="button"
                onClick={nextStep}
                className="bg-amber-600 hover:bg-amber-700"
              >
                İleri
              </Button>
            ) : (
              <Button
                type="submit"
                disabled={isLoading}
                onClick={handleSubmit}
                className="bg-amber-600 hover:bg-amber-700"
              >
                {isLoading ? 'Kaydediliyor...' : (mode === 'create' ? 'Ürün Ekle' : 'Güncelle')}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
