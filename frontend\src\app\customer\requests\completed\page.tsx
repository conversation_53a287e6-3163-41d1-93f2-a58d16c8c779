'use client';

import React from 'react';
import CompletedRequestsPage from '@/components/dashboard/pages/CompletedRequestsPage';
import { useRouter } from 'next/navigation';

export default function CustomerCompletedRequestsPage() {
  const router = useRouter();
  
  const handleNavigate = (route: string) => {
    router.push(route);
  };

  return <CompletedRequestsPage onNavigate={handleNavigate} />;
}
