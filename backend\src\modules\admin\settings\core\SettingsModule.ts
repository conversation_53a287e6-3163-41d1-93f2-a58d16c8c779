import { SettingsCategory, AdminSetting, SettingValue } from '../types/settings.types';

export abstract class SettingsModule {
  protected abstract category: SettingsCategory;

  /**
   * Get default settings for this module
   */
  protected abstract getDefaultSettings(): Record<string, {
    key: string;
    value: SettingValue;
    type: string;
    category: SettingsCategory;
    description: string;
  }>;

  /**
   * Validate a setting value
   */
  abstract validateSetting(key: string, value: any): Promise<boolean>;

  /**
   * Get all settings for this category
   */
  async getSettings(): Promise<AdminSetting[]> {
    const defaults = this.getDefaultSettings();
    const settings: AdminSetting[] = [];

    for (const [settingKey, defaultSetting] of Object.entries(defaults)) {
      settings.push({
        id: `${this.category}_${settingKey}`,
        category: this.category,
        key: defaultSetting.key,
        value: defaultSetting.value,
        dataType: defaultSetting.type as any,
        description: defaultSetting.description,
        isSensitive: this.isSensitiveSetting(defaultSetting.key),
        requiresRestart: this.requiresRestart(defaultSetting.key),
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    return settings;
  }

  /**
   * Get a specific setting by key
   */
  async getSetting(key: string): Promise<AdminSetting | null> {
    const settings = await this.getSettings();
    return settings.find(s => s.key === key) || null;
  }

  /**
   * Update a setting
   */
  async updateSetting(key: string, value: SettingValue): Promise<AdminSetting> {
    const isValid = await this.validateSetting(key, value);
    if (!isValid) {
      throw new Error(`Invalid value for setting: ${key}`);
    }

    const setting = await this.getSetting(key);
    if (!setting) {
      throw new Error(`Setting not found: ${key}`);
    }

    setting.value = value;
    setting.updatedAt = new Date();

    return setting;
  }

  /**
   * Check if a setting is sensitive (should be masked in UI)
   */
  protected isSensitiveSetting(key: string): boolean {
    const sensitiveKeys = [
      'password', 'secret', 'key', 'token', 'api_key',
      'private_key', 'auth_token', 'access_key'
    ];
    
    return sensitiveKeys.some(sensitive => 
      key.toLowerCase().includes(sensitive)
    );
  }

  /**
   * Check if a setting requires application restart
   */
  protected requiresRestart(key: string): boolean {
    const restartKeys = [
      'port', 'host', 'database_url', 'redis_url',
      'log_level', 'debug_mode'
    ];
    
    return restartKeys.some(restart => 
      key.toLowerCase().includes(restart)
    );
  }

  /**
   * Get category name
   */
  getCategory(): SettingsCategory {
    return this.category;
  }

  /**
   * Initialize default settings
   */
  async initializeDefaults(): Promise<void> {
    // This would typically save defaults to database
    // For now, just validate that defaults are correct
    const defaults = this.getDefaultSettings();
    
    for (const [key, setting] of Object.entries(defaults)) {
      const isValid = await this.validateSetting(setting.key, setting.value);
      if (!isValid) {
        console.warn(`Invalid default value for ${setting.key} in ${this.category}`);
      }
    }
  }
}
