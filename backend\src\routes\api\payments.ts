import { Router, Request, Response } from 'express';
import { authMiddleware } from '../../middleware/authMiddleware';
import { asyncHandler } from '../../middleware/errorHandler';
import { PrismaClient, PaymentStatus } from '@prisma/client';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();
const prisma = new PrismaClient();

// Configure multer for receipt uploads
const storage = multer.diskStorage({
  destination: (req: Request, file: Express.Multer.File, cb: (error: Error | null, destination: string) => void) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'receipts');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req: Request, file: Express.Multer.File, cb: (error: Error | null, filename: string) => void) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `receipt-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req: Request, file: Express.Multer.File, cb: (error: Error | null, acceptFile?: boolean) => void) => {
    const allowedTypes = /jpeg|jpg|png|pdf/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Sadece JPEG, PNG ve PDF dosyaları kabul edilir'));
    }
  }
});

// Apply authentication middleware to all routes
router.use(authMiddleware);

/**
 * @route GET /api/payments/my-payments
 * @desc Get user's payment requests
 * @access Private (Customer)
 */
router.get('/my-payments', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'customer') {
    return res.status(403).json({
      success: false,
      error: 'Only customers can view their payments'
    });
  }

  const payments = await prisma.payment.findMany({
    where: {
      userId: user.id,
    },
    include: {
      order: {
        include: {
          orderItems: {
            include: {
              product: true
            }
          }
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  // Transform data for frontend
  const transformedPayments = payments.map(payment => ({
    id: payment.id,
    orderId: payment.orderId,
    amount: payment.amount,
    currency: payment.currency || 'TRY',
    status: payment.status,
    createdAt: payment.createdAt,
    dueDate: payment.dueDate,
    bankInfo: {
      bankName: process.env.PLATFORM_BANK_NAME || 'Türkiye İş Bankası',
      iban: process.env.PLATFORM_IBAN || 'TR33 0006 4000 0011 2345 6789 01',
      accountHolder: process.env.PLATFORM_ACCOUNT_HOLDER || 'Doğal Taş Pazaryeri Ltd. Şti.',
      referenceCode: payment.referenceCode || `REF${payment.id.slice(-6).toUpperCase()}`
    },
    receipt: payment.receiptUrl ? {
      url: `/api/payments/${payment.id}/receipt`,
      uploadedAt: payment.receiptUploadedAt || payment.updatedAt
    } : null,
    rejectionReason: payment.rejectionReason
  }));

  res.json({
    success: true,
    data: { payments: transformedPayments }
  });
}));

/**
 * @route POST /api/payments/:paymentId/receipt
 * @desc Upload payment receipt
 * @access Private (Customer)
 */
router.post('/:paymentId/receipt', upload.single('receipt'), asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  const { paymentId } = req.params;
  const { bankReference } = req.body;
  
  if (!user || user.userType !== 'customer') {
    return res.status(403).json({
      success: false,
      error: 'Only customers can upload receipts'
    });
  }

  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: 'Receipt file is required'
    });
  }

  if (!bankReference) {
    return res.status(400).json({
      success: false,
      error: 'Bank reference number is required'
    });
  }

  // Check if payment exists and belongs to user
  const payment = await prisma.payment.findFirst({
    where: {
      id: paymentId,
      userId: user.id
    }
  });

  if (!payment) {
    return res.status(404).json({
      success: false,
      error: 'Payment not found'
    });
  }

  if (payment.status !== PaymentStatus.PENDING) {
    return res.status(400).json({
      success: false,
      error: 'Receipt can only be uploaded for pending payments'
    });
  }

  // Update payment with receipt information
  const receiptUrl = `/uploads/receipts/${req.file!.filename}`;

  const updatedPayment = await prisma.payment.update({
    where: { id: paymentId },
    data: {
      receiptUrl,
      receiptUploadedAt: new Date(),
      bankReference,
      status: PaymentStatus.RECEIPT_UPLOADED
    }
  });

  // Create notification for admin
  await prisma.notification.create({
    data: {
      userId: 'admin', // Admin notification
      title: 'Yeni Ödeme Dekontu',
      message: `${user.email} tarafından ödeme dekontu yüklendi`,
      notificationType: 'PAYMENT_RECEIPT_UPLOADED',
      data: {
        paymentId,
        customerId: user.id,
        amount: payment.amount
      }
    }
  });

  res.json({
    success: true,
    message: 'Receipt uploaded successfully',
    data: { payment: updatedPayment }
  });
}));

/**
 * @route GET /api/payments/admin/pending-receipts
 * @desc Get pending receipt verifications for admin
 * @access Private (Admin)
 */
router.get('/admin/pending-receipts', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const payments = await prisma.payment.findMany({
    where: {
      status: PaymentStatus.RECEIPT_UPLOADED
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          companyName: true,
          firstName: true,
          lastName: true
        }
      },
      order: {
        include: {
          orderItems: {
            include: {
              product: {
                select: {
                  name: true,
                  category: true
                }
              }
            }
          }
        }
      }
    },
    orderBy: {
      receiptUploadedAt: 'desc'
    }
  });

  const transformedPayments = payments.map(payment => ({
    id: payment.id,
    paymentId: payment.id,
    customerName: payment.user.companyName || `${payment.user.firstName} ${payment.user.lastName}`,
    customerEmail: payment.user.email,
    amount: payment.amount,
    currency: payment.currency || 'TRY',
    uploadedAt: payment.receiptUploadedAt || payment.updatedAt,
    status: payment.status.toLowerCase(),
    receiptUrl: payment.receiptUrl,
    bankDetails: {
      senderName: payment.user.companyName || `${payment.user.firstName} ${payment.user.lastName}`,
      referenceNumber: payment.bankReference,
      amount: payment.amount,
      transferDate: payment.receiptUploadedAt || payment.updatedAt
    },
    orderInfo: {
      orderId: payment.orderId,
      productName: payment.order?.orderItems?.[0]?.product?.name || 'N/A',
      quantity: payment.order?.orderItems?.reduce((total: number, item: any) => total + item.quantity, 0) || 0
    }
  }));

  res.json({
    success: true,
    data: { receipts: transformedPayments }
  });
}));

/**
 * @route POST /api/payments/admin/:paymentId/verify
 * @desc Verify or reject payment receipt
 * @access Private (Admin)
 */
router.post('/admin/:paymentId/verify', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  const { paymentId } = req.params;
  const { approved, reason } = req.body;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const payment = await prisma.payment.findUnique({
    where: { id: paymentId },
    include: {
      user: true,
      order: true
    }
  });

  if (!payment) {
    return res.status(404).json({
      success: false,
      error: 'Payment not found'
    });
  }

  if (payment.status !== PaymentStatus.RECEIPT_UPLOADED) {
    return res.status(400).json({
      success: false,
      error: 'Payment is not in receipt uploaded status'
    });
  }

  const newStatus = approved ? PaymentStatus.COMPLETED : PaymentStatus.FAILED;

  const updatedPayment = await prisma.payment.update({
    where: { id: paymentId },
    data: {
      status: newStatus,
      verifiedAt: new Date(),
      verifiedBy: user.id,
      rejectionReason: approved ? null : reason
    }
  });

  // Create notification for customer
  await prisma.notification.create({
    data: {
      userId: payment.userId,
      title: approved ? 'Ödeme Onaylandı' : 'Ödeme Reddedildi',
      message: approved
        ? 'Ödemeniz onaylandı ve siparişiniz işleme alındı'
        : `Ödemeniz reddedildi. Sebep: ${reason}`,
      notificationType: approved ? 'PAYMENT_APPROVED' : 'PAYMENT_REJECTED',
      data: {
        paymentId,
        orderId: payment.orderId,
        amount: payment.amount
      }
    }
  });

  // If approved, update order status
  if (approved && payment.order) {
    await prisma.order.update({
      where: { id: payment.orderId },
      data: {
        status: 'CONFIRMED',
        confirmedAt: new Date()
      }
    });
  }

  res.json({
    success: true,
    message: approved ? 'Payment verified successfully' : 'Payment rejected',
    data: { payment: updatedPayment }
  });
}));

/**
 * @route GET /api/payments/:paymentId/receipt
 * @desc Download payment receipt
 * @access Private
 */
router.get('/:paymentId/receipt', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  const { paymentId } = req.params;
  
  if (!user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  const payment = await prisma.payment.findUnique({
    where: { id: paymentId }
  });

  if (!payment) {
    return res.status(404).json({
      success: false,
      error: 'Payment not found'
    });
  }

  // Check access permissions
  if (user.userType === 'customer' && payment.userId !== user.id) {
    return res.status(403).json({
      success: false,
      error: 'Access denied'
    });
  }

  if (!payment.receiptUrl) {
    return res.status(404).json({
      success: false,
      error: 'Receipt not found'
    });
  }

  const filePath = path.join(process.cwd(), payment.receiptUrl);
  
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({
      success: false,
      error: 'Receipt file not found'
    });
  }

  res.download(filePath);
}));

export default router;
