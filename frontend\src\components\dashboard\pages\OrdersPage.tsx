'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ShoppingCartIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  TruckIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
  StarIcon
} from '@heroicons/react/24/outline';

interface ProductSummary {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  unitPrice: number;
}

interface Order {
  id: string;
  supplier: string;
  products: ProductSummary[];
  totalAmount: number;
  currency: string;
  status: 'confirmed' | 'production' | 'shipping' | 'delivered' | 'completed' | 'cancelled';
  timeline: {
    confirmed: Date;
    production?: Date;
    shipping?: Date;
    delivered?: Date;
  };
  tracking?: {
    carrier: string;
    trackingNumber: string;
    estimatedDelivery: Date;
  };
  orderDate: Date;
  deliveryAddress: string;
  paymentStatus: 'pending' | 'paid' | 'partial';
}

interface OrdersPageProps {
  onNavigate?: (route: string) => void;
}

const OrdersPage: React.FC<OrdersPageProps> = ({ onNavigate }) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'ongoing' | 'completed' | 'cancelled'>('ongoing');
  const [filters, setFilters] = useState({
    dateRange: { from: '', to: '' },
    supplier: '',
    status: ''
  });

  // Mock data
  const mockOrders: Order[] = [
    {
      id: 'ORD-001',
      supplier: 'ABC Mermer Ltd.',
      products: [
        { id: '1', name: 'Beyaz Mermer', category: 'Mermer', quantity: 50, unit: 'm²', unitPrice: 60 }
      ],
      totalAmount: 3000,
      currency: 'USD',
      status: 'shipping',
      timeline: {
        confirmed: new Date('2024-01-20'),
        production: new Date('2024-01-22'),
        shipping: new Date('2024-01-25')
      },
      tracking: {
        carrier: 'Aras Kargo',
        trackingNumber: 'AR123456789',
        estimatedDelivery: new Date('2024-01-28')
      },
      orderDate: new Date('2024-01-20'),
      deliveryAddress: 'İstanbul, Türkiye',
      paymentStatus: 'paid'
    },
    {
      id: 'ORD-002',
      supplier: 'XYZ Granit A.Ş.',
      products: [
        { id: '2', name: 'Siyah Granit', category: 'Granit', quantity: 30, unit: 'm²', unitPrice: 75 }
      ],
      totalAmount: 2250,
      currency: 'USD',
      status: 'production',
      timeline: {
        confirmed: new Date('2024-01-18'),
        production: new Date('2024-01-20')
      },
      orderDate: new Date('2024-01-18'),
      deliveryAddress: 'Ankara, Türkiye',
      paymentStatus: 'paid'
    },
    {
      id: 'ORD-003',
      supplier: 'DEF Traverten',
      products: [
        { id: '3', name: 'Bej Traverten', category: 'Traverten', quantity: 40, unit: 'm²', unitPrice: 45 }
      ],
      totalAmount: 1800,
      currency: 'USD',
      status: 'completed',
      timeline: {
        confirmed: new Date('2024-01-10'),
        production: new Date('2024-01-12'),
        shipping: new Date('2024-01-15'),
        delivered: new Date('2024-01-18')
      },
      orderDate: new Date('2024-01-10'),
      deliveryAddress: 'Antalya, Türkiye',
      paymentStatus: 'paid'
    }
  ];

  useEffect(() => {
    setTimeout(() => {
      setOrders(mockOrders);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircleIcon className="h-5 w-5 text-blue-500" />;
      case 'production':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'shipping':
        return <TruckIcon className="h-5 w-5 text-purple-500" />;
      case 'delivered':
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Onaylandı';
      case 'production':
        return 'Üretimde';
      case 'shipping':
        return 'Kargoda';
      case 'delivered':
        return 'Teslim Edildi';
      case 'completed':
        return 'Tamamlandı';
      case 'cancelled':
        return 'İptal Edildi';
      default:
        return 'Bilinmiyor';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'production':
        return 'bg-yellow-100 text-yellow-800';
      case 'shipping':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredOrders = orders.filter(order => {
    switch (activeTab) {
      case 'ongoing':
        return ['confirmed', 'production', 'shipping', 'delivered'].includes(order.status);
      case 'completed':
        return order.status === 'completed';
      case 'cancelled':
        return order.status === 'cancelled';
      default:
        return true;
    }
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <ShoppingCartIcon className="h-8 w-8 text-green-600 mr-3" />
              Siparişlerim
            </h1>
            <p className="text-gray-600 mt-2">
              Siparişlerinizi takip edin ve teslimat durumunu kontrol edin
            </p>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'ongoing', label: 'Devam Eden', count: filteredOrders.length },
              { id: 'completed', label: 'Tamamlanan', count: orders.filter(o => o.status === 'completed').length },
              { id: 'cancelled', label: 'İptal Edilen', count: orders.filter(o => o.status === 'cancelled').length }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Orders List */}
      {filteredOrders.length > 0 ? (
        <div className="space-y-6">
          {filteredOrders.map((order, index) => (
            <motion.div
              key={order.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200"
            >
              {/* Order Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(order.status)}
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                      {getStatusLabel(order.status)}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {order.id}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {order.supplier} • {order.orderDate.toLocaleDateString('tr-TR')}
                    </p>
                  </div>
                </div>

                <div className="text-right">
                  <p className="text-2xl font-bold text-gray-900">
                    ${order.totalAmount.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-600">{order.currency}</p>
                </div>
              </div>

              {/* Products */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Ürünler:</h4>
                <div className="space-y-2">
                  {order.products.map((product) => (
                    <div key={product.id} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                      <div>
                        <span className="font-medium text-gray-900">{product.name}</span>
                        <span className="text-gray-600 ml-2">({product.category})</span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900">
                          {product.quantity} {product.unit} × ${product.unitPrice}
                        </div>
                        <div className="text-sm text-gray-600">
                          = ${(product.quantity * product.unitPrice).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Timeline */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Sipariş Durumu:</h4>
                <div className="flex items-center space-x-4">
                  {[
                    { key: 'confirmed', label: 'Onaylandı', date: order.timeline.confirmed },
                    { key: 'production', label: 'Üretim', date: order.timeline.production },
                    { key: 'shipping', label: 'Kargo', date: order.timeline.shipping },
                    { key: 'delivered', label: 'Teslim', date: order.timeline.delivered }
                  ].map((step, stepIndex) => (
                    <div key={step.key} className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                        step.date 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-400'
                      }`}>
                        {stepIndex + 1}
                      </div>
                      <div className="ml-2 text-xs">
                        <div className={step.date ? 'text-gray-900 font-medium' : 'text-gray-400'}>
                          {step.label}
                        </div>
                        {step.date && (
                          <div className="text-gray-600">
                            {step.date.toLocaleDateString('tr-TR')}
                          </div>
                        )}
                      </div>
                      {stepIndex < 3 && (
                        <div className={`w-8 h-0.5 mx-2 ${
                          step.date ? 'bg-green-200' : 'bg-gray-200'
                        }`} />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Tracking Info */}
              {order.tracking && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="font-medium text-blue-900">Kargo Takibi</h5>
                      <p className="text-blue-700">
                        {order.tracking.carrier} - {order.tracking.trackingNumber}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-blue-600">Tahmini Teslimat</p>
                      <p className="font-medium text-blue-900">
                        {order.tracking.estimatedDelivery.toLocaleDateString('tr-TR')}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Delivery Address */}
              <div className="mb-4">
                <span className="text-sm text-gray-600">Teslimat Adresi: </span>
                <span className="text-sm font-medium text-gray-900">{order.deliveryAddress}</span>
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                  <EyeIcon className="h-4 w-4" />
                  <span>Detayları Görüntüle</span>
                </button>
                
                {order.tracking && (
                  <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <TruckIcon className="h-4 w-4" />
                    <span>Kargo Takibi</span>
                  </button>
                )}
                
                <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                  <ChatBubbleLeftRightIcon className="h-4 w-4" />
                  <span>Üretici ile İletişim</span>
                </button>
                
                {order.status === 'completed' && (
                  <button className="flex items-center space-x-2 px-4 py-2 border border-yellow-300 text-yellow-700 rounded-lg hover:bg-yellow-50 transition-colors">
                    <StarIcon className="h-4 w-4" />
                    <span>Değerlendir</span>
                  </button>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <ShoppingCartIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {activeTab === 'ongoing' && 'Devam eden siparişiniz bulunmuyor'}
            {activeTab === 'completed' && 'Tamamlanan siparişiniz bulunmuyor'}
            {activeTab === 'cancelled' && 'İptal edilen siparişiniz bulunmuyor'}
          </h3>
          <p className="text-gray-600 mb-6">
            {activeTab === 'ongoing' && 'Yeni bir teklif kabul ederek sipariş oluşturabilirsiniz.'}
            {activeTab === 'completed' && 'Tamamladığınız siparişler burada görünecek.'}
            {activeTab === 'cancelled' && 'İptal ettiğiniz siparişler burada görünecek.'}
          </p>
          {activeTab === 'ongoing' && (
            <button
              onClick={() => onNavigate?.('/customer/requests/active')}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
            >
              Taleplerime Git
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default OrdersPage;
