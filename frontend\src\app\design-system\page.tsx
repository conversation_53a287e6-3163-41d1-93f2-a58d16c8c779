"use client"

import * as React from "react"
import { Container } from "@/components/ui/container"
import { Grid } from "@/components/ui/grid"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import ProductCard from "@/components/ui/product-card"
import { BidCard } from "@/components/ui/bid-card"
import { CountdownTimer } from "@/components/ui/countdown-timer"
import { StoneTexture } from "@/components/ui/stone-texture"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { Navigation } from "@/components/ui/navigation"

/**
 * Design System Demo Page
 * Showcases all RFC-004 UI/UX components and design tokens
 */
export default function DesignSystemPage() {
  const demoProduct = {
    id: "demo-1",
    name: "Carrara Beyaz Mermer",
    category: "Mermer",
    price: { min: 45, max: 65, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.8,
    reviewCount: 127,
    location: "Afyon, Türkiye",
    producer: "ABC Mermer Ltd.",
    features: ["Cilalı", "60x60cm", "2cm Kalınlık"]
  }

  const demoBid = {
    id: "bid-1",
    rank: 1,
    price: 4200,
    currency: "$",
    deliveryTime: 15,
    deliveryUnit: "days" as const,
    paymentTerms: "%30 ön ödeme",
    notes: "Yüksek kalite garantisi ile",
    isWinning: true
  }

  const futureDate = new Date()
  futureDate.setHours(futureDate.getHours() + 18)

  return (
    <div className="min-h-screen bg-[var(--bg-primary)]">
      <Navigation
        brand={{ name: "Tasarım Sistemi Demo", href: "/design-system" }}
        links={[
          { name: "Ana Sayfa", href: "/" },
          { name: "Tasarım Sistemi", href: "/design-system", active: true }
        ]}
        actions={<ThemeToggle variant="button" showLabel />}
      />

      <main className="py-8">
        <Container>
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-[var(--text-primary)] mb-4">
              RFC-004 UI/UX Tasarım Sistemi
            </h1>
            <p className="text-lg text-[var(--text-secondary)]">
              Türkiye Doğal Taş Pazarı için tasarlanmış bileşen kütüphanesi
            </p>
          </div>

          {/* Color Palette */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Renk Paleti</h2>
            <Grid cols={1} responsive={{ md: 2, lg: 3 }} gap="lg">
              <Card>
                <CardHeader>
                  <CardTitle>Birincil Renkler</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[var(--primary-stone)] rounded"></div>
                      <span>Primary Stone</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[var(--primary-dark)] rounded"></div>
                      <span>Primary Dark</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[var(--primary-light)] rounded"></div>
                      <span>Primary Light</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>İkincil Renkler</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[var(--secondary-marble)] border rounded"></div>
                      <span>Marble</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[var(--secondary-granite)] rounded"></div>
                      <span>Granite</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[var(--secondary-travertine)] rounded"></div>
                      <span>Travertine</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Sistem Renkleri</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[var(--success)] rounded"></div>
                      <span>Success</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[var(--warning)] rounded"></div>
                      <span>Warning</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[var(--error)] rounded"></div>
                      <span>Error</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[var(--info)] rounded"></div>
                      <span>Info</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Grid>
          </section>

          {/* Buttons */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Butonlar</h2>
            <Card>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="font-semibold mb-3">Varyantlar</h3>
                    <div className="flex flex-wrap gap-3">
                      <Button variant="primary">Primary</Button>
                      <Button variant="secondary">Secondary</Button>
                      <Button variant="outline">Outline</Button>
                      <Button variant="ghost">Ghost</Button>
                      <Button variant="link">Link</Button>
                      <Button variant="destructive">Destructive</Button>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold mb-3">Boyutlar</h3>
                    <div className="flex flex-wrap items-center gap-3">
                      <Button size="sm">Small</Button>
                      <Button size="md">Medium</Button>
                      <Button size="lg">Large</Button>
                      <Button size="xl">Extra Large</Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>

          {/* Form Elements */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Form Elemanları</h2>
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4 max-w-md">
                  <Input placeholder="Normal input" />
                  <Input placeholder="Error input" error helperText="Bu alan zorunludur" />
                  <Input placeholder="Helper text ile" helperText="Yardımcı metin örneği" />
                </div>
              </CardContent>
            </Card>
          </section>

          {/* Stone Textures */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Doğal Taş Texture'ları</h2>
            <Grid cols={2} responsive={{ md: 4 }} gap="lg">
              {(['marble', 'granite', 'travertine', 'onyx'] as const).map((type) => (
                <Card key={type}>
                  <CardContent className="p-0">
                    <StoneTexture type={type} className="h-32 flex items-center justify-center">
                      <span className="text-white font-semibold bg-black bg-opacity-50 px-3 py-1 rounded">
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </span>
                    </StoneTexture>
                  </CardContent>
                </Card>
              ))}
            </Grid>
          </section>

          {/* Product Card */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Ürün Kartı</h2>
            <div className="max-w-sm">
              <ProductCard
                product={demoProduct}
                onViewDetails={(id) => console.log("View details:", id)}
                onRequestQuote={(id) => console.log("Request quote:", id)}
                onToggleFavorite={(id) => console.log("Toggle favorite:", id)}
                onView3D={(id) => console.log("View 3D:", id)}
                show3DViewer={true}
              />
            </div>
          </section>

          {/* Bid Card */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Teklif Kartı</h2>
            <div className="max-w-sm">
              <BidCard
                bid={demoBid}
                onSelect={(id) => console.log("Select bid:", id)}
                onViewDetails={(id) => console.log("View bid details:", id)}
              />
            </div>
          </section>

          {/* Countdown Timer */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Geri Sayım Timer</h2>
            <div className="space-y-4">
              <CountdownTimer targetDate={futureDate} />
              <CountdownTimer targetDate={futureDate} variant="urgent" size="sm" />
              <CountdownTimer targetDate={futureDate} variant="minimal" showLabels={false} />
            </div>
          </section>

          {/* Typography */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Tipografi</h2>
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <h1 className="text-6xl font-bold">Heading 1</h1>
                  <h2 className="text-5xl font-bold">Heading 2</h2>
                  <h3 className="text-4xl font-bold">Heading 3</h3>
                  <h4 className="text-3xl font-bold">Heading 4</h4>
                  <h5 className="text-2xl font-bold">Heading 5</h5>
                  <h6 className="text-xl font-bold">Heading 6</h6>
                  <p className="text-base">Normal paragraph text</p>
                  <p className="text-sm text-[var(--text-secondary)]">Small secondary text</p>
                </div>
              </CardContent>
            </Card>
          </section>
        </Container>
      </main>
    </div>
  )
}
