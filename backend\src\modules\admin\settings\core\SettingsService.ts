// Core Settings Service - RFC-012
import { SettingsAuditService } from './SettingsAudit';
import { SettingsValidation } from './SettingsValidation';
import { 
  AdminSetting, 
  SettingUpdate, 
  SettingsCategory, 
  SettingValue,
  SettingsQueryOptions 
} from '../types/settings.types';

export class SettingsService {
  private auditService: SettingsAuditService;
  private validation: SettingsValidation;
  private cache: Map<string, any> = new Map();

  constructor() {
    this.auditService = new SettingsAuditService();
    this.validation = new SettingsValidation();
  }

  /**
   * Get all settings or by category
   */
  async getSettings(options: SettingsQueryOptions = {}): Promise<AdminSetting[]> {
    const { category, includeDescription = false, includeSensitive = false } = options;
    
    try {
      // Check cache first
      const cacheKey = `settings:${category || 'all'}:${includeDescription}:${includeSensitive}`;
      if (this.cache.has(cacheKey)) {
        return this.cache.get(cacheKey);
      }

      // Mock data for now - will be replaced with database query
      const mockSettings: AdminSetting[] = [
        {
          id: '1',
          category: 'PLATFORM',
          key: 'siteName',
          value: 'Doğal Taş Pazaryeri',
          dataType: 'string',
          description: 'Site adı',
          isSensitive: false,
          requiresRestart: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '2',
          category: 'PLATFORM',
          key: 'maintenanceMode',
          value: false,
          dataType: 'boolean',
          description: 'Bakım modu durumu',
          isSensitive: false,
          requiresRestart: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '3',
          category: 'BUSINESS',
          key: 'commissionRateM2',
          value: 1.0,
          dataType: 'number',
          description: 'M² başına komisyon oranı ($)',
          isSensitive: false,
          requiresRestart: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '4',
          category: 'BUSINESS',
          key: 'commissionRateTon',
          value: 10.0,
          dataType: 'number',
          description: 'Ton başına komisyon oranı ($)',
          isSensitive: false,
          requiresRestart: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '5',
          category: 'SECURITY',
          key: 'passwordMinLength',
          value: 8,
          dataType: 'number',
          description: 'Minimum şifre uzunluğu',
          isSensitive: false,
          requiresRestart: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '6',
          category: 'SECURITY',
          key: 'require2FA',
          value: false,
          dataType: 'boolean',
          description: '2FA zorunluluğu',
          isSensitive: false,
          requiresRestart: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '7',
          category: 'NOTIFICATIONS',
          key: 'smtpHost',
          value: 'smtp.gmail.com',
          dataType: 'string',
          description: 'SMTP sunucu adresi',
          isSensitive: true,
          requiresRestart: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '8',
          category: 'SYSTEM',
          key: 'cacheTimeout',
          value: 3600,
          dataType: 'number',
          description: 'Cache timeout süresi (saniye)',
          isSensitive: false,
          requiresRestart: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      let filteredSettings = mockSettings;

      // Filter by category
      if (category) {
        filteredSettings = filteredSettings.filter(s => s.category === category);
      }

      // Filter sensitive settings
      if (!includeSensitive) {
        filteredSettings = filteredSettings.filter(s => !s.isSensitive);
      }

      // Remove description if not requested
      if (!includeDescription) {
        filteredSettings = filteredSettings.map(s => ({
          ...s,
          description: undefined
        }));
      }

      // Cache the result
      this.cache.set(cacheKey, filteredSettings);

      return filteredSettings;
    } catch (error) {
      console.error('Error fetching settings:', error);
      throw new Error('Failed to fetch settings');
    }
  }

  /**
   * Update multiple settings
   */
  async updateSettings(
    updates: SettingUpdate[], 
    userId: string, 
    ipAddress?: string, 
    userAgent?: string
  ): Promise<void> {
    try {
      // Validate all updates first
      for (const update of updates) {
        await this.validation.validateSetting(update.category, update.key, update.value);
      }

      // Get current settings for audit
      const currentSettings = await this.getSettings({ includeSensitive: true });
      const settingsMap = new Map(
        currentSettings.map(s => [`${s.category}:${s.key}`, s])
      );

      // Process updates
      for (const update of updates) {
        const settingKey = `${update.category}:${update.key}`;
        const currentSetting = settingsMap.get(settingKey);

        if (currentSetting) {
          // Log the change
          await this.auditService.logChange({
            settingId: currentSetting.id,
            category: update.category,
            key: update.key,
            oldValue: currentSetting.value,
            newValue: update.value,
            changedBy: userId,
            changeReason: update.changeReason,
            ipAddress,
            userAgent
          });

          // Update the setting (mock implementation)
          console.log(`Updating ${settingKey}: ${currentSetting.value} -> ${update.value}`);
        }
      }

      // Clear cache
      this.clearCache();

    } catch (error) {
      console.error('Error updating settings:', error);
      throw new Error('Failed to update settings');
    }
  }

  /**
   * Get setting by category and key
   */
  async getSetting(category: string, key: string): Promise<SettingValue | null> {
    const settings = await this.getSettings({ category: category as SettingsCategory });
    const setting = settings.find(s => s.key === key);
    return setting ? setting.value : null;
  }

  /**
   * Reset category to defaults
   */
  async resetToDefaults(category: string, userId: string): Promise<void> {
    try {
      // This would reset all settings in a category to their default values
      console.log(`Resetting category ${category} to defaults by user ${userId}`);
      
      // Clear cache for this category
      this.clearCacheByCategory(category);
    } catch (error) {
      console.error('Error resetting to defaults:', error);
      throw new Error('Failed to reset to defaults');
    }
  }

  /**
   * Export settings
   */
  async exportSettings(categories?: string[]): Promise<any> {
    try {
      const settings = await this.getSettings({ includeSensitive: false });
      
      let filteredSettings = settings;
      if (categories && categories.length > 0) {
        filteredSettings = settings.filter(s => categories.includes(s.category));
      }

      return {
        exportDate: new Date().toISOString(),
        version: '1.0',
        settings: filteredSettings.reduce((acc, setting) => {
          if (!acc[setting.category]) {
            acc[setting.category] = {};
          }
          acc[setting.category][setting.key] = setting.value;
          return acc;
        }, {} as Record<string, Record<string, any>>)
      };
    } catch (error) {
      console.error('Error exporting settings:', error);
      throw new Error('Failed to export settings');
    }
  }

  /**
   * Import settings
   */
  async importSettings(
    settingsData: any, 
    userId: string, 
    overwrite: boolean = false
  ): Promise<void> {
    try {
      // Validate import data structure
      if (!settingsData.settings || typeof settingsData.settings !== 'object') {
        throw new Error('Invalid settings data format');
      }

      const updates: SettingUpdate[] = [];

      // Convert imported data to updates
      for (const [category, categorySettings] of Object.entries(settingsData.settings)) {
        if (typeof categorySettings === 'object') {
          for (const [key, value] of Object.entries(categorySettings as Record<string, any>)) {
            updates.push({
              category: category as SettingsCategory,
              key,
              value,
              changeReason: 'Settings import'
            });
          }
        }
      }

      // Apply updates
      await this.updateSettings(updates, userId);

    } catch (error) {
      console.error('Error importing settings:', error);
      throw new Error('Failed to import settings');
    }
  }

  /**
   * Clear cache
   */
  private clearCache(): void {
    this.cache.clear();
  }

  /**
   * Clear cache by category
   */
  private clearCacheByCategory(category: string): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => 
      key.includes(`settings:${category}:`) || key === 'settings:all'
    );
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Get settings schema for validation
   */
  async getSettingsSchema(): Promise<any> {
    return this.validation.getSchema();
  }
}
