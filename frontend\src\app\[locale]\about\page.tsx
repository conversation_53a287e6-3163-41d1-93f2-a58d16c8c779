"use client"

import type { Metada<PERSON> } from 'next';
import { Navigation } from "@/components/ui/navigation";
import { Button } from "@/components/ui/button";
import { useSimpleTranslation } from "@/hooks/useSimpleTranslation";

const stats = [
  { number: "500+", label: "Aktif Üretici" },
  { number: "10,000+", label: "Ürün Çeşidi" },
  { number: "50+", label: "Ülkeye İhracat" },
  { number: "1M+", label: "Mutlu Müşteri" }
];

const values = [
  {
    title: "Güvenilirlik",
    description: "Tüm işlemlerimizde şeffaflık ve güven önceliğimizdir.",
    icon: "🛡️"
  },
  {
    title: "Kali<PERSON>",
    description: "Yalnızca en kaliteli doğal taş ürünlerini platformumuzda bulundururuz.",
    icon: "⭐"
  },
  {
    title: "İnovasyon",
    description: "3D teknolojisi ve yapay zeka ile sektöre yenilik getiriyoruz.",
    icon: "🚀"
  },
  {
    title: "Sürdürülebilirlik",
    description: "Çevre dostu üretim ve sürdürülebilir madencilik uygulamalarını destekliyoruz.",
    icon: "🌱"
  }
];

const team = [
  {
    name: "Ahmet Yılmaz",
    role: "Kurucu & CEO",
    description: "20 yıllık doğal taş sektörü deneyimi",
    image: "/api/placeholder/150/150"
  },
  {
    name: "Fatma Demir",
    role: "CTO",
    description: "Teknoloji ve inovasyon lideri",
    image: "/api/placeholder/150/150"
  },
  {
    name: "Mehmet Kaya",
    role: "İş Geliştirme Direktörü",
    description: "Uluslararası pazar uzmanı",
    image: "/api/placeholder/150/150"
  }
];

export default function AboutPage() {
  const { t } = useSimpleTranslation();

  // Create navigation links with translations
  const navigationLinks = [
    { name: t('nav.home'), href: "/" },
    { name: t('nav.products'), href: "/products" },
    { name: t('nav.3d_showroom'), href: "/3d-showroom" },
    { name: t('nav.news'), href: "/news" },
    { name: t('nav.about'), href: "/about", active: true },
    { name: t('nav.contact'), href: "/contact" }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <Navigation
        brand={{
          name: "Türkiye Doğal Taş Pazarı",
          href: "/"
        }}
        links={navigationLinks}
      />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-stone-50 to-stone-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              {t('about.hero.title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              {t('about.hero.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-stone-600 hover:bg-stone-700">
                {t('about.hero.cta_primary')}
              </Button>
              <Button variant="outline" size="lg">
                {t('about.hero.cta_secondary')}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl font-bold text-stone-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-stone-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                {t('about.mission.title')}
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                {t('about.mission.description')}
              </p>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                {t('about.vision.title')}
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                {t('about.vision.description')}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {t('about.values.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('about.values.subtitle')}
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {value.title}
                </h3>
                <p className="text-gray-600">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-20 bg-stone-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {t('about.team.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('about.team.subtitle')}
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <div key={index} className="text-center bg-white p-8 rounded-lg shadow-sm">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {member.name}
                </h3>
                <p className="text-stone-600 font-medium mb-3">
                  {member.role}
                </p>
                <p className="text-gray-600">
                  {member.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-stone-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            {t('about.cta.title')}
          </h2>
          <p className="text-xl text-stone-100 mb-8 max-w-2xl mx-auto">
            {t('about.cta.subtitle')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="outline" className="bg-white text-stone-600 hover:bg-stone-50">
              {t('about.cta.button_primary')}
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-stone-600">
              {t('about.cta.button_secondary')}
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
