'use client';

import React, { useState, useCallback } from 'react';
import { 
  Home, 
  Plus, 
  Trash2, 
  Eye, 
  EyeOff, 
  RotateCw, 
  Move, 
  Layers,
  Search,
  Filter,
  Grid3X3,
  Palette,
  Settings,
  Sparkles
} from 'lucide-react';
import { ProductPlacement, ShowroomConfiguration } from '../core/ShowroomEngine';
import { PATTERN_CONFIGS, TilingPattern } from '../utils/patterns';
import { GROUT_PRESETS, GroutSettings } from '../utils/grout';
import DimensionSelector, { CustomDimensions } from './DimensionSelector';

interface ControlPanelProps {
  products: ProductPlacement[];
  configuration: ShowroomConfiguration;
  selectedProduct: string | null;
  availableProducts: Product[];
  onProductAdd: (productId: string, dimensions?: CustomDimensions) => void;
  onProductRemove: (productId: string) => void;
  onProductUpdate: (productId: string, updates: Partial<ProductPlacement>) => void;
  onConfigurationChange: (updates: Partial<ShowroomConfiguration>) => void;
  onAIRecommendation: () => void;
  className?: string;
}

interface Product {
  id: string;
  name: string;
  category: string;
  image: string;
  dimensions: { width: number; height: number; thickness: number };
  available: boolean;
}

export const ControlPanel: React.FC<ControlPanelProps> = ({
  products,
  configuration,
  selectedProduct,
  availableProducts,
  onProductAdd,
  onProductRemove,
  onProductUpdate,
  onConfigurationChange,
  onAIRecommendation,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'room' | 'products' | 'layers' | 'ai'>('room');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showDimensionSelector, setShowDimensionSelector] = useState<string | null>(null);
  const [pendingDimensions, setPendingDimensions] = useState<CustomDimensions | null>(null);

  // Filter products based on search and category
  const filteredProducts = availableProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    return matchesSearch && matchesCategory && product.available;
  });

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(availableProducts.map(p => p.category)))];

  // Room templates
  const roomTemplates = [
    { id: 'living', name: 'Salon', icon: Home },
    { id: 'kitchen', name: 'Mutfak', icon: Grid3X3 },
    { id: 'bathroom', name: 'Banyo', icon: Layers },
    { id: 'bedroom', name: 'Yatak Odası', icon: Home },
    { id: 'outdoor', name: 'Dış Mekan', icon: Home }
  ];

  // Handle product addition with dimension selection
  const handleProductAdd = useCallback((productId: string) => {
    setShowDimensionSelector(productId);
  }, []);

  // Handle dimension confirmation
  const handleDimensionConfirm = useCallback(() => {
    if (showDimensionSelector && pendingDimensions) {
      onProductAdd(showDimensionSelector, pendingDimensions);
      setShowDimensionSelector(null);
      setPendingDimensions(null);
    }
  }, [showDimensionSelector, pendingDimensions, onProductAdd]);

  // Handle dimension cancel
  const handleDimensionCancel = useCallback(() => {
    setShowDimensionSelector(null);
    setPendingDimensions(null);
  }, []);

  // Handle product property update
  const handleProductPropertyUpdate = useCallback((property: string, value: any) => {
    if (selectedProduct) {
      onProductUpdate(selectedProduct, { [property]: value });
    }
  }, [selectedProduct, onProductUpdate]);

  const selectedProductData = products.find(p => p.id === selectedProduct);

  return (
    <div className={`bg-white border-r border-gray-200 flex flex-col ${className}`}>
      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => setActiveTab('room')}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === 'room' 
              ? 'text-amber-600 border-b-2 border-amber-600 bg-amber-50' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Home size={16} className="mx-auto mb-1" />
          Mekan
        </button>
        
        <button
          onClick={() => setActiveTab('products')}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === 'products' 
              ? 'text-amber-600 border-b-2 border-amber-600 bg-amber-50' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Grid3X3 size={16} className="mx-auto mb-1" />
          Ürünler
        </button>
        
        <button
          onClick={() => setActiveTab('layers')}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === 'layers' 
              ? 'text-amber-600 border-b-2 border-amber-600 bg-amber-50' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Layers size={16} className="mx-auto mb-1" />
          Katmanlar
        </button>
        
        <button
          onClick={() => setActiveTab('ai')}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === 'ai' 
              ? 'text-amber-600 border-b-2 border-amber-600 bg-amber-50' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Sparkles size={16} className="mx-auto mb-1" />
          AI Öneri
        </button>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Room Tab */}
        {activeTab === 'room' && (
          <div className="p-4 space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Mekan Şablonu</h3>
              <div className="grid grid-cols-2 gap-2">
                {roomTemplates.map((room) => {
                  const Icon = room.icon;
                  return (
                    <button
                      key={room.id}
                      onClick={() => onConfigurationChange({ room: room.id as any })}
                      className={`p-3 rounded-lg border text-sm transition-colors ${
                        configuration.room === room.id
                          ? 'border-amber-500 bg-amber-50 text-amber-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <Icon size={20} className="mx-auto mb-1" />
                      {room.name}
                    </button>
                  );
                })}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Görünüm Ayarları</h3>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={configuration.showGrid}
                    onChange={(e) => onConfigurationChange({ showGrid: e.target.checked })}
                    className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Grid Göster</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={configuration.showShadows}
                    onChange={(e) => onConfigurationChange({ showShadows: e.target.checked })}
                    className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Gölgeler</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={configuration.showEnvironment}
                    onChange={(e) => onConfigurationChange({ showEnvironment: e.target.checked })}
                    className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Ortam</span>
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Products Tab */}
        {activeTab === 'products' && (
          <div className="p-4 space-y-4">
            {/* Search and Filter */}
            <div className="space-y-3">
              <div className="relative">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Ürün ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                />
              </div>
              
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'Tüm Kategoriler' : category}
                  </option>
                ))}
              </select>
            </div>

            {/* Product List */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-900">Mevcut Ürünler</h3>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {filteredProducts.map((product) => (
                  <div
                    key={product.id}
                    className="flex items-center p-3 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
                  >
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-12 h-12 object-cover rounded-lg mr-3"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{product.name}</p>
                      <p className="text-xs text-gray-500">{product.category}</p>
                      <p className="text-xs text-gray-400">
                        {product.dimensions.width}x{product.dimensions.height} cm
                      </p>
                    </div>
                    <button
                      onClick={() => handleProductAdd(product.id)}
                      className="p-2 text-amber-600 hover:bg-amber-50 rounded-lg transition-colors"
                      title="Sahneye Ekle"
                    >
                      <Plus size={16} />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Layers Tab */}
        {activeTab === 'layers' && (
          <div className="p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">Aktif Ürünler</h3>
              <span className="text-xs text-gray-500">{products.length} ürün</span>
            </div>
            
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {products.map((product, index) => (
                <div
                  key={product.id}
                  className={`p-3 border rounded-lg transition-colors ${
                    selectedProduct === product.id
                      ? 'border-amber-500 bg-amber-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-900">
                      {product.productId}
                    </span>
                    <div className="flex items-center gap-1">
                      <button
                        onClick={() => handleProductPropertyUpdate('visible', !product.visible)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title={product.visible ? 'Gizle' : 'Göster'}
                      >
                        {product.visible ? <Eye size={14} /> : <EyeOff size={14} />}
                      </button>
                      <button
                        onClick={() => onProductRemove(product.id)}
                        className="p-1 text-red-400 hover:text-red-600"
                        title="Kaldır"
                      >
                        <Trash2 size={14} />
                      </button>
                    </div>
                  </div>
                  
                  {selectedProduct === product.id && selectedProductData && (
                    <div className="space-y-2 pt-2 border-t border-gray-200">
                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Opaklık</label>
                        <input
                          type="range"
                          min="0.1"
                          max="1"
                          step="0.1"
                          value={selectedProductData.opacity}
                          onChange={(e) => handleProductPropertyUpdate('opacity', parseFloat(e.target.value))}
                          className="w-full"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Döşeme Pattern</label>
                        <select
                          value={selectedProductData.pattern}
                          onChange={(e) => handleProductPropertyUpdate('pattern', e.target.value as TilingPattern)}
                          className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                        >
                          {Object.entries(PATTERN_CONFIGS).map(([key, config]) => (
                            <option key={key} value={key}>
                              {config.displayName}
                            </option>
                          ))}
                        </select>
                        <div className="text-xs text-gray-500 mt-1">
                          {PATTERN_CONFIGS[selectedProductData.pattern as TilingPattern]?.description}
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Derz Ayarları</label>

                        <div className="space-y-2">
                          <div>
                            <label className="block text-xs text-gray-500 mb-1">Derz Genişliği (mm)</label>
                            <input
                              type="range"
                              min="0"
                              max="10"
                              step="0.5"
                              value={selectedProductData.groutSettings.width}
                              onChange={(e) => handleProductPropertyUpdate('groutSettings', {
                                ...selectedProductData.groutSettings,
                                width: parseFloat(e.target.value)
                              })}
                              className="w-full"
                            />
                            <div className="text-xs text-gray-400 text-center">
                              {selectedProductData.groutSettings.width}mm
                            </div>
                          </div>

                          <div>
                            <label className="block text-xs text-gray-500 mb-1">Derz Rengi</label>
                            <input
                              type="color"
                              value={selectedProductData.groutSettings.color}
                              onChange={(e) => handleProductPropertyUpdate('groutSettings', {
                                ...selectedProductData.groutSettings,
                                color: e.target.value
                              })}
                              className="w-full h-6 rounded border border-gray-300"
                            />
                          </div>

                          <div>
                            <label className="block text-xs text-gray-500 mb-1">Derz Stili</label>
                            <select
                              value={selectedProductData.groutSettings.style}
                              onChange={(e) => handleProductPropertyUpdate('groutSettings', {
                                ...selectedProductData.groutSettings,
                                style: e.target.value as 'flat' | 'recessed' | 'raised'
                              })}
                              className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                            >
                              <option value="flat">Düz</option>
                              <option value="recessed">Çukur</option>
                              <option value="raised">Yüksek</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-xs text-gray-500 mb-1">Hazır Ayarlar</label>
                            <select
                              onChange={(e) => {
                                if (e.target.value && GROUT_PRESETS[e.target.value as keyof typeof GROUT_PRESETS]) {
                                  handleProductPropertyUpdate('groutSettings',
                                    GROUT_PRESETS[e.target.value as keyof typeof GROUT_PRESETS]
                                  );
                                }
                              }}
                              className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                              defaultValue=""
                            >
                              <option value="">Hazır ayar seçin...</option>
                              <option value="minimal">Minimal</option>
                              <option value="standard">Standart</option>
                              <option value="bold">Kalın</option>
                              <option value="contrast">Kontrastlı</option>
                              <option value="invisible">Görünmez</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
              
              {products.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Layers size={32} className="mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Henüz ürün eklenmemiş</p>
                  <p className="text-xs">Ürünler sekmesinden ürün ekleyin</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* AI Tab */}
        {activeTab === 'ai' && (
          <div className="p-4 space-y-4">
            <div className="text-center">
              <Sparkles size={32} className="mx-auto mb-3 text-amber-500" />
              <h3 className="text-sm font-medium text-gray-900 mb-2">AI Tasarım Önerileri</h3>
              <p className="text-xs text-gray-600 mb-4">
                Yapay zeka ile size özel tasarım önerileri alın
              </p>
              
              <button
                onClick={onAIRecommendation}
                className="w-full px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors text-sm font-medium"
              >
                Öneri Al
              </button>
            </div>
            
            <div className="border-t border-gray-200 pt-4">
              <h4 className="text-xs font-medium text-gray-900 mb-2">Popüler Kombinasyonlar</h4>
              <div className="space-y-2">
                <div className="p-2 border border-gray-200 rounded-lg text-xs">
                  <div className="font-medium">Modern Salon</div>
                  <div className="text-gray-600">Beyaz mermer + Gri granit</div>
                </div>
                <div className="p-2 border border-gray-200 rounded-lg text-xs">
                  <div className="font-medium">Klasik Banyo</div>
                  <div className="text-gray-600">Traverten + Oniks detay</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Dimension Selector Modal */}
      {showDimensionSelector && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="max-w-md w-full mx-4">
            <DimensionSelector
              productId={showDimensionSelector}
              productName={availableProducts.find(p => p.id === showDimensionSelector)?.name || 'Ürün'}
              currentDimensions={pendingDimensions || {
                width: availableProducts.find(p => p.id === showDimensionSelector)?.dimensions.width || 60,
                height: availableProducts.find(p => p.id === showDimensionSelector)?.dimensions.height || 60,
                thickness: availableProducts.find(p => p.id === showDimensionSelector)?.dimensions.thickness || 2,
                unit: 'cm'
              }}
              onDimensionsChange={setPendingDimensions}
              onConfirm={handleDimensionConfirm}
              onCancel={handleDimensionCancel}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ControlPanel;
