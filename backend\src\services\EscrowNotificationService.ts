import { PrismaClient, NotificationType } from '@prisma/client';
import { EscrowService } from './EscrowService';
import { EmailService } from './EmailService';
import { NotificationService } from './NotificationService';
import { WhatsAppService } from './WhatsAppService';
import { createEmailConfig } from '../config/email';

const prisma = new PrismaClient();

export interface EscrowNotificationData {
  escrowId: string;
  orderId: string;
  orderNumber: string;
  customerId: string;
  producerId: string;
  amount?: number;
  referenceCode?: string;
}

export class EscrowNotificationService {
  private escrowService: EscrowService;
  private emailService: EmailService;
  private notificationService: NotificationService;
  private whatsappService: WhatsAppService;

  constructor(escrowService: EscrowService, notificationService: NotificationService) {
    this.escrowService = escrowService;
    this.notificationService = notificationService;
    const emailConfig = createEmailConfig();
    this.emailService = new EmailService(emailConfig);
    this.whatsappService = new WhatsAppService();
    this.setupEventListeners();
  }

  /**
   * Setup event listeners for escrow events
   */
  private setupEventListeners(): void {
    this.escrowService.on('escrowCreated', this.handleEscrowCreated.bind(this));
    this.escrowService.on('customerPaymentConfirmed', this.handleCustomerPaymentConfirmed.bind(this));
    this.escrowService.on('goodsReady', this.handleGoodsReady.bind(this));
    this.escrowService.on('customerApproved', this.handleCustomerApproved.bind(this));
    this.escrowService.on('producerPaid', this.handleProducerPaid.bind(this));
    this.escrowService.on('disputeResolved', this.handleDisputeResolved.bind(this));
  }

  /**
   * Handle escrow account creation
   */
  private async handleEscrowCreated(data: EscrowNotificationData): Promise<void> {
    const { customerId, producerId, orderId, amount, referenceCode } = data;

    // Get user details for email
    const customer = await prisma.user.findUnique({
      where: { id: customerId },
      include: { profile: true },
    });

    const producer = await prisma.user.findUnique({
      where: { id: producerId },
      include: { profile: true },
    });

    const order = await prisma.order.findUnique({
      where: { id: orderId },
    });

    if (customer && order) {
      // Send payment instructions email
      await this.emailService.sendPaymentInstructions(customer.email, {
        customerName: customer.profile?.companyName || customer.companyName || 'Değerli Müşteri',
        producerName: producer?.profile?.companyName || producer?.companyName || 'Üretici',
        orderNumber: order.orderNumber,
        amount: amount || 0,
        currency: 'TRY',
        referenceCode,
        bankInfo: {
          bankName: process.env.PLATFORM_BANK_NAME || 'Türkiye İş Bankası',
          iban: process.env.PLATFORM_IBAN || '**************************',
          accountHolder: process.env.PLATFORM_ACCOUNT_HOLDER || 'Doğal Taş Pazaryeri Ltd. Şti.',
        },
      });

      // Send WhatsApp payment instructions if customer has phone
      if (customer.profile?.phone) {
        try {
          await this.whatsappService.sendPaymentInstructions(customer.profile.phone, {
            customerName: customer.profile?.companyName || customer.companyName || 'Değerli Müşteri',
            orderNumber: order.orderNumber,
            amount: amount || 0,
            currency: 'TRY',
            referenceCode,
            bankInfo: {
              bankName: process.env.PLATFORM_BANK_NAME || 'Türkiye İş Bankası',
              iban: process.env.PLATFORM_IBAN || '**************************',
              accountHolder: process.env.PLATFORM_ACCOUNT_HOLDER || 'Doğal Taş Pazaryeri Ltd. Şti.',
            },
          });
        } catch (error) {
          console.error('Failed to send WhatsApp payment instructions:', error);
        }
      }
    }

    // Send real-time notification to customer
    await this.notificationService.sendNotification({
      userId: customerId,
      title: 'Ödeme Talimatları',
      message: `Siparişiniz için ödeme yapmanız gerekmektedir. Referans kodu: ${referenceCode}`,
      type: NotificationType.PAYMENT_RECEIVED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      sendEmail: true,
      priority: 'high',
      data: {
        escrowId: data.escrowId,
        amount,
        referenceCode,
        paymentInstructions: true,
      },
    });

    // Send real-time notification to producer
    await this.notificationService.sendNotification({
      userId: producerId,
      title: 'Yeni Sipariş Alındı',
      message: 'Yeni bir sipariş aldınız. Müşteri ödemesi onaylandıktan sonra üretime başlayabilirsiniz.',
      type: NotificationType.ORDER_CONFIRMED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      sendEmail: true,
      priority: 'medium',
      data: {
        escrowId: data.escrowId,
        amount,
      },
    });
  }

  /**
   * Handle customer payment confirmation
   */
  private async handleCustomerPaymentConfirmed(data: EscrowNotificationData): Promise<void> {
    const { customerId, producerId, orderId } = data;

    // Notify customer that payment is confirmed
    await this.createNotification({
      userId: customerId,
      title: 'Ödeme Onaylandı',
      message: 'Ödemeniz onaylandı ve emanette tutuluyor. Üretici ürününüzü hazırlamaya başlayacak.',
      notificationType: NotificationType.PAYMENT_RECEIVED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      data: {
        escrowId: data.escrowId,
        paymentConfirmed: true,
      },
    });

    // Notify producer to start production
    await this.createNotification({
      userId: producerId,
      title: 'Ödeme Onaylandı - Üretime Başlayın',
      message: 'Müşteri ödemesi onaylandı. Artık üretime başlayabilir ve hazır olduğunda bildirebilirsiniz.',
      notificationType: NotificationType.ORDER_CONFIRMED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      data: {
        escrowId: data.escrowId,
        startProduction: true,
      },
    });
  }

  /**
   * Handle goods ready notification
   */
  private async handleGoodsReady(data: EscrowNotificationData): Promise<void> {
    const { customerId, producerId, orderId } = data;

    // Notify customer for approval
    await this.createNotification({
      userId: customerId,
      title: 'Ürün Hazır - Onayınız Bekleniyor',
      message: 'Üretici ürününüzün hazır olduğunu bildirdi. Lütfen onaylayın ve ödeme talimatı verin.',
      notificationType: NotificationType.ORDER_SHIPPED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      data: {
        escrowId: data.escrowId,
        approvalRequired: true,
      },
    });

    // Notify producer that customer is notified
    await this.createNotification({
      userId: producerId,
      title: 'Müşteri Bilgilendirildi',
      message: 'Müşteri ürünün hazır olduğu konusunda bilgilendirildi. Onayını bekliyoruz.',
      notificationType: NotificationType.ORDER_SHIPPED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      data: {
        escrowId: data.escrowId,
        waitingApproval: true,
      },
    });
  }

  /**
   * Handle customer approval
   */
  private async handleCustomerApproved(data: EscrowNotificationData): Promise<void> {
    const { customerId, producerId, orderId } = data;

    // Notify customer that payment will be made
    await this.createNotification({
      userId: customerId,
      title: 'Onay Alındı',
      message: 'Onayınız alındı. Üreticiye ödeme yapılacak ve sipariş tamamlanacak.',
      notificationType: NotificationType.ORDER_DELIVERED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      data: {
        escrowId: data.escrowId,
        approved: true,
      },
    });

    // Notify producer about approval
    await this.createNotification({
      userId: producerId,
      title: 'Müşteri Onayı Alındı',
      message: 'Müşteri ürünü onayladı. Ödemeniz yakında hesabınıza yatırılacak.',
      notificationType: NotificationType.ORDER_DELIVERED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      data: {
        escrowId: data.escrowId,
        approved: true,
      },
    });
  }

  /**
   * Handle producer payment
   */
  private async handleProducerPaid(data: any): Promise<void> {
    const { producerId, customerId, orderId, amount } = data;

    // Notify producer about payment
    await this.createNotification({
      userId: producerId,
      title: 'Ödeme Yapıldı',
      message: `Ödemeniz (${amount} TL) hesabınıza yatırıldı. Sipariş tamamlandı.`,
      notificationType: NotificationType.PAYMENT_RECEIVED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      data: {
        escrowId: data.escrowId,
        amount,
        paid: true,
      },
    });

    // Notify customer about completion
    await this.createNotification({
      userId: customerId,
      title: 'Sipariş Tamamlandı',
      message: 'Siparişiniz başarıyla tamamlandı. Üreticiye ödeme yapıldı.',
      notificationType: NotificationType.ORDER_DELIVERED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      data: {
        escrowId: data.escrowId,
        completed: true,
      },
    });
  }

  /**
   * Handle dispute resolution
   */
  private async handleDisputeResolved(data: any): Promise<void> {
    const { customerId, producerId, orderId, reason } = data;

    // Notify customer about refund
    await this.createNotification({
      userId: customerId,
      title: 'Anlaşmazlık Çözüldü - İade',
      message: `Anlaşmazlık çözüldü ve ödemeniz iade edilecek. Sebep: ${reason}`,
      notificationType: NotificationType.PAYMENT_RECEIVED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      data: {
        escrowId: data.escrowId,
        refunded: true,
        reason,
      },
    });

    // Notify producer about dispute resolution
    await this.createNotification({
      userId: producerId,
      title: 'Anlaşmazlık Çözüldü',
      message: `Sipariş anlaşmazlığı çözüldü. Müşteriye iade yapıldı. Sebep: ${reason}`,
      notificationType: NotificationType.ORDER_DELIVERED,
      relatedEntityType: 'order',
      relatedEntityId: orderId,
      data: {
        escrowId: data.escrowId,
        disputeResolved: true,
        reason,
      },
    });
  }

  /**
   * Create notification in database
   */
  private async createNotification(notificationData: {
    userId: string;
    title: string;
    message: string;
    notificationType: NotificationType;
    relatedEntityType?: string;
    relatedEntityId?: string;
    data?: any;
  }): Promise<void> {
    try {
      await prisma.notification.create({
        data: notificationData,
      });
    } catch (error) {
      console.error('Failed to create notification:', error);
    }
  }

  /**
   * Send payment instructions to customer
   */
  async sendPaymentInstructions(escrowId: string): Promise<void> {
    const escrowAccount = await prisma.escrowAccount.findUnique({
      where: { id: escrowId },
      include: {
        order: true,
        customer: true,
      },
    });

    if (!escrowAccount) {
      throw new Error('Escrow account not found');
    }

    const bankTransferInfo = this.escrowService.getBankTransferInfo(escrowAccount);

    await this.createNotification({
      userId: escrowAccount.customerId,
      title: 'Ödeme Talimatları',
      message: `Sipariş ${escrowAccount.order.orderNumber} için ödeme yapmanız gerekmektedir.`,
      notificationType: NotificationType.PAYMENT_RECEIVED,
      relatedEntityType: 'order',
      relatedEntityId: escrowAccount.orderId,
      data: {
        escrowId,
        bankTransferInfo,
        paymentInstructions: true,
      },
    });
  }

  /**
   * Send reminder notifications
   */
  async sendPaymentReminder(escrowId: string): Promise<void> {
    const escrowAccount = await prisma.escrowAccount.findUnique({
      where: { id: escrowId },
      include: { order: true },
    });

    if (!escrowAccount || escrowAccount.status !== 'PENDING') {
      return;
    }

    await this.createNotification({
      userId: escrowAccount.customerId,
      title: 'Ödeme Hatırlatması',
      message: `Sipariş ${escrowAccount.order.orderNumber} için ödemeniz bekleniyor. Referans: ${escrowAccount.referenceCode}`,
      notificationType: NotificationType.PAYMENT_RECEIVED,
      relatedEntityType: 'order',
      relatedEntityId: escrowAccount.orderId,
      data: {
        escrowId,
        reminder: true,
      },
    });
  }
}
