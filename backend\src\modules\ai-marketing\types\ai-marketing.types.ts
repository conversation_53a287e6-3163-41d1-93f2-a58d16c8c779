// RFC-013: AI Marketing Type Definitions

export interface AIModel {
  name: string;
  version: string;
  isHealthy(): boolean;
  execute(task: MarketingTask): Promise<TaskResult>;
  applyResult(result: TaskResult): Promise<void>;
  getStats(): Promise<any>;
  cleanup(): Promise<void>;
}

export interface MarketingTask {
  id: string;
  type: TaskType;
  priority: TaskPriority;
  aiModel: string;
  data: any;
  requiresApproval: boolean;
  createdAt: Date;
  scheduledFor?: Date;
  retryCount?: number;
  maxRetries?: number;
}

export interface TaskResult {
  taskId: string;
  success: boolean;
  data: any;
  error?: string;
  executionTime: number;
  aiModel: string;
  timestamp: Date;
  requiresApproval?: boolean;
  approvalData?: any;
}

export type TaskType =
  | 'email_campaign'
  | 'social_content'
  | 'customer_acquisition'
  | 'ads_optimization'
  | 'analytics_report'
  | 'content_generation'
  | 'lead_scoring'
  | 'campaign_optimization'
  | 'learning'
  | 'research'
  | 'strategy'
  | 'optimization'
  | 'knowledge';

export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface AISystemStatus {
  orchestrator: SystemHealth;
  emailAI: SystemHealth;
  socialAI: SystemHealth;
  customerAI: SystemHealth;
  adsAI: SystemHealth;
  lastUpdate: Date;
  totalTasks: number;
  completedTasks: number;
  errorCount: number;
}

export type SystemHealth = 'active' | 'inactive' | 'error' | 'maintenance';

// Email Marketing Types
export interface CountryEmailList {
  id: string;
  countryCode: string;
  countryName: string;
  emails: CustomerEmail[];
  segments: EmailSegment[];
  lastUpdated: Date;
  totalSubscribers: number;
  activeSubscribers: number;
  unsubscribeRate: number;
  bounceRate: number;
}

export interface CustomerEmail {
  email: string;
  customerId: string;
  firstName: string;
  lastName: string;
  company: string;
  industry: string;
  country: string;
  addedDate: Date;
  isActive: boolean;
  preferences: EmailPreferences;
  engagementScore: number;
  lastOpenDate?: Date;
  lastClickDate?: Date;
}

export interface EmailPreferences {
  frequency: 'daily' | 'weekly' | 'monthly';
  contentTypes: string[];
  language: string;
  timezone: string;
  optedIn: boolean;
  gdprConsent: boolean;
}

export interface EmailSegment {
  id: string;
  name: string;
  criteria: SegmentCriteria;
  subscriberCount: number;
  createdAt: Date;
  lastUsed?: Date;
}

export interface SegmentCriteria {
  industry?: string[];
  companySize?: string[];
  engagementLevel?: string[];
  location?: string[];
  customFields?: Record<string, any>;
}

export interface EmailCampaign {
  id: string;
  name: string;
  subject: string;
  content: string;
  targetSegments: string[];
  targetCountries: string[];
  status: CampaignStatus;
  scheduledFor?: Date;
  sentAt?: Date;
  recipients: number;
  metrics: EmailMetrics;
  aiGenerated: boolean;
  approvalStatus: ApprovalStatus;
  createdAt: Date;
  updatedAt: Date;
}

export type CampaignStatus = 'draft' | 'scheduled' | 'sending' | 'sent' | 'paused' | 'cancelled';
export type ApprovalStatus = 'pending' | 'approved' | 'rejected' | 'auto_approved';

export interface EmailMetrics {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  converted: number;
  unsubscribed: number;
  bounced: number;
  openRate: number;
  clickRate: number;
  conversionRate: number;
  unsubscribeRate: number;
  bounceRate: number;
}

// Social Media Types
export interface SocialMediaAccount {
  id: string;
  platform: SocialPlatform;
  accountId: string;
  accountName: string;
  accessToken: string;
  refreshToken?: string;
  isActive: boolean;
  lastSync: Date;
  followers: number;
  engagement: number;
  permissions: string[];
}

export type SocialPlatform = 'facebook' | 'instagram' | 'linkedin' | 'twitter' | 'youtube' | 'tiktok';

export interface SocialMediaPost {
  id: string;
  platform: SocialPlatform;
  content: string;
  mediaUrls: string[];
  hashtags: string[];
  scheduledFor: Date;
  publishedAt?: Date;
  status: PostStatus;
  metrics: PostMetrics;
  aiGenerated: boolean;
  approvalStatus: ApprovalStatus;
  createdAt: Date;
}

export type PostStatus = 'draft' | 'scheduled' | 'published' | 'failed' | 'deleted';

export interface PostMetrics {
  views: number;
  likes: number;
  comments: number;
  shares: number;
  clicks: number;
  engagement: number;
  reach: number;
  impressions: number;
}

export interface ContentStrategy {
  platform: SocialPlatform;
  contentType: ContentType;
  frequency: PostingFrequency;
  targetAudience: AudienceSegment;
  contentThemes: ContentTheme[];
  visualStyle: VisualStyle;
  hashtagStrategy: HashtagStrategy;
}

export type ContentType = 
  | 'product_showcase'
  | 'educational'
  | 'behind_scenes'
  | 'customer_stories'
  | 'industry_news'
  | 'promotional'
  | 'user_generated'
  | 'trending';

export interface PostingFrequency {
  daily: number;
  weekly: number;
  monthly: number;
  optimalTimes: string[];
}

// Customer Acquisition Types
export interface ProspectCustomer {
  id: string;
  companyName: string;
  industry: string;
  location: Location;
  contactInfo: ContactInfo;
  estimatedSize: CompanySize;
  potentialValue: number;
  contactHistory: ContactAttempt[];
  leadScore: number;
  dataSource: DataSource;
  status: ProspectStatus;
  assignedTo?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Location {
  country: string;
  city: string;
  address?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface ContactInfo {
  email?: string;
  phone?: string;
  website?: string;
  linkedin?: string;
  contactPerson?: {
    name: string;
    title: string;
    email?: string;
    phone?: string;
  };
}

export type CompanySize = 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
export type DataSource = 'google_maps' | 'linkedin' | 'trade_directory' | 'web_scraping' | 'manual';
export type ProspectStatus = 'new' | 'contacted' | 'responded' | 'qualified' | 'converted' | 'rejected';

export interface ContactAttempt {
  id: string;
  date: Date;
  method: ContactMethod;
  message: string;
  response?: string;
  status: ContactStatus;
  nextFollowUp?: Date;
  aiGenerated: boolean;
}

export type ContactMethod = 'email' | 'linkedin' | 'phone' | 'website_form';
export type ContactStatus = 'sent' | 'delivered' | 'opened' | 'responded' | 'bounced' | 'failed';

// Ad Management Types
export interface AdCampaign {
  id: string;
  platform: AdPlatform;
  campaignId: string;
  name: string;
  campaignType: CampaignType;
  targetAudience: AudienceDefinition;
  budget: BudgetAllocation;
  creatives: AdCreative[];
  performance: CampaignMetrics;
  optimizationRules: OptimizationRule[];
  status: CampaignStatus;
  startDate: Date;
  endDate?: Date;
  aiManaged: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export type AdPlatform = 'google_ads' | 'facebook_ads' | 'linkedin_ads' | 'twitter_ads';
export type CampaignType = 'search' | 'display' | 'video' | 'shopping' | 'social';

export interface AudienceDefinition {
  demographics: {
    ageRange?: [number, number];
    gender?: string[];
    income?: string[];
    education?: string[];
  };
  interests: string[];
  behaviors: string[];
  locations: string[];
  languages: string[];
  customAudiences?: string[];
  lookalikeSources?: string[];
}

export interface BudgetAllocation {
  totalBudget: number;
  dailyBudget: number;
  currency: string;
  bidStrategy: BidStrategy;
  maxCpc?: number;
  targetCpa?: number;
  targetRoas?: number;
}

export type BidStrategy = 'manual_cpc' | 'auto_cpc' | 'target_cpa' | 'target_roas' | 'maximize_clicks';

export interface AdCreative {
  id: string;
  type: CreativeType;
  headline: string;
  description: string;
  imageUrl?: string;
  videoUrl?: string;
  callToAction: string;
  landingPageUrl: string;
  aiGenerated: boolean;
  performance: CreativeMetrics;
}

export type CreativeType = 'text' | 'image' | 'video' | 'carousel' | 'collection';

export interface CreativeMetrics {
  impressions: number;
  clicks: number;
  conversions: number;
  ctr: number;
  cpc: number;
  cpa: number;
  roas: number;
}

export interface CampaignMetrics {
  impressions: number;
  clicks: number;
  conversions: number;
  spend: number;
  revenue: number;
  ctr: number;
  cpc: number;
  cpa: number;
  roas: number;
  qualityScore?: number;
}

export interface OptimizationRule {
  id: string;
  condition: string;
  action: OptimizationAction;
  threshold: number;
  isActive: boolean;
  lastTriggered?: Date;
}

export type OptimizationAction = 
  | 'increase_bid'
  | 'decrease_bid'
  | 'pause_campaign'
  | 'pause_adgroup'
  | 'pause_keyword'
  | 'change_budget'
  | 'update_audience'
  | 'refresh_creative';

// Analytics Types
export interface AnalyticsReport {
  id: string;
  type: ReportType;
  period: ReportPeriod;
  data: any;
  insights: string[];
  recommendations: string[];
  generatedAt: Date;
  aiGenerated: boolean;
}

export type ReportType = 
  | 'email_performance'
  | 'social_performance'
  | 'ad_performance'
  | 'lead_generation'
  | 'roi_analysis'
  | 'competitive_analysis';

export type ReportPeriod = 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';

// Approval System Types
export interface ApprovalRequest {
  id: string;
  type: ApprovalType;
  content: any;
  requestedBy: string;
  requestedAt: Date;
  status: ApprovalStatus;
  reviewedBy?: string;
  reviewedAt?: Date;
  comments?: string;
  priority: TaskPriority;
}

export type ApprovalType = 
  | 'email_campaign'
  | 'social_post'
  | 'ad_campaign'
  | 'content_piece'
  | 'budget_change'
  | 'audience_update';

// Shared Types
export interface AudienceSegment {
  id: string;
  name: string;
  description: string;
  criteria: any;
  size: number;
}

export interface ContentTheme {
  id: string;
  name: string;
  description: string;
  keywords: string[];
  tone: string;
  style: string;
}

export interface VisualStyle {
  colorPalette: string[];
  fonts: string[];
  logoUsage: string;
  imageStyle: string;
  brandGuidelines: string;
}

export interface HashtagStrategy {
  branded: string[];
  industry: string[];
  trending: string[];
  location: string[];
  maxPerPost: number;
}
