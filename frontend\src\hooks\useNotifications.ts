import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { io, Socket } from 'socket.io-client';
import { toast } from 'react-hot-toast';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  data?: any;
  createdAt: Date;
  relatedEntityId?: string;
  relatedEntityType?: string;
  status: 'UNREAD' | 'READ';
}

export interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  isLoading: boolean;
}

export const useNotifications = () => {
  const { data: session } = useSession();
  const [state, setState] = useState<NotificationState>({
    notifications: [],
    unreadCount: 0,
    isConnected: false,
    isLoading: true
  });

  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize socket connection
  const initializeSocket = useCallback(() => {
    if (!session?.user?.id) return;

    const socket = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000', {
      transports: ['websocket'],
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    socketRef.current = socket;

    // Connection events
    socket.on('connect', () => {
      console.log('Connected to notification server');
      setState(prev => ({ ...prev, isConnected: true }));

      // Authenticate with server
      socket.emit('authenticate', {
        userId: session.user.id,
        userType: session.user.userType,
        token: session.accessToken
      });
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from notification server');
      setState(prev => ({ ...prev, isConnected: false }));
    });

    socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      setState(prev => ({ ...prev, isConnected: false }));
    });

    // Authentication events
    socket.on('authenticated', (data) => {
      console.log('Authenticated successfully:', data);
    });

    socket.on('authentication-error', (error) => {
      console.error('Authentication failed:', error);
      toast.error('Failed to connect to notification service');
    });

    // Notification events
    socket.on('notification', (notification: Notification) => {
      console.log('New notification received:', notification);
      
      setState(prev => ({
        ...prev,
        notifications: [notification, ...prev.notifications],
        unreadCount: prev.unreadCount + 1
      }));

      // Show toast notification based on priority
      const toastOptions = {
        duration: notification.priority === 'URGENT' ? 8000 : 4000,
        position: 'top-right' as const,
      };

      switch (notification.priority) {
        case 'URGENT':
          toast.error(notification.message, toastOptions);
          break;
        case 'HIGH':
          toast.success(notification.message, toastOptions);
          break;
        case 'MEDIUM':
          toast(notification.message, toastOptions);
          break;
        case 'LOW':
          // Don't show toast for low priority
          break;
      }

      // Play notification sound for high priority
      if (notification.priority === 'URGENT' || notification.priority === 'HIGH') {
        playNotificationSound();
      }
    });

    socket.on('unread-count', (data: { count: number }) => {
      setState(prev => ({ ...prev, unreadCount: data.count }));
    });

    // Real-time updates
    socket.on('quote-request', (data) => {
      console.log('Quote request update:', data);
      toast.success('New quote request received!');
    });

    socket.on('quote-response', (data) => {
      console.log('Quote response update:', data);
      toast.success(`New quote received for ${data.productName}!`);
    });

    socket.on('order-update', (data) => {
      console.log('Order update:', data);
      toast.success(`Order status updated to ${data.newStatus}`);
    });

    socket.on('product-approval', (data) => {
      console.log('Product approval update:', data);
      if (data.type === 'APPROVED') {
        toast.success(`Product "${data.productName}" approved!`);
      } else {
        toast.error(`Product "${data.productName}" rejected: ${data.reason}`);
      }
    });

    socket.on('system-announcement', (data) => {
      console.log('System announcement:', data);
      toast(data.message, { duration: 6000 });
    });

    return socket;
  }, [session]);

  // Play notification sound
  const playNotificationSound = useCallback(() => {
    try {
      // Create a simple beep sound using Web Audio API
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = 800; // Frequency in Hz
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.5);
    } catch (error) {
      console.log('Notification sound not available:', error);
    }
  }, []);

  // Fetch notifications from API
  const fetchNotifications = useCallback(async (page = 1, limit = 20) => {
    if (!session?.accessToken) return;

    try {
      setState(prev => ({ ...prev, isLoading: true }));

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/users/notifications?page=${page}&limit=${limit}`,
        {
          headers: {
            'Authorization': `Bearer ${session.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setState(prev => ({
          ...prev,
          notifications: page === 1 ? data.data.notifications : [...prev.notifications, ...data.data.notifications],
          unreadCount: data.data.unreadCount,
          isLoading: false
        }));
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [session]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    if (!session?.accessToken || !socketRef.current) return;

    try {
      // Optimistic update
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => 
          n.id === notificationId ? { ...n, status: 'read' as const } : n
        ),
        unreadCount: Math.max(0, prev.unreadCount - 1)
      }));

      // Send to server via WebSocket
      socketRef.current.emit('mark-notification-read', { notificationId });

      // Also call API as backup
      await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/users/notifications/${notificationId}/read`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${session.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      // Revert optimistic update
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => 
          n.id === notificationId ? { ...n, status: 'UNREAD' as const } : n
        ),
        unreadCount: prev.unreadCount + 1
      }));
    }
  }, [session]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    if (!session?.accessToken || !socketRef.current) return;

    try {
      // Optimistic update
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => ({ ...n, status: 'read' as const })),
        unreadCount: 0
      }));

      // Send to server via WebSocket
      socketRef.current.emit('mark-all-read');

      // Also call API as backup
      await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/users/notifications/mark-all-read`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${session.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      // Revert optimistic update
      fetchNotifications();
    }
  }, [session, fetchNotifications]);

  // Initialize connection and fetch notifications
  useEffect(() => {
    if (session?.user?.id) {
      const socket = initializeSocket();
      fetchNotifications();

      return () => {
        if (socket) {
          socket.disconnect();
        }
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
        }
      };
    }
  }, [session, initializeSocket, fetchNotifications]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  return {
    notifications: state.notifications,
    unreadCount: state.unreadCount,
    isConnected: state.isConnected,
    isLoading: state.isLoading,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    refetch: () => fetchNotifications(1),
  };
};

export default useNotifications;
