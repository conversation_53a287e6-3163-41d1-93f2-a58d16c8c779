// Settings Routes - RFC-012
import { Router, Request, Response } from 'express';
import { SettingsService } from '../core/SettingsService';
import { SettingsAuditService } from '../core/SettingsAudit';
import { 
  GetSettingsRequest,
  UpdateSettingsRequest,
  ResetSettingsRequest,
  ImportSettingsRequest,
  SettingsValidationError 
} from '../types/settings.types';

const router = Router();
const settingsService = new SettingsService();
const auditService = new SettingsAuditService();

// Initialize mock data for testing
auditService.initializeMockData();

// Middleware to check admin permissions
const requireAdmin = (req: Request, res: Response, next: any) => {
  // Mock admin check - in real implementation, check JWT token and role
  const isAdmin = req.headers.authorization === 'Bearer admin-token';
  if (!isAdmin) {
    return res.status(403).json({
      success: false,
      message: 'Admin access required'
    });
  }
  next();
};

/**
 * @swagger
 * /api/admin/settings:
 *   get:
 *     summary: Get admin settings
 *     tags: [Admin Settings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [platform, security, business, notification, system, integration]
 *         description: Filter by category
 *       - in: query
 *         name: includeDescription
 *         schema:
 *           type: boolean
 *         description: Include field descriptions
 *     responses:
 *       200:
 *         description: Settings retrieved successfully
 *       403:
 *         description: Admin access required
 */
router.get('/', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { category, includeDescription } = req.query as GetSettingsRequest;
    
    const settings = await settingsService.getSettings({
      category: category as any,
      includeDescription: typeof includeDescription === 'string' ? includeDescription === 'true' : false,
      includeSensitive: false // Never include sensitive settings in API response
    });

    res.json({
      success: true,
      data: settings
    });

  } catch (error) {
    console.error('Error fetching settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings'
    });
  }
});

/**
 * @swagger
 * /api/admin/settings:
 *   put:
 *     summary: Update admin settings
 *     tags: [Admin Settings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               settings:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     category:
 *                       type: string
 *                     key:
 *                       type: string
 *                     value:
 *                       oneOf:
 *                         - type: string
 *                         - type: number
 *                         - type: boolean
 *                     changeReason:
 *                       type: string
 *     responses:
 *       200:
 *         description: Settings updated successfully
 *       400:
 *         description: Validation error
 *       403:
 *         description: Admin access required
 */
router.put('/', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { settings } = req.body as UpdateSettingsRequest;
    
    if (!settings || !Array.isArray(settings)) {
      return res.status(400).json({
        success: false,
        message: 'Settings array is required'
      });
    }

    // Mock user ID - in real implementation, get from JWT token
    const userId = 'admin_user_1';
    const ipAddress = req.ip;
    const userAgent = req.get('User-Agent');

    await settingsService.updateSettings(settings, userId, ipAddress, userAgent);

    // Check if any settings require restart
    const schema = await settingsService.getSettingsSchema();
    const requiresRestart = settings.some(setting => {
      const categorySchema = schema[setting.category];
      const fieldSchema = categorySchema?.[setting.key];
      return fieldSchema?.requiresRestart;
    });

    res.json({
      success: true,
      message: 'Settings updated successfully',
      requiresRestart
    });

  } catch (error) {
    console.error('Error updating settings:', error);
    
    if (error instanceof SettingsValidationError) {
      return res.status(400).json({
        success: false,
        message: error.message,
        field: error.field,
        value: error.value,
        rule: error.rule
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update settings'
    });
  }
});

/**
 * @swagger
 * /api/admin/settings/schema:
 *   get:
 *     summary: Get settings validation schema
 *     tags: [Admin Settings]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Schema retrieved successfully
 */
router.get('/schema', requireAdmin, async (req: Request, res: Response) => {
  try {
    const schema = await settingsService.getSettingsSchema();
    
    res.json({
      success: true,
      data: schema
    });

  } catch (error) {
    console.error('Error fetching schema:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch schema'
    });
  }
});

/**
 * @swagger
 * /api/admin/settings/reset:
 *   post:
 *     summary: Reset settings category to defaults
 *     tags: [Admin Settings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               category:
 *                 type: string
 *                 enum: [platform, security, business, notification, system, integration]
 *     responses:
 *       200:
 *         description: Settings reset successfully
 */
router.post('/reset', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { category } = req.body as ResetSettingsRequest;
    
    if (!category) {
      return res.status(400).json({
        success: false,
        message: 'Category is required'
      });
    }

    const userId = 'admin_user_1';
    await settingsService.resetToDefaults(category, userId);

    res.json({
      success: true,
      message: `Settings for category '${category}' reset to defaults`
    });

  } catch (error) {
    console.error('Error resetting settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset settings'
    });
  }
});

/**
 * @swagger
 * /api/admin/settings/export:
 *   get:
 *     summary: Export settings
 *     tags: [Admin Settings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: categories
 *         schema:
 *           type: string
 *         description: Comma-separated list of categories to export
 *     responses:
 *       200:
 *         description: Settings exported successfully
 */
router.get('/export', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { categories } = req.query;
    const categoryList = categories ? (categories as string).split(',') : undefined;
    
    const exportData = await settingsService.exportSettings(categoryList);
    const filename = `settings-export-${new Date().toISOString().split('T')[0]}.json`;

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    res.json({
      success: true,
      data: exportData,
      filename
    });

  } catch (error) {
    console.error('Error exporting settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export settings'
    });
  }
});

/**
 * @swagger
 * /api/admin/settings/import:
 *   post:
 *     summary: Import settings
 *     tags: [Admin Settings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               settings:
 *                 type: object
 *               overwrite:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Settings imported successfully
 */
router.post('/import', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { settings, overwrite = false } = req.body as ImportSettingsRequest;
    
    if (!settings) {
      return res.status(400).json({
        success: false,
        message: 'Settings data is required'
      });
    }

    const userId = 'admin_user_1';
    await settingsService.importSettings(settings, userId, overwrite);

    res.json({
      success: true,
      message: 'Settings imported successfully'
    });

  } catch (error) {
    console.error('Error importing settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to import settings'
    });
  }
});

/**
 * @swagger
 * /api/admin/settings/audit:
 *   get:
 *     summary: Get settings audit logs
 *     tags: [Admin Settings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of records to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of records to skip
 *     responses:
 *       200:
 *         description: Audit logs retrieved successfully
 */
router.get('/audit', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { category, limit, offset } = req.query;
    
    const result = await auditService.getAuditLogs({
      category: category as any,
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined
    });

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error fetching audit logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch audit logs'
    });
  }
});

/**
 * @swagger
 * /api/admin/settings/audit/summary:
 *   get:
 *     summary: Get audit summary
 *     tags: [Admin Settings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days to include in summary
 *     responses:
 *       200:
 *         description: Audit summary retrieved successfully
 */
router.get('/audit/summary', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { days } = req.query;
    const daysNumber = days ? parseInt(days as string) : 30;
    
    const summary = await auditService.getAuditSummary(daysNumber);

    res.json({
      success: true,
      data: summary
    });

  } catch (error) {
    console.error('Error fetching audit summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch audit summary'
    });
  }
});

export default router;
