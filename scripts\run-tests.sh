#!/bin/bash

# Automated Testing Script
# Türkiye Doğal Taş Paz<PERSON>eri - Test Runner

set -e

# Configuration
TEST_ENV=${1:-"staging"}
TEST_TYPE=${2:-"all"}  # unit, integration, e2e, all
PARALLEL=${3:-"false"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Create test results directory
mkdir -p test-results

log_info "Starting automated tests for $TEST_ENV environment"
log_info "Test type: $TEST_TYPE"
log_info "Parallel execution: $PARALLEL"

# Function to wait for services
wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    local backend_url="http://localhost:8001"
    local frontend_url="http://localhost:3001"
    
    if [[ "$TEST_ENV" == "production" ]]; then
        backend_url="https://yourdomain.com/api"
        frontend_url="https://yourdomain.com"
    fi
    
    # Wait for backend
    local retries=30
    while [[ $retries -gt 0 ]]; do
        if curl -f "$backend_url/health" > /dev/null 2>&1; then
            log_success "Backend is ready"
            break
        fi
        log_info "Waiting for backend... ($retries retries left)"
        sleep 10
        ((retries--))
    done
    
    if [[ $retries -eq 0 ]]; then
        log_error "Backend is not ready"
        return 1
    fi
    
    # Wait for frontend
    retries=30
    while [[ $retries -gt 0 ]]; do
        if curl -f "$frontend_url" > /dev/null 2>&1; then
            log_success "Frontend is ready"
            break
        fi
        log_info "Waiting for frontend... ($retries retries left)"
        sleep 10
        ((retries--))
    done
    
    if [[ $retries -eq 0 ]]; then
        log_error "Frontend is not ready"
        return 1
    fi
    
    log_success "All services are ready"
}

# Function to run unit tests
run_unit_tests() {
    log_info "Running unit tests..."
    
    local exit_code=0
    
    # Backend unit tests
    log_info "Running backend unit tests..."
    cd backend
    if npm test -- --coverage --coverageReporters=json-summary --coverageReporters=lcov; then
        log_success "Backend unit tests passed"
    else
        log_error "Backend unit tests failed"
        exit_code=1
    fi
    
    # Copy coverage report
    cp coverage/coverage-summary.json ../test-results/backend-coverage.json 2>/dev/null || true
    
    cd ..
    
    # Frontend unit tests
    log_info "Running frontend unit tests..."
    cd frontend
    if npm test -- --coverage --coverageReporters=json-summary --coverageReporters=lcov --watchAll=false; then
        log_success "Frontend unit tests passed"
    else
        log_error "Frontend unit tests failed"
        exit_code=1
    fi
    
    # Copy coverage report
    cp coverage/coverage-summary.json ../test-results/frontend-coverage.json 2>/dev/null || true
    
    cd ..
    
    return $exit_code
}

# Function to run integration tests
run_integration_tests() {
    log_info "Running integration tests..."
    
    # API integration tests
    log_info "Running API integration tests..."
    cd backend
    if npm run test:integration; then
        log_success "API integration tests passed"
    else
        log_error "API integration tests failed"
        return 1
    fi
    cd ..
    
    # Database integration tests
    log_info "Running database integration tests..."
    cd backend
    if npm run test:db; then
        log_success "Database integration tests passed"
    else
        log_error "Database integration tests failed"
        return 1
    fi
    cd ..
    
    log_success "Integration tests completed"
}

# Function to run E2E tests
run_e2e_tests() {
    log_info "Running E2E tests..."
    
    # Install Playwright if not already installed
    if [[ ! -d "node_modules/@playwright" ]]; then
        npm install @playwright/test
        npx playwright install
    fi
    
    # Run Playwright E2E tests
    if npx playwright test --reporter=html --output-dir=test-results/e2e; then
        log_success "E2E tests passed"
    else
        log_error "E2E tests failed"
        return 1
    fi
    
    log_success "E2E tests completed"
}

# Function to run performance tests
run_performance_tests() {
    log_info "Running performance tests..."
    
    local backend_url="http://localhost:8001"
    local frontend_url="http://localhost:3001"
    
    if [[ "$TEST_ENV" == "production" ]]; then
        backend_url="https://yourdomain.com/api"
        frontend_url="https://yourdomain.com"
    fi
    
    # Install k6 if not available
    if ! command -v k6 &> /dev/null; then
        log_warning "k6 not installed, skipping performance tests"
        return 0
    fi
    
    # Run load tests
    log_info "Running load tests..."
    k6 run --out json=test-results/performance.json tests/performance/load-test.js
    
    # Run stress tests
    log_info "Running stress tests..."
    k6 run --out json=test-results/stress.json tests/performance/stress-test.js
    
    log_success "Performance tests completed"
}

# Function to run security tests
run_security_tests() {
    log_info "Running security tests..."
    
    # OWASP ZAP security scan (if available)
    if command -v zap-baseline.py &> /dev/null; then
        local target_url="http://localhost:3001"
        if [[ "$TEST_ENV" == "production" ]]; then
            target_url="https://yourdomain.com"
        fi
        
        log_info "Running OWASP ZAP baseline scan..."
        zap-baseline.py -t "$target_url" -J test-results/zap-report.json || true
    else
        log_warning "OWASP ZAP not available, skipping security scan"
    fi
    
    # NPM audit
    log_info "Running NPM security audit..."
    cd backend
    npm audit --audit-level=moderate --json > ../test-results/backend-audit.json || true
    cd ..
    
    cd frontend
    npm audit --audit-level=moderate --json > ../test-results/frontend-audit.json || true
    cd ..
    
    log_success "Security tests completed"
}

# Function to generate test report
generate_test_report() {
    log_info "Generating test report..."
    
    local report_file="test-results/test-report-$(date +%Y%m%d-%H%M%S).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Test Report - Natural Stone Marketplace</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Report - Natural Stone Marketplace</h1>
        <p><strong>Environment:</strong> $TEST_ENV</p>
        <p><strong>Test Type:</strong> $TEST_TYPE</p>
        <p><strong>Date:</strong> $(date)</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <div class="metric">
            <strong>Total Tests:</strong> <span id="total-tests">-</span>
        </div>
        <div class="metric">
            <strong>Passed:</strong> <span id="passed-tests">-</span>
        </div>
        <div class="metric">
            <strong>Failed:</strong> <span id="failed-tests">-</span>
        </div>
        <div class="metric">
            <strong>Coverage:</strong> <span id="coverage">-</span>
        </div>
    </div>
    
    <div class="section">
        <h2>Test Results</h2>
        <p>Detailed test results are available in the test-results directory.</p>
        <ul>
            <li><a href="backend-coverage.json">Backend Coverage Report</a></li>
            <li><a href="frontend-coverage.json">Frontend Coverage Report</a></li>
            <li><a href="e2e/index.html">E2E Test Report</a></li>
            <li><a href="performance.json">Performance Test Results</a></li>
        </ul>
    </div>
</body>
</html>
EOF
    
    log_success "Test report generated: $report_file"
}

# Main test execution
main() {
    local exit_code=0
    
    # Wait for services to be ready
    wait_for_services || exit 1
    
    # Run tests based on type
    case "$TEST_TYPE" in
        "unit")
            run_unit_tests || exit_code=1
            ;;
        "integration")
            run_integration_tests || exit_code=1
            ;;
        "e2e")
            run_e2e_tests || exit_code=1
            ;;
        "performance")
            run_performance_tests || exit_code=1
            ;;
        "security")
            run_security_tests || exit_code=1
            ;;
        "all")
            run_unit_tests || exit_code=1
            run_integration_tests || exit_code=1
            run_e2e_tests || exit_code=1
            run_performance_tests || exit_code=1
            run_security_tests || exit_code=1
            ;;
        *)
            log_error "Unknown test type: $TEST_TYPE"
            exit 1
            ;;
    esac
    
    # Generate test report
    generate_test_report
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "All tests completed successfully"
    else
        log_error "Some tests failed"
    fi
    
    exit $exit_code
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [environment] [test_type] [parallel]"
        echo ""
        echo "Arguments:"
        echo "  environment    Test environment (staging|production) [default: staging]"
        echo "  test_type      Type of tests to run (unit|integration|e2e|performance|security|all) [default: all]"
        echo "  parallel       Run tests in parallel (true|false) [default: false]"
        echo ""
        echo "Examples:"
        echo "  $0 staging unit"
        echo "  $0 production all true"
        exit 0
        ;;
esac

# Run main function
main "$@"
