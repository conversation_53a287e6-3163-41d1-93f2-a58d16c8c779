'use client';

import React from 'react';
import { ClockIcon } from '@heroicons/react/24/outline';
import { CustomerMultiDeliveryDashboard } from '@/components/ui/customer-multi-delivery-dashboard';
import { mockMultiDeliveryOrder } from '@/data/mock-multi-delivery';
import { Button } from '@/components/ui/button';
import { Package, MessageSquare, CheckCircle, AlertTriangle, Eye } from 'lucide-react';
import toast from 'react-hot-toast';

interface OngoingOrdersPageProps {
  onNavigate?: (route: string) => void;
}

const OngoingOrdersPage: React.FC<OngoingOrdersPageProps> = ({ onNavigate }) => {
  const [showDemo, setShowDemo] = React.useState(true);

  const handleContactSupplier = (orderId: string) => {
    alert(`Üretici ile iletişim kurulacak - Sipariş: ${orderId}`);
  };

  const handleViewPackageDetails = (packageId: string) => {
    alert(`Paket detayları görüntülenecek - Paket: ${packageId}`);
  };

  const handleApproveOrder = async (orderId: string) => {
    try {
      // API call to approve order and release escrow payment
      const response = await fetch(`/api/escrow/${orderId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          approved: true,
          customerNotes: 'Sipariş onaylandı, ödeme serbest bırakılabilir'
        })
      });

      if (response.ok) {
        toast.success('Sipariş onaylandı! Ödeme üreticiye gönderilecek.');
        // Refresh orders list
        window.location.reload();
      } else {
        throw new Error('Approval failed');
      }
    } catch (error) {
      console.error('Error approving order:', error);
      toast.error('Sipariş onaylanırken hata oluştu');
    }
  };

  const handleRejectOrder = async (orderId: string, reason: string) => {
    try {
      const response = await fetch(`/api/escrow/${orderId}/dispute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason,
          disputeType: 'quality_issue'
        })
      });

      if (response.ok) {
        toast.success('Sipariş itirazı kaydedildi. Admin inceleyecek.');
        window.location.reload();
      } else {
        throw new Error('Dispute failed');
      }
    } catch (error) {
      console.error('Error disputing order:', error);
      toast.error('İtiraz kaydedilirken hata oluştu');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Devam Eden Siparişler</h1>
          <p className="text-gray-600 mt-1">Üretim ve teslimat aşamasındaki siparişleriniz</p>
        </div>
        {showDemo && (
          <Button
            variant="outline"
            onClick={() => setShowDemo(false)}
          >
            Demo'yu Gizle
          </Button>
        )}
      </div>

      {/* Multi Delivery Demo */}
      {showDemo && (
        <div className="space-y-4">
          <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
            <h3 className="font-semibold text-purple-800 mb-2 flex items-center gap-2">
              <Package className="w-5 h-5" />
              🎯 Çoklu Teslimat Sistemi Demo
            </h3>
            <p className="text-sm text-purple-700">
              Bu, büyük metrajlı siparişlerin paket paket takip edildiği çoklu teslimat sistemi demo'sudur.
              Gerçek implementasyonda müşterinin aktif siparişleri burada görünecektir.
            </p>
          </div>

          <CustomerMultiDeliveryDashboard
            order={mockMultiDeliveryOrder}
            onContactSupplier={handleContactSupplier}
            onViewPackageDetails={handleViewPackageDetails}
          />
        </div>
      )}

      {/* Empty State - Only show when demo is hidden */}
      {!showDemo && (
        <div className="text-center py-12">
          <ClockIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Devam eden siparişiniz bulunmuyor
          </h3>
          <p className="text-gray-600 mb-6">
            Yeni bir teklif kabul ederek sipariş oluşturabilirsiniz.
          </p>
          <button
            onClick={() => onNavigate?.('/customer/requests')}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Taleplerime Git
          </button>
        </div>
      )}
    </div>
  );
};

export default OngoingOrdersPage;
