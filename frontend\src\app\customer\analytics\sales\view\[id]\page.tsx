'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';

export default function ViewSalePage() {
  const params = useParams();
  const saleId = params.id;

  const [saleData, setSaleData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Mock data - gerçek uygulamada API'den gelecek
  const mockSaleData = {
    1: {
      id: 1,
      productName: 'Beyaz Mermer',
      category: 'Mermer',
      dimensions: '2.0 x 60 x 120 cm',
      quantity: 15.5,
      surfaceFinish: 'Cilalı',
      companyName: 'ABC İnşaat Ltd.',
      phone: '0532 123 45 67',
      email: '<EMAIL>',
      address: 'İstanbul, Türkiye',
      description: 'Yüksek kalite beyaz mermer projesi',
      salePrice: 2500.00,
      currency: 'USD',
      saleDate: '2024-01-15',
      paymentStatus: 'Ödendi',
      paymentMethod: '<PERSON><PERSON>',
      deliveryStatus: 'Teslim Edildi',
      deliveryDate: '2024-01-20',
      orderNumber: 'SIP-2024-001',
      createdAt: '2024-01-10',
      updatedAt: '2024-01-20'
    },
    2: {
      id: 2,
      productName: 'Gri Granit',
      category: 'Granit',
      dimensions: '3.0 x 80 x 100 cm',
      quantity: 22.0,
      surfaceFinish: 'Mat',
      companyName: 'XYZ Yapı A.Ş.',
      phone: '0533 987 65 43',
      email: '<EMAIL>',
      address: 'Ankara, Türkiye',
      description: 'Özel granit kaplama projesi',
      salePrice: 3200.00,
      currency: 'USD',
      saleDate: '2024-01-20',
      paymentStatus: 'Kısmi Ödendi',
      paymentMethod: 'Çek',
      deliveryStatus: 'Hazırlanıyor',
      deliveryDate: '2024-02-15',
      orderNumber: 'SIP-2024-002',
      paidAmount: 1600.00,
      remainingAmount: 1600.00,
      checkDetails: [
        { checkNumber: 'ÇEK-001', amount: 800, dueDate: '2024-02-01' },
        { checkNumber: 'ÇEK-002', amount: 800, dueDate: '2024-03-01' }
      ],
      createdAt: '2024-01-18',
      updatedAt: '2024-01-25'
    }
  };

  useEffect(() => {
    // Mock API call
    setTimeout(() => {
      const data = mockSaleData[saleId as keyof typeof mockSaleData];
      setSaleData(data);
      setLoading(false);
    }, 500);
  }, [saleId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Ödendi': return 'bg-green-100 text-green-800';
      case 'Kısmi Ödendi': return 'bg-yellow-100 text-yellow-800';
      case 'Bekliyor': return 'bg-gray-100 text-gray-800';
      case 'Gecikmiş': return 'bg-red-100 text-red-800';
      case 'Teslim Edildi': return 'bg-green-100 text-green-800';
      case 'Hazırlanıyor': return 'bg-blue-100 text-blue-800';
      case 'Kargoda': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Satış bilgileri yükleniyor...</span>
        </div>
      </div>
    );
  }

  if (!saleData) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Satış Bulunamadı</h2>
          <p className="text-gray-600 mb-6">Aradığınız satış kaydı bulunamadı.</p>
          <Link
            href="/customer/analytics/sales"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Satış Listesine Dön
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Satış Detayları</h1>
            <p className="text-gray-600 mt-1">Sipariş No: {saleData.orderNumber}</p>
          </div>
          <div className="flex space-x-3">
            <Link
              href={`/customer/analytics/sales/edit/${saleData.id}`}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Düzenle
            </Link>
            <Link
              href="/customer/analytics/sales"
              className="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              ← Geri Dön
            </Link>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        {/* Ana Bilgiler */}
        <div className="lg:col-span-2 space-y-6">
          
          {/* Ürün Bilgileri */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Ürün Bilgileri</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">Ürün Adı</label>
                <p className="text-sm text-gray-900 mt-1">{saleData.productName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Kategori</label>
                <p className="text-sm text-gray-900 mt-1">{saleData.category}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Ebat</label>
                <p className="text-sm text-gray-900 mt-1">{saleData.dimensions}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Miktar</label>
                <p className="text-sm text-gray-900 mt-1">{saleData.quantity} m²</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Yüzey İşlemi</label>
                <p className="text-sm text-gray-900 mt-1">{saleData.surfaceFinish}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Satış Fiyatı</label>
                <p className="text-sm text-gray-900 mt-1 font-semibold">
                  ${saleData.salePrice.toLocaleString()} {saleData.currency}
                </p>
              </div>
            </div>
            {saleData.description && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-500">Açıklama</label>
                <p className="text-sm text-gray-900 mt-1">{saleData.description}</p>
              </div>
            )}
          </div>

          {/* Müşteri Bilgileri */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Müşteri Bilgileri</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">Firma Adı</label>
                <p className="text-sm text-gray-900 mt-1">{saleData.companyName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Telefon</label>
                <p className="text-sm text-gray-900 mt-1">{saleData.phone}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Email</label>
                <p className="text-sm text-gray-900 mt-1">{saleData.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Adres</label>
                <p className="text-sm text-gray-900 mt-1">{saleData.address}</p>
              </div>
            </div>
          </div>

          {/* Çek Detayları (eğer varsa) */}
          {saleData.checkDetails && saleData.checkDetails.length > 0 && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Çek Detayları</h2>
              <div className="space-y-3">
                {saleData.checkDetails.map((check: any, index: number) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{check.checkNumber}</p>
                      <p className="text-xs text-gray-500">Vade: {new Date(check.dueDate).toLocaleDateString('tr-TR')}</p>
                    </div>
                    <p className="text-sm font-semibold text-gray-900">${check.amount}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Yan Panel */}
        <div className="space-y-6">
          
          {/* Durum Bilgileri */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Durum Bilgileri</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Ödeme Durumu</label>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(saleData.paymentStatus)}`}>
                  {saleData.paymentStatus}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Teslimat Durumu</label>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(saleData.deliveryStatus)}`}>
                  {saleData.deliveryStatus}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Ödeme Yöntemi</label>
                <p className="text-sm text-gray-900 mt-1">{saleData.paymentMethod}</p>
              </div>
              {saleData.deliveryDate && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Teslimat Tarihi</label>
                  <p className="text-sm text-gray-900 mt-1">
                    {new Date(saleData.deliveryDate).toLocaleDateString('tr-TR')}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Kısmi Ödeme Bilgileri */}
          {saleData.paymentStatus === 'Kısmi Ödendi' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Ödeme Detayları</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Ödenen Tutar:</span>
                  <span className="text-sm font-medium text-green-600">
                    ${saleData.paidAmount?.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Kalan Tutar:</span>
                  <span className="text-sm font-medium text-red-600">
                    ${saleData.remainingAmount?.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Tarih Bilgileri */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Tarih Bilgileri</h2>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-500">Satış Tarihi</label>
                <p className="text-sm text-gray-900 mt-1">
                  {new Date(saleData.saleDate).toLocaleDateString('tr-TR')}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Oluşturulma</label>
                <p className="text-sm text-gray-900 mt-1">
                  {new Date(saleData.createdAt).toLocaleDateString('tr-TR')}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Son Güncelleme</label>
                <p className="text-sm text-gray-900 mt-1">
                  {new Date(saleData.updatedAt).toLocaleDateString('tr-TR')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
