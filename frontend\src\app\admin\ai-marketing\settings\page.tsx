'use client';

// AI Marketing Settings Page - Fixed saved state issue
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Settings,
  Bot,
  Key,
  Clock,
  Shield,
  Zap,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Play,
  Pause,
  XCircle,
  Eye,
  Download
} from 'lucide-react';

interface AISettings {
  orchestrator: {
    cycleInterval: number; // minutes
    maxRetries: number;
    isActive: boolean;
  };
  apiKeys: {
    openai: string;
    claude: string;
    gemini: string;
  };
  emailMarketing: {
    maxCampaignsPerDay: number;
    defaultFrequency: 'daily' | 'weekly' | 'monthly';
    autoApproval: boolean;
    contentTone: string;
  };
  socialMedia: {
    maxPostsPerDay: number;
    autoApproval: boolean;
    platforms: {
      facebook: boolean;
      instagram: boolean;
      linkedin: boolean;
      twitter: boolean;
      youtube: boolean;
      tiktok: boolean;
    };
    contentTone: string;
  };
  customerAcquisition: {
    maxContactsPerDay: number;
    responseTimeout: number; // hours
    autoFollowUp: boolean;
    leadScoreThreshold: number;
  };
  adsManagement: {
    maxBudgetPerDay: number;
    autoOptimization: boolean;
    bidStrategy: 'conservative' | 'aggressive' | 'balanced';
    roasThreshold: number;
  };
}

export default function AIMarketingSettingsPage() {
  const [settings, setSettings] = useState<AISettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false); // Save success state
  const [showApiKeys, setShowApiKeys] = useState(false);
  const [testingConnection, setTestingConnection] = useState<string | null>(null);

  // Notification states
  const [notification, setNotification] = useState<{
    show: boolean;
    message: string;
    type: 'success' | 'error' | 'info';
  }>({ show: false, message: '', type: 'info' });

  // API key testing states
  const [testingApiKeys, setTestingApiKeys] = useState(false);
  const [apiKeyResults, setApiKeyResults] = useState<{
    openai: boolean | null;
    claude: boolean | null;
    gemini: boolean | null;
  }>({ openai: null, claude: null, gemini: null });

  // Category saving states
  const [savingCategory, setSavingCategory] = useState<string | null>(null);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewChanges, setPreviewChanges] = useState<any>(null);
  const [showBackupModal, setShowBackupModal] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const showNotification = (message: string, type: 'success' | 'error' | 'info') => {
    setNotification({ show: true, message, type });
    setTimeout(() => {
      setNotification({ show: false, message: '', type: 'info' });
    }, 5000);
  };

  const handleTestApiKey = async (provider: string) => {
    if (!settings) return;

    setTestingConnection(provider);
    try {
      const response = await fetch('/api/admin/ai-marketing/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'test-api-key',
          provider,
          apiKey: settings.apiKeys[provider as keyof typeof settings.apiKeys]
        }),
      });

      const data = await response.json();

      if (data.success) {
        setApiKeyResults(prev => ({ ...prev, [provider]: true }));
        showNotification(`${provider.toUpperCase()} API anahtarı başarıyla test edildi`, 'success');
      } else {
        setApiKeyResults(prev => ({ ...prev, [provider]: false }));
        showNotification(`${provider.toUpperCase()} API anahtarı test edilemedi: ${data.error}`, 'error');
      }
    } catch (error) {
      console.error('API key test error:', error);
      setApiKeyResults(prev => ({ ...prev, [provider]: false }));
      showNotification(`${provider.toUpperCase()} API anahtarı test edilirken bir hata oluştu.`, 'error');
    } finally {
      setTestingConnection(null);
    }
  };

  const handleTestAllApiKeys = async () => {
    if (!settings) return;

    setTestingApiKeys(true);
    const providers = ['openai', 'claude', 'gemini'];

    for (const provider of providers) {
      await handleTestApiKey(provider);
    }

    setTestingApiKeys(false);
  };

  const handleCategorySave = async (category: string) => {
    if (!settings) return;

    setSavingCategory(category);
    try {
      const categoryData = { [category]: settings[category as keyof AISettings] };

      const response = await fetch('/api/admin/ai-marketing/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'save-category',
          category,
          data: categoryData
        }),
      });

      const data = await response.json();

      if (data.success) {
        showNotification(`${getCategoryName(category)} ayarları kaydedildi`, 'success');
      } else {
        showNotification(`Kaydetme hatası: ${data.error}`, 'error');
      }
    } catch (error) {
      console.error('Category save error:', error);
      showNotification('Ayarlar kaydedilirken bir hata oluştu.', 'error');
    } finally {
      setSavingCategory(null);
    }
  };

  const handlePreviewChanges = () => {
    if (!settings) return;

    setPreviewChanges(settings);
    setShowPreviewModal(true);
  };

  const handleBackupSettings = async () => {
    try {
      const backup = {
        settings,
        timestamp: new Date().toISOString(),
        version: '1.0'
      };

      const blob = new Blob([JSON.stringify(backup, null, 2)], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `ai-settings-backup-${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showNotification('Ayar yedeği başarıyla indirildi', 'success');
    } catch (error) {
      console.error('Backup error:', error);
      showNotification('Yedek oluşturulurken bir hata oluştu.', 'error');
    }
  };

  const getCategoryName = (category: string): string => {
    const names: { [key: string]: string } = {
      'orchestrator': 'Orchestrator',
      'apiKeys': 'API Anahtarları',
      'emailMarketing': 'Email Marketing',
      'socialMedia': 'Sosyal Medya',
      'customerAcquisition': 'Müşteri Arama',
      'adsManagement': 'Reklam Yönetimi'
    };
    return names[category] || category;
  };

  const fetchSettings = async () => {
    setLoading(true);
    try {
      // Fetch real settings from API
      const response = await fetch('/api/admin/ai-marketing/settings');
      const data = await response.json();

      if (data.success) {
        setSettings(data.data);
      } else {
        console.error('API Error:', data.error);
        // Fallback to mock data
        const mockSettings: AISettings = {
        orchestrator: {
          cycleInterval: 30,
          maxRetries: 3,
          isActive: true
        },
        apiKeys: {
          openai: 'sk-***************************',
          claude: 'claude-*********************',
          gemini: 'gemini-*********************'
        },
        emailMarketing: {
          maxCampaignsPerDay: 5,
          defaultFrequency: 'weekly',
          autoApproval: false,
          contentTone: 'professional'
        },
        socialMedia: {
          maxPostsPerDay: 10,
          autoApproval: false,
          platforms: {
            facebook: true,
            instagram: true,
            linkedin: true,
            twitter: true,
            youtube: false,
            tiktok: false
          },
          contentTone: 'friendly'
        },
        customerAcquisition: {
          maxContactsPerDay: 20,
          responseTimeout: 72,
          autoFollowUp: true,
          leadScoreThreshold: 75
        },
        adsManagement: {
          maxBudgetPerDay: 500,
          autoOptimization: true,
          bidStrategy: 'balanced',
          roasThreshold: 2.0
        }
      };

        setSettings(mockSettings);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    console.log('handleSave called');
    if (!settings) return;

    setSaving(true);
    try {
      // Ayarları validate et
      const validationResult = validateSettings(settings);
      if (!validationResult.isValid) {
        showNotification(`Ayar hatası: ${validationResult.errors.join(', ')}`, 'error');
        return;
      }

      // Real API call
      console.log('Ayarlar kaydediliyor:', settings);

      const response = await fetch('/api/admin/ai-marketing/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Ayarlar kaydedilemedi');
      }

      setSaved(true);
      setTimeout(() => setSaved(false), 3000);
      showNotification('Ayarlar başarıyla kaydedildi', 'success');
    } catch (error) {
      console.error('Error saving settings:', error);
      showNotification('Ayarlar kaydedilirken bir hata oluştu.', 'error');
    } finally {
      setSaving(false);
    }
  };

  const validateSettings = (settings: AISettings) => {
    const errors: string[] = [];

    // Orchestrator validasyonu
    if (settings.orchestrator.cycleInterval < 1) {
      errors.push('Döngü aralığı en az 1 dakika olmalıdır');
    }
    if (settings.orchestrator.maxRetries < 1) {
      errors.push('Maksimum deneme sayısı en az 1 olmalıdır');
    }

    // API Keys validasyonu
    if (!settings.apiKeys.openai || settings.apiKeys.openai.length < 10) {
      errors.push('Geçerli bir OpenAI API anahtarı gereklidir');
    }

    // Email Marketing validasyonu
    if (settings.emailMarketing.maxCampaignsPerDay < 1) {
      errors.push('Günlük maksimum kampanya sayısı en az 1 olmalıdır');
    }

    // Social Media validasyonu
    if (settings.socialMedia.maxPostsPerDay < 1) {
      errors.push('Günlük maksimum post sayısı en az 1 olmalıdır');
    }

    // Customer Acquisition validasyonu
    if (settings.customerAcquisition.maxContactsPerDay < 1) {
      errors.push('Günlük maksimum iletişim sayısı en az 1 olmalıdır');
    }
    if (settings.customerAcquisition.responseTimeout < 1) {
      errors.push('Yanıt zaman aşımı en az 1 saat olmalıdır');
    }

    // Ads Management validasyonu
    if (settings.adsManagement.maxBudgetPerDay < 1) {
      errors.push('Günlük maksimum bütçe en az 1 TL olmalıdır');
    }
    if (settings.adsManagement.roasThreshold < 0.1) {
      errors.push('ROAS eşiği en az 0.1 olmalıdır');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const handleSettingChange = (section: keyof AISettings, field: string, value: any) => {
    if (!settings) return;

    setSaved(false); // Reset saved state when settings change
    setSettings(prev => {
      if (!prev) return prev;

      return {
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value
        }
      };
    });
  };

  const handleNestedSettingChange = (section: keyof AISettings, nestedField: string, field: string, value: any) => {
    if (!settings) return;

    setSaved(false); // Reset saved state when settings change
    setSettings(prev => {
      if (!prev) return prev;

      return {
        ...prev,
        [section]: {
          ...prev[section],
          [nestedField]: {
            ...(prev[section] as any)[nestedField],
            [field]: value
          }
        }
      };
    });
  };

  const handleReset = async () => {
    if (confirm('Tüm ayarları varsayılan değerlere sıfırlamak istediğinizden emin misiniz?')) {
      setLoading(true);
      try {
        await fetchSettings(); // Varsayılan ayarları yeniden yükle
        console.log('Ayarlar sıfırlandı');
      } catch (error) {
        console.error('Ayarları sıfırlama hatası:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const updateSettings = (path: string, value: any) => {
    if (!settings) return;
    
    const keys = path.split('.');
    const newSettings = { ...settings };
    let current: any = newSettings;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    setSettings(newSettings);
  };

  const toggleOrchestrator = () => {
    if (settings) {
      updateSettings('orchestrator.isActive', !settings.orchestrator.isActive);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <span className="text-lg">AI ayarları yükleniyor...</span>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Ayarlar Yüklenemedi</h3>
        <p className="text-gray-600 mb-4">AI ayarları yüklenirken bir hata oluştu.</p>
        <Button onClick={fetchSettings}>
          <RefreshCw className="w-4 h-4 mr-2" />
          Tekrar Dene
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI Sistem Ayarları</h1>
          <p className="text-gray-600 mt-1">
            AI pazarlama sisteminin davranışını ve parametrelerini yapılandırın
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handlePreviewChanges}
            className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
          >
            <Eye className="w-4 h-4 mr-2" />
            Önizleme
          </Button>
          <Button
            variant="outline"
            onClick={handleBackupSettings}
            className="hover:bg-green-50 hover:border-green-300 transition-colors"
          >
            <Download className="w-4 h-4 mr-2" />
            Yedekle
          </Button>
          <Button
            variant="outline"
            onClick={toggleOrchestrator}
            className={settings.orchestrator.isActive ? 'border-red-500 text-red-600' : 'border-green-500 text-green-600'}
          >
            {settings.orchestrator.isActive ? (
              <>
                <Pause className="w-4 h-4 mr-2" />
                Sistemi Durdur
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Sistemi Başlat
              </>
            )}
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : saved ? (
              <CheckCircle className="w-4 h-4 mr-2" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {saved ? 'Kaydedildi' : 'Kaydet'}
          </Button>
        </div>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bot className="w-5 h-5 mr-2" />
            Sistem Durumu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className={`w-3 h-3 rounded-full ${settings.orchestrator.isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="font-medium">
                AI Orchestrator {settings.orchestrator.isActive ? 'Aktif' : 'Pasif'}
              </span>
            </div>
            <Badge variant={settings.orchestrator.isActive ? 'default' : 'secondary'}>
              {settings.orchestrator.isActive ? 'Çalışıyor' : 'Durduruldu'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Main Tabs */}
      <Tabs defaultValue="orchestrator" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="orchestrator">Orchestrator</TabsTrigger>
          <TabsTrigger value="api-keys">API Anahtarları</TabsTrigger>
          <TabsTrigger value="email">Email Marketing</TabsTrigger>
          <TabsTrigger value="social">Sosyal Medya</TabsTrigger>
          <TabsTrigger value="customers">Müşteri Arama</TabsTrigger>
          <TabsTrigger value="ads">Reklam Yönetimi</TabsTrigger>
        </TabsList>

        <TabsContent value="orchestrator" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Orchestrator Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Döngü Aralığı (dakika)
                </label>
                <Input
                  type="number"
                  value={settings.orchestrator.cycleInterval}
                  onChange={(e) => updateSettings('orchestrator.cycleInterval', parseInt(e.target.value))}
                  className="w-32"
                />
                <p className="text-xs text-gray-500 mt-1">
                  AI sisteminin ne sıklıkla çalışacağını belirler
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Maksimum Yeniden Deneme
                </label>
                <Input
                  type="number"
                  value={settings.orchestrator.maxRetries}
                  onChange={(e) => updateSettings('orchestrator.maxRetries', parseInt(e.target.value))}
                  className="w-32"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Başarısız görevler için yeniden deneme sayısı
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Category Save Button */}
          <div className="flex justify-end">
            <Button
              onClick={() => handleCategorySave('orchestrator')}
              disabled={savingCategory === 'orchestrator'}
              className="hover:bg-blue-600 transition-colors"
            >
              {savingCategory === 'orchestrator' ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Orchestrator Ayarlarını Kaydet
                </>
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="api-keys" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Key className="w-5 h-5 mr-2" />
                AI API Anahtarları
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  OpenAI API Key
                </label>
                <div className="flex items-center space-x-2">
                  <Input
                    type={showApiKeys ? 'text' : 'password'}
                    value={settings.apiKeys.openai}
                    onChange={(e) => updateSettings('apiKeys.openai', e.target.value)}
                    placeholder="sk-..."
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowApiKeys(!showApiKeys)}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                  {apiKeyResults.openai !== null && (
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                      apiKeyResults.openai ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                    }`}>
                      {apiKeyResults.openai ? '✓' : '✗'}
                    </div>
                  )}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Claude API Key
                </label>
                <div className="flex items-center space-x-2">
                  <Input
                    type={showApiKeys ? 'text' : 'password'}
                    value={settings.apiKeys.claude}
                    onChange={(e) => updateSettings('apiKeys.claude', e.target.value)}
                    placeholder="claude-..."
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowApiKeys(!showApiKeys)}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                  {apiKeyResults.claude !== null && (
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                      apiKeyResults.claude ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                    }`}>
                      {apiKeyResults.claude ? '✓' : '✗'}
                    </div>
                  )}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gemini API Key
                </label>
                <div className="flex items-center space-x-2">
                  <Input
                    type={showApiKeys ? 'text' : 'password'}
                    value={settings.apiKeys.gemini}
                    onChange={(e) => updateSettings('apiKeys.gemini', e.target.value)}
                    placeholder="gemini-..."
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowApiKeys(!showApiKeys)}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                  {apiKeyResults.gemini !== null && (
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                      apiKeyResults.gemini ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                    }`}>
                      {apiKeyResults.gemini ? '✓' : '✗'}
                    </div>
                  )}
                </div>
              </div>

              <div className="pt-4 border-t space-y-2">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestApiKey('openai')}
                    disabled={!settings?.apiKeys.openai || testingConnection === 'openai'}
                  >
                    {testingConnection === 'openai' ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                    ) : (
                      <Zap className="w-4 h-4 mr-2" />
                    )}
                    Test OpenAI
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestApiKey('claude')}
                    disabled={!settings?.apiKeys.claude || testingConnection === 'claude'}
                  >
                    {testingConnection === 'claude' ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                    ) : (
                      <Zap className="w-4 h-4 mr-2" />
                    )}
                    Test Claude
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestApiKey('gemini')}
                    disabled={!settings?.apiKeys.gemini || testingConnection === 'gemini'}
                  >
                    {testingConnection === 'gemini' ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                    ) : (
                      <Zap className="w-4 h-4 mr-2" />
                    )}
                    Test Gemini
                  </Button>
                </div>
                <Button
                  variant="outline"
                  onClick={handleTestAllApiKeys}
                  disabled={testingApiKeys}
                  className="w-full"
                >
                  {testingApiKeys ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                      Tüm API Anahtarları Test Ediliyor...
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4 mr-2" />
                      Tüm API Anahtarlarını Test Et
                    </>
                  )}
                </Button>
                <p className="text-xs text-gray-500 mt-2 text-center">
                  Bu işlem API anahtarlarınızın geçerliliğini kontrol eder
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Category Save Button */}
          <div className="flex justify-end">
            <Button
              onClick={() => handleCategorySave('apiKeys')}
              disabled={savingCategory === 'apiKeys'}
              className="hover:bg-blue-600 transition-colors"
            >
              {savingCategory === 'apiKeys' ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  API Anahtarlarını Kaydet
                </>
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="email" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Marketing Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Günlük Maksimum Kampanya
                  </label>
                  <Input
                    type="number"
                    value={settings.emailMarketing.maxCampaignsPerDay}
                    onChange={(e) => updateSettings('emailMarketing.maxCampaignsPerDay', parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Varsayılan Sıklık
                  </label>
                  <select 
                    value={settings.emailMarketing.defaultFrequency}
                    onChange={(e) => updateSettings('emailMarketing.defaultFrequency', e.target.value)}
                    className="w-full border rounded px-3 py-2"
                  >
                    <option value="daily">Günlük</option>
                    <option value="weekly">Haftalık</option>
                    <option value="monthly">Aylık</option>
                  </select>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  İçerik Tonu
                </label>
                <Input
                  value={settings.emailMarketing.contentTone}
                  onChange={(e) => updateSettings('emailMarketing.contentTone', e.target.value)}
                  placeholder="professional, friendly, formal..."
                />
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="email-auto-approval"
                  checked={settings.emailMarketing.autoApproval}
                  onChange={(e) => updateSettings('emailMarketing.autoApproval', e.target.checked)}
                />
                <label htmlFor="email-auto-approval" className="text-sm font-medium text-gray-700">
                  Otomatik Onay
                </label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="social" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sosyal Medya Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Günlük Maksimum Post
                </label>
                <Input
                  type="number"
                  value={settings.socialMedia.maxPostsPerDay}
                  onChange={(e) => updateSettings('socialMedia.maxPostsPerDay', parseInt(e.target.value))}
                  className="w-32"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Aktif Platformlar
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {Object.entries(settings.socialMedia.platforms).map(([platform, enabled]) => (
                    <div key={platform} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`platform-${platform}`}
                        checked={enabled}
                        onChange={(e) => updateSettings(`socialMedia.platforms.${platform}`, e.target.checked)}
                      />
                      <label htmlFor={`platform-${platform}`} className="text-sm capitalize">
                        {platform}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  İçerik Tonu
                </label>
                <Input
                  value={settings.socialMedia.contentTone}
                  onChange={(e) => updateSettings('socialMedia.contentTone', e.target.value)}
                  placeholder="friendly, professional, casual..."
                />
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="social-auto-approval"
                  checked={settings.socialMedia.autoApproval}
                  onChange={(e) => updateSettings('socialMedia.autoApproval', e.target.checked)}
                />
                <label htmlFor="social-auto-approval" className="text-sm font-medium text-gray-700">
                  Otomatik Onay
                </label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Müşteri Arama Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Günlük Maksimum İletişim
                  </label>
                  <Input
                    type="number"
                    value={settings.customerAcquisition.maxContactsPerDay}
                    onChange={(e) => updateSettings('customerAcquisition.maxContactsPerDay', parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Yanıt Bekleme Süresi (saat)
                  </label>
                  <Input
                    type="number"
                    value={settings.customerAcquisition.responseTimeout}
                    onChange={(e) => updateSettings('customerAcquisition.responseTimeout', parseInt(e.target.value))}
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Lead Skor Eşiği
                </label>
                <Input
                  type="number"
                  value={settings.customerAcquisition.leadScoreThreshold}
                  onChange={(e) => updateSettings('customerAcquisition.leadScoreThreshold', parseInt(e.target.value))}
                  className="w-32"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Bu skorun üzerindeki potansiyel müşterilerle iletişim kurulur
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="auto-followup"
                  checked={settings.customerAcquisition.autoFollowUp}
                  onChange={(e) => updateSettings('customerAcquisition.autoFollowUp', e.target.checked)}
                />
                <label htmlFor="auto-followup" className="text-sm font-medium text-gray-700">
                  Otomatik Takip
                </label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ads" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Reklam Yönetimi Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Günlük Maksimum Bütçe ($)
                  </label>
                  <Input
                    type="number"
                    value={settings.adsManagement.maxBudgetPerDay}
                    onChange={(e) => updateSettings('adsManagement.maxBudgetPerDay', parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ROAS Eşiği
                  </label>
                  <Input
                    type="number"
                    step="0.1"
                    value={settings.adsManagement.roasThreshold}
                    onChange={(e) => updateSettings('adsManagement.roasThreshold', parseFloat(e.target.value))}
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Teklif Stratejisi
                </label>
                <select 
                  value={settings.adsManagement.bidStrategy}
                  onChange={(e) => updateSettings('adsManagement.bidStrategy', e.target.value)}
                  className="w-full border rounded px-3 py-2"
                >
                  <option value="conservative">Muhafazakar</option>
                  <option value="balanced">Dengeli</option>
                  <option value="aggressive">Agresif</option>
                </select>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="auto-optimization"
                  checked={settings.adsManagement.autoOptimization}
                  onChange={(e) => updateSettings('adsManagement.autoOptimization', e.target.checked)}
                />
                <label htmlFor="auto-optimization" className="text-sm font-medium text-gray-700">
                  Otomatik Optimizasyon
                </label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6 border-t">
        <div className="flex items-center space-x-2">
          {saved && (
            <div className="flex items-center text-green-600">
              <CheckCircle className="w-4 h-4 mr-1" />
              <span className="text-sm">Ayarlar kaydedildi</span>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={saving || loading}
            className="hover:bg-red-50 hover:border-red-300 transition-colors"
          >
            Sıfırla
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving || !settings}
            className="hover:bg-green-600 transition-colors"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Kaydediliyor...
              </>
            ) : (
              'Ayarları Kaydet'
            )}
          </Button>
        </div>
      </div>

      {/* Notification Toast */}
      {notification.show && (
        <div className={`fixed top-4 right-4 z-[10000] p-4 rounded-lg shadow-lg transition-all duration-300 ${
          notification.type === 'success' ? 'bg-green-500 text-white' :
          notification.type === 'error' ? 'bg-red-500 text-white' :
          'bg-blue-500 text-white'
        }`}>
          <div className="flex items-center space-x-2">
            {notification.type === 'success' && <CheckCircle className="w-5 h-5" />}
            {notification.type === 'error' && <XCircle className="w-5 h-5" />}
            {notification.type === 'info' && <Clock className="w-5 h-5" />}
            <span>{notification.message}</span>
            <button
              onClick={() => setNotification({ show: false, message: '', type: 'info' })}
              className="ml-2 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Preview Changes Modal */}
      {showPreviewModal && previewChanges && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">Ayar Değişiklikleri Önizlemesi</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPreviewModal(false)}
              >
                <XCircle className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-4">
              {/* Quick Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Özet</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Sistem Durumu</label>
                      <Badge variant={previewChanges.orchestrator.isActive ? 'default' : 'secondary'}>
                        {previewChanges.orchestrator.isActive ? 'Aktif' : 'Pasif'}
                      </Badge>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">API Anahtarları</label>
                      <p className="text-sm">
                        {Object.values(previewChanges.apiKeys).filter(Boolean).length}/3 Yapılandırılmış
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email Kampanya Limiti</label>
                      <p className="text-sm">{previewChanges.emailMarketing.maxCampaignsPerDay}/gün</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Sosyal Medya Post Limiti</label>
                      <p className="text-sm">{previewChanges.socialMedia.maxPostsPerDay}/gün</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Impact Analysis */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg text-orange-600">Etki Analizi</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <AlertTriangle className="w-4 h-4 text-orange-600 mr-2" />
                      <span className="text-sm">Bu değişiklikler sistem performansını etkileyebilir</span>
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>• Orchestrator ayarları değiştirildiğinde sistem yeniden başlatılacak</p>
                      <p>• API anahtarları değiştirildiğinde mevcut bağlantılar test edilecek</p>
                      <p>• Sosyal medya ayarları anında etkili olacak</p>
                      <p>• Email marketing ayarları mevcut kampanyaları etkilemeyecek</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Configuration Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Yapılandırma Detayları</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <strong>Döngü Aralığı:</strong> {previewChanges.orchestrator.cycleInterval}dk
                      </div>
                      <div>
                        <strong>Max Deneme:</strong> {previewChanges.orchestrator.maxRetries}
                      </div>
                      <div>
                        <strong>Email Sıklığı:</strong> {previewChanges.emailMarketing.defaultFrequency}
                      </div>
                    </div>
                    <div className="text-sm">
                      <strong>Aktif Platformlar:</strong>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {Object.entries(previewChanges.socialMedia.platforms).map(([platform, active]) => (
                          <Badge key={platform} variant={active ? 'default' : 'secondary'} className="text-xs">
                            {platform}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowPreviewModal(false)}
              >
                Kapat
              </Button>
              <Button
                onClick={() => {
                  setShowPreviewModal(false);
                  handleSave();
                }}
              >
                <Save className="w-4 h-4 mr-2" />
                Tüm Değişiklikleri Kaydet
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
