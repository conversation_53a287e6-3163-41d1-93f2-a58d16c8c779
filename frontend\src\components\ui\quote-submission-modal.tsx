"use client"

import * as React from "react"
import { But<PERSON> } from "./button"
import { Input } from "./input"
import { Textarea } from "./textarea"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./card"
import { Badge } from "./badge"
import { useQuote } from "@/contexts/quote-context"
import { useProducerAuth } from "@/contexts/producer-auth-context"

interface QuoteSubmissionModalProps {
  isOpen: boolean
  onClose: () => void
  request: any | null
  onSubmitQuote?: (quoteData: any) => Promise<boolean>
}

interface QuoteItemData {
  productId: string
  productName: string
  productCategory: string
  specificationId: string
  type: 'sized' | 'slab'
  thickness: string
  width: string
  length: string
  surface: string
  packaging: string
  delivery: string
  area: number // metraj m²
  targetPrice: number // müşterinin hedef fiyatı
  unitPrice: number // üreticinin teklif fiyatı
  totalPrice: number // area * unitPrice
  currency: string
  deliveryTime: string
  notes: string
}

export function QuoteSubmissionModal({ isOpen, onClose, request, onSubmitQuote }: QuoteSubmissionModalProps) {
  const { createQuote } = useQuote()
  const { producer, isLoading } = useProducerAuth()

  // Debug producer state
  React.useEffect(() => {
    console.log('QuoteSubmissionModal - Producer state:', { producer, isLoading })
  }, [producer, isLoading])

  const [quoteItems, setQuoteItems] = React.useState<QuoteItemData[]>([])
  const [formData, setFormData] = React.useState({
    validUntil: '',
    terms: '',
    currency: 'USD'
  })

  // Initialize quote items when request changes
  React.useEffect(() => {
    if (request && request.products) {
      const items: QuoteItemData[] = []
      request.products.forEach((product: any) => {
        product.specifications.forEach((spec: any) => {
          items.push({
            productId: product.productId,
            productName: product.productName,
            productCategory: product.productCategory,
            specificationId: spec.id,
            type: spec.type,
            thickness: spec.thickness,
            width: spec.width,
            length: spec.length,
            surface: spec.surface,
            packaging: spec.packaging,
            delivery: spec.delivery,
            area: parseFloat(spec.area) || 0,
            targetPrice: parseFloat(spec.targetPrice) || 0,
            unitPrice: 0,
            totalPrice: 0,
            currency: spec.currency || 'USD',
            deliveryTime: '15 gün',
            notes: ''
          })
        })
      })
      setQuoteItems(items)
    }
  }, [request])

  const updateQuoteItem = (index: number, field: keyof QuoteItemData, value: any) => {
    setQuoteItems(prev => {
      const updated = [...prev]
      updated[index] = { ...updated[index], [field]: value }

      // Auto-calculate total price when unit price or area changes
      if (field === 'unitPrice' || field === 'area') {
        updated[index].totalPrice = updated[index].unitPrice * updated[index].area
      }

      return updated
    })
  }

  const getTotalAmount = () => {
    return quoteItems.reduce((sum, item) => sum + item.totalPrice, 0)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    console.log('Producer:', producer)
    console.log('Request:', request)

    // Use fallback producer if not available
    const currentProducer = producer || {
      id: '1',
      name: 'Ahmet Taş',
      email: '<EMAIL>',
      companyName: 'Taş Üretim A.Ş.',
      role: 'producer' as const,
      isApproved: true,
      phone: '+90 ************',
      address: 'Afyon, Türkiye'
    }

    if (!request) {
      alert('Talep bilgileri eksik')
      console.error('Missing request data:', { request })
      return
    }

    if (quoteItems.some(item => item.unitPrice <= 0)) {
      alert('Lütfen tüm ürünler için geçerli fiyat girin')
      return
    }

    if (!formData.validUntil) {
      alert('Lütfen teklif geçerlilik tarihini belirtin')
      return
    }

    try {
      const quoteData = {
        quoteRequestId: request.id,
        producerId: currentProducer.id,
        producerName: currentProducer.name,
        producerCompany: currentProducer.companyName || 'Taş Üretim A.Ş.',
        producerEmail: currentProducer.email,
        items: quoteItems,
        totalAmount: getTotalAmount(),
        currency: formData.currency,
        validUntil: formData.validUntil,
        terms: formData.terms,
        isResubmission: request.status === 'rejected'
      }

      // If onSubmitQuote prop is provided (for rejected requests), use it
      if (onSubmitQuote) {
        const success = await onSubmitQuote(quoteData)
        if (success) {
          // Reset form
          setQuoteItems([])
          setFormData({
            validUntil: '',
            terms: '',
            currency: 'USD'
          })
        }
        return
      }

      // Default behavior for normal quote submission
      // Send quote to backend API
      const response = await fetch(`/api/quotes/requests/${request.id}/quotes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(quoteData),
      })

      if (!response.ok) {
        throw new Error('Teklif gönderilirken hata oluştu')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Teklif gönderilirken hata oluştu')
      }

      // Convert QuoteItemData to QuoteItem format
      const convertedItems = quoteItems.map(item => ({
        id: `item-${Date.now()}-${Math.random()}`,
        productId: item.productId,
        productName: item.productName,
        specificationId: item.specificationId,
        quantity: item.area, // Use area as quantity
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        currency: item.currency,
        deliveryTime: item.deliveryTime,
        notes: item.notes
      }))

      // Add quote to context using the returned data
      const newQuote = {
        quoteRequestId: request.id,
        producerId: currentProducer.id,
        producerName: currentProducer.name,
        producerCompany: currentProducer.companyName || 'Taş Üretim A.Ş.',
        producerEmail: currentProducer.email,
        items: convertedItems,
        totalAmount: getTotalAmount(),
        currency: formData.currency,
        validUntil: new Date(formData.validUntil),
        terms: formData.terms,
        status: 'pending' as const
      }

      // Update context with new quote
      await createQuote(newQuote)

      alert('Teklif başarıyla gönderildi! Teklif Verildi sayfasından takip edebilirsiniz.')
      onClose()

      // Reset form
      setQuoteItems([])
      setFormData({
        validUntil: '',
        terms: '',
        currency: 'USD'
      })

      // Redirect to quoted page with a small delay to ensure context update
      setTimeout(() => {
        if (typeof window !== 'undefined') {
          window.location.href = '/producer/quote-requests/quoted'
        }
      }, 500)

    } catch (error) {
      console.error('Error submitting quote:', error)
      alert('Teklif gönderilirken bir hata oluştu. Lütfen tekrar deneyin.')
    }
  }

  if (!isOpen || !request) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {request.status === 'rejected' ? 'Yeniden Teklif Ver' : 'Teklif Ver'}
            </h2>
            <p className="text-gray-600">
              {request.customerName} - Talep #{request.id.slice(-6)}
              {request.status === 'rejected' && (
                <span className="ml-2 text-red-600 font-medium">(Reddedildi)</span>
              )}
            </p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Customer Request Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Müşteri Talep Özeti</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Müşteri:</span>
                  <span className="ml-2 font-medium">{request.customerName}</span>
                </div>
                <div>
                  <span className="text-gray-500">Teslimat:</span>
                  <span className="ml-2 font-medium">{request.deliveryLocation}</span>
                </div>
                <div>
                  <span className="text-gray-500">Son Tarih:</span>
                  <span className="ml-2 font-medium text-red-600">{request.deadline}</span>
                </div>
              </div>
              {request.message && (
                <div className="mt-3">
                  <span className="text-gray-500">Mesaj:</span>
                  <p className="mt-1 text-sm text-gray-700">{request.message}</p>
                </div>
              )}
            </div>

            {/* Rejection Reason (if rejected) */}
            {request.status === 'rejected' && request.rejectionReason && (
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-red-800 mb-2 flex items-center gap-2">
                  <span className="text-red-600">⚠️</span>
                  Önceki Red Sebebi
                </h3>
                <p className="text-red-700 bg-red-100 p-3 rounded">{request.rejectionReason}</p>
                <p className="text-sm text-red-600 mt-2">
                  💡 Bu sebepleri göz önünde bulundurarak yeni teklifinizi hazırlayın.
                </p>
              </div>
            )}

            {/* Excel-Style Quote Table */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Teklif Detayları</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full border border-gray-300 bg-white">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        #
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Ürün Adı
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Kategori
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Tip
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Kalınlık (cm)
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        En (cm)
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Boy (cm)
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Yüzey
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Ambalaj
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Teslimat
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Metraj (m²)
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Hedef Fiyat
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider bg-amber-50">
                        Teklif Fiyatı*
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider bg-amber-50">
                        Toplam Fiyat
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Teslimat Süresi
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Notlar
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {quoteItems.map((item, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        <td className="border border-gray-300 px-3 py-2 text-sm text-gray-900">
                          {index + 1}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm font-medium text-gray-900">
                          <a
                            href={`/products/${item.productId}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 hover:underline"
                          >
                            {item.productName}
                          </a>
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm text-gray-700">
                          {item.productCategory}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm text-gray-700">
                          <Badge variant={item.type === 'sized' ? 'default' : 'secondary'}>
                            {item.type === 'sized' ? 'Ebatlı' : 'Plaka'}
                          </Badge>
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm text-gray-700">
                          {item.thickness}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm text-gray-700">
                          {item.width}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm text-gray-700">
                          {item.length}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm text-gray-700">
                          {item.surface}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm text-gray-700">
                          {item.packaging}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm text-gray-700">
                          {item.delivery}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm text-gray-700">
                          {item.area.toFixed(2)}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm text-gray-700">
                          {item.targetPrice.toFixed(2)} {item.currency}
                        </td>
                        <td className="border border-gray-300 px-3 py-2 bg-amber-50">
                          <Input
                            type="number"
                            step="0.01"
                            value={item.unitPrice}
                            onChange={(e) => updateQuoteItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                            className="w-full min-w-[100px] text-sm"
                            placeholder="0.00"
                          />
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-sm font-medium bg-amber-50">
                          {item.totalPrice.toFixed(2)} {item.currency}
                        </td>
                        <td className="border border-gray-300 px-3 py-2">
                          <Input
                            type="text"
                            value={item.deliveryTime}
                            onChange={(e) => updateQuoteItem(index, 'deliveryTime', e.target.value)}
                            className="w-full min-w-[80px] text-sm"
                            placeholder="15 gün"
                          />
                        </td>
                        <td className="border border-gray-300 px-3 py-2">
                          <Textarea
                            value={item.notes}
                            onChange={(e) => updateQuoteItem(index, 'notes', e.target.value)}
                            className="w-full min-w-[150px] text-sm"
                            rows={2}
                            placeholder="Özel notlar..."
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Total Summary */}
            <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-amber-900">Teklif Özeti</h3>
                <div className="text-right">
                  <div className="text-sm text-amber-700">Toplam Metraj</div>
                  <div className="text-lg font-semibold text-amber-900">
                    {quoteItems.reduce((sum, item) => sum + item.area, 0).toFixed(2)} m²
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-center p-3 bg-white rounded-lg border border-amber-200">
                  <div className="text-sm text-gray-600">Ürün Sayısı</div>
                  <div className="text-xl font-bold text-gray-900">{quoteItems.length}</div>
                </div>
                <div className="text-center p-3 bg-white rounded-lg border border-amber-200">
                  <div className="text-sm text-gray-600">Ortalama Fiyat</div>
                  <div className="text-xl font-bold text-gray-900">
                    {quoteItems.length > 0 ? (getTotalAmount() / quoteItems.reduce((sum, item) => sum + item.area, 0)).toFixed(2) : '0.00'} {formData.currency}/m²
                  </div>
                </div>
                <div className="text-center p-3 bg-amber-100 rounded-lg border border-amber-300">
                  <div className="text-sm text-amber-700">TOPLAM TUTAR</div>
                  <div className="text-2xl font-bold text-amber-900">
                    {getTotalAmount().toFixed(2)} {formData.currency}
                  </div>
                </div>
              </div>
            </div>

            {/* Quote Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Para Birimi *
                </label>
                <select
                  value={formData.currency}
                  onChange={(e) => {
                    const newCurrency = e.target.value
                    setFormData(prev => ({ ...prev, currency: newCurrency }))
                    // Update all quote items currency
                    setQuoteItems(prev => prev.map(item => ({ ...item, currency: newCurrency })))
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                >
                  <option value="USD">USD ($)</option>
                  <option value="EUR">EUR (€)</option>
                  <option value="TL">TL (₺)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Teklif Geçerlilik Tarihi *
                </label>
                <Input
                  type="date"
                  value={formData.validUntil}
                  onChange={(e) => setFormData(prev => ({ ...prev, validUntil: e.target.value }))}
                  className="w-full"
                  min={new Date().toISOString().split('T')[0]}
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Şartlar ve Koşullar
              </label>
              <Textarea
                value={formData.terms}
                onChange={(e) => setFormData(prev => ({ ...prev, terms: e.target.value }))}
                className="w-full"
                rows={4}
                placeholder="Ödeme şartları, teslimat koşulları, garanti bilgileri, özel notlar vb..."
              />
              <p className="text-xs text-gray-500 mt-1">
                Bu bilgiler müşteriye gönderilecek teklif belgesinde yer alacaktır.
              </p>
            </div>

            {/* Individual Product Notes */}
            {quoteItems.some(item => item.notes) && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Ürün Notları</h4>
                <div className="space-y-2">
                  {quoteItems.map((item, index) => (
                    item.notes && (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium text-gray-900">{item.productName}</div>
                        <div className="text-sm text-gray-700 mt-1">{item.notes}</div>
                      </div>
                    )
                  ))}
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <span>
                <strong>{quoteItems.length}</strong> ürün
              </span>
              <span>
                <strong>{quoteItems.reduce((sum, item) => sum + item.area, 0).toFixed(2)}</strong> m²
              </span>
              <span>
                Toplam: <span className="font-bold text-lg text-amber-600">{getTotalAmount().toFixed(2)} {formData.currency}</span>
              </span>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={onClose}>
              İptal
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={quoteItems.some(item => item.unitPrice <= 0) || !formData.validUntil}
              className="bg-amber-600 hover:bg-amber-700"
            >
              Teklif Gönder
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
