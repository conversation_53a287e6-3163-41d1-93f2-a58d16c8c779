import { SettingsModule } from '../core/SettingsModule';
import { SettingsCategory } from '../types/settings.types';

export class SystemSettingsModule extends SettingsModule {
  protected category: SettingsCategory = 'SYSTEM';

  protected getDefaultSettings() {
    return {
      debugMode: {
        key: 'debug_mode',
        value: false,
        type: 'boolean',
        category: this.category,
        description: 'Enable debug mode'
      },
      logLevel: {
        key: 'log_level',
        value: 'info',
        type: 'string',
        category: this.category,
        description: 'System log level'
      },
      cacheEnabled: {
        key: 'cache_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable system caching'
      },
      cacheTtl: {
        key: 'cache_ttl',
        value: 3600, // 1 hour in seconds
        type: 'number',
        category: this.category,
        description: 'Cache TTL in seconds'
      },
      rateLimitEnabled: {
        key: 'rate_limit_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable API rate limiting'
      },
      rateLimitRequests: {
        key: 'rate_limit_requests',
        value: 100,
        type: 'number',
        category: this.category,
        description: 'Rate limit requests per window'
      },
      rateLimitWindow: {
        key: 'rate_limit_window',
        value: 900, // 15 minutes in seconds
        type: 'number',
        category: this.category,
        description: 'Rate limit window in seconds'
      },
      backupEnabled: {
        key: 'backup_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable automatic backups'
      },
      backupFrequency: {
        key: 'backup_frequency',
        value: 'daily',
        type: 'string',
        category: this.category,
        description: 'Backup frequency'
      },
      backupRetentionDays: {
        key: 'backup_retention_days',
        value: 30,
        type: 'number',
        category: this.category,
        description: 'Backup retention period in days'
      },
      performanceMonitoring: {
        key: 'performance_monitoring',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable performance monitoring'
      },
      errorReporting: {
        key: 'error_reporting',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable error reporting'
      },
      analyticsEnabled: {
        key: 'analytics_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable analytics tracking'
      },
      googleAnalyticsId: {
        key: 'google_analytics_id',
        value: '',
        type: 'string',
        category: this.category,
        description: 'Google Analytics tracking ID'
      },
      compressionEnabled: {
        key: 'compression_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable response compression'
      },
      corsEnabled: {
        key: 'cors_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable CORS'
      },
      allowedOrigins: {
        key: 'allowed_origins',
        value: ['http://localhost:3000', 'http://localhost:3001'],
        type: 'array',
        category: this.category,
        description: 'Allowed CORS origins'
      },
      maxRequestSize: {
        key: 'max_request_size',
        value: 52428800, // 50MB in bytes
        type: 'number',
        category: this.category,
        description: 'Maximum request size in bytes'
      },
      requestTimeout: {
        key: 'request_timeout',
        value: 30000, // 30 seconds in milliseconds
        type: 'number',
        category: this.category,
        description: 'Request timeout in milliseconds'
      }
    };
  }

  async validateSetting(key: string, value: any): Promise<boolean> {
    switch (key) {
      case 'debug_mode':
      case 'cache_enabled':
      case 'rate_limit_enabled':
      case 'backup_enabled':
      case 'performance_monitoring':
      case 'error_reporting':
      case 'analytics_enabled':
      case 'compression_enabled':
      case 'cors_enabled':
        return typeof value === 'boolean';
      case 'log_level':
        return typeof value === 'string' && ['error', 'warn', 'info', 'debug'].includes(value);
      case 'backup_frequency':
        return typeof value === 'string' && ['hourly', 'daily', 'weekly', 'monthly'].includes(value);
      case 'cache_ttl':
      case 'rate_limit_requests':
      case 'rate_limit_window':
      case 'backup_retention_days':
      case 'max_request_size':
      case 'request_timeout':
        return typeof value === 'number' && value > 0;
      case 'google_analytics_id':
        return typeof value === 'string';
      case 'allowed_origins':
        return Array.isArray(value) && value.every(origin => typeof origin === 'string');
      default:
        return true;
    }
  }
}
