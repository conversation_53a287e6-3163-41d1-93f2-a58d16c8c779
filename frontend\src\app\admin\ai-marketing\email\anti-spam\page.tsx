'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import {
  Shield,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Mail,
  Server,
  Eye,
  TrendingUp,
  Settings,
  FileText,
  Users,
  BarChart3,
  RefreshCw,
  Download,
  Upload,
  Zap,
  Clock,
  Target
} from 'lucide-react';

interface SpamScore {
  score: number;
  status: 'excellent' | 'good' | 'warning' | 'danger';
  factors: {
    spf: boolean;
    dkim: boolean;
    dmarc: boolean;
    reputation: number;
    content: number;
    authentication: boolean;
  };
}

interface DeliverabilityMetrics {
  deliveryRate: number;
  openRate: number;
  clickRate: number;
  bounceRate: number;
  spamRate: number;
  unsubscribeRate: number;
}

export default function AntiSpamPage() {
  const [loading, setLoading] = useState(true);
  const [spamScore, setSpamScore] = useState<SpamScore | null>(null);
  const [metrics, setMetrics] = useState<DeliverabilityMetrics | null>(null);
  const [testingEmail, setTestingEmail] = useState(false);
  const [notification, setNotification] = useState<{
    show: boolean;
    message: string;
    type: 'success' | 'error' | 'info';
  }>({ show: false, message: '', type: 'info' });

  useEffect(() => {
    fetchSpamData();
  }, []);

  const showNotification = (message: string, type: 'success' | 'error' | 'info') => {
    setNotification({ show: true, message, type });
    setTimeout(() => {
      setNotification({ show: false, message: '', type: 'info' });
    }, 5000);
  };

  const fetchSpamData = async () => {
    setLoading(true);
    try {
      // Mock data - gerçek implementasyonda API'den çekilecek
      const mockSpamScore: SpamScore = {
        score: 8.7,
        status: 'excellent',
        factors: {
          spf: true,
          dkim: true,
          dmarc: true,
          reputation: 9.2,
          content: 8.5,
          authentication: true
        }
      };

      const mockMetrics: DeliverabilityMetrics = {
        deliveryRate: 98.5,
        openRate: 24.3,
        clickRate: 3.8,
        bounceRate: 1.2,
        spamRate: 0.3,
        unsubscribeRate: 0.8
      };

      setSpamScore(mockSpamScore);
      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Error fetching spam data:', error);
      showNotification('Veri yüklenirken bir hata oluştu.', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleTestEmail = async () => {
    setTestingEmail(true);
    try {
      // Mock email test
      await new Promise(resolve => setTimeout(resolve, 3000));
      showNotification('Test email başarıyla gönderildi ve analiz edildi', 'success');
      // Refresh data after test
      await fetchSpamData();
    } catch (error) {
      console.error('Email test error:', error);
      showNotification('Test email gönderilirken bir hata oluştu.', 'error');
    } finally {
      setTestingEmail(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600';
    if (score >= 6) return 'text-yellow-600';
    if (score >= 4) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreBadge = (status: string) => {
    switch (status) {
      case 'excellent':
        return <Badge className="bg-green-100 text-green-800">Mükemmel</Badge>;
      case 'good':
        return <Badge className="bg-blue-100 text-blue-800">İyi</Badge>;
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800">Dikkat</Badge>;
      case 'danger':
        return <Badge className="bg-red-100 text-red-800">Tehlike</Badge>;
      default:
        return <Badge variant="secondary">Bilinmiyor</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2" />
          <p>Spam analizi yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Email Spam Önleme</h1>
          <p className="text-gray-600">Email deliverability ve spam skorunu optimize edin</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={fetchSpamData}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
          <Button
            onClick={handleTestEmail}
            disabled={testingEmail}
          >
            {testingEmail ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Test Ediliyor...
              </>
            ) : (
              <>
                <Mail className="w-4 h-4 mr-2" />
                Test Email Gönder
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Spam Score Overview */}
      {spamScore && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="md:col-span-2">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Spam Skoru</p>
                  <div className="flex items-center space-x-2">
                    <p className={`text-4xl font-bold ${getScoreColor(spamScore.score)}`}>
                      {spamScore.score}/10
                    </p>
                    {getScoreBadge(spamScore.status)}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Yüksek skor = Düşük spam riski
                  </p>
                </div>
                <Shield className={`w-16 h-16 ${getScoreColor(spamScore.score)}`} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Reputation</p>
                  <p className="text-2xl font-bold text-blue-600">{spamScore.factors.reputation}/10</p>
                </div>
                <TrendingUp className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">İçerik Skoru</p>
                  <p className="text-2xl font-bold text-purple-600">{spamScore.factors.content}/10</p>
                </div>
                <FileText className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
          <TabsTrigger value="authentication">Kimlik Doğrulama</TabsTrigger>
          <TabsTrigger value="content">İçerik Analizi</TabsTrigger>
          <TabsTrigger value="reputation">Reputation</TabsTrigger>
          <TabsTrigger value="settings">Ayarlar</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Deliverability Metrics */}
          {metrics && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Teslimat Metrikleri</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>Teslimat Oranı</span>
                    <span className="font-semibold text-green-600">{metrics.deliveryRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Açılma Oranı</span>
                    <span className="font-semibold text-blue-600">{metrics.openRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tıklama Oranı</span>
                    <span className="font-semibold text-purple-600">{metrics.clickRate}%</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Problem Metrikleri</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>Bounce Oranı</span>
                    <span className="font-semibold text-orange-600">{metrics.bounceRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Spam Oranı</span>
                    <span className="font-semibold text-red-600">{metrics.spamRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Unsubscribe Oranı</span>
                    <span className="font-semibold text-gray-600">{metrics.unsubscribeRate}%</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Öneriler</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                      <span>SPF kaydı aktif</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                      <span>DKIM imzası aktif</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <AlertTriangle className="w-4 h-4 text-yellow-600 mr-2" />
                      <span>Gönderim hızını azaltın</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="authentication" className="space-y-4">
          {/* DNS Records */}
          <Card>
            <CardHeader>
              <CardTitle>DNS Kimlik Doğrulama Kayıtları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {spamScore && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${spamScore.factors.spf ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <div>
                        <p className="font-medium">SPF (Sender Policy Framework)</p>
                        <p className="text-sm text-gray-600">Gönderen sunucu doğrulaması</p>
                      </div>
                    </div>
                    {spamScore.factors.spf ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${spamScore.factors.dkim ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <div>
                        <p className="font-medium">DKIM (DomainKeys Identified Mail)</p>
                        <p className="text-sm text-gray-600">Email imzalama ve doğrulama</p>
                      </div>
                    </div>
                    {spamScore.factors.dkim ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${spamScore.factors.dmarc ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <div>
                        <p className="font-medium">DMARC (Domain-based Message Authentication)</p>
                        <p className="text-sm text-gray-600">Email politikası ve raporlama</p>
                      </div>
                    </div>
                    {spamScore.factors.dmarc ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                  </div>
                </div>
              )}

              <div className="mt-6">
                <h4 className="font-medium mb-3">DNS Kayıt Örnekleri</h4>
                <div className="space-y-3">
                  <div className="bg-gray-50 p-3 rounded font-mono text-sm">
                    <p className="text-gray-600 mb-1">SPF Kaydı:</p>
                    <p>v=spf1 include:_spf.google.com include:mailgun.org ~all</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded font-mono text-sm">
                    <p className="text-gray-600 mb-1">DKIM Kaydı:</p>
                    <p>v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA...</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded font-mono text-sm">
                    <p className="text-gray-600 mb-1">DMARC Kaydı:</p>
                    <p>v=DMARC1; p=quarantine; rua=mailto:<EMAIL></p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          {/* Content Analysis */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>İçerik Analizi</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Spam Trigger Kelimeler</span>
                    <Badge className="bg-green-100 text-green-800">Temiz</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>HTML/Text Oranı</span>
                    <Badge className="bg-blue-100 text-blue-800">Optimal</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Link Sayısı</span>
                    <Badge className="bg-yellow-100 text-yellow-800">Orta</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Görsel/Metin Dengesi</span>
                    <Badge className="bg-green-100 text-green-800">İyi</Badge>
                  </div>
                </div>

                <div className="mt-4">
                  <h5 className="font-medium mb-2">Öneriler</h5>
                  <ul className="text-sm space-y-1">
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                      Konu satırında büyük harf kullanımını azaltın
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                      Alt text'leri ekleyin
                    </li>
                    <li className="flex items-center">
                      <AlertTriangle className="w-4 h-4 text-yellow-600 mr-2" />
                      Link sayısını 3-5 arasında tutun
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Spam Trigger Kelimeler</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <h5 className="font-medium text-red-600 mb-2">Kaçınılması Gerekenler</h5>
                    <div className="flex flex-wrap gap-1">
                      {['ÜCRETSİZ', 'HEMEN', 'PARA KAZAN', 'GARANTİ', 'ACELE', 'SINIRSIZ'].map((word) => (
                        <Badge key={word} variant="destructive" className="text-xs">{word}</Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h5 className="font-medium text-green-600 mb-2">Güvenli Alternatifler</h5>
                    <div className="flex flex-wrap gap-1">
                      {['Özel Teklif', 'Sınırlı Süre', 'Kaliteli', 'Profesyonel', 'Güvenilir'].map((word) => (
                        <Badge key={word} className="bg-green-100 text-green-800 text-xs">{word}</Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Content Testing */}
          <Card>
            <CardHeader>
              <CardTitle>İçerik Test Aracı</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Email İçeriğini Test Edin</label>
                <textarea
                  className="w-full h-32 border rounded px-3 py-2"
                  placeholder="Email içeriğinizi buraya yapıştırın..."
                />
              </div>
              <Button>
                <Eye className="w-4 h-4 mr-2" />
                İçeriği Analiz Et
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reputation" className="space-y-4">
          {/* IP Reputation */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>IP Reputation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-green-600 mb-2">
                    {spamScore?.factors.reputation}/10
                  </div>
                  <Badge className="bg-green-100 text-green-800">Mükemmel Reputation</Badge>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Blacklist Durumu</span>
                    <Badge className="bg-green-100 text-green-800">Temiz</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Gönderim Hacmi</span>
                    <Badge className="bg-blue-100 text-blue-800">Normal</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Şikayet Oranı</span>
                    <Badge className="bg-green-100 text-green-800">Düşük</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Warm-up Süreci</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>Günlük Limit</span>
                    <span className="font-semibold">1,000 email</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Mevcut Hacim</span>
                    <span className="font-semibold text-blue-600">750 email</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                  </div>
                </div>

                <div className="mt-4">
                  <h5 className="font-medium mb-2">Warm-up Planı</h5>
                  <div className="text-sm space-y-1">
                    <p>• 1. Hafta: 50-100 email/gün</p>
                    <p>• 2. Hafta: 200-500 email/gün</p>
                    <p>• 3. Hafta: 1,000+ email/gün</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          {/* Anti-Spam Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Spam Önleme Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Gönderim Hızı Limiti</label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="slow">Yavaş (10 email/dakika)</option>
                    <option value="medium">Orta (50 email/dakika)</option>
                    <option value="fast">Hızlı (100 email/dakika)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Bounce Handling</label>
                  <select className="w-full border rounded px-3 py-2">
                    <option value="auto">Otomatik</option>
                    <option value="manual">Manuel</option>
                    <option value="disabled">Devre Dışı</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">List Hygiene</label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="auto-clean" defaultChecked />
                      <label htmlFor="auto-clean" className="text-sm">Otomatik liste temizleme</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="double-optin" defaultChecked />
                      <label htmlFor="double-optin" className="text-sm">Double opt-in zorunlu</label>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Monitoring</label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="real-time" defaultChecked />
                      <label htmlFor="real-time" className="text-sm">Gerçek zamanlı izleme</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="alerts" defaultChecked />
                      <label htmlFor="alerts" className="text-sm">Spam uyarıları</label>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <Button variant="outline">Varsayılana Sıfırla</Button>
                <Button>Ayarları Kaydet</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Notification Toast */}
      {notification.show && (
        <div className={`fixed top-4 right-4 z-[10000] p-4 rounded-lg shadow-lg transition-all duration-300 ${
          notification.type === 'success' ? 'bg-green-500 text-white' :
          notification.type === 'error' ? 'bg-red-500 text-white' :
          'bg-blue-500 text-white'
        }`}>
          <div className="flex items-center space-x-2">
            {notification.type === 'success' && <CheckCircle className="w-5 h-5" />}
            {notification.type === 'error' && <XCircle className="w-5 h-5" />}
            {notification.type === 'info' && <Clock className="w-5 h-5" />}
            <span>{notification.message}</span>
            <button
              onClick={() => setNotification({ show: false, message: '', type: 'info' })}
              className="ml-2 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
