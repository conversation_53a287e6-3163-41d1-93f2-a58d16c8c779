'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  X,
  Save,
  Settings,
  Mail,
  Users,
  Clock,
  Globe
} from 'lucide-react';

interface CountryEmailList {
  id: string;
  countryCode: string;
  countryName: string;
  flag: string;
  totalSubscribers: number;
  activeSubscribers: number;
  lastUpdated: Date;
  segments: number;
}

interface EditCountryModalProps {
  country: CountryEmailList;
  onClose: () => void;
  onSave: (updatedCountry: CountryEmailList) => void;
}

export default function EditCountryModal({ country, onClose, onSave }: EditCountryModalProps) {
  const [formData, setFormData] = useState({
    countryName: country.countryName,
    emailFrequency: 'weekly',
    autoSegmentation: true,
    maxEmailsPerDay: 5,
    contentLanguage: 'tr',
    timezone: 'Europe/Istanbul',
    gdprCompliance: true,
    customSettings: {
      welcomeEmailEnabled: true,
      unsubscribeReminderEnabled: true,
      engagementTracking: true,
      personalizedContent: true
    },
    notes: ''
  });

  const [saving, setSaving] = useState(false);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCustomSettingChange = (setting: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      customSettings: {
        ...prev.customSettings,
        [setting]: value
      }
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Mock save operation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedCountry: CountryEmailList = {
        ...country,
        countryName: formData.countryName,
        lastUpdated: new Date()
      };
      
      onSave(updatedCountry);
    } catch (error) {
      console.error('Error saving country settings:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">{country.flag}</span>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Liste Ayarlarını Düzenle</h2>
              <p className="text-gray-600">{country.countryName}</p>
            </div>
          </div>
          <Button variant="ghost" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>

        <div className="p-6 space-y-6">
          {/* Basic Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                Temel Ayarlar
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ülke Adı
                </label>
                <Input
                  value={formData.countryName}
                  onChange={(e) => handleInputChange('countryName', e.target.value)}
                  placeholder="Ülke adını girin"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Sıklığı
                  </label>
                  <select 
                    value={formData.emailFrequency}
                    onChange={(e) => handleInputChange('emailFrequency', e.target.value)}
                    className="w-full border rounded px-3 py-2"
                  >
                    <option value="daily">Günlük</option>
                    <option value="weekly">Haftalık</option>
                    <option value="monthly">Aylık</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Günlük Maksimum Email
                  </label>
                  <Input
                    type="number"
                    value={formData.maxEmailsPerDay}
                    onChange={(e) => handleInputChange('maxEmailsPerDay', parseInt(e.target.value))}
                    min="1"
                    max="20"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    İçerik Dili
                  </label>
                  <select 
                    value={formData.contentLanguage}
                    onChange={(e) => handleInputChange('contentLanguage', e.target.value)}
                    className="w-full border rounded px-3 py-2"
                  >
                    <option value="tr">Türkçe</option>
                    <option value="en">English</option>
                    <option value="de">Deutsch</option>
                    <option value="fr">Français</option>
                    <option value="it">Italiano</option>
                    <option value="es">Español</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Saat Dilimi
                  </label>
                  <select 
                    value={formData.timezone}
                    onChange={(e) => handleInputChange('timezone', e.target.value)}
                    className="w-full border rounded px-3 py-2"
                  >
                    <option value="Europe/Istanbul">Europe/Istanbul</option>
                    <option value="America/New_York">America/New_York</option>
                    <option value="Europe/Berlin">Europe/Berlin</option>
                    <option value="Europe/Paris">Europe/Paris</option>
                    <option value="Europe/Rome">Europe/Rome</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Advanced Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="w-5 h-5 mr-2" />
                Gelişmiş Email Ayarları
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Otomatik Segmentasyon</p>
                    <p className="text-sm text-gray-500">Aboneleri otomatik olarak segmentlere ayır</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={formData.autoSegmentation}
                    onChange={(e) => handleInputChange('autoSegmentation', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Hoş Geldin Emaili</p>
                    <p className="text-sm text-gray-500">Yeni abonelere hoş geldin emaili gönder</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={formData.customSettings.welcomeEmailEnabled}
                    onChange={(e) => handleCustomSettingChange('welcomeEmailEnabled', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Abonelik İptal Hatırlatması</p>
                    <p className="text-sm text-gray-500">Pasif abonelere hatırlatma gönder</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={formData.customSettings.unsubscribeReminderEnabled}
                    onChange={(e) => handleCustomSettingChange('unsubscribeReminderEnabled', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Etkileşim Takibi</p>
                    <p className="text-sm text-gray-500">Email açılma ve tıklama takibi</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={formData.customSettings.engagementTracking}
                    onChange={(e) => handleCustomSettingChange('engagementTracking', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Kişiselleştirilmiş İçerik</p>
                    <p className="text-sm text-gray-500">AI ile kişiselleştirilmiş email içeriği</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={formData.customSettings.personalizedContent}
                    onChange={(e) => handleCustomSettingChange('personalizedContent', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">GDPR Uyumluluğu</p>
                    <p className="text-sm text-gray-500">GDPR/KVKK veri koruma uyumluluğu</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={formData.gdprCompliance}
                    onChange={(e) => handleInputChange('gdprCompliance', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Notlar</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Bu ülke listesi hakkında notlarınızı yazın..."
                rows={3}
              />
            </CardContent>
          </Card>

          {/* Current Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Mevcut İstatistikler
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-blue-600">{country.totalSubscribers.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Toplam Abone</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-600">{country.activeSubscribers.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Aktif Abone</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-600">{country.segments}</p>
                  <p className="text-sm text-gray-600">Segment Sayısı</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              İptal
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              {saving ? (
                <>
                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Kaydet
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
