import React from 'react';
import { 
  BanknotesIcon,
  ShoppingCartIcon,
  TruckIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  SpeakerWaveIcon
} from '@heroicons/react/24/outline';
import { useNotifications } from '../../contexts/NotificationContext';
import { Notification } from '../../services/websocketService';

interface NotificationItemProps {
  notification: Notification;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification }) => {
  const { markAsRead } = useNotifications();

  const getNotificationIcon = (type: string, priority: string) => {
    const iconClass = "h-6 w-6";
    
    switch (type) {
      case 'PAYMENT_RECEIVED':
        return <BanknotesIcon className={`${iconClass} text-green-600`} />;
      case 'ORDER_CONFIRMED':
        return <ShoppingCartIcon className={`${iconClass} text-blue-600`} />;
      case 'ORDER_SHIPPED':
        return <TruckIcon className={`${iconClass} text-orange-600`} />;
      case 'ORDER_DELIVERED':
        return <CheckCircleIcon className={`${iconClass} text-green-600`} />;
      case 'SYSTEM_ANNOUNCEMENT':
        return <SpeakerWaveIcon className={`${iconClass} text-purple-600`} />;
      default:
        if (priority === 'urgent') {
          return <ExclamationTriangleIcon className={`${iconClass} text-red-600`} />;
        }
        return <InformationCircleIcon className={`${iconClass} text-gray-600`} />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500 bg-red-50';
      case 'high':
        return 'border-l-orange-500 bg-orange-50';
      case 'medium':
        return 'border-l-blue-500 bg-blue-50';
      default:
        return 'border-l-gray-300 bg-gray-50';
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Şimdi';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} dakika önce`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} saat önce`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} gün önce`;
    }
  };

  const handleClick = () => {
    // Mark as read if unread
    if (!(notification as any).isRead) {
      markAsRead(notification.id);
    }

    // Handle navigation based on notification data
    if (notification.data?.relatedEntityType && notification.data?.relatedEntityId) {
      handleNavigation();
    }
  };

  const handleNavigation = () => {
    const { relatedEntityType, relatedEntityId } = notification.data || {};
    
    switch (relatedEntityType) {
      case 'order':
        window.location.href = `/orders/${relatedEntityId}`;
        break;
      case 'quote':
        window.location.href = `/quotes/${relatedEntityId}`;
        break;
      case 'escrow':
        window.location.href = `/escrow/${relatedEntityId}`;
        break;
      default:
        // No specific navigation
        break;
    }
  };

  const isUnread = !(notification as any).isRead;

  return (
    <div
      onClick={handleClick}
      className={`
        p-4 border-l-4 cursor-pointer transition-all duration-200 hover:bg-gray-50
        ${isUnread ? getPriorityColor(notification.priority) : 'border-l-gray-200 bg-white'}
        ${isUnread ? 'font-medium' : 'font-normal'}
      `}
    >
      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className="flex-shrink-0 mt-1">
          {getNotificationIcon(notification.type, notification.priority)}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <p className={`text-sm ${isUnread ? 'text-gray-900' : 'text-gray-700'}`}>
                {notification.title}
              </p>
              <p className={`text-sm mt-1 ${isUnread ? 'text-gray-700' : 'text-gray-500'}`}>
                {notification.message}
              </p>
              
              {/* Additional data display */}
              {notification.data?.amount && (
                <div className="mt-2 text-xs text-gray-600">
                  <span className="bg-gray-100 px-2 py-1 rounded">
                    {notification.data.amount} {notification.data.currency || 'TRY'}
                  </span>
                </div>
              )}
              
              {notification.data?.orderNumber && (
                <div className="mt-2 text-xs text-gray-600">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    Sipariş: {notification.data.orderNumber}
                  </span>
                </div>
              )}
            </div>

            {/* Time and unread indicator */}
            <div className="flex flex-col items-end space-y-1">
              <span className="text-xs text-gray-500">
                {formatTime(notification.createdAt)}
              </span>
              
              {isUnread && (
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Priority indicator for urgent notifications */}
      {notification.priority === 'urgent' && (
        <div className="mt-2 flex items-center space-x-1">
          <ExclamationTriangleIcon className="h-4 w-4 text-red-600" />
          <span className="text-xs text-red-600 font-medium">Acil</span>
        </div>
      )}
    </div>
  );
};

export default NotificationItem;
