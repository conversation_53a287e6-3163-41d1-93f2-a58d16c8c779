'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from './card'
import { But<PERSON> } from './button'
import { Badge } from './badge'
import { 
  CreditCard,
  Calendar,
  DollarSign,
  Banknote,
  Building,
  FileText,
  Info,
  AlertTriangle
} from 'lucide-react'

interface PaymentStepProps {
  formData: any
  setFormData: (data: any) => void
}

export function PaymentStep({ formData, setFormData }: PaymentStepProps) {
  const [paymentData, setPaymentData] = React.useState({
    paymentMethod: 'bank-transfer',
    advancePayment: {
      percentage: 50,
      amount: 0,
      dueDate: '',
      bankDetails: ''
    },
    remainingPayment: {
      amount: 0,
      dueDate: '',
      condition: 'on-delivery'
    },
    paymentInstructions: '',
    bankAccount: {
      bankName: 'Türkiye İş Bankası',
      accountName: 'Şirket Adı A.Ş.',
      accountNumber: 'TR33 0006 4000 0011 2345 6789 01',
      iban: 'TR33 0006 4000 0011 2345 6789 01',
      swiftCode: 'ISBKTRIS'
    },
    ...formData.payment
  })

  const totalAmount = formData.pricing?.totalAmount || 0

  // Calculate payment amounts when percentage or total changes
  React.useEffect(() => {
    const advanceAmount = (totalAmount * paymentData.advancePayment.percentage) / 100
    const remainingAmount = totalAmount - advanceAmount

    const updatedPayment = {
      ...paymentData,
      advancePayment: {
        ...paymentData.advancePayment,
        amount: advanceAmount
      },
      remainingPayment: {
        ...paymentData.remainingPayment,
        amount: remainingAmount
      }
    }

    setPaymentData(updatedPayment)
    setFormData({ ...formData, payment: updatedPayment })
  }, [totalAmount, paymentData.advancePayment.percentage])

  const updatePayment = (field: string, value: any) => {
    const updatedPayment = { ...paymentData, [field]: value }
    setPaymentData(updatedPayment)
    setFormData({ ...formData, payment: updatedPayment })
  }

  const updateAdvancePayment = (field: string, value: any) => {
    const updatedAdvance = { ...paymentData.advancePayment, [field]: value }
    const updatedPayment = { ...paymentData, advancePayment: updatedAdvance }
    
    // Recalculate amounts if percentage changed
    if (field === 'percentage') {
      const advanceAmount = (totalAmount * value) / 100
      const remainingAmount = totalAmount - advanceAmount
      updatedAdvance.amount = advanceAmount
      updatedPayment.remainingPayment = {
        ...paymentData.remainingPayment,
        amount: remainingAmount
      }
    }
    
    setPaymentData(updatedPayment)
    setFormData({ ...formData, payment: updatedPayment })
  }

  const updateRemainingPayment = (field: string, value: any) => {
    const updatedRemaining = { ...paymentData.remainingPayment, [field]: value }
    const updatedPayment = { ...paymentData, remainingPayment: updatedRemaining }
    setPaymentData(updatedPayment)
    setFormData({ ...formData, payment: updatedPayment })
  }

  const updateBankAccount = (field: string, value: string) => {
    const updatedBank = { ...paymentData.bankAccount, [field]: value }
    const updatedPayment = { ...paymentData, bankAccount: updatedBank }
    setPaymentData(updatedPayment)
    setFormData({ ...formData, payment: updatedPayment })
  }

  const formatCurrency = (amount: number) => {
    const currency = formData.pricing?.currency || 'USD'
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount)
  }

  const paymentMethods = [
    { 
      value: 'bank-transfer', 
      label: 'Banka Havalesi', 
      icon: Building,
      description: 'Banka hesabına havale/EFT'
    },
    { 
      value: 'credit-card', 
      label: 'Kredi Kartı', 
      icon: CreditCard,
      description: 'Kredi kartı ile ödeme'
    },
    { 
      value: 'cash', 
      label: 'Nakit', 
      icon: Banknote,
      description: 'Nakit ödeme'
    },
    { 
      value: 'check', 
      label: 'Çek', 
      icon: FileText,
      description: 'Çek ile ödeme'
    }
  ]

  const remainingPaymentConditions = [
    { value: 'on-delivery', label: 'Teslimatta', description: 'Ürün teslim edildiğinde' },
    { value: 'before-delivery', label: 'Teslimattan Önce', description: 'Ürün hazır olduğunda' },
    { value: 'after-delivery', label: 'Teslimattan Sonra', description: 'Belirtilen vade ile' },
    { value: 'custom', label: 'Özel Koşul', description: 'Özel ödeme koşulu' }
  ]

  if (!formData.pricing || !totalAmount) {
    return (
      <div className="text-center py-12">
        <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Ödeme Ayarları</h3>
        <p className="text-gray-600">Ödeme ayarları yapabilmek için önce fiyatlandırmayı tamamlayın.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Payment Method Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Ödeme Yöntemi
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {paymentMethods.map(method => {
              const Icon = method.icon
              return (
                <div
                  key={method.value}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    paymentData.paymentMethod === method.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => updatePayment('paymentMethod', method.value)}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <Icon className="w-5 h-5 text-gray-600" />
                    <h4 className="font-medium text-gray-900">{method.label}</h4>
                  </div>
                  <p className="text-sm text-gray-600">{method.description}</p>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Payment Schedule */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Ödeme Planı
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Advance Payment */}
          <div className="border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-4 flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Ön Ödeme
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Ön Ödeme Oranı (%)</label>
                <div className="flex items-center gap-2">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    step="5"
                    value={paymentData.advancePayment.percentage}
                    onChange={(e) => updateAdvancePayment('percentage', parseInt(e.target.value))}
                    className="flex-1"
                  />
                  <span className="w-12 text-sm font-medium">{paymentData.advancePayment.percentage}%</span>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Ön Ödeme Tutarı</label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md">
                  <span className="font-semibold text-green-600">
                    {formatCurrency(paymentData.advancePayment.amount)}
                  </span>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Ön Ödeme Tarihi</label>
                <input
                  type="date"
                  value={paymentData.advancePayment.dueDate}
                  onChange={(e) => updateAdvancePayment('dueDate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Remaining Payment */}
          <div className="border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-4 flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Kalan Ödeme
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Kalan Tutar</label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md">
                  <span className="font-semibold text-blue-600">
                    {formatCurrency(paymentData.remainingPayment.amount)}
                  </span>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Ödeme Koşulu</label>
                <select
                  value={paymentData.remainingPayment.condition}
                  onChange={(e) => updateRemainingPayment('condition', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {remainingPaymentConditions.map(condition => (
                    <option key={condition.value} value={condition.value}>
                      {condition.label}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  {remainingPaymentConditions.find(c => c.value === paymentData.remainingPayment.condition)?.description}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {paymentData.remainingPayment.condition === 'after-delivery' ? 'Vade Tarihi' : 'Tahmini Tarih'}
                </label>
                <input
                  type="date"
                  value={paymentData.remainingPayment.dueDate}
                  onChange={(e) => updateRemainingPayment('dueDate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bank Account Details (if bank transfer selected) */}
      {paymentData.paymentMethod === 'bank-transfer' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="w-5 h-5" />
              Banka Hesap Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Banka Adı</label>
                <input
                  type="text"
                  value={paymentData.bankAccount.bankName}
                  onChange={(e) => updateBankAccount('bankName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Banka adı"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Hesap Sahibi</label>
                <input
                  type="text"
                  value={paymentData.bankAccount.accountName}
                  onChange={(e) => updateBankAccount('accountName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Hesap sahibi adı"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">IBAN</label>
                <input
                  type="text"
                  value={paymentData.bankAccount.iban}
                  onChange={(e) => updateBankAccount('iban', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="TR33 0006 4000 0011 2345 6789 01"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">SWIFT Kodu</label>
                <input
                  type="text"
                  value={paymentData.bankAccount.swiftCode}
                  onChange={(e) => updateBankAccount('swiftCode', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="SWIFT kodu"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Payment Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Ödeme Talimatları
          </CardTitle>
        </CardHeader>
        <CardContent>
          <textarea
            value={paymentData.paymentInstructions}
            onChange={(e) => updatePayment('paymentInstructions', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Ödeme ile ilgili özel talimatlar, notlar veya koşullar..."
          />
        </CardContent>
      </Card>

      {/* Payment Summary */}
      <Card className="bg-green-50 border-green-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Info className="w-5 h-5 text-green-600" />
              <span className="font-medium text-green-900">Ödeme Özeti</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-green-700">Ön Ödeme ({paymentData.advancePayment.percentage}%):</span>
              <span className="font-semibold text-green-900">
                {formatCurrency(paymentData.advancePayment.amount)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-green-700">Kalan Ödeme:</span>
              <span className="font-semibold text-green-900">
                {formatCurrency(paymentData.remainingPayment.amount)}
              </span>
            </div>
            <hr className="border-green-300" />
            <div className="flex justify-between items-center text-lg">
              <span className="font-bold text-green-900">Toplam:</span>
              <span className="font-bold text-green-900">
                {formatCurrency(totalAmount)}
              </span>
            </div>
          </div>
          
          <div className="mt-3 text-sm text-green-700">
            <p>
              Ödeme Yöntemi: {paymentMethods.find(m => m.value === paymentData.paymentMethod)?.label}
            </p>
            {paymentData.advancePayment.dueDate && (
              <p>Ön Ödeme Tarihi: {new Date(paymentData.advancePayment.dueDate).toLocaleDateString('tr-TR')}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Warning */}
      {paymentData.advancePayment.percentage < 30 && (
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
              <span className="font-medium text-yellow-900">Uyarı</span>
            </div>
            <p className="text-yellow-800 text-sm mt-1">
              Ön ödeme oranı %30'un altında. Risk yönetimi açısından daha yüksek bir ön ödeme oranı önerilir.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
