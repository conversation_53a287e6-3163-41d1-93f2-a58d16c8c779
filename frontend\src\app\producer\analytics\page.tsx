'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { DetailedReportModal } from '@/components/ui/detailed-report-modal'
import { AnalyticsFilterModal } from '@/components/ui/analytics-filter-modal'
import { PredictionModelModal } from '@/components/ui/prediction-model-modal'
import { ComparisonAnalysisModal } from '@/components/ui/comparison-analysis-modal'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Package,
  Users,
  Calendar,
  Download,
  Filter,
  Eye,
  Target,
  Award,
  Globe,
  AlertTriangle
} from 'lucide-react'
import { useRouter } from 'next/navigation'

// Mock data temizlendi - gerçek veriler API'den gelecek
export default function ProducerAnalytics() {
  const { producer } = useProducerAuth()
  const router = useRouter()

  // Onay kontrolü
  if (!producer?.isApproved) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="p-6 max-w-md mx-auto text-center">
          <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Hesap Onayı Bekleniyor</h2>
          <p className="text-gray-600 mb-6">
            Bu sayfaya erişebilmek için hesabınızın admin tarafından onaylanması gerekiyor.
          </p>
          <div className="flex justify-center gap-4">
            <Button
              onClick={() => router.push('/producer/settings')}
              className="bg-amber-500 text-white hover:bg-amber-600"
            >
              Ayarları Düzenle
            </Button>
            <Button
              onClick={() => router.push('/producer/dashboard')}
              variant="outline"
            >
              Dashboard'a Dön
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  const [selectedPeriod, setSelectedPeriod] = React.useState('6months')
  const [selectedMetric, setSelectedMetric] = React.useState('revenue')
  const [isReportModalOpen, setIsReportModalOpen] = React.useState(false)
  const [isFilterModalOpen, setIsFilterModalOpen] = React.useState(false)
  const [isPredictionModalOpen, setIsPredictionModalOpen] = React.useState(false)
  const [isComparisonModalOpen, setIsComparisonModalOpen] = React.useState(false)

  const periods = [
    { id: '1month', label: 'Son 1 Ay' },
    { id: '3months', label: 'Son 3 Ay' },
    { id: '6months', label: 'Son 6 Ay' },
    { id: '1year', label: 'Son 1 Yıl' }
  ]

  const metrics = [
    { id: 'revenue', label: 'Gelir', icon: DollarSign },
    { id: 'orders', label: 'Sipariş Sayısı', icon: Package },
    { id: 'customers', label: 'Müşteri Sayısı', icon: Users }
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  // Modal handlers
  const handleDetailedReport = () => {
    setIsReportModalOpen(true)
  }

  const handleFilter = () => {
    setIsFilterModalOpen(true)
  }

  const handlePredictionModel = () => {
    setIsPredictionModalOpen(true)
  }

  const handleComparison = () => {
    setIsComparisonModalOpen(true)
  }

  const handleGenerateReport = async (reportConfig: any) => {
    try {
      console.log('Generating report with config:', reportConfig)
      alert('Detaylı rapor başarıyla oluşturuldu ve indirildi!')
      return true
    } catch (error) {
      console.error('Error generating report:', error)
      alert('Rapor oluşturulurken hata oluştu.')
      return false
    }
  }

  const handleApplyFilters = async (filters: any) => {
    try {
      console.log('Applying filters:', filters)
      alert('Filtreler başarıyla uygulandı!')
      return true
    } catch (error) {
      console.error('Error applying filters:', error)
      alert('Filtreler uygulanırken hata oluştu.')
      return false
    }
  }

  const handleGeneratePrediction = async (predictionConfig: any) => {
    try {
      console.log('Generating prediction with config:', predictionConfig)
      alert('Tahmin modeli başarıyla oluşturuldu!')
      return true
    } catch (error) {
      console.error('Error generating prediction:', error)
      alert('Tahmin modeli oluşturulurken hata oluştu.')
      return false
    }
  }

  const handleGenerateComparison = async (comparisonConfig: any) => {
    try {
      console.log('Generating comparison with config:', comparisonConfig)
      alert('Karşılaştırma analizi başarıyla oluşturuldu!')
      return true
    } catch (error) {
      console.error('Error generating comparison:', error)
      alert('Karşılaştırma analizi oluşturulurken hata oluştu.')
      return false
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Satış Analizleri</h1>
          <p className="text-gray-600">
            Satış performansınızı analiz edin ve trendleri takip edin
          </p>
        </div>
        
        <div className="flex gap-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
          >
            {periods.map(period => (
              <option key={period.id} value={period.id}>{period.label}</option>
            ))}
          </select>
          
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Rapor İndir
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Gelir</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(mockAnalytics.overview.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="w-3 h-3 mr-1" />
                {formatPercentage(mockAnalytics.overview.growthRate)}
              </span>
              önceki döneme göre
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Sipariş</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalytics.overview.totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              Ortalama: {formatCurrency(mockAnalytics.overview.averageOrderValue)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Müşteri Sayısı</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalytics.overview.customerCount}</div>
            <p className="text-xs text-muted-foreground">
              %{mockAnalytics.trends.repeatCustomerRate} tekrar müşteri
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Müşteri Memnuniyeti</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalytics.trends.customerSatisfaction}/5</div>
            <p className="text-xs text-muted-foreground">
              Ortalama teslimat: {mockAnalytics.trends.averageDeliveryTime}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Revenue Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Aylık Gelir Trendi
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockAnalytics.monthlyRevenue.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium w-16">{item.month}</span>
                    <div className="flex-1 bg-gray-200 rounded-full h-2 w-32">
                      <div 
                        className="bg-amber-600 h-2 rounded-full"
                        style={{ width: `${(item.revenue / 125000) * 100}%` }}
                      />
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{formatCurrency(item.revenue)}</div>
                    <div className="text-xs text-gray-500">{item.orders} sipariş</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Product Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Ürün Performansı
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockAnalytics.productPerformance.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{item.product}</span>
                    <span className="text-sm text-gray-500">{item.percentage}%</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-amber-500 to-orange-500 h-2 rounded-full"
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                    <div className="text-right min-w-0">
                      <div className="text-sm font-medium">{formatCurrency(item.revenue)}</div>
                      <div className="text-xs text-gray-500">{item.orders} sipariş</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customer & Region Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Segments */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Müşteri Segmentleri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockAnalytics.customerSegments.map((segment, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">{segment.segment}</div>
                    <div className="text-sm text-gray-500">{segment.count} müşteri</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{formatCurrency(segment.revenue)}</div>
                    <div className="text-sm text-gray-500">%{segment.percentage}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Regional Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              Bölgesel Performans
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockAnalytics.regionPerformance.map((region, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">{region.region}</div>
                    <div className="text-sm text-gray-500">{region.orders} sipariş</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{formatCurrency(region.revenue)}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Insights & Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Öngörüler ve Öneriler
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="w-4 h-4 text-green-600" />
                <span className="font-medium text-green-800">Güçlü Trend</span>
              </div>
              <p className="text-sm text-green-700">
                {mockAnalytics.overview.topProduct} ürününüz en yüksek geliri getiriyor. 
                Bu kategorideki stokları artırmayı düşünün.
              </p>
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Users className="w-4 h-4 text-blue-600" />
                <span className="font-medium text-blue-800">Müşteri Analizi</span>
              </div>
              <p className="text-sm text-blue-700">
                İnşaat firmaları en büyük müşteri segmentiniz. 
                Bu segmente özel kampanyalar düzenleyebilirsiniz.
              </p>
            </div>

            <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="w-4 h-4 text-amber-600" />
                <span className="font-medium text-amber-800">Sezonsal Trend</span>
              </div>
              <p className="text-sm text-amber-700">
                {mockAnalytics.trends.seasonalTrend}. 
                Yaz ayları için kapasiteyi artırmayı planlayın.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Hızlı İşlemler</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2 hover:bg-blue-50 hover:border-blue-300"
              onClick={handleDetailedReport}
            >
              <Download className="w-6 h-6 text-blue-600" />
              <span>Detaylı Rapor</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2 hover:bg-green-50 hover:border-green-300"
              onClick={handleFilter}
            >
              <Filter className="w-6 h-6 text-green-600" />
              <span>Filtrele</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2 hover:bg-purple-50 hover:border-purple-300"
              onClick={handlePredictionModel}
            >
              <Eye className="w-6 h-6 text-purple-600" />
              <span>Tahmin Modeli</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2 hover:bg-orange-50 hover:border-orange-300"
              onClick={handleComparison}
            >
              <BarChart3 className="w-6 h-6 text-orange-600" />
              <span>Karşılaştır</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      <DetailedReportModal
        isOpen={isReportModalOpen}
        onClose={() => setIsReportModalOpen(false)}
        onGenerateReport={handleGenerateReport}
        analyticsData={{
          period: selectedPeriod,
          totalRevenue: 125000,
          totalOrders: 45,
          totalCustomers: 32,
          avgOrderValue: 2777
        }}
      />

      <AnalyticsFilterModal
        isOpen={isFilterModalOpen}
        onClose={() => setIsFilterModalOpen(false)}
        onApplyFilters={handleApplyFilters}
        currentFilters={{
          period: selectedPeriod,
          metric: selectedMetric
        }}
      />

      <PredictionModelModal
        isOpen={isPredictionModalOpen}
        onClose={() => setIsPredictionModalOpen(false)}
        onGeneratePrediction={handleGeneratePrediction}
        historicalData={{
          revenue: [15000, 18000, 22000, 25000, 28000, 32000],
          orders: [6, 7, 8, 9, 10, 11],
          customers: [4, 5, 6, 7, 8, 9]
        }}
      />

      <ComparisonAnalysisModal
        isOpen={isComparisonModalOpen}
        onClose={() => setIsComparisonModalOpen(false)}
        onGenerateComparison={handleGenerateComparison}
        availablePeriods={['1month', '3months', '6months', '1year']}
      />
    </div>
  )
}
