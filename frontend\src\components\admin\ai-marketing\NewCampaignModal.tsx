'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  X,
  Send,
  Wand2,
  Calendar,
  Users,
  Mail,
  Target,
  Clock
} from 'lucide-react';

interface CountryEmailList {
  id: string;
  countryCode: string;
  countryName: string;
  flag: string;
  totalSubscribers: number;
  activeSubscribers: number;
  lastUpdated: Date;
  segments: number;
}

interface EmailCampaign {
  id: string;
  name: string;
  subject: string;
  targetCountries: string[];
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'paused';
  scheduledFor?: Date;
  sentAt?: Date;
  recipients: number;
  openRate: number;
  clickRate: number;
  conversionRate: number;
  createdAt: Date;
}

interface NewCampaignModalProps {
  targetCountry: CountryEmailList;
  onClose: () => void;
  onSave: (newCampaign: EmailCampaign) => void;
}

export default function NewCampaignModal({ targetCountry, onClose, onSave }: NewCampaignModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    content: '',
    campaignType: 'promotional',
    scheduledFor: '',
    sendImmediately: false,
    useAI: true,
    productCategory: 'traverten',
    tone: 'professional',
    includeImages: true,
    includeDiscount: false,
    discountPercentage: 0
  });

  const [generating, setGenerating] = useState(false);
  const [saving, setSaving] = useState(false);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateAIContent = async () => {
    setGenerating(true);
    try {
      // Mock AI content generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const aiContent = {
        name: `${formData.productCategory} Özel Kampanyası - ${targetCountry.countryName}`,
        subject: `🏛️ Yeni ${formData.productCategory} koleksiyonumuz ${targetCountry.countryName} için özel fiyatlarla!`,
        content: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h1 style="color: #2563eb; text-align: center;">Yeni ${formData.productCategory} Koleksiyonu</h1>
            
            <p>Sevgili ${targetCountry.countryName} müşterimiz,</p>
            
            <p>Türkiye'nin en kaliteli doğal taş ürünleri ile projelerinizi hayata geçirmenin zamanı geldi!</p>
            
            <h2 style="color: #1f2937;">Neden Bizim ${formData.productCategory} Ürünlerimizi Seçmelisiniz?</h2>
            <ul>
              <li>✅ Premium kalite garantisi</li>
              <li>✅ Çeşitli boyut ve yüzey seçenekleri</li>
              <li>✅ Hızlı ve güvenli teslimat</li>
              <li>✅ Rekabetçi fiyatlar</li>
              <li>✅ Profesyonel teknik destek</li>
            </ul>
            
            ${formData.includeDiscount ? `
            <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
              <h3 style="color: #d97706; margin: 0;">🎉 Özel İndirim!</h3>
              <p style="margin: 10px 0; font-size: 18px; font-weight: bold;">
                Sadece ${targetCountry.countryName} müşterilerimiz için %${formData.discountPercentage} indirim!
              </p>
            </div>
            ` : ''}
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="#" style="background: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                🔍 Ürünleri İncele ve Teklif Al
              </a>
            </div>
            
            <p>Detaylı bilgi ve özel fiyat teklifi için hemen bizimle iletişime geçin!</p>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
            
            <p style="font-size: 12px; color: #6b7280; text-align: center;">
              Bu email ${targetCountry.countryName} bölgesindeki değerli müşterilerimize gönderilmiştir.<br>
              Abonelikten çıkmak için <a href="#">buraya tıklayın</a>.
            </p>
          </div>
        `
      };
      
      setFormData(prev => ({
        ...prev,
        ...aiContent
      }));
    } catch (error) {
      console.error('Error generating AI content:', error);
    } finally {
      setGenerating(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Mock save operation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newCampaign: EmailCampaign = {
        id: `campaign-${Date.now()}`,
        name: formData.name,
        subject: formData.subject,
        targetCountries: [targetCountry.countryCode],
        status: formData.sendImmediately ? 'sending' : 'scheduled',
        scheduledFor: formData.sendImmediately ? undefined : new Date(formData.scheduledFor),
        recipients: targetCountry.activeSubscribers,
        openRate: 0,
        clickRate: 0,
        conversionRate: 0,
        createdAt: new Date()
      };
      
      onSave(newCampaign);
    } catch (error) {
      console.error('Error saving campaign:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">{targetCountry.flag}</span>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Yeni Email Kampanyası</h2>
              <p className="text-gray-600">{targetCountry.countryName} - {targetCountry.activeSubscribers.toLocaleString()} aktif abone</p>
            </div>
          </div>
          <Button variant="ghost" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>

        <div className="p-6 space-y-6">
          {/* Campaign Settings */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="w-5 h-5 mr-2" />
                  Kampanya Ayarları
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Kampanya Adı
                  </label>
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Kampanya adını girin"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Konusu
                  </label>
                  <Input
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    placeholder="Email konusunu girin"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Kampanya Türü
                    </label>
                    <select 
                      value={formData.campaignType}
                      onChange={(e) => handleInputChange('campaignType', e.target.value)}
                      className="w-full border rounded px-3 py-2"
                    >
                      <option value="promotional">Promosyonel</option>
                      <option value="educational">Eğitici</option>
                      <option value="newsletter">Haber Bülteni</option>
                      <option value="announcement">Duyuru</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ürün Kategorisi
                    </label>
                    <select 
                      value={formData.productCategory}
                      onChange={(e) => handleInputChange('productCategory', e.target.value)}
                      className="w-full border rounded px-3 py-2"
                    >
                      <option value="traverten">Traverten</option>
                      <option value="mermer">Mermer</option>
                      <option value="granit">Granit</option>
                      <option value="oniks">Oniks</option>
                      <option value="plaka">Plaka Ürünleri</option>
                    </select>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">AI İçerik Üretimi</p>
                      <p className="text-sm text-gray-500">Yapay zeka ile otomatik içerik oluştur</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={formData.useAI}
                      onChange={(e) => handleInputChange('useAI', e.target.checked)}
                      className="w-4 h-4"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Görseller Dahil Et</p>
                      <p className="text-sm text-gray-500">Email'e ürün görselleri ekle</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={formData.includeImages}
                      onChange={(e) => handleInputChange('includeImages', e.target.checked)}
                      className="w-4 h-4"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">İndirim Dahil Et</p>
                      <p className="text-sm text-gray-500">Özel indirim teklifi ekle</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={formData.includeDiscount}
                      onChange={(e) => handleInputChange('includeDiscount', e.target.checked)}
                      className="w-4 h-4"
                    />
                  </div>

                  {formData.includeDiscount && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        İndirim Oranı (%)
                      </label>
                      <Input
                        type="number"
                        value={formData.discountPercentage}
                        onChange={(e) => handleInputChange('discountPercentage', parseInt(e.target.value))}
                        min="1"
                        max="50"
                        placeholder="10"
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Zamanlama
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="send-now"
                      name="timing"
                      checked={formData.sendImmediately}
                      onChange={() => handleInputChange('sendImmediately', true)}
                    />
                    <label htmlFor="send-now" className="font-medium">
                      Hemen Gönder
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="schedule"
                      name="timing"
                      checked={!formData.sendImmediately}
                      onChange={() => handleInputChange('sendImmediately', false)}
                    />
                    <label htmlFor="schedule" className="font-medium">
                      Zamanla
                    </label>
                  </div>

                  {!formData.sendImmediately && (
                    <div className="ml-6">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Gönderim Tarihi ve Saati
                      </label>
                      <Input
                        type="datetime-local"
                        value={formData.scheduledFor}
                        onChange={(e) => handleInputChange('scheduledFor', e.target.value)}
                        min={new Date().toISOString().slice(0, 16)}
                      />
                    </div>
                  )}
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">📊 Hedef Kitle</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Ülke:</span>
                      <span className="font-medium">{targetCountry.countryName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Aktif Abone:</span>
                      <span className="font-medium">{targetCountry.activeSubscribers.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Segment:</span>
                      <span className="font-medium">{targetCountry.segments} segment</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* AI Content Generation */}
          {formData.useAI && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Wand2 className="w-5 h-5 mr-2" />
                    AI İçerik Üretimi
                  </div>
                  <Button 
                    onClick={generateAIContent} 
                    disabled={generating}
                    variant="outline"
                  >
                    {generating ? (
                      <>
                        <Clock className="w-4 h-4 mr-2 animate-spin" />
                        Üretiliyor...
                      </>
                    ) : (
                      <>
                        <Wand2 className="w-4 h-4 mr-2" />
                        İçerik Üret
                      </>
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    İçerik Tonu
                  </label>
                  <select 
                    value={formData.tone}
                    onChange={(e) => handleInputChange('tone', e.target.value)}
                    className="w-full border rounded px-3 py-2 mb-4"
                  >
                    <option value="professional">Profesyonel</option>
                    <option value="friendly">Samimi</option>
                    <option value="formal">Resmi</option>
                    <option value="enthusiastic">Coşkulu</option>
                  </select>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Content Editor */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="w-5 h-5 mr-2" />
                Email İçeriği
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                placeholder="Email içeriğinizi buraya yazın veya AI ile üretin..."
                rows={12}
                className="font-mono text-sm"
              />
              {formData.content && (
                <div className="mt-4">
                  <h4 className="font-medium mb-2">Önizleme:</h4>
                  <div className="border rounded p-4 bg-gray-50 max-h-64 overflow-y-auto">
                    <div dangerouslySetInnerHTML={{ __html: formData.content }} />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              İptal
            </Button>
            <Button 
              onClick={handleSave} 
              disabled={saving || !formData.name || !formData.subject}
            >
              {saving ? (
                <>
                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  {formData.sendImmediately ? 'Gönder' : 'Zamanla'}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
