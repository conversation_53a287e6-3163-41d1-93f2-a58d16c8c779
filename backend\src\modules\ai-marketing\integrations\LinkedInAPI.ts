// LinkedIn API Integration
// LinkedIn Marketing API entegrasyonu - B2B pazarlama ve lead generation

import axios, { AxiosInstance } from 'axios';
import { EventEmitter } from 'events';

export interface LinkedInConfig {
  clientId: string;
  clientSecret: string;
  accessToken: string;
  baseUrl: string;
  timeout: number;
}

export interface LinkedInProfile {
  id: string;
  firstName: string;
  lastName: string;
  headline: string;
  location: string;
  industry: string;
  positions: Position[];
  connections: number;
  profileUrl: string;
  profilePicture?: string;
  summary?: string;
  skills: string[];
  languages: string[];
  lastUpdated: Date;
}

export interface Position {
  title: string;
  company: string;
  companyId?: string;
  location?: string;
  startDate: Date;
  endDate?: Date;
  isCurrent: boolean;
  description?: string;
}

export interface Company {
  id: string;
  name: string;
  industry: string;
  size: string;
  location: string;
  website?: string;
  description?: string;
  employeeCount: number;
  foundedYear?: number;
  specialties: string[];
  logoUrl?: string;
}

export interface LinkedInCampaign {
  id: string;
  name: string;
  status: 'ACTIVE' | 'PAUSED' | 'ARCHIVED';
  type: 'SPONSORED_CONTENT' | 'MESSAGE_AD' | 'DYNAMIC_AD';
  targeting: CampaignTargeting;
  budget: CampaignBudget;
  creatives: Creative[];
  metrics: CampaignMetrics;
  createdAt: Date;
  lastModified: Date;
}

export interface CampaignTargeting {
  geography: string[];
  industries: string[];
  jobTitles: string[];
  companySizes: string[];
  seniorityLevels: string[];
  skills: string[];
  interests: string[];
  languages: string[];
}

export interface CampaignBudget {
  dailyBudget: number;
  totalBudget: number;
  bidType: 'CPC' | 'CPM' | 'CPA';
  bidAmount: number;
}

export interface Creative {
  id: string;
  type: 'SINGLE_IMAGE' | 'VIDEO' | 'CAROUSEL' | 'TEXT_AD';
  headline: string;
  description: string;
  callToAction: string;
  imageUrl?: string;
  videoUrl?: string;
  landingPageUrl: string;
  status: 'ACTIVE' | 'PAUSED';
}

export interface CampaignMetrics {
  impressions: number;
  clicks: number;
  leads: number;
  cost: number;
  ctr: number; // Click-through rate
  cpc: number; // Cost per click
  cpl: number; // Cost per lead
  conversionRate: number;
  reach: number;
  frequency: number;
}

export interface LeadGenForm {
  id: string;
  name: string;
  headline: string;
  description: string;
  privacyPolicy: string;
  fields: FormField[];
  thankYouMessage: string;
  status: 'ACTIVE' | 'PAUSED';
}

export interface FormField {
  type: 'FIRST_NAME' | 'LAST_NAME' | 'EMAIL' | 'COMPANY' | 'JOB_TITLE' | 'PHONE' | 'CUSTOM';
  label: string;
  required: boolean;
  predefinedOptions?: string[];
}

export class LinkedInAPI extends EventEmitter {
  private client: AxiosInstance;
  private config: LinkedInConfig;
  private rateLimitDelay: number = 1000; // 1 second between requests

  constructor(config: LinkedInConfig) {
    super();
    this.config = config;
    this.client = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: {
        'Authorization': `Bearer ${config.accessToken}`,
        'Content-Type': 'application/json',
        'LinkedIn-Version': '202312',
        'X-Restli-Protocol-Version': '2.0.0'
      }
    });

    this.setupInterceptors();
  }

  /**
   * Profesyonel profil arama
   */
  public async searchProfiles(criteria: {
    keywords?: string[];
    location?: string[];
    industry?: string[];
    currentCompany?: string[];
    jobTitle?: string[];
    seniorityLevel?: string[];
    limit?: number;
  }): Promise<LinkedInProfile[]> {
    try {
      await this.rateLimitWait();

      const searchParams = this.buildSearchParams(criteria);
      
      const response = await this.client.get('/people-search', {
        params: {
          ...searchParams,
          count: criteria.limit || 25,
          start: 0
        }
      });

      const profiles = response.data.elements?.map((element: any) => this.parseProfile(element)) || [];
      
      this.emit('profilesSearched', { 
        criteria, 
        count: profiles.length,
        totalAvailable: response.data.paging?.total || 0
      });

      return profiles;

    } catch (error) {
      this.emit('error', { type: 'profile_search', error, criteria });
      throw new Error(`LinkedIn profile search failed: ${error}`);
    }
  }

  /**
   * Şirket arama
   */
  public async searchCompanies(criteria: {
    keywords?: string[];
    industry?: string[];
    location?: string[];
    size?: string[];
    limit?: number;
  }): Promise<Company[]> {
    try {
      await this.rateLimitWait();

      const response = await this.client.get('/company-search', {
        params: {
          keywords: criteria.keywords?.join(' '),
          industries: criteria.industry?.join(','),
          locations: criteria.location?.join(','),
          companySizes: criteria.size?.join(','),
          count: criteria.limit || 25
        }
      });

      const companies = response.data.elements?.map((element: any) => this.parseCompany(element)) || [];
      
      this.emit('companiesSearched', { criteria, count: companies.length });
      return companies;

    } catch (error) {
      this.emit('error', { type: 'company_search', error, criteria });
      throw new Error(`LinkedIn company search failed: ${error}`);
    }
  }

  /**
   * Sponsored Content kampanyası oluştur
   */
  public async createSponsoredContentCampaign(campaignData: {
    name: string;
    targeting: CampaignTargeting;
    budget: CampaignBudget;
    creative: Omit<Creative, 'id' | 'status'>;
  }): Promise<LinkedInCampaign> {
    try {
      await this.rateLimitWait();

      // Kampanya oluştur
      const campaignResponse = await this.client.post('/campaigns', {
        name: campaignData.name,
        type: 'SPONSORED_CONTENT',
        status: 'PAUSED', // Başlangıçta duraklat
        targeting: this.formatTargeting(campaignData.targeting),
        costType: campaignData.budget.bidType,
        dailyBudget: {
          amount: campaignData.budget.dailyBudget,
          currencyCode: 'USD'
        },
        totalBudget: {
          amount: campaignData.budget.totalBudget,
          currencyCode: 'USD'
        }
      });

      const campaignId = campaignResponse.data.id;

      // Creative oluştur
      const creativeResponse = await this.client.post('/creatives', {
        campaign: `urn:li:sponsoredCampaign:${campaignId}`,
        type: campaignData.creative.type,
        variables: {
          data: {
            'com.linkedin.ads.SponsoredContentCreativeVariables': {
              clickUri: campaignData.creative.landingPageUrl,
              directSponsoredContent: {
                contentElements: [{
                  headline: campaignData.creative.headline,
                  description: campaignData.creative.description,
                  callToAction: {
                    type: campaignData.creative.callToAction
                  },
                  media: campaignData.creative.imageUrl ? {
                    reference: campaignData.creative.imageUrl,
                    mediaType: 'IMAGE'
                  } : undefined
                }]
              }
            }
          }
        }
      });

      const campaign: LinkedInCampaign = {
        id: campaignId,
        name: campaignData.name,
        status: 'PAUSED',
        type: 'SPONSORED_CONTENT',
        targeting: campaignData.targeting,
        budget: campaignData.budget,
        creatives: [{
          id: creativeResponse.data.id,
          status: 'ACTIVE',
          ...campaignData.creative
        }],
        metrics: {
          impressions: 0,
          clicks: 0,
          leads: 0,
          cost: 0,
          ctr: 0,
          cpc: 0,
          cpl: 0,
          conversionRate: 0,
          reach: 0,
          frequency: 0
        },
        createdAt: new Date(),
        lastModified: new Date()
      };

      this.emit('campaignCreated', { campaignId, name: campaignData.name });
      return campaign;

    } catch (error) {
      this.emit('error', { type: 'campaign_creation', error, campaignData });
      throw new Error(`LinkedIn campaign creation failed: ${error}`);
    }
  }

  /**
   * Lead Gen Form oluştur
   */
  public async createLeadGenForm(formData: {
    name: string;
    headline: string;
    description: string;
    fields: Omit<FormField, 'type'>[];
    thankYouMessage: string;
    privacyPolicy: string;
  }): Promise<LeadGenForm> {
    try {
      await this.rateLimitWait();

      const response = await this.client.post('/leadGenForms', {
        name: formData.name,
        headline: formData.headline,
        description: formData.description,
        privacyPolicyUrl: formData.privacyPolicy,
        thankYouMessage: formData.thankYouMessage,
        fields: formData.fields.map(field => ({
          fieldType: this.mapFieldType(field.label),
          label: field.label,
          required: field.required
        }))
      });

      const leadGenForm: LeadGenForm = {
        id: response.data.id,
        name: formData.name,
        headline: formData.headline,
        description: formData.description,
        privacyPolicy: formData.privacyPolicy,
        fields: formData.fields.map(field => ({
          type: this.mapFieldType(field.label),
          ...field
        })),
        thankYouMessage: formData.thankYouMessage,
        status: 'ACTIVE'
      };

      this.emit('leadGenFormCreated', { formId: leadGenForm.id, name: formData.name });
      return leadGenForm;

    } catch (error) {
      this.emit('error', { type: 'leadgen_form_creation', error, formData });
      throw new Error(`LinkedIn Lead Gen Form creation failed: ${error}`);
    }
  }

  /**
   * Kampanya metriklerini getir
   */
  public async getCampaignMetrics(campaignId: string, dateRange?: {
    start: Date;
    end: Date;
  }): Promise<CampaignMetrics> {
    try {
      await this.rateLimitWait();

      const params: any = {
        campaigns: `urn:li:sponsoredCampaign:${campaignId}`,
        fields: 'impressions,clicks,costInUsd,externalWebsiteConversions,leads'
      };

      if (dateRange) {
        params.dateRange = {
          start: {
            year: dateRange.start.getFullYear(),
            month: dateRange.start.getMonth() + 1,
            day: dateRange.start.getDate()
          },
          end: {
            year: dateRange.end.getFullYear(),
            month: dateRange.end.getMonth() + 1,
            day: dateRange.end.getDate()
          }
        };
      }

      const response = await this.client.get('/analytics', { params });

      const data = response.data.elements?.[0] || {};
      
      const metrics: CampaignMetrics = {
        impressions: data.impressions || 0,
        clicks: data.clicks || 0,
        leads: data.leads || data.externalWebsiteConversions || 0,
        cost: data.costInUsd || 0,
        ctr: data.impressions > 0 ? (data.clicks / data.impressions) * 100 : 0,
        cpc: data.clicks > 0 ? data.costInUsd / data.clicks : 0,
        cpl: data.leads > 0 ? data.costInUsd / data.leads : 0,
        conversionRate: data.clicks > 0 ? (data.leads / data.clicks) * 100 : 0,
        reach: data.reach || 0,
        frequency: data.frequency || 0
      };

      this.emit('metricsRetrieved', { campaignId, metrics });
      return metrics;

    } catch (error) {
      this.emit('error', { type: 'metrics_retrieval', error, campaignId });
      throw new Error(`LinkedIn metrics retrieval failed: ${error}`);
    }
  }

  /**
   * Kampanya durumunu güncelle
   */
  public async updateCampaignStatus(campaignId: string, status: 'ACTIVE' | 'PAUSED' | 'ARCHIVED'): Promise<void> {
    try {
      await this.rateLimitWait();

      await this.client.patch(`/campaigns/${campaignId}`, {
        status
      });

      this.emit('campaignStatusUpdated', { campaignId, status });

    } catch (error) {
      this.emit('error', { type: 'campaign_status_update', error, campaignId, status });
      throw new Error(`LinkedIn campaign status update failed: ${error}`);
    }
  }

  // Yardımcı metodlar
  private async rateLimitWait(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, this.rateLimitDelay));
  }

  private setupInterceptors(): void {
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 429) {
          // Rate limit hit
          const retryAfter = error.response.headers['retry-after'] || 60;
          await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
          return this.client.request(error.config);
        }
        return Promise.reject(error);
      }
    );
  }

  private buildSearchParams(criteria: any): any {
    const params: any = {};

    if (criteria.keywords?.length) {
      params.keywords = criteria.keywords.join(' ');
    }

    if (criteria.location?.length) {
      params.geoLocations = criteria.location.join(',');
    }

    if (criteria.industry?.length) {
      params.industries = criteria.industry.join(',');
    }

    if (criteria.currentCompany?.length) {
      params.currentCompany = criteria.currentCompany.join(',');
    }

    if (criteria.jobTitle?.length) {
      params.title = criteria.jobTitle.join(',');
    }

    if (criteria.seniorityLevel?.length) {
      params.seniorityLevel = criteria.seniorityLevel.join(',');
    }

    return params;
  }

  private parseProfile(element: any): LinkedInProfile {
    return {
      id: element.id || '',
      firstName: element.firstName || '',
      lastName: element.lastName || '',
      headline: element.headline || '',
      location: element.location?.name || '',
      industry: element.industry || '',
      positions: (element.positions?.values || []).map((pos: any) => ({
        title: pos.title || '',
        company: pos.company?.name || '',
        companyId: pos.company?.id,
        location: pos.location?.name,
        startDate: new Date(pos.startDate?.year || 2020, (pos.startDate?.month || 1) - 1),
        endDate: pos.endDate ? new Date(pos.endDate.year, pos.endDate.month - 1) : undefined,
        isCurrent: pos.isCurrent || false,
        description: pos.summary
      })),
      connections: element.numConnections || 0,
      profileUrl: element.publicProfileUrl || '',
      profilePicture: element.pictureUrl,
      summary: element.summary,
      skills: (element.skills?.values || []).map((skill: any) => skill.skill?.name || ''),
      languages: (element.languages?.values || []).map((lang: any) => lang.language?.name || ''),
      lastUpdated: new Date()
    };
  }

  private parseCompany(element: any): Company {
    return {
      id: element.id || '',
      name: element.name || '',
      industry: element.industries?.values?.[0]?.name || '',
      size: element.employeeCountRange?.name || '',
      location: element.locations?.values?.[0]?.description || '',
      website: element.websiteUrl,
      description: element.description,
      employeeCount: element.numEmployees || 0,
      foundedYear: element.foundedYear,
      specialties: (element.specialties?.values || []).map((spec: any) => spec.name || ''),
      logoUrl: element.logoUrl
    };
  }

  private formatTargeting(targeting: CampaignTargeting): any {
    return {
      includedTargetingFacets: {
        geoLocations: targeting.geography.map(geo => ({ name: geo })),
        industries: targeting.industries.map(ind => ({ name: ind })),
        jobTitles: targeting.jobTitles.map(title => ({ name: title })),
        companySizes: targeting.companySizes.map(size => ({ name: size })),
        seniorityLevels: targeting.seniorityLevels.map(level => ({ name: level })),
        skills: targeting.skills.map(skill => ({ name: skill }))
      }
    };
  }

  private mapFieldType(label: string): FormField['type'] {
    const lowerLabel = label.toLowerCase();
    
    if (lowerLabel.includes('first') && lowerLabel.includes('name')) return 'FIRST_NAME';
    if (lowerLabel.includes('last') && lowerLabel.includes('name')) return 'LAST_NAME';
    if (lowerLabel.includes('email')) return 'EMAIL';
    if (lowerLabel.includes('company')) return 'COMPANY';
    if (lowerLabel.includes('job') || lowerLabel.includes('title')) return 'JOB_TITLE';
    if (lowerLabel.includes('phone')) return 'PHONE';
    
    return 'CUSTOM';
  }

  /**
   * API durumu kontrolü
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.client.get('/me');
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Access token yenileme
   */
  public async refreshAccessToken(): Promise<string> {
    try {
      const response = await axios.post('https://www.linkedin.com/oauth/v2/accessToken', {
        grant_type: 'refresh_token',
        refresh_token: this.config.accessToken,
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret
      });

      const newToken = response.data.access_token;
      this.config.accessToken = newToken;
      this.client.defaults.headers['Authorization'] = `Bearer ${newToken}`;
      
      this.emit('tokenRefreshed', { newToken });
      return newToken;

    } catch (error) {
      this.emit('error', { type: 'token_refresh', error });
      throw new Error(`LinkedIn token refresh failed: ${error}`);
    }
  }
}
