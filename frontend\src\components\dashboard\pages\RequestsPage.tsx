'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ClipboardDocumentListIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { useQuote, QuoteRequest } from '@/contexts/quote-context';

interface RequestsPageProps {
  onNavigate?: (route: string) => void;
}

const RequestsPage: React.FC<RequestsPageProps> = ({ onNavigate }) => {
  const { customerQuoteRequests, isLoading } = useQuote();
  const [activeTab, setActiveTab] = useState<'active' | 'completed' | 'cancelled'>('active');

  // Quote context'ten gelen gerçek veriler kullanılıyor

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'quoted':
        return <ChatBubbleLeftRightIcon className="h-5 w-5 text-blue-500" />;
      case 'accepted':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'rejected':
      case 'cancelled':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClipboardDocumentListIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor';
      case 'quoted':
        return 'Teklif Alındı';
      case 'accepted':
        return 'Kabul Edildi';
      case 'rejected':
        return 'Reddedildi';
      case 'cancelled':
        return 'İptal Edildi';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'quoted':
        return 'bg-blue-100 text-blue-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredRequests = customerQuoteRequests.filter(request => {
    switch (activeTab) {
      case 'active':
        return ['pending', 'quoted'].includes(request.status);
      case 'completed':
        return ['accepted', 'rejected'].includes(request.status);
      case 'cancelled':
        return request.status === 'cancelled';
      default:
        return true;
    }
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Taleplerim</h1>
          <p className="text-gray-600 mt-1">Teklif taleplerinizi görüntüleyin ve yönetin</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'active', label: 'Aktif Talepler', count: filteredRequests.length },
            { id: 'completed', label: 'Tamamlanan', count: 0 },
            { id: 'cancelled', label: 'İptal Edilen', count: 0 }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Requests List */}
      {filteredRequests.length > 0 ? (
        <div className="space-y-6">
          {filteredRequests.map((request, index) => (
            <motion.div
              key={request.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200"
            >
              {/* Request Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(request.status)}
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(request.status)}`}>
                      {getStatusLabel(request.status)}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Talep #{request.id}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {request.createdAt && new Date(request.createdAt).toLocaleDateString('tr-TR')}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Alınan Teklif</p>
                  <p className="text-lg font-semibold text-blue-600">
                    {request.quotes?.length || 0}
                  </p>
                </div>
              </div>

              {/* Products */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Ürünler</h4>
                <div className="space-y-2">
                  {request.products.map((product) => (
                    <div key={product.id} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                      <div>
                        <p className="font-medium text-gray-900">{product.productName}</p>
                        <p className="text-sm text-gray-600">{product.productCategory}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {product.specifications?.length || 0} farklı ebat
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {product.specifications?.[0]?.area ? `${product.specifications[0].area} m²` : 'Belirtilmemiş'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {product.specifications?.[0]?.thickness ? `${product.specifications[0].thickness} cm` : ''}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Message */}
              {request.message && (
                <div className="mb-4 bg-blue-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-900 mb-2">Mesaj</h4>
                  <p className="text-sm text-blue-800">{request.message}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => onNavigate?.(`/customer/requests/detail/${request.id}`)}
                    className="flex items-center space-x-2 text-blue-600 hover:text-blue-700"
                  >
                    <EyeIcon className="h-4 w-4" />
                    <span className="text-sm font-medium">Detay</span>
                  </button>
                </div>
                {request.status === 'quoted' && (
                  <div className="flex items-center space-x-3">
                    <button className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                      Reddet
                    </button>
                    <button className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
                      Kabul Et
                    </button>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <ClipboardDocumentListIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {activeTab === 'active' && 'Aktif talebiniz bulunmuyor'}
            {activeTab === 'completed' && 'Tamamlanan talebiniz bulunmuyor'}
            {activeTab === 'cancelled' && 'İptal edilen talebiniz bulunmuyor'}
          </h3>
          <p className="text-gray-600 mb-6">
            {activeTab === 'active' && 'Yeni bir teklif talebi oluşturarak başlayabilirsiniz.'}
            {activeTab === 'completed' && 'Tamamladığınız talepler burada görünecek.'}
            {activeTab === 'cancelled' && 'İptal ettiğiniz talepler burada görünecek.'}
          </p>
          {activeTab === 'active' && (
            <button
              onClick={() => onNavigate?.('/customer/dashboard')}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Yeni Teklif İste
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default RequestsPage;
