import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { UnauthorizedError, ForbiddenError } from './errorHandler';

export interface AuthUser {
  id: string;
  email: string;
  userType: 'producer' | 'customer' | 'admin';
  status: string;
  role?: string; // Optional role field for admin users
}

declare global {
  namespace Express {
    interface Request {
      user?: AuthUser;
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user?: AuthUser;
}

export const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    let token: string | undefined;

    // Try to get token from Authorization header first
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7); // Remove 'Bearer ' prefix
    }

    // If no Bearer token, try to get from admin_token cookie (for admin routes)
    if (!token && req.cookies && req.cookies.admin_token) {
      token = req.cookies.admin_token;
      console.log('Admin token found in cookies:', !!token);
    }

    if (!token) {
      console.log('No token found. Cookies:', req.cookies);
      throw new UnauthorizedError('Access token required');
    }

    const jwtSecret = process.env.JWT_SECRET || 'admin_secret_key';
    if (!jwtSecret) {
      throw new Error('JWT_SECRET not configured');
    }

    const decoded = jwt.verify(token, jwtSecret) as any;

    // Handle different token formats (admin vs user tokens)
    if (decoded.adminId) {
      // Admin token format
      req.user = {
        id: decoded.adminId,
        email: decoded.email,
        userType: 'admin',
        status: 'active',
        role: decoded.role
      };
      console.log('Admin user set:', req.user);
    } else {
      // Regular user token format
      req.user = {
        id: decoded.userId,
        email: decoded.email,
        userType: decoded.userType,
        status: decoded.status
      };
    }

    // Check if user is active (skip for admin tokens)
    if (req.user && req.user.userType !== 'admin' && req.user.status !== 'active') {
      throw new ForbiddenError('Account is not active');
    }

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new UnauthorizedError('Invalid token'));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new UnauthorizedError('Token expired'));
    } else {
      next(error);
    }
  }
};

// Role-based authorization middleware
export const requireRole = (allowedRoles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    console.log('requireRole - req.user:', req.user);
    console.log('requireRole - allowedRoles:', allowedRoles);

    if (!req.user) {
      console.log('requireRole - No user found');
      throw new UnauthorizedError('Authentication required');
    }

    if (!allowedRoles.includes(req.user.userType)) {
      console.log('requireRole - Access denied. User type:', req.user.userType, 'Allowed:', allowedRoles);
      throw new ForbiddenError('Insufficient permissions');
    }

    console.log('requireRole - Access granted');
    next();
  };
};

// Admin only middleware
export const requireAdmin = requireRole(['ADMIN', 'admin']);

// Producer only middleware
export const requireProducer = requireRole(['PRODUCER', 'producer']);

// Customer only middleware
export const requireCustomer = requireRole(['CUSTOMER', 'customer']);

// Producer or Admin middleware
export const requireProducerOrAdmin = requireRole(['PRODUCER', 'producer', 'ADMIN', 'admin']);
