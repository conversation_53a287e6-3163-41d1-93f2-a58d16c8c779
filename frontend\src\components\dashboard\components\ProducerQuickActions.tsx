'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon,
  CubeIcon,
  ShoppingCartIcon,
  BellIcon,
  CogIcon
} from '@heroicons/react/24/outline';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  action: () => void;
}

interface ProducerQuickActionsProps {
  onNavigate?: (route: string) => void;
}

const ProducerQuickActions: React.FC<ProducerQuickActionsProps> = ({ onNavigate }) => {
  const quickActions: QuickAction[] = [
    {
      id: 'add-product',
      title: '<PERSON><PERSON>',
      description: '<PERSON>rün katalo<PERSON> yeni <PERSON>ün ekle',
      icon: PlusIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100',
      action: () => {
        onNavigate?.('/producer/products/add');
      }
    },
    {
      id: 'quote-requests',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      description: '<PERSON><PERSON><PERSON> teklif tale<PERSON> gö<PERSON>',
      icon: ClipboardDocumentListIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50 hover:bg-green-100',
      action: () => {
        onNavigate?.('/producer/quote-requests');
      }
    },
    {
      id: 'products',
      title: 'Ürünlerim',
      description: 'Ürün listesini yönet',
      icon: CubeIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 hover:bg-purple-100',
      action: () => {
        onNavigate?.('/producer/products');
      }
    },
    {
      id: 'orders',
      title: 'Siparişlerim',
      description: 'Aktif siparişleri takip et',
      icon: ShoppingCartIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 hover:bg-orange-100',
      action: () => {
        onNavigate?.('/producer/orders');
      }
    },
    {
      id: 'analytics',
      title: 'Satış Analizi',
      description: 'Detaylı satış raporları',
      icon: ChartBarIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-50 hover:bg-red-100',
      action: () => {
        onNavigate?.('/producer/analytics');
      }
    },
    {
      id: 'settings',
      title: 'Ayarlar',
      description: 'Hesap ve şirket ayarları',
      icon: CogIcon,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50 hover:bg-indigo-100',
      action: () => {
        onNavigate?.('/producer/settings');
      }
    }
  ];

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Hızlı İşlemler
        </h3>
        <button 
          className="text-sm text-amber-600 hover:text-amber-700 font-medium"
          onClick={() => onNavigate?.('/producer/dashboard')}
        >
          Tümünü Gör
        </button>
      </div>

      {/* Actions Grid */}
      <div className="space-y-3">
        {quickActions.map((action, index) => (
          <motion.button
            key={action.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={action.action}
            className={`w-full p-4 rounded-lg border border-gray-100 ${action.bgColor} transition-all duration-200 text-left group`}
          >
            <div className="flex items-start space-x-3">
              <div className={`p-2 rounded-lg ${action.bgColor.replace('hover:', '')} group-hover:scale-110 transition-transform duration-200`}>
                <action.icon className={`h-5 w-5 ${action.color}`} />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 group-hover:text-gray-700">
                  {action.title}
                </h4>
                <p className="text-xs text-gray-600 mt-1 group-hover:text-gray-500">
                  {action.description}
                </p>
              </div>
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </motion.button>
        ))}
      </div>

      {/* Bottom CTA */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => onNavigate?.('/producer/products/add')}
          className="w-full bg-gradient-to-r from-amber-500 to-orange-500 text-white py-3 px-4 rounded-lg font-medium hover:from-amber-600 hover:to-orange-600 transition-all duration-200 shadow-sm"
        >
          <div className="flex items-center justify-center space-x-2">
            <PlusIcon className="h-5 w-5" />
            <span>Yeni Ürün Ekle</span>
          </div>
        </motion.button>
      </div>
    </div>
  );
};

export default ProducerQuickActions;
