import sgMail from '@sendgrid/mail';
import nodemailer from 'nodemailer';
import handlebars from 'handlebars';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const readFileAsync = promisify(fs.readFile);

export interface EmailConfig {
  provider: 'sendgrid' | 'smtp';
  sendgrid?: {
    apiKey: string;
    fromEmail: string;
    fromName: string;
  };
  smtp?: {
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
    fromEmail: string;
    fromName: string;
  };
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface EmailData {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject?: string;
  html?: string;
  text?: string;
  templateName?: string;
  templateData?: any;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

export interface EscrowEmailData {
  customerName: string;
  producerName: string;
  orderNumber: string;
  amount: number;
  currency: string;
  referenceCode?: string;
  bankInfo?: {
    bankName: string;
    iban: string;
    accountHolder: string;
  };
  productName?: string;
  dueDate?: Date;
}

export class EmailService {
  private config: EmailConfig;
  private transporter?: nodemailer.Transporter;
  private templatesCache: Map<string, handlebars.TemplateDelegate> = new Map();

  constructor(config: EmailConfig) {
    this.config = config;
    this.initializeProvider();
  }

  /**
   * Initialize email provider
   */
  private initializeProvider(): void {
    if (this.config.provider === 'sendgrid' && this.config.sendgrid) {
      sgMail.setApiKey(this.config.sendgrid.apiKey);
    } else if (this.config.provider === 'smtp' && this.config.smtp) {
      this.transporter = nodemailer.createTransport({
        host: this.config.smtp.host,
        port: this.config.smtp.port,
        secure: this.config.smtp.secure,
        auth: this.config.smtp.auth,
      });
    }
  }

  /**
   * Send email
   */
  async sendEmail(emailData: EmailData): Promise<void> {
    try {
      let html = emailData.html;
      let subject = emailData.subject;

      // Process template if specified
      if (emailData.templateName && emailData.templateData) {
        const template = await this.getTemplate(emailData.templateName);
        html = template(emailData.templateData);

        // Extract subject from template data if not provided
        if (!subject && emailData.templateData.subject) {
          subject = emailData.templateData.subject;
        }
      }

      // Subject zorunlu kontrolü
      if (!subject) {
        throw new Error('Email subject is required');
      }

      if (this.config.provider === 'sendgrid') {
        await this.sendWithSendGrid({
          ...emailData,
          html,
          subject: subject!,
        });
      } else {
        await this.sendWithSMTP({
          ...emailData,
          html,
          subject: subject!,
        });
      }
    } catch (error) {
      console.error('Email sending failed:', error);
      throw new Error('Failed to send email');
    }
  }

  /**
   * Send email with SendGrid
   */
  private async sendWithSendGrid(emailData: EmailData): Promise<void> {
    if (!this.config.sendgrid) {
      throw new Error('SendGrid configuration not found');
    }

    const msg: any = {
      to: emailData.to,
      cc: emailData.cc,
      bcc: emailData.bcc,
      from: {
        email: this.config.sendgrid.fromEmail,
        name: this.config.sendgrid.fromName,
      },
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text,
      attachments: emailData.attachments?.map(att => ({
        filename: att.filename,
        content: att.content.toString('base64'),
        type: att.contentType,
        disposition: 'attachment',
      })),
    };

    // SendGrid için boş alanları temizle
    Object.keys(msg).forEach(key => {
      if (msg[key] === undefined || msg[key] === null) {
        delete msg[key];
      }
    });

    await sgMail.send(msg);
  }

  /**
   * Send email with SMTP
   */
  private async sendWithSMTP(emailData: EmailData): Promise<void> {
    if (!this.transporter || !this.config.smtp) {
      throw new Error('SMTP configuration not found');
    }

    const mailOptions = {
      from: `"${this.config.smtp.fromName}" <${this.config.smtp.fromEmail}>`,
      to: emailData.to,
      cc: emailData.cc,
      bcc: emailData.bcc,
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text,
      attachments: emailData.attachments,
    };

    await this.transporter.sendMail(mailOptions);
  }

  /**
   * Get compiled template
   */
  private async getTemplate(templateName: string): Promise<handlebars.TemplateDelegate> {
    if (this.templatesCache.has(templateName)) {
      return this.templatesCache.get(templateName)!;
    }

    const templatePath = path.join(__dirname, '../templates/email', `${templateName}.hbs`);
    
    try {
      const templateContent = await readFileAsync(templatePath, 'utf-8');
      const template = handlebars.compile(templateContent);
      this.templatesCache.set(templateName, template);
      return template;
    } catch (error) {
      throw new Error(`Template ${templateName} not found`);
    }
  }

  /**
   * Send payment instructions email
   */
  async sendPaymentInstructions(to: string, data: EscrowEmailData): Promise<void> {
    await this.sendEmail({
      to,
      subject: `Ödeme Talimatları - Sipariş ${data.orderNumber}`,
      templateName: 'payment-instructions',
      templateData: {
        customerName: data.customerName,
        orderNumber: data.orderNumber,
        amount: data.amount,
        currency: data.currency,
        referenceCode: data.referenceCode,
        bankInfo: data.bankInfo,
        productName: data.productName,
        dueDate: data.dueDate,
      },
    });
  }

  /**
   * Send payment confirmation email
   */
  async sendPaymentConfirmation(to: string, data: EscrowEmailData): Promise<void> {
    await this.sendEmail({
      to,
      subject: `Ödeme Onaylandı - Sipariş ${data.orderNumber}`,
      templateName: 'payment-confirmation',
      templateData: {
        customerName: data.customerName,
        orderNumber: data.orderNumber,
        amount: data.amount,
        currency: data.currency,
        productName: data.productName,
      },
    });
  }

  /**
   * Send production start notification
   */
  async sendProductionStartNotification(to: string, data: EscrowEmailData): Promise<void> {
    await this.sendEmail({
      to,
      subject: `Üretime Başlandı - Sipariş ${data.orderNumber}`,
      templateName: 'production-start',
      templateData: {
        producerName: data.producerName,
        orderNumber: data.orderNumber,
        amount: data.amount,
        currency: data.currency,
        productName: data.productName,
      },
    });
  }

  /**
   * Send goods ready notification
   */
  async sendGoodsReadyNotification(to: string, data: EscrowEmailData): Promise<void> {
    await this.sendEmail({
      to,
      subject: `Ürün Hazır - Onayınız Bekleniyor - Sipariş ${data.orderNumber}`,
      templateName: 'goods-ready',
      templateData: {
        customerName: data.customerName,
        orderNumber: data.orderNumber,
        productName: data.productName,
        producerName: data.producerName,
      },
    });
  }

  /**
   * Send payment release notification
   */
  async sendPaymentReleaseNotification(to: string, data: EscrowEmailData): Promise<void> {
    await this.sendEmail({
      to,
      subject: `Ödeme Yapıldı - Sipariş ${data.orderNumber}`,
      templateName: 'payment-release',
      templateData: {
        producerName: data.producerName,
        orderNumber: data.orderNumber,
        amount: data.amount,
        currency: data.currency,
        productName: data.productName,
      },
    });
  }

  /**
   * Send dispute notification
   */
  async sendDisputeNotification(to: string, data: EscrowEmailData & { reason: string }): Promise<void> {
    await this.sendEmail({
      to,
      subject: `Anlaşmazlık Bildirimi - Sipariş ${data.orderNumber}`,
      templateName: 'dispute-notification',
      templateData: {
        customerName: data.customerName,
        producerName: data.producerName,
        orderNumber: data.orderNumber,
        reason: data.reason,
        productName: data.productName,
      },
    });
  }

  /**
   * Send welcome email
   */
  async sendWelcomeEmail(to: string, data: { name: string; userType: string }): Promise<void> {
    await this.sendEmail({
      to,
      subject: 'Doğal Taş Pazaryeri\'ne Hoş Geldiniz',
      templateName: 'welcome',
      templateData: {
        name: data.name,
        userType: data.userType,
      },
    });
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(to: string, data: { name: string; resetLink: string }): Promise<void> {
    await this.sendEmail({
      to,
      subject: 'Şifre Sıfırlama Talebi',
      templateName: 'password-reset',
      templateData: {
        name: data.name,
        resetLink: data.resetLink,
      },
    });
  }

  /**
   * Test email configuration
   */
  async testConfiguration(): Promise<boolean> {
    try {
      await this.sendEmail({
        to: this.config.sendgrid?.fromEmail || this.config.smtp?.fromEmail || '<EMAIL>',
        subject: 'Email Service Test',
        html: '<p>Email service is working correctly!</p>',
        text: 'Email service is working correctly!',
      });
      return true;
    } catch (error) {
      console.error('Email configuration test failed:', error);
      return false;
    }
  }
}
