'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { MultiDeliveryDashboard } from '@/components/ui/multi-delivery-dashboard'
import { mockMultiDeliveryOrder } from '@/data/mock-multi-delivery'
import { ProductionSchedule } from '@/types/multi-delivery'

export default function MultiDeliveryOrderPage() {
  const { producer } = useProducerAuth()
  const [order, setOrder] = React.useState(mockMultiDeliveryOrder)

  const handleUpdateStage = (packageId: string, stageId: string, status: ProductionSchedule['status']) => {
    console.log('Updating stage:', { packageId, stageId, status })
    
    setOrder(prevOrder => ({
      ...prevOrder,
      deliveryPackages: prevOrder.deliveryPackages.map(pkg => {
        if (pkg.id === packageId) {
          const updatedSchedules = pkg.productionSchedules.map(schedule => {
            if (schedule.id === stageId) {
              return {
                ...schedule,
                status,
                actualEndDate: status === 'completed' ? new Date().toISOString() : schedule.actualEndDate
              }
            }
            return schedule
          })

          // Check if all stages are completed
          const allCompleted = updatedSchedules.every(s => s.status === 'completed')
          const hasInProgress = updatedSchedules.some(s => s.status === 'in_progress')
          
          let newProductionStatus = pkg.productionStatus
          if (allCompleted) {
            newProductionStatus = 'completed'
          } else if (hasInProgress || status === 'in_progress') {
            newProductionStatus = 'in_progress'
          }

          return {
            ...pkg,
            productionSchedules: updatedSchedules,
            productionStatus: newProductionStatus,
            productionEndDate: allCompleted ? new Date().toISOString().split('T')[0] : pkg.productionEndDate
          }
        }
        return pkg
      })
    }))

    // Show success message
    alert('Aşama durumu güncellendi!')
  }

  const handlePauseProduction = (packageId: string) => {
    console.log('Pausing production for package:', packageId)
    
    setOrder(prevOrder => ({
      ...prevOrder,
      deliveryPackages: prevOrder.deliveryPackages.map(pkg => {
        if (pkg.id === packageId) {
          return {
            ...pkg,
            productionStatus: 'paused',
            productionSchedules: pkg.productionSchedules.map(schedule => 
              schedule.status === 'in_progress' 
                ? { ...schedule, status: 'paused' as const }
                : schedule
            )
          }
        }
        return pkg
      })
    }))

    alert('Paket üretimi duraklatıldı!')
  }

  const handleViewPackageDetails = (packageId: string) => {
    console.log('Viewing package details:', packageId)
    const pkg = order.deliveryPackages.find(p => p.id === packageId)
    if (pkg) {
      alert(`Paket #${pkg.packageNumber} detayları:\n\nMiktar: ${pkg.quantity} m²\nTutar: $${pkg.amount.toLocaleString()}\nDurum: ${pkg.productionStatus}`)
    }
  }

  const handleGoBack = () => {
    window.history.back()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleGoBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Geri Dön
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Çoklu Teslimat Yönetimi</h1>
          <p className="text-gray-600">
            Büyük metrajlı siparişlerin paket bazlı üretim ve teslimat takibi
          </p>
        </div>
      </div>

      {/* Multi Delivery Dashboard */}
      <MultiDeliveryDashboard
        order={order}
        onUpdateStage={handleUpdateStage}
        onPauseProduction={handlePauseProduction}
        onViewPackageDetails={handleViewPackageDetails}
      />

      {/* Development Info */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h3 className="font-semibold text-blue-800 mb-2">🚧 Geliştirme Aşamasında</h3>
        <p className="text-sm text-blue-700">
          Bu sayfa çoklu teslimat sistemi için demo amaçlı oluşturulmuştur. 
          Gerçek implementasyonda backend API entegrasyonu yapılacaktır.
        </p>
        <div className="mt-3 text-xs text-blue-600">
          <p><strong>Mevcut Özellikler:</strong></p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Paket bazlı üretim takibi</li>
            <li>Aşama durumu güncelleme</li>
            <li>Üretim duraklatma</li>
            <li>Genel ilerleme görünümü</li>
            <li>Responsive tasarım</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
