'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { History, Search, Filter, Download, User, Clock, Settings } from 'lucide-react';

interface AuditLogEntry {
  id: string;
  category: string;
  key: string;
  oldValue: any;
  newValue: any;
  changedBy: string;
  changeReason?: string;
  ipAddress?: string;
  createdAt: string;
}

const SettingsAuditLog = () => {
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [userFilter, setUserFilter] = useState('all');

  // Mock data for demonstration
  const mockAuditLogs: AuditLogEntry[] = [
    {
      id: 'audit_1',
      category: 'platform',
      key: 'siteName',
      oldValue: 'Old Site Name',
      newValue: 'Doğal Taş Pazaryeri',
      changedBy: '<EMAIL>',
      changeReason: 'Site rebranding',
      ipAddress: '*************',
      createdAt: new Date(Date.now() - 86400000).toISOString() // 1 day ago
    },
    {
      id: 'audit_2',
      category: 'business',
      key: 'commissionRateM2',
      oldValue: 0.8,
      newValue: 1.0,
      changedBy: '<EMAIL>',
      changeReason: 'Commission rate adjustment',
      ipAddress: '*************',
      createdAt: new Date(Date.now() - 43200000).toISOString() // 12 hours ago
    },
    {
      id: 'audit_3',
      category: 'platform',
      key: 'maintenanceMode',
      oldValue: true,
      newValue: false,
      changedBy: '<EMAIL>',
      changeReason: 'Maintenance completed',
      ipAddress: '*************',
      createdAt: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
    },
    {
      id: 'audit_4',
      category: 'security',
      key: 'passwordMinLength',
      oldValue: 6,
      newValue: 8,
      changedBy: '<EMAIL>',
      changeReason: 'Security enhancement',
      ipAddress: '*************',
      createdAt: new Date(Date.now() - 1800000).toISOString() // 30 minutes ago
    },
    {
      id: 'audit_5',
      category: 'business',
      key: 'upfrontPaymentPercentage',
      oldValue: 25,
      newValue: 30,
      changedBy: '<EMAIL>',
      changeReason: 'Business policy update',
      ipAddress: '*************',
      createdAt: new Date(Date.now() - 900000).toISOString() // 15 minutes ago
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setAuditLogs(mockAuditLogs);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredLogs = auditLogs.filter(log => {
    const matchesSearch = searchTerm === '' || 
      log.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.changedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (log.changeReason && log.changeReason.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = categoryFilter === 'all' || log.category === categoryFilter;
    const matchesUser = userFilter === 'all' || log.changedBy === userFilter;
    
    return matchesSearch && matchesCategory && matchesUser;
  });

  const formatValue = (value: any): string => {
    if (typeof value === 'boolean') {
      return value ? 'Aktif' : 'Pasif';
    }
    if (typeof value === 'number') {
      return value.toString();
    }
    if (typeof value === 'string') {
      return value;
    }
    return JSON.stringify(value);
  };

  const getCategoryBadgeVariant = (category: string) => {
    switch (category) {
      case 'platform': return 'default';
      case 'security': return 'destructive';
      case 'business': return 'secondary';
      case 'notification': return 'outline';
      case 'system': return 'secondary';
      case 'integration': return 'outline';
      default: return 'outline';
    }
  };

  const getCategoryName = (category: string): string => {
    const categoryNames: Record<string, string> = {
      platform: 'Platform',
      security: 'Güvenlik',
      business: 'İş Kuralları',
      notification: 'Bildirimler',
      system: 'Sistem',
      integration: 'Entegrasyonlar'
    };
    return categoryNames[category] || category;
  };

  const getTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Az önce';
    if (diffInMinutes < 60) return `${diffInMinutes} dakika önce`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} saat önce`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} gün önce`;
  };

  const uniqueCategories = [...new Set(auditLogs.map(log => log.category))];
  const uniqueUsers = [...new Set(auditLogs.map(log => log.changedBy))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Ayar adı, kullanıcı veya açıklama ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Kategori filtrele" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tüm Kategoriler</SelectItem>
            {uniqueCategories.map(category => (
              <SelectItem key={category} value={category}>
                {getCategoryName(category)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={userFilter} onValueChange={setUserFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Kullanıcı filtrele" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tüm Kullanıcılar</SelectItem>
            {uniqueUsers.map(user => (
              <SelectItem key={user} value={user}>
                {user}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button variant="outline" size="sm">
          <Download className="w-4 h-4 mr-2" />
          Dışa Aktar
        </Button>
      </div>

      {/* Results Count */}
      <div className="text-sm text-gray-600">
        {filteredLogs.length} değişiklik gösteriliyor
      </div>

      {/* Audit Log Entries */}
      <div className="space-y-3">
        {filteredLogs.length === 0 ? (
          <Alert>
            <History className="h-4 w-4" />
            <AlertDescription>
              Filtrelere uygun değişiklik kaydı bulunamadı.
            </AlertDescription>
          </Alert>
        ) : (
          filteredLogs.map((log) => (
            <Card key={log.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge variant={getCategoryBadgeVariant(log.category)}>
                        {getCategoryName(log.category)}
                      </Badge>
                      <span className="font-medium text-gray-900">{log.key}</span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                      <div>
                        <p className="text-xs text-gray-500 mb-1">Eski Değer:</p>
                        <p className="text-sm bg-red-50 text-red-700 px-2 py-1 rounded">
                          {formatValue(log.oldValue)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 mb-1">Yeni Değer:</p>
                        <p className="text-sm bg-green-50 text-green-700 px-2 py-1 rounded">
                          {formatValue(log.newValue)}
                        </p>
                      </div>
                    </div>

                    {log.changeReason && (
                      <div className="mb-3">
                        <p className="text-xs text-gray-500 mb-1">Değişiklik Sebebi:</p>
                        <p className="text-sm text-gray-700">{log.changeReason}</p>
                      </div>
                    )}

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <User className="w-3 h-3" />
                        <span>{log.changedBy}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{getTimeAgo(log.createdAt)}</span>
                      </div>
                      {log.ipAddress && (
                        <span>IP: {log.ipAddress}</span>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Load More Button */}
      {filteredLogs.length > 0 && (
        <div className="text-center pt-4">
          <Button variant="outline">
            Daha Fazla Yükle
          </Button>
        </div>
      )}
    </div>
  );
};

export default SettingsAuditLog;
