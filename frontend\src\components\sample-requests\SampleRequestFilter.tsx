'use client';

import React from 'react';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon,
  CalendarIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { SampleRequestFilterProps } from '@/types/sample-request';
import { getSampleStatusLabel, getAllSampleStatuses } from '@/utils/sample-status-utils';

const SampleRequestFilter: React.FC<SampleRequestFilterProps> = ({
  searchTerm,
  onSearchChange,
  dateRange,
  onDateRangeChange,
  statusFilter = [],
  onStatusFilterChange
}) => {
  const [showFilters, setShowFilters] = React.useState(false);

  const handleStatusToggle = (status: string) => {
    if (!onStatusFilterChange) return;
    
    const newStatusFilter = statusFilter.includes(status)
      ? statusFilter.filter(s => s !== status)
      : [...statusFilter, status];
    
    onStatusFilterChange(newStatusFilter);
  };

  const clearFilters = () => {
    onSearchChange('');
    onDateRangeChange?.({ start: null, end: null });
    onStatusFilterChange?.([]);
  };

  const hasActiveFilters = searchTerm || 
    (dateRange?.start || dateRange?.end) || 
    statusFilter.length > 0;

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
      {/* Search and Filter Toggle */}
      <div className="flex items-center space-x-4 mb-4">
        {/* Search Input */}
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
          <input
            type="text"
            placeholder="Numune talebi ara..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Filter Toggle */}
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
            showFilters || hasActiveFilters
              ? 'bg-blue-50 border-blue-200 text-blue-700'
              : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
          }`}
        >
          <FunnelIcon className="h-4 w-4" />
          <span>Filtrele</span>
          {hasActiveFilters && (
            <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-0.5">
              {[searchTerm, dateRange?.start, dateRange?.end, ...statusFilter].filter(Boolean).length}
            </span>
          )}
        </button>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-800"
          >
            <XMarkIcon className="h-4 w-4" />
            <span className="text-sm">Temizle</span>
          </button>
        )}
      </div>

      {/* Advanced Filters */}
      {showFilters && (
        <div className="border-t border-gray-200 pt-4 space-y-4">
          {/* Date Range Filter */}
          {onDateRangeChange && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <CalendarIcon className="h-4 w-4 inline mr-1" />
                Tarih Aralığı
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="date"
                  value={dateRange?.start ? dateRange.start.toISOString().split('T')[0] : ''}
                  onChange={(e) => onDateRangeChange({
                    start: e.target.value ? new Date(e.target.value) : null,
                    end: dateRange?.end || null
                  })}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="text-gray-500">-</span>
                <input
                  type="date"
                  value={dateRange?.end ? dateRange.end.toISOString().split('T')[0] : ''}
                  onChange={(e) => onDateRangeChange({
                    start: dateRange?.start || null,
                    end: e.target.value ? new Date(e.target.value) : null
                  })}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          )}

          {/* Status Filter */}
          {onStatusFilterChange && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Durum Filtresi
              </label>
              <div className="flex flex-wrap gap-2">
                {getAllSampleStatuses().map((status) => (
                  <button
                    key={status}
                    onClick={() => handleStatusToggle(status)}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      statusFilter.includes(status)
                        ? 'bg-blue-100 text-blue-800 border border-blue-200'
                        : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                    }`}
                  >
                    {getSampleStatusLabel(status)}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SampleRequestFilter;
