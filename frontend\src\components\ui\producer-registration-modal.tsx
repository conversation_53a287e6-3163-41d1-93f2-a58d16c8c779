'use client'

import * as React from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  User, 
  Building, 
  MapPin, 
  FileText, 
  Factory,
  Mountain,
  Upload,
  Plus,
  Trash2,
  Check
} from 'lucide-react'

interface ProducerRegisterData {
  // Kişisel Bilgiler
  name: string
  email: string
  password: string
  confirmPassword: string
  phone: string
  position: string

  // Şirket Bilgileri
  companyName: string
  taxNumber: string
  tradeRegistryNumber: string
  companyAddress: string
  companyPhone: string
  companyEmail: string
  website?: string
  foundedYear: string
  employeeCount: string

  // Üretim Bilgileri
  productionCapacity: string
  productCategories: string[]
  certifications: string[]
  customCertification?: string

  // Lokasyonlar
  hasQuarry: boolean
  quarries: Array<{
    name: string
    address: string
    googleMapsLink?: string
    description?: string
  }>
  factories: Array<{
    name: string
    address: string
    googleMapsLink?: string
    capacity: string
    description?: string
  }>

  // <PERSON><PERSON>im <PERSON>ite Raporu
  productionCapacityReport?: File

  // Fason Üretim
  providesCustomManufacturing: boolean
  customManufacturingDetails?: string

  // Belgeler
  documents: Array<{
    type: string
    file?: File
    fileName?: string
    uploaded: boolean
  }>

  // Şirket Tanıtımı
  companyDescription: string
}

interface ProducerRegistrationModalProps {
  isOpen: boolean
  onClose: () => void
  onRegister: (data: ProducerRegisterData) => Promise<boolean>
  onSwitchToLogin: () => void
}

const PRODUCT_CATEGORIES = [
  'Mermer', 'Traverten', 'Granit', 'Oniks', 'Kireçtaşı', 'Bazalt', 'Andezit', 'Plaka'
]

const CERTIFICATIONS = [
  'ISO 9001', 'ISO 14001', 'CE', 'TSE', 'OHSAS 18001', 'Diğer'
]

const DOCUMENT_TYPES = [
  'Maden İşletme Ruhsatı',
  'Ticaret Sicil Belgesi',
  'Vergi Levhası',
  'Sanayi Sicil Belgesi'
]

export function ProducerRegistrationModal({ 
  isOpen, 
  onClose, 
  onRegister, 
  onSwitchToLogin 
}: ProducerRegistrationModalProps) {
  const [currentStep, setCurrentStep] = React.useState(1)
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState('')

  const [formData, setFormData] = React.useState<ProducerRegisterData>({
    // Kişisel Bilgiler
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    position: '',

    // Şirket Bilgileri
    companyName: '',
    taxNumber: '',
    tradeRegistryNumber: '',
    companyAddress: '',
    companyPhone: '',
    companyEmail: '',
    website: '',
    foundedYear: '',
    employeeCount: '',

    // Üretim Bilgileri
    productionCapacity: '',
    productCategories: [],
    certifications: [],
    customCertification: '',

    // Lokasyonlar
    hasQuarry: true,
    quarries: [{ name: '', address: '', googleMapsLink: '', description: '' }],
    factories: [{ name: '', address: '', googleMapsLink: '', capacity: '', description: '' }],

    // Üretim Kapasite Raporu
    productionCapacityReport: undefined,

    // Fason Üretim
    providesCustomManufacturing: false,
    customManufacturingDetails: '',

    // Belgeler
    documents: DOCUMENT_TYPES.map(type => ({ type, uploaded: false })),

    // Şirket Tanıtımı
    companyDescription: ''
  })

  const totalSteps = 6

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const addQuarry = () => {
    setFormData(prev => ({
      ...prev,
      quarries: [...prev.quarries, { name: '', address: '', googleMapsLink: '', description: '' }]
    }))
  }

  const removeQuarry = (index: number) => {
    setFormData(prev => ({
      ...prev,
      quarries: prev.quarries.filter((_, i) => i !== index)
    }))
  }

  const updateQuarry = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      quarries: prev.quarries.map((quarry, i) => 
        i === index ? { ...quarry, [field]: value } : quarry
      )
    }))
  }

  const addFactory = () => {
    setFormData(prev => ({
      ...prev,
      factories: [...prev.factories, { name: '', address: '', googleMapsLink: '', capacity: '', description: '' }]
    }))
  }

  const removeFactory = (index: number) => {
    setFormData(prev => ({
      ...prev,
      factories: prev.factories.filter((_, i) => i !== index)
    }))
  }

  const updateFactory = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      factories: prev.factories.map((factory, i) => 
        i === index ? { ...factory, [field]: value } : factory
      )
    }))
  }

  const toggleCategory = (category: string) => {
    setFormData(prev => ({
      ...prev,
      productCategories: prev.productCategories.includes(category)
        ? prev.productCategories.filter(c => c !== category)
        : [...prev.productCategories, category]
    }))
  }

  const toggleCertification = (certification: string) => {
    setFormData(prev => ({
      ...prev,
      certifications: prev.certifications.includes(certification)
        ? prev.certifications.filter(c => c !== certification)
        : [...prev.certifications, certification]
    }))
  }

  const nextStep = () => {
    // Adım 4 validation - Fabrika bilgileri zorunlu
    if (currentStep === 4) {
      // Fabrika kontrolü
      if (formData.factories.length === 0 || !formData.factories[0].name || !formData.factories[0].address || !formData.factories[0].capacity) {
        setError('Fabrika bilgileri zorunludur. En az bir fabrika eklemelisiniz.')
        return
      }

      // Üretim kapasite raporu kontrolü
      if (!formData.productionCapacityReport) {
        setError('Üretim kapasite raporu yüklenmesi zorunludur.')
        return
      }
    }

    setError('')
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    setError('')

    try {
      // Validation
      if (formData.password !== formData.confirmPassword) {
        setError('Şifreler eşleşmiyor')
        return
      }

      if (formData.productCategories.length === 0) {
        setError('En az bir ürün kategorisi seçmelisiniz')
        return
      }

      const success = await onRegister(formData)
      if (!success) {
        setError('Kayıt işlemi başarısız oldu')
      }
    } catch (error) {
      setError('Kayıt işlemi sırasında bir hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <User className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Kişisel Bilgiler</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ad Soyad *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  E-posta *
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => updateFormData('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Telefon *
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => updateFormData('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pozisyon *
                </label>
                <input
                  type="text"
                  value={formData.position}
                  onChange={(e) => updateFormData('position', e.target.value)}
                  placeholder="Genel Müdür, Satış Müdürü, vb."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Şifre *
                </label>
                <input
                  type="password"
                  value={formData.password}
                  onChange={(e) => updateFormData('password', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Şifre Tekrar *
                </label>
                <input
                  type="password"
                  value={formData.confirmPassword}
                  onChange={(e) => updateFormData('confirmPassword', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Building className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Şirket Bilgileri</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Şirket Adı *
                </label>
                <input
                  type="text"
                  value={formData.companyName}
                  onChange={(e) => updateFormData('companyName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Vergi Numarası *
                </label>
                <input
                  type="text"
                  value={formData.taxNumber}
                  onChange={(e) => updateFormData('taxNumber', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ticaret Sicil No *
                </label>
                <input
                  type="text"
                  value={formData.tradeRegistryNumber}
                  onChange={(e) => updateFormData('tradeRegistryNumber', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Şirket Adresi *
                </label>
                <textarea
                  value={formData.companyAddress}
                  onChange={(e) => updateFormData('companyAddress', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Şirket Telefonu *
                </label>
                <input
                  type="tel"
                  value={formData.companyPhone}
                  onChange={(e) => updateFormData('companyPhone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Şirket E-postası *
                </label>
                <input
                  type="email"
                  value={formData.companyEmail}
                  onChange={(e) => updateFormData('companyEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Website
                </label>
                <input
                  type="url"
                  value={formData.website}
                  onChange={(e) => updateFormData('website', e.target.value)}
                  placeholder="https://www.sirketiniz.com"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Kuruluş Yılı *
                </label>
                <input
                  type="number"
                  value={formData.foundedYear}
                  onChange={(e) => updateFormData('foundedYear', e.target.value)}
                  min="1900"
                  max="2025"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Çalışan Sayısı *
                </label>
                <select
                  value={formData.employeeCount}
                  onChange={(e) => updateFormData('employeeCount', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                >
                  <option value="">Seçiniz</option>
                  <option value="1-10">1-10 kişi</option>
                  <option value="11-50">11-50 kişi</option>
                  <option value="51-100">51-100 kişi</option>
                  <option value="101-500">101-500 kişi</option>
                  <option value="500+">500+ kişi</option>
                </select>
              </div>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Factory className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Üretim Bilgileri</h3>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Üretim Kapasitesi *
                </label>
                <textarea
                  value={formData.productionCapacity}
                  onChange={(e) => updateFormData('productionCapacity', e.target.value)}
                  placeholder="Aylık üretim kapasitesi, makine parkı, vb. bilgileri yazınız"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Ürün Kategorileri * (En az bir tane seçiniz)
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {PRODUCT_CATEGORIES.map((category) => (
                    <label key={category} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.productCategories.includes(category)}
                        onChange={() => toggleCategory(category)}
                        className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                      />
                      <span className="text-sm">{category}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Sertifikalar
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {CERTIFICATIONS.map((cert) => (
                    <label key={cert} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.certifications.includes(cert)}
                        onChange={() => toggleCertification(cert)}
                        className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                      />
                      <span className="text-sm">{cert}</span>
                    </label>
                  ))}
                </div>

                {formData.certifications.includes('Diğer') && (
                  <div className="mt-3">
                    <input
                      type="text"
                      value={formData.customCertification}
                      onChange={(e) => updateFormData('customCertification', e.target.value)}
                      placeholder="Diğer sertifikalarınızı yazınız"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <MapPin className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Lokasyonlar</h3>
            </div>

            {/* Ocak Durumu */}
            <div>
              <h4 className="font-medium flex items-center gap-2 mb-3">
                <Mountain className="w-4 h-4" />
                Ocak Durumu
              </h4>
              <div className="space-y-3 mb-4">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="hasQuarry"
                    checked={formData.hasQuarry === true}
                    onChange={() => updateFormData('hasQuarry', true)}
                    className="text-amber-600 focus:ring-amber-500"
                  />
                  <span className="text-sm">Ocağım var</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="hasQuarry"
                    checked={formData.hasQuarry === false}
                    onChange={() => updateFormData('hasQuarry', false)}
                    className="text-amber-600 focus:ring-amber-500"
                  />
                  <span className="text-sm">Ocağım yok</span>
                </label>
              </div>

              {formData.hasQuarry && (
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-medium">Ocak Bilgileri</h5>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addQuarry}
                    >
                      <Plus className="w-4 h-4 mr-1" />
                      Ocak Ekle
                    </Button>
                  </div>

                  {formData.quarries.map((quarry, index) => (
                    <Card key={index} className="mb-3">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <h5 className="font-medium">Ocak {index + 1}</h5>
                          {formData.quarries.length > 1 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeQuarry(index)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Ocak Adı
                            </label>
                            <input
                              type="text"
                              value={quarry.name}
                              onChange={(e) => updateQuarry(index, 'name', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Google Maps Link
                            </label>
                            <input
                              type="url"
                              value={quarry.googleMapsLink}
                              onChange={(e) => updateQuarry(index, 'googleMapsLink', e.target.value)}
                              placeholder="https://maps.google.com/..."
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                            />
                          </div>

                          <div className="md:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Adres
                            </label>
                            <textarea
                              value={quarry.address}
                              onChange={(e) => updateQuarry(index, 'address', e.target.value)}
                              rows={2}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                            />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>

            {/* Fabrikalar */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Factory className="w-4 h-4" />
                  Fabrikalar *
                  <span className="text-xs text-gray-500 font-normal">(Zorunlu)</span>
                </h4>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addFactory}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Fabrika Ekle
                </Button>
              </div>

              {formData.factories.map((factory, index) => (
                <Card key={index} className="mb-3">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <h5 className="font-medium">Fabrika {index + 1}</h5>
                      {formData.factories.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeFactory(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Fabrika Adı *
                        </label>
                        <input
                          type="text"
                          value={factory.name}
                          onChange={(e) => updateFactory(index, 'name', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Kapasite *
                        </label>
                        <input
                          type="text"
                          value={factory.capacity}
                          onChange={(e) => updateFactory(index, 'capacity', e.target.value)}
                          placeholder="Aylık kapasite"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Google Maps Link
                        </label>
                        <input
                          type="url"
                          value={factory.googleMapsLink}
                          onChange={(e) => updateFactory(index, 'googleMapsLink', e.target.value)}
                          placeholder="https://maps.google.com/..."
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Adres *
                        </label>
                        <textarea
                          value={factory.address}
                          onChange={(e) => updateFactory(index, 'address', e.target.value)}
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          required
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Üretim Kapasite Raporu */}
            <div>
              <h4 className="font-medium flex items-center gap-2 mb-3">
                <FileText className="w-4 h-4" />
                Üretim Kapasite Raporu *
                <span className="text-xs text-gray-500 font-normal">(Zorunlu)</span>
              </h4>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  type="file"
                  id="productionCapacityReport"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      updateFormData('productionCapacityReport', file)
                    }
                  }}
                  className="hidden"
                />
                <label htmlFor="productionCapacityReport" className="cursor-pointer">
                  <div className="flex flex-col items-center">
                    <Upload className="w-8 h-8 text-gray-400 mb-2" />
                    <p className="text-sm text-gray-600 mb-1">
                      {formData.productionCapacityReport
                        ? formData.productionCapacityReport.name
                        : 'Üretim kapasite raporunu yükleyin'
                      }
                    </p>
                    <p className="text-xs text-gray-500">
                      PDF, DOC, DOCX, JPG, PNG formatları desteklenir
                    </p>
                  </div>
                </label>
                {formData.productionCapacityReport && (
                  <div className="mt-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                      <Check className="w-3 h-3 mr-1" />
                      Dosya yüklendi
                    </span>
                  </div>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-2">
                * Üretim kapasitesi, makine parkı ve teknik altyapı bilgilerini içeren resmi rapor
              </p>
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <FileText className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Fason Üretim ve Belgeler</h3>
            </div>

            {/* Fason Üretim */}
            <div>
              <h4 className="font-medium mb-3">Fason Üretim Hizmeti</h4>
              <div className="space-y-3">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.providesCustomManufacturing}
                    onChange={(e) => updateFormData('providesCustomManufacturing', e.target.checked)}
                    className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                  />
                  <span className="text-sm">Fason üretim hizmeti veriyorum</span>
                </label>

                {formData.providesCustomManufacturing && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Fason Üretim Detayları
                    </label>
                    <textarea
                      value={formData.customManufacturingDetails}
                      onChange={(e) => updateFormData('customManufacturingDetails', e.target.value)}
                      placeholder="Hangi tür fason üretim hizmetleri verdiğinizi açıklayınız"
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Belgeler */}
            <div>
              <h4 className="font-medium mb-3">Gerekli Belgeler</h4>
              <div className="space-y-3">
                {formData.documents.map((doc, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div className="flex items-center gap-3">
                      <FileText className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">{doc.type}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {doc.uploaded ? (
                        <span className="flex items-center gap-1 text-green-600 text-sm">
                          <Check className="w-4 h-4" />
                          {doc.fileName || 'Yüklendi'}
                        </span>
                      ) : (
                        <>
                          <input
                            type="file"
                            id={`document-${index}`}
                            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                            onChange={(e) => {
                              const file = e.target.files?.[0]
                              if (file) {
                                const newDocs = [...formData.documents]
                                newDocs[index].uploaded = true
                                newDocs[index].file = file
                                newDocs[index].fileName = file.name
                                updateFormData('documents', newDocs)
                              }
                            }}
                            className="hidden"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              document.getElementById(`document-${index}`)?.click()
                            }}
                          >
                            <Upload className="w-4 h-4 mr-1" />
                            Yükle
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-2">
                * Belgeler kayıt sonrası admin onayı için gereklidir
              </p>
            </div>
          </div>
        )

      case 6:
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Building className="w-5 h-5 text-amber-600" />
              <h3 className="text-lg font-semibold">Şirket Tanıtımı</h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Şirket Tanıtımı *
              </label>
              <textarea
                value={formData.companyDescription}
                onChange={(e) => updateFormData('companyDescription', e.target.value)}
                placeholder="Şirketinizi tanıtın: Hangi tür ürünler üretiyorsunuz, ne kadar süredir faaliyet gösteriyorsunuz, özel hizmetleriniz nelerdir, vb."
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Bu bilgiler müşterilerin sizi tanıması için kullanılacaktır.
              </p>
            </div>

            {/* Özet */}
            <div className="mt-6 p-4 bg-amber-50 rounded-lg">
              <h4 className="font-medium text-amber-800 mb-2">Kayıt Özeti</h4>
              <div className="text-sm text-amber-700 space-y-1">
                <p><strong>Şirket:</strong> {formData.companyName}</p>
                <p><strong>Yetkili:</strong> {formData.name} ({formData.position})</p>
                <p><strong>Kategoriler:</strong> {formData.productCategories.join(', ')}</p>
                <p><strong>Ocak Sayısı:</strong> {formData.quarries.filter(q => q.name).length}</p>
                <p><strong>Fabrika Sayısı:</strong> {formData.factories.filter(f => f.name).length}</p>
                <p><strong>Fason Üretim:</strong> {formData.providesCustomManufacturing ? 'Evet' : 'Hayır'}</p>
              </div>
            </div>
          </div>
        )

      // Diğer adımlar için placeholder
      default:
        return (
          <div className="text-center py-8">
            <p className="text-gray-600">Adım {currentStep} içeriği yakında eklenecektir.</p>
          </div>
        )
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Üretici Kayıt</h2>
            <p className="text-sm text-gray-600">
              Adım {currentStep} / {totalSteps}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-amber-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {renderStepContent()}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-white">
          <div className="flex gap-3">
            {currentStep > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
              >
                Geri
              </Button>
            )}
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              İptal
            </Button>
          </div>

          <div className="flex gap-3">
            {currentStep < totalSteps ? (
              <Button
                type="button"
                onClick={nextStep}
                className="bg-amber-600 hover:bg-amber-700"
              >
                İleri
              </Button>
            ) : (
              <Button
                type="submit"
                disabled={isLoading}
                onClick={handleSubmit}
                className="bg-amber-600 hover:bg-amber-700"
              >
                {isLoading ? 'Kayıt oluyor...' : 'Kayıt Tamamla'}
              </Button>
            )}
          </div>
        </div>

        {/* Switch to Login */}
        <div className="p-4 text-center border-t border-gray-200 bg-gray-50">
          <p className="text-sm text-gray-600">
            Zaten hesabınız var mı?{' '}
            <button
              onClick={onSwitchToLogin}
              className="text-amber-600 hover:text-amber-700 font-medium"
            >
              Giriş Yap
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
