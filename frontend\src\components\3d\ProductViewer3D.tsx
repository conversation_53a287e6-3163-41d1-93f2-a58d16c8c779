'use client';

import React, { Suspense, useRef, useEffect, useState } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import {
  OrbitControls,
  Environment,
  ContactShadows,
  Html,
  useProgress,
  Loader
} from '@react-three/drei';
import { 
  ViewerProps, 
  ViewerConfiguration, 
  Asset3D, 
  AssetQuality,
  LoadingProgress,
  ViewerError,
  CameraState,
  Annotation
} from '../../types/3d';
import { ViewerControls } from './ViewerControls';
import { AnnotationMarker } from './AnnotationMarker';
import { PerformanceMonitor } from './PerformanceMonitor';
import { ModelLoader } from './ModelLoader';

export const ProductViewer3D: React.FC<ViewerProps> = ({
  productId,
  assets,
  configuration,
  materials = [],
  className = '',
  width = 800,
  height = 600,
  onLoad,
  onError,
  onProgress,
  onCameraChange,
  onAnnotationClick,
  enableControls = true,
  enableAnnotations = true,
  enablePerformanceMonitor = false,
  quality = AssetQuality.HIGH
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<ViewerError | null>(null);
  const [currentQuality, setCurrentQuality] = useState(quality);
  const [showWireframe, setShowWireframe] = useState(false);
  const [showAnnotations, setShowAnnotations] = useState(enableAnnotations);
  const [autoRotate, setAutoRotate] = useState(configuration?.autoRotate || false);
  const [selectedMaterial, setSelectedMaterial] = useState<string | undefined>();

  // Get the main 3D model asset
  const modelAsset = assets.find(asset => asset.type === 'MODEL_3D');
  
  // Get texture assets
  const textureAssets = assets.filter(asset => asset.type === 'TEXTURE');

  useEffect(() => {
    if (!modelAsset) {
      setError({
        code: 'NO_MODEL',
        message: 'No 3D model found for this product',
        recoverable: false
      });
    }
  }, [modelAsset]);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = (error: ViewerError) => {
    setError(error);
    setIsLoading(false);
    onError?.(error);
  };

  const handleProgress = (progress: LoadingProgress) => {
    onProgress?.(progress);
  };

  const handleCameraChange = (camera: CameraState) => {
    onCameraChange?.(camera);
  };

  const handleQualityChange = (newQuality: AssetQuality) => {
    setCurrentQuality(newQuality);
  };

  const handleMaterialChange = (materialId: string) => {
    setSelectedMaterial(materialId);
  };

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 ${className}`} style={{ width, height }}>
        <div className="text-center p-6">
          <div className="text-red-600 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">3D Viewer Error</h3>
          <p className="text-gray-600 mb-4">{error.message}</p>
          {error.recoverable && (
            <button
              onClick={() => setError(null)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} style={{ width, height }}>
      {/* 3D Canvas */}
      <Canvas
        camera={{
          position: configuration?.cameraPosition ? 
            [configuration.cameraPosition.x, configuration.cameraPosition.y, configuration.cameraPosition.z] : 
            [5, 5, 5],
          fov: configuration?.cameraFov || 75
        }}
        shadows={configuration?.enableShadows !== false}
        gl={{ 
          antialias: configuration?.enableAntialiasing !== false,
          pixelRatio: configuration?.pixelRatio || Math.min(window.devicePixelRatio, 2)
        }}
        style={{ background: configuration?.backgroundColor || '#f0f0f0' }}
      >
        {/* Lighting */}
        <ambientLight 
          color={configuration?.ambientLightColor || '#ffffff'} 
          intensity={configuration?.ambientLightIntensity || 0.4} 
        />
        <directionalLight
          color={configuration?.directionalLightColor || '#ffffff'}
          intensity={configuration?.directionalLightIntensity || 1.0}
          position={configuration?.directionalLightPosition ? 
            [configuration.directionalLightPosition.x, configuration.directionalLightPosition.y, configuration.directionalLightPosition.z] :
            [10, 10, 5]
          }
          castShadow={configuration?.enableShadows !== false}
          shadow-mapSize-width={configuration?.shadowMapSize || 1024}
          shadow-mapSize-height={configuration?.shadowMapSize || 1024}
        />

        {/* Environment */}
        {configuration?.backgroundType === 'environment' && (
          <Environment preset="sunset" />
        )}

        {/* Controls */}
        {enableControls && (
          <OrbitControls
            enabled={configuration?.enableOrbitControls !== false}
            enableZoom={configuration?.enableZoom !== false}
            enablePan={configuration?.enablePan !== false}
            enableRotate={configuration?.enableRotate !== false}
            autoRotate={autoRotate}
            autoRotateSpeed={configuration?.autoRotateSpeed || 2.0}
            target={configuration?.cameraTarget ? 
              [configuration.cameraTarget.x, configuration.cameraTarget.y, configuration.cameraTarget.z] :
              [0, 0, 0]
            }
            onChange={(e) => {
              if (e?.target) {
                const camera = e.target.object;
                handleCameraChange({
                  position: { x: camera.position.x, y: camera.position.y, z: camera.position.z },
                  target: { x: 0, y: 0, z: 0 }, // OrbitControls target
                  zoom: camera.zoom || 1,
                  fov: camera.fov || 75
                });
              }
            }}
          />
        )}

        {/* 3D Model */}
        <Suspense fallback={<ModelLoadingFallback />}>
          {modelAsset && (
            <ModelLoader
              asset={modelAsset}
              quality={currentQuality}
              wireframe={showWireframe}
              material={selectedMaterial}
              materials={materials}
              onLoad={handleLoad}
              onError={handleError}
              onProgress={handleProgress}
            />
          )}
        </Suspense>

        {/* Annotations */}
        {showAnnotations && configuration?.annotations?.map((annotation) => (
          <AnnotationMarker
            key={annotation.id}
            annotation={annotation}
            onClick={onAnnotationClick}
            visible={annotation.visible}
          />
        ))}

        {/* Ground/Stage */}
        <ContactShadows 
          position={[0, -1, 0]} 
          opacity={0.4} 
          scale={10} 
          blur={2} 
          far={4} 
        />

        {/* Performance Monitor */}
        {enablePerformanceMonitor && (
          <PerformanceMonitor />
        )}
      </Canvas>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading 3D model...</p>
            <Loader />
          </div>
        </div>
      )}

      {/* Controls Panel */}
      {enableControls && !isLoading && (
        <div className="absolute top-4 right-4 z-20">
          <ViewerControls
            onCameraReset={() => {
              // Reset camera to default position
              // This would be implemented with a ref to OrbitControls
            }}
            onToggleWireframe={() => setShowWireframe(!showWireframe)}
            onToggleAnnotations={() => setShowAnnotations(!showAnnotations)}
            onToggleAutoRotate={() => setAutoRotate(!autoRotate)}
            onQualityChange={handleQualityChange}
            onMaterialChange={handleMaterialChange}
            materials={materials}
            currentQuality={currentQuality}
            currentMaterial={selectedMaterial}
            showWireframe={showWireframe}
            showAnnotations={showAnnotations}
            autoRotate={autoRotate}
          />
        </div>
      )}
    </div>
  );
};

// Loading fallback component for Suspense
const ModelLoadingFallback: React.FC = () => {
  const { progress } = useProgress();
  
  return (
    <Html center>
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-2"></div>
        <div className="text-sm text-gray-600">
          Loading... {Math.round(progress)}%
        </div>
      </div>
    </Html>
  );
};

export default ProductViewer3D;
