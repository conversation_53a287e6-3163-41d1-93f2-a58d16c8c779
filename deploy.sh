#!/bin/bash

# Natural Stone Marketplace - Production Deployment Script
# Türkiye Doğal Taş Pazaryeri - Production Deployment

set -e

# Configuration
PROJECT_NAME="natural-stone-marketplace"
DEPLOY_USER="deploy"
DEPLOY_PATH="/var/www/$PROJECT_NAME"
BACKUP_PATH="/var/backups/$PROJECT_NAME"
NODE_VERSION="18"
PM2_APP_NAME="natural-stone-marketplace"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check if running as root
check_root() {
    if [ "$EUID" -eq 0 ]; then
        error "Please do not run this script as root. Use a deploy user instead."
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed. Please install Node.js $NODE_VERSION first."
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        error "npm is not installed. Please install npm first."
    fi
    
    # Check PM2
    if ! command -v pm2 &> /dev/null; then
        warning "PM2 is not installed. Installing PM2..."
        npm install -g pm2
    fi
    
    # Check PostgreSQL
    if ! command -v psql &> /dev/null; then
        warning "PostgreSQL client is not installed. Please install postgresql-client."
    fi
    
    log "System requirements check completed."
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_DIR="$BACKUP_PATH/$TIMESTAMP"
    
    mkdir -p "$BACKUP_DIR"
    
    # Backup application files
    if [ -d "$DEPLOY_PATH" ]; then
        cp -r "$DEPLOY_PATH" "$BACKUP_DIR/app"
        log "Application files backed up to $BACKUP_DIR/app"
    fi
    
    # Backup database
    if command -v pg_dump &> /dev/null; then
        pg_dump -h localhost -U postgres natural_stone_marketplace_prod > "$BACKUP_DIR/database.sql"
        log "Database backed up to $BACKUP_DIR/database.sql"
    fi
    
    # Backup uploads
    if [ -d "$DEPLOY_PATH/backend/uploads" ]; then
        cp -r "$DEPLOY_PATH/backend/uploads" "$BACKUP_DIR/uploads"
        log "Uploads backed up to $BACKUP_DIR/uploads"
    fi
    
    log "Backup completed: $BACKUP_DIR"
}

# Setup directories
setup_directories() {
    log "Setting up directories..."
    
    mkdir -p "$DEPLOY_PATH"
    mkdir -p "$BACKUP_PATH"
    mkdir -p "$DEPLOY_PATH/backend/uploads"
    mkdir -p "$DEPLOY_PATH/backend/logs"
    
    # Set permissions
    chmod 755 "$DEPLOY_PATH"
    chmod 755 "$DEPLOY_PATH/backend/uploads"
    chmod 755 "$DEPLOY_PATH/backend/logs"
    
    log "Directories setup completed."
}

# Deploy backend
deploy_backend() {
    log "Deploying backend..."
    
    cd "$DEPLOY_PATH"
    
    # Copy backend files
    cp -r ./backend/* "$DEPLOY_PATH/backend/" 2>/dev/null || true
    
    cd "$DEPLOY_PATH/backend"
    
    # Install dependencies
    log "Installing backend dependencies..."
    npm ci --production
    
    # Generate Prisma client
    log "Generating Prisma client..."
    npx prisma generate
    
    # Run database migrations
    log "Running database migrations..."
    npx prisma migrate deploy
    
    # Build TypeScript
    log "Building TypeScript..."
    npm run build
    
    log "Backend deployment completed."
}

# Deploy frontend
deploy_frontend() {
    log "Deploying frontend..."
    
    cd "$DEPLOY_PATH"
    
    # Copy frontend files
    cp -r ./frontend/* "$DEPLOY_PATH/frontend/" 2>/dev/null || true
    
    cd "$DEPLOY_PATH/frontend"
    
    # Install dependencies
    log "Installing frontend dependencies..."
    npm ci
    
    # Build React app
    log "Building React application..."
    npm run build
    
    # Copy build to nginx directory
    if [ -d "/var/www/html" ]; then
        sudo cp -r build/* /var/www/html/
        log "Frontend build copied to nginx directory."
    fi
    
    log "Frontend deployment completed."
}

# Configure PM2
configure_pm2() {
    log "Configuring PM2..."
    
    cd "$DEPLOY_PATH/backend"
    
    # Create PM2 ecosystem file
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '$PM2_APP_NAME',
    script: 'dist/index.js',
    cwd: '$DEPLOY_PATH/backend',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 8001
    },
    error_file: '$DEPLOY_PATH/backend/logs/error.log',
    out_file: '$DEPLOY_PATH/backend/logs/out.log',
    log_file: '$DEPLOY_PATH/backend/logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'uploads'],
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
EOF
    
    log "PM2 configuration completed."
}

# Start services
start_services() {
    log "Starting services..."
    
    cd "$DEPLOY_PATH/backend"
    
    # Stop existing PM2 processes
    pm2 stop $PM2_APP_NAME 2>/dev/null || true
    pm2 delete $PM2_APP_NAME 2>/dev/null || true
    
    # Start application with PM2
    pm2 start ecosystem.config.js
    
    # Save PM2 configuration
    pm2 save
    
    # Setup PM2 startup
    pm2 startup
    
    log "Services started successfully."
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."
    
    # Install PM2 monitoring
    pm2 install pm2-logrotate
    
    # Configure log rotation
    pm2 set pm2-logrotate:max_size 10M
    pm2 set pm2-logrotate:retain 30
    pm2 set pm2-logrotate:compress true
    
    # Create health check script
    cat > /usr/local/bin/health-check.sh << 'EOF'
#!/bin/bash
# Health check script for Natural Stone Marketplace

APP_URL="http://localhost:8001/health"
EMAIL="<EMAIL>"

# Check application health
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $APP_URL)

if [ "$HTTP_STATUS" != "200" ]; then
    echo "❌ Application health check failed. HTTP Status: $HTTP_STATUS"
    # Restart application
    pm2 restart natural-stone-marketplace
    # Send notification (requires mail command)
    # echo "Application health check failed. Restarted automatically." | mail -s "Health Check Alert" $EMAIL
else
    echo "✅ Application is healthy. HTTP Status: $HTTP_STATUS"
fi
EOF
    
    chmod +x /usr/local/bin/health-check.sh
    
    # Add health check to cron
    (crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/health-check.sh") | crontab -
    
    log "Monitoring setup completed."
}

# Setup security
setup_security() {
    log "Setting up security..."
    
    # Set file permissions
    find "$DEPLOY_PATH" -type f -name "*.js" -exec chmod 644 {} \;
    find "$DEPLOY_PATH" -type f -name "*.json" -exec chmod 644 {} \;
    find "$DEPLOY_PATH" -type d -exec chmod 755 {} \;
    
    # Secure sensitive files
    chmod 600 "$DEPLOY_PATH/backend/.env.production"
    
    # Create security headers script
    cat > /usr/local/bin/security-check.sh << 'EOF'
#!/bin/bash
# Security check script

# Check for suspicious files
find /var/www -name "*.php" -o -name "*.asp" -o -name "*.jsp" | while read file; do
    echo "⚠️ Suspicious file found: $file"
done

# Check file permissions
find /var/www -type f -perm /o+w | while read file; do
    echo "⚠️ World-writable file found: $file"
done
EOF
    
    chmod +x /usr/local/bin/security-check.sh
    
    log "Security setup completed."
}

# Main deployment function
main() {
    log "🚀 Starting Natural Stone Marketplace deployment..."
    
    check_root
    check_requirements
    create_backup
    setup_directories
    deploy_backend
    deploy_frontend
    configure_pm2
    start_services
    setup_monitoring
    setup_security
    
    log "✅ Deployment completed successfully!"
    
    info "📋 Post-deployment checklist:"
    info "1. Update DNS records to point to this server"
    info "2. Configure SSL certificates (run ssl-setup.sh)"
    info "3. Update environment variables in .env.production"
    info "4. Test the application: http://your-domain.com"
    info "5. Monitor logs: pm2 logs $PM2_APP_NAME"
    info "6. Check PM2 status: pm2 status"
    
    info "🔧 Useful commands:"
    info "- View logs: pm2 logs $PM2_APP_NAME"
    info "- Restart app: pm2 restart $PM2_APP_NAME"
    info "- Monitor app: pm2 monit"
    info "- Health check: curl http://localhost:8001/health"
}

# Run main function
main "$@"
