/**
 * Optimization Service
 * Handles 3D model and texture optimization for web delivery
 */

import { promises as fs } from 'fs';
import path from 'path';
import sharp from 'sharp';
import { spawn } from 'child_process';
import { 
  Asset3D, 
  AssetQuality, 
  ModelOptimizationResult,
  TextureOptimizationResult,
  AssetFormat
} from '../types';

export class OptimizationService {
  private gltfPipelinePath: string;
  private dracoEncoderPath: string;

  constructor() {
    this.gltfPipelinePath = process.env.GLTF_PIPELINE_PATH || 'gltf-pipeline';
    this.dracoEncoderPath = process.env.DRACO_ENCODER_PATH || 'draco_encoder';
  }

  /**
   * Optimize 3D model for web delivery
   */
  async optimizeModel(
    inputPath: string,
    outputDir: string,
    options: {
      qualities: AssetQuality[];
      enableDraco: boolean;
      generateLOD: boolean;
      targetTriangles?: Record<AssetQuality, number>;
    }
  ): Promise<ModelOptimizationResult> {
    const startTime = Date.now();
    const originalStats = await fs.stat(inputPath);
    const variants: any[] = [];

    // Default triangle targets for each quality level
    const defaultTargets = {
      [AssetQuality.LOW]: 5000,
      [AssetQuality.MEDIUM]: 15000,
      [AssetQuality.HIGH]: 50000,
      [AssetQuality.ULTRA]: 150000
    };

    const triangleTargets = options.targetTriangles || defaultTargets;

    for (const quality of options.qualities) {
      try {
        const variant = await this.createModelVariant(
          inputPath,
          outputDir,
          quality,
          triangleTargets[quality],
          options.enableDraco
        );
        variants.push(variant);
      } catch (error) {
        console.error(`Failed to create ${quality} variant:`, error);
      }
    }

    const processingTime = Date.now() - startTime;
    const totalOptimizedSize = variants.reduce((sum, v) => sum + v.fileSize, 0);

    return {
      originalSize: originalStats.size,
      optimizedSize: totalOptimizedSize,
      compressionRatio: originalStats.size / (totalOptimizedSize || 1),
      originalVertices: await this.getModelVertexCount(inputPath),
      optimizedVertices: variants.length > 0 ? variants[0].vertices : 0,
      processingTime,
      variants
    };
  }

  /**
   * Create optimized model variant
   */
  private async createModelVariant(
    inputPath: string,
    outputDir: string,
    quality: AssetQuality,
    targetTriangles: number,
    enableDraco: boolean
  ): Promise<any> {
    const fileName = `${path.parse(inputPath).name}_${quality.toLowerCase()}.glb`;
    const outputPath = path.join(outputDir, fileName);

    // Step 1: Simplify geometry if needed
    let processedPath = inputPath;
    if (quality !== AssetQuality.ULTRA) {
      processedPath = await this.simplifyGeometry(inputPath, outputDir, targetTriangles, quality);
    }

    // Step 2: Apply Draco compression if enabled
    if (enableDraco) {
      processedPath = await this.applyDracoCompression(processedPath, outputDir, quality);
    }

    // Step 3: Optimize with gltf-pipeline
    await this.optimizeWithGltfPipeline(processedPath, outputPath);

    // Get final stats
    const stats = await fs.stat(outputPath);
    const vertexCount = await this.getModelVertexCount(outputPath);

    return {
      quality,
      fileName,
      filePath: outputPath,
      fileSize: stats.size,
      vertices: vertexCount,
      faces: Math.floor(vertexCount / 3),
      lodLevel: this.getLODLevel(quality),
      compressionRatio: (await fs.stat(inputPath)).size / stats.size
    };
  }

  /**
   * Simplify geometry using external tools
   */
  private async simplifyGeometry(
    inputPath: string,
    outputDir: string,
    targetTriangles: number,
    quality: AssetQuality
  ): Promise<string> {
    const outputPath = path.join(outputDir, `temp_simplified_${quality.toLowerCase()}.glb`);
    
    // For now, we'll copy the file as-is
    // In a real implementation, you would use tools like:
    // - Meshlab for mesh simplification
    // - Blender headless mode
    // - Custom mesh processing libraries
    
    await fs.copyFile(inputPath, outputPath);
    return outputPath;
  }

  /**
   * Apply Draco compression
   */
  private async applyDracoCompression(
    inputPath: string,
    outputDir: string,
    quality: AssetQuality
  ): Promise<string> {
    const outputPath = path.join(outputDir, `temp_draco_${quality.toLowerCase()}.glb`);
    
    return new Promise((resolve, reject) => {
      const args = [
        '--input', inputPath,
        '--output', outputPath,
        '--compression-level', this.getDracoCompressionLevel(quality).toString()
      ];

      const process = spawn(this.dracoEncoderPath, args);
      
      process.on('close', (code) => {
        if (code === 0) {
          resolve(outputPath);
        } else {
          // Fallback: copy original file if Draco fails
          fs.copyFile(inputPath, outputPath)
            .then(() => resolve(outputPath))
            .catch(reject);
        }
      });

      process.on('error', () => {
        // Fallback: copy original file if Draco is not available
        fs.copyFile(inputPath, outputPath)
          .then(() => resolve(outputPath))
          .catch(reject);
      });
    });
  }

  /**
   * Optimize with gltf-pipeline
   */
  private async optimizeWithGltfPipeline(inputPath: string, outputPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const args = [
        '--input', inputPath,
        '--output', outputPath,
        '--binary',
        '--separate-textures'
      ];

      const process = spawn(this.gltfPipelinePath, args);
      
      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          // Fallback: copy original file if gltf-pipeline fails
          fs.copyFile(inputPath, outputPath)
            .then(() => resolve())
            .catch(reject);
        }
      });

      process.on('error', () => {
        // Fallback: copy original file if gltf-pipeline is not available
        fs.copyFile(inputPath, outputPath)
          .then(() => resolve())
          .catch(reject);
      });
    });
  }

  /**
   * Optimize textures for web delivery
   */
  async optimizeTextures(
    inputPath: string,
    outputDir: string,
    options: {
      qualities: AssetQuality[];
      formats: AssetFormat[];
      generateMipmaps: boolean;
    }
  ): Promise<TextureOptimizationResult> {
    const originalStats = await fs.stat(inputPath);
    const metadata = await sharp(inputPath).metadata();
    const originalResolution = {
      width: metadata.width || 1024,
      height: metadata.height || 1024
    };

    const variants: any[] = [];

    for (const quality of options.qualities) {
      for (const format of options.formats) {
        try {
          const variant = await this.createTextureVariant(
            inputPath,
            outputDir,
            quality,
            format,
            options.generateMipmaps
          );
          variants.push(variant);
        } catch (error) {
          console.error(`Failed to create ${quality} ${format} variant:`, error);
        }
      }
    }

    const totalOptimizedSize = variants.reduce((sum, v) => sum + v.fileSize, 0);

    return {
      originalSize: originalStats.size,
      optimizedSize: totalOptimizedSize,
      compressionRatio: originalStats.size / (totalOptimizedSize || 1),
      originalResolution,
      optimizedResolution: variants.length > 0 ? 
        { width: variants[0].width, height: variants[0].height } : 
        originalResolution,
      format: variants.length > 0 ? variants[0].format : AssetFormat.WEBP,
      variants
    };
  }

  /**
   * Create texture variant
   */
  private async createTextureVariant(
    inputPath: string,
    outputDir: string,
    quality: AssetQuality,
    format: AssetFormat,
    generateMipmaps: boolean
  ): Promise<any> {
    const resolution = this.getTextureResolution(quality);
    const compressionQuality = this.getTextureQuality(quality);
    const extension = format.toLowerCase();
    const fileName = `${path.parse(inputPath).name}_${quality.toLowerCase()}.${extension}`;
    const outputPath = path.join(outputDir, fileName);

    let pipeline = sharp(inputPath)
      .resize(resolution, resolution, { 
        fit: 'cover',
        withoutEnlargement: true 
      });

    // Apply format-specific optimizations
    switch (format) {
      case AssetFormat.WEBP:
        pipeline = pipeline.webp({ 
          quality: compressionQuality,
          effort: 6 
        });
        break;
      case AssetFormat.JPG:
        pipeline = pipeline.jpeg({ 
          quality: compressionQuality,
          progressive: true 
        });
        break;
      case AssetFormat.PNG:
        pipeline = pipeline.png({ 
          compressionLevel: 9,
          progressive: true 
        });
        break;
      default:
        pipeline = pipeline.webp({ quality: compressionQuality });
    }

    await pipeline.toFile(outputPath);

    // Generate mipmaps if requested
    if (generateMipmaps) {
      await this.generateMipmaps(outputPath, outputDir, format);
    }

    const stats = await fs.stat(outputPath);
    const resultMetadata = await sharp(outputPath).metadata();

    return {
      quality,
      format,
      fileName,
      filePath: outputPath,
      fileSize: stats.size,
      width: resultMetadata.width,
      height: resultMetadata.height,
      compressionRatio: (await fs.stat(inputPath)).size / stats.size
    };
  }

  /**
   * Generate mipmaps for texture
   */
  private async generateMipmaps(
    texturePath: string,
    outputDir: string,
    format: AssetFormat
  ): Promise<void> {
    const baseName = path.parse(texturePath).name;
    const extension = format.toLowerCase();
    const metadata = await sharp(texturePath).metadata();
    
    let currentWidth = metadata.width || 1024;
    let currentHeight = metadata.height || 1024;
    let level = 1;

    while (currentWidth > 1 && currentHeight > 1) {
      currentWidth = Math.max(1, Math.floor(currentWidth / 2));
      currentHeight = Math.max(1, Math.floor(currentHeight / 2));
      
      const mipmapPath = path.join(outputDir, `${baseName}_mip${level}.${extension}`);
      
      await sharp(texturePath)
        .resize(currentWidth, currentHeight)
        .toFile(mipmapPath);
      
      level++;
    }
  }

  /**
   * Get model vertex count (simplified estimation)
   */
  private async getModelVertexCount(modelPath: string): Promise<number> {
    // This is a simplified estimation
    // In a real implementation, you would parse the model file
    const stats = await fs.stat(modelPath);
    return Math.floor(stats.size / 100); // Rough estimation
  }

  /**
   * Get texture resolution for quality level
   */
  private getTextureResolution(quality: AssetQuality): number {
    switch (quality) {
      case AssetQuality.LOW: return 256;
      case AssetQuality.MEDIUM: return 512;
      case AssetQuality.HIGH: return 1024;
      case AssetQuality.ULTRA: return 2048;
      default: return 1024;
    }
  }

  /**
   * Get texture compression quality
   */
  private getTextureQuality(quality: AssetQuality): number {
    switch (quality) {
      case AssetQuality.LOW: return 60;
      case AssetQuality.MEDIUM: return 75;
      case AssetQuality.HIGH: return 85;
      case AssetQuality.ULTRA: return 95;
      default: return 85;
    }
  }

  /**
   * Get Draco compression level
   */
  private getDracoCompressionLevel(quality: AssetQuality): number {
    switch (quality) {
      case AssetQuality.LOW: return 10;
      case AssetQuality.MEDIUM: return 7;
      case AssetQuality.HIGH: return 5;
      case AssetQuality.ULTRA: return 3;
      default: return 7;
    }
  }

  /**
   * Get LOD level for quality
   */
  private getLODLevel(quality: AssetQuality): number {
    switch (quality) {
      case AssetQuality.LOW: return 0;
      case AssetQuality.MEDIUM: return 1;
      case AssetQuality.HIGH: return 2;
      case AssetQuality.ULTRA: return 3;
      default: return 2;
    }
  }

  /**
   * Clean up temporary files
   */
  async cleanupTempFiles(directory: string): Promise<void> {
    try {
      const files = await fs.readdir(directory);
      const tempFiles = files.filter(file => file.startsWith('temp_'));
      
      for (const file of tempFiles) {
        await fs.unlink(path.join(directory, file));
      }
    } catch (error) {
      console.error('Error cleaning up temp files:', error);
    }
  }

  /**
   * Estimate optimization time
   */
  estimateOptimizationTime(
    fileSize: number,
    qualities: AssetQuality[],
    enableDraco: boolean
  ): number {
    // Base time in seconds per MB
    let baseTime = fileSize / (1024 * 1024) * 10;
    
    // Multiply by number of quality variants
    baseTime *= qualities.length;
    
    // Add extra time for Draco compression
    if (enableDraco) {
      baseTime *= 1.5;
    }
    
    return Math.ceil(baseTime);
  }
}
