'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Search,
  Filter,
  Download,
  Eye,
  Calendar,
  DollarSign,
  User,
  Building,
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  FileText
} from 'lucide-react'

// Mock data - gerçek API'den gelecek
const mockPaymentHistory = [
  {
    id: 'pay_12345',
    orderId: 'ORD_001',
    customerName: 'Marble Export Ltd.',
    producerName: 'Afyon Mermer A.Ş.',
    amount: 15000,
    currency: 'USD',
    paymentMethod: 'bank_transfer',
    status: 'completed',
    escrowStatus: 'released',
    createdAt: '2025-01-02T10:30:00Z',
    completedAt: '2025-01-02T11:00:00Z',
    commission: 150,
    productName: 'Beyaz Mermer - Afyon',
    quantity: '150 m²'
  },
  {
    id: 'pay_12346',
    orderId: 'ORD_002',
    customerName: 'Stone Trading Co.',
    producerName: 'Denizli Traverten Ltd.',
    amount: 8500,
    currency: 'USD',
    paymentMethod: 'bank_transfer',
    status: 'pending',
    escrowStatus: 'held',
    createdAt: '2025-01-02T09:15:00Z',
    commission: 85,
    productName: 'Traverten - Denizli',
    quantity: '85 m²'
  },
  {
    id: 'pay_12347',
    orderId: 'ORD_003',
    customerName: 'Natural Stone Inc.',
    producerName: 'Çanakkale Granit San.',
    amount: 22000,
    currency: 'USD',
    paymentMethod: 'bank_transfer',
    status: 'completed',
    escrowStatus: 'released',
    createdAt: '2025-01-01T16:45:00Z',
    completedAt: '2025-01-02T08:30:00Z',
    commission: 220,
    productName: 'Granit - Çanakkale',
    quantity: '220 m²'
  },
  {
    id: 'pay_12348',
    orderId: 'ORD_004',
    customerName: 'Suspicious Trading Ltd.',
    producerName: 'Marmara Oniks A.Ş.',
    amount: 50000,
    currency: 'USD',
    paymentMethod: 'bank_transfer',
    status: 'failed',
    escrowStatus: 'refunded',
    createdAt: '2025-01-02T14:30:00Z',
    failedAt: '2025-01-02T15:00:00Z',
    commission: 0,
    productName: 'Oniks - Marmara',
    quantity: '500 m²',
    failureReason: 'Fraud detection - Suspicious activity'
  },
  {
    id: 'pay_12349',
    orderId: 'ORD_005',
    customerName: 'Quick Stone Co.',
    producerName: 'Bursa Mermer Ltd.',
    amount: 12000,
    currency: 'USD',
    paymentMethod: 'credit_card',
    status: 'completed',
    escrowStatus: 'released',
    createdAt: '2025-01-02T12:15:00Z',
    completedAt: '2025-01-02T12:45:00Z',
    commission: 120,
    productName: 'Beyaz Mermer - Bursa',
    quantity: '120 m²'
  }
]

const mockStats = {
  totalPayments: 125,
  totalAmount: 450000,
  completedPayments: 98,
  pendingPayments: 15,
  failedPayments: 12,
  totalCommission: 4500
}

export default function PaymentHistoryPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [paymentMethodFilter, setPaymentMethodFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [paymentHistory, setPaymentHistory] = useState(mockPaymentHistory)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<any>(null)

  const handleRefresh = async () => {
    setIsLoading(true)
    try {
      // API çağrısı simülasyonu
      await new Promise(resolve => setTimeout(resolve, 1000))
      // Veri yenileme işlemi burada yapılacak
      // Payment history refreshed successfully
    } catch (error) {
      // Error handling - could be logged to monitoring service
    } finally {
      setIsLoading(false)
    }
  }

  const handleExportData = () => {
    // CSV export işlemi
    const csvData = filteredPayments.map(payment => ({
      'Ödeme ID': payment.id,
      'Sipariş ID': payment.orderId,
      'Müşteri': payment.customerName,
      'Üretici': payment.producerName,
      'Tutar': `${payment.amount} ${payment.currency}`,
      'Ödeme Yöntemi': payment.paymentMethod,
      'Durum': payment.status,
      'Escrow Durumu': payment.escrowStatus,
      'Komisyon': payment.commission,
      'Tarih': new Date(payment.createdAt).toLocaleDateString('tr-TR')
    }))

    // CSV data prepared for export
    // CSV download işlemi burada yapılacak
  }

  const filteredPayments = paymentHistory.filter(payment => {
    const matchesSearch = payment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.producerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.orderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || payment.status === statusFilter
    const matchesPaymentMethod = paymentMethodFilter === 'all' || payment.paymentMethod === paymentMethodFilter

    // Date filtering
    if (dateFilter !== 'all') {
      const paymentDate = new Date(payment.createdAt)
      const now = new Date()
      const daysDiff = Math.floor((now.getTime() - paymentDate.getTime()) / (1000 * 60 * 60 * 24))

      switch (dateFilter) {
        case 'today':
          if (daysDiff !== 0) return false
          break
        case 'week':
          if (daysDiff > 7) return false
          break
        case 'month':
          if (daysDiff > 30) return false
          break
        case 'quarter':
          if (daysDiff > 90) return false
          break
      }
    }

    return matchesSearch && matchesStatus && matchesPaymentMethod
  })

  // Analytics hesaplamaları
  const analytics = {
    totalRevenue: filteredPayments.reduce((sum, p) => p.status === 'completed' ? sum + p.amount : sum, 0),
    totalCommission: filteredPayments.reduce((sum, p) => p.status === 'completed' ? sum + (p.commission || 0) : sum, 0),
    totalTransactions: filteredPayments.length,
    completedTransactions: filteredPayments.filter(p => p.status === 'completed').length,
    pendingTransactions: filteredPayments.filter(p => p.status === 'pending').length,
    failedTransactions: filteredPayments.filter(p => p.status === 'failed').length,
    refundedTransactions: filteredPayments.filter(p => p.status === 'refunded').length,
    bankTransfers: filteredPayments.filter(p => p.paymentMethod === 'bank_transfer').length,
    creditCards: filteredPayments.filter(p => p.paymentMethod === 'credit_card').length,
    averageTransactionValue: filteredPayments.length > 0 ?
      filteredPayments.reduce((sum, p) => sum + p.amount, 0) / filteredPayments.length : 0,
    successRate: filteredPayments.length > 0 ?
      (filteredPayments.filter(p => p.status === 'completed').length / filteredPayments.length) * 100 : 0
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Tamamlandı</Badge>
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Bekliyor</Badge>
      case 'failed':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Başarısız</Badge>
      case 'refunded':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">İade Edildi</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getEscrowStatusBadge = (status: string) => {
    switch (status) {
      case 'held':
        return <Badge variant="outline" className="text-blue-600">Beklemede</Badge>
      case 'released':
        return <Badge variant="outline" className="text-green-600">Serbest Bırakıldı</Badge>
      case 'refunded':
        return <Badge variant="outline" className="text-red-600">İade Edildi</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'bank_transfer':
        return <Building className="w-4 h-4" />
      case 'credit_card':
        return <CreditCard className="w-4 h-4" />
      default:
        return <DollarSign className="w-4 h-4" />
    }
  }

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'bank_transfer':
        return 'Banka Havalesi'
      case 'credit_card':
        return 'Kredi Kartı'
      case 'paypal':
        return 'PayPal'
      default:
        return method
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Ödeme Geçmişi</h1>
          <p className="text-gray-600 mt-1">
            Tüm ödeme işlemlerini görüntüleyin ve yönetin
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" onClick={handleExportData}>
            <Download className="w-4 h-4 mr-2" />
            Excel İndir
          </Button>
          <Button variant="outline" onClick={handleExportData}>
            <FileText className="w-4 h-4 mr-2" />
            PDF Rapor
          </Button>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Gelir</p>
              <p className="text-2xl font-bold text-gray-900">
                ${analytics.totalRevenue.toLocaleString()}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {filteredPayments.length} işlem
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Başarı Oranı</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.successRate.toFixed(1)}%</p>
              <p className="text-xs text-gray-500 mt-1">
                {analytics.completedTransactions} / {analytics.totalTransactions}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Ortalama İşlem</p>
              <p className="text-2xl font-bold text-gray-900">
                ${analytics.averageTransactionValue.toLocaleString()}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                İşlem başına
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Komisyon</p>
              <p className="text-2xl font-bold text-gray-900">
                ${analytics.totalCommission.toLocaleString()}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Müşteri, üretici, sipariş no veya ödeme ID ile ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tüm Durumlar</option>
              <option value="completed">Tamamlanan</option>
              <option value="pending">Bekleyen</option>
              <option value="failed">Başarısız</option>
              <option value="refunded">İade Edildi</option>
            </select>
            <select
              value={paymentMethodFilter}
              onChange={(e) => setPaymentMethodFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tüm Yöntemler</option>
              <option value="bank_transfer">Banka Havalesi</option>
              <option value="credit_card">Kredi Kartı</option>
              <option value="paypal">PayPal</option>
            </select>
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tüm Zamanlar</option>
              <option value="today">Bugün</option>
              <option value="week">Son 7 Gün</option>
              <option value="month">Son 30 Gün</option>
              <option value="quarter">Son 3 Ay</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Analytics Dashboard */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payment Method Distribution */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Ödeme Yöntemi Dağılımı</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Building className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-gray-600">Banka Havalesi</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full"
                    style={{ width: `${analytics.totalTransactions > 0 ? (analytics.bankTransfers / analytics.totalTransactions) * 100 : 0}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">{analytics.bankTransfers}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4 text-green-600" />
                <span className="text-sm text-gray-600">Kredi Kartı</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${analytics.totalTransactions > 0 ? (analytics.creditCards / analytics.totalTransactions) * 100 : 0}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">{analytics.creditCards}</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Transaction Status Distribution */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">İşlem Durumu Dağılımı</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm text-gray-600">Tamamlanan</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${analytics.totalTransactions > 0 ? (analytics.completedTransactions / analytics.totalTransactions) * 100 : 0}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">{analytics.completedTransactions}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <span className="text-sm text-gray-600">Bekleyen</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-yellow-500 h-2 rounded-full"
                    style={{ width: `${analytics.totalTransactions > 0 ? (analytics.pendingTransactions / analytics.totalTransactions) * 100 : 0}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">{analytics.pendingTransactions}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <XCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm text-gray-600">Başarısız</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-red-500 h-2 rounded-full"
                    style={{ width: `${analytics.totalTransactions > 0 ? (analytics.failedTransactions / analytics.totalTransactions) * 100 : 0}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">{analytics.failedTransactions}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <RefreshCw className="h-4 w-4 text-gray-600" />
                <span className="text-sm text-gray-600">İade Edildi</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gray-500 h-2 rounded-full"
                    style={{ width: `${analytics.totalTransactions > 0 ? (analytics.refundedTransactions / analytics.totalTransactions) * 100 : 0}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">{analytics.refundedTransactions}</span>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Payment History Table */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ödeme Bilgileri
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Taraflar
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ürün
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tutar
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Yöntem
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Durum
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tarih
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPayments.map((payment) => (
                <tr key={payment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 font-mono">
                        {payment.id}
                      </div>
                      <div className="text-sm text-gray-500">
                        Sipariş: {payment.orderId}
                      </div>
                      {payment.commission > 0 && (
                        <div className="text-xs text-green-600">
                          Komisyon: ${payment.commission}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="font-medium">Müşteri:</div>
                      <div className="text-gray-600">{payment.customerName}</div>
                      <div className="font-medium mt-1">Üretici:</div>
                      <div className="text-gray-600">{payment.producerName}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="font-medium">{payment.productName}</div>
                      <div className="text-gray-500">{payment.quantity}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      ${payment.amount.toLocaleString()} {payment.currency}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      {getPaymentMethodIcon(payment.paymentMethod)}
                      <span className="text-sm text-gray-900">
                        {getPaymentMethodText(payment.paymentMethod)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      {getStatusBadge(payment.status)}
                      {payment.escrowStatus && getEscrowStatusBadge(payment.escrowStatus)}
                    </div>
                    {payment.failureReason && (
                      <div className="text-xs text-red-600 mt-1">
                        {payment.failureReason}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      <div>
                        <div>{formatDate(payment.createdAt)}</div>
                        {payment.completedAt && (
                          <div className="text-xs text-green-600">
                            Tamamlandı: {formatDate(payment.completedAt)}
                          </div>
                        )}
                        {payment.failedAt && (
                          <div className="text-xs text-red-600">
                            Başarısız: {formatDate(payment.failedAt)}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-1" />
                        Detay
                      </Button>
                      {payment.status === 'failed' && (
                        <Button variant="outline" size="sm" className="text-blue-600">
                          <RefreshCw className="w-4 h-4 mr-1" />
                          Yeniden Dene
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-700">
          Toplam {filteredPayments.length} ödeme gösteriliyor
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" disabled>
            Önceki
          </Button>
          <Button variant="outline" size="sm" className="bg-blue-50 text-blue-600">
            1
          </Button>
          <Button variant="outline" size="sm">
            2
          </Button>
          <Button variant="outline" size="sm">
            3
          </Button>
          <Button variant="outline" size="sm">
            Sonraki
          </Button>
        </div>
      </div>
    </div>
  )
}
