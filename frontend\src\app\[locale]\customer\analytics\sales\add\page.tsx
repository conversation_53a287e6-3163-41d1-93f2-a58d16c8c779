'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function AddSalePage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    productName: '',
    thickness: '',
    width: '',
    length: '',
    surfaceFinish: '',
    customSurfaceFinish: '',
    companyName: '',
    phone: '',
    email: '',
    address: '',
    description: '',
    salePrice: '',
    saleDate: new Date().toISOString().split('T')[0],
    quantity: '',
    deliveryStatus: 'Bekliyor',
    deliveryReason: '',
    deliveryDate: '',
    deliveryReminder: false,
    deliveryReminderDate: '',
    paymentStatus: 'Bekliyor',
    paymentMethod: '',
    paidAmount: '',
    remainingAmount: '',
    installmentCount: '',
    installmentDates: '',
    checkCount: '',
    checkDetails: [],
    category: '',
    customCategory: '',
    orderNumber: ''
  });

  const surfaceFinishes = [
    'C<PERSON>lı', 'Mat', 'Antik', 'Fır<PERSON>lı', 'Alevli', 'Doğal', 'Sandblast', 'Diğer'
  ];

  const categories = [
    'Mermer', 'Granit', 'Traverten', 'Oniks', 'Kuvarsit', 'Diğer'
  ];

  const deliveryStatuses = [
    'Bekliyor', 'Hazırlanıyor', 'Kargoda', 'Teslim Edildi'
  ];

  const paymentStatuses = [
    'Bekliyor', 'Ödendi', 'Kısmi Ödendi', 'Gecikmiş'
  ];

  const paymentMethods = [
    'Nakit', 'Banka Havalesi', 'Kredi Kartı', 'Çek'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => {
        const newData = {
          ...prev,
          [name]: value
        };

        // Kısmi ödendi seçildiğinde kalan tutarı hesapla
        if (name === 'paymentStatus' && value === 'Kısmi Ödendi') {
          newData.paidAmount = '';
          newData.remainingAmount = '';
        }

        // Ödenen tutar değiştiğinde kalan tutarı hesapla
        if (name === 'paidAmount' && prev.salePrice && prev.paymentStatus === 'Kısmi Ödendi') {
          const remaining = parseFloat(prev.salePrice) - parseFloat(value || '0');
          newData.remainingAmount = remaining > 0 ? remaining.toString() : '0';
        }

        // Çek sayısı değiştiğinde çek detaylarını güncelle
        if (name === 'checkCount' && value) {
          const count = parseInt(value);
          const newCheckDetails = [];
          for (let i = 0; i < count; i++) {
            newCheckDetails.push({
              checkNumber: '',
              amount: '',
              dueDate: '',
              reminder: false,
              reminderDate: ''
            });
          }
          newData.checkDetails = newCheckDetails;
        }

        // Ödeme yöntemi değiştiğinde çek detaylarını temizle
        if (name === 'paymentMethod' && value !== 'Çek') {
          newData.checkCount = '';
          newData.checkDetails = [];
        }

        return newData;
      });
    }
  };

  const handleCheckDetailChange = (index: number, field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      checkDetails: prev.checkDetails.map((check, i) =>
        i === index ? { ...check, [field]: value } : check
      )
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Çek validasyonu
    if (formData.paymentMethod === 'Çek') {
      const invalidChecks = formData.checkDetails.filter(check =>
        !check.amount || !check.dueDate || (check.reminder && check.reminderDate && new Date(check.reminderDate) > new Date(check.dueDate))
      );

      if (invalidChecks.length > 0) {
        alert('Lütfen tüm çek bilgilerini eksiksiz doldurun. Hatırlatma tarihi vade tarihinden sonra olamaz.');
        return;
      }
    }

    try {
      // API call will be here
      console.log('Satış kaydı:', formData);

      // Success message and redirect
      alert('Satış kaydı başarıyla eklendi!');
      router.push('/customer/analytics/sales');
    } catch (error) {
      console.error('Satış kaydı eklenirken hata:', error);
      alert('Satış kaydı eklenirken bir hata oluştu.');
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Yeni Satış Kaydı</h1>
            <p className="text-gray-600 mt-1">Satış bilgilerini ekleyin</p>
          </div>
          <Link
            href="/customer/analytics/sales"
            className="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            ← Geri Dön
          </Link>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        
        {/* Ürün Bilgileri */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Ürün Bilgileri</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ürün Adı *
              </label>
              <input
                type="text"
                name="productName"
                value={formData.productName}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Örn: Beyaz Mermer"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kategori *
              </label>
              <select
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Kategori Seçin</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>

              {formData.category === 'Diğer' && (
                <div className="mt-2">
                  <input
                    type="text"
                    name="customCategory"
                    value={formData.customCategory}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Kategori adını yazın"
                  />
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Yüzey İşlemi *
              </label>
              <select
                name="surfaceFinish"
                value={formData.surfaceFinish}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Yüzey İşlemi Seçin</option>
                {surfaceFinishes.map(finish => (
                  <option key={finish} value={finish}>{finish}</option>
                ))}
              </select>

              {formData.surfaceFinish === 'Diğer' && (
                <div className="mt-2">
                  <input
                    type="text"
                    name="customSurfaceFinish"
                    value={formData.customSurfaceFinish}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Yüzey işlemi adını yazın"
                  />
                </div>
              )}
            </div>

          </div>
        </div>

        {/* Ebat Bilgileri */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Ebat Bilgileri</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kalınlık (cm) *
              </label>
              <input
                type="number"
                name="thickness"
                value={formData.thickness}
                onChange={handleInputChange}
                required
                step="0.1"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="2.0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                En (cm) *
              </label>
              <input
                type="number"
                name="width"
                value={formData.width}
                onChange={handleInputChange}
                required
                step="0.1"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="60"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Boy (cm) *
              </label>
              <input
                type="number"
                name="length"
                value={formData.length}
                onChange={handleInputChange}
                required
                step="0.1"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="120"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Miktar (m²) *
              </label>
              <input
                type="number"
                name="quantity"
                value={formData.quantity}
                onChange={handleInputChange}
                required
                step="0.01"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="10.5"
              />
            </div>

          </div>
        </div>

        {/* Firma Bilgileri */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Satılan Firma Bilgileri</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Firma İsmi *
              </label>
              <input
                type="text"
                name="companyName"
                value={formData.companyName}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Firma adını girin"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Telefon
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0532 123 45 67"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Adres
              </label>
              <textarea
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Firma adresi (isteğe bağlı)"
              />
            </div>

          </div>
        </div>

        {/* Satış Detayları */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Satış Detayları</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Satış Fiyatı (USD) *
              </label>
              <input
                type="number"
                name="salePrice"
                value={formData.salePrice}
                onChange={handleInputChange}
                required
                step="0.01"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="1500.00"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Satış Tarihi *
              </label>
              <input
                type="date"
                name="saleDate"
                value={formData.saleDate}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sipariş Numarası
              </label>
              <input
                type="text"
                name="orderNumber"
                value={formData.orderNumber}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="SIP-2024-001"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Teslimat Durumu
              </label>
              <select
                name="deliveryStatus"
                value={formData.deliveryStatus}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {deliveryStatuses.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>

              {formData.deliveryStatus === 'Bekliyor' && (
                <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bekleme Nedeni
                  </label>
                  <textarea
                    name="deliveryReason"
                    value={formData.deliveryReason}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Teslimatın neden bekletildiğini açıklayın..."
                  />

                  <div className="mt-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        name="deliveryReminder"
                        checked={formData.deliveryReminder}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label className="ml-2 text-sm text-gray-700">
                        Hatırlatma bildirimi gönder
                      </label>
                    </div>

                    {formData.deliveryReminder && (
                      <div className="mt-3">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Hatırlatma Tarihi *
                        </label>
                        <input
                          type="date"
                          name="deliveryReminderDate"
                          value={formData.deliveryReminderDate}
                          onChange={handleInputChange}
                          required
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Bu tarihte teslimat durumu hakkında hatırlatma bildirimi alacaksınız
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {formData.deliveryStatus === 'Hazırlanıyor' && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Planlanan Teslimat Tarihi *
                  </label>
                  <input
                    type="date"
                    name="deliveryDate"
                    value={formData.deliveryDate}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />

                  <div className="mt-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        name="deliveryReminder"
                        checked={formData.deliveryReminder}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label className="ml-2 text-sm text-gray-700">
                        Teslimat günü hatırlatma bildirimi gönder
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Seçilen teslimat tarihinde otomatik hatırlatma bildirimi gönderilecek
                    </p>
                  </div>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ödeme Durumu
              </label>
              <select
                name="paymentStatus"
                value={formData.paymentStatus}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {paymentStatuses.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ödeme Yöntemi
              </label>
              <select
                name="paymentMethod"
                value={formData.paymentMethod}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Seçin</option>
                {paymentMethods.map(method => (
                  <option key={method} value={method}>{method}</option>
                ))}
              </select>
            </div>

            {/* Çek Ödeme Detayları */}
            {formData.paymentMethod === 'Çek' && (
              <div className="md:col-span-2">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Çek Sayısı *
                    </label>
                    <input
                      type="number"
                      name="checkCount"
                      value={formData.checkCount}
                      onChange={handleInputChange}
                      required
                      min="1"
                      max="10"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Kaç adet çek?"
                    />
                  </div>

                  {formData.checkDetails.length > 0 && (
                    <div className="space-y-4">
                      <h4 className="text-sm font-medium text-gray-700">Çek Detayları</h4>
                      {formData.checkDetails.map((check, index) => (
                        <div key={index} className="p-4 bg-white rounded-lg border border-gray-200">
                          <h5 className="text-sm font-medium text-gray-600 mb-3">
                            {index + 1}. Çek
                          </h5>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                            <div>
                              <label className="block text-xs font-medium text-gray-600 mb-1">
                                Çek Numarası
                              </label>
                              <input
                                type="text"
                                value={check.checkNumber}
                                onChange={(e) => handleCheckDetailChange(index, 'checkNumber', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="ÇEK-001"
                              />
                            </div>

                            <div>
                              <label className="block text-xs font-medium text-gray-600 mb-1">
                                Tutar (USD) *
                              </label>
                              <input
                                type="number"
                                value={check.amount}
                                onChange={(e) => handleCheckDetailChange(index, 'amount', e.target.value)}
                                required
                                step="0.01"
                                min="0"
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="100.00"
                              />
                            </div>

                            <div>
                              <label className="block text-xs font-medium text-gray-600 mb-1">
                                Vade Tarihi *
                              </label>
                              <input
                                type="date"
                                value={check.dueDate}
                                onChange={(e) => handleCheckDetailChange(index, 'dueDate', e.target.value)}
                                required
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                          </div>

                          <div className="mt-3">
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                checked={check.reminder}
                                onChange={(e) => handleCheckDetailChange(index, 'reminder', e.target.checked)}
                                className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <label className="ml-2 text-xs text-gray-600">
                                Vade tarihinde hatırlatma gönder
                              </label>
                            </div>

                            {check.reminder && (
                              <div className="mt-2">
                                <label className="block text-xs font-medium text-gray-600 mb-1">
                                  Hatırlatma Tarihi
                                </label>
                                <input
                                  type="date"
                                  value={check.reminderDate}
                                  onChange={(e) => handleCheckDetailChange(index, 'reminderDate', e.target.value)}
                                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                />
                                <p className="text-xs text-gray-500 mt-1">
                                  Boş bırakılırsa vade tarihinde hatırlatma gönderilir
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {formData.paymentStatus === 'Kısmi Ödendi' && (
              <div className="md:col-span-2">
                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-green-50 rounded-lg">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Ödenen Tutar (USD) *
                    </label>
                    <input
                      type="number"
                      name="paidAmount"
                      value={formData.paidAmount}
                      onChange={handleInputChange}
                      required
                      step="0.01"
                      min="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="500.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Kalan Tutar (USD)
                    </label>
                    <input
                      type="number"
                      name="remainingAmount"
                      value={formData.remainingAmount}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                      placeholder="Otomatik hesaplanacak"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Taksit Sayısı
                    </label>
                    <input
                      type="number"
                      name="installmentCount"
                      value={formData.installmentCount}
                      onChange={handleInputChange}
                      min="1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="3"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Taksit Tarihleri
                    </label>
                    <textarea
                      name="installmentDates"
                      value={formData.installmentDates}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Örn: 15.02.2024, 15.03.2024, 15.04.2024"
                    />
                  </div>
                </div>
              </div>
            )}

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Açıklama
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Satış ile ilgili ek bilgiler, notlar..."
              />
            </div>

          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Link
            href="/customer/analytics/sales"
            className="px-6 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            İptal
          </Link>
          <button
            type="submit"
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            Satış Kaydını Ekle
          </button>
        </div>

      </form>
    </div>
  );
}
