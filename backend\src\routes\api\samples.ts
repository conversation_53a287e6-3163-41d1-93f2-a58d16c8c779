import { Router, Request, Response } from 'express';
import { ShippingCalculator, SamplePaymentService, SamplePayment } from '../../services/shipping-calculator';

const router = Router();

// Gerçek veriler veritabanından gelecek - şimdilik boş array
let sampleRequests: any[] = [];

// Gerçek ödeme verileri veritabanından gelecek - şimdilik boş array
let samplePayments: SamplePayment[] = [];

// Gerçek takip verileri veritabanından gelecek - şimdilik boş array
let sampleRequestTracking: any[] = [];

// Customer API Endpoints

// Create sample request
router.post('/request', (req: Request, res: Response) => {
  try {
    const { 
      quoteRequestId, 
      quoteId, 
      customerId, 
      producerId,
      requestedProducts, 
      deliveryAddress, 
      specialRequirements 
    } = req.body;

    if (!quoteRequestId || !quoteId || !customerId || !producerId || !requestedProducts || !deliveryAddress) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    const newSampleRequest = {
      id: `sample-${Date.now()}`,
      quoteRequestId,
      quoteId,
      customerId,
      producerId,
      requestedProducts,
      sampleSpecifications: specialRequirements || null,
      deliveryAddress,
      status: 'pending',
      adminNotes: null,
      rejectionReason: null,
      producerResponse: null,
      preparationTimeDays: null,
      shippingInfo: null,
      customerEvaluation: null,
      willOrder: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      approvedAt: null,
      shippedAt: null,
      deliveredAt: null,
      evaluatedAt: null
    };

    sampleRequests.push(newSampleRequest);

    // Add tracking entry
    const trackingEntry = {
      id: `track-${Date.now()}`,
      sampleRequestId: newSampleRequest.id,
      status: 'pending',
      notes: 'Numune talebi oluşturuldu',
      createdBy: customerId,
      createdByType: 'customer',
      createdAt: new Date()
    };

    sampleRequestTracking.push(trackingEntry);

    res.status(201).json({
      success: true,
      data: newSampleRequest
    });
  } catch (error) {
    console.error('Error creating sample request:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get customer sample requests
router.get('/customer/:customerId', (req: Request, res: Response) => {
  try {
    const { customerId } = req.params;
    const customerSamples = sampleRequests.filter(sample => sample.customerId === customerId);

    res.json({
      success: true,
      data: customerSamples
    });
  } catch (error) {
    console.error('Error fetching customer sample requests:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get sample request detail
router.get('/:sampleRequestId', (req: Request, res: Response) => {
  try {
    const { sampleRequestId } = req.params;
    const sampleRequest = sampleRequests.find(sample => sample.id === sampleRequestId);

    if (!sampleRequest) {
      return res.status(404).json({
        success: false,
        error: 'Sample request not found'
      });
    }

    // Get tracking history
    const tracking = sampleRequestTracking.filter(track => track.sampleRequestId === sampleRequestId);

    res.json({
      success: true,
      data: {
        ...sampleRequest,
        tracking
      }
    });
  } catch (error) {
    console.error('Error fetching sample request detail:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Customer evaluation
router.put('/:sampleRequestId/evaluate', (req: Request, res: Response) => {
  try {
    const { sampleRequestId } = req.params;
    const { rating, feedback, willOrder, orderNotes } = req.body;

    const sampleIndex = sampleRequests.findIndex(sample => sample.id === sampleRequestId);
    
    if (sampleIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Sample request not found'
      });
    }

    // Update sample request
    sampleRequests[sampleIndex].customerEvaluation = {
      rating,
      feedback,
      orderNotes
    };
    sampleRequests[sampleIndex].willOrder = willOrder;
    sampleRequests[sampleIndex].status = 'evaluated';
    sampleRequests[sampleIndex].evaluatedAt = new Date();
    sampleRequests[sampleIndex].updatedAt = new Date();

    // Add tracking entry
    const trackingEntry = {
      id: `track-${Date.now()}`,
      sampleRequestId,
      status: 'evaluated',
      notes: `Müşteri değerlendirmesi: ${rating}/5 - ${willOrder ? 'Sipariş verecek' : 'Sipariş vermeyecek'}`,
      createdBy: sampleRequests[sampleIndex].customerId,
      createdByType: 'customer',
      createdAt: new Date()
    };

    sampleRequestTracking.push(trackingEntry);

    res.json({
      success: true,
      data: sampleRequests[sampleIndex]
    });
  } catch (error) {
    console.error('Error evaluating sample request:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Admin API Endpoints (Sadece Görüntüleme - Onay Yok)

// Get all sample requests (admin - sadece takip için)
router.get('/admin/all', (req: Request, res: Response) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;

    let filteredSamples = sampleRequests;

    if (status && status !== 'all') {
      filteredSamples = sampleRequests.filter(sample => sample.status === status);
    }

    // Pagination
    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedSamples = filteredSamples.slice(startIndex, endIndex);

    // Add statistics
    const stats = {
      total: sampleRequests.length,
      pending: sampleRequests.filter(s => s.status === 'pending').length,
      approved: sampleRequests.filter(s => s.status === 'approved').length,
      rejected: sampleRequests.filter(s => s.status === 'rejected').length,
      preparing: sampleRequests.filter(s => s.status === 'preparing').length,
      shipped: sampleRequests.filter(s => s.status === 'shipped').length,
      delivered: sampleRequests.filter(s => s.status === 'delivered').length,
      evaluated: sampleRequests.filter(s => s.status === 'evaluated').length
    };

    res.json({
      success: true,
      data: {
        samples: paginatedSamples,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: filteredSamples.length,
          totalPages: Math.ceil(filteredSamples.length / Number(limit))
        },
        stats
      }
    });
  } catch (error) {
    console.error('Error fetching admin sample requests:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Admin sample request detail (sadece görüntüleme)
router.get('/admin/:sampleRequestId', (req: Request, res: Response) => {
  try {
    const { sampleRequestId } = req.params;
    const sampleRequest = sampleRequests.find(sample => sample.id === sampleRequestId);

    if (!sampleRequest) {
      return res.status(404).json({
        success: false,
        error: 'Sample request not found'
      });
    }

    // Get tracking history
    const tracking = sampleRequestTracking.filter(track => track.sampleRequestId === sampleRequestId);

    res.json({
      success: true,
      data: {
        ...sampleRequest,
        tracking
      }
    });
  } catch (error) {
    console.error('Error fetching admin sample request detail:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Producer API Endpoints

// Get producer sample requests
router.get('/producer/:producerId', (req: Request, res: Response) => {
  try {
    const { producerId } = req.params;
    const { status } = req.query;

    let producerSamples = sampleRequests.filter(sample => sample.producerId === producerId);

    if (status && status !== 'all') {
      producerSamples = producerSamples.filter(sample => sample.status === status);
    }

    res.json({
      success: true,
      data: producerSamples
    });
  } catch (error) {
    console.error('Error fetching producer sample requests:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Producer approve/reject sample request with shipping cost (GÜNCELLENMIŞ)
router.put('/producer/:sampleRequestId/approve', async (req: Request, res: Response) => {
  try {
    const { sampleRequestId } = req.params;
    const { approved, notes, rejectionReason, preparationDays, shippingCost, carrier } = req.body;

    const sampleIndex = sampleRequests.findIndex(sample => sample.id === sampleRequestId);

    if (sampleIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Sample request not found'
      });
    }

    if (approved) {
      // Calculate shipping cost if not provided
      let finalShippingCost = shippingCost;
      if (!finalShippingCost) {
        const shippingCalc = ShippingCalculator.calculateShippingCost({
          fromAddress: { city: 'İstanbul', country: 'Türkiye' }, // Producer location
          toAddress: {
            city: sampleRequests[sampleIndex].deliveryAddress.city,
            country: sampleRequests[sampleIndex].deliveryAddress.country
          },
          productCount: sampleRequests[sampleIndex].requestedProducts.length,
          carrier: carrier || 'aras'
        });
        finalShippingCost = shippingCalc.baseRate;
      }

      // Update sample request
      sampleRequests[sampleIndex].status = 'approved_pending_payment';
      sampleRequests[sampleIndex].producerResponse = {
        approved: true,
        notes: notes || null,
        preparationDays: preparationDays || 3,
        shippingCost: finalShippingCost,
        carrier: carrier || 'aras',
        responseDate: new Date()
      };
      sampleRequests[sampleIndex].preparationTimeDays = preparationDays || 3;
      sampleRequests[sampleIndex].approvedAt = new Date();

      // Create payment request
      const payment = SamplePaymentService.createPaymentRequest(sampleRequestId, finalShippingCost);
      samplePayments.push(payment);

      // Add tracking entry
      const trackingEntry = {
        id: `track-${Date.now()}`,
        sampleRequestId,
        status: 'approved_pending_payment',
        notes: `Üretici onayladı. Kargo ücreti: ${finalShippingCost} TL (${carrier || 'aras'}). Ödeme bekleniyor.`,
        createdBy: sampleRequests[sampleIndex].producerId,
        createdByType: 'producer',
        createdAt: new Date()
      };

      sampleRequestTracking.push(trackingEntry);

      // Send notification to customer
      try {
        await fetch('http://localhost:8001/api/notifications/sample-approval', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sampleRequestId,
            customerId: sampleRequests[sampleIndex].customerId,
            approved: true,
            shippingCost: finalShippingCost
          }),
        });
      } catch (error) {
        console.error('Error sending approval notification:', error);
      }

      res.json({
        success: true,
        data: sampleRequests[sampleIndex],
        payment: payment
      });

    } else {
      // Rejection
      sampleRequests[sampleIndex].status = 'rejected';
      sampleRequests[sampleIndex].producerResponse = {
        approved: false,
        rejectionReason: rejectionReason || '',
        responseDate: new Date()
      };
      sampleRequests[sampleIndex].updatedAt = new Date();

      // Add tracking entry
      const trackingEntry = {
        id: `track-${Date.now()}`,
        sampleRequestId,
        status: 'rejected',
        notes: `Üretici reddetti: ${rejectionReason}`,
        createdBy: sampleRequests[sampleIndex].producerId,
        createdByType: 'producer',
        createdAt: new Date()
      };

      sampleRequestTracking.push(trackingEntry);

      // Send rejection notification to customer
      try {
        await fetch('http://localhost:8001/api/notifications/sample-approval', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sampleRequestId,
            customerId: sampleRequests[sampleIndex].customerId,
            approved: false,
            rejectionReason
          }),
        });
      } catch (error) {
        console.error('Error sending rejection notification:', error);
      }

      res.json({
        success: true,
        data: sampleRequests[sampleIndex]
      });
    }

  } catch (error) {
    console.error('Error approving/rejecting sample request:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update sample status (producer)
router.put('/producer/:sampleRequestId/status', (req: Request, res: Response) => {
  try {
    const { sampleRequestId } = req.params;
    const { status, preparationDays, shippingInfo, notes } = req.body;

    const sampleIndex = sampleRequests.findIndex(sample => sample.id === sampleRequestId);

    if (sampleIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Sample request not found'
      });
    }

    // Update sample request
    sampleRequests[sampleIndex].status = status;
    sampleRequests[sampleIndex].preparationTimeDays = preparationDays || null;
    sampleRequests[sampleIndex].shippingInfo = shippingInfo || null;

    if (status === 'shipped') {
      sampleRequests[sampleIndex].shippedAt = new Date();
    } else if (status === 'delivered') {
      sampleRequests[sampleIndex].deliveredAt = new Date();
    }

    sampleRequests[sampleIndex].updatedAt = new Date();

    // Add tracking entry
    const trackingEntry = {
      id: `track-${Date.now()}`,
      sampleRequestId,
      status,
      notes: notes || `Durum güncellendi: ${status}`,
      createdBy: sampleRequests[sampleIndex].producerId,
      createdByType: 'producer',
      createdAt: new Date()
    };

    sampleRequestTracking.push(trackingEntry);

    res.json({
      success: true,
      data: sampleRequests[sampleIndex]
    });
  } catch (error) {
    console.error('Error updating sample status:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Payment APIs

// Get payment info for sample request
router.get('/payment/:sampleRequestId', (req: Request, res: Response) => {
  try {
    const { sampleRequestId } = req.params;
    const payment = samplePayments.find(p => p.sampleRequestId === sampleRequestId);

    if (!payment) {
      return res.status(404).json({
        success: false,
        error: 'Payment not found'
      });
    }

    res.json({
      success: true,
      data: payment
    });
  } catch (error) {
    console.error('Error fetching payment info:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Process payment
router.post('/payment/:paymentId/process', (req: Request, res: Response) => {
  try {
    const { paymentId } = req.params;
    const { paymentMethod, transactionData } = req.body;

    const paymentIndex = samplePayments.findIndex(p => p.id === paymentId);
    if (paymentIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Payment not found'
      });
    }

    // Process payment
    const success = SamplePaymentService.processPayment(paymentId, paymentMethod, transactionData);

    if (success) {
      // Update payment status
      samplePayments[paymentIndex].status = 'completed';
      samplePayments[paymentIndex].paymentMethod = paymentMethod;
      samplePayments[paymentIndex].transactionId = `txn_${Date.now()}`;
      samplePayments[paymentIndex].paymentDate = new Date();
      samplePayments[paymentIndex].updatedAt = new Date();

      // Update sample request status
      const sampleRequestId = samplePayments[paymentIndex].sampleRequestId;
      const sampleIndex = sampleRequests.findIndex(s => s.id === sampleRequestId);
      if (sampleIndex !== -1) {
        sampleRequests[sampleIndex].status = 'approved';
        sampleRequests[sampleIndex].updatedAt = new Date();

        // Add tracking entry
        sampleRequestTracking.push({
          id: `track-${Date.now()}`,
          sampleRequestId,
          status: 'approved',
          notes: `Kargo ücreti ödendi (${samplePayments[paymentIndex].amount} TL). Numune hazırlığı başlayabilir.`,
          createdBy: sampleRequests[sampleIndex].customerId,
          createdByType: 'customer',
          createdAt: new Date()
        });
      }

      res.json({
        success: true,
        data: {
          payment: samplePayments[paymentIndex],
          sampleRequest: sampleRequests[sampleIndex]
        }
      });
    } else {
      // Payment failed
      samplePayments[paymentIndex].status = 'failed';
      samplePayments[paymentIndex].updatedAt = new Date();

      res.status(400).json({
        success: false,
        error: 'Payment processing failed'
      });
    }

  } catch (error) {
    console.error('Error processing payment:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Calculate shipping cost
router.post('/shipping/calculate', (req: Request, res: Response) => {
  try {
    const { fromAddress, toAddress, productCount, carrier } = req.body;

    const shippingRate = ShippingCalculator.calculateShippingCost({
      fromAddress,
      toAddress,
      productCount,
      carrier
    });

    const availableCarriers = ShippingCalculator.getAvailableCarriers(
      fromAddress.country,
      toAddress.country
    );

    res.json({
      success: true,
      data: {
        shippingRate,
        availableCarriers: availableCarriers.map(c => ({
          code: c,
          name: ShippingCalculator.getCarrierName(c)
        }))
      }
    });
  } catch (error) {
    console.error('Error calculating shipping cost:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get customer payments
router.get('/payments/customer/:customerId', (req: Request, res: Response) => {
  try {
    const { customerId } = req.params;

    // Get sample requests for customer
    const customerSamples = sampleRequests.filter(s => s.customerId === customerId);
    const sampleIds = customerSamples.map(s => s.id);

    // Get payments for those samples
    const customerPayments = samplePayments.filter(p => sampleIds.includes(p.sampleRequestId));

    res.json({
      success: true,
      data: customerPayments
    });
  } catch (error) {
    console.error('Error fetching customer payments:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get all pending payments (for reminder service)
router.get('/payments/pending', (req: Request, res: Response) => {
  try {
    const pendingPayments = samplePayments.filter(payment =>
      payment.status === 'pending'
    );

    res.json({
      success: true,
      data: pendingPayments
    });
  } catch (error) {
    console.error('Error fetching pending payments:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Cancel payment
router.put('/payment/:paymentId/cancel', (req: Request, res: Response) => {
  try {
    const { paymentId } = req.params;
    const { reason } = req.body;

    const paymentIndex = samplePayments.findIndex(p => p.id === paymentId);
    if (paymentIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Payment not found'
      });
    }

    samplePayments[paymentIndex].status = 'cancelled';
    (samplePayments[paymentIndex] as any).cancelledAt = new Date();
    (samplePayments[paymentIndex] as any).cancellationReason = reason;

    // Update sample request status
    const sampleIndex = sampleRequests.findIndex(s => s.id === samplePayments[paymentIndex].sampleRequestId);
    if (sampleIndex !== -1) {
      sampleRequests[sampleIndex].status = 'cancelled';
    }

    res.json({
      success: true,
      data: samplePayments[paymentIndex]
    });
  } catch (error) {
    console.error('Error cancelling payment:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

export default router;
