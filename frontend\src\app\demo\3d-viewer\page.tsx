'use client';

import React, { useState, useEffect } from 'react';
import Simple3D<PERSON>iewer from '../../../components/3d/Simple3DViewer';
import { use3DViewer } from '../../../hooks/use3DViewer';
import { 
  Asset3D, 
  AssetType, 
  AssetFormat, 
  AssetQuality, 
  ViewerConfiguration,
  MaterialDefinition,
  Annotation
} from '../../../types/3d';

// Mock data for demo
const mockAssets: Asset3D[] = [
  {
    id: 'asset-1',
    productId: 'product-1',
    name: 'White Marble Slab',
    description: 'High-quality white marble 3D model',
    type: AssetType.MODEL_3D,
    format: AssetFormat.GLB,
    quality: AssetQuality.HIGH,
    fileName: 'white_marble.glb',
    filePath: '/models/white_marble.glb',
    fileSize: 2048000,
    mimeType: 'model/gltf-binary',
    vertices: 15000,
    faces: 5000,
    tags: ['marble', 'white', 'natural-stone'],
    downloadCount: 0,
    viewCount: 0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    variants: [
      {
        id: 'variant-1',
        assetId: 'asset-1',
        quality: AssetQuality.LOW,
        fileName: 'white_marble_low.glb',
        filePath: '/models/white_marble_low.glb',
        fileSize: 512000,
        vertices: 3000,
        faces: 1000,
        lodLevel: 0,
        compressionRatio: 4.0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'variant-2',
        assetId: 'asset-1',
        quality: AssetQuality.MEDIUM,
        fileName: 'white_marble_medium.glb',
        filePath: '/models/white_marble_medium.glb',
        fileSize: 1024000,
        vertices: 8000,
        faces: 2667,
        lodLevel: 1,
        compressionRatio: 2.0,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]
  }
];

const mockMaterials: MaterialDefinition[] = [
  {
    id: 'material-1',
    name: 'Polished White Marble',
    description: 'High-gloss white marble material',
    baseColor: '#f8f8f8',
    metallic: 0.0,
    roughness: 0.1,
    tilingU: 1.0,
    tilingV: 1.0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'material-2',
    name: 'Matte White Marble',
    description: 'Matte finish white marble material',
    baseColor: '#f5f5f5',
    metallic: 0.0,
    roughness: 0.4,
    tilingU: 1.0,
    tilingV: 1.0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const mockConfiguration: ViewerConfiguration = {
  id: 'config-1',
  productId: 'product-1',
  cameraPosition: { x: 5, y: 5, z: 5 },
  cameraTarget: { x: 0, y: 0, z: 0 },
  cameraFov: 75,
  ambientLightColor: '#ffffff',
  ambientLightIntensity: 0.4,
  directionalLightColor: '#ffffff',
  directionalLightIntensity: 1.0,
  directionalLightPosition: { x: 10, y: 10, z: 5 },
  backgroundType: 'color',
  backgroundColor: '#f0f0f0',
  enableOrbitControls: true,
  enableZoom: true,
  enablePan: true,
  enableRotate: true,
  autoRotate: false,
  autoRotateSpeed: 2.0,
  enableShadows: true,
  shadowMapSize: 1024,
  enableAntialiasing: true,
  pixelRatio: 1.0,
  enableAnnotations: true,
  annotations: [
    {
      id: 'annotation-1',
      position: { x: 1, y: 1, z: 1 },
      title: 'Surface Quality',
      description: 'High-quality polished surface with natural veining patterns',
      type: 'feature',
      visible: true
    },
    {
      id: 'annotation-2',
      position: { x: -1, y: 0.5, z: 1 },
      title: 'Dimensions',
      description: 'Standard slab dimensions: 120cm x 60cm x 2cm',
      type: 'dimension',
      visible: true
    }
  ],
  createdAt: new Date(),
  updatedAt: new Date()
};

export default function ThreeDViewerDemo() {
  const [selectedQuality, setSelectedQuality] = useState<AssetQuality>(AssetQuality.HIGH);
  const [showStats, setShowStats] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleLoad = () => {
    setIsLoading(false);
    console.log('3D model loaded successfully');
  };

  const handleError = (error: any) => {
    setIsLoading(false);
    setError(error.message);
    console.error('3D viewer error:', error);
  };

  const handleProgress = (progress: any) => {
    console.log('Loading progress:', progress);
  };

  const handleCameraChange = (camera: any) => {
    console.log('Camera changed:', camera);
  };

  const handleAnnotationClick = (annotation: Annotation) => {
    console.log('Annotation clicked:', annotation);
    alert(`${annotation.title}: ${annotation.description}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">
              3D Product Viewer Demo
            </h1>
            <p className="mt-2 text-gray-600">
              Interactive 3D visualization powered by Three.js and React Three Fiber
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 3D Viewer */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">
                    White Marble Slab
                  </h2>
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium text-gray-700">
                      Quality:
                    </label>
                    <select
                      value={selectedQuality}
                      onChange={(e) => setSelectedQuality(e.target.value as AssetQuality)}
                      className="px-3 py-1 border border-gray-300 rounded-md text-sm"
                    >
                      <option value={AssetQuality.LOW}>Low</option>
                      <option value={AssetQuality.MEDIUM}>Medium</option>
                      <option value={AssetQuality.HIGH}>High</option>
                    </select>
                  </div>
                </div>

                {error && (
                  <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-800 text-sm">{error}</p>
                  </div>
                )}

                <div className="relative">
                  <Simple3DViewer className="border border-gray-200 rounded-lg" />
                </div>

                {/* Controls */}
                <div className="mt-4 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => setShowStats(!showStats)}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                        showStats
                          ? 'bg-blue-100 text-blue-700'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {showStats ? 'Hide Stats' : 'Show Stats'}
                    </button>
                  </div>

                  <div className="text-sm text-gray-500">
                    Use mouse to rotate, zoom, and pan
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Product Info */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Product Information
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">Material:</label>
                  <p className="text-sm text-gray-600">White Marble</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Dimensions:</label>
                  <p className="text-sm text-gray-600">120cm × 60cm × 2cm</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Finish:</label>
                  <p className="text-sm text-gray-600">Polished</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Origin:</label>
                  <p className="text-sm text-gray-600">Afyon, Turkey</p>
                </div>
              </div>
            </div>

            {/* Technical Specs */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Technical Specifications
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">Density:</label>
                  <p className="text-sm text-gray-600">2.7 g/cm³</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Hardness:</label>
                  <p className="text-sm text-gray-600">3-4 Mohs</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Water Absorption:</label>
                  <p className="text-sm text-gray-600">0.2%</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Frost Resistance:</label>
                  <p className="text-sm text-gray-600">Excellent</p>
                </div>
              </div>
            </div>

            {/* 3D Model Info */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                3D Model Details
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">Vertices:</label>
                  <p className="text-sm text-gray-600">
                    {mockAssets[0].vertices?.toLocaleString()}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Faces:</label>
                  <p className="text-sm text-gray-600">
                    {mockAssets[0].faces?.toLocaleString()}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">File Size:</label>
                  <p className="text-sm text-gray-600">
                    {(mockAssets[0].fileSize / 1024 / 1024).toFixed(1)} MB
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Format:</label>
                  <p className="text-sm text-gray-600">{mockAssets[0].format}</p>
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Viewer Features
              </h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  360° rotation
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Zoom & pan controls
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Multiple quality levels
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Interactive annotations
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Material variations
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Performance monitoring
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
