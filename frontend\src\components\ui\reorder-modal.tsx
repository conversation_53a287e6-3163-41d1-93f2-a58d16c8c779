'use client'

import * as React from 'react'
import { Dialog, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  RefreshCw, 
  Package, 
  DollarSign,
  Calendar,
  User,
  CheckCircle,
  TrendingUp,
  Star
} from 'lucide-react'

interface ReorderModalProps {
  isOpen: boolean
  onClose: () => void
  order: any
  onCreateReorder: (reorderData: any) => Promise<boolean>
}

export function ReorderModal({
  isOpen,
  onClose,
  order,
  onCreateReorder
}: ReorderModalProps) {
  const [reorderData, setReorderData] = React.useState({
    quantity: '',
    unitPrice: '',
    totalPrice: '',
    currency: 'USD',
    deliveryTime: '',
    specialRequests: '',
    discountOffered: '',
    notes: ''
  })
  const [isLoading, setIsLoading] = React.useState(false)
  const [step, setStep] = React.useState(1) // 1: Review, 2: New Order Form, 3: Confirmation

  React.useEffect(() => {
    if (isOpen && order) {
      // Pre-fill with previous order data
      setReorderData({
        quantity: order.quantity.toString(),
        unitPrice: (order.totalValue / order.quantity).toFixed(2),
        totalPrice: order.totalValue.toString(),
        currency: order.currency || 'USD',
        deliveryTime: '15 gün',
        specialRequests: '',
        discountOffered: '5',
        notes: `${order.customerName} için tekrar sipariş`
      })
      setStep(1)
    }
  }, [isOpen, order])

  const handleInputChange = (field: string, value: string) => {
    setReorderData(prev => {
      const updated = { ...prev, [field]: value }
      
      // Auto-calculate total price when quantity or unit price changes
      if ((field === 'quantity' || field === 'unitPrice') && updated.quantity && updated.unitPrice) {
        const quantity = parseFloat(updated.quantity)
        const unitPrice = parseFloat(updated.unitPrice)
        const discount = parseFloat(updated.discountOffered) || 0
        const subtotal = quantity * unitPrice
        const discountAmount = subtotal * (discount / 100)
        updated.totalPrice = (subtotal - discountAmount).toFixed(2)
      }
      
      return updated
    })
  }

  const handleSubmit = async () => {
    if (!reorderData.quantity || !reorderData.unitPrice) {
      alert('Lütfen zorunlu alanları doldurun!')
      return
    }

    setIsLoading(true)
    try {
      const success = await onCreateReorder({
        ...reorderData,
        originalOrderId: order.id,
        customerId: order.customerId,
        customerName: order.customerName,
        customerEmail: order.customerEmail,
        productName: order.productName,
        isReorder: true
      })
      
      if (success) {
        setStep(3)
        setTimeout(() => {
          onClose()
        }, 2000)
      }
    } catch (error) {
      console.error('Error creating reorder:', error)
      alert('Tekrar sipariş oluşturulurken hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const getCustomerLoyaltyLevel = () => {
    // Mock loyalty calculation based on order value
    if (order.totalValue > 15000) return { level: 'Platinum', color: 'text-purple-600', discount: 10 }
    if (order.totalValue > 10000) return { level: 'Gold', color: 'text-yellow-600', discount: 7 }
    if (order.totalValue > 5000) return { level: 'Silver', color: 'text-gray-600', discount: 5 }
    return { level: 'Bronze', color: 'text-orange-600', discount: 3 }
  }

  const loyalty = getCustomerLoyaltyLevel()

  if (!order) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <RefreshCw className="w-5 h-5" />
            Tekrar Sipariş Oluştur - #{order.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step 1: Review Previous Order */}
          {step === 1 && (
            <>
              {/* Previous Order Info */}
              <Card className="bg-blue-50 border-blue-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg text-blue-800">Önceki Sipariş Bilgileri</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-blue-700">Müşteri:</span>
                    <p className="text-blue-900">{order.customerName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">Ürün:</span>
                    <p className="text-blue-900">{order.productName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">Miktar:</span>
                    <p className="text-blue-900">{order.quantity} {order.unit}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">Toplam Tutar:</span>
                    <p className="text-blue-900">{order.totalValue} {order.currency}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Customer Loyalty */}
              <Card className="bg-green-50 border-green-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg text-green-800 flex items-center gap-2">
                    <Star className="w-5 h-5" />
                    Müşteri Sadakat Durumu
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2">
                        <Badge className={`${loyalty.color} bg-white border`}>
                          {loyalty.level} Müşteri
                        </Badge>
                        {order.customerRating && (
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="text-sm font-medium">{order.customerRating}/5</span>
                          </div>
                        )}
                      </div>
                      <p className="text-green-700 mt-1">
                        Önerilen indirim: %{loyalty.discount}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-green-600">Önceki Sipariş</p>
                      <p className="font-medium text-green-800">{order.completedDate}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Success Metrics */}
              <Card className="bg-yellow-50 border-yellow-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg text-yellow-800 flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Başarı Metrikleri
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <p className="text-2xl font-bold text-yellow-900">
                        {order.customerRating || 'N/A'}
                      </p>
                      <p className="text-sm text-yellow-700">Müşteri Puanı</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-yellow-900">Zamanında</p>
                      <p className="text-sm text-yellow-700">Teslimat</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-yellow-900">%100</p>
                      <p className="text-sm text-yellow-700">Ödeme</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={onClose}>
                  İptal
                </Button>
                <Button onClick={() => setStep(2)} className="bg-green-600 hover:bg-green-700">
                  Yeni Sipariş Oluştur
                </Button>
              </div>
            </>
          )}

          {/* Step 2: New Order Form */}
          {step === 2 && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Yeni Sipariş Bilgileri</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Product and Quantity */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="quantity">Miktar *</Label>
                      <div className="relative">
                        <Package className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          id="quantity"
                          type="number"
                          value={reorderData.quantity}
                          onChange={(e) => handleInputChange('quantity', e.target.value)}
                          className="pl-10"
                          placeholder="0"
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Birim: {order.unit}</p>
                    </div>
                    
                    <div>
                      <Label htmlFor="deliveryTime">Teslimat Süresi *</Label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          id="deliveryTime"
                          value={reorderData.deliveryTime}
                          onChange={(e) => handleInputChange('deliveryTime', e.target.value)}
                          className="pl-10"
                          placeholder="örn: 15 gün"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Price Information */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <Label htmlFor="unitPrice">Birim Fiyat *</Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          id="unitPrice"
                          type="number"
                          step="0.01"
                          value={reorderData.unitPrice}
                          onChange={(e) => handleInputChange('unitPrice', e.target.value)}
                          className="pl-10"
                          placeholder="0.00"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="discountOffered">İndirim (%)</Label>
                      <Input
                        id="discountOffered"
                        type="number"
                        step="0.1"
                        value={reorderData.discountOffered}
                        onChange={(e) => handleInputChange('discountOffered', e.target.value)}
                        placeholder="0"
                      />
                      <p className="text-xs text-green-600 mt-1">Önerilen: %{loyalty.discount}</p>
                    </div>
                    
                    <div>
                      <Label htmlFor="currency">Para Birimi</Label>
                      <Select value={reorderData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="TRY">TRY</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor="totalPrice">Toplam Fiyat</Label>
                      <Input
                        id="totalPrice"
                        value={reorderData.totalPrice}
                        readOnly
                        className="bg-gray-50 font-medium"
                        placeholder="Otomatik hesaplanacak"
                      />
                    </div>
                  </div>

                  {/* Special Requests */}
                  <div>
                    <Label htmlFor="specialRequests">Özel İstekler</Label>
                    <Textarea
                      id="specialRequests"
                      value={reorderData.specialRequests}
                      onChange={(e) => handleInputChange('specialRequests', e.target.value)}
                      rows={3}
                      placeholder="Müşterinin özel istekleri, değişiklikler..."
                    />
                  </div>

                  {/* Notes */}
                  <div>
                    <Label htmlFor="notes">Sipariş Notları</Label>
                    <Textarea
                      id="notes"
                      value={reorderData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      rows={2}
                      placeholder="Sipariş ile ilgili ek notlar..."
                    />
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>
                  Geri Dön
                </Button>
                <Button 
                  onClick={handleSubmit} 
                  disabled={!reorderData.quantity || !reorderData.unitPrice || isLoading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Oluşturuluyor...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Tekrar Sipariş Oluştur
                    </>
                  )}
                </Button>
              </div>
            </>
          )}

          {/* Step 3: Confirmation */}
          {step === 3 && (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-green-800 mb-2">
                Tekrar Sipariş Başarıyla Oluşturuldu!
              </h3>
              <p className="text-green-600 mb-4">
                {order.customerName} müşterisi için yeni sipariş oluşturuldu.
              </p>
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <p className="text-sm text-green-700">
                  <strong>Ürün:</strong> {order.productName}<br />
                  <strong>Miktar:</strong> {reorderData.quantity} {order.unit}<br />
                  <strong>Toplam:</strong> {reorderData.totalPrice} {reorderData.currency}
                </p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
