'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft,
  Building2,
  MapPin,
  Package,
  Users,
  TrendingUp,
  DollarSign,
  Clock,
  Star,
  Phone,
  Mail,
  Globe,
  Warehouse,
  AlertTriangle,
  CheckCircle,
  Eye,
  BarChart3,
  Calendar,
  Award,
  Truck,
  Factory,
  Target,
  MessageSquare
} from 'lucide-react'

interface ProducerDetailPageProps {
  params: {
    id: string
  }
}

export default function AdminProducerDetailPage({ params }: ProducerDetailPageProps) {
  const router = useRouter()
  const [selectedTab, setSelectedTab] = React.useState('overview')

  // Mock data - gerçek uygulamada API'den gelecek
  const mockProducer = {
    id: params.id,
    name: 'Afyon Doğal Taş A.Ş.',
    logo: '/images/producers/afyon-logo.png',
    establishedYear: 2010,
    location: {
      city: 'Afyon',
      district: 'Merkez',
      address: 'Afyon Sanayi Sitesi, 1. Cadde No: 15'
    },
    contact: {
      phone: '+90 272 123 45 67',
      email: '<EMAIL>',
      website: 'www.afyondogaltas.com'
    },
    legalInfo: {
      taxNumber: '1234567890',
      tradeRegister: 'Afyon Ticaret Sicil No: 12345'
    },
    certifications: ['ISO 9001', 'CE Belgesi', 'TSE Belgesi'],
    quarries: [
      {
        id: '1',
        name: 'Afyon Merkez Ocağı',
        location: 'Afyon Merkez',
        address: 'Afyon Merkez, Ocak Mevkii',
        capacity: '300 ton/ay',
        products: ['Afyon Beyaz Mermer', 'Afyon Sugar Mermer'],
        coordinates: '38.7569, 30.5387',
        googleMapsLink: 'https://maps.google.com/?q=38.7569,30.5387',
        status: 'active',
        established: 2010,
        area: '15 dönüm'
      },
      {
        id: '2',
        name: 'Afyon Güney Ocağı',
        location: 'Afyon Güney',
        address: 'Afyon Güney, Taş Ocağı Mevkii',
        capacity: '200 ton/ay',
        products: ['Afyon Krem Mermer'],
        coordinates: '38.7269, 30.5187',
        googleMapsLink: 'https://maps.google.com/?q=38.7269,30.5187',
        status: 'active',
        established: 2015,
        area: '10 dönüm'
      }
    ],
    factories: [
      {
        id: '1',
        name: 'Ana Fabrika',
        location: 'Afyon Merkez',
        address: 'Afyon Sanayi Sitesi, 1. Cadde No: 15',
        capacity: '500 m³/ay',
        employees: 45,
        established: 2010,
        coordinates: '38.7669, 30.5487',
        googleMapsLink: 'https://maps.google.com/?q=38.7669,30.5487',
        area: '5000 m²',
        machinery: ['Köprü Kesim Makinesi', 'Cilalama Makinesi', 'Kalibrasyon Makinesi'],
        certifications: ['ISO 9001', 'CE Belgesi']
      },
      {
        id: '2',
        name: 'İkinci Tesis',
        location: 'Afyon Sandıklı',
        address: 'Sandıklı Sanayi Bölgesi, B Blok No: 8',
        capacity: '300 m³/ay',
        employees: 28,
        established: 2018,
        coordinates: '38.4669, 30.2687',
        googleMapsLink: 'https://maps.google.com/?q=38.4669,30.2687',
        area: '3000 m²',
        machinery: ['Köprü Kesim Makinesi', 'Fırçalama Makinesi'],
        certifications: ['TSE Belgesi']
      }
    ],
    performance: {
      rating: 4.8,
      totalOrders: 156,
      completedOrders: 148,
      onTimeDelivery: 95,
      responseTime: '2 saat',
      customerSatisfaction: 4.7
    },
    financials: {
      monthlyRevenue: 450000,
      yearlyRevenue: 5200000,
      currency: 'TL',
      creditScore: 'A+',
      paymentTerms: '30 gün'
    }
  }

  const mockProducts = [
    {
      id: '1',
      name: 'Afyon Beyaz Mermer',
      category: 'Mermer',
      image: '/images/products/white-marble.jpg',
      stock: 250,
      unit: 'm²',
      priceRange: { min: 115, max: 125 },
      lastUpdate: '2 saat önce',
      status: 'active'
    },
    {
      id: '4',
      name: 'Oniks Yeşil',
      category: 'Oniks',
      image: '/images/products/green-onyx.jpg',
      stock: 180,
      unit: 'm²',
      priceRange: { min: 120, max: 130 },
      lastUpdate: '1 gün önce',
      status: 'active'
    }
  ]

  const mockQuarries = [
    {
      id: '1',
      name: 'Afyon Beyaz Mermer Ocağı',
      location: 'Afyon Merkez',
      ownership: 'Sahibi',
      capacity: '1000 m³/ay',
      established: 2010,
      stones: ['Afyon Beyaz Mermer', 'Afyon Bej Mermer']
    },
    {
      id: '2',
      name: 'Afyon Oniks Ocağı',
      location: 'Afyon Sandıklı',
      ownership: 'Anlaşmalı',
      capacity: '500 m³/ay',
      established: 2015,
      stones: ['Yeşil Oniks', 'Sarı Oniks']
    }
  ]

  const mockCustomers = [
    {
      name: 'İnşaat A.Ş.',
      sector: 'İnşaat',
      totalOrders: 25,
      totalValue: 850000,
      lastOrder: '1 hafta önce'
    },
    {
      name: 'Dekorasyon Ltd.',
      sector: 'Dekorasyon',
      totalOrders: 18,
      totalValue: 620000,
      lastOrder: '3 gün önce'
    }
  ]

  if (!mockProducer) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Üretici bulunamadı</h3>
          <p className="text-gray-600 mb-4">Aradığınız üretici mevcut değil.</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Geri Dön
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Geri Dön
          </Button>
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
              <Building2 className="w-8 h-8 text-gray-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{mockProducer.name}</h1>
              <p className="text-gray-600 mt-1">
                {mockProducer.establishedYear} yılından beri faaliyet • {mockProducer.location.city}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Star className="w-5 h-5 text-yellow-500 fill-current" />
            <span className="text-lg font-semibold">{mockProducer.performance.rating}</span>
            <span className="text-gray-500">({mockProducer.performance.totalOrders} sipariş)</span>
          </div>
          <Button variant="outline">
            <MessageSquare className="w-4 h-4 mr-2" />
            Mesaj Gönder
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Package className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Ürün</p>
              <p className="text-xl font-bold text-gray-900">{mockProducts.length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Warehouse className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Stok</p>
              <p className="text-xl font-bold text-gray-900">
                {mockProducts.reduce((sum, p) => sum + p.stock, 0)} m²
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-amber-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Aylık Ciro</p>
              <p className="text-xl font-bold text-gray-900">
                {(mockProducer.financials.monthlyRevenue / 1000).toFixed(0)}K ₺
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Truck className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Teslimat</p>
              <p className="text-xl font-bold text-gray-900">{mockProducer.performance.onTimeDelivery}%</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <Clock className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Yanıt Süresi</p>
              <p className="text-xl font-bold text-gray-900">{mockProducer.performance.responseTime}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Genel Bakış', icon: Eye },
            { id: 'products', label: 'Ürünleri', icon: Package },
            { id: 'quarries', label: 'Ocakları', icon: MapPin },
            { id: 'factories', label: 'Fabrikaları', icon: Factory },
            { id: 'performance', label: 'Performans', icon: TrendingUp },
            { id: 'customers', label: 'Müşteriler', icon: Users }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === tab.id
                    ? 'border-amber-500 text-amber-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {selectedTab === 'overview' && (
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            {/* Company Info */}
            <div className="xl:col-span-2 space-y-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Firma Bilgileri</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Kuruluş Yılı</label>
                    <p className="text-gray-900">{mockProducer.establishedYear}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Şehir</label>
                    <p className="text-gray-900">{mockProducer.location.city}</p>
                  </div>
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-gray-600">Adres</label>
                    <p className="text-gray-900">{mockProducer.location.address}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Telefon</label>
                    <p className="text-gray-900">{mockProducer.contact.phone}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">E-posta</label>
                    <p className="text-gray-900">{mockProducer.contact.email}</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Üretim Tesisleri</h3>
                <div className="space-y-4">
                  {mockProducer.factories.map((factory, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{factory.name}</h4>
                        <Badge variant="outline">{factory.capacity}</Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Konum:</span>
                          <p className="font-medium">{factory.location}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Çalışan:</span>
                          <p className="font-medium">{factory.employees} kişi</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Kuruluş:</span>
                          <p className="font-medium">{factory.established}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>

            {/* Side Info */}
            <div className="space-y-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">İletişim</h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-gray-500" />
                    <span className="text-sm">{mockProducer.contact.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-gray-500" />
                    <span className="text-sm">{mockProducer.contact.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Globe className="w-4 h-4 text-gray-500" />
                    <span className="text-sm">{mockProducer.contact.website}</span>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Sertifikalar</h3>
                <div className="space-y-2">
                  {mockProducer.certifications.map((cert, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Award className="w-4 h-4 text-green-600" />
                      <span className="text-sm">{cert}</span>
                    </div>
                  ))}
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Finansal Durum</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Kredi Notu</span>
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      {mockProducer.financials.creditScore}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Ödeme Vadesi</span>
                    <span className="text-sm font-medium">{mockProducer.financials.paymentTerms}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Yıllık Ciro</span>
                    <span className="text-sm font-medium">
                      {(mockProducer.financials.yearlyRevenue / 1000000).toFixed(1)}M ₺
                    </span>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}

        {selectedTab === 'products' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Ürettiği Ürünler ({mockProducts.length})</h3>
              <Button variant="outline" size="sm">
                <TrendingUp className="w-4 h-4 mr-2" />
                Performans Analizi
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {mockProducts.map((product) => (
                <Card key={product.id} className="overflow-hidden">
                  <div className="aspect-video bg-gray-200">
                    <img
                      src={product.image || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <button
                        onClick={() => router.push(`/admin/products/${product.id}`)}
                        className="text-lg font-semibold text-blue-600 hover:underline text-left"
                      >
                        {product.name}
                      </button>
                      <Badge variant="outline">{product.category}</Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Stok:</span>
                        <span className="font-medium">{product.stock} {product.unit}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Fiyat:</span>
                        <span className="font-medium">
                          ${product.priceRange.min}-{product.priceRange.max}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Güncelleme:</span>
                        <span className="text-gray-500">{product.lastUpdate}</span>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {selectedTab === 'quarries' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Ocakları ({mockProducer.quarries.length})</h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {mockProducer.quarries.map((quarry) => (
                <Card key={quarry.id} className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">{quarry.name}</h4>
                        <p className="text-sm text-gray-600">{quarry.address}</p>
                      </div>
                      <Badge variant="outline" className="bg-amber-50 text-amber-700">
                        <MapPin className="w-3 h-3 mr-1" />
                        Ocak
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Kapasite:</span>
                        <span className="font-medium">{quarry.capacity}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Alan:</span>
                        <span className="font-medium">{quarry.area}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Kuruluş:</span>
                        <span className="font-medium">{quarry.established}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Durum:</span>
                        <Badge className={quarry.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                          {quarry.status === 'active' ? 'Aktif' : 'Pasif'}
                        </Badge>
                      </div>
                    </div>

                    <div>
                      <span className="text-sm text-gray-600 block mb-2">Çıkan Taşlar:</span>
                      <div className="flex flex-wrap gap-1">
                        {quarry.products.map((product, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {product}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="pt-2 border-t space-y-3">
                      {/* Embedded Google Maps */}
                      <div>
                        <span className="text-sm text-gray-600 block mb-2">Konum:</span>
                        <div className="w-full h-48 rounded-lg overflow-hidden border border-gray-200">
                          <iframe
                            src={`https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=${quarry.coordinates}`}
                            width="100%"
                            height="100%"
                            style={{ border: 0 }}
                            allowFullScreen
                            loading="lazy"
                            referrerPolicy="no-referrer-when-downgrade"
                            title={`${quarry.name} Konumu`}
                          />
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() => window.open(quarry.googleMapsLink, '_blank')}
                        >
                          <MapPin className="w-4 h-4 mr-2" />
                          Haritada Aç
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() => navigator.clipboard.writeText(quarry.coordinates)}
                        >
                          📍 Koordinatları Kopyala
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {selectedTab === 'factories' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Fabrikaları ({mockProducer.factories.length})</h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {mockProducer.factories.map((factory) => (
                <Card key={factory.id} className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">{factory.name}</h4>
                        <p className="text-sm text-gray-600">{factory.address}</p>
                      </div>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        <Factory className="w-3 h-3 mr-1" />
                        Fabrika
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Kapasite:</span>
                        <span className="font-medium">{factory.capacity}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Çalışan:</span>
                        <span className="font-medium">{factory.employees} kişi</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Kuruluş:</span>
                        <span className="font-medium">{factory.established}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Alan:</span>
                        <span className="font-medium">{factory.area}</span>
                      </div>
                    </div>

                    <div>
                      <span className="text-sm text-gray-600 block mb-2">Makineler:</span>
                      <div className="flex flex-wrap gap-1">
                        {factory.machinery.map((machine, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {machine}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <span className="text-sm text-gray-600 block mb-2">Sertifikalar:</span>
                      <div className="flex flex-wrap gap-1">
                        {factory.certifications.map((cert, index) => (
                          <Badge key={index} variant="outline" className="text-xs bg-green-50 text-green-700">
                            <Award className="w-3 h-3 mr-1" />
                            {cert}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="pt-2 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => window.open(factory.googleMapsLink, '_blank')}
                      >
                        <MapPin className="w-4 h-4 mr-2" />
                        Haritada Görüntüle
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {selectedTab === 'performance' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Operasyonel Performans</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Zamanında Teslimat</span>
                  <div className="flex items-center gap-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${mockProducer.performance.onTimeDelivery}%` }}
                      ></div>
                    </div>
                    <span className="font-medium">{mockProducer.performance.onTimeDelivery}%</span>
                  </div>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Toplam Sipariş</span>
                  <span className="font-medium">{mockProducer.performance.totalOrders}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Tamamlanan</span>
                  <span className="font-medium">{mockProducer.performance.completedOrders}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Yanıt Süresi</span>
                  <span className="font-medium">{mockProducer.performance.responseTime}</span>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Müşteri Memnuniyeti</h3>
              <div className="text-center">
                <div className="text-4xl font-bold text-amber-600 mb-2">
                  {mockProducer.performance.customerSatisfaction}
                </div>
                <div className="flex justify-center mb-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`w-6 h-6 ${
                        star <= mockProducer.performance.customerSatisfaction
                          ? 'text-yellow-500 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <p className="text-gray-600">5 üzerinden</p>
              </div>
            </Card>
          </div>
        )}

        {selectedTab === 'customers' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Müşteri Portföyü ({mockCustomers.length})</h3>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Müşteri</th>
                    <th className="text-left py-3 px-4">Sektör</th>
                    <th className="text-left py-3 px-4">Sipariş Sayısı</th>
                    <th className="text-left py-3 px-4">Toplam Değer</th>
                    <th className="text-left py-3 px-4">Son Sipariş</th>
                  </tr>
                </thead>
                <tbody>
                  {mockCustomers.map((customer, index) => (
                    <tr key={index} className="border-b">
                      <td className="py-3 px-4 font-medium">{customer.name}</td>
                      <td className="py-3 px-4">
                        <Badge variant="outline">{customer.sector}</Badge>
                      </td>
                      <td className="py-3 px-4">{customer.totalOrders}</td>
                      <td className="py-3 px-4">₺{customer.totalValue.toLocaleString()}</td>
                      <td className="py-3 px-4 text-gray-600">{customer.lastOrder}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
