// RFC-501: Order Management Service Implementation
import { PrismaClient, OrderStatus, NotificationType } from '@prisma/client';
import { createClient, RedisClientType } from 'redis';

export interface OrderSearchCriteria {
  query?: string;
  status?: OrderStatus;
  customerId?: string;
  producerId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  minAmount?: number;
  maxAmount?: number;
  page?: number;
  limit?: number;
}

export interface OrderDetails {
  id: string;
  orderNumber: string;
  status: OrderStatus;
  totalAmount: number;
  createdAt: Date;
  updatedAt: Date;
  customer: {
    id: string;
    email: string;
    companyName?: string;
  };
  producer: {
    id: string;
    email: string;
    companyName?: string;
  };
  orderItems: OrderItem[];
  payments: PaymentInfo[];
  timeline: OrderTimeline[];
  dispute?: DisputeInfo;
}

export interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  specifications?: any;
}

export interface PaymentInfo {
  id: string;
  amount: number;
  status: string;
  method: string;
  createdAt: Date;
  escrowStatus?: string;
}

export interface OrderTimeline {
  id: string;
  status: string;
  timestamp: Date;
  notes?: string;
  updatedBy?: string;
}

export interface DisputeInfo {
  id: string;
  reason: string;
  status: string;
  createdAt: Date;
  description: string;
}

export interface OrderStatistics {
  totalOrders: number;
  ordersByStatus: { [key: string]: number };
  totalRevenue: number;
  averageOrderValue: number;
  orderTrend: { date: string; count: number; revenue: number }[];
  topProducts: { productId: string; name: string; orders: number; revenue: number }[];
  fulfillmentMetrics: {
    averageProcessingTime: number;
    onTimeDeliveryRate: number;
    customerSatisfactionScore: number;
  };
}

export class OrderManagementService {
  private prisma: PrismaClient;
  private redis: RedisClientType;

  constructor() {
    this.prisma = new PrismaClient();
    this.redis = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    this.redis.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });
  }

  async connect(): Promise<void> {
    if (!this.redis.isOpen) {
      await this.redis.connect();
    }
  }

  async disconnect(): Promise<void> {
    if (this.redis.isOpen) {
      await this.redis.disconnect();
    }
    await this.prisma.$disconnect();
  }

  // Search orders with advanced criteria
  async searchOrders(criteria: OrderSearchCriteria): Promise<{
    orders: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    await this.connect();
    
    const page = criteria.page || 1;
    const limit = criteria.limit || 20;
    const skip = (page - 1) * limit;

    const where: any = {};

    if (criteria.query) {
      where.OR = [
        { orderNumber: { contains: criteria.query, mode: 'insensitive' } },
        { customer: { email: { contains: criteria.query, mode: 'insensitive' } } },
        { producer: { email: { contains: criteria.query, mode: 'insensitive' } } }
      ];
    }

    if (criteria.status) {
      where.status = criteria.status;
    }

    if (criteria.customerId) {
      where.customerId = criteria.customerId;
    }

    if (criteria.producerId) {
      where.producerId = criteria.producerId;
    }

    if (criteria.dateFrom || criteria.dateTo) {
      where.createdAt = {};
      if (criteria.dateFrom) {
        where.createdAt.gte = criteria.dateFrom;
      }
      if (criteria.dateTo) {
        where.createdAt.lte = criteria.dateTo;
      }
    }

    if (criteria.minAmount || criteria.maxAmount) {
      where.totalAmount = {};
      if (criteria.minAmount) {
        where.totalAmount.gte = criteria.minAmount;
      }
      if (criteria.maxAmount) {
        where.totalAmount.lte = criteria.maxAmount;
      }
    }

    const [orders, total] = await Promise.all([
      this.prisma.order.findMany({
        where,
        include: {
          customer: {
            select: {
              id: true,
              email: true,
              profile: {
                select: { companyName: true }
              }
            }
          },
          producer: {
            select: {
              id: true,
              email: true,
              profile: {
                select: { companyName: true }
              }
            }
          },
          orderItems: {
            include: {
              product: {
                select: { name: true }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      this.prisma.order.count({ where })
    ]);

    return {
      orders,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  // Get detailed order information
  async getOrderDetails(orderId: string): Promise<OrderDetails | null> {
    await this.connect();
    
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      include: {
        customer: {
          select: {
            id: true,
            email: true,
            profile: {
              select: { companyName: true }
            }
          }
        },
        producer: {
          select: {
            id: true,
            email: true,
            profile: {
              select: { companyName: true }
            }
          }
        },
        orderItems: {
          include: {
            product: true
          }
        },
        payments: true,
        dispute: true
      }
    });

    if (!order) return null;

    // Get order timeline from audit logs
    const timeline = await this.prisma.auditLog.findMany({
      where: {
        // entityType: 'ORDER', // Remove if not in schema
        resourceId: orderId
      },
      orderBy: { createdAt: 'asc' }
    });

    return {
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      totalAmount: Number(order.totalAmount),
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      customer: {
        id: order.customer.id,
        email: order.customer.email,
        companyName: order.customer.profile?.companyName
      },
      producer: {
        id: order.producer.id,
        email: order.producer.email,
        companyName: order.producer.profile?.companyName
      },
      orderItems: order.orderItems.map(item => ({
        id: item.id,
        productId: item.productId,
        productName: item.product.name,
        quantity: item.quantity,
        unitPrice: Number(item.unitPrice),
        totalPrice: Number(item.totalPrice),
        specifications: item.specifications
      })),
      payments: order.payments.map(payment => ({
        id: payment.id,
        amount: Number(payment.amount),
        status: payment.status,
        method: payment.paymentMethod,
        createdAt: payment.createdAt,
        escrowStatus: payment.escrowStatus
      })),
      timeline: timeline.map(log => ({
        id: log.id,
        status: log.action,
        timestamp: log.createdAt,
        notes: log.newValues ? JSON.stringify(log.newValues) : '',
        updatedBy: log.userId || undefined
      })),
      dispute: order.dispute ? {
        id: order.dispute.id,
        reason: order.dispute.reason,
        status: order.dispute.status,
        createdAt: order.dispute.createdAt,
        description: order.dispute.description
      } : undefined
    };
  }

  // Update order status
  async updateOrderStatus(orderId: string, status: OrderStatus, adminId: string, notes?: string): Promise<void> {
    await this.connect();
    
    const order = await this.prisma.order.update({
      where: { id: orderId },
      data: { 
        status,
        updatedAt: new Date()
      },
      include: {
        customer: true,
        producer: true
      }
    });

    // Log the action
    await this.prisma.auditLog.create({
      data: {
        userId: adminId,
        action: `UPDATE_ORDER_STATUS_${status}`,
        resource: 'ORDER',
        resourceId: orderId,
        newValues: { notes, newStatus: status, previousStatus: order.status }
      }
    });

    // Send notifications to customer and producer
    const notifications = [
      {
        userId: order.customerId,
        title: 'Order Status Updated',
        message: `Your order ${order.orderNumber} status has been updated to ${status.toLowerCase()}${notes ? `. Notes: ${notes}` : ''}`,
        notificationType: NotificationType.ORDER_CONFIRMED
      },
      {
        userId: order.producerId,
        title: 'Order Status Updated',
        message: `Order ${order.orderNumber} status has been updated to ${status.toLowerCase()}${notes ? `. Notes: ${notes}` : ''}`,
        notificationType: NotificationType.ORDER_CONFIRMED
      }
    ];

    await this.prisma.notification.createMany({
      data: notifications
    });

    // Clear order cache
    await this.redis.del(`order_details_${orderId}`);
  }

  // Cancel order
  async cancelOrder(orderId: string, adminId: string, reason: string): Promise<void> {
    await this.connect();
    
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      include: { payments: true }
    });

    if (!order) {
      throw new Error('Order not found');
    }

    if (order.status === 'DELIVERED' || order.status === 'CANCELLED') {
      throw new Error('Cannot cancel order in current status');
    }

    // Update order status
    await this.updateOrderStatus(orderId, 'CANCELLED', adminId, reason);

    // Process refunds for completed payments
    for (const payment of order.payments) {
      if (payment.status === 'COMPLETED') {
        await this.prisma.payment.update({
          where: { id: payment.id },
          data: { status: 'REFUNDED' }
        });

        // Release escrow if applicable
        if (payment.escrowStatus === 'HELD') {
          await this.prisma.payment.update({
            where: { id: payment.id },
            data: { escrowStatus: 'REFUNDED' }
          });
        }
      }
    }
  }

  // Get order statistics
  async getOrderStatistics(): Promise<OrderStatistics> {
    await this.connect();
    
    // Check cache first
    const cached = await this.redis.get('order_statistics');
    if (cached) {
      return JSON.parse(cached);
    }

    const [
      totalOrders,
      ordersByStatus,
      totalRevenue,
      orderTrend,
      topProducts
    ] = await Promise.all([
      this.prisma.order.count(),
      this.prisma.order.groupBy({
        by: ['status'],
        _count: true
      }),
      this.prisma.order.aggregate({
        _sum: { totalAmount: true }
      }),
      this.getOrderTrend(30),
      this.getTopProducts(10)
    ]);

    const averageOrderValue = totalOrders > 0 ? Number(totalRevenue._sum.totalAmount || 0) / totalOrders : 0;

    // Calculate fulfillment metrics (simplified)
    const fulfillmentMetrics = {
      averageProcessingTime: 3.5, // days
      onTimeDeliveryRate: 92.5, // percentage
      customerSatisfactionScore: 4.2 // out of 5
    };

    const statistics: OrderStatistics = {
      totalOrders,
      ordersByStatus: ordersByStatus.reduce((acc, item) => {
        acc[item.status] = item._count;
        return acc;
      }, {} as { [key: string]: number }),
      totalRevenue: Number(totalRevenue._sum.totalAmount || 0),
      averageOrderValue,
      orderTrend,
      topProducts,
      fulfillmentMetrics
    };

    // Cache for 10 minutes
    await this.redis.setEx('order_statistics', 600, JSON.stringify(statistics));

    return statistics;
  }

  // Get order trend
  private async getOrderTrend(days: number): Promise<{ date: string; count: number; revenue: number }[]> {
    const trend = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const [count, revenue] = await Promise.all([
        this.prisma.order.count({
          where: {
            createdAt: {
              gte: startOfDay,
              lte: endOfDay
            }
          }
        }),
        this.prisma.order.aggregate({
          _sum: { totalAmount: true },
          where: {
            createdAt: {
              gte: startOfDay,
              lte: endOfDay
            }
          }
        })
      ]);

      trend.push({
        date: startOfDay.toISOString().split('T')[0],
        count,
        revenue: Number(revenue._sum.totalAmount || 0)
      });
    }

    return trend;
  }

  // Get top products by orders and revenue
  private async getTopProducts(limit: number): Promise<{ productId: string; name: string; orders: number; revenue: number }[]> {
    const topProducts = await this.prisma.orderItem.groupBy({
      by: ['productId'],
      _count: true,
      _sum: { totalPrice: true },
      orderBy: { _count: { productId: 'desc' } },
      take: limit
    });

    const productsWithNames = await Promise.all(
      topProducts.map(async (item) => {
        const product = await this.prisma.product.findUnique({
          where: { id: item.productId },
          select: { name: true }
        });

        return {
          productId: item.productId,
          name: product?.name || 'Unknown Product',
          orders: item._count,
          revenue: Number(item._sum.totalPrice || 0)
        };
      })
    );

    return productsWithNames;
  }
}
