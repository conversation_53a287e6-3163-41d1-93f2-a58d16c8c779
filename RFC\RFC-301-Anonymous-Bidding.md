# RFC-301: <PERSON><PERSON><PERSON>

**Durum**: DRAFT  
**Yazar**: Augment Agent  
**Tarih**: 2025-06-27  
**Bağımlılıklar**: RFC-001, RFC-003, RFC-102  
**İlgili RFC'ler**: RFC-302, RFC-303, RFC-401, RFC-304  

## Özet

Bu RFC, Türkiye Doğal Taş Marketplace platformunun ana özelliği olan anonim teklif sistemini tanımlar. Sistem, müşterilerin üretici kimliklerini görmeden teklif alabilmesini ve üreticilerin müşteri bilgilerini görmeden rekabetçi teklifler verebilmesini sağlar.

## Motivasyon

Geleneksel B2B pazarlarda, teklif süreçlerinde tarafların birbirlerini tanıması rekabeti olumsuz etkileyebilir. Anonim teklif sistemi:
- Üreticiler arasında adil rekabet sağlar
- Müşterilerin önyargısız karar vermesini destekler
- Fiyat manipülasyonunu önler
- Pazar şeffaflığını artırır

## Detaylı Tasarım

### 1. Sistem Mimarisi

```
┌─────────────────────────────────────────────────────────────┐
│                    ANONYMOUS BIDDING SYSTEM                 │
├─────────────────────────────────────────────────────────────┤
│  Customer Side          │  System Core    │  Producer Side  │
├─────────────────────────┼─────────────────┼─────────────────┤
│ • Request Creation      │ • Anonymization │ • Bid Submission│
│ • Bid Comparison        │ • Privacy Layer │ • Competition   │
│ • Selection Process     │ • Escrow Trigger│ • Notification  │
│ • Identity Reveal       │ • Identity Mgmt │ • Order Process │
└─────────────────────────┴─────────────────┴─────────────────┘
```

### 2. Teklif Talep Süreci

#### 2.1 Müşteri Teklif Talebi
```typescript
interface BidRequest {
  id: string;
  anonymousId: string; // Public identifier
  customerId: string; // Hidden from producers
  
  // Request Details
  title: string;
  description: string;
  specifications: ProductSpecifications;
  quantity: number;
  unit: 'm2' | 'ton' | 'piece';
  
  // Delivery Information
  deliveryAddress: {
    country: string;
    city: string;
    // Detailed address hidden until selection
  };
  deliveryDate: Date;
  deliveryTerms: 'FOB' | 'CIF' | 'DDP';
  
  // Bidding Settings
  bidDeadline: Date;
  maxSuppliers: number;
  autoClose: boolean;
  
  // Privacy Settings
  showCompanySize: boolean;
  showIndustryType: boolean;
  showPreviousOrders: boolean;
}
```

#### 2.2 Üretici Bildirim Sistemi
```typescript
interface ProducerNotification {
  bidRequestId: string;
  anonymousRequestId: string; // Public ID
  
  // Visible Information
  requestSummary: {
    title: string;
    description: string;
    specifications: ProductSpecifications;
    quantity: number;
    deliveryCountry: string;
    deliveryTerms: string;
    deadline: Date;
  };
  
  // Hidden Information (null until selection)
  customerDetails: null;
  exactDeliveryAddress: null;
  customerHistory: null;
}
```

### 3. Teklif Verme Süreci

#### 3.1 Üretici Teklif Formu
```typescript
interface BidSubmission {
  id: string;
  anonymousId: string; // Public bid identifier
  bidRequestId: string;
  producerId: string; // Hidden from customer
  
  // Pricing
  unitPrice: number;
  totalPrice: number;
  currency: string;
  
  // Terms
  deliveryTimeDays: number;
  paymentTerms: string;
  validityDays: number;
  
  // Additional Info
  notes?: string;
  attachments?: FileReference[];
  
  // Quality Assurance
  certifications: string[];
  sampleAvailable: boolean;
  
  status: 'submitted' | 'selected' | 'rejected' | 'expired';
}
```

#### 3.2 Rekabet Görünürlüğü
```typescript
interface CompetitionView {
  bidRequestId: string;
  totalBids: number;
  priceRange: {
    lowest: number;
    highest: number;
    average: number;
  };
  myBidRanking: number; // 1st, 2nd, 3rd, etc.
  timeRemaining: number; // seconds until deadline
}
```

### 4. Müşteri Teklif Karşılaştırma

#### 4.1 Anonim Teklif Listesi
```typescript
interface AnonymousBidView {
  anonymousBidId: string;
  
  // Pricing Information
  unitPrice: number;
  totalPrice: number;
  currency: string;
  
  // Terms
  deliveryTime: number;
  paymentTerms: string;
  validity: number;
  
  // Producer Information (Anonymous)
  producerProfile: {
    anonymousId: string; // "Producer A", "Producer B", etc.
    experienceYears: number;
    productionCapacity: string; // "High", "Medium", "Low"
    certifications: string[];
    rating: number; // 1-5 stars
    completedOrders: number;
    location: {
      region: string; // "Marmara", "Aegean", etc.
      // Exact location hidden
    };
  };
  
  // Quality Indicators
  sampleAvailable: boolean;
  qualityGuarantee: boolean;
  previousCustomerRatings: number;
  
  ranking: number; // Based on price and quality score
}
```

#### 4.2 Teklif Sıralama Algoritması
```typescript
interface BidRankingCriteria {
  price: {
    weight: 0.4;
    score: number; // 0-100
  };
  deliveryTime: {
    weight: 0.2;
    score: number;
  };
  producerRating: {
    weight: 0.2;
    score: number;
  };
  qualityIndicators: {
    weight: 0.2;
    score: number;
  };
  totalScore: number;
}
```

### 5. Kimlik Açığa Çıkarma Süreci

#### 5.1 Teklif Seçimi ve Ön Ödeme
```typescript
interface BidSelection {
  selectedBidId: string;
  customerId: string;
  producerId: string; // Still hidden at this point
  
  // Escrow Payment Required
  escrowPayment: {
    amount: number; // 30% of total
    status: 'pending' | 'completed' | 'failed';
    paymentMethod: string;
  };
  
  // Identity reveal triggered after payment
  identityRevealStatus: 'pending' | 'revealed';
}
```

#### 5.2 Karşılıklı Bilgi Paylaşımı
```typescript
interface IdentityReveal {
  orderId: string;
  revealedAt: Date;
  
  // Customer Information (to Producer)
  customerDetails: {
    companyName: string;
    contactPerson: string;
    email: string;
    phone: string;
    address: FullAddress;
    businessType: string;
  };
  
  // Producer Information (to Customer)
  producerDetails: {
    companyName: string;
    contactPerson: string;
    email: string;
    phone: string;
    address: FullAddress;
    productionFacilities: FacilityInfo[];
  };
}
```

### 6. Gizlilik ve Güvenlik Protokolleri

#### 6.1 Veri Anonimleştirme
```typescript
class AnonymizationService {
  // Generate anonymous IDs
  generateAnonymousId(type: 'bid_request' | 'bid' | 'producer'): string {
    const prefix = type === 'bid_request' ? 'REQ' : 
                   type === 'bid' ? 'BID' : 'PROD';
    return `${prefix}_${crypto.randomUUID().substring(0, 8)}`;
  }
  
  // Anonymize producer profile
  anonymizeProducerProfile(producer: ProducerProfile): AnonymousProducerProfile {
    return {
      anonymousId: this.generateAnonymousId('producer'),
      experienceYears: producer.experienceYears,
      productionCapacity: this.categorizeCapacity(producer.capacity),
      certifications: producer.certifications,
      rating: producer.averageRating,
      completedOrders: producer.orderCount,
      location: {
        region: this.getRegion(producer.address.city)
      }
    };
  }
  
  // Anonymize customer request
  anonymizeCustomerRequest(request: BidRequest): AnonymousBidRequest {
    return {
      anonymousId: this.generateAnonymousId('bid_request'),
      title: request.title,
      description: request.description,
      specifications: request.specifications,
      quantity: request.quantity,
      deliveryCountry: request.deliveryAddress.country,
      deliveryTerms: request.deliveryTerms,
      deadline: request.bidDeadline
    };
  }
}
```

#### 6.2 Erişim Kontrolü
```typescript
class BiddingAccessControl {
  // Check if user can view bid request
  canViewBidRequest(userId: string, bidRequestId: string): boolean {
    const request = this.getBidRequest(bidRequestId);
    
    // Customer can see their own requests
    if (request.customerId === userId) return true;
    
    // Producers can see active requests in their categories
    if (this.isProducer(userId) && request.status === 'active') {
      return this.hasMatchingCapabilities(userId, request.specifications);
    }
    
    return false;
  }
  
  // Check if user can view bid details
  canViewBidDetails(userId: string, bidId: string): boolean {
    const bid = this.getBid(bidId);
    const request = this.getBidRequest(bid.bidRequestId);
    
    // Producer can see their own bids
    if (bid.producerId === userId) return true;
    
    // Customer can see bids for their requests (anonymized)
    if (request.customerId === userId) return true;
    
    return false;
  }
  
  // Check if identities can be revealed
  canRevealIdentities(orderId: string): boolean {
    const order = this.getOrder(orderId);
    const payment = this.getEscrowPayment(orderId);
    
    return payment.status === 'completed' && 
           order.status === 'confirmed';
  }
}
```

### 7. Real-time Bildirimler

#### 7.1 WebSocket Events
```typescript
interface BiddingWebSocketEvents {
  // For customers
  'new_bid_received': {
    bidRequestId: string;
    anonymousBidId: string;
    totalBids: number;
    priceRange: PriceRange;
  };
  
  'bid_deadline_approaching': {
    bidRequestId: string;
    timeRemaining: number; // minutes
  };
  
  // For producers
  'new_bid_request': {
    anonymousRequestId: string;
    category: string;
    deadline: Date;
  };
  
  'competition_update': {
    bidRequestId: string;
    totalCompetitors: number;
    myRanking: number;
  };
  
  // For both
  'identity_revealed': {
    orderId: string;
    counterpartyDetails: ContactDetails;
  };
}
```

### 8. API Endpoints

#### 8.1 Customer Endpoints
```typescript
// Create bid request
POST /api/bid-requests
Body: BidRequestCreateDto

// Get my bid requests
GET /api/bid-requests/my

// Get bids for a request (anonymized)
GET /api/bid-requests/:id/bids

// Select a bid
POST /api/bid-requests/:id/select-bid
Body: { bidId: string }
```

#### 8.2 Producer Endpoints
```typescript
// Get available bid requests
GET /api/bid-requests/available

// Submit a bid
POST /api/bids
Body: BidSubmissionDto

// Get my bids
GET /api/bids/my

// Get competition info
GET /api/bid-requests/:id/competition
```

### 9. Redis Entegrasyonu (PRD Gereksinimleri)

#### 9.1 Real-time Teklif Takibi
```typescript
class BiddingRedisService {
  private client: RedisClientType;

  constructor() {
    this.client = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
  }

  // Active bid requests tracking
  async addActiveBidRequest(bidRequestId: string, deadline: Date): Promise<void> {
    const score = deadline.getTime();
    await this.client.zAdd('active_bid_requests', {
      score,
      value: bidRequestId
    });
  }

  // Get expiring bid requests (48-72 saat - PRD requirement)
  async getExpiringBidRequests(hoursFromNow: number = 2): Promise<string[]> {
    const maxScore = Date.now() + (hoursFromNow * 60 * 60 * 1000);
    return await this.client.zRangeByScore('active_bid_requests', 0, maxScore);
  }

  // Track bid competition in real-time
  async updateBidCompetition(bidRequestId: string, bidData: {
    totalBids: number;
    lowestPrice: number;
    highestPrice: number;
    averagePrice: number;
  }): Promise<void> {
    const key = `bid_competition:${bidRequestId}`;
    await this.client.hSet(key, {
      total_bids: bidData.totalBids.toString(),
      lowest_price: bidData.lowestPrice.toString(),
      highest_price: bidData.highestPrice.toString(),
      average_price: bidData.averagePrice.toString(),
      last_updated: Date.now().toString()
    });

    // Set expiration (cleanup after bid deadline + 7 days)
    await this.client.expire(key, 7 * 24 * 60 * 60);
  }

  // Get real-time competition data
  async getBidCompetition(bidRequestId: string): Promise<BidCompetitionData | null> {
    const key = `bid_competition:${bidRequestId}`;
    const data = await this.client.hGetAll(key);

    if (Object.keys(data).length === 0) return null;

    return {
      totalBids: parseInt(data.total_bids),
      lowestPrice: parseFloat(data.lowest_price),
      highestPrice: parseFloat(data.highest_price),
      averagePrice: parseFloat(data.average_price),
      lastUpdated: new Date(parseInt(data.last_updated))
    };
  }

  // Producer ranking cache (PRD: Kendi teklif sıralaması)
  async updateProducerRanking(bidRequestId: string, producerId: string, ranking: number): Promise<void> {
    const key = `producer_ranking:${bidRequestId}`;
    await this.client.hSet(key, producerId, ranking.toString());
    await this.client.expire(key, 7 * 24 * 60 * 60);
  }

  async getProducerRanking(bidRequestId: string, producerId: string): Promise<number | null> {
    const ranking = await this.client.hGet(`producer_ranking:${bidRequestId}`, producerId);
    return ranking ? parseInt(ranking) : null;
  }
}
```

#### 9.2 Anonim ID Yönetimi
```typescript
class AnonymousIdService {
  private client: RedisClientType;

  // Generate and cache anonymous IDs
  async generateAnonymousBidId(bidId: string): Promise<string> {
    const anonymousId = `BID_${crypto.randomBytes(4).toString('hex').toUpperCase()}`;

    // Bidirectional mapping for anonymization
    await this.client.setEx(`anon_bid:${bidId}`, 24 * 60 * 60, anonymousId);
    await this.client.setEx(`bid_anon:${anonymousId}`, 24 * 60 * 60, bidId);

    return anonymousId;
  }

  async generateAnonymousProducerId(producerId: string, bidRequestId: string): Promise<string> {
    // Check if producer already has anonymous ID for this request
    const existingId = await this.client.get(`anon_producer:${bidRequestId}:${producerId}`);
    if (existingId) return existingId;

    const anonymousId = `PRODUCER_${crypto.randomBytes(3).toString('hex').toUpperCase()}`;

    // Cache for the duration of bid request + 7 days
    const ttl = 30 * 24 * 60 * 60; // 30 days
    await this.client.setEx(`anon_producer:${bidRequestId}:${producerId}`, ttl, anonymousId);
    await this.client.setEx(`producer_anon:${bidRequestId}:${anonymousId}`, ttl, producerId);

    return anonymousId;
  }

  // Resolve anonymous IDs (only after payment - PRD requirement)
  async resolveAnonymousIds(bidRequestId: string, selectedBidId: string): Promise<{
    customerId: string;
    producerId: string;
  }> {
    // This should only be called after escrow payment is confirmed
    const realBidId = await this.client.get(`bid_anon:${selectedBidId}`);
    if (!realBidId) throw new Error('Invalid anonymous bid ID');

    // Get real IDs from database
    const bid = await this.getBidFromDatabase(realBidId);
    const bidRequest = await this.getBidRequestFromDatabase(bidRequestId);

    return {
      customerId: bidRequest.customerId,
      producerId: bid.producerId
    };
  }
}
```

#### 9.3 Real-time Bildirimler (PRD: Otomatik bildirim sistemi)
```typescript
class BiddingNotificationService {
  private client: RedisClientType;
  private pubSub: RedisClientType;

  // Notify producers of new bid requests
  async notifyProducersOfNewRequest(bidRequest: BidRequest): Promise<void> {
    // Find matching producers based on capabilities
    const matchingProducers = await this.findMatchingProducers(bidRequest.specifications);

    for (const producerId of matchingProducers) {
      // Add to producer's notification queue
      const notification = {
        type: 'new_bid_request',
        bidRequestId: bidRequest.anonymousId,
        title: bidRequest.title,
        category: bidRequest.specifications.category,
        deadline: bidRequest.bidDeadline,
        deliveryCountry: bidRequest.deliveryAddress.country,
        timestamp: Date.now()
      };

      await this.client.lPush(`notifications:${producerId}`, JSON.stringify(notification));

      // Publish real-time notification
      await this.pubSub.publish(`producer:${producerId}`, JSON.stringify({
        event: 'new_bid_request',
        data: notification
      }));
    }
  }

  // Notify customer of new bids (PRD: Sadece toplam teklif tutarları görünür)
  async notifyCustomerOfNewBid(bidRequestId: string, customerId: string): Promise<void> {
    // Get updated competition data
    const competition = await this.getBidCompetition(bidRequestId);

    const notification = {
      type: 'new_bid_received',
      bidRequestId,
      totalBids: competition.totalBids,
      priceRange: {
        lowest: competition.lowestPrice,
        highest: competition.highestPrice,
        average: competition.averagePrice
      },
      timestamp: Date.now()
    };

    await this.client.lPush(`notifications:${customerId}`, JSON.stringify(notification));

    // Real-time update
    await this.pubSub.publish(`customer:${customerId}`, JSON.stringify({
      event: 'new_bid_received',
      data: notification
    }));
  }

  // Deadline approaching notifications (PRD: 48-72 saat)
  async notifyDeadlineApproaching(): Promise<void> {
    // Get bid requests expiring in next 2 hours
    const expiringRequests = await this.getExpiringBidRequests(2);

    for (const bidRequestId of expiringRequests) {
      const bidRequest = await this.getBidRequestFromDatabase(bidRequestId);

      // Notify customer
      await this.client.lPush(`notifications:${bidRequest.customerId}`, JSON.stringify({
        type: 'bid_deadline_approaching',
        bidRequestId,
        timeRemaining: Math.floor((bidRequest.bidDeadline.getTime() - Date.now()) / 1000 / 60), // minutes
        timestamp: Date.now()
      }));

      // Notify all bidding producers
      const bids = await this.getBidsForRequest(bidRequestId);
      for (const bid of bids) {
        await this.client.lPush(`notifications:${bid.producerId}`, JSON.stringify({
          type: 'bid_deadline_approaching',
          bidRequestId: bidRequest.anonymousId,
          timeRemaining: Math.floor((bidRequest.bidDeadline.getTime() - Date.now()) / 1000 / 60),
          timestamp: Date.now()
        }));
      }
    }
  }
}
```

## Implementasyon

### Faz 1: Temel Teklif Sistemi (Hafta 1-4)
1. **Veritabanı şeması oluşturma** (RFC-003 referansı)
2. **Redis entegrasyonu** (real-time tracking)
3. **Temel API endpoints** (anonim teklif CRUD)
4. **Anonimleştirme servisleri** (ID masking)
5. **Basit UI bileşenleri** (teklif formu, liste)

### Faz 2: Gelişmiş Özellikler (Hafta 5-8)
1. **Real-time bildirimler** (WebSocket + Redis Pub/Sub)
2. **Rekabet görünürlüğü** (PRD: toplam tutarlar, sıralama)
3. **Teklif sıralama algoritması** (fiyat + kalite skoru)
4. **Kimlik açığa çıkarma süreci** (escrow ödeme sonrası)
5. **Otomatik deadline yönetimi** (48-72 saat PRD gereksinimi)

### Faz 3: Güvenlik ve Optimizasyon (Hafta 9-12)
1. **Güvenlik testleri** (privacy leak detection)
2. **Performans optimizasyonu** (Redis caching strategies)
3. **Fraud detection** (suspicious bidding patterns)
4. **Analytics entegrasyonu** (bid success rates, market trends)
5. **Mobile optimization** (responsive bidding interface)

### 10. PRD Gereksinimlerinin Implementasyonu

#### 10.1 Escrow Ödeme Entegrasyonu (PRD: %30 ön ödeme)
```typescript
class EscrowPaymentService {
  private redis: RedisClientType;

  // Trigger escrow payment after bid selection
  async initializeEscrowPayment(bidSelectionData: {
    bidRequestId: string;
    selectedBidId: string;
    customerId: string;
    totalAmount: number;
  }): Promise<EscrowPayment> {
    const escrowAmount = bidSelectionData.totalAmount * 0.3; // 30% PRD requirement

    const escrowPayment = {
      id: crypto.randomUUID(),
      orderId: crypto.randomUUID(),
      bidRequestId: bidSelectionData.bidRequestId,
      selectedBidId: bidSelectionData.selectedBidId,
      customerId: bidSelectionData.customerId,
      amount: escrowAmount,
      totalAmount: bidSelectionData.totalAmount,
      status: 'pending',
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours to pay
    };

    // Cache escrow payment data
    await this.redis.setEx(
      `escrow:${escrowPayment.id}`,
      24 * 60 * 60,
      JSON.stringify(escrowPayment)
    );

    return escrowPayment;
  }

  // Process bank transfer receipt upload (PRD: dekont yükleme)
  async processBankTransferReceipt(escrowId: string, receiptData: {
    receiptUrl: string;
    uploadedBy: string;
    bankReference: string;
  }): Promise<void> {
    const escrowData = await this.redis.get(`escrow:${escrowId}`);
    if (!escrowData) throw new Error('Escrow payment not found');

    const escrow = JSON.parse(escrowData);
    escrow.bankTransferReceipt = receiptData;
    escrow.status = 'receipt_uploaded';
    escrow.receiptUploadedAt = new Date();

    await this.redis.setEx(`escrow:${escrowId}`, 24 * 60 * 60, JSON.stringify(escrow));

    // Notify admin for verification (PRD: admin kontrol eder)
    await this.notifyAdminForVerification(escrowId, receiptData);
  }

  // Admin verification (PRD: admin onaylarsa ödeme olmüş olur)
  async adminVerifyPayment(escrowId: string, adminId: string, approved: boolean): Promise<void> {
    const escrowData = await this.redis.get(`escrow:${escrowId}`);
    if (!escrowData) throw new Error('Escrow payment not found');

    const escrow = JSON.parse(escrowData);
    escrow.adminVerification = {
      verifiedBy: adminId,
      verifiedAt: new Date(),
      approved
    };
    escrow.status = approved ? 'verified' : 'rejected';

    await this.redis.setEx(`escrow:${escrowId}`, 7 * 24 * 60 * 60, JSON.stringify(escrow));

    if (approved) {
      // Trigger identity reveal (PRD: bilgi paylaşımı)
      await this.triggerIdentityReveal(escrow);
    }
  }

  // Identity reveal after payment confirmation
  private async triggerIdentityReveal(escrow: EscrowPayment): Promise<void> {
    // Resolve anonymous IDs to real IDs
    const identities = await this.anonymousIdService.resolveAnonymousIds(
      escrow.bidRequestId,
      escrow.selectedBidId
    );

    // Create order and reveal identities
    const order = await this.createOrder(escrow, identities);

    // Notify both parties with contact details
    await this.notifyIdentityReveal(order, identities);
  }
}
```

#### 10.2 Platform Komisyon Hesaplama (PRD: m2 başına $1, ton başına $10)
```typescript
class CommissionCalculationService {
  // Calculate platform commission based on PRD requirements
  calculateCommission(orderData: {
    items: OrderItem[];
    unit: 'm2' | 'ton' | 'piece';
    totalQuantity: number;
  }): number {
    let commission = 0;

    for (const item of orderData.items) {
      if (item.unit === 'm2') {
        commission += item.quantity * 1; // $1 per m2
      } else if (item.unit === 'ton') {
        commission += item.quantity * 10; // $10 per ton
      }
      // No commission for 'piece' items as per PRD
    }

    return commission;
  }

  // Update order with commission calculation
  async updateOrderWithCommission(orderId: string): Promise<void> {
    const order = await this.getOrderFromDatabase(orderId);
    const commission = this.calculateCommission({
      items: order.items,
      unit: order.unit,
      totalQuantity: order.totalQuantity
    });

    // Update order in database
    await this.updateOrderCommission(orderId, commission);

    // Cache commission data for quick access
    await this.redis.setEx(
      `commission:${orderId}`,
      30 * 24 * 60 * 60,
      commission.toString()
    );
  }
}
```

#### 10.3 Teklif Süresi Yönetimi (PRD: 48-72 saat)
```typescript
class BidDeadlineService {
  private redis: RedisClientType;

  // Set bid deadline (48-72 hours as per PRD)
  async setBidDeadline(bidRequestId: string, hours: number = 72): Promise<Date> {
    if (hours < 48 || hours > 72) {
      throw new Error('Bid deadline must be between 48-72 hours');
    }

    const deadline = new Date(Date.now() + hours * 60 * 60 * 1000);

    // Add to Redis sorted set for deadline tracking
    await this.redis.zAdd('bid_deadlines', {
      score: deadline.getTime(),
      value: bidRequestId
    });

    return deadline;
  }

  // Background job to check expiring bids
  async checkExpiringBids(): Promise<void> {
    const now = Date.now();
    const twoHoursFromNow = now + (2 * 60 * 60 * 1000);

    // Get bids expiring in next 2 hours
    const expiringBids = await this.redis.zRangeByScore('bid_deadlines', now, twoHoursFromNow);

    for (const bidRequestId of expiringBids) {
      await this.notifyDeadlineApproaching(bidRequestId);
    }

    // Get expired bids
    const expiredBids = await this.redis.zRangeByScore('bid_deadlines', 0, now);

    for (const bidRequestId of expiredBids) {
      await this.closeBidRequest(bidRequestId);
      await this.redis.zRem('bid_deadlines', bidRequestId);
    }
  }

  private async closeBidRequest(bidRequestId: string): Promise<void> {
    // Update bid request status to closed
    await this.updateBidRequestStatus(bidRequestId, 'closed');

    // Notify customer and producers
    await this.notifyBidRequestClosed(bidRequestId);
  }
}
```

## Güvenlik Değerlendirmesi

### Gizlilik Riskleri
- **Identity Leakage**: Veri analizi ile kimlik tespiti riski
- **Timing Attacks**: Yanıt sürelerinden bilgi çıkarma
- **Side Channel**: Metadata'dan kimlik tespiti
- **Redis Data Exposure**: Cache'deki hassas verilerin korunması

### Güvenlik Önlemleri
- **Data Minimization**: Minimum gerekli veri paylaşımı
- **Noise Injection**: Sahte verilerle karıştırma
- **Access Logging**: Tüm erişimlerin loglanması
- **Regular Audits**: Gizlilik ihlali kontrolü
- **Redis Security**: AUTH, TLS, network isolation
- **TTL Management**: Otomatik veri temizleme
- **Encryption**: Hassas verilerin şifrelenmesi

## Performans Etkisi

### Redis Optimizasyon Stratejileri
- **Connection Pooling**: Redis connection pool yönetimi
- **Pipeline Operations**: Toplu Redis operasyonları
- **Memory Management**: TTL ile otomatik cleanup
- **Pub/Sub Optimization**: Efficient real-time messaging
- **Data Structures**: Optimal Redis data types kullanımı

```typescript
class RedisOptimizationService {
  private client: RedisClientType;

  // Batch operations for better performance
  async batchUpdateBidCompetition(updates: BidCompetitionUpdate[]): Promise<void> {
    const pipeline = this.client.multi();

    for (const update of updates) {
      const key = `bid_competition:${update.bidRequestId}`;
      pipeline.hSet(key, {
        total_bids: update.totalBids.toString(),
        lowest_price: update.lowestPrice.toString(),
        highest_price: update.highestPrice.toString(),
        average_price: update.averagePrice.toString(),
        last_updated: Date.now().toString()
      });
      pipeline.expire(key, 7 * 24 * 60 * 60);
    }

    await pipeline.exec();
  }

  // Memory-efficient notification queues
  async cleanupOldNotifications(): Promise<void> {
    const users = await this.getAllActiveUsers();
    const pipeline = this.client.multi();

    for (const userId of users) {
      // Keep only last 100 notifications per user
      pipeline.lTrim(`notifications:${userId}`, 0, 99);
    }

    await pipeline.exec();
  }

  // Efficient anonymous ID lookup
  async batchResolveAnonymousIds(anonymousIds: string[]): Promise<Map<string, string>> {
    const pipeline = this.client.multi();

    for (const anonymousId of anonymousIds) {
      pipeline.get(`bid_anon:${anonymousId}`);
    }

    const results = await pipeline.exec();
    const resolved = new Map<string, string>();

    anonymousIds.forEach((anonymousId, index) => {
      if (results[index] && results[index].result) {
        resolved.set(anonymousId, results[index].result as string);
      }
    });

    return resolved;
  }
}
```

### Caching Strategies
- **Anonim Profiller**: Producer profiles cache (1 hour TTL)
- **Teklif Verileri**: Bid competition data (real-time updates)
- **Bildirimler**: Notification queues (auto-cleanup)
- **Session Data**: User sessions (24 hour TTL)

## Alternatifler

### Sealed Bid Auction
- Teklifler açılana kadar gizli
- Daha basit implementasyon
- Daha az interaktivite

### Dutch Auction
- Fiyat düşürme sistemi
- Hızlı karar verme
- Farklı pazar dinamiği

## Test Stratejisi

### Unit Tests
```typescript
describe('AnonymousBiddingService', () => {
  let service: AnonymousBiddingService;
  let redisClient: RedisClientType;

  beforeEach(async () => {
    redisClient = createClient({ url: 'redis://localhost:6379' });
    await redisClient.connect();
    service = new AnonymousBiddingService(redisClient);
  });

  test('should generate unique anonymous IDs', async () => {
    const bidId1 = await service.generateAnonymousBidId('bid-123');
    const bidId2 = await service.generateAnonymousBidId('bid-456');

    expect(bidId1).toMatch(/^BID_[A-F0-9]{8}$/);
    expect(bidId2).toMatch(/^BID_[A-F0-9]{8}$/);
    expect(bidId1).not.toBe(bidId2);
  });

  test('should maintain bid competition data', async () => {
    await service.updateBidCompetition('req-123', {
      totalBids: 5,
      lowestPrice: 1000,
      highestPrice: 1500,
      averagePrice: 1250
    });

    const competition = await service.getBidCompetition('req-123');
    expect(competition.totalBids).toBe(5);
    expect(competition.lowestPrice).toBe(1000);
  });
});
```

### Integration Tests
```typescript
describe('Bidding Flow Integration', () => {
  test('complete anonymous bidding flow', async () => {
    // 1. Customer creates bid request
    const bidRequest = await createBidRequest(customerData);
    expect(bidRequest.anonymousId).toBeDefined();

    // 2. Producers receive notifications
    const notifications = await getProducerNotifications();
    expect(notifications.length).toBeGreaterThan(0);

    // 3. Producers submit bids
    const bid = await submitBid(producerData, bidRequest.id);
    expect(bid.anonymousId).toBeDefined();

    // 4. Customer sees anonymous bids
    const anonymousBids = await getAnonymousBids(bidRequest.id);
    expect(anonymousBids[0].producerProfile.anonymousId).toBeDefined();
    expect(anonymousBids[0].producerProfile.companyName).toBeUndefined();

    // 5. Customer selects bid and makes payment
    const escrow = await selectBidAndPay(bidRequest.id, bid.anonymousId);
    expect(escrow.status).toBe('verified');

    // 6. Identities are revealed
    const identities = await getRevealedIdentities(escrow.orderId);
    expect(identities.customerDetails.companyName).toBeDefined();
    expect(identities.producerDetails.companyName).toBeDefined();
  });
});
```

## Monitoring ve Analytics

### Key Metrics
```typescript
interface BiddingMetrics {
  // Performance Metrics
  averageResponseTime: number; // ms
  bidRequestsPerHour: number;
  bidsPerRequest: number;

  // Business Metrics
  conversionRate: number; // bids to orders
  averageBidValue: number;
  competitionLevel: number; // avg bids per request

  // Privacy Metrics
  anonymityBreaches: number; // should be 0
  identityLeaks: number; // should be 0

  // System Health
  redisLatency: number;
  cacheHitRate: number;
  notificationDeliveryRate: number;
}
```

### Monitoring Dashboard
```typescript
class BiddingMonitoringService {
  async generateDailyReport(): Promise<BiddingMetrics> {
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

    return {
      averageResponseTime: await this.getAverageResponseTime(yesterday, today),
      bidRequestsPerHour: await this.getBidRequestsPerHour(yesterday, today),
      bidsPerRequest: await this.getAverageBidsPerRequest(yesterday, today),
      conversionRate: await this.getConversionRate(yesterday, today),
      averageBidValue: await this.getAverageBidValue(yesterday, today),
      competitionLevel: await this.getCompetitionLevel(yesterday, today),
      anonymityBreaches: await this.getAnonymityBreaches(yesterday, today),
      identityLeaks: await this.getIdentityLeaks(yesterday, today),
      redisLatency: await this.getRedisLatency(),
      cacheHitRate: await this.getCacheHitRate(yesterday, today),
      notificationDeliveryRate: await this.getNotificationDeliveryRate(yesterday, today)
    };
  }
}
```

## Gelecek Çalışmalar

1. **AI-Powered Matching**: Otomatik üretici-müşteri eşleştirme
2. **Blockchain Integration**: Şeffaflık ve güven artırma
3. **Advanced Analytics**: Pazar trend analizi
4. **Mobile Optimization**: Mobil teklif verme deneyimi
5. **Smart Contracts**: Otomatik escrow yönetimi
6. **Machine Learning**: Fraud detection ve risk analizi
7. **Multi-language Support**: Çoklu dil desteği
8. **API Rate Limiting**: Redis ile gelişmiş rate limiting

## Özet ve Sonuç

Bu RFC, PRD gereksinimlerine tam uyumlu bir anonim teklif sistemi tasarımı sunmaktadır:

### ✅ PRD Gereksinimleri Karşılandı
- **48-72 saat teklif süresi**: Redis ile otomatik deadline yönetimi
- **Anonim sistem**: Kimlik maskeleme ve güvenli ID yönetimi
- **%30 ön ödeme**: Escrow sistemi ile güvenli ödeme
- **Banka havalesi**: Dekont yükleme ve admin onay süreci
- **Komisyon hesaplama**: m² başına $1, ton başına $10
- **Real-time bildirimler**: Redis Pub/Sub ile anlık güncellemeler
- **Rekabet görünürlüğü**: Toplam tutarlar ve sıralama

### 🚀 Teknik Özellikler
- **Redis Entegrasyonu**: High-performance caching ve real-time data
- **Scalable Architecture**: Mikroservis uyumlu tasarım
- **Security First**: Çoklu katmanlı gizlilik koruması
- **Monitoring Ready**: Kapsamlı metrik ve izleme sistemi

---

**Doküman Bilgileri**:
- **RFC Numarası**: RFC-301
- **Versiyon**: 2.0
- **Son Güncelleme**: 2025-06-28
- **Durum**: DRAFT → REVIEW
- **Onay Bekleyen**: Tech Lead, Security Team

**Bağlantılı RFC'ler**:
- RFC-001: Sistem mimarisindeki teklif sistemi yeri
- RFC-003: Teklif sistemi veri yapıları
- RFC-102: Kimlik doğrulama entegrasyonu
- RFC-302: Sipariş yönetimi entegrasyonu
- RFC-303: Gizlilik ve güvenlik protokolleri
- RFC-401: Escrow ödeme sistemi entegrasyonu
- RFC-304: Bildirim sistemi entegrasyonu

**İletişim**:
- **Yazar**: Augment Agent
- **Email**: <EMAIL>
- **Slack**: #anonymous-bidding
