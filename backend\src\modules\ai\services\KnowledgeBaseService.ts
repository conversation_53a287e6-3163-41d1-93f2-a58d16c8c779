/**
 * Knowledge Base Service
 * Manages product knowledge, process information, and FAQ data
 */

import { 
  KnowledgeBase, 
  ProductKnowledgeBase, 
  ProcessKnowledgeBase, 
  FAQ,
  StoneTypeInfo,
  ChatbotIntent 
} from '../types';

export class KnowledgeBaseService {
  private knowledgeBase: KnowledgeBase;

  constructor() {
    this.knowledgeBase = this.initializeKnowledgeBase();
  }

  /**
   * Get knowledge base
   */
  getKnowledgeBase(): KnowledgeBase {
    return this.knowledgeBase;
  }

  /**
   * Search FAQ by keywords
   */
  searchFAQ(query: string, language: string = 'en'): FAQ[] {
    const keywords = query.toLowerCase().split(' ');
    
    return this.knowledgeBase.faqDatabase
      .filter(faq => faq.language === language)
      .filter(faq => {
        const searchText = `${faq.question} ${faq.answer} ${faq.keywords.join(' ')}`.toLowerCase();
        return keywords.some(keyword => searchText.includes(keyword));
      })
      .slice(0, 5); // Return top 5 matches
  }

  /**
   * Get stone type information
   */
  getStoneTypeInfo(stoneType: string): StoneTypeInfo | null {
    const normalizedType = stoneType.toLowerCase();
    return this.knowledgeBase.productKnowledge.stoneTypes[normalizedType] || null;
  }

  /**
   * Get process information by intent
   */
  getProcessInfo(intent: ChatbotIntent): any {
    const processMap: Partial<Record<ChatbotIntent, any>> = {
      [ChatbotIntent.ACCOUNT_SETUP]: this.knowledgeBase.processKnowledge.registration,
      [ChatbotIntent.BID_PROCESS]: this.knowledgeBase.processKnowledge.bidding,
      [ChatbotIntent.ORDER_STATUS]: this.knowledgeBase.processKnowledge.orderProcess,
      [ChatbotIntent.COMPLAINT]: this.knowledgeBase.processKnowledge.disputeResolution,
      [ChatbotIntent.PRODUCT_INQUIRY]: this.knowledgeBase.productKnowledge,
      [ChatbotIntent.PRODUCT_PRICING]: this.knowledgeBase.productKnowledge.pricingGuidelines,
      [ChatbotIntent.ORDER_TRACKING]: this.knowledgeBase.processKnowledge.orderProcess,
      [ChatbotIntent.PAYMENT_METHODS]: this.knowledgeBase.processKnowledge.orderProcess,
      [ChatbotIntent.GREETING]: this.knowledgeBase.processKnowledge,
      [ChatbotIntent.GOODBYE]: this.knowledgeBase.processKnowledge,
      [ChatbotIntent.UNKNOWN]: null
    };

    return processMap[intent] || null;
  }

  /**
   * Get pricing guidelines
   */
  getPricingGuidelines(stoneType?: string) {
    const guidelines = this.knowledgeBase.productKnowledge.pricingGuidelines;
    
    if (stoneType) {
      const ranges = guidelines.marketRanges.filter(range => 
        range.stoneType.toLowerCase() === stoneType.toLowerCase()
      );
      return { ...guidelines, marketRanges: ranges };
    }
    
    return guidelines;
  }

  /**
   * Add new FAQ
   */
  addFAQ(faq: FAQ): void {
    this.knowledgeBase.faqDatabase.push(faq);
  }

  /**
   * Update FAQ
   */
  updateFAQ(question: string, updates: Partial<FAQ>): boolean {
    const index = this.knowledgeBase.faqDatabase.findIndex(faq => faq.question === question);
    if (index !== -1) {
      this.knowledgeBase.faqDatabase[index] = { 
        ...this.knowledgeBase.faqDatabase[index], 
        ...updates 
      };
      return true;
    }
    return false;
  }

  /**
   * Initialize knowledge base with default data
   */
  private initializeKnowledgeBase(): KnowledgeBase {
    return {
      productKnowledge: this.initializeProductKnowledge(),
      processKnowledge: this.initializeProcessKnowledge(),
      faqDatabase: this.initializeFAQDatabase()
    };
  }

  /**
   * Initialize product knowledge
   */
  private initializeProductKnowledge(): ProductKnowledgeBase {
    return {
      stoneTypes: {
        marble: {
          name: 'Marble',
          properties: {
            density: 2.7,
            hardness: 3,
            waterAbsorption: 0.2,
            freezeThawResistance: 'Good'
          },
          applications: ['Flooring', 'Wall cladding', 'Countertops', 'Sculptures'],
          maintenanceRequirements: ['Regular sealing', 'Avoid acidic cleaners', 'Polish periodically'],
          commonQuestions: [
            {
              question: 'What is the best marble for kitchen countertops?',
              answer: 'Carrara and Calacatta marbles are popular choices for kitchen countertops due to their durability and beauty.',
              category: 'product',
              language: 'en',
              keywords: ['marble', 'kitchen', 'countertop', 'carrara', 'calacatta']
            }
          ]
        },
        travertine: {
          name: 'Travertine',
          properties: {
            density: 2.5,
            hardness: 3,
            waterAbsorption: 2.0,
            freezeThawResistance: 'Moderate'
          },
          applications: ['Flooring', 'Pool coping', 'Exterior facades', 'Bathroom tiles'],
          maintenanceRequirements: ['Regular sealing required', 'Fill holes if needed', 'Gentle cleaning'],
          commonQuestions: []
        },
        granite: {
          name: 'Granite',
          properties: {
            density: 2.8,
            hardness: 6,
            waterAbsorption: 0.1,
            freezeThawResistance: 'Excellent'
          },
          applications: ['Countertops', 'Flooring', 'Monuments', 'Exterior cladding'],
          maintenanceRequirements: ['Minimal maintenance', 'Occasional sealing', 'Regular cleaning'],
          commonQuestions: []
        },
        onyx: {
          name: 'Onyx',
          properties: {
            density: 2.6,
            hardness: 3,
            waterAbsorption: 0.5,
            freezeThawResistance: 'Poor'
          },
          applications: ['Decorative panels', 'Backlighting features', 'Luxury interiors'],
          maintenanceRequirements: ['Careful handling', 'Avoid moisture', 'Professional installation'],
          commonQuestions: []
        }
      },
      technicalSpecs: {
        dimensions: {
          standardSizes: ['30x30cm', '60x60cm', '80x80cm', '120x60cm'],
          customSizes: true,
          tolerances: '±1mm'
        },
        finishTypes: {
          types: ['Polished', 'Honed', 'Brushed', 'Sandblasted', 'Flamed', 'Antiqued'],
          descriptions: {
            'Polished': 'High-gloss mirror finish',
            'Honed': 'Smooth matte finish',
            'Brushed': 'Textured surface with brush marks',
            'Sandblasted': 'Rough textured surface',
            'Flamed': 'Rough surface created by flame treatment',
            'Antiqued': 'Aged appearance with rounded edges'
          },
          applications: {
            'Polished': ['Interior floors', 'Countertops', 'Wall cladding'],
            'Honed': ['Bathroom floors', 'Pool areas', 'Exterior paving'],
            'Brushed': ['Exterior facades', 'Non-slip surfaces'],
            'Sandblasted': ['Exterior walls', 'Non-slip applications'],
            'Flamed': ['Exterior paving', 'Steps', 'Pool coping'],
            'Antiqued': ['Traditional architecture', 'Restoration projects']
          }
        },
        qualityStandards: {
          standards: ['EN 12058', 'ASTM C503', 'ISO 9001'],
          certifications: ['CE Marking', 'ISO 9001', 'ISO 14001'],
          testMethods: ['Water absorption test', 'Flexural strength test', 'Freeze-thaw test']
        },
        installationGuides: [
          {
            title: 'Marble Floor Installation',
            steps: [
              'Prepare substrate',
              'Apply adhesive',
              'Place tiles with spacers',
              'Check alignment',
              'Apply grout',
              'Clean surface',
              'Apply sealer'
            ],
            tools: ['Trowel', 'Level', 'Spacers', 'Grout float', 'Sponge'],
            tips: ['Acclimate tiles before installation', 'Use appropriate adhesive', 'Seal natural stone']
          }
        ]
      },
      pricingGuidelines: {
        factors: [
          { name: 'Stone type', impact: 'high', description: 'Different stones have different base costs' },
          { name: 'Quality grade', impact: 'high', description: 'Premium grades cost more' },
          { name: 'Finish type', impact: 'medium', description: 'Complex finishes increase cost' },
          { name: 'Thickness', impact: 'medium', description: 'Thicker slabs cost more' },
          { name: 'Quantity', impact: 'high', description: 'Bulk orders get better pricing' },
          { name: 'Delivery location', impact: 'medium', description: 'Shipping costs vary by destination' }
        ],
        marketRanges: [
          { stoneType: 'marble', minPrice: 25, maxPrice: 150, currency: 'USD', unit: 'm²' },
          { stoneType: 'travertine', minPrice: 20, maxPrice: 80, currency: 'USD', unit: 'm²' },
          { stoneType: 'granite', minPrice: 30, maxPrice: 200, currency: 'USD', unit: 'm²' },
          { stoneType: 'onyx', minPrice: 100, maxPrice: 500, currency: 'USD', unit: 'm²' }
        ],
        calculationMethods: [
          {
            name: 'Per square meter',
            formula: 'Price = Base price × Area × Quality multiplier × Finish multiplier',
            variables: ['Base price', 'Area', 'Quality multiplier', 'Finish multiplier']
          },
          {
            name: 'Per ton (blocks)',
            formula: 'Price = Base price per ton × Weight × Quality grade',
            variables: ['Base price per ton', 'Weight', 'Quality grade']
          }
        ]
      }
    };
  }

  /**
   * Initialize process knowledge
   */
  private initializeProcessKnowledge(): ProcessKnowledgeBase {
    return {
      registration: {
        steps: [
          { order: 1, title: 'Create Account', description: 'Fill in basic company information', duration: '5 minutes' },
          { order: 2, title: 'Upload Documents', description: 'Provide business registration and certificates', duration: '10 minutes' },
          { order: 3, title: 'Verification', description: 'Admin reviews and verifies documents', duration: '1-3 business days' },
          { order: 4, title: 'Profile Setup', description: 'Complete detailed company profile', duration: '15 minutes' },
          { order: 5, title: 'Account Activation', description: 'Account is activated for trading', duration: 'Immediate' }
        ],
        requirements: [
          { name: 'Business Registration', type: 'document', mandatory: true, description: 'Official business registration certificate' },
          { name: 'Tax ID', type: 'information', mandatory: true, description: 'Company tax identification number' },
          { name: 'Bank Details', type: 'information', mandatory: true, description: 'Company bank account information' }
        ],
        commonIssues: [
          { problem: 'Document upload fails', solution: 'Check file format (PDF, JPG, PNG) and size (max 10MB)', frequency: 'common' },
          { problem: 'Verification takes too long', solution: 'Contact support if no response within 3 business days', frequency: 'rare' }
        ]
      },
      bidding: {
        steps: [
          { order: 1, title: 'Browse Products', description: 'Find products that match your requirements' },
          { order: 2, title: 'Create Bid Request', description: 'Specify product details, quantity, and delivery requirements' },
          { order: 3, title: 'Receive Bids', description: 'Producers submit anonymous bids within 48-72 hours' },
          { order: 4, title: 'Compare Offers', description: 'Review bid amounts and terms (identities remain hidden)' },
          { order: 5, title: 'Select Winner', description: 'Choose the best bid and proceed to payment' },
          { order: 6, title: 'Make Payment', description: 'Pay 30% upfront to escrow account' },
          { order: 7, title: 'Identity Reveal', description: 'Contact details are shared after payment confirmation' }
        ],
        requirements: [
          { name: 'Verified Account', type: 'action', mandatory: true, description: 'Account must be verified to participate in bidding' },
          { name: 'Product Specifications', type: 'information', mandatory: true, description: 'Clear product requirements and specifications' },
          { name: 'Delivery Details', type: 'information', mandatory: true, description: 'Delivery location and timeline' }
        ],
        commonIssues: [
          { problem: 'No bids received', solution: 'Check if specifications are clear and realistic', frequency: 'common' },
          { problem: 'Payment issues', solution: 'Ensure bank details are correct and funds are available', frequency: 'rare' }
        ],
        timeline: {
          totalDuration: '5-10 days',
          milestones: [
            { name: 'Bid submission deadline', duration: '48-72 hours', dependencies: ['Bid request created'] },
            { name: 'Bid selection', duration: '24 hours', dependencies: ['All bids received'] },
            { name: 'Payment processing', duration: '1-2 days', dependencies: ['Bid selected'] },
            { name: 'Production start', duration: 'Immediate', dependencies: ['Payment confirmed'] }
          ]
        }
      },
      orderProcess: {
        steps: [
          { order: 1, title: 'Order Confirmation', description: 'Order details confirmed by both parties' },
          { order: 2, title: 'Production Planning', description: 'Producer schedules production' },
          { order: 3, title: 'Production Start', description: 'Manufacturing begins' },
          { order: 4, title: 'Quality Control', description: 'Products undergo quality inspection' },
          { order: 5, title: 'Packaging', description: 'Products are carefully packaged for shipping' },
          { order: 6, title: 'Shipping', description: 'Products are shipped to destination' },
          { order: 7, title: 'Delivery', description: 'Products delivered to customer' },
          { order: 8, title: 'Final Payment', description: 'Remaining balance released to producer' }
        ],
        requirements: [
          { name: 'Production Schedule', type: 'information', mandatory: true, description: 'Agreed production timeline' },
          { name: 'Quality Standards', type: 'information', mandatory: true, description: 'Specified quality requirements' },
          { name: 'Shipping Terms', type: 'information', mandatory: true, description: 'FOB, CIF, or DDP terms' }
        ],
        commonIssues: [
          { problem: 'Production delays', solution: 'Contact producer for updated timeline', frequency: 'common' },
          { problem: 'Quality issues', solution: 'Report to admin for dispute resolution', frequency: 'rare' }
        ]
      },
      disputeResolution: {
        steps: [
          { order: 1, title: 'Report Issue', description: 'Submit detailed complaint with evidence' },
          { order: 2, title: 'Initial Review', description: 'Admin reviews complaint and evidence' },
          { order: 3, title: 'Contact Parties', description: 'Both parties are contacted for their side' },
          { order: 4, title: 'Investigation', description: 'Thorough investigation of the issue' },
          { order: 5, title: 'Mediation', description: 'Attempt to reach mutual agreement' },
          { order: 6, title: 'Resolution', description: 'Final decision and action taken' }
        ],
        requirements: [
          { name: 'Evidence', type: 'document', mandatory: true, description: 'Photos, documents, or other proof' },
          { name: 'Detailed Description', type: 'information', mandatory: true, description: 'Clear explanation of the issue' },
          { name: 'Timeline', type: 'information', mandatory: true, description: 'When the issue occurred' }
        ],
        commonIssues: [
          { problem: 'Insufficient evidence', solution: 'Provide more detailed documentation', frequency: 'common' },
          { problem: 'Communication breakdown', solution: 'Admin facilitates communication', frequency: 'rare' }
        ]
      }
    };
  }

  /**
   * Initialize FAQ database
   */
  private initializeFAQDatabase(): FAQ[] {
    return [
      // English FAQs
      {
        question: 'How does the anonymous bidding system work?',
        answer: 'Our anonymous bidding system allows customers to request quotes without revealing their identity to producers. Producers can see bid requirements and submit offers, but identities are only revealed after payment confirmation.',
        category: 'bidding',
        language: 'en',
        keywords: ['anonymous', 'bidding', 'system', 'identity', 'quotes']
      },
      {
        question: 'What payment methods do you accept?',
        answer: 'We accept bank transfers, letters of credit (L/C), and credit cards. All payments go through our secure escrow system for buyer protection.',
        category: 'payment',
        language: 'en',
        keywords: ['payment', 'methods', 'bank', 'transfer', 'credit', 'card', 'escrow']
      },
      {
        question: 'What are your delivery terms?',
        answer: 'We offer FOB (Free on Board), CIF (Cost, Insurance, and Freight), and DDP (Delivered Duty Paid) terms. The specific terms depend on your location and order size.',
        category: 'delivery',
        language: 'en',
        keywords: ['delivery', 'terms', 'FOB', 'CIF', 'DDP', 'shipping']
      },
      {
        question: 'How long does verification take?',
        answer: 'Account verification typically takes 1-3 business days. We review all submitted documents and may contact you if additional information is needed.',
        category: 'account',
        language: 'en',
        keywords: ['verification', 'account', 'documents', 'business', 'days']
      },
      
      // Turkish FAQs
      {
        question: 'Anonim teklif sistemi nasıl çalışır?',
        answer: 'Anonim teklif sistemimiz müşterilerin kimliklerini üreticilere açıklamadan fiyat teklifi almalarını sağlar. Üreticiler teklif gereksinimlerini görebilir ve tekliflerini verebilir, ancak kimlikler sadece ödeme onayından sonra açıklanır.',
        category: 'bidding',
        language: 'tr',
        keywords: ['anonim', 'teklif', 'sistem', 'kimlik', 'fiyat']
      },
      {
        question: 'Hangi ödeme yöntemlerini kabul ediyorsunuz?',
        answer: 'Banka havalesi, akreditif (L/C) ve kredi kartı kabul ediyoruz. Tüm ödemeler alıcı koruması için güvenli emanet sistemimizden geçer.',
        category: 'payment',
        language: 'tr',
        keywords: ['ödeme', 'yöntem', 'banka', 'havale', 'kredi', 'kart', 'emanet']
      },
      {
        question: 'Teslimat koşullarınız nelerdir?',
        answer: 'FOB (Gemide Teslim), CIF (Maliyet, Sigorta ve Navlun) ve DDP (Gümrük Vergisi Ödenmiş Teslim) koşulları sunuyoruz. Spesifik koşullar lokasyonunuza ve sipariş büyüklüğünüze bağlıdır.',
        category: 'delivery',
        language: 'tr',
        keywords: ['teslimat', 'koşul', 'FOB', 'CIF', 'DDP', 'nakliye']
      }
    ];
  }
}
