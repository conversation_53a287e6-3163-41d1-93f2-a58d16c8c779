# 🚀 Natural Stone Marketplace - Production Deployment Checklist

## 📋 Pre-Deployment Checklist

### **🔧 Gün 1: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve Konfigürasyon**

#### **Server Hazırlığı**
- [ ] Ubuntu 20.04/22.04 LTS server kurulumu
- [ ] Deploy user olu<PERSON><PERSON><PERSON><PERSON><PERSON> (`adduser deploy`)
- [ ] SSH key-based authentication kurulumu
- [ ] Sudo <PERSON><PERSON><PERSON> ve<PERSON> (`usermod -aG sudo deploy`)
- [ ] Firewall temel konfigürasyonu

#### **Gerekli <PERSON>ların Kurulumu**
```bash
# Node.js 18.x kurulumu
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# MySQL 8.0 kurulumu
sudo apt update
sudo apt install mysql-server

# Nginx kurulumu
sudo apt install nginx

# Redis kurulumu
sudo apt install redis-server

# PM2 kurulumu (process manager)
sudo npm install -g pm2

# Certbot kurulumu (SSL)
sudo apt install certbot python3-certbot-nginx
```

#### **Database Kurulumu**
- [ ] MySQL root şifresi ayarlanması
- [ ] Production database oluşturulması
- [ ] Production user oluşturulması
- [ ] Database güvenlik ayarları (`mysql_secure_installation`)

#### **Environment Configuration**
- [ ] `.env.production` dosyası konfigürasyonu
- [ ] Güvenli şifreler ve secret key'ler oluşturulması
- [ ] API key'lerin production değerleri ile güncellenmesi
- [ ] CORS ayarlarının production domain'i ile güncellenmesi

#### **SSL Sertifikası**
- [ ] Domain DNS ayarlarının server IP'sine yönlendirilmesi
- [ ] Let's Encrypt SSL sertifikası kurulumu
- [ ] SSL sertifikası otomatik yenileme ayarları

---

### **🧪 Gün 2: Test ve Optimizasyon**

#### **Deployment Test**
- [ ] Deployment script'inin test edilmesi
- [ ] Database migration'ların çalıştırılması
- [ ] Backend build ve start test
- [ ] Frontend build ve start test
- [ ] Nginx konfigürasyon test

#### **Güvenlik Testleri**
- [ ] Security hardening script çalıştırılması
- [ ] Firewall kurallarının test edilmesi
- [ ] Fail2Ban konfigürasyon test
- [ ] SSL sertifikası doğrulaması
- [ ] Güvenlik açığı taraması

#### **Performance Testleri**
- [ ] Load testing (Apache Bench veya Artillery)
- [ ] Database performance test
- [ ] API response time ölçümü
- [ ] Frontend loading speed test
- [ ] Memory ve CPU kullanım analizi

#### **Monitoring Setup**
- [ ] Log rotation konfigürasyonu
- [ ] System monitoring script'lerinin kurulumu
- [ ] Performance monitoring setup
- [ ] Error alerting konfigürasyonu
- [ ] Backup script'lerinin test edilmesi

#### **Backup Sistemi**
- [ ] Database backup script test
- [ ] File backup script test
- [ ] Backup restore test
- [ ] Backup retention policy konfigürasyonu

---

### **🌐 Gün 3: Deployment ve Monitoring**

#### **Final Deployment**
- [ ] Production deployment script çalıştırılması
- [ ] Database migration'ların production'da çalıştırılması
- [ ] SSL sertifikası aktifleştirilmesi
- [ ] Domain DNS'inin final konfigürasyonu

#### **Service Configuration**
- [ ] Systemd service'lerinin kurulumu ve aktifleştirilmesi
- [ ] Nginx konfigürasyonunun aktifleştirilmesi
- [ ] Auto-start konfigürasyonları
- [ ] Service health check'lerinin test edilmesi

#### **Final Testing**
- [ ] Tüm API endpoint'lerinin test edilmesi
- [ ] Frontend sayfalarının test edilmesi
- [ ] User registration/login test
- [ ] File upload test
- [ ] Email gönderimi test
- [ ] WhatsApp integration test
- [ ] AI Marketing system test

#### **Monitoring Activation**
- [ ] System monitoring script'lerinin aktifleştirilmesi
- [ ] Log monitoring setup
- [ ] Performance monitoring aktifleştirme
- [ ] Error alerting test
- [ ] Backup cron job'larının aktifleştirilmesi

---

## 🔧 **Deployment Commands**

### **1. Server Hazırlığı**
```bash
# Deploy user oluştur
sudo adduser deploy
sudo usermod -aG sudo deploy
sudo su - deploy

# SSH key setup
mkdir -p ~/.ssh
chmod 700 ~/.ssh
# Public key'i ~/.ssh/authorized_keys dosyasına ekle
chmod 600 ~/.ssh/authorized_keys
```

### **2. Proje Deployment**
```bash
# Proje dosyalarını server'a kopyala
scp -r . deploy@your-server-ip:/var/www/natural-stone-marketplace/

# Server'da deployment script çalıştır
cd /var/www/natural-stone-marketplace
chmod +x scripts/*.sh
./scripts/deploy.sh production
```

### **3. Database Setup**
```bash
# Database migration
./scripts/migrate-production.sh
```

### **4. Security Hardening**
```bash
# Güvenlik ayarları
sudo ./scripts/security-hardening.sh
```

### **5. Monitoring Setup**
```bash
# Monitoring kurulumu
./scripts/setup-monitoring.sh
```

---

## 📊 **Post-Deployment Verification**

### **Health Checks**
```bash
# Backend health check
curl https://yourdomain.com/api/health

# Frontend check
curl https://yourdomain.com

# Database connection test
mysql -u production_user -p natural_stone_marketplace_prod -e "SELECT 1;"

# Service status check
sudo systemctl status natural-stone-marketplace-backend
sudo systemctl status natural-stone-marketplace-frontend
sudo systemctl status nginx
sudo systemctl status mysql
```

### **Performance Verification**
```bash
# Response time test
curl -w "@curl-format.txt" -o /dev/null -s https://yourdomain.com/api/products

# Load test (Apache Bench)
ab -n 1000 -c 10 https://yourdomain.com/

# SSL test
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com
```

---

## 🚨 **Emergency Procedures**

### **Rollback Plan**
```bash
# Service'leri durdur
sudo systemctl stop natural-stone-marketplace-backend
sudo systemctl stop natural-stone-marketplace-frontend

# Backup'tan restore et
cp -r /var/backups/natural-stone-marketplace/BACKUP_DATE/* /var/www/natural-stone-marketplace/

# Database restore
mysql -u root -p natural_stone_marketplace_prod < /var/backups/natural-stone-marketplace/BACKUP_DATE/database_backup.sql

# Service'leri başlat
sudo systemctl start natural-stone-marketplace-backend
sudo systemctl start natural-stone-marketplace-frontend
```

### **Emergency Contacts**
- **System Admin**: <EMAIL>
- **Developer**: <EMAIL>
- **Hosting Provider**: <EMAIL>

---

## 📈 **Monitoring URLs**

- **Main Site**: https://yourdomain.com
- **API Health**: https://yourdomain.com/api/health
- **Admin Panel**: https://yourdomain.com/admin
- **Monitoring Dashboard**: https://yourdomain.com/monitoring/dashboard.html
- **Metrics**: http://server-ip:9090/metrics

---

## 🔐 **Security Checklist**

- [ ] Firewall aktif ve konfigüre edilmiş
- [ ] Fail2Ban aktif
- [ ] SSH key-based authentication
- [ ] SSL sertifikası aktif
- [ ] Database güvenlik ayarları
- [ ] File permissions doğru ayarlanmış
- [ ] Automatic security updates aktif
- [ ] Log monitoring aktif
- [ ] Intrusion detection aktif
- [ ] Regular security audits scheduled

---

## 📝 **Documentation**

- [ ] API documentation güncel
- [ ] Deployment procedures documented
- [ ] Emergency procedures documented
- [ ] Monitoring procedures documented
- [ ] Backup/restore procedures documented

---

## ✅ **Sign-off**

- [ ] **Technical Lead**: _________________ Date: _________
- [ ] **System Admin**: _________________ Date: _________
- [ ] **Project Manager**: ______________ Date: _________
- [ ] **Client Approval**: ______________ Date: _________

---

**🎉 Deployment Completed Successfully!**

**Production URL**: https://yourdomain.com  
**Deployment Date**: _______________  
**Version**: v1.0.0
