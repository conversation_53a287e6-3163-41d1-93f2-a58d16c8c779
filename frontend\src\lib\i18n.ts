import { GetStaticPropsContext } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import nextI18NextConfig from '../../next-i18next.config.js';

// Common namespaces used across the app
export const commonNamespaces = [
  'common',
  'navigation',
  'errors'
];

// Page-specific namespace mappings
export const pageNamespaces = {
  home: [...commonNamespaces],
  products: [...commonNamespaces, 'products'],
  '3d-showroom': [...commonNamespaces, '3d-viewer'],
  news: [...commonNamespaces, 'news'],
  about: [...commonNamespaces, 'about'],
  contact: [...commonNamespaces, 'contact'],
  auth: [...commonNamespaces, 'auth', 'forms'],
  dashboard: [...commonNamespaces, 'dashboard'],
  'customer-dashboard': [...commonNamespaces, 'dashboard', 'products'],
  'producer-dashboard': [...commonNamespaces, 'dashboard', 'products'],
  'admin-dashboard': [...commonNamespaces, 'dashboard', 'products'],
};

/**
 * Get server-side translations for a page
 * @param context - GetStaticPropsContext or GetServerSidePropsContext
 * @param namespaces - Array of namespace strings or page key
 * @returns Promise with translations
 */
export async function getServerSideTranslations(
  context: GetStaticPropsContext,
  namespaces: string[] | keyof typeof pageNamespaces = commonNamespaces
) {
  const locale = context.locale || 'tr';
  
  // If namespaces is a page key, get the corresponding namespaces
  const namespacesToLoad = Array.isArray(namespaces) 
    ? namespaces 
    : pageNamespaces[namespaces] || commonNamespaces;

  try {
    return await serverSideTranslations(
      locale,
      namespacesToLoad,
      nextI18NextConfig
    );
  } catch (error) {
    console.error('Error loading translations:', error);
    // Fallback to common namespaces if there's an error
    return await serverSideTranslations(
      locale,
      commonNamespaces,
      nextI18NextConfig
    );
  }
}

/**
 * Get static props with translations for static pages
 * @param namespaces - Array of namespace strings or page key
 * @returns Function that can be used in getStaticProps
 */
export function getStaticPropsWithTranslations(
  namespaces: string[] | keyof typeof pageNamespaces = commonNamespaces
) {
  return async (context: GetStaticPropsContext) => {
    const translations = await getServerSideTranslations(context, namespaces);
    
    return {
      props: {
        ...translations,
      },
    };
  };
}

/**
 * Language configuration
 */
export const languages = [
  {
    code: 'tr',
    name: 'Turkish',
    nativeName: 'Türkçe',
    flag: '🇹🇷',
    rtl: false,
  },
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false,
  },
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    rtl: true,
  },
  {
    code: 'zh',
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    flag: '🇨🇳',
    rtl: false,
  },
  {
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    flag: '🇷🇺',
    rtl: false,
  },
] as const;

export type LanguageCode = typeof languages[number]['code'];

/**
 * Get language info by code
 */
export function getLanguageInfo(code: string) {
  return languages.find(lang => lang.code === code) || languages[0];
}

/**
 * Check if a language is RTL
 */
export function isRTLLanguage(code: string): boolean {
  const language = getLanguageInfo(code);
  return language.rtl;
}

/**
 * Get default locale
 */
export const defaultLocale = 'tr';

/**
 * Get all supported locales
 */
export const supportedLocales = languages.map(lang => lang.code);

/**
 * Validate if a locale is supported
 */
export function isSupportedLocale(locale: string): boolean {
  return supportedLocales.includes(locale as LanguageCode);
}

/**
 * Get browser language preference
 */
export function getBrowserLanguage(): LanguageCode {
  if (typeof window === 'undefined') return defaultLocale;
  
  const browserLang = navigator.language.split('-')[0];
  return isSupportedLocale(browserLang) ? browserLang as LanguageCode : defaultLocale;
}

/**
 * Get stored language preference
 */
export function getStoredLanguage(): LanguageCode | null {
  if (typeof window === 'undefined') return null;
  
  const stored = localStorage.getItem('preferred-language');
  return stored && isSupportedLocale(stored) ? stored as LanguageCode : null;
}

/**
 * Store language preference
 */
export function storeLanguagePreference(language: LanguageCode): void {
  if (typeof window === 'undefined') return;
  
  localStorage.setItem('preferred-language', language);
}

/**
 * Format number according to locale
 */
export function formatNumber(
  number: number, 
  locale: string = defaultLocale,
  options?: Intl.NumberFormatOptions
): string {
  try {
    return new Intl.NumberFormat(locale, options).format(number);
  } catch (error) {
    console.error('Error formatting number:', error);
    return number.toString();
  }
}

/**
 * Format currency according to locale
 */
export function formatCurrency(
  amount: number,
  currency: string = 'USD',
  locale: string = defaultLocale
): string {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
    }).format(amount);
  } catch (error) {
    console.error('Error formatting currency:', error);
    return `${amount} ${currency}`;
  }
}

/**
 * Format date according to locale
 */
export function formatDate(
  date: Date | string,
  locale: string = defaultLocale,
  options?: Intl.DateTimeFormatOptions
): string {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, options).format(dateObj);
  } catch (error) {
    console.error('Error formatting date:', error);
    return date.toString();
  }
}

/**
 * Get relative time format
 */
export function formatRelativeTime(
  date: Date | string,
  locale: string = defaultLocale
): string {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
    
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
    
    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second');
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    } else if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
    } else if (diffInSeconds < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
    }
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return date.toString();
  }
}
