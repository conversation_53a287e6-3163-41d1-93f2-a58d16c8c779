"use client"

import * as React from "react"

interface Product {
  id: string
  name: string
  category: string
  image: string
  price: {
    min: number
    max: number
    currency: string
    unit: string
  }
}

interface FavoritesContextType {
  favorites: Product[]
  addToFavorites: (product: Product) => void
  removeFromFavorites: (productId: string) => void
  isFavorite: (productId: string) => boolean
  clearFavorites: () => void
}

const FavoritesContext = React.createContext<FavoritesContextType | undefined>(undefined)

export function FavoritesProvider({ children }: { children: React.ReactNode }) {
  const [favorites, setFavorites] = React.useState<Product[]>([])

  // Load favorites from localStorage on mount
  React.useEffect(() => {
    const savedFavorites = localStorage.getItem('favorites')
    if (savedFavorites) {
      try {
        setFavorites(JSON.parse(savedFavorites))
      } catch (error) {
        console.error('Error loading favorites:', error)
      }
    }
  }, [])

  // Save favorites to localStorage whenever favorites change
  React.useEffect(() => {
    localStorage.setItem('favorites', JSON.stringify(favorites))
  }, [favorites])

  const addToFavorites = React.useCallback((product: Product) => {
    setFavorites(prev => {
      if (prev.some(fav => fav.id === product.id)) {
        return prev // Already in favorites
      }
      return [...prev, product]
    })
  }, [])

  const removeFromFavorites = React.useCallback((productId: string) => {
    setFavorites(prev => prev.filter(fav => fav.id !== productId))
  }, [])

  const isFavorite = React.useCallback((productId: string) => {
    return favorites.some(fav => fav.id === productId)
  }, [favorites])

  const clearFavorites = React.useCallback(() => {
    setFavorites([])
  }, [])

  const value = React.useMemo(() => ({
    favorites,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    clearFavorites
  }), [favorites, addToFavorites, removeFromFavorites, isFavorite, clearFavorites])

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  )
}

export function useFavorites() {
  const context = React.useContext(FavoritesContext)
  if (context === undefined) {
    throw new Error('useFavorites must be used within a FavoritesProvider')
  }
  return context
}
