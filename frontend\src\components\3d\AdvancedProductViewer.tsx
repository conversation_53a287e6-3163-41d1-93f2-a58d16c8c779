'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Canvas } from '@react-three/fiber';
import * as THREE from 'three';
import {
  OrbitControls,
  Environment,
  ContactShadows,
  Loader,
  Html,
  useProgress
} from '@react-three/drei';
import {
  DimensionConfiguration,
  SurfaceFinishConfiguration,
  RoomSimulationConfiguration,
  AdvancedProductConfig,
  SurfaceFinishName
} from '../../types/3d';
import { DimensionConfigurator } from './DimensionConfigurator';
import { SurfaceFinishSimulator } from './SurfaceFinishSimulator';
import { RoomSimulator } from './RoomSimulator';
import { PBRMaterialManager } from './PBRMaterialManager';
import { PerformanceOptimizer } from './PerformanceOptimizer';
import { 
  Settings, 
  Maximize2, 
  Download, 
  Share2, 
  Eye,
  EyeOff,
  RotateCw,
  Zap
} from 'lucide-react';

interface AdvancedProductViewerProps {
  productId: string;
  initialConfig?: Partial<AdvancedProductConfig>;
  dimensionConfig: DimensionConfiguration;
  surfaceFinishConfig: SurfaceFinishConfiguration;
  roomConfig: RoomSimulationConfiguration;
  onConfigChange?: (config: AdvancedProductConfig) => void;
  className?: string;
  width?: number;
  height?: number;
}

interface MaterialProperties {
  roughness: number;
  metallic: number;
  normalIntensity: number;
  displacementScale: number;
  emissive: number;
}

export const AdvancedProductViewer: React.FC<AdvancedProductViewerProps> = ({
  productId,
  initialConfig,
  dimensionConfig,
  surfaceFinishConfig,
  roomConfig,
  onConfigChange,
  className = '',
  width = 800,
  height = 600
}) => {
  // State management
  const [config, setConfig] = useState<AdvancedProductConfig>({
    productId,
    dimensions: { width: 60, height: 60, thickness: 2 },
    surfaceFinish: 'ham' as SurfaceFinishName,
    ...initialConfig
  });

  const [materialProperties, setMaterialProperties] = useState<MaterialProperties>({
    roughness: 0.9,
    metallic: 0.0,
    normalIntensity: 1.0,
    displacementScale: 0.1,
    emissive: 0
  });


  const [activePanel, setActivePanel] = useState<'dimensions' | 'surface' | 'room' | null>('dimensions');
  const [viewerSettings, setViewerSettings] = useState({
    showWireframe: false,
    autoRotate: false,
    showEnvironment: true,
    quality: 'high' as 'low' | 'medium' | 'high'
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [roomData, setRoomData] = useState<any>(null);

  // Get current surface finish configuration
  const currentSurfaceFinish = useMemo(() => {
    return surfaceFinishConfig.availableFinishes.find(f => f.name === config.surfaceFinish) ||
           surfaceFinishConfig.availableFinishes[0];
  }, [config.surfaceFinish, surfaceFinishConfig]);

  // Handle configuration changes
  const handleConfigChange = useCallback((newConfig: Partial<AdvancedProductConfig>) => {
    const updatedConfig = { ...config, ...newConfig };
    setConfig(updatedConfig);
    onConfigChange?.(updatedConfig);
  }, [config, onConfigChange]);

  // Handle material property updates
  const handleMaterialUpdate = useCallback((properties: MaterialProperties) => {
    setMaterialProperties(properties);
  }, []);

  // Removed price update functionality

  // Handle room data updates
  const handleRoomUpdate = useCallback((data: any) => {
    setRoomData(data);
  }, []);

  // Removed price calculation

  // Toggle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Export configuration
  const exportConfiguration = () => {
    const exportData = {
      config,
      materialProperties,
      roomData,
      price: currentPrice,
      timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `product-config-${productId}-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Share configuration
  const shareConfiguration = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Ürün Konfigürasyonu',
          text: `${config.dimensions.width}×${config.dimensions.height}×${config.dimensions.thickness} cm ${config.surfaceFinish} yüzey işlemi`,
          url: window.location.href
        });
      } catch (error) {
        console.error('Sharing failed:', error);
      }
    } else {
      // Fallback: copy to clipboard
      const shareText = `Ürün: ${productId}\nEbat: ${config.dimensions.width}×${config.dimensions.height}×${config.dimensions.thickness} cm\nYüzey: ${config.surfaceFinish}\nFiyat: ${currentPrice?.total} ${currentPrice?.currency}`;
      navigator.clipboard.writeText(shareText);
      alert('Konfigürasyon panoya kopyalandı!');
    }
  };

  return (
    <div className={`flex flex-col lg:flex-row gap-6 ${className}`}>
      {/* 3D Viewer */}
      <div className={`relative bg-gray-100 rounded-lg overflow-hidden ${
        isFullscreen ? 'fixed inset-0 z-50' : ''
      }`} style={{ width: isFullscreen ? '100vw' : width, height: isFullscreen ? '100vh' : height }}>
        
        {/* 3D Canvas */}
        <Canvas
          camera={{ position: [5, 5, 5], fov: 50 }}
          shadows
          dpr={[1, 2]}
          gl={{
            antialias: true,
            alpha: false,
            powerPreference: 'high-performance'
          }}
        >
          {/* Performance Optimizer */}
          <PerformanceOptimizer
            enableLOD={true}
            enableAdaptiveQuality={true}
            targetFPS={60}
          >
            {/* Lighting */}
            <ambientLight intensity={0.4} />
            <directionalLight
              position={[10, 10, 5]}
              intensity={1}
              castShadow
              shadow-mapSize-width={2048}
              shadow-mapSize-height={2048}
            />
            <pointLight position={[-10, -10, -10]} intensity={0.3} />

            {/* Environment */}
            {viewerSettings.showEnvironment && (
              <Environment preset="studio" />
            )}

            {/* Product Model with PBR Material */}
            <PBRMaterialManager
              productId={productId}
              surfaceFinish={config.surfaceFinish}
              surfaceFinishConfig={currentSurfaceFinish}
              materialProperties={materialProperties}
            >
              <ProductMesh
                dimensions={config.dimensions}
                wireframe={viewerSettings.showWireframe}
              />
            </PBRMaterialManager>

            {/* Room Environment (if selected) */}
            {config.room && roomData && (
              <RoomEnvironment
                room={config.room}
                pattern={config.pattern}
                grout={config.grout}
                roomData={roomData}
              />
            )}

            {/* Ground */}
            <ContactShadows
              position={[0, -1, 0]}
              opacity={0.4}
              scale={10}
              blur={2}
              far={4}
            />

            {/* Controls */}
            <OrbitControls
              enablePan={true}
              enableZoom={true}
              enableRotate={true}
              autoRotate={viewerSettings.autoRotate}
              autoRotateSpeed={2}
              minDistance={2}
              maxDistance={20}
            />
          </PerformanceOptimizer>
        </Canvas>

        {/* Loading Overlay */}
        <Loader />

        {/* Viewer Controls */}
        <div className="absolute top-4 right-4 z-20 flex flex-col space-y-2">
          <ViewerControlPanel
            settings={viewerSettings}
            onSettingsChange={setViewerSettings}
            onFullscreen={toggleFullscreen}
            onExport={exportConfiguration}
            onShare={shareConfiguration}
            isFullscreen={isFullscreen}
          />
        </div>

        {/* Removed price display */}
      </div>

      {/* Configuration Panels */}
      <div className="flex-1 space-y-4">
        {/* Panel Navigation */}
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActivePanel('dimensions')}
            className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              activePanel === 'dimensions'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Ebat
          </button>
          <button
            onClick={() => setActivePanel('surface')}
            className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              activePanel === 'surface'
                ? 'bg-white text-purple-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Yüzey
          </button>
          <button
            onClick={() => setActivePanel('room')}
            className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              activePanel === 'room'
                ? 'bg-white text-green-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Mekan
          </button>
        </div>

        {/* Active Panel */}
        {activePanel === 'dimensions' && (
          <DimensionConfigurator
            productId={productId}
            configuration={dimensionConfig}
            currentConfig={config}
            onConfigChange={handleConfigChange}
          />
        )}

        {activePanel === 'surface' && (
          <SurfaceFinishSimulator
            productId={productId}
            configuration={surfaceFinishConfig}
            currentConfig={config}
            onConfigChange={handleConfigChange}
            onMaterialUpdate={handleMaterialUpdate}
          />
        )}

        {activePanel === 'room' && (
          <RoomSimulator
            productId={productId}
            configuration={roomConfig}
            currentConfig={config}
            onConfigChange={handleConfigChange}
            onRoomUpdate={handleRoomUpdate}
          />
        )}
      </div>
    </div>
  );
};

// Product Mesh Component
interface ProductMeshProps {
  dimensions: { width: number; height: number; thickness: number };
  wireframe?: boolean;
}

const ProductMesh: React.FC<ProductMeshProps> = ({ dimensions, wireframe = false }) => {
  const geometry = useMemo(() => {
    return new THREE.BoxGeometry(
      dimensions.width / 100, // Convert cm to meters for Three.js
      dimensions.thickness / 100,
      dimensions.height / 100
    );
  }, [dimensions]);

  return (
    <mesh geometry={geometry} castShadow receiveShadow>
      <meshStandardMaterial wireframe={wireframe} />
    </mesh>
  );
};

// Room Environment Component
const RoomEnvironment: React.FC<any> = ({ room, pattern, grout, roomData }) => {
  // This would render the room environment based on the selected room template
  return null; // Placeholder
};

// Viewer Control Panel Component
interface ViewerControlPanelProps {
  settings: any;
  onSettingsChange: (settings: any) => void;
  onFullscreen: () => void;
  onExport: () => void;
  onShare: () => void;
  isFullscreen: boolean;
}

const ViewerControlPanel: React.FC<ViewerControlPanelProps> = ({
  settings,
  onSettingsChange,
  onFullscreen,
  onExport,
  onShare,
  isFullscreen
}) => {
  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-2">
      <div className="flex flex-col space-y-2">
        {/* Wireframe Toggle */}
        <button
          onClick={() => onSettingsChange({ ...settings, showWireframe: !settings.showWireframe })}
          className={`p-2 rounded-lg transition-colors ${
            settings.showWireframe 
              ? 'bg-blue-100 text-blue-600' 
              : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
          }`}
          title="Wireframe"
        >
          <Eye className="w-4 h-4" />
        </button>

        {/* Auto Rotate Toggle */}
        <button
          onClick={() => onSettingsChange({ ...settings, autoRotate: !settings.autoRotate })}
          className={`p-2 rounded-lg transition-colors ${
            settings.autoRotate 
              ? 'bg-green-100 text-green-600' 
              : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
          }`}
          title="Otomatik Döndür"
        >
          <RotateCw className="w-4 h-4" />
        </button>

        {/* Fullscreen Toggle */}
        <button
          onClick={onFullscreen}
          className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors"
          title={isFullscreen ? 'Tam Ekrandan Çık' : 'Tam Ekran'}
        >
          <Maximize2 className="w-4 h-4" />
        </button>

        {/* Export */}
        <button
          onClick={onExport}
          className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors"
          title="Dışa Aktar"
        >
          <Download className="w-4 h-4" />
        </button>

        {/* Share */}
        <button
          onClick={onShare}
          className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors"
          title="Paylaş"
        >
          <Share2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

// Removed price display component
