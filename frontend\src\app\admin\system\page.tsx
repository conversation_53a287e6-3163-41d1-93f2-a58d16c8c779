// Admin Sistem İzleme Sayfası
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'react-hot-toast';
import {
  Activity,
  Server,
  Database,
  Cpu,
  HardDrive,
  Wifi,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Settings,
  Shield,
  FileText,
  Wrench,
  BarChart3,
  Clock,
  Users,
  Zap
} from 'lucide-react';
import { systemService, SystemMetrics, SystemAlert, ServiceStatus } from '@/services/systemService';
import SystemCharts from '@/components/admin/SystemCharts';
import SystemLogs from '@/components/admin/SystemLogs';
import MaintenanceTools from '@/components/admin/MaintenanceTools';
import { useSystemMetrics } from '@/hooks/useSystemMetrics';

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  responseTime: number;
  errorRate: number;
  activeUsers: number;
  databaseStatus: 'connected' | 'disconnected' | 'slow';
  redisStatus: 'connected' | 'disconnected' | 'slow';
}

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    temperature: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    inbound: number;
    outbound: number;
  };
}

interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  resolved: boolean;
}

export default function AdminSystemPage() {
  const {
    metrics: systemMetrics,
    alerts,
    services,
    isLoading,
    lastUpdated,
    systemStatus,
    needsAttention,
    activeAlertsCount,
    refresh,
    clearAlerts: handleClearAlerts,
    clearCache: handleClearCache,
    optimizeDatabase: handleOptimizeDatabase
  } = useSystemMetrics({
    refreshInterval: 0, // Otomatik yenileme devre dışı
    autoRefresh: false, // Manuel yenileme
    onError: (error) => {
      console.error('System metrics error:', error);
      toast.error('Sistem verileri yüklenirken hata oluştu');
    }
  });

  // Mock system health data when Redis is not available
  const systemHealth: SystemHealth = {
    status: 'healthy',
    uptime: 1296000, // 15 days in seconds
    responseTime: 125,
    errorRate: 0.2,
    activeUsers: 1245,
    databaseStatus: 'connected',
    redisStatus: 'disconnected' // Redis is not available
  };

  const refreshData = async () => {
    try {
      await refresh();
      toast.success('Sistem verileri güncellendi');
    } catch (error) {
      toast.error('Veriler güncellenirken hata oluştu');
    }
  };

  const handleClearCacheWithToast = async () => {
    try {
      await handleClearCache();
      toast.success('Cache başarıyla temizlendi');
    } catch (error) {
      toast.error('Cache temizlenirken hata oluştu');
    }
  };

  const handleOptimizeDatabaseWithToast = async () => {
    try {
      await handleOptimizeDatabase();
      toast.success('Veritabanı optimizasyonu tamamlandı');
    } catch (error) {
      toast.error('Veritabanı optimizasyonu başarısız');
    }
  };

  const handleClearAlertsWithToast = async () => {
    try {
      await handleClearAlerts();
      toast.success('Uyarılar temizlendi');
    } catch (error) {
      toast.error('Uyarılar temizlenirken hata oluştu');
    }
  };

  if (!systemMetrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p>Sistem verileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
      case 'running':
        return 'text-green-600';
      case 'warning':
      case 'slow':
        return 'text-yellow-600';
      case 'critical':
      case 'disconnected':
      case 'stopped':
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
      case 'running':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'warning':
      case 'slow':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'critical':
      case 'disconnected':
      case 'stopped':
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600" />;
      default:
        return <Activity className="w-5 h-5 text-gray-600" />;
    }
  };

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    return `${days}d ${hours}h`;
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Sistem İzleme</h1>
          <p className="text-gray-600 mt-1">
            Platform sistem sağlığı ve performans metrikleri
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge
            variant={systemStatus === 'healthy' ? 'default' : 'destructive'}
            className="text-sm"
          >
            {getStatusIcon(systemStatus)}
            <span className="ml-2">
              {systemStatus === 'healthy' ? 'Sistem Sağlıklı' :
               systemStatus === 'warning' ? 'Uyarı' : 'Kritik'}
            </span>
          </Badge>
          {lastUpdated && (
            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
              Son güncelleme: {lastUpdated.toLocaleTimeString('tr-TR')}
            </span>
          )}
          {needsAttention && (
            <Badge variant="destructive" className="text-xs">
              <AlertTriangle className="w-3 h-3 mr-1" />
              Dikkat Gerekli
            </Badge>
          )}
          <Button
            variant="outline"
            onClick={refresh}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
        </div>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Sistem Durumu</p>
                <p className={`text-2xl font-bold ${getStatusColor(systemStatus)}`}>
                  {systemStatus === 'healthy' ? 'Sağlıklı' :
                   systemStatus === 'warning' ? 'Uyarı' : 'Kritik'}
                </p>
              </div>
              <Activity className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">CPU Kullanımı</p>
                <p className={`text-2xl font-bold ${systemService.getStatusColor(systemMetrics.cpu.usage, 70, 85)}`}>
                  {systemMetrics.cpu.usage}%
                </p>
              </div>
              <Clock className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Bellek Kullanımı</p>
                <p className={`text-2xl font-bold ${systemService.getStatusColor(systemMetrics.memory.percentage, 75, 90)}`}>
                  {systemMetrics.memory.percentage}%
                </p>
              </div>
              <Zap className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Disk Kullanımı</p>
                <p className={`text-2xl font-bold ${systemService.getStatusColor(systemMetrics.disk.percentage, 80, 90)}`}>
                  {systemMetrics.disk.percentage}%
                </p>
              </div>
              <Users className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
          <TabsTrigger value="performance">Performans</TabsTrigger>
          <TabsTrigger value="services">Servisler</TabsTrigger>
          <TabsTrigger value="logs">Loglar</TabsTrigger>
          <TabsTrigger value="alerts">
            Uyarılar
            {activeAlertsCount > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {activeAlertsCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="maintenance">Bakım</TabsTrigger>
          <TabsTrigger value="security">Güvenlik</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Real-time Charts */}
          <SystemCharts metrics={systemMetrics} />

          {/* System Metrics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* CPU & Memory */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Cpu className="w-5 h-5" />
                  CPU ve Bellek Kullanımı
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>CPU Kullanımı</span>
                    <span>{systemMetrics.cpu.usage}%</span>
                  </div>
                  <Progress value={systemMetrics.cpu.usage} className="h-2" />
                  <p className="text-xs text-gray-500 mt-1">
                    {systemMetrics.cpu.cores} çekirdek • {systemMetrics.cpu.temperature}°C
                  </p>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Bellek Kullanımı</span>
                    <span>{systemMetrics.memory.percentage}%</span>
                  </div>
                  <Progress value={systemMetrics.memory.percentage} className="h-2" />
                  <p className="text-xs text-gray-500 mt-1">
                    {systemMetrics.memory.used}GB / {systemMetrics.memory.total}GB
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Disk & Network */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <HardDrive className="w-5 h-5" />
                  Disk ve Ağ Kullanımı
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Disk Kullanımı</span>
                    <span>{systemMetrics.disk.percentage}%</span>
                  </div>
                  <Progress value={systemMetrics.disk.percentage} className="h-2" />
                  <p className="text-xs text-gray-500 mt-1">
                    {systemMetrics.disk.used}GB / {systemMetrics.disk.total}GB
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium">Gelen Trafik</p>
                    <p className="text-lg font-bold text-green-600">
                      {systemMetrics.network.inbound} MB/s
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Giden Trafik</p>
                    <p className="text-lg font-bold text-blue-600">
                      {systemMetrics.network.outbound} MB/s
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Database & Redis Status */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Veritabanı Durumu
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">PostgreSQL</p>
                    <p className={`font-semibold ${getStatusColor(systemHealth.databaseStatus)}`}>
                      {systemHealth.databaseStatus === 'connected' ? 'Bağlı' :
                       systemHealth.databaseStatus === 'slow' ? 'Yavaş' : 'Bağlantı Kesildi'}
                    </p>
                  </div>
                  {getStatusIcon(systemHealth.databaseStatus)}
                </div>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Aktif Bağlantı</span>
                    <span>25/100</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Ortalama Sorgu Süresi</span>
                    <span>45ms</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="w-5 h-5" />
                  Redis Cache Durumu
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Redis</p>
                    <p className={`font-semibold ${getStatusColor(systemHealth.redisStatus)}`}>
                      {systemHealth.redisStatus === 'connected' ? 'Bağlı' :
                       systemHealth.redisStatus === 'slow' ? 'Yavaş' : 'Bağlantı Kesildi'}
                    </p>
                  </div>
                  {getStatusIcon(systemHealth.redisStatus)}
                </div>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Cache Hit Rate</span>
                    <span>94.5%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Bellek Kullanımı</span>
                    <span>2.1GB / 4GB</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>API Performansı</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Ortalama Yanıt Süresi</span>
                    <span className="font-semibold">{systemHealth.responseTime}ms</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Hata Oranı</span>
                    <span className="font-semibold text-green-600">{systemHealth.errorRate}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">İstek/Saniye</span>
                    <span className="font-semibold">1,245</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>En Yavaş Endpoint'ler</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">/api/products/search</span>
                    <span className="text-sm font-semibold text-red-600">850ms</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">/api/admin/analytics</span>
                    <span className="text-sm font-semibold text-yellow-600">420ms</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">/api/quotes/create</span>
                    <span className="text-sm font-semibold text-green-600">180ms</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Services Tab */}
        <TabsContent value="services" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="w-5 h-5" />
                  Web Server
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between mb-4">
                  <span>Nginx</span>
                  {getStatusIcon('healthy')}
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Versiyon</span>
                    <span>1.25.0</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Uptime</span>
                    <span>15d 8h</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Veritabanı
                </CardTitle>
              </CardHeader>
              <CardContent>
                {services.find(s => s.name === 'postgresql') ? (
                  <>
                    <div className="flex items-center justify-between mb-4">
                      <span>PostgreSQL</span>
                      {getStatusIcon(services.find(s => s.name === 'postgresql')?.status || 'unknown')}
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Versiyon</span>
                        <span>{services.find(s => s.name === 'postgresql')?.version}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Uptime</span>
                        <span>{formatUptime(services.find(s => s.name === 'postgresql')?.uptime || 0)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Bellek</span>
                        <span>{services.find(s => s.name === 'postgresql')?.memory?.toFixed(1)}MB</span>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500">Servis bilgisi yükleniyor...</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wifi className="w-5 h-5" />
                  Cache
                </CardTitle>
              </CardHeader>
              <CardContent>
                {services.find(s => s.name === 'redis') ? (
                  <>
                    <div className="flex items-center justify-between mb-4">
                      <span>Redis</span>
                      {getStatusIcon(services.find(s => s.name === 'redis')?.status || 'unknown')}
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Versiyon</span>
                        <span>{services.find(s => s.name === 'redis')?.version}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Uptime</span>
                        <span>{formatUptime(services.find(s => s.name === 'redis')?.uptime || 0)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Bellek</span>
                        <span>{services.find(s => s.name === 'redis')?.memory?.toFixed(1)}MB</span>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500">Servis bilgisi yükleniyor...</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Logs Tab */}
        <TabsContent value="logs" className="space-y-6">
          <SystemLogs />
        </TabsContent>

        {/* Alerts Tab */}
        <TabsContent value="alerts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5" />
                  Sistem Uyarıları
                </div>
                {alerts.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearAlertsWithToast}
                  >
                    Tümünü Temizle
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
                    <p className="text-gray-600">Aktif uyarı bulunmuyor</p>
                  </div>
                ) : (
                  alerts.map((alert) => (
                    <Alert key={alert.id} className={
                      alert.type === 'error' ? 'border-red-200 bg-red-50' :
                      alert.type === 'warning' ? 'border-yellow-200 bg-yellow-50' :
                      'border-blue-200 bg-blue-50'
                    }>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-semibold">{alert.title}</p>
                            <p className="text-sm text-gray-600">{alert.message}</p>
                            <p className="text-xs text-gray-500 mt-1">
                              {new Date(alert.timestamp).toLocaleString('tr-TR')}
                            </p>
                          </div>
                          <Badge variant={alert.resolved ? 'default' : 'destructive'}>
                            {alert.resolved ? 'Çözüldü' : 'Aktif'}
                          </Badge>
                        </div>
                      </AlertDescription>
                    </Alert>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Maintenance Tab */}
        <TabsContent value="maintenance" className="space-y-6">
          <MaintenanceTools />
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Güvenlik Durumu
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">SSL Sertifikası</span>
                    <Badge variant="default">Geçerli</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Firewall</span>
                    <Badge variant="default">Aktif</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">DDoS Koruması</span>
                    <Badge variant="default">Aktif</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Son Güvenlik Taraması</span>
                    <span className="text-sm text-gray-600">2 saat önce</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Güvenlik Olayları</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Başarısız Giriş Denemesi</span>
                    <span className="text-xs text-red-600">3 adet</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Şüpheli IP Engellendi</span>
                    <span className="text-xs text-yellow-600">1 adet</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">API Rate Limit Aşımı</span>
                    <span className="text-xs text-blue-600">12 adet</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
