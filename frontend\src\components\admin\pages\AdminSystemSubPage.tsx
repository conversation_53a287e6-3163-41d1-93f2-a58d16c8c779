'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Server,
  Database,
  Cloud,
  FileText,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCcw
} from 'lucide-react';

interface AdminSystemSubPageProps {
  type: 'server' | 'database' | 'api' | 'logs';
  title: string;
  description: string;
}

const AdminSystemSubPage: React.FC<AdminSystemSubPageProps> = ({
  type,
  title,
  description
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [systemData, setSystemData] = useState<any>(null);

  useEffect(() => {
    loadSystemData();
  }, [type]);

  const loadSystemData = async () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setSystemData(getMockData(type));
      setIsLoading(false);
    }, 1000);
  };

  const getMockData = (dataType: string) => {
    switch (dataType) {
      case 'server':
        return {
          status: 'healthy',
          uptime: '15 gün 8 saat',
          cpu: 45,
          memory: 68,
          disk: 32,
          network: 'Stabil'
        };
      case 'database':
        return {
          status: 'healthy',
          connections: 24,
          queries: 1250,
          avgResponseTime: '12ms',
          storage: 78
        };
      case 'api':
        return {
          status: 'healthy',
          totalRequests: 15420,
          avgResponseTime: '245ms',
          errorRate: 0.2,
          endpoints: [
            { name: '/api/products', requests: 5420, avgTime: '180ms', status: 'healthy' },
            { name: '/api/quotes', requests: 3210, avgTime: '320ms', status: 'healthy' },
            { name: '/api/users', requests: 2890, avgTime: '150ms', status: 'healthy' }
          ]
        };
      case 'logs':
        return {
          totalLogs: 45230,
          errors: 12,
          warnings: 45,
          info: 44173,
          recentLogs: [
            { level: 'info', message: 'User login successful', timestamp: new Date() },
            { level: 'warning', message: 'High memory usage detected', timestamp: new Date() },
            { level: 'error', message: 'Database connection timeout', timestamp: new Date() }
          ]
        };
      default:
        return {};
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <CheckCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getTypeIcon = () => {
    switch (type) {
      case 'server':
        return <Server className="h-6 w-6 text-blue-600" />;
      case 'database':
        return <Database className="h-6 w-6 text-green-600" />;
      case 'api':
        return <Cloud className="h-6 w-6 text-purple-600" />;
      case 'logs':
        return <FileText className="h-6 w-6 text-orange-600" />;
      default:
        return <Server className="h-6 w-6 text-gray-600" />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Sistem verileri yükleniyor...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getTypeIcon()}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
            <p className="text-gray-600 mt-1">{description}</p>
          </div>
        </div>
        <button
          onClick={loadSystemData}
          className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
        >
          <RefreshCcw className="h-4 w-4" />
          <span>Yenile</span>
        </button>
      </div>

      {/* Status Overview */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon(systemData?.status || 'healthy')}
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Sistem Durumu
              </h3>
              <p className="text-sm text-gray-600">
                {systemData?.status === 'healthy' ? 'Tüm sistemler normal çalışıyor' : 'Sistem sorunları tespit edildi'}
              </p>
            </div>
          </div>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            systemData?.status === 'healthy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {systemData?.status === 'healthy' ? 'Sağlıklı' : 'Sorunlu'}
          </div>
        </div>
      </div>

      {/* Type-specific Content */}
      {type === 'server' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h4 className="text-sm font-medium text-gray-500 mb-2">CPU Kullanımı</h4>
            <p className="text-2xl font-bold text-gray-900">{systemData.cpu}%</p>
          </div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Bellek Kullanımı</h4>
            <p className="text-2xl font-bold text-gray-900">{systemData.memory}%</p>
          </div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Disk Kullanımı</h4>
            <p className="text-2xl font-bold text-gray-900">{systemData.disk}%</p>
          </div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Çalışma Süresi</h4>
            <p className="text-lg font-bold text-gray-900">{systemData.uptime}</p>
          </div>
        </div>
      )}

      {type === 'database' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Aktif Bağlantılar</h4>
            <p className="text-2xl font-bold text-gray-900">{systemData.connections}</p>
          </div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Sorgu Sayısı</h4>
            <p className="text-2xl font-bold text-gray-900">{systemData.queries}</p>
          </div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Ortalama Yanıt</h4>
            <p className="text-2xl font-bold text-gray-900">{systemData.avgResponseTime}</p>
          </div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Depolama</h4>
            <p className="text-2xl font-bold text-gray-900">{systemData.storage}%</p>
          </div>
        </div>
      )}

      {type === 'api' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Toplam İstek</h4>
              <p className="text-2xl font-bold text-gray-900">{systemData.totalRequests}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Ortalama Yanıt</h4>
              <p className="text-2xl font-bold text-gray-900">{systemData.avgResponseTime}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Hata Oranı</h4>
              <p className="text-2xl font-bold text-gray-900">{systemData.errorRate}%</p>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">API Endpoint'leri</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Endpoint</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">İstekler</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Ortalama Süre</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Durum</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {systemData.endpoints?.map((endpoint: any, index: number) => (
                    <tr key={index}>
                      <td className="px-6 py-4 text-sm font-medium text-gray-900">{endpoint.name}</td>
                      <td className="px-6 py-4 text-sm text-gray-500">{endpoint.requests}</td>
                      <td className="px-6 py-4 text-sm text-gray-500">{endpoint.avgTime}</td>
                      <td className="px-6 py-4">
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                          Sağlıklı
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {type === 'logs' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Toplam Log</h4>
              <p className="text-2xl font-bold text-gray-900">{systemData.totalLogs}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Hatalar</h4>
              <p className="text-2xl font-bold text-red-600">{systemData.errors}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Uyarılar</h4>
              <p className="text-2xl font-bold text-yellow-600">{systemData.warnings}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Bilgi</h4>
              <p className="text-2xl font-bold text-blue-600">{systemData.info}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminSystemSubPage;
