'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Package, 
  CreditCard, 
  ArrowRight,
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Upload,
  Download,
  Eye,
  Building,
  User,
  DollarSign,
  TrendingUp,
  Minus
} from 'lucide-react';
import toast from 'react-hot-toast';

interface PaymentFlow {
  customerPayment: {
    id: string;
    amount: number;
    currency: string;
    status: 'pending' | 'receipt_uploaded' | 'verified' | 'completed';
    receivedAt?: string;
    receiptUrl?: string;
  };
  platformCommission: {
    amount: number;
    rate: number;
    status: 'pending' | 'calculated' | 'deducted';
  };
  producerPayment: {
    id: string;
    amount: number;
    currency: string;
    status: 'pending' | 'ready' | 'sent' | 'completed';
    sentAt?: string;
    bankDetails?: {
      iban: string;
      accountHolder: string;
    };
  };
}

interface AdminOrderDetail {
  id: string;
  orderNumber: string;
  productName: string;
  totalQuantity: number;
  unit: string;
  totalAmount: number;
  currency: string;
  status: string;
  createdAt: string;
  customer: {
    id: string;
    name: string;
    email: string;
    company: string;
  };
  producer: {
    id: string;
    name: string;
    email: string;
    company: string;
    bankDetails: {
      iban: string;
      accountHolder: string;
    };
  };
  deliverySchedules: Array<{
    id: string;
    deliveryNumber: number;
    quantity: number;
    unit: string;
    scheduledDate: string;
    status: string;
    paymentFlow: PaymentFlow;
  }>;
  financialSummary: {
    totalCustomerPayments: number;
    totalCommissions: number;
    totalProducerPayments: number;
    pendingPayments: number;
  };
}

export default function AdminOrderDetailPage() {
  const params = useParams();
  const orderId = params.orderId as string;
  const [order, setOrder] = useState<AdminOrderDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchOrderDetail();
  }, [orderId]);

  const fetchOrderDetail = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockOrder: AdminOrderDetail = {
        id: orderId,
        orderNumber: `ORD-${orderId.slice(-6).toUpperCase()}`,
        productName: 'Beyaz Mermer - Afyon',
        totalQuantity: 500,
        unit: 'm²',
        totalAmount: 75000,
        currency: 'TRY',
        status: 'in_production',
        createdAt: '2024-01-15T10:00:00Z',
        customer: {
          id: 'cust_001',
          name: 'Ahmet Yılmaz',
          email: '<EMAIL>',
          company: 'Marble Export Ltd.'
        },
        producer: {
          id: 'prod_001',
          name: 'Mehmet Kaya',
          email: '<EMAIL>',
          company: 'Afyon Mermer A.Ş.',
          bankDetails: {
            iban: 'TR64 0001 2009 4520 0058 0000 01',
            accountHolder: 'Afyon Mermer A.Ş.'
          }
        },
        deliverySchedules: [
          {
            id: 'del_001',
            deliveryNumber: 1,
            quantity: 150,
            unit: 'm²',
            scheduledDate: '2024-02-01',
            status: 'delivered',
            paymentFlow: {
              customerPayment: {
                id: 'cpay_001',
                amount: 22500,
                currency: 'TRY',
                status: 'completed',
                receivedAt: '2024-01-20T14:30:00Z',
                receiptUrl: '/receipts/receipt_001.pdf'
              },
              platformCommission: {
                amount: 1125,
                rate: 5,
                status: 'deducted'
              },
              producerPayment: {
                id: 'ppay_001',
                amount: 21375,
                currency: 'TRY',
                status: 'completed',
                sentAt: '2024-02-02T10:15:00Z',
                bankDetails: {
                  iban: 'TR64 0001 2009 4520 0058 0000 01',
                  accountHolder: 'Afyon Mermer A.Ş.'
                }
              }
            }
          },
          {
            id: 'del_002',
            deliveryNumber: 2,
            quantity: 200,
            unit: 'm²',
            scheduledDate: '2024-02-15',
            status: 'ready',
            paymentFlow: {
              customerPayment: {
                id: 'cpay_002',
                amount: 30000,
                currency: 'TRY',
                status: 'verified',
                receivedAt: '2024-02-10T09:20:00Z',
                receiptUrl: '/receipts/receipt_002.pdf'
              },
              platformCommission: {
                amount: 1500,
                rate: 5,
                status: 'calculated'
              },
              producerPayment: {
                id: 'ppay_002',
                amount: 28500,
                currency: 'TRY',
                status: 'ready',
                bankDetails: {
                  iban: 'TR64 0001 2009 4520 0058 0000 01',
                  accountHolder: 'Afyon Mermer A.Ş.'
                }
              }
            }
          },
          {
            id: 'del_003',
            deliveryNumber: 3,
            quantity: 150,
            unit: 'm²',
            scheduledDate: '2024-03-01',
            status: 'pending',
            paymentFlow: {
              customerPayment: {
                id: 'cpay_003',
                amount: 22500,
                currency: 'TRY',
                status: 'pending'
              },
              platformCommission: {
                amount: 1125,
                rate: 5,
                status: 'pending'
              },
              producerPayment: {
                id: 'ppay_003',
                amount: 21375,
                currency: 'TRY',
                status: 'pending'
              }
            }
          }
        ],
        financialSummary: {
          totalCustomerPayments: 52500,
          totalCommissions: 2625,
          totalProducerPayments: 49875,
          pendingPayments: 22500
        }
      };
      setOrder(mockOrder);
    } catch (error) {
      console.error('Error fetching order:', error);
      toast.error('Sipariş detayları yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyPayment = async (paymentId: string, approved: boolean, reason?: string) => {
    try {
      const response = await fetch(`/api/payments/admin/${paymentId}/verify`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ approved, reason })
      });

      if (response.ok) {
        toast.success(approved ? 'Ödeme onaylandı' : 'Ödeme reddedildi');
        await fetchOrderDetail();
      } else {
        throw new Error('Verification failed');
      }
    } catch (error) {
      console.error('Error verifying payment:', error);
      toast.error('Ödeme işlemi sırasında hata oluştu');
    }
  };

  const handleSendProducerPayment = async (paymentId: string) => {
    try {
      const response = await fetch(`/api/payments/admin/${paymentId}/send-to-producer`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        toast.success('Ödeme üreticiye gönderildi');
        await fetchOrderDetail();
      } else {
        throw new Error('Payment send failed');
      }
    } catch (error) {
      console.error('Error sending payment:', error);
      toast.error('Ödeme gönderilirken hata oluştu');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Bekliyor', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      receipt_uploaded: { label: 'Dekont Yüklendi', color: 'bg-blue-100 text-blue-800', icon: Upload },
      verified: { label: 'Onaylandı', color: 'bg-green-100 text-green-800', icon: CheckCircle },
      completed: { label: 'Tamamlandı', color: 'bg-green-100 text-green-800', icon: CheckCircle },
      ready: { label: 'Hazır', color: 'bg-blue-100 text-blue-800', icon: Package },
      sent: { label: 'Gönderildi', color: 'bg-purple-100 text-purple-800', icon: ArrowRight },
      calculated: { label: 'Hesaplandı', color: 'bg-orange-100 text-orange-800', icon: TrendingUp },
      deducted: { label: 'Kesildi', color: 'bg-green-100 text-green-800', icon: Minus }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Sipariş detayları yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Sipariş bulunamadı</h3>
        <p className="text-gray-600">Bu sipariş mevcut değil.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin - Sipariş Detayı</h1>
          <p className="text-gray-600 mt-1">{order.orderNumber}</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-900">
            {formatCurrency(order.totalAmount, order.currency)}
          </div>
          {getStatusBadge(order.status)}
        </div>
      </div>

      {/* Financial Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Finansal Özet
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-600 font-medium">Müşteri Ödemeleri</p>
              <p className="text-2xl font-bold text-blue-900">
                {formatCurrency(order.financialSummary.totalCustomerPayments, order.currency)}
              </p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600 font-medium">İşlem Bedeli</p>
              <p className="text-2xl font-bold text-gray-900">
                Dahil
              </p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <p className="text-sm text-purple-600 font-medium">Üretici Ödemeleri</p>
              <p className="text-2xl font-bold text-purple-900">
                {formatCurrency(order.financialSummary.totalProducerPayments, order.currency)}
              </p>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <p className="text-sm text-yellow-600 font-medium">Bekleyen Ödemeler</p>
              <p className="text-2xl font-bold text-yellow-900">
                {formatCurrency(order.financialSummary.pendingPayments, order.currency)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Participants */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Müşteri Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><span className="font-medium">Ad:</span> {order.customer.name}</p>
              <p><span className="font-medium">Şirket:</span> {order.customer.company}</p>
              <p><span className="font-medium">E-posta:</span> {order.customer.email}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="w-5 h-5" />
              Üretici Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><span className="font-medium">Ad:</span> {order.producer.name}</p>
              <p><span className="font-medium">Şirket:</span> {order.producer.company}</p>
              <p><span className="font-medium">E-posta:</span> {order.producer.email}</p>
              <p><span className="font-medium">IBAN:</span> {order.producer.bankDetails.iban}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Flow Details */}
      <Tabs defaultValue="payment-flows" className="space-y-4">
        <TabsList>
          <TabsTrigger value="payment-flows">Ödeme Akışları</TabsTrigger>
          <TabsTrigger value="customer-payments">Müşteri Ödemeleri</TabsTrigger>
          <TabsTrigger value="producer-payments">Üretici Ödemeleri</TabsTrigger>
        </TabsList>

        <TabsContent value="payment-flows" className="space-y-4">
          {order.deliverySchedules.map((delivery) => (
            <Card key={delivery.id}>
              <CardHeader>
                <CardTitle className="text-lg">
                  {delivery.deliveryNumber}. Teslimat - Ödeme Akışı
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  {/* Customer Payment */}
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                      <User className="w-8 h-8 text-blue-600" />
                    </div>
                    <p className="text-sm font-medium">Müşteri</p>
                    <p className="text-lg font-bold text-blue-600">
                      {formatCurrency(delivery.paymentFlow.customerPayment.amount, delivery.paymentFlow.customerPayment.currency)}
                    </p>
                    {getStatusBadge(delivery.paymentFlow.customerPayment.status)}
                  </div>

                  <ArrowRight className="w-6 h-6 text-gray-400" />

                  {/* Platform Commission - Hidden from view */}
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                      <ArrowRight className="w-8 h-8 text-gray-600" />
                    </div>
                    <p className="text-sm font-medium">İşlem</p>
                    <p className="text-lg font-bold text-gray-600">
                      Hizmet Bedeli
                    </p>
                    <Badge className="bg-gray-100 text-gray-600">İşleniyor</Badge>
                  </div>

                  <ArrowRight className="w-6 h-6 text-gray-400" />

                  {/* Producer Payment */}
                  <div className="text-center">
                    <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                      <Building className="w-8 h-8 text-purple-600" />
                    </div>
                    <p className="text-sm font-medium">Üretici</p>
                    <p className="text-lg font-bold text-purple-600">
                      {formatCurrency(delivery.paymentFlow.producerPayment.amount, delivery.paymentFlow.producerPayment.currency)}
                    </p>
                    {getStatusBadge(delivery.paymentFlow.producerPayment.status)}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 mt-4">
                  {delivery.paymentFlow.customerPayment.status === 'receipt_uploaded' && (
                    <>
                      <Button 
                        onClick={() => handleVerifyPayment(delivery.paymentFlow.customerPayment.id, true)}
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Ödemeyi Onayla
                      </Button>
                      <Button 
                        onClick={() => handleVerifyPayment(delivery.paymentFlow.customerPayment.id, false, 'Dekont geçersiz')}
                        size="sm"
                        variant="destructive"
                      >
                        <AlertTriangle className="w-4 h-4 mr-2" />
                        Reddet
                      </Button>
                    </>
                  )}
                  
                  {delivery.paymentFlow.producerPayment.status === 'ready' && (
                    <Button 
                      onClick={() => handleSendProducerPayment(delivery.paymentFlow.producerPayment.id)}
                      size="sm"
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      <ArrowRight className="w-4 h-4 mr-2" />
                      Üreticiye Gönder
                    </Button>
                  )}
                  
                  {delivery.paymentFlow.customerPayment.receiptUrl && (
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-2" />
                      Dekontu Görüntüle
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="customer-payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Müşteri Ödemeleri</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.deliverySchedules.map((delivery) => (
                  <div key={delivery.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">
                        {delivery.deliveryNumber}. Teslimat Ödemesi
                      </h4>
                      {getStatusBadge(delivery.paymentFlow.customerPayment.status)}
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Tutar:</span>
                        <span className="ml-2">
                          {formatCurrency(delivery.paymentFlow.customerPayment.amount, delivery.paymentFlow.customerPayment.currency)}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium">Alındı:</span>
                        <span className="ml-2">
                          {delivery.paymentFlow.customerPayment.receivedAt 
                            ? formatDate(delivery.paymentFlow.customerPayment.receivedAt)
                            : '-'
                          }
                        </span>
                      </div>
                      <div>
                        <span className="font-medium">Dekont:</span>
                        <span className="ml-2">
                          {delivery.paymentFlow.customerPayment.receiptUrl ? 'Mevcut' : 'Yok'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="producer-payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Üretici Ödemeleri</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.deliverySchedules.map((delivery) => (
                  <div key={delivery.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">
                        {delivery.deliveryNumber}. Teslimat Ödemesi
                      </h4>
                      {getStatusBadge(delivery.paymentFlow.producerPayment.status)}
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Net Tutar:</span>
                        <span className="ml-2">
                          {formatCurrency(delivery.paymentFlow.producerPayment.amount, delivery.paymentFlow.producerPayment.currency)}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium">Gönderildi:</span>
                        <span className="ml-2">
                          {delivery.paymentFlow.producerPayment.sentAt 
                            ? formatDate(delivery.paymentFlow.producerPayment.sentAt)
                            : '-'
                          }
                        </span>
                      </div>
                      <div>
                        <span className="font-medium">IBAN:</span>
                        <span className="ml-2 font-mono text-xs">
                          {delivery.paymentFlow.producerPayment.bankDetails?.iban || '-'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
