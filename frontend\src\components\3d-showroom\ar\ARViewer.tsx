'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { 
  Smartphone, 
  Camera, 
  Download, 
  Share2, 
  X, 
  RotateCw,
  Move,
  ZoomIn,
  ZoomOut,
  Info,
  AlertTriangle
} from 'lucide-react';
import { ProductPlacement } from '../core/ShowroomEngine';

interface ARViewerProps {
  products: ProductPlacement[];
  onClose: () => void;
  className?: string;
}

interface ARSession {
  isActive: boolean;
  isSupported: boolean;
  error: string | null;
}

export const ARViewer: React.FC<ARViewerProps> = ({
  products,
  onClose,
  className = ''
}) => {
  const [arSession, setArSession] = useState<ARSession>({
    isActive: false,
    isSupported: false,
    error: null
  });
  const [isLoading, setIsLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [arMode, setArMode] = useState<'placement' | 'viewing'>('placement');
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Check AR support on component mount
  useEffect(() => {
    checkARSupport();
  }, []);

  const checkARSupport = useCallback(async () => {
    try {
      // Check for WebXR support
      if ('xr' in navigator) {
        const isSupported = await (navigator as any).xr.isSessionSupported('immersive-ar');
        setArSession(prev => ({ ...prev, isSupported }));
      } else {
        // Fallback to camera access for basic AR simulation
        const hasCamera = await checkCameraAccess();
        setArSession(prev => ({ 
          ...prev, 
          isSupported: hasCamera,
          error: hasCamera ? null : 'Kamera erişimi gerekli'
        }));
      }
    } catch (error) {
      setArSession(prev => ({ 
        ...prev, 
        isSupported: false, 
        error: 'AR desteği bulunamadı' 
      }));
    }
  }, []);

  const checkCameraAccess = useCallback(async (): Promise<boolean> => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } 
      });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      return false;
    }
  }, []);

  const startARSession = useCallback(async () => {
    setIsLoading(true);
    
    try {
      if ('xr' in navigator) {
        // WebXR AR implementation
        await startWebXRSession();
      } else {
        // Fallback camera-based AR simulation
        await startCameraSession();
      }
      
      setArSession(prev => ({ ...prev, isActive: true, error: null }));
    } catch (error) {
      setArSession(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'AR başlatılamadı' 
      }));
    } finally {
      setIsLoading(false);
    }
  }, []);

  const startWebXRSession = useCallback(async () => {
    const xr = (navigator as any).xr;
    const session = await xr.requestSession('immersive-ar', {
      requiredFeatures: ['hit-test'],
      optionalFeatures: ['dom-overlay'],
      domOverlay: { root: document.body }
    });

    // WebXR session setup would go here
    // This is a simplified implementation
    console.log('WebXR AR session started', session);
  }, []);

  const startCameraSession = useCallback(async () => {
    if (!videoRef.current) return;

    const stream = await navigator.mediaDevices.getUserMedia({
      video: { 
        facingMode: 'environment',
        width: { ideal: 1280 },
        height: { ideal: 720 }
      }
    });

    videoRef.current.srcObject = stream;
    await videoRef.current.play();
  }, []);

  const stopARSession = useCallback(() => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    
    setArSession(prev => ({ ...prev, isActive: false }));
    setSelectedProduct(null);
  }, []);

  const captureARPhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) return;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    // Draw video frame
    ctx.drawImage(video, 0, 0);
    
    // Add AR overlay (simplified)
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.font = '20px Arial';
    ctx.fillText('AR Showroom - Doğal Taş Pazarı', 20, 40);
    
    // Convert to blob and download
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ar-showroom-${Date.now()}.png`;
        a.click();
        URL.revokeObjectURL(url);
      }
    });
  }, []);

  const shareARExperience = useCallback(async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: '3D Showroom AR Deneyimi',
          text: 'Doğal taş ürünlerini AR ile keşfedin!',
          url: window.location.href
        });
      } catch (error) {
        console.log('Paylaşım iptal edildi');
      }
    } else {
      // Fallback to clipboard
      await navigator.clipboard.writeText(window.location.href);
      alert('Link kopyalandı!');
    }
  }, []);

  if (!arSession.isSupported) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">AR Desteği Bulunamadı</h3>
          <p className="text-gray-600 mb-4">
            {arSession.error || 'Cihazınız AR özelliğini desteklemiyor.'}
          </p>
          <div className="text-sm text-gray-500 space-y-1">
            <p>AR deneyimi için gereksinimler:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>Modern mobil tarayıcı (Chrome, Safari)</li>
              <li>Kamera erişim izni</li>
              <li>HTTPS bağlantısı</li>
            </ul>
          </div>
          <button
            onClick={onClose}
            className="mt-4 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            Kapat
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`fixed inset-0 bg-black z-50 ${className}`}>
      {/* AR Video Stream */}
      <div className="relative w-full h-full">
        <video
          ref={videoRef}
          className="w-full h-full object-cover"
          playsInline
          muted
        />
        
        {/* AR Overlay */}
        {arSession.isActive && (
          <div className="absolute inset-0 pointer-events-none">
            {/* AR Content would be rendered here */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-32 h-32 border-2 border-amber-500 border-dashed rounded-lg flex items-center justify-center bg-amber-500/20">
                <span className="text-white text-sm font-medium">Ürün Yerleştir</span>
              </div>
            </div>
          </div>
        )}

        {/* Top Controls */}
        <div className="absolute top-4 left-4 right-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <div className="bg-black/50 text-white px-3 py-2 rounded-lg text-sm">
              AR Showroom
            </div>
            {arSession.isActive && (
              <div className="bg-green-500 text-white px-3 py-2 rounded-lg text-sm flex items-center gap-2">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                Aktif
              </div>
            )}
          </div>
          
          <button
            onClick={arSession.isActive ? stopARSession : onClose}
            className="bg-black/50 text-white p-2 rounded-lg hover:bg-black/70 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Product Selection */}
        {arSession.isActive && (
          <div className="absolute bottom-20 left-4 right-4">
            <div className="bg-black/80 rounded-lg p-4">
              <h3 className="text-white font-medium mb-3">Ürün Seçin</h3>
              <div className="flex gap-2 overflow-x-auto">
                {products.map((product) => (
                  <button
                    key={product.id}
                    onClick={() => setSelectedProduct(product.id)}
                    className={`flex-shrink-0 p-3 rounded-lg border-2 transition-colors ${
                      selectedProduct === product.id
                        ? 'border-amber-500 bg-amber-500/20'
                        : 'border-gray-600 bg-gray-800'
                    }`}
                  >
                    <div className="text-white text-sm font-medium">
                      {product.productId}
                    </div>
                    <div className="text-gray-300 text-xs">
                      {product.scale.width}×{product.scale.height} cm
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Bottom Controls */}
        <div className="absolute bottom-4 left-4 right-4 flex justify-center items-center gap-4">
          {!arSession.isActive ? (
            <button
              onClick={startARSession}
              disabled={isLoading}
              className="bg-amber-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-amber-600 transition-colors disabled:opacity-50 flex items-center gap-2"
            >
              <Smartphone size={20} />
              {isLoading ? 'Başlatılıyor...' : 'AR Başlat'}
            </button>
          ) : (
            <>
              <button
                onClick={captureARPhoto}
                className="bg-white text-gray-900 p-3 rounded-full hover:bg-gray-100 transition-colors"
                title="Fotoğraf Çek"
              >
                <Camera size={20} />
              </button>
              
              <button
                onClick={shareARExperience}
                className="bg-white text-gray-900 p-3 rounded-full hover:bg-gray-100 transition-colors"
                title="Paylaş"
              >
                <Share2 size={20} />
              </button>
              
              <button
                onClick={() => setArMode(arMode === 'placement' ? 'viewing' : 'placement')}
                className="bg-white text-gray-900 p-3 rounded-full hover:bg-gray-100 transition-colors"
                title="Mod Değiştir"
              >
                {arMode === 'placement' ? <Move size={20} /> : <RotateCw size={20} />}
              </button>
            </>
          )}
        </div>

        {/* Instructions */}
        {arSession.isActive && (
          <div className="absolute top-20 left-4 right-4">
            <div className="bg-black/70 text-white p-3 rounded-lg text-sm">
              <div className="flex items-center gap-2 mb-2">
                <Info size={16} />
                <span className="font-medium">Nasıl Kullanılır</span>
              </div>
              <ul className="space-y-1 text-xs">
                <li>• Ürün seçin ve yerleştirmek istediğiniz yüzeyi hedefleyin</li>
                <li>• Dokunarak ürünü yerleştirin</li>
                <li>• Fotoğraf çekmek için kamera butonunu kullanın</li>
              </ul>
            </div>
          </div>
        )}
      </div>

      {/* Hidden canvas for photo capture */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
};

export default ARViewer;
