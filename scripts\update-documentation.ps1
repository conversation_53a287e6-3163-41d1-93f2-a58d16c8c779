# Otomatik Dokümantasyon Güncelleme Script'i
# Türkiye Doğal Taş Marketplace Platformu
# PowerShell Script - Windows Uyumlu

param(
    [string]$ChangeType = "general",
    [string]$Description = "Genel güncelleme",
    [string]$Version = "auto"
)

# Script başlangıç bilgileri
Write-Host "🔄 Dokümantasyon Güncelleme Script'i Başlatılıyor..." -ForegroundColor Green
Write-Host "📅 Tarih: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
Write-Host "📝 Değişiklik Türü: $ChangeType" -ForegroundColor Yellow
Write-Host "📋 Açıklama: $Description" -ForegroundColor Yellow

# Proje kök dizinini belirle
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Write-Host "📁 Proje Dizini: $ProjectRoot" -ForegroundColor Cyan

# Tarih ve versiyon bilgileri
$CurrentDate = Get-Date -Format "yyyy-MM-dd"
$CurrentDateTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

if ($Version -eq "auto") {
    # Otomatik versiyon belirleme (basit increment)
    $Version = "2.3.$(Get-Date -Format 'MMdd')"
}

Write-Host "🏷️ Versiyon: $Version" -ForegroundColor Yellow

# Güncelleme fonksiyonları
function Update-PRD {
    Write-Host "📄 PRD.md güncelleniyor..." -ForegroundColor Blue
    
    $prdPath = Join-Path $ProjectRoot "PRD.md"
    if (Test-Path $prdPath) {
        # Son güncelleme tarihini güncelle
        $content = Get-Content $prdPath -Raw
        $content = $content -replace '\*\*Son Güncelleme\*\*: \d{4}-\d{2}-\d{2}', "**Son Güncelleme**: $CurrentDate"
        $content = $content -replace '\*\*Doküman Versiyonu\*\*: [\d\.]+', "**Doküman Versiyonu**: $Version"
        
        Set-Content -Path $prdPath -Value $content -Encoding UTF8
        Write-Host "✅ PRD.md güncellendi" -ForegroundColor Green
    } else {
        Write-Host "❌ PRD.md bulunamadı" -ForegroundColor Red
    }
}

function Update-README {
    Write-Host "📄 README.md güncelleniyor..." -ForegroundColor Blue
    
    $readmePath = Join-Path $ProjectRoot "README.md"
    if (Test-Path $readmePath) {
        # Son güncelleme tarihini güncelle
        $content = Get-Content $readmePath -Raw
        $content = $content -replace '\*\*Son Güncelleme\*\*: \d{4}-\d{2}-\d{2}', "**Son Güncelleme**: $CurrentDate"
        $content = $content -replace '\*\*Versiyon\*\*: [\d\.\-\w]+', "**Versiyon**: $Version"
        
        Set-Content -Path $readmePath -Value $content -Encoding UTF8
        Write-Host "✅ README.md güncellendi" -ForegroundColor Green
    } else {
        Write-Host "❌ README.md bulunamadı" -ForegroundColor Red
    }
}

function Update-RFC-Files {
    Write-Host "📁 RFC dosyaları güncelleniyor..." -ForegroundColor Blue
    
    $rfcPath = Join-Path $ProjectRoot "RFC"
    if (Test-Path $rfcPath) {
        $rfcFiles = Get-ChildItem -Path $rfcPath -Filter "*.md" -Exclude "README.md"
        
        foreach ($file in $rfcFiles) {
            Write-Host "  📝 $($file.Name) güncelleniyor..." -ForegroundColor Cyan
            
            $content = Get-Content $file.FullName -Raw
            
            # Tarih güncellemeleri
            if ($content -match '\*\*Tarih\*\*: \d{4}-\d{2}-\d{2}') {
                $content = $content -replace '\*\*Tarih\*\*: \d{4}-\d{2}-\d{2}', "**Tarih**: $CurrentDate"
            }
            
            if ($content -match '\*\*Son Güncelleme\*\*: \d{4}-\d{2}-\d{2}') {
                $content = $content -replace '\*\*Son Güncelleme\*\*: \d{4}-\d{2}-\d{2}', "**Son Güncelleme**: $CurrentDate"
            }
            
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        }
        
        Write-Host "✅ RFC dosyaları güncellendi" -ForegroundColor Green
    } else {
        Write-Host "❌ RFC dizini bulunamadı" -ForegroundColor Red
    }
}

function Add-Changelog-Entry {
    Write-Host "📋 Changelog girişi ekleniyor..." -ForegroundColor Blue
    
    $changelogPath = Join-Path $ProjectRoot "CHANGELOG.md"
    
    # Changelog dosyası yoksa oluştur
    if (-not (Test-Path $changelogPath)) {
        $initialContent = @"
# Changelog
# Türkiye Doğal Taş Marketplace Platformu

Bu dosya projedeki tüm önemli değişiklikleri takip eder.

## [Unreleased]

"@
        Set-Content -Path $changelogPath -Value $initialContent -Encoding UTF8
    }
    
    # Yeni giriş ekle
    $newEntry = @"
## [$Version] - $CurrentDate

### $ChangeType
- $Description

"@
    
    $content = Get-Content $changelogPath -Raw
    $content = $content -replace '## \[Unreleased\]', "## [Unreleased]`n`n$newEntry"
    
    Set-Content -Path $changelogPath -Value $content -Encoding UTF8
    Write-Host "✅ Changelog güncellendi" -ForegroundColor Green
}

function Show-Summary {
    Write-Host "`n📊 Güncelleme Özeti:" -ForegroundColor Magenta
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Magenta
    Write-Host "📅 Tarih: $CurrentDate" -ForegroundColor White
    Write-Host "🏷️ Versiyon: $Version" -ForegroundColor White
    Write-Host "📝 Değişiklik Türü: $ChangeType" -ForegroundColor White
    Write-Host "📋 Açıklama: $Description" -ForegroundColor White
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Magenta
    
    Write-Host "`n✅ Güncellenen Dosyalar:" -ForegroundColor Green
    Write-Host "  • PRD.md" -ForegroundColor White
    Write-Host "  • README.md" -ForegroundColor White
    Write-Host "  • RFC/*.md dosyaları" -ForegroundColor White
    Write-Host "  • CHANGELOG.md" -ForegroundColor White
    
    Write-Host "`n🎯 Sonraki Adımlar:" -ForegroundColor Yellow
    Write-Host "  1. Git commit oluşturun: git add . && git commit -m 'docs: $Description'" -ForegroundColor Cyan
    Write-Host "  2. Değişiklikleri push edin: git push origin main" -ForegroundColor Cyan
    Write-Host "  3. Gerekirse PR oluşturun" -ForegroundColor Cyan
}

# Ana güncelleme süreci
try {
    Write-Host "`n🚀 Güncelleme süreci başlatılıyor..." -ForegroundColor Green
    
    Update-PRD
    Update-README
    Update-RFC-Files
    Add-Changelog-Entry
    
    Show-Summary
    
    Write-Host "`n🎉 Dokümantasyon güncelleme tamamlandı!" -ForegroundColor Green
    
} catch {
    Write-Host "`n❌ Hata oluştu: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "📞 Destek için: <EMAIL>" -ForegroundColor Yellow
    exit 1
}

# PowerShell komut örnekleri
Write-Host "`n💡 PowerShell Komut Örnekleri:" -ForegroundColor Cyan
Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Cyan
Write-Host "# Basit güncelleme" -ForegroundColor Gray
Write-Host ".\scripts\update-documentation.ps1" -ForegroundColor White
Write-Host ""
Write-Host "# Özel açıklama ile" -ForegroundColor Gray
Write-Host ".\scripts\update-documentation.ps1 -Description 'UI basitleştirmesi tamamlandı'" -ForegroundColor White
Write-Host ""
Write-Host "# Belirli değişiklik türü ile" -ForegroundColor Gray
Write-Host ".\scripts\update-documentation.ps1 -ChangeType 'Feature' -Description 'Yeni 3D goruntuleyici eklendi'" -ForegroundColor White
Write-Host ""
Write-Host "# Çoklu komut (PowerShell için ; kullanın)" -ForegroundColor Gray
Write-Host "npm install; npm run build; .\scripts\update-documentation.ps1" -ForegroundColor White
Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Cyan
