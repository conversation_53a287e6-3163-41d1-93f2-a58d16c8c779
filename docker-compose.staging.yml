version: '3.8'

services:
  # PostgreSQL Database for Staging
  postgres-staging:
    image: postgres:15-alpine
    container_name: natural-stone-postgres-staging
    restart: unless-stopped
    environment:
      POSTGRES_DB: natural_stone_marketplace_staging
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-staging_password}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_staging_data:/var/lib/postgresql/data
      - ./backend/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    networks:
      - staging-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d natural_stone_marketplace_staging"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis Cache for Staging
  redis-staging:
    image: redis:7-alpine
    container_name: natural-stone-redis-staging
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-staging_redis} --maxmemory 128mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_staging_data:/data
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    networks:
      - staging-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API for Staging
  backend-staging:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: natural-stone-backend-staging
    restart: unless-stopped
    environment:
      NODE_ENV: staging
      PORT: 8001
      DATABASE_URL: postgresql://postgres:${POSTGRES_PASSWORD:-staging_password}@postgres-staging:5432/natural_stone_marketplace_staging
      REDIS_HOST: redis-staging
      REDIS_PASSWORD: ${REDIS_PASSWORD:-staging_redis}
      JWT_SECRET: ${JWT_SECRET:-staging_jwt_secret}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET:-staging_refresh_secret}
      FRONTEND_URL: http://localhost:3001
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - ./backend/.env.staging:/app/.env.staging:ro
    ports:
      - "8001:8001"
    networks:
      - staging-network
    depends_on:
      postgres-staging:
        condition: service_healthy
      redis-staging:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend Application for Staging
  frontend-staging:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
    container_name: natural-stone-frontend-staging
    restart: unless-stopped
    environment:
      NODE_ENV: staging
      PORT: 3001
      NEXT_PUBLIC_API_URL: http://localhost:8001
      NEXTAUTH_URL: http://localhost:3001
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET:-staging_nextauth_secret}
    volumes:
      - ./frontend/.env.staging:/app/.env.staging:ro
    ports:
      - "3001:3001"
    networks:
      - staging-network
    depends_on:
      backend-staging:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx for Staging (optional)
  nginx-staging:
    image: nginx:alpine
    container_name: natural-stone-nginx-staging
    restart: unless-stopped
    volumes:
      - ./nginx/staging.conf:/etc/nginx/nginx.conf:ro
      - ./frontend/public:/var/www/static:ro
    ports:
      - "8080:80"
    networks:
      - staging-network
    depends_on:
      - frontend-staging
      - backend-staging
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring for Staging
  prometheus-staging:
    image: prom/prometheus:latest
    container_name: natural-stone-prometheus-staging
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus-staging.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_staging_data:/prometheus
    ports:
      - "9091:9090"
    networks:
      - staging-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'  # Shorter retention for staging
      - '--web.enable-lifecycle'

  # Grafana for Staging
  grafana-staging:
    image: grafana/grafana:latest
    container_name: natural-stone-grafana-staging
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-staging_grafana}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-piechart-panel
    volumes:
      - grafana_staging_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3002:3000"
    networks:
      - staging-network
    depends_on:
      - prometheus-staging

  # Test Runner (for automated testing)
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    container_name: natural-stone-test-runner
    environment:
      NODE_ENV: test
      DATABASE_URL: postgresql://postgres:${POSTGRES_PASSWORD:-staging_password}@postgres-staging:5432/natural_stone_marketplace_staging
      API_URL: http://backend-staging:8001
      FRONTEND_URL: http://frontend-staging:3001
    volumes:
      - ./tests:/app/tests
      - ./test-results:/app/test-results
    networks:
      - staging-network
    depends_on:
      backend-staging:
        condition: service_healthy
      frontend-staging:
        condition: service_healthy
    profiles:
      - testing  # Only run when explicitly requested

networks:
  staging-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_staging_data:
    driver: local
  redis_staging_data:
    driver: local
  prometheus_staging_data:
    driver: local
  grafana_staging_data:
    driver: local
