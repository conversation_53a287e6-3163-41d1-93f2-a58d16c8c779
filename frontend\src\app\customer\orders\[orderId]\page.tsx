'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import {
  Package,
  CreditCard,
  Truck,
  CheckCircle,
  Clock,
  AlertTriangle,
  Upload,
  Download,
  Eye,
  Building,
  Calendar,
  DollarSign,
  FileText,
  MapPin,
  XCircle
} from 'lucide-react';
import { ShippingPlanModal } from '@/components/shipping/ShippingPlanModal';
import toast from 'react-hot-toast';

interface DeliverySchedule {
  id: string;
  deliveryNumber: number;
  quantity: number;
  unit: string;
  scheduledDate: string;
  status: 'pending' | 'in_production' | 'ready' | 'shipped' | 'delivered';
  payment: {
    id: string;
    amount: number;
    currency: string;
    status: 'pending' | 'receipt_uploaded' | 'verified' | 'completed';
    dueDate: string;
    receiptUrl?: string;
    bankInfo?: {
      iban: string;
      referenceCode: string;
    };
  };
  trackingNumber?: string;
  estimatedDelivery?: string;
}

interface OrderDetail {
  id: string;
  orderNumber: string;
  productName: string;
  totalQuantity: number;
  unit: string;
  totalAmount: number;
  currency: string;
  status: 'pending' | 'confirmed' | 'in_production' | 'partially_delivered' | 'completed' | 'cancelled';
  createdAt: string;
  producer: {
    name: string;
    contact: string;
    location: string;
  };
  deliverySchedules: DeliverySchedule[];
  overallProgress: number;
}

export default function OrderDetailPage() {
  const params = useParams();
  const orderId = params.orderId as string;
  const [order, setOrder] = useState<OrderDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPayment, setSelectedPayment] = useState<DeliverySchedule['payment'] | null>(null);
  const [receiptFile, setReceiptFile] = useState<File | null>(null);
  const [bankReference, setBankReference] = useState('');
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [selectedDelivery, setSelectedDelivery] = useState<DeliverySchedule | null>(null);
  const [approvalNotes, setApprovalNotes] = useState('');
  const [showShippingPlanModal, setShowShippingPlanModal] = useState(false);

  useEffect(() => {
    fetchOrderDetail();
  }, [orderId]);

  const fetchOrderDetail = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockOrder: OrderDetail = {
        id: orderId,
        orderNumber: `ORD-${orderId.slice(-6).toUpperCase()}`,
        productName: 'Beyaz Mermer - Afyon',
        totalQuantity: 500,
        unit: 'm²',
        totalAmount: 75000,
        currency: 'TRY',
        status: 'in_production',
        createdAt: '2024-01-15T10:00:00Z',
        producer: {
          name: 'Afyon Mermer A.Ş.',
          contact: '+90 272 123 45 67',
          location: 'Afyonkarahisar, Türkiye'
        },
        deliverySchedules: [
          {
            id: 'del_001',
            deliveryNumber: 1,
            quantity: 150,
            unit: 'm²',
            scheduledDate: '2024-02-01',
            status: 'delivered',
            payment: {
              id: 'pay_001',
              amount: 22500,
              currency: 'TRY',
              status: 'completed',
              dueDate: '2024-01-25T23:59:59Z',
              receiptUrl: '/receipts/receipt_001.pdf',
              bankInfo: {
                iban: 'TR33 0006 4000 0011 2345 6789 01',
                referenceCode: 'REF001'
              }
            },
            trackingNumber: 'TRK123456789',
            estimatedDelivery: '2024-02-03'
          },
          {
            id: 'del_002',
            deliveryNumber: 2,
            quantity: 200,
            unit: 'm²',
            scheduledDate: '2024-02-15',
            status: 'ready',
            payment: {
              id: 'pay_002',
              amount: 30000,
              currency: 'TRY',
              status: 'verified',
              dueDate: '2024-02-10T23:59:59Z',
              receiptUrl: '/receipts/receipt_002.pdf',
              bankInfo: {
                iban: 'TR33 0006 4000 0011 2345 6789 01',
                referenceCode: 'REF002'
              }
            }
          },
          {
            id: 'del_003',
            deliveryNumber: 3,
            quantity: 150,
            unit: 'm²',
            scheduledDate: '2024-03-01',
            status: 'pending',
            payment: {
              id: 'pay_003',
              amount: 22500,
              currency: 'TRY',
              status: 'pending',
              dueDate: '2024-02-25T23:59:59Z',
              bankInfo: {
                iban: 'TR33 0006 4000 0011 2345 6789 01',
                referenceCode: 'REF003'
              }
            }
          }
        ],
        overallProgress: 45
      };
      setOrder(mockOrder);
    } catch (error) {
      console.error('Error fetching order:', error);
      toast.error('Sipariş detayları yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentUpload = async (paymentId: string) => {
    if (!receiptFile || !bankReference.trim()) {
      toast.error('Lütfen dekont dosyası seçin ve banka referans numarasını girin');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('receipt', receiptFile);
      formData.append('bankReference', bankReference);
      formData.append('paymentId', paymentId);

      const response = await fetch(`/api/payments/${paymentId}/receipt`, {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        toast.success('Dekont başarıyla yüklendi');
        await fetchOrderDetail();
        setSelectedPayment(null);
        setReceiptFile(null);
        setBankReference('');
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Error uploading receipt:', error);
      toast.error('Dekont yüklenirken hata oluştu');
    }
  };

  const handleApproveDelivery = async (deliveryId: string, approved: boolean, notes?: string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}/deliveries/${deliveryId}/approve`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          approved,
          notes: notes || approvalNotes,
          timestamp: new Date().toISOString()
        })
      });

      if (response.ok) {
        if (approved) {
          toast.success('Teslimat onaylandı! Ödeme üreticiye gönderilecek.');
        } else {
          toast.success('Teslimat reddedildi. Üretici bilgilendirilecek.');
        }
        await fetchOrderDetail();
        setShowApprovalModal(false);
        setSelectedDelivery(null);
        setApprovalNotes('');
      } else {
        throw new Error('Approval failed');
      }
    } catch (error) {
      console.error('Error processing delivery approval:', error);
      toast.error('İşlem sırasında hata oluştu');
    }
  };

  const openApprovalModal = (delivery: DeliverySchedule) => {
    setSelectedDelivery(delivery);
    setShowApprovalModal(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Bekliyor', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      receipt_uploaded: { label: 'Dekont Yüklendi', color: 'bg-blue-100 text-blue-800', icon: Upload },
      verified: { label: 'Onaylandı', color: 'bg-green-100 text-green-800', icon: CheckCircle },
      completed: { label: 'Tamamlandı', color: 'bg-green-100 text-green-800', icon: CheckCircle },
      in_production: { label: 'Üretimde', color: 'bg-purple-100 text-purple-800', icon: Package },
      ready: { label: 'Hazır', color: 'bg-blue-100 text-blue-800', icon: Package },
      shipped: { label: 'Kargoda', color: 'bg-orange-100 text-orange-800', icon: Truck },
      delivered: { label: 'Teslim Edildi', color: 'bg-green-100 text-green-800', icon: CheckCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Sipariş detayları yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Sipariş bulunamadı</h3>
        <p className="text-gray-600">Bu sipariş mevcut değil veya erişim yetkiniz bulunmuyor.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Sipariş Detayı</h1>
          <p className="text-gray-600 mt-1">{order.orderNumber}</p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            onClick={() => setShowShippingPlanModal(true)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Truck className="w-4 h-4" />
            Sevkiyat Planı
          </Button>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(order.totalAmount, order.currency)}
            </div>
            {getStatusBadge(order.status)}
          </div>
        </div>
      </div>

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Sipariş Özeti
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Ürün Bilgileri</h4>
              <p className="text-sm text-gray-600">Ürün: {order.productName}</p>
              <p className="text-sm text-gray-600">Toplam Miktar: {order.totalQuantity} {order.unit}</p>
              <p className="text-sm text-gray-600">Sipariş Tarihi: {formatDate(order.createdAt)}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Üretici Bilgileri</h4>
              <p className="text-sm text-gray-600 flex items-center gap-1">
                <Building className="w-4 h-4" />
                {order.producer.name}
              </p>
              <p className="text-sm text-gray-600">{order.producer.contact}</p>
              <p className="text-sm text-gray-600 flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                {order.producer.location}
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Genel İlerleme</h4>
              <Progress value={order.overallProgress} className="mb-2" />
              <p className="text-sm text-gray-600">%{order.overallProgress} tamamlandı</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delivery Schedules and Payments */}
      <Tabs defaultValue="deliveries" className="space-y-4">
        <TabsList>
          <TabsTrigger value="deliveries">Teslimat Programı</TabsTrigger>
          <TabsTrigger value="payments">Ödeme Takibi</TabsTrigger>
        </TabsList>

        <TabsContent value="deliveries" className="space-y-4">
          {order.deliverySchedules.map((delivery) => (
            <Card key={delivery.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    {delivery.deliveryNumber}. Teslimat - {delivery.quantity} {delivery.unit}
                  </CardTitle>
                  {getStatusBadge(delivery.status)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Planlanan Tarih</p>
                    <p className="text-sm text-gray-600 flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {formatDate(delivery.scheduledDate)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Ödeme Durumu</p>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(delivery.payment.status)}
                      <span className="text-sm text-gray-600">
                        {formatCurrency(delivery.payment.amount, delivery.payment.currency)}
                      </span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Kargo Takip</p>
                    <p className="text-sm text-gray-600">
                      {delivery.trackingNumber || 'Henüz kargoya verilmedi'}
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 mt-4">
                  {delivery.payment.status === 'pending' && (
                    <Button 
                      onClick={() => setSelectedPayment(delivery.payment)}
                      size="sm"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Ödeme Dekontu Yükle
                    </Button>
                  )}
                  
                  {delivery.status === 'ready' && delivery.payment.status === 'verified' && (
                    <>
                      <Button
                        onClick={() => openApprovalModal(delivery)}
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Teslimatı Onayla
                      </Button>
                      <Button
                        onClick={() => openApprovalModal(delivery)}
                        size="sm"
                        variant="destructive"
                      >
                        <XCircle className="w-4 h-4 mr-2" />
                        Reddet
                      </Button>
                    </>
                  )}
                  
                  {delivery.payment.receiptUrl && (
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-2" />
                      Dekontu Görüntüle
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="w-5 h-5" />
                Ödeme Geçmişi
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.deliverySchedules.map((delivery) => (
                  <div key={delivery.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">
                        {delivery.deliveryNumber}. Teslimat Ödemesi
                      </h4>
                      {getStatusBadge(delivery.payment.status)}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-900">Tutar:</span>
                        <span className="ml-2">
                          {formatCurrency(delivery.payment.amount, delivery.payment.currency)}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-900">Son Ödeme:</span>
                        <span className="ml-2">{formatDate(delivery.payment.dueDate)}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-900">Referans:</span>
                        <span className="ml-2 font-mono">
                          {delivery.payment.bankInfo?.referenceCode || '-'}
                        </span>
                      </div>
                    </div>

                    {delivery.payment.bankInfo && delivery.payment.status === 'pending' && (
                      <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm font-medium text-blue-900 mb-2">Banka Bilgileri:</p>
                        <p className="text-sm text-blue-800">
                          IBAN: {delivery.payment.bankInfo.iban}
                        </p>
                        <p className="text-sm text-blue-800">
                          Referans: {delivery.payment.bankInfo.referenceCode}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Payment Upload Modal */}
      {selectedPayment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Ödeme Dekontu Yükle</CardTitle>
              <p className="text-sm text-gray-600">
                Tutar: {formatCurrency(selectedPayment.amount, selectedPayment.currency)}
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Dekont Dosyası</label>
                <input
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={(e) => setReceiptFile(e.target.files?.[0] || null)}
                  className="w-full p-2 border rounded-lg"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Banka Referans Numarası</label>
                <input
                  type="text"
                  value={bankReference}
                  onChange={(e) => setBankReference(e.target.value)}
                  placeholder="Havale referans numaranızı girin"
                  className="w-full p-2 border rounded-lg"
                />
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedPayment(null);
                    setReceiptFile(null);
                    setBankReference('');
                  }}
                  className="flex-1"
                >
                  İptal
                </Button>
                <Button
                  onClick={() => handlePaymentUpload(selectedPayment.id)}
                  disabled={!receiptFile || !bankReference.trim()}
                  className="flex-1"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Yükle
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Approval/Rejection Modal */}
      {showApprovalModal && selectedDelivery && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Teslimat Onayı</CardTitle>
              <p className="text-sm text-gray-600">
                {selectedDelivery.deliveryNumber}. Teslimat - {selectedDelivery.quantity} {selectedDelivery.unit}
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="approval-notes">Notlarınız (İsteğe bağlı)</Label>
                <Textarea
                  id="approval-notes"
                  value={approvalNotes}
                  onChange={(e) => setApprovalNotes(e.target.value)}
                  placeholder="Teslimat hakkında notlarınızı yazabilirsiniz..."
                  className="mt-1"
                  rows={3}
                />
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-800">
                  <strong>Onay:</strong> Ödeme üreticiye gönderilecek<br/>
                  <strong>Red:</strong> Üretici bilgilendirilecek ve çözüm aranacak
                </p>
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowApprovalModal(false);
                    setSelectedDelivery(null);
                    setApprovalNotes('');
                  }}
                  className="flex-1"
                >
                  İptal
                </Button>
                <Button
                  onClick={() => handleApproveDelivery(selectedDelivery.id, false)}
                  variant="destructive"
                  className="flex-1"
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  Reddet
                </Button>
                <Button
                  onClick={() => handleApproveDelivery(selectedDelivery.id, true)}
                  className="flex-1 bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Onayla
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Shipping Plan Modal */}
      <ShippingPlanModal
        isOpen={showShippingPlanModal}
        onClose={() => setShowShippingPlanModal(false)}
        orderId={order.orderNumber}
        userType="customer"
        initialSchedules={order.deliverySchedules.map(d => ({
          id: d.id,
          deliveryNumber: d.deliveryNumber,
          quantity: d.quantity,
          unit: d.unit,
          proposedDate: d.scheduledDate,
          status: 'approved',
          proposedBy: 'producer' as const,
          producerNotes: `${d.deliveryNumber}. teslimat planı`
        }))}
        onSavePlan={(schedules) => {
          console.log('Shipping plan saved:', schedules);
          // Here you would save the plan via API
        }}
      />
    </div>
  );
}
