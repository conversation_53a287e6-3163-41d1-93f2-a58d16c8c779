import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = 'https://dogaltaspazari.com'
  
  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          '/products',
          '/3d-showroom',
          '/news',
          '/about',
          '/contact',
        ],
        disallow: [
          '/admin/',
          '/api/',
          '/customer/dashboard/',
          '/producer/dashboard/',
          '/_next/',
          '/private/',
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: [
          '/',
          '/products',
          '/3d-showroom',
          '/news',
          '/about',
          '/contact',
        ],
        disallow: [
          '/admin/',
          '/api/',
          '/customer/dashboard/',
          '/producer/dashboard/',
        ],
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  }
}
