# RFC-004: UI/UX Tasarım Sistemi ve Kullanıcı Deneyimi
**Türkiye Doğal Taş Marketplace Platformu**

## Özet

Bu RFC, Türkiye Doğal Taş Marketplace platformu için kapsamlı UI/UX tasarım sistemi, kullanıc<PERSON> deneyimi prensipleri, görsel tasarım rehberi ve kullanıcı arayüzü spesifikasyonlarını tanımlar. Modern, kullanıcı dostu ve erişilebilir bir platform tasarımı hedeflenmektedir.

## Durum
- **Durum**: Taslak
- **Tarih**: 2025-06-28
- **Yazar**: Augment Agent
- **Gözden Geçiren**: -
- **Onaylayan**: -

## 1. Giriş

### 1.1 Amaç
Türk doğal taş sektörüne özel, kullanıcı merkezli bir tasarım sistemi oluşturmak ve tutarlı kullanıcı deneyimi sağ<PERSON>ak.

### 1.2 Kapsam
- Görsel tasarım sistemi (renk, tip<PERSON><PERSON><PERSON>, icon<PERSON>)
- Kullanıcı arayüzü bileşenleri
- Kullanıcı akışları ve wireframe'ler
- Responsive tasarım kuralları
- Erişilebilirlik standartları
- Çok dilli tasarım prensipleri

### 1.3 Hedef Kullanıcılar
- **Üreticiler**: Türk doğal taş üreticileri ve işleyicileri
- **Müşteriler**: Uluslararası ithalatçılar ve distribütörler
- **Adminler**: Platform yöneticileri
- **Ziyaretçiler**: Potansiyel kullanıcılar

## 2. Tasarım Felsefesi

### 2.1 Temel Prensipler

#### 2.1.1 Güven ve Şeffaflık
- Açık ve anlaşılır bilgi sunumu
- Güvenilir görsel dil
- Şeffaf süreç gösterimleri
- Profesyonel ve ciddi yaklaşım

#### 2.1.2 Kültürel Uyum
- Türk kültürüne saygılı tasarım
- Doğal taş sektörünün geleneksel değerleri
- Uluslararası standartlara uyum
- Çok kültürlü kullanıcı deneyimi

#### 2.1.3 Kullanım Kolaylığı
- Sezgisel navigasyon
- Minimal öğrenme eğrisi
- Hızlı görev tamamlama
- Hata toleransı

#### 2.1.4 Performans ve Erişilebilirlik
- Hızlı yükleme süreleri
- WCAG 2.1 AA uyumluluğu
- Çok cihaz desteği
- Düşük bant genişliği optimizasyonu

## 3. Görsel Tasarım Sistemi

### 3.1 Renk Paleti

#### 3.1.1 Ana Renkler
```css
/* Birincil Renkler */
--primary-stone: #8B7355;      /* Doğal taş rengi */
--primary-dark: #5D4E37;       /* Koyu kahve */
--primary-light: #A68B5B;      /* Açık taş rengi */

/* İkincil Renkler */
--secondary-marble: #F5F5DC;   /* Mermer beyazı */
--secondary-granite: #2F4F4F;  /* Granit grisi */
--secondary-travertine: #DEB887; /* Traverten bej */

/* Sistem Renkleri */
--success: #22C55E;            /* Başarı yeşili */
--warning: #F59E0B;            /* Uyarı sarısı */
--error: #EF4444;              /* Hata kırmızısı */
--info: #3B82F6;               /* Bilgi mavisi */
```

#### 3.1.2 Nötr Renkler
```css
/* Gri Tonları */
--gray-50: #F9FAFB;
--gray-100: #F3F4F6;
--gray-200: #E5E7EB;
--gray-300: #D1D5DB;
--gray-400: #9CA3AF;
--gray-500: #6B7280;
--gray-600: #4B5563;
--gray-700: #374151;
--gray-800: #1F2937;
--gray-900: #111827;
```

#### 3.1.3 Renk Kullanım Kuralları
- **Birincil renkler**: Ana eylemler, başlıklar, vurgular
- **İkincil renkler**: Arka planlar, kartlar, bölümler
- **Sistem renkleri**: Durum bildirimleri, uyarılar
- **Nötr renkler**: Metinler, kenarlıklar, gölgeler

### 3.2 Tipografi

#### 3.2.1 Font Ailesi
```css
/* Birincil Font - Modern ve Okunabilir */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

/* İkincil Font - Başlıklar için */
--font-secondary: 'Playfair Display', Georgia, serif;

/* Monospace Font - Kodlar için */
--font-mono: 'JetBrains Mono', 'Fira Code', monospace;
```

#### 3.2.2 Font Boyutları
```css
/* Başlık Boyutları */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */
--text-4xl: 2.25rem;   /* 36px */
--text-5xl: 3rem;      /* 48px */
--text-6xl: 3.75rem;   /* 60px */
```

#### 3.2.3 Font Ağırlıkları
```css
--font-thin: 100;
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
--font-extrabold: 800;
--font-black: 900;
```

### 3.3 Spacing (Boşluk) Sistemi

#### 3.3.1 Spacing Scale
```css
--space-0: 0;
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-5: 1.25rem;   /* 20px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-10: 2.5rem;   /* 40px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
--space-20: 5rem;     /* 80px */
--space-24: 6rem;     /* 96px */
--space-32: 8rem;     /* 128px */
```

### 3.4 Gölge ve Derinlik

#### 3.4.1 Box Shadow
```css
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
--shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
```

### 3.5 Border Radius
```css
--radius-none: 0;
--radius-sm: 0.125rem;   /* 2px */
--radius: 0.25rem;       /* 4px */
--radius-md: 0.375rem;   /* 6px */
--radius-lg: 0.5rem;     /* 8px */
--radius-xl: 0.75rem;    /* 12px */
--radius-2xl: 1rem;      /* 16px */
--radius-3xl: 1.5rem;    /* 24px */
--radius-full: 9999px;   /* Tam yuvarlak */
```

## 4. UI Bileşenleri

### 4.1 Butonlar

#### 4.1.1 Buton Varyantları
```typescript
type ButtonVariant = 
  | 'primary'      // Ana eylemler
  | 'secondary'    // İkincil eylemler
  | 'outline'      // Çerçeveli butonlar
  | 'ghost'        // Şeffaf butonlar
  | 'link'         // Link görünümü
  | 'destructive'; // Tehlikeli eylemler

type ButtonSize = 'sm' | 'md' | 'lg' | 'xl';
```

#### 4.1.2 Buton Stilleri
```css
/* Primary Button */
.btn-primary {
  background-color: var(--primary-stone);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Secondary Button */
.btn-secondary {
  background-color: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

/* Outline Button */
.btn-outline {
  background-color: transparent;
  color: var(--primary-stone);
  border: 2px solid var(--primary-stone);
}
```

### 4.2 Form Elemanları

#### 4.2.1 Input Alanları
```css
.input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--primary-stone);
  box-shadow: 0 0 0 3px rgb(139 115 85 / 0.1);
}

.input:invalid {
  border-color: var(--error);
}
```

#### 4.2.2 Select Dropdown
```css
.select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--space-3) center;
  background-size: 1rem;
  padding-right: var(--space-10);
}
```

### 4.3 Kartlar ve Paneller

#### 4.3.1 Ürün Kartı
```css
.product-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.product-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.product-card-content {
  padding: var(--space-6);
}

.product-card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--gray-900);
  margin-bottom: var(--space-2);
}

.product-card-price {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--primary-stone);
}
```

### 4.4 Navigasyon

#### 4.4.1 Ana Navigasyon
```css
.navbar {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) 0;
  position: sticky;
  top: 0;
  z-index: 50;
}

.navbar-brand {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--primary-stone);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  gap: var(--space-8);
  align-items: center;
}

.navbar-link {
  color: var(--gray-700);
  text-decoration: none;
  font-weight: var(--font-medium);
  transition: color 0.2s ease;
}

.navbar-link:hover,
.navbar-link.active {
  color: var(--primary-stone);
}
```

#### 4.4.2 Breadcrumb
```css
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-6);
}

.breadcrumb-item {
  color: var(--gray-500);
  font-size: var(--text-sm);
}

.breadcrumb-item:last-child {
  color: var(--gray-900);
  font-weight: var(--font-medium);
}

.breadcrumb-separator {
  color: var(--gray-400);
}
```

## 5. Layout Sistemi

### 5.1 Grid Sistemi

#### 5.1.1 Container
```css
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container { padding: 0 var(--space-6); }
}

@media (min-width: 1024px) {
  .container { padding: 0 var(--space-8); }
}
```

#### 5.1.2 Grid Layout
```css
.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive Grid */
@media (min-width: 640px) {
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}
```

### 5.2 Flexbox Utilities
```css
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
```

## 6. Responsive Tasarım

### 6.1 Breakpoint'ler
```css
/* Mobile First Approach */
/* xs: 0px - 639px (default) */
/* sm: 640px - 767px */
@media (min-width: 640px) { /* sm */ }

/* md: 768px - 1023px */
@media (min-width: 768px) { /* md */ }

/* lg: 1024px - 1279px */
@media (min-width: 1024px) { /* lg */ }

/* xl: 1280px - 1535px */
@media (min-width: 1280px) { /* xl */ }

/* 2xl: 1536px+ */
@media (min-width: 1536px) { /* 2xl */ }
```

### 6.2 Responsive Kuralları

#### 6.2.1 Mobil Öncelikli Tasarım
- Tüm tasarımlar önce mobil için optimize edilir
- Desktop özellikleri progressively enhancement ile eklenir
- Touch-friendly interface elemanları
- Minimum 44px touch target boyutu

#### 6.2.2 Tablet Optimizasyonu
- Orta boyut ekranlar için özel layout'lar
- Sidebar'ların collapsible olması
- Grid sisteminin tablet için optimize edilmesi

#### 6.2.3 Desktop Enhancements
- Hover efektleri
- Keyboard navigation
- Multi-column layouts
- Advanced filtering ve sorting

## 7. Kullanıcı Akışları ve Wireframe'ler

### 7.1 Ana Kullanıcı Senaryoları

#### 7.1.1 Üretici Kayıt Akışı
```mermaid
flowchart TD
    A[Ana Sayfa] --> B[Üretici Ol]
    B --> C[Kayıt Formu]
    C --> D[Firma Bilgileri]
    D --> E[Belge Yükleme]
    E --> F[Ocak/Fabrika Bilgileri]
    F --> G[Ürün Kategorileri]
    G --> H[Başvuru Gönder]
    H --> I[Email Doğrulama]
    I --> J[Admin Onayı Bekle]
    J --> K[Onay Bildirimi]
    K --> L[Dashboard Erişimi]
```

#### 7.1.2 Müşteri Teklif Talep Akışı
```mermaid
flowchart TD
    A[Ürün Arama] --> B[Ürün Detayı]
    B --> C[3D Görüntüleme]
    C --> D[Ebat Seçimi]
    D --> E[Yüzey İşlemi]
    E --> F[Miktar Belirleme]
    F --> G[Teklif Talep Formu]
    G --> H[Teslimat Bilgileri]
    H --> I[Anonim Teklif Oluştur]
    I --> J[Üreticilere Bildirim]
    J --> K[Teklifleri Bekle]
    K --> L[Teklif Karşılaştırma]
    L --> M[Teklif Seçimi]
    M --> N[Ön Ödeme]
    N --> O[İletişim Bilgileri Paylaşımı]
```

#### 7.1.3 Üretici Teklif Verme Akışı
```mermaid
flowchart TD
    A[Teklif Bildirimi] --> B[Teklif Detayları]
    B --> C[Ürün Uygunluğu Kontrolü]
    C --> D[Fiyat Hesaplama]
    D --> E[Teslimat Süresi]
    E --> F[Ödeme Koşulları]
    F --> G[Teklif Gönder]
    G --> H[Diğer Teklifleri İzle]
    H --> I{Teklif Seçildi mi?}
    I -->|Evet| J[Ön Ödeme Bildirimi]
    I -->|Hayır| K[Süre Doldu]
    J --> L[Müşteri İletişimi]
    K --> M[Teklif Sona Erdi]
```

### 7.2 Sayfa Wireframe'leri

#### 7.2.1 Ana Sayfa Wireframe
```
┌─────────────────────────────────────────────────────────────┐
│ [LOGO] Türkiye Doğal Taş Pazarı    [Giriş] [Kayıt] [TR/EN] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │              HERO SECTION                           │    │
│  │  "Türkiye'nin En Büyük Doğal Taş Pazarı"          │    │
│  │  [Ürün Ara] [Üretici Ol] [Teklif Al]              │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │              ARAMA FİLTRELERİ                       │    │
│  │  [Kategori ▼] [Renk ▼] [Boyut ▼] [Konum ▼] [ARA]  │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │              ÖNE ÇIKAN ÜRÜNLER                      │    │
│  │  [Ürün 1] [Ürün 2] [Ürün 3] [Ürün 4]             │    │
│  │  [3D Görünüm] [Fiyat] [Üretici]                   │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │              NASIL ÇALIŞIR                          │    │
│  │  1. Ürün Ara  2. Teklif Al  3. Karşılaştır       │    │
│  │  4. Seç       5. Öde        6. Teslim Al          │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ [Hakkımızda] [İletişim] [Yardım] [Gizlilik] [Şartlar]     │
└─────────────────────────────────────────────────────────────┘
```

#### 7.2.2 Ürün Detay Sayfası Wireframe
```
┌─────────────────────────────────────────────────────────────┐
│ [LOGO] > Mermer > Beyaz Mermer > Carrara Beyaz             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────────┐ ┌─────────────────────────────────┐ │
│ │                     │ │  CARRARA BEYAZ MERMER           │ │
│ │    3D GÖRÜNTÜ       │ │                                 │ │
│ │                     │ │  ⭐⭐⭐⭐⭐ (4.8) 127 değerlendirme │ │
│ │  [360° Döndür]      │ │                                 │ │
│ │  [AR Görüntüle]     │ │  📍 Afyon, Türkiye              │ │
│ │  [Yakınlaştır]      │ │  🏭 ABC Mermer Ltd.             │ │
│ │                     │ │                                 │ │
│ │  [Galeri] [Video]   │ │  💰 Fiyat: $45-65/m²           │ │
│ └─────────────────────┘ │                                 │ │
│                         │  [TEKLİF AL] [FAVORİLERE EKLE] │ │
│                         └─────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              ÜRÜN KONFİGÜRATÖRÜ                        │ │
│ │                                                         │ │
│ │  Ebat Seçimi:     [30x60] [60x60] [80x80] [Özel]      │ │
│ │  Kalınlık:        [2cm] [3cm] [4cm] [5cm]              │ │
│ │  Yüzey İşlemi:    [Ham] [Honlu] [Cilalı] [Fırçalı]    │ │
│ │  Miktar:          [___] m² (Min: 100m²)                │ │
│ │                                                         │ │
│ │  Toplam Fiyat: $4,500 (100m² x $45/m²)                │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              TEKNİK ÖZELLİKLER                          │ │
│ │  • Yoğunluk: 2.7 g/cm³                                 │ │
│ │  • Su Emme: %0.2                                       │ │
│ │  • Basınç Dayanımı: 120 MPa                            │ │
│ │  • Donma Direnci: Yüksek                               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ [Benzer Ürünler] [Üretici Profili] [Değerlendirmeler]     │
└─────────────────────────────────────────────────────────────┘
```

#### 7.2.3 Teklif Karşılaştırma Sayfası Wireframe
```
┌─────────────────────────────────────────────────────────────┐
│ Teklif Talebi #12345 - Carrara Beyaz Mermer (100m²)       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              GELEN TEKLİFLER (5 Adet)                   │ │
│ │                                                         │ │
│ │ ┌─────────┬─────────┬─────────┬─────────┬─────────────┐ │ │
│ │ │ Sıra    │ Fiyat   │ Teslimat│ Ödeme   │ Eylem       │ │ │
│ │ ├─────────┼─────────┼─────────┼─────────┼─────────────┤ │ │
│ │ │ 🥇 #1   │ $4,200  │ 15 gün  │ %30 ön  │ [SEÇ]       │ │ │
│ │ │ 🥈 #2   │ $4,350  │ 12 gün  │ %40 ön  │ [SEÇ]       │ │ │
│ │ │ 🥉 #3   │ $4,500  │ 20 gün  │ %30 ön  │ [SEÇ]       │ │ │
│ │ │    #4   │ $4,750  │ 10 gün  │ %50 ön  │ [SEÇ]       │ │ │
│ │ │    #5   │ $4,900  │ 25 gün  │ %30 ön  │ [SEÇ]       │ │ │
│ │ └─────────┴─────────┴─────────┴─────────┴─────────────┘ │ │
│ │                                                         │ │
│ │ ⏰ Kalan Süre: 18 saat 32 dakika                        │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              TALEP DETAYLARI                            │ │
│ │  • Ürün: Carrara Beyaz Mermer                          │ │
│ │  • Ebat: 60x60x2cm                                     │ │
│ │  • Yüzey: Cilalı                                       │ │
│ │  • Miktar: 100m²                                       │ │
│ │  • Teslimat: İstanbul, Türkiye                         │ │
│ │  • Tarih: 30 gün içinde                                │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              NOTLAR                                     │ │
│ │  • Tüm fiyatlar FOB (Free on Board) bazında            │ │
│ │  • Ön ödeme sonrası üretici bilgileri paylaşılacak     │ │
│ │  • Kalite garantisi tüm tekliflerde geçerli            │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ [Yeni Teklif Talebi] [Geçmiş Talepler] [Yardım]           │
└─────────────────────────────────────────────────────────────┘
```

### 7.3 Mobil Wireframe'ler

#### 7.3.1 Mobil Ana Sayfa
```
┌─────────────────────┐
│ ☰ [LOGO]    🔍 👤   │
├─────────────────────┤
│                     │
│   🏛️ DOĞAL TAŞ      │
│     PAZARI          │
│                     │
│  [Ürün Ara]        │
│  [Teklif Al]        │
│                     │
├─────────────────────┤
│ Kategori Seç:       │
│ [Mermer] [Granit]   │
│ [Traverten] [Oniks] │
├─────────────────────┤
│                     │
│ 📱 Öne Çıkan        │
│                     │
│ ┌─────────────────┐ │
│ │ [Ürün Resmi]    │ │
│ │ Carrara Mermer  │ │
│ │ $45/m²          │ │
│ │ [3D Gör] [❤️]   │ │
│ └─────────────────┘ │
│                     │
│ ┌─────────────────┐ │
│ │ [Ürün Resmi]    │ │
│ │ Granit Siyah    │ │
│ │ $35/m²          │ │
│ │ [3D Gör] [❤️]   │ │
│ └─────────────────┘ │
│                     │
├─────────────────────┤
│ [Ana] [Arama] [❤️]  │
│ [Hesap] [Daha]      │
└─────────────────────┘
```

#### 7.3.2 Mobil Ürün Detay
```
┌─────────────────────┐
│ ← Carrara Mermer ⋮  │
├─────────────────────┤
│                     │
│ ┌─────────────────┐ │
│ │                 │ │
│ │   3D GÖRÜNÜM    │ │
│ │                 │ │
│ │ [360°] [AR] [📷] │ │
│ └─────────────────┘ │
│                     │
│ Carrara Beyaz       │
│ ⭐⭐⭐⭐⭐ (4.8)        │
│ 📍 Afyon, TR        │
│                     │
│ 💰 $45-65/m²        │
│                     │
│ [TEKLİF AL]         │
│ [❤️ Favorile]        │
│                     │
├─────────────────────┤
│ Ebat: [60x60 ▼]     │
│ Kalınlık: [2cm ▼]   │
│ Yüzey: [Cilalı ▼]   │
│ Miktar: [100] m²    │
│                     │
│ Toplam: $4,500      │
├─────────────────────┤
│ [Özellikler]        │
│ [Değerlendirmeler]  │
│ [Üretici]           │
└─────────────────────┘
```

### 7.4 Dashboard Wireframe'leri

#### 7.4.1 Üretici Dashboard
```
┌─────────────────────────────────────────────────────────────┐
│ Hoş Geldiniz, ABC Mermer Ltd. | Son Giriş: 2 saat önce     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              ÖZET İSTATİSTİKLER                         │ │
│ │                                                         │ │
│ │ ┌─────────┬─────────┬─────────┬─────────┬─────────────┐ │ │
│ │ │ Aktif   │ Bekleyen│ Toplam  │ Bu Ay   │ Gelir       │ │ │
│ │ │ Teklif  │ Teklif  │ Sipariş │ Satış   │ (Bu Ay)     │ │ │
│ │ ├─────────┼─────────┼─────────┼─────────┼─────────────┤ │ │
│ │ │   12    │    5    │   48    │ $45,000 │ $125,000    │ │ │
│ │ └─────────┴─────────┴─────────┴─────────┴─────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              YENİ TEKLİF TALEPLERİ                      │ │
│ │                                                         │ │
│ │ 🔔 #12345 - Carrara Mermer (100m²) - 18 saat kaldı     │ │
│ │    📍 İstanbul | 💰 Beklenen: $4,000-5,000 [TEKLİF VER]│ │
│ │                                                         │ │
│ │ 🔔 #12346 - Granit Siyah (50m²) - 2 gün kaldı         │ │
│ │    📍 Ankara | 💰 Beklenen: $1,500-2,000 [TEKLİF VER] │ │
│ │                                                         │ │
│ │ 🔔 #12347 - Traverten (200m²) - 3 gün kaldı           │ │
│ │    📍 İzmir | 💰 Beklenen: $6,000-8,000 [TEKLİF VER]  │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              AKTİF SİPARİŞLER                           │ │
│ │                                                         │ │
│ │ 📦 #ORD-001 - Beyaz Mermer | Üretimde | %60 Tamamlandı │ │
│ │ 📦 #ORD-002 - Granit Gri | Paketleme | %90 Tamamlandı  │ │
│ │ 📦 #ORD-003 - Traverten | Sevkiyat | %100 Hazır        │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ [Ürünlerim] [Tekliflerim] [Siparişlerim] [Raporlar] [Ayar] │
└─────────────────────────────────────────────────────────────┘
```

#### 7.4.2 Müşteri Dashboard
```
┌─────────────────────────────────────────────────────────────┐
│ Hoş Geldiniz, John Smith | Son Giriş: 1 gün önce          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              ÖZET İSTATİSTİKLER                         │ │
│ │                                                         │ │
│ │ ┌─────────┬─────────┬─────────┬─────────┬─────────────┐ │ │
│ │ │ Aktif   │ Bekleyen│ Toplam  │ Bu Yıl  │ Harcama     │ │ │
│ │ │ Teklif  │ Teklif  │ Sipariş │ Sipariş │ (Bu Yıl)    │ │ │
│ │ ├─────────┼─────────┼─────────┼─────────┼─────────────┤ │ │
│ │ │    3    │    2    │   15    │   28    │ $85,000     │ │ │
│ │ └─────────┴─────────┴─────────┴─────────┴─────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              AKTİF TEKLİF TALEPLERİM                    │ │
│ │                                                         │ │
│ │ 📋 #12345 - Carrara Mermer (100m²)                     │ │
│ │    5 Teklif Geldi | En Düşük: $4,200 | 18 saat kaldı  │ │
│ │    [TEKLİFLERİ GÖRÜNTÜLE] [SEÇ VE DEVAM ET]            │ │
│ │                                                         │ │
│ │ 📋 #12348 - Granit Siyah (75m²)                        │ │
│ │    2 Teklif Geldi | En Düşük: $2,100 | 2 gün kaldı    │ │
│ │    [TEKLİFLERİ GÖRÜNTÜLE]                              │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              SİPARİŞ TAKİBİ                             │ │
│ │                                                         │ │
│ │ 🚚 #ORD-001 - Beyaz Mermer | Transit | İstanbul'a 2 gün│ │
│ │ 📦 #ORD-002 - Traverten | Paketleme | 3 gün içinde     │ │
│ │ ⏳ #ORD-003 - Granit | Üretim | 10 gün içinde          │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              HIZLI ERİŞİM                               │ │
│ │                                                         │ │
│ │ [🔍 Yeni Ürün Ara] [📋 Teklif Talebi Oluştur]         │ │
│ │ [❤️ Favorilerim] [📊 Satın Alma Geçmişi]               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ [Tekliflerim] [Siparişlerim] [Favoriler] [Hesabım] [Destek]│
└─────────────────────────────────────────────────────────────┘
```

## 8. İcon Sistemi ve Görsel Elemanlar

### 8.1 İcon Kütüphanesi
- **Birincil**: Lucide React (modern, minimal)
- **İkincil**: Heroicons (Tailwind ekosistemi)
- **Özel**: Doğal taş sektörüne özel custom iconlar

### 8.2 İcon Boyutları
```css
.icon-xs { width: 12px; height: 12px; }
.icon-sm { width: 16px; height: 16px; }
.icon-md { width: 20px; height: 20px; }
.icon-lg { width: 24px; height: 24px; }
.icon-xl { width: 32px; height: 32px; }
.icon-2xl { width: 48px; height: 48px; }
```

### 8.3 Sektörel İconlar
```typescript
// Doğal Taş Sektörü İconları
const StoneIcons = {
  marble: '🏛️',        // Mermer
  granite: '⬛',        // Granit
  travertine: '🟫',    // Traverten
  onyx: '💎',          // Oniks
  limestone: '🪨',     // Kireçtaşı
  quarry: '⛏️',        // Ocak
  factory: '🏭',       // Fabrika
  certificate: '📜',   // Sertifika
  viewer3d: '🔄',      // 3D Görüntüleme
  ar: '📱',            // AR Deneyimi
  measurement: '📏',   // Ölçüm
  surface: '✨',       // Yüzey İşlemi
  shipping: '🚚',      // Kargo
  quality: '✅'        // Kalite
};
```

### 8.4 Durum İconları
```css
/* Durum Göstergeleri */
.status-pending { color: var(--warning); }
.status-approved { color: var(--success); }
.status-rejected { color: var(--error); }
.status-processing { color: var(--info); }

/* Animasyonlu Durum İconları */
.status-loading {
  animation: spin 1s linear infinite;
}

.status-pulse {
  animation: pulse 2s ease-in-out infinite;
}
```

## 9. Animasyon ve Geçişler

### 9.1 Temel Geçişler
```css
.transition-base {
  transition: all 0.2s ease;
}

.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

.transition-opacity {
  transition: opacity 0.3s ease;
}
```

### 9.2 Hover Efektleri
```css
.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgb(139 115 85 / 0.3);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}
```

### 9.3 Loading Animasyonları
```css
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(-25%); animation-timing-function: cubic-bezier(0.8,0,1,1); }
  50% { transform: none; animation-timing-function: cubic-bezier(0,0,0.2,1); }
}

.animate-bounce {
  animation: bounce 1s infinite;
}
```

### 9.4 Sayfa Geçiş Animasyonları
```css
/* Sayfa Giriş Animasyonu */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-enter {
  animation: fadeInUp 0.5s ease-out;
}

/* Modal Animasyonları */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-enter {
  animation: modalFadeIn 0.3s ease-out;
}
```

## 10. Erişilebilirlik (Accessibility)

### 10.1 WCAG 2.1 AA Uyumluluğu

#### 10.1.1 Renk Kontrastı
```css
/* Minimum Kontrast Oranları */
/* Normal metin: 4.5:1 */
/* Büyük metin (18pt+): 3:1 */
/* UI bileşenleri: 3:1 */

/* Kontrast Kontrollü Renkler */
.text-high-contrast {
  color: var(--gray-900);
  background: var(--gray-50);
}

.text-medium-contrast {
  color: var(--gray-700);
  background: var(--gray-100);
}
```

#### 10.1.2 Keyboard Navigation
```css
.focus-visible {
  outline: 2px solid var(--primary-stone);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-stone);
  color: white;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 4px;
  font-weight: var(--font-medium);
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Tab Order Indicators */
.tab-indicator {
  position: relative;
}

.tab-indicator::after {
  content: attr(data-tab-order);
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--primary-stone);
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
}

.tab-indicator:focus::after {
  opacity: 1;
}
```

#### 10.1.3 Screen Reader Desteği
```html
<!-- Semantic HTML Örnekleri -->
<main role="main" aria-labelledby="main-heading">
  <h1 id="main-heading">Doğal Taş Ürünleri</h1>

  <section aria-labelledby="products-heading">
    <h2 id="products-heading">Öne Çıkan Ürünler</h2>

    <article aria-labelledby="product-1-title">
      <h3 id="product-1-title">Carrara Beyaz Mermer</h3>
      <img src="..." alt="Carrara beyaz mermer, cilalı yüzey işlemi, 60x60cm ebat">
      <p aria-describedby="product-1-price">Yüksek kaliteli İtalyan mermeri</p>
      <span id="product-1-price" aria-label="Fiyat metre kare başına 45 dolar">$45/m²</span>
    </article>
  </section>
</main>

<!-- ARIA Etiketleri -->
<button
  aria-expanded="false"
  aria-controls="filter-menu"
  aria-describedby="filter-help"
>
  Filtreler
</button>

<div
  id="filter-menu"
  aria-hidden="true"
  role="menu"
  aria-labelledby="filter-button"
>
  <!-- Menü içeriği -->
</div>

<span id="filter-help" class="sr-only">
  Ürünleri kategoriye göre filtrelemek için bu menüyü kullanın
</span>
```

### 10.2 Çok Dilli Erişilebilirlik
```css
/* RTL (Right-to-Left) Dil Desteği */
[dir="rtl"] .text-left { text-align: right; }
[dir="rtl"] .text-right { text-align: left; }
[dir="rtl"] .ml-4 { margin-left: 0; margin-right: 1rem; }
[dir="rtl"] .mr-4 { margin-right: 0; margin-left: 1rem; }

/* Font Boyutu Ölçeklendirme */
.text-scalable {
  font-size: clamp(0.875rem, 2.5vw, 1.125rem);
}

/* Kültürel Renk Algısı */
.success-global {
  /* Yeşil çoğu kültürde olumlu */
  background-color: var(--success);
}

.warning-cultural {
  /* Sarı bazı kültürlerde farklı anlamlar taşıyabilir */
  background-color: var(--warning);
}
```

## 11. Performans Optimizasyonu

### 11.1 CSS Optimizasyonu
```css
/* Critical CSS - Above the fold */
.critical {
  /* Sayfa yüklenirken görünen alanlar için kritik stiller */
  display: block;
  visibility: visible;
}

/* Non-critical CSS - Lazy loaded */
.non-critical {
  /* Sayfa kaydırıldığında görünen alanlar için */
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* CSS Containment */
.product-card {
  contain: layout style paint;
}

/* GPU Acceleration */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}
```

### 11.2 Font Optimizasyonu
```css
/* Font Display Optimization */
@font-face {
  font-family: 'Inter';
  font-display: swap;
  src: url('/fonts/inter-var.woff2') format('woff2-variations');
  font-weight: 100 900;
}

/* Preload Critical Fonts */
/* HTML Head'de: */
/* <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin> */

/* Font Subset Loading */
@font-face {
  font-family: 'Inter-Latin';
  src: url('/fonts/inter-latin.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153;
}
```

### 11.3 Image ve Media Optimizasyonu
```css
/* Responsive Images */
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  loading: lazy;
}

/* Progressive Enhancement */
.webp-support .image {
  background-image: url('image.webp');
}

.no-webp .image {
  background-image: url('image.jpg');
}

/* Aspect Ratio Containers */
.aspect-ratio-16-9 {
  aspect-ratio: 16 / 9;
}

.aspect-ratio-1-1 {
  aspect-ratio: 1 / 1;
}
```

## 12. Dark Mode Desteği

### 12.1 Dark Mode Renk Paleti
```css
/* Light Mode (Default) */
:root {
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --border-primary: #E5E7EB;
}

/* Dark Mode */
[data-theme="dark"] {
  --bg-primary: #111827;
  --bg-secondary: #1F2937;
  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --border-primary: #374151;

  /* Doğal Taş Renkleri - Dark Mode */
  --primary-stone: #A68B5B;
  --primary-dark: #8B7355;
  --primary-light: #C4A373;

  --secondary-marble: #2D2D2D;
  --secondary-granite: #1A1A1A;
  --secondary-travertine: #3D3D3D;
}

/* Sistem Tercihi */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #111827;
    --bg-secondary: #1F2937;
    --text-primary: #F9FAFB;
    --text-secondary: #D1D5DB;
  }
}
```

### 12.2 Dark Mode Geçiş Animasyonu
```css
* {
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

/* Dark Mode Toggle Button */
.theme-toggle {
  position: relative;
  width: 60px;
  height: 30px;
  background: var(--gray-200);
  border-radius: 15px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.theme-toggle::after {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

[data-theme="dark"] .theme-toggle {
  background: var(--primary-stone);
}

[data-theme="dark"] .theme-toggle::after {
  transform: translateX(30px);
}
```

## 13. Özel Sektörel Tasarım Elemanları

### 13.1 Doğal Taş Texture'ları ve Materyaller
```css
/* Mermer Texture */
.marble-texture {
  background: linear-gradient(45deg,
    #F5F5DC 0%,
    #FFFFFF 25%,
    #F0F0F0 50%,
    #F5F5DC 75%,
    #FFFFFF 100%);
  background-size: 200px 200px;
  position: relative;
}

.marble-texture::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(200,200,200,0.3) 1px, transparent 1px),
    radial-gradient(circle at 80% 70%, rgba(180,180,180,0.2) 1px, transparent 1px);
  background-size: 50px 50px, 75px 75px;
}

/* Granit Texture */
.granite-texture {
  background: #2F4F4F;
  background-image:
    radial-gradient(circle at 25% 25%, #4A4A4A 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, #1A1A1A 1px, transparent 1px),
    radial-gradient(circle at 50% 50%, #3A3A3A 0.5px, transparent 0.5px);
  background-size: 20px 20px, 30px 30px, 15px 15px;
}

/* Traverten Texture */
.travertine-texture {
  background: #DEB887;
  background-image:
    linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px),
    linear-gradient(rgba(0,0,0,0.05) 1px, transparent 1px);
  background-size: 40px 40px, 20px 20px;
}

/* Oniks Şeffaflık Efekti */
.onyx-effect {
  background: linear-gradient(45deg,
    rgba(255,255,255,0.9) 0%,
    rgba(255,255,255,0.7) 50%,
    rgba(255,255,255,0.9) 100%);
  backdrop-filter: blur(2px);
  position: relative;
}

.onyx-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    transparent 0%,
    rgba(255,255,255,0.3) 50%,
    transparent 100%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}
```

### 13.2 3D Görüntüleme UI Bileşenleri
```css
/* 3D Viewer Container */
.viewer-3d-container {
  position: relative;
  width: 100%;
  height: 400px;
  background: var(--gray-100);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 2px solid var(--gray-200);
}

/* 3D Kontrol Paneli */
.viewer-3d-controls {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--space-2);
  background: rgba(0, 0, 0, 0.7);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.viewer-3d-button {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-md);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.viewer-3d-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.viewer-3d-button.active {
  background: var(--primary-stone);
  border-color: var(--primary-light);
}

/* Zoom Kontrolleri */
.zoom-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.zoom-button {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.zoom-button:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}
```

### 13.3 Teklif Sistemi UI Bileşenleri
```css
/* Anonim Teklif Kartı */
.anonymous-bid-card {
  background: white;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  position: relative;
  transition: all 0.3s ease;
}

.anonymous-bid-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg,
    var(--primary-stone) 0%,
    var(--primary-light) 100%);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.anonymous-bid-card.winning {
  border-color: var(--success);
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.2);
}

.anonymous-bid-card.winning::before {
  background: var(--success);
}

/* Fiyat Karşılaştırma Tablosu */
.price-comparison-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.price-comparison-table th {
  background: var(--primary-stone);
  color: white;
  padding: var(--space-4);
  text-align: left;
  font-weight: var(--font-semibold);
}

.price-comparison-table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--gray-200);
}

.price-comparison-table tr:hover {
  background: var(--gray-50);
}

.price-comparison-table .rank-1 {
  background: rgba(34, 197, 94, 0.1);
  border-left: 4px solid var(--success);
}

.price-comparison-table .rank-2 {
  background: rgba(245, 158, 11, 0.1);
  border-left: 4px solid var(--warning);
}

.price-comparison-table .rank-3 {
  background: rgba(239, 68, 68, 0.1);
  border-left: 4px solid var(--error);
}

/* Geri Sayım Timer */
.countdown-timer {
  display: flex;
  gap: var(--space-4);
  align-items: center;
  justify-content: center;
  background: var(--gray-900);
  color: white;
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  font-family: var(--font-mono);
}

.countdown-unit {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}

.countdown-number {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  line-height: 1;
}

.countdown-label {
  font-size: var(--text-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.7;
}

.countdown-separator {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  opacity: 0.5;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 0.5; }
  51%, 100% { opacity: 0; }
}

/* Durum Göstergeleri */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.status-indicator.pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-indicator.approved {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-indicator.rejected {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-indicator.processing {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.status-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.status-indicator.processing::before {
  animation: pulse 2s ease-in-out infinite;
}
```

## 14. Responsive Tasarım Detayları

### 14.1 Mobil Optimizasyonu (320px - 767px)

#### 14.1.1 Mobil Navigation
```css
/* Mobil Hamburger Menü */
.mobile-nav {
  display: none;
}

@media (max-width: 767px) {
  .desktop-nav { display: none; }
  .mobile-nav { display: block; }

  .hamburger-menu {
    position: fixed;
    top: 0;
    left: -100%;
    width: 280px;
    height: 100vh;
    background: white;
    z-index: 1000;
    transition: left 0.3s ease;
    box-shadow: var(--shadow-xl);
  }

  .hamburger-menu.open {
    left: 0;
  }

  .hamburger-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .hamburger-overlay.open {
    opacity: 1;
    visibility: visible;
  }
}
```

#### 14.1.2 Mobil Ürün Kartları
```css
@media (max-width: 767px) {
  .product-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .product-card {
    margin: 0 var(--space-4);
  }

  .product-card-image {
    height: 200px;
  }

  .product-card-content {
    padding: var(--space-4);
  }

  .product-card-title {
    font-size: var(--text-base);
  }

  .product-card-price {
    font-size: var(--text-lg);
  }
}
```

#### 14.1.3 Mobil Form Elemanları
```css
@media (max-width: 767px) {
  .form-group {
    margin-bottom: var(--space-4);
  }

  .input, .select, .textarea {
    font-size: 16px; /* iOS zoom önleme */
    padding: var(--space-4);
  }

  .button {
    width: 100%;
    padding: var(--space-4);
    font-size: var(--text-base);
  }

  .button-group {
    flex-direction: column;
    gap: var(--space-2);
  }

  .button-group .button {
    width: 100%;
  }
}
```

### 14.2 Tablet Optimizasyonu (768px - 1023px)

#### 14.2.1 Tablet Layout
```css
@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    max-width: 768px;
    padding: 0 var(--space-6);
  }

  .product-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }

  .hero-section {
    padding: var(--space-16) 0;
  }

  .hero-title {
    font-size: var(--text-4xl);
  }

  .hero-subtitle {
    font-size: var(--text-lg);
  }
}
```

#### 14.2.2 Tablet Sidebar
```css
@media (min-width: 768px) and (max-width: 1023px) {
  .sidebar {
    width: 250px;
    position: fixed;
    left: -250px;
    top: 0;
    height: 100vh;
    background: white;
    z-index: 100;
    transition: left 0.3s ease;
    box-shadow: var(--shadow-lg);
  }

  .sidebar.open {
    left: 0;
  }

  .sidebar-toggle {
    display: block;
    position: fixed;
    top: var(--space-4);
    left: var(--space-4);
    z-index: 101;
    background: var(--primary-stone);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-2);
    cursor: pointer;
  }
}
```

### 14.3 Desktop Optimizasyonu (1024px+)

#### 14.3.1 Desktop Layout
```css
@media (min-width: 1024px) {
  .container {
    max-width: 1200px;
    padding: 0 var(--space-8);
  }

  .product-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-8);
  }

  .hero-section {
    padding: var(--space-24) 0;
  }

  .hero-title {
    font-size: var(--text-6xl);
  }

  .hero-subtitle {
    font-size: var(--text-xl);
  }
}
```

#### 14.3.2 Desktop Hover Efektleri
```css
@media (min-width: 1024px) {
  .product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
  }

  .button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .navbar-link:hover {
    color: var(--primary-stone);
  }

  .navbar-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-stone);
    transition: width 0.3s ease;
  }

  .navbar-link:hover::after {
    width: 100%;
  }
}
```

### 14.4 Ultra-wide Desktop (1536px+)
```css
@media (min-width: 1536px) {
  .container {
    max-width: 1400px;
  }

  .product-grid {
    grid-template-columns: repeat(5, 1fr);
  }

  .hero-section {
    padding: var(--space-32) 0;
  }

  .sidebar {
    width: 300px;
  }

  .main-content {
    margin-left: 300px;
  }
}
```

## 15. Sonuç ve Uygulama Rehberi

### 15.1 Tasarım Sistemi Uygulama Adımları

1. **CSS Custom Properties Kurulumu**
   - Tüm renk, spacing ve tipografi değişkenlerini tanımla
   - Dark mode desteği için tema değişkenleri oluştur

2. **Bileşen Kütüphanesi Geliştirme**
   - React bileşenlerini tasarım sistemine göre oluştur
   - Storybook ile dokümantasyon hazırla

3. **Responsive Grid Sistemi**
   - CSS Grid ve Flexbox kombinasyonu kullan
   - Mobile-first yaklaşımı benimse

4. **Erişilebilirlik Testleri**
   - WCAG 2.1 AA standartlarına uygunluk kontrol et
   - Screen reader testleri yap

5. **Performans Optimizasyonu**
   - Critical CSS inline yükleme
   - Font ve image optimizasyonu
   - CSS minification

### 15.2 Kalite Kontrol Checklist

- [ ] Renk kontrast oranları WCAG uyumlu
- [ ] Keyboard navigation çalışıyor
- [ ] Screen reader uyumluluğu test edildi
- [ ] Mobil dokunmatik hedefler 44px+
- [ ] Font boyutları ölçeklenebilir
- [ ] Dark mode geçişleri sorunsuz
- [ ] Loading durumları tanımlandı
- [ ] Error durumları tasarlandı
- [ ] Responsive breakpoint'ler test edildi
- [ ] Cross-browser uyumluluk kontrol edildi

### 15.3 Gelecek Geliştirmeler

1. **Gelişmiş Animasyonlar**
   - Micro-interactions
   - Page transitions
   - Loading animations

2. **AR/VR UI Elemanları**
   - 3D interface bileşenleri
   - Spatial design patterns
   - Gesture controls

3. **AI Destekli Tasarım**
   - Adaptive layouts
   - Personalized themes
   - Smart content organization

---

**Doküman Versiyonu**: 1.0
**Son Güncelleme**: 2025-06-28
**Hazırlayan**: Augment Agent
**Onay**: Bekliyor
