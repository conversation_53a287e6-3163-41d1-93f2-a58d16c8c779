'use client';

import React from 'react';
import { CheckCircleIcon } from '@heroicons/react/24/outline';

interface CompletedRequestsPageProps {
  onNavigate?: (route: string) => void;
}

const CompletedRequestsPage: React.FC<CompletedRequestsPageProps> = ({ onNavigate }) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tamamlanan Talepler</h1>
          <p className="text-gray-600 mt-1">Kabul edilen ve reddedilen talepleriniz</p>
        </div>
      </div>

      {/* Empty State */}
      <div className="text-center py-12">
        <CheckCircleIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Tamamlanan talebiniz bulunmuyor
        </h3>
        <p className="text-gray-600 mb-6">
          Tamamladığınız talepler burada görünecek.
        </p>
        <button
          onClick={() => onNavigate?.('/customer/requests')}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Aktif Taleplere Dön
        </button>
      </div>
    </div>
  );
};

export default CompletedRequestsPage;
