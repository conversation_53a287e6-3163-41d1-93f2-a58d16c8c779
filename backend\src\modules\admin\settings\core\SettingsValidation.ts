// Settings Validation Service - RFC-012
import { 
  SettingsSchema, 
  SettingValidationRule, 
  SettingsValidationError,
  SettingsCategory,
  SettingValue 
} from '../types/settings.types';

export class SettingsValidation {
  private schema: SettingsSchema;

  constructor() {
    this.schema = this.buildSchema();
  }

  /**
   * Validate a setting value
   */
  async validateSetting(category: SettingsCategory, key: string, value: SettingValue): Promise<void> {
    const categorySchema = this.schema[category];
    if (!categorySchema) {
      throw new SettingsValidationError(
        `${category}.${key}`,
        value,
        'category',
        `Invalid category: ${category}`
      );
    }

    const fieldSchema = categorySchema[key];
    if (!fieldSchema) {
      throw new SettingsValidationError(
        `${category}.${key}`,
        value,
        'field',
        `Invalid field: ${key} in category ${category}`
      );
    }

    // Required check
    if (fieldSchema.required && (value === null || value === undefined || value === '')) {
      throw new SettingsValidationError(
        `${category}.${key}`,
        value,
        'required',
        `Field ${key} is required`
      );
    }

    // Type check
    if (!this.validateType(value, fieldSchema.type)) {
      throw new SettingsValidationError(
        `${category}.${key}`,
        value,
        'type',
        `Field ${key} must be of type ${fieldSchema.type}`
      );
    }

    // String validations
    if (fieldSchema.type === 'string' && typeof value === 'string') {
      if (fieldSchema.minLength && value.length < fieldSchema.minLength) {
        throw new SettingsValidationError(
          `${category}.${key}`,
          value,
          'minLength',
          `Field ${key} must be at least ${fieldSchema.minLength} characters long`
        );
      }

      if (fieldSchema.maxLength && value.length > fieldSchema.maxLength) {
        throw new SettingsValidationError(
          `${category}.${key}`,
          value,
          'maxLength',
          `Field ${key} must be at most ${fieldSchema.maxLength} characters long`
        );
      }

      if (fieldSchema.pattern) {
        const regex = new RegExp(fieldSchema.pattern);
        if (!regex.test(value)) {
          throw new SettingsValidationError(
            `${category}.${key}`,
            value,
            'pattern',
            `Field ${key} does not match required pattern`
          );
        }
      }
    }

    // Number validations
    if (fieldSchema.type === 'number' && typeof value === 'number') {
      if (fieldSchema.min !== undefined && value < fieldSchema.min) {
        throw new SettingsValidationError(
          `${category}.${key}`,
          value,
          'min',
          `Field ${key} must be at least ${fieldSchema.min}`
        );
      }

      if (fieldSchema.max !== undefined && value > fieldSchema.max) {
        throw new SettingsValidationError(
          `${category}.${key}`,
          value,
          'max',
          `Field ${key} must be at most ${fieldSchema.max}`
        );
      }
    }

    // Enum validation
    if (fieldSchema.enum && !fieldSchema.enum.includes(value)) {
      throw new SettingsValidationError(
        `${category}.${key}`,
        value,
        'enum',
        `Field ${key} must be one of: ${fieldSchema.enum.join(', ')}`
      );
    }

    // Custom validation
    if (fieldSchema.custom) {
      const result = fieldSchema.custom(value);
      if (result !== true) {
        throw new SettingsValidationError(
          `${category}.${key}`,
          value,
          'custom',
          typeof result === 'string' ? result : `Field ${key} failed custom validation`
        );
      }
    }
  }

  /**
   * Validate type
   */
  private validateType(value: any, type: string): boolean {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'json':
        return typeof value === 'object' && value !== null;
      case 'array':
        return Array.isArray(value);
      default:
        return false;
    }
  }

  /**
   * Get validation schema
   */
  getSchema(): SettingsSchema {
    return this.schema;
  }

  /**
   * Build validation schema
   */
  private buildSchema(): SettingsSchema {
    return {
      platform: {
        siteName: {
          type: 'string',
          required: true,
          minLength: 3,
          maxLength: 100,
          description: 'Site adı',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 'Doğal Taş Pazaryeri'
        },
        siteDescription: {
          type: 'string',
          required: false,
          maxLength: 500,
          description: 'Site açıklaması',
          sensitive: false,
          requiresRestart: false,
          defaultValue: ''
        },
        logoUrl: {
          type: 'string',
          required: false,
          pattern: '^https?://.+\\.(jpg|jpeg|png|gif|svg)$',
          description: 'Logo URL\'si',
          sensitive: false,
          requiresRestart: false,
          defaultValue: ''
        },
        maintenanceMode: {
          type: 'boolean',
          required: true,
          description: 'Bakım modu durumu',
          sensitive: false,
          requiresRestart: false,
          defaultValue: false
        },
        maintenanceMessage: {
          type: 'string',
          required: false,
          maxLength: 1000,
          description: 'Bakım modu mesajı',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 'Site bakımda. Lütfen daha sonra tekrar deneyin.'
        },
        defaultLanguage: {
          type: 'string',
          required: true,
          enum: ['tr', 'en', 'ar', 'de', 'fr', 'es', 'it', 'ru', 'zh', 'ja'],
          description: 'Varsayılan dil',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 'tr'
        },
        timezone: {
          type: 'string',
          required: true,
          description: 'Zaman dilimi',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 'Europe/Istanbul'
        }
      },
      security: {
        passwordMinLength: {
          type: 'number',
          required: true,
          min: 6,
          max: 50,
          description: 'Minimum şifre uzunluğu',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 8
        },
        passwordRequireUppercase: {
          type: 'boolean',
          required: true,
          description: 'Şifrede büyük harf zorunluluğu',
          sensitive: false,
          requiresRestart: false,
          defaultValue: true
        },
        passwordRequireNumbers: {
          type: 'boolean',
          required: true,
          description: 'Şifrede rakam zorunluluğu',
          sensitive: false,
          requiresRestart: false,
          defaultValue: true
        },
        sessionTimeout: {
          type: 'number',
          required: true,
          min: 300,
          max: 86400,
          description: 'Session timeout süresi (saniye)',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 3600
        },
        require2FA: {
          type: 'boolean',
          required: true,
          description: '2FA zorunluluğu',
          sensitive: false,
          requiresRestart: false,
          defaultValue: false
        },
        loginAttemptLimit: {
          type: 'number',
          required: true,
          min: 3,
          max: 20,
          description: 'Maksimum giriş deneme sayısı',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 5
        }
      },
      business: {
        commissionRateM2: {
          type: 'number',
          required: true,
          min: 0,
          max: 100,
          description: 'M² başına komisyon oranı ($)',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 1.0
        },
        commissionRateTon: {
          type: 'number',
          required: true,
          min: 0,
          max: 1000,
          description: 'Ton başına komisyon oranı ($)',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 10.0
        },
        upfrontPaymentPercentage: {
          type: 'number',
          required: true,
          min: 10,
          max: 100,
          description: 'Ön ödeme yüzdesi',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 30
        },
        quoteValidityDays: {
          type: 'number',
          required: true,
          min: 1,
          max: 90,
          description: 'Teklif geçerlilik süresi (gün)',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 7
        }
      },
      notification: {
        emailEnabled: {
          type: 'boolean',
          required: true,
          description: 'Email bildirimleri aktif',
          sensitive: false,
          requiresRestart: false,
          defaultValue: true
        },
        smtpHost: {
          type: 'string',
          required: false,
          description: 'SMTP sunucu adresi',
          sensitive: true,
          requiresRestart: false,
          defaultValue: ''
        },
        smtpPort: {
          type: 'number',
          required: false,
          min: 1,
          max: 65535,
          description: 'SMTP port numarası',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 587
        },
        smsEnabled: {
          type: 'boolean',
          required: true,
          description: 'SMS bildirimleri aktif',
          sensitive: false,
          requiresRestart: false,
          defaultValue: false
        }
      },
      system: {
        cacheTimeout: {
          type: 'number',
          required: true,
          min: 60,
          max: 86400,
          description: 'Cache timeout süresi (saniye)',
          sensitive: false,
          requiresRestart: true,
          defaultValue: 3600
        },
        logLevel: {
          type: 'string',
          required: true,
          enum: ['debug', 'info', 'warn', 'error'],
          description: 'Log seviyesi',
          sensitive: false,
          requiresRestart: true,
          defaultValue: 'info'
        },
        maxFileUploadSize: {
          type: 'number',
          required: true,
          min: 1,
          max: 100,
          description: 'Maksimum dosya yükleme boyutu (MB)',
          sensitive: false,
          requiresRestart: false,
          defaultValue: 10
        }
      },
      integration: {
        stripeEnabled: {
          type: 'boolean',
          required: true,
          description: 'Stripe entegrasyonu aktif',
          sensitive: false,
          requiresRestart: false,
          defaultValue: false
        },
        googleMapsApiKey: {
          type: 'string',
          required: false,
          description: 'Google Maps API anahtarı',
          sensitive: true,
          requiresRestart: false,
          defaultValue: ''
        },
        chatbotEnabled: {
          type: 'boolean',
          required: true,
          description: 'AI Chatbot aktif',
          sensitive: false,
          requiresRestart: false,
          defaultValue: false
        }
      }
    };
  }
}
