/**
 * Asset Manager Service
 * Handles 3D asset upload, processing, and optimization
 */

import { PrismaClient } from '@prisma/client';
import { promises as fs } from 'fs';
import path from 'path';
import sharp from 'sharp';
import { 
  Asset3D, 
  AssetUploadRequest, 
  AssetProcessingOptions, 
  ModelOptimizationResult,
  TextureOptimizationResult,
  AssetType,
  AssetFormat,
  AssetQuality,
  ProcessingStatus
} from '../types';

export class AssetManager {
  private prisma: PrismaClient;
  private uploadDir: string;
  private processedDir: string;

  constructor() {
    this.prisma = new PrismaClient();
    this.uploadDir = process.env.ASSET_UPLOAD_DIR || './uploads/3d';
    this.processedDir = process.env.ASSET_PROCESSED_DIR || './uploads/3d/processed';
    this.ensureDirectories();
  }

  /**
   * Ensure upload directories exist
   */
  private async ensureDirectories(): Promise<void> {
    try {
      await fs.mkdir(this.uploadDir, { recursive: true });
      await fs.mkdir(this.processedDir, { recursive: true });
      await fs.mkdir(path.join(this.processedDir, 'models'), { recursive: true });
      await fs.mkdir(path.join(this.processedDir, 'textures'), { recursive: true });
      await fs.mkdir(path.join(this.processedDir, 'thumbnails'), { recursive: true });
    } catch (error) {
      console.error('Error creating directories:', error);
    }
  }

  /**
   * Upload and process 3D asset
   */
  async uploadAsset(
    file: Express.Multer.File,
    request: AssetUploadRequest,
    options: AssetProcessingOptions = {
      generateVariants: true,
      qualities: [AssetQuality.LOW, AssetQuality.MEDIUM, AssetQuality.HIGH],
      optimizeForWeb: true,
      generateThumbnails: true
    }
  ): Promise<Asset3D> {
    try {
      // Create asset record
      const asset = await this.prisma.asset3D.create({
        data: {
          productId: request.productId,
          name: request.name,
          description: request.description,
          type: request.type,
          format: this.getFormatFromFile(file),
          quality: AssetQuality.HIGH, // Original quality
          originalFileName: file.originalname,
          fileName: file.filename,
          filePath: file.path,
          fileSize: BigInt(file.size),
          mimeType: file.mimetype,
          tags: request.tags || [],
          metadata: request.metadata || {},
          processingStatus: ProcessingStatus.PENDING
        }
      });

      // Start processing in background
      this.processAssetAsync(asset.id, options);

      return this.mapPrismaAsset(asset);
    } catch (error) {
      console.error('Error uploading asset:', error);
      throw error;
    }
  }

  /**
   * Process asset asynchronously
   */
  private async processAssetAsync(assetId: string, options: AssetProcessingOptions): Promise<void> {
    try {
      await this.prisma.asset3D.update({
        where: { id: assetId },
        data: { processingStatus: ProcessingStatus.PROCESSING }
      });

      const asset = await this.prisma.asset3D.findUnique({
        where: { id: assetId }
      });

      if (!asset) {
        throw new Error('Asset not found');
      }

      let result: ModelOptimizationResult | TextureOptimizationResult;

      if (asset.type === AssetType.MODEL_3D) {
        result = await this.processModel(asset, options);
      } else if (asset.type === AssetType.TEXTURE) {
        result = await this.processTexture(asset, options);
      } else {
        throw new Error(`Unsupported asset type: ${asset.type}`);
      }

      // Update asset with processing results
      await this.prisma.asset3D.update({
        where: { id: assetId },
        data: {
          processingStatus: ProcessingStatus.COMPLETED,
          processedAt: new Date(),
          vertices: 'originalVertices' in result ? result.originalVertices : undefined,
          faces: 'originalVertices' in result ? Math.floor(result.originalVertices / 3) : undefined,
          width: 'originalResolution' in result ? result.originalResolution.width : undefined,
          height: 'originalResolution' in result ? result.originalResolution.height : undefined
        }
      });

      // Create variant records
      for (const variant of result.variants) {
        await this.prisma.asset3DVariant.create({
          data: {
            assetId,
            quality: variant.quality,
            fileName: variant.fileName,
            filePath: variant.filePath,
            fileSize: BigInt(variant.fileSize),
            vertices: variant.vertices,
            faces: variant.faces,
            lodLevel: variant.lodLevel,
            width: variant.width,
            height: variant.height,
            compressionRatio: variant.compressionRatio,
            processingStatus: ProcessingStatus.COMPLETED,
            processedAt: new Date()
          }
        });
      }

    } catch (error) {
      console.error('Error processing asset:', error);
      
      await this.prisma.asset3D.update({
        where: { id: assetId },
        data: {
          processingStatus: ProcessingStatus.FAILED,
          processingLog: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  /**
   * Process 3D model
   */
  private async processModel(asset: any, options: AssetProcessingOptions): Promise<ModelOptimizationResult> {
    const originalPath = asset.filePath;
    const originalStats = await fs.stat(originalPath);
    
    // For now, we'll simulate model processing
    // In a real implementation, you would use tools like:
    // - gltf-pipeline for GLTF optimization
    // - Draco compression
    // - Meshoptimizer
    // - Custom LOD generation
    
    const variants: any[] = [];
    
    if (options.generateVariants) {
      for (const quality of options.qualities) {
        const variant = await this.generateModelVariant(asset, quality, options);
        variants.push(variant);
      }
    }

    return {
      originalSize: originalStats.size,
      optimizedSize: variants.reduce((sum, v) => sum + v.fileSize, 0),
      compressionRatio: variants.length > 0 ? originalStats.size / variants[0].fileSize : 1,
      originalVertices: 10000, // Would be extracted from actual model
      optimizedVertices: 5000, // Would be calculated during optimization
      processingTime: Date.now(),
      variants
    };
  }

  /**
   * Process texture
   */
  private async processTexture(asset: any, options: AssetProcessingOptions): Promise<TextureOptimizationResult> {
    const originalPath = asset.filePath;
    const originalStats = await fs.stat(originalPath);
    
    // Get original image metadata
    const metadata = await sharp(originalPath).metadata();
    const originalResolution = {
      width: metadata.width || 1024,
      height: metadata.height || 1024
    };

    const variants: any[] = [];
    
    if (options.generateVariants) {
      for (const quality of options.qualities) {
        const variant = await this.generateTextureVariant(asset, quality, options);
        variants.push(variant);
      }
    }

    return {
      originalSize: originalStats.size,
      optimizedSize: variants.reduce((sum, v) => sum + v.fileSize, 0),
      compressionRatio: variants.length > 0 ? originalStats.size / variants[0].fileSize : 1,
      originalResolution,
      optimizedResolution: variants[0] ? { width: variants[0].width, height: variants[0].height } : originalResolution,
      format: AssetFormat.WEBP,
      variants
    };
  }

  /**
   * Generate model variant for different quality levels
   */
  private async generateModelVariant(asset: any, quality: AssetQuality, options: AssetProcessingOptions): Promise<any> {
    const qualitySettings = {
      [AssetQuality.LOW]: { vertexReduction: 0.1, textureSize: 256 },
      [AssetQuality.MEDIUM]: { vertexReduction: 0.3, textureSize: 512 },
      [AssetQuality.HIGH]: { vertexReduction: 0.7, textureSize: 1024 },
      [AssetQuality.ULTRA]: { vertexReduction: 1.0, textureSize: 2048 }
    };

    const settings = qualitySettings[quality];
    const outputFileName = `${path.parse(asset.fileName).name}_${quality.toLowerCase()}.glb`;
    const outputPath = path.join(this.processedDir, 'models', outputFileName);

    // Simulate model optimization
    // In reality, you would use actual 3D processing libraries
    const originalVertices = 10000;
    const optimizedVertices = Math.floor(originalVertices * settings.vertexReduction);
    
    // Copy original file for now (in real implementation, process it)
    await fs.copyFile(asset.filePath, outputPath);
    const stats = await fs.stat(outputPath);

    return {
      quality,
      fileName: outputFileName,
      filePath: outputPath,
      fileSize: stats.size,
      vertices: optimizedVertices,
      faces: Math.floor(optimizedVertices / 3),
      lodLevel: quality === AssetQuality.LOW ? 0 : quality === AssetQuality.MEDIUM ? 1 : 2,
      compressionRatio: 1.0 // Would be calculated during actual compression
    };
  }

  /**
   * Generate texture variant for different quality levels
   */
  private async generateTextureVariant(asset: any, quality: AssetQuality, options: AssetProcessingOptions): Promise<any> {
    const qualitySettings = {
      [AssetQuality.LOW]: { size: 256, quality: 60 },
      [AssetQuality.MEDIUM]: { size: 512, quality: 75 },
      [AssetQuality.HIGH]: { size: 1024, quality: 85 },
      [AssetQuality.ULTRA]: { size: 2048, quality: 95 }
    };

    const settings = qualitySettings[quality];
    const outputFileName = `${path.parse(asset.fileName).name}_${quality.toLowerCase()}.webp`;
    const outputPath = path.join(this.processedDir, 'textures', outputFileName);

    // Process texture with Sharp
    await sharp(asset.filePath)
      .resize(settings.size, settings.size, { 
        fit: 'cover',
        withoutEnlargement: true 
      })
      .webp({ quality: settings.quality })
      .toFile(outputPath);

    const stats = await fs.stat(outputPath);
    const metadata = await sharp(outputPath).metadata();

    return {
      quality,
      fileName: outputFileName,
      filePath: outputPath,
      fileSize: stats.size,
      width: metadata.width,
      height: metadata.height,
      compressionRatio: asset.fileSize / stats.size
    };
  }

  /**
   * Get asset by ID
   */
  async getAsset(id: string): Promise<Asset3D | null> {
    try {
      const asset = await this.prisma.asset3D.findUnique({
        where: { id },
        include: {
          variants: true
        }
      });

      return asset ? this.mapPrismaAsset(asset) : null;
    } catch (error) {
      console.error('Error getting asset:', error);
      return null;
    }
  }

  /**
   * Get assets by product ID
   */
  async getAssetsByProduct(productId: string): Promise<Asset3D[]> {
    try {
      const assets = await this.prisma.asset3D.findMany({
        where: { 
          productId,
          isActive: true 
        },
        include: {
          variants: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return assets.map(asset => this.mapPrismaAsset(asset));
    } catch (error) {
      console.error('Error getting assets by product:', error);
      return [];
    }
  }

  /**
   * Delete asset
   */
  async deleteAsset(id: string): Promise<boolean> {
    try {
      const asset = await this.prisma.asset3D.findUnique({
        where: { id },
        include: { variants: true }
      });

      if (!asset) {
        return false;
      }

      // Delete physical files
      try {
        await fs.unlink(asset.filePath);
        
        for (const variant of asset.variants) {
          await fs.unlink(variant.filePath);
        }
      } catch (fileError) {
        console.warn('Error deleting physical files:', fileError);
      }

      // Delete database records
      await this.prisma.asset3D.delete({
        where: { id }
      });

      return true;
    } catch (error) {
      console.error('Error deleting asset:', error);
      return false;
    }
  }

  /**
   * Get format from file
   */
  private getFormatFromFile(file: Express.Multer.File): AssetFormat {
    const ext = path.extname(file.originalname).toLowerCase();
    
    switch (ext) {
      case '.glb': return AssetFormat.GLB;
      case '.gltf': return AssetFormat.GLTF;
      case '.fbx': return AssetFormat.FBX;
      case '.obj': return AssetFormat.OBJ;
      case '.jpg':
      case '.jpeg': return AssetFormat.JPG;
      case '.png': return AssetFormat.PNG;
      case '.webp': return AssetFormat.WEBP;
      case '.hdr': return AssetFormat.HDR;
      case '.exr': return AssetFormat.EXR;
      default: return AssetFormat.GLB;
    }
  }

  /**
   * Map Prisma asset to domain model
   */
  private mapPrismaAsset(asset: any): Asset3D {
    return {
      id: asset.id,
      productId: asset.productId,
      name: asset.name,
      description: asset.description,
      type: asset.type as AssetType,
      format: asset.format as AssetFormat,
      quality: asset.quality as AssetQuality,
      originalFileName: asset.originalFileName,
      fileName: asset.fileName,
      filePath: asset.filePath,
      fileSize: Number(asset.fileSize),
      mimeType: asset.mimeType,
      vertices: asset.vertices,
      faces: asset.faces,
      materials: asset.materials,
      textures: asset.textures,
      animations: asset.animations,
      width: asset.width,
      height: asset.height,
      channels: asset.channels,
      processingStatus: asset.processingStatus as ProcessingStatus,
      processingLog: asset.processingLog,
      processedAt: asset.processedAt,
      variants: asset.variants?.map((v: any) => ({
        id: v.id,
        assetId: v.assetId,
        quality: v.quality as AssetQuality,
        fileName: v.fileName,
        filePath: v.filePath,
        fileSize: Number(v.fileSize),
        vertices: v.vertices,
        faces: v.faces,
        lodLevel: v.lodLevel,
        width: v.width,
        height: v.height,
        compressionRatio: v.compressionRatio ? Number(v.compressionRatio) : undefined,
        processingStatus: v.processingStatus as ProcessingStatus,
        processedAt: v.processedAt,
        createdAt: v.createdAt,
        updatedAt: v.updatedAt
      })),
      metadata: asset.metadata,
      tags: asset.tags,
      downloadCount: asset.downloadCount,
      viewCount: asset.viewCount,
      lastAccessedAt: asset.lastAccessedAt,
      isActive: asset.isActive,
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
      createdBy: asset.createdBy
    };
  }

  /**
   * Cleanup - close database connection
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
