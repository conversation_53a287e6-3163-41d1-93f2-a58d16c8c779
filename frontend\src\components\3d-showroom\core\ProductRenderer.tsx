'use client';

import React, { useRef, useState, useCallback, useMemo } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import { useGLTF, useDragControls, Html } from '@react-three/drei';
import * as THREE from 'three';
import { ProductPlacement } from './ShowroomEngine';
import { PatternGenerator, PATTERN_CONFIGS } from '../utils/patterns';
import { GroutManager } from '../utils/grout';
import RealisticMaterialSystem from '../materials/RealisticMaterialSystem';

interface ProductRendererProps {
  products: ProductPlacement[];
  selectedProduct: string | null;
  onProductSelect: (productId: string) => void;
  onProductMove: (productId: string, position: { x: number; y: number; z: number }) => void;
  showWireframe?: boolean;
}

interface SingleProductProps {
  product: ProductPlacement;
  isSelected: boolean;
  onSelect: () => void;
  onMove: (position: { x: number; y: number; z: number }) => void;
  showWireframe?: boolean;
}

// Single Product Component
const SingleProduct: React.FC<SingleProductProps> = ({
  product,
  isSelected,
  onSelect,
  onMove,
  showWireframe = false
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [hovered, setHovered] = useState(false);
  
  // Mock 3D model - In real implementation, this would load actual product models
  const geometry = useMemo(() => {
    const geo = new THREE.BoxGeometry(
      product.scale.width / 100, // Convert cm to meters
      0.02, // 2cm thickness
      product.scale.height / 100
    );
    return geo;
  }, [product.scale]);

  // Calculate proper position (place on ground)
  const finalPosition = useMemo(() => {
    return [
      product.position.x,
      0.01, // Slightly above ground to avoid z-fighting
      product.position.z
    ] as [number, number, number];
  }, [product.position]);

  // Use realistic material system instead of basic material
  const useRealisticMaterial = true;

  // Handle click
  const handleClick = useCallback((event: any) => {
    event.stopPropagation();
    onSelect();
  }, [onSelect]);

  // Handle drag
  const handlePointerDown = useCallback((event: any) => {
    event.stopPropagation();
    setIsDragging(true);
    onSelect();
  }, [onSelect]);

  const handlePointerUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handlePointerMove = useCallback((event: any) => {
    if (isDragging && meshRef.current) {
      const newPosition = {
        x: event.point.x,
        y: 0, // Keep on ground
        z: event.point.z
      };
      onMove(newPosition);
    }
  }, [isDragging, onMove]);

  // Update position
  React.useEffect(() => {
    if (meshRef.current) {
      meshRef.current.position.set(
        product.position.x,
        product.position.y,
        product.position.z
      );
      meshRef.current.rotation.y = product.rotation;
    }
  }, [product.position, product.rotation]);

  if (!product.visible) return null;

  return (
    <group position={finalPosition} rotation={[0, product.rotation, 0]}>
      <mesh
        ref={meshRef}
        geometry={geometry}
        onClick={handleClick}
        onPointerDown={handlePointerDown}
        onPointerUp={handlePointerUp}
        onPointerMove={handlePointerMove}
        onPointerEnter={() => setHovered(true)}
        onPointerLeave={() => setHovered(false)}
        castShadow
        receiveShadow
      >
        <meshStandardMaterial
          color={getProductColor(product.productId)}
          roughness={0.3}
          metalness={0.1}
          transparent={product.opacity < 1}
          opacity={product.opacity}
          wireframe={showWireframe}
        />
      </mesh>
      
      {/* Selection Indicator */}
      {isSelected && (
        <mesh position={[product.position.x, 0.01, product.position.z]}>
          <ringGeometry args={[
            Math.max(product.scale.width, product.scale.height) / 150,
            Math.max(product.scale.width, product.scale.height) / 140,
            32
          ]} />
          <meshBasicMaterial color="#f59e0b" transparent opacity={0.8} />
        </mesh>
      )}

      {/* Hover Indicator */}
      {hovered && !isSelected && (
        <mesh position={[product.position.x, 0.005, product.position.z]}>
          <ringGeometry args={[
            Math.max(product.scale.width, product.scale.height) / 150,
            Math.max(product.scale.width, product.scale.height) / 145,
            32
          ]} />
          <meshBasicMaterial color="#94a3b8" transparent opacity={0.5} />
        </mesh>
      )}

      {/* Product Info on Hover */}
      {hovered && (
        <Html
          position={[product.position.x, 0.5, product.position.z]}
          center
          distanceFactor={10}
        >
          <div className="bg-black/80 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap">
            <div className="font-medium">{getProductName(product.productId)}</div>
            <div className="text-xs opacity-75">
              {product.scale.width}x{product.scale.height} cm
            </div>
            <div className="text-xs opacity-75">
              Pattern: {PATTERN_CONFIGS[product.pattern]?.displayName || product.pattern}
            </div>
          </div>
        </Html>
      )}
    </group>
  );
};

// Main ProductRenderer Component
export const ProductRenderer: React.FC<ProductRendererProps> = ({
  products,
  selectedProduct,
  onProductSelect,
  onProductMove,
  showWireframe = false
}) => {
  // Handle ground click to deselect
  const handleGroundClick = useCallback(() => {
    if (selectedProduct) {
      onProductSelect('');
    }
  }, [selectedProduct, onProductSelect]);

  return (
    <group>
      {/* Ground Plane for deselection */}
      <mesh
        position={[0, -0.01, 0]}
        rotation={[-Math.PI / 2, 0, 0]}
        onClick={handleGroundClick}
        receiveShadow
      >
        <planeGeometry args={[50, 50]} />
        <meshStandardMaterial 
          color="#f8f9fa" 
          transparent 
          opacity={0.1}
          visible={false}
        />
      </mesh>

      {/* Render all products */}
      {products.map((product) => (
        <SingleProduct
          key={product.id}
          product={product}
          isSelected={selectedProduct === product.id}
          onSelect={() => onProductSelect(product.id)}
          onMove={(position) => onProductMove(product.id, position)}
          showWireframe={showWireframe}
        />
      ))}

      {/* Area Calculation Helpers */}
      {products.length > 0 && (
        <AreaCalculator products={products} />
      )}
    </group>
  );
};

// Area Calculator Component
const AreaCalculator: React.FC<{ products: ProductPlacement[] }> = ({ products }) => {
  const totalArea = useMemo(() => {
    return products.reduce((sum, product) => {
      if (product.visible) {
        return sum + (product.scale.width * product.scale.height) / 10000; // Convert cm² to m²
      }
      return sum;
    }, 0);
  }, [products]);

  return null; // This component just calculates, doesn't render
};

// Helper Functions
function getProductColor(productId: string): string {
  // Mock color mapping - in real implementation, this would come from product data
  const colors: Record<string, string> = {
    'marble-carrara-white': '#f8f9fa',
    'marble-emperador-dark': '#8b4513',
    'granite-absolute-black': '#2d3748',
    'granite-kashmir-white': '#e2e8f0',
    'travertine-classic-beige': '#f7fafc',
    'travertine-noce': '#d4a574',
    'onyx-green-pakistan': '#38a169',
    'limestone-jerusalem-gold': '#f6d55c'
  };

  return colors[productId] || '#e2e8f0';
}

function getProductName(productId: string): string {
  // Mock name mapping - in real implementation, this would come from product data
  const names: Record<string, string> = {
    'marble-carrara-white': 'Carrara Beyaz Mermer',
    'marble-emperador-dark': 'Emperador Koyu Mermer',
    'granite-absolute-black': 'Absolute Black Granit',
    'granite-kashmir-white': 'Kashmir White Granit',
    'travertine-classic-beige': 'Klasik Bej Traverten',
    'travertine-noce': 'Noce Traverten',
    'onyx-green-pakistan': 'Pakistan Yeşil Oniks',
    'limestone-jerusalem-gold': 'Jerusalem Gold Kireçtaşı'
  };

  return names[productId] || 'Bilinmeyen Ürün';
}

function getGroutColor(baseColor: string): string {
  // Generate a slightly darker grout color based on base color
  const color = new THREE.Color(baseColor);
  color.multiplyScalar(0.8); // Make it 20% darker
  return `#${color.getHexString()}`;
}

export default ProductRenderer;
