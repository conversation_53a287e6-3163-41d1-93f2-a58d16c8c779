"use client"

import * as React from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useQuote } from "@/contexts/quote-context"
import {
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  EnvelopeIcon,
  EyeIcon
} from "@heroicons/react/24/outline"
import { QuoteSubmissionModal } from "@/components/ui/quote-submission-modal"

interface ProducerQuoteRequestsPageProps {
  onNavigate?: (path: string) => void
}

export default function ProducerQuoteRequestsPage({ onNavigate }: ProducerQuoteRequestsPageProps) {
  const { 
    producerQuoteRequests, 
    createQuote,
    isLoading 
  } = useQuote()

  const [selectedRequestId, setSelectedRequestId] = React.useState<string | null>(null)
  const [isQuoteModalO<PERSON>, setIsQuoteModalOpen] = React.useState(false)
  const [selectedRequest, setSelectedRequest] = React.useState<any>(null)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'quoted':
        return 'bg-blue-100 text-blue-800'
      case 'accepted':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      case 'completed':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'quoted':
        return 'Teklif Verildi'
      case 'accepted':
        return 'Kabul Edildi'
      case 'rejected':
        return 'Reddedildi'
      case 'completed':
        return 'Tamamlandı'
      default:
        return 'Bilinmiyor'
    }
  }

  const handleViewDetails = (requestId: string) => {
    setSelectedRequestId(selectedRequestId === requestId ? null : requestId)
  }

  const handleSubmitQuote = (request: any) => {
    setSelectedRequest(request)
    setIsQuoteModalOpen(true)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Teklif Talepleri</h2>
          <p className="text-gray-600">
            Müşterilerden gelen teklif taleplerini görüntüleyin ve teklif verin
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="text-sm">
            {producerQuoteRequests.length} Talep
          </Badge>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <button className="px-4 py-2 text-sm font-medium bg-white text-gray-900 rounded-md shadow-sm">
          Tümü ({producerQuoteRequests.length})
        </button>
        <button className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900">
          Yeni ({producerQuoteRequests.filter(r => r.status === 'pending').length})
        </button>
        <button className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900">
          Teklif Verildi ({producerQuoteRequests.filter(r => r.status === 'quoted').length})
        </button>
        <button className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900">
          Kabul Edildi ({producerQuoteRequests.filter(r => r.status === 'accepted').length})
        </button>
      </div>

      {/* Requests List */}
      {producerQuoteRequests.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz teklif talebi yok</h3>
            <p className="text-gray-600 mb-4">
              Müşteriler ürünleriniz için teklif istediğinde burada görünecektir.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {producerQuoteRequests.map((request) => {
            const isExpanded = selectedRequestId === request.id
            
            return (
              <Card key={request.id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <CardTitle className="text-lg">
                          Teklif Talebi #{request.id.slice(-6)}
                        </CardTitle>
                        <Badge className={getStatusColor(request.status)}>
                          {getStatusText(request.status)}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <BuildingOfficeIcon className="h-4 w-4" />
                          <span>{request.customerName}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <EnvelopeIcon className="h-4 w-4" />
                          <span>{request.customerEmail}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <CalendarIcon className="h-4 w-4" />
                          <span>{new Date(request.createdAt).toLocaleDateString('tr-TR')}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(request.id)}
                      >
                        <EyeIcon className="h-4 w-4 mr-1" />
                        {isExpanded ? 'Gizle' : 'Detaylar'}
                      </Button>
                      
                      {request.status === 'pending' && (
                        <Button
                          size="sm"
                          onClick={() => handleSubmitQuote(request)}
                        >
                          <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                          Teklif Ver
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>

                {/* Product Summary */}
                <CardContent className="pt-0">
                  <div className="flex items-center space-x-4 mb-3">
                    <span className="text-sm font-medium text-gray-900">
                      {request.products.length} Ürün:
                    </span>
                    <div className="flex flex-wrap gap-2">
                      {request.products.slice(0, 3).map((product, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {product.productName}
                        </Badge>
                      ))}
                      {request.products.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{request.products.length - 3} daha
                        </Badge>
                      )}
                    </div>
                  </div>

                  {request.message && (
                    <div className="bg-gray-50 rounded-lg p-3 mb-3">
                      <p className="text-sm text-gray-700">
                        <span className="font-medium">Müşteri Notu:</span> {request.message}
                      </p>
                    </div>
                  )}

                  {/* Expanded Details */}
                  {isExpanded && (
                    <div className="border-t pt-4 mt-4">
                      <h4 className="font-medium text-gray-900 mb-3">Ürün Detayları</h4>
                      <div className="space-y-4">
                        {request.products.map((product, index) => (
                          <div key={index} className="bg-white border rounded-lg p-4">
                            <div className="flex items-start space-x-4">
                              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                <span className="text-2xl">🪨</span>
                              </div>
                              <div className="flex-1">
                                <h5 className="font-medium text-gray-900 mb-1">
                                  {product.productName}
                                </h5>
                                <p className="text-sm text-gray-600 mb-2">
                                  Kategori: {product.productCategory}
                                </p>
                                
                                {product.specifications.length > 0 && (
                                  <div className="space-y-2">
                                    <p className="text-sm font-medium text-gray-700">Özellikler:</p>
                                    {product.specifications.map((spec, specIndex) => (
                                      <div key={specIndex} className="text-xs text-gray-600 bg-gray-50 rounded p-2">
                                        <div className="grid grid-cols-2 gap-2">
                                          <span>Kalınlık: {spec.thickness}</span>
                                          <span>En: {spec.width}</span>
                                          <span>Boy: {spec.length}</span>
                                          <span>Yüzey: {spec.surface}</span>
                                          <span>Ambalaj: {spec.packaging}</span>
                                          <span>Teslimat: {spec.delivery}</span>
                                          <span>Alan: {spec.area} m²</span>
                                          {spec.targetPrice && (
                                            <span>Hedef Fiyat: {spec.targetPrice} {spec.currency}</span>
                                          )}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {/* Quote Submission Modal */}
      <QuoteSubmissionModal
        isOpen={isQuoteModalOpen}
        onClose={() => setIsQuoteModalOpen(false)}
        request={selectedRequest}
      />
    </div>
  )
}
