'use client'

import React, { createContext, useContext, useState, ReactNode } from 'react'

// Product interface
export interface Product {
  id: string
  name: string
  category: string
  image?: string
  status: 'active' | 'inactive' | 'draft'
  stock?: number
  unit?: string
  basePrice?: number
  currency?: string
  createdAt: string
  approvalStatus?: 'pending' | 'approved' | 'rejected'
  rejectionReason?: string
  submittedAt?: Date
  reviewedAt?: Date
  reviewedBy?: string
  producer?: string
  description?: string
}

// Deletion request interface
export interface DeletionRequest {
  id: string
  productId: string
  productName: string
  reason: string
  requestedAt: Date
  requestedBy: string
  status: 'pending' | 'approved' | 'rejected'
}

// Context type
interface ProductsContextType {
  products: Product[]
  addProduct: (product: Product) => void
  updateProduct: (id: string, updates: Partial<Product>) => void
  deleteProduct: (id: string, reason: string) => void
  getProductsByStatus: (status: string) => Product[]
  getPendingProducts: () => Product[]
  getApprovedProducts: () => Product[]
  updateStock: (productId: string, producerId: string, newStock: number) => void
  getProductStock: (productId: string, producerId: string) => number
  deletionRequests: DeletionRequest[]
}

const ProductsContext = createContext<ProductsContextType | undefined>(undefined)

// Test verileri kaldırıldı - gerçek veriler API'den gelecek
const initialProducts: Product[] = []

export function ProductsProvider({ children }: { children: React.ReactNode }) {
  const [products, setProducts] = useState<Product[]>(initialProducts)
  const [deletionRequests, setDeletionRequests] = useState<DeletionRequest[]>([])

  const addProduct = (product: Product) => {
    setProducts(prev => [...prev, product])
  }

  const updateProduct = (id: string, updates: Partial<Product>) => {
    setProducts(prev => prev.map(product => 
      product.id === id ? { ...product, ...updates } : product
    ))
  }

  const deleteProduct = (id: string, reason: string) => {
    const product = products.find(p => p.id === id)
    if (product) {
      const deletionRequest: DeletionRequest = {
        id: Date.now().toString(),
        productId: id,
        productName: product.name,
        reason,
        requestedAt: new Date(),
        requestedBy: 'current-user', // Gerçek uygulamada auth context'ten gelecek
        status: 'pending'
      }
      setDeletionRequests(prev => [...prev, deletionRequest])
    }
  }

  const getProductsByStatus = (status: string) => {
    return products.filter(product => product.status === status)
  }

  const getPendingProducts = () => {
    return products.filter(product => product.approvalStatus === 'pending')
  }

  const getApprovedProducts = () => {
    return products.filter(product => product.approvalStatus === 'approved')
  }

  // Stok güncelleme fonksiyonu - gerçek zamanlı
  const updateStock = (productId: string, producerId: string, newStock: number) => {
    setProducts(prevProducts =>
      prevProducts.map(product =>
        product.id === productId
          ? { ...product, stock: newStock }
          : product
      )
    )
  }

  // Stok sorgulama fonksiyonu
  const getProductStock = (productId: string, producerId: string): number => {
    const product = products.find(p => p.id === productId)
    return product?.stock || 0
  }

  const value: ProductsContextType = {
    products,
    addProduct,
    updateProduct,
    deleteProduct,
    getProductsByStatus,
    getPendingProducts,
    getApprovedProducts,
    updateStock,
    getProductStock,
    deletionRequests
  }

  return (
    <ProductsContext.Provider value={value}>
      {children}
    </ProductsContext.Provider>
  )
}

export function useProducts() {
  const context = useContext(ProductsContext)
  if (context === undefined) {
    throw new Error('useProducts must be used within a ProductsProvider')
  }
  return context
}
