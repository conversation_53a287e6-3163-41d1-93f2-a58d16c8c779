#!/usr/bin/env node

/**
 * SSL Certificate Setup Script
 * Sets up SSL certificates for production deployment
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔒 SSL Certificate Setup for Production\n');

// Configuration
const DOMAIN = process.env.DOMAIN || 'yourdomain.com';
const EMAIL = process.env.SSL_EMAIL || '<EMAIL>';
const SSL_DIR = '/etc/ssl';
const CERT_DIR = path.join(SSL_DIR, 'certs');
const KEY_DIR = path.join(SSL_DIR, 'private');

// Check if running as root (required for SSL setup)
function checkRoot() {
  if (process.getuid && process.getuid() !== 0) {
    console.error('❌ This script must be run as root (sudo)');
    console.log('Usage: sudo node setup-ssl.js');
    process.exit(1);
  }
}

// Install Certbot (Let's Encrypt client)
function installCertbot() {
  console.log('📦 Installing Certbot...');
  
  try {
    // Detect OS and install accordingly
    const os = execSync('uname -s', { encoding: 'utf8' }).trim();
    
    if (os === 'Linux') {
      // Ubuntu/Debian
      try {
        execSync('apt-get update', { stdio: 'inherit' });
        execSync('apt-get install -y certbot python3-certbot-nginx', { stdio: 'inherit' });
      } catch {
        // CentOS/RHEL
        execSync('yum install -y certbot python3-certbot-nginx', { stdio: 'inherit' });
      }
    } else {
      console.warn('⚠️ Automatic Certbot installation not supported on this OS');
      console.log('Please install Certbot manually: https://certbot.eff.org/');
      return false;
    }
    
    console.log('✅ Certbot installed successfully');
    return true;
  } catch (error) {
    console.error('❌ Certbot installation failed:', error.message);
    return false;
  }
}

// Generate SSL certificate using Let's Encrypt
function generateSSLCertificate() {
  console.log(`🔐 Generating SSL certificate for ${DOMAIN}...`);
  
  try {
    const certbotCommand = [
      'certbot certonly',
      '--standalone',
      '--non-interactive',
      '--agree-tos',
      `--email ${EMAIL}`,
      `-d ${DOMAIN}`,
      `-d www.${DOMAIN}`
    ].join(' ');
    
    execSync(certbotCommand, { stdio: 'inherit' });
    
    console.log('✅ SSL certificate generated successfully');
    return true;
  } catch (error) {
    console.error('❌ SSL certificate generation failed:', error.message);
    return false;
  }
}

// Create self-signed certificate for development/testing
function createSelfSignedCertificate() {
  console.log('🔐 Creating self-signed certificate for development...');
  
  try {
    // Ensure directories exist
    if (!fs.existsSync(CERT_DIR)) {
      fs.mkdirSync(CERT_DIR, { recursive: true });
    }
    if (!fs.existsSync(KEY_DIR)) {
      fs.mkdirSync(KEY_DIR, { recursive: true });
    }
    
    const certPath = path.join(CERT_DIR, `${DOMAIN}.crt`);
    const keyPath = path.join(KEY_DIR, `${DOMAIN}.key`);
    
    // Generate private key
    execSync(`openssl genrsa -out "${keyPath}" 2048`, { stdio: 'inherit' });
    
    // Generate certificate
    const opensslCommand = [
      'openssl req -new -x509',
      '-key', `"${keyPath}"`,
      '-out', `"${certPath}"`,
      '-days 365',
      '-subj', `"/C=TR/ST=Istanbul/L=Istanbul/O=Natural Stone Marketplace/CN=${DOMAIN}"`
    ].join(' ');
    
    execSync(opensslCommand, { stdio: 'inherit' });
    
    // Set proper permissions
    execSync(`chmod 600 "${keyPath}"`, { stdio: 'inherit' });
    execSync(`chmod 644 "${certPath}"`, { stdio: 'inherit' });
    
    console.log('✅ Self-signed certificate created');
    console.log(`   Certificate: ${certPath}`);
    console.log(`   Private Key: ${keyPath}`);
    
    return { certPath, keyPath };
  } catch (error) {
    console.error('❌ Self-signed certificate creation failed:', error.message);
    return null;
  }
}

// Setup SSL certificate renewal
function setupCertificateRenewal() {
  console.log('🔄 Setting up automatic certificate renewal...');
  
  try {
    // Create renewal script
    const renewalScript = `#!/bin/bash
# SSL Certificate Renewal Script

echo "$(date): Starting certificate renewal check..."

# Renew certificates
certbot renew --quiet

# Restart services if certificates were renewed
if [ $? -eq 0 ]; then
    echo "$(date): Certificate renewal check completed"
    
    # Restart nginx if running
    if systemctl is-active --quiet nginx; then
        systemctl reload nginx
        echo "$(date): Nginx reloaded"
    fi
    
    # Restart your Node.js application
    # systemctl restart your-app-name
    
else
    echo "$(date): Certificate renewal failed"
fi
`;

    const renewalScriptPath = '/usr/local/bin/renew-ssl.sh';
    fs.writeFileSync(renewalScriptPath, renewalScript);
    execSync(`chmod +x "${renewalScriptPath}"`, { stdio: 'inherit' });
    
    // Add to crontab (run twice daily)
    const cronJob = '0 */12 * * * /usr/local/bin/renew-ssl.sh >> /var/log/ssl-renewal.log 2>&1';
    execSync(`(crontab -l 2>/dev/null; echo "${cronJob}") | crontab -`, { stdio: 'inherit' });
    
    console.log('✅ Automatic renewal configured');
    console.log('   Script: /usr/local/bin/renew-ssl.sh');
    console.log('   Cron: Runs twice daily');
    
  } catch (error) {
    console.error('❌ Renewal setup failed:', error.message);
  }
}

// Configure Nginx SSL
function configureNginxSSL() {
  console.log('🌐 Configuring Nginx SSL...');
  
  const nginxConfig = `
# SSL Configuration for Natural Stone Marketplace
server {
    listen 80;
    server_name ${DOMAIN} www.${DOMAIN};
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ${DOMAIN} www.${DOMAIN};
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/${DOMAIN}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/${DOMAIN}/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Frontend (Next.js)
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Static files
    location /static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
`;

  try {
    const configPath = `/etc/nginx/sites-available/${DOMAIN}`;
    fs.writeFileSync(configPath, nginxConfig);
    
    // Enable site
    const enabledPath = `/etc/nginx/sites-enabled/${DOMAIN}`;
    if (fs.existsSync(enabledPath)) {
      fs.unlinkSync(enabledPath);
    }
    fs.symlinkSync(configPath, enabledPath);
    
    // Test nginx configuration
    execSync('nginx -t', { stdio: 'inherit' });
    
    // Reload nginx
    execSync('systemctl reload nginx', { stdio: 'inherit' });
    
    console.log('✅ Nginx SSL configuration completed');
    
  } catch (error) {
    console.error('❌ Nginx configuration failed:', error.message);
  }
}

// Main setup process
async function main() {
  const args = process.argv.slice(2);
  const isDevelopment = args.includes('--dev');
  
  try {
    if (!isDevelopment) {
      checkRoot();
    }
    
    if (isDevelopment) {
      // Development setup with self-signed certificate
      console.log('🔧 Setting up development SSL...');
      const result = createSelfSignedCertificate();
      
      if (result) {
        console.log('\n🎉 Development SSL setup completed!');
        console.log('\n📋 Next steps:');
        console.log('1. Update your .env.production file with certificate paths');
        console.log('2. Configure your application to use HTTPS');
        console.log('3. Add certificate to your browser (for development)');
      }
      
    } else {
      // Production setup with Let's Encrypt
      console.log('🚀 Setting up production SSL...');
      
      const certbotInstalled = installCertbot();
      if (!certbotInstalled) {
        process.exit(1);
      }
      
      const certGenerated = generateSSLCertificate();
      if (!certGenerated) {
        console.log('⚠️ Falling back to self-signed certificate...');
        createSelfSignedCertificate();
      } else {
        setupCertificateRenewal();
        configureNginxSSL();
      }
      
      console.log('\n🎉 Production SSL setup completed!');
      console.log('\n📋 Next steps:');
      console.log('1. Update your DNS records to point to this server');
      console.log('2. Test your SSL configuration');
      console.log('3. Monitor certificate renewal logs');
    }
    
  } catch (error) {
    console.error('\n❌ SSL setup failed:', error.message);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('Usage: node setup-ssl.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --dev     Setup self-signed certificate for development');
  console.log('  --help    Show this help message');
  console.log('');
  console.log('Environment variables:');
  console.log('  DOMAIN     Domain name (default: yourdomain.com)');
  console.log('  SSL_EMAIL  Email for Let\'s Encrypt (default: <EMAIL>)');
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
