import { Request, Response } from 'express';
import { NotificationService, NotificationData, NotificationPreferences } from '../services/NotificationService';
import { asyncHandler } from '../middleware/errorHandler';
import { z } from 'zod';
import { PrismaClient, NotificationType } from '@prisma/client';

const prisma = new PrismaClient();

// Validation schemas
const sendNotificationSchema = z.object({
  userId: z.string().cuid(),
  title: z.string().min(1),
  message: z.string().min(1),
  type: z.nativeEnum(NotificationType),
  relatedEntityType: z.string().optional(),
  relatedEntityId: z.string().optional(),
  data: z.any().optional(),
  sendEmail: z.boolean().default(false),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
});

const broadcastNotificationSchema = z.object({
  userType: z.enum(['customer', 'producer', 'admin']),
  title: z.string().min(1),
  message: z.string().min(1),
  type: z.nativeEnum(NotificationType),
  data: z.any().optional(),
  sendEmail: z.boolean().default(false),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
});

const updatePreferencesSchema = z.object({
  emailEnabled: z.boolean(),
  browserEnabled: z.boolean(),
  escrowNotifications: z.boolean(),
  orderNotifications: z.boolean(),
  quoteNotifications: z.boolean(),
  systemNotifications: z.boolean(),
  marketingEmails: z.boolean(),
});

export class NotificationController {
  private notificationService: NotificationService;

  constructor(notificationService: NotificationService) {
    this.notificationService = notificationService;
  }

  /**
   * Send notification to specific user
   * @route POST /api/notifications/send
   * @access Private (Admin only)
   */
  sendNotification = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const notificationData = sendNotificationSchema.parse(req.body);

    await this.notificationService.sendNotification(notificationData);

    res.json({
      success: true,
      message: 'Notification sent successfully'
    });
  });

  /**
   * Broadcast notification to all users of a type
   * @route POST /api/notifications/broadcast
   * @access Private (Admin only)
   */
  broadcastNotification = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const { userType, ...notificationData } = broadcastNotificationSchema.parse(req.body);

    await this.notificationService.broadcastToUserType(userType, notificationData);

    res.json({
      success: true,
      message: `Notification broadcasted to all ${userType} users`
    });
  });

  /**
   * Get user's notifications
   * @route GET /api/notifications
   * @access Private
   */
  getNotifications = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;

    const result = await this.notificationService.getUserNotifications(user.id, page, limit);

    res.json({
      success: true,
      data: result
    });
  });

  /**
   * Get unread notifications count
   * @route GET /api/notifications/unread-count
   * @access Private
   */
  getUnreadCount = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const count = await prisma.notification.count({
      where: {
        userId: user.id,
        isRead: false,
      },
    });

    res.json({
      success: true,
      data: { count }
    });
  });

  /**
   * Mark notification as read
   * @route PUT /api/notifications/:id/read
   * @access Private
   */
  markAsRead = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    const { id } = req.params;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    await this.notificationService.markAsRead(id, user.id);

    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  });

  /**
   * Mark all notifications as read
   * @route PUT /api/notifications/mark-all-read
   * @access Private
   */
  markAllAsRead = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    await this.notificationService.markAllAsRead(user.id);

    res.json({
      success: true,
      message: 'All notifications marked as read'
    });
  });

  /**
   * Get notification preferences
   * @route GET /api/notifications/preferences
   * @access Private
   */
  getPreferences = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const preferences = await this.notificationService.getNotificationPreferences(user.id);

    res.json({
      success: true,
      data: preferences
    });
  });

  /**
   * Update notification preferences
   * @route PUT /api/notifications/preferences
   * @access Private
   */
  updatePreferences = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const preferences = updatePreferencesSchema.parse(req.body);

    await this.notificationService.updateNotificationPreferences(user.id, preferences);

    res.json({
      success: true,
      message: 'Notification preferences updated successfully'
    });
  });

  /**
   * Get notification statistics (Admin only)
   * @route GET /api/notifications/stats
   * @access Private (Admin only)
   */
  getStats = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const [
      totalNotifications,
      unreadNotifications,
      notificationsByType,
      recentActivity
    ] = await Promise.all([
      prisma.notification.count(),
      prisma.notification.count({ where: { isRead: false } }),
      prisma.notification.groupBy({
        by: ['notificationType'],
        _count: { notificationType: true },
      }),
      prisma.notification.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: { email: true, companyName: true, userType: true }
          }
        }
      })
    ]);

    const connectedUsers = this.notificationService.getConnectedUsersCount();
    const connectedByType = this.notificationService.getConnectedUsersByType();

    res.json({
      success: true,
      data: {
        totalNotifications,
        unreadNotifications,
        connectedUsers,
        connectedByType,
        notificationsByType,
        recentActivity,
      }
    });
  });

  /**
   * Test notification system
   * @route POST /api/notifications/test
   * @access Private (Admin only)
   */
  testNotification = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const { targetUserId, includeEmail } = req.body;

    await this.notificationService.sendNotification({
      userId: targetUserId || user.id,
      title: 'Test Notification',
      message: 'This is a test notification to verify the notification system is working correctly.',
      type: NotificationType.SYSTEM_ANNOUNCEMENT,
      sendEmail: includeEmail || false,
      priority: 'medium',
      data: {
        test: true,
        timestamp: new Date().toISOString(),
      },
    });

    res.json({
      success: true,
      message: 'Test notification sent successfully'
    });
  });

  /**
   * Get system health
   * @route GET /api/notifications/health
   * @access Private (Admin only)
   */
  getHealth = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      websocket: {
        connected: true,
        connectedUsers: this.notificationService.getConnectedUsersCount(),
        usersByType: this.notificationService.getConnectedUsersByType(),
      },
      database: {
        connected: true, // This would be checked in a real implementation
      },
      email: {
        configured: !!(process.env.EMAIL_PROVIDER && 
                     (process.env.SENDGRID_API_KEY || process.env.SMTP_HOST)),
      },
    };

    res.json({
      success: true,
      data: health
    });
  });
}
