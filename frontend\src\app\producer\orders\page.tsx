'use client'

import * as React from 'react'
import Link from 'next/link'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  ShoppingCart,
  Clock,
  CheckCircle,
  Truck,
  Factory,
  AlertTriangle
} from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function ProducerOrders() {
  const { producer } = useProducerAuth()
  const router = useRouter()

  // Onay kontrolü
  if (!producer?.isApproved) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="p-6 max-w-md mx-auto text-center">
          <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Hesap <PERSON><PERSON><PERSON></h2>
          <p className="text-gray-600 mb-6">
            Bu sayfaya erişebilmek için hesabınızın admin tarafından onaylanması gerekiyor.
          </p>
          <div className="flex justify-center gap-4">
            <Button
              onClick={() => router.push('/producer/settings')}
              className="bg-amber-500 text-white hover:bg-amber-600"
            >
              Ayarları Düzenle
            </Button>
            <Button
              onClick={() => router.push('/producer/dashboard')}
              variant="outline"
            >
              Dashboard'a Dön
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  const menuItems = [
    {
      id: 'pending',
      label: 'Bekleyen Siparişler',
      href: '/producer/orders/pending',
      count: 0,
      icon: Clock,
      color: 'text-yellow-600 bg-yellow-100'
    },
    {
      id: 'production',
      label: 'Üretimde',
      href: '/producer/orders/production',
      count: 0,
      icon: Factory,
      color: 'text-blue-600 bg-blue-100'
    },
    {
      id: 'shipped',
      label: 'Kargoda',
      href: '/producer/orders/shipped',
      count: 0,
      icon: Truck,
      color: 'text-purple-600 bg-purple-100'
    },
    {
      id: 'completed',
      label: 'Tamamlanan',
      href: '/producer/orders/completed',
      count: 0,
      icon: CheckCircle,
      color: 'text-green-600 bg-green-100'
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Siparişler</h1>
          <p className="text-gray-600">Siparişlerinizi yönetin ve takip edin</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {menuItems.map((item) => {
          const IconComponent = item.icon
          return (
            <Link key={item.id} href={item.href}>
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        {item.label}
                      </p>
                      <p className="text-2xl font-bold text-gray-900">
                        {item.count}
                      </p>
                    </div>
                    <div className={`p-3 rounded-full ${item.color}`}>
                      <IconComponent className="w-6 h-6" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          )
        })}
      </div>

      <Card>
        <CardContent className="p-12 text-center">
          <ShoppingCart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Henüz sipariş yok
          </h3>
          <p className="text-gray-600 mb-6">
            Müşterilerden gelen siparişler burada görünecek
          </p>
        </CardContent>
      </Card>
    </div>
  )
}