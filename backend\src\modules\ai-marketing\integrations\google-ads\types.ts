// Google Ads API Types
export interface GoogleAdsConfig {
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  developerToken: string;
  customerId: string;
}

export interface GoogleAdsCredentials {
  access_token: string;
  refresh_token: string;
  scope: string;
  token_type: string;
  expiry_date: number;
}

export interface GoogleAdsCustomer {
  resourceName: string;
  id: string;
  descriptiveName: string;
  currencyCode: string;
  timeZone: string;
  trackingUrlTemplate?: string;
  finalUrlSuffix?: string;
  autoTaggingEnabled: boolean;
  hasPartnersBadge: boolean;
  manager: boolean;
  testAccount: boolean;
  callReportingSetting?: {
    callReportingEnabled: boolean;
    callConversionReportingEnabled: boolean;
    callConversionAction?: string;
  };
  conversionTrackingSetting?: {
    conversionTrackingId: string;
    crossAccountConversionTrackingId: string;
    acceptedCustomerDataTerms: boolean;
    conversionTrackingStatus: string;
    enhancedConversionsForLeadsEnabled: boolean;
    googleAdsConversionCustomer: string;
  };
  remarketingSetting?: {
    googleGlobalSiteTag: string;
  };
  payPerConversionEligibilityFailureReasons: string[];
  optimizationScore?: number;
  optimizationScoreWeight?: number;
  status: string;
}

export interface GoogleAdsCampaign {
  resourceName: string;
  id: string;
  name: string;
  status: string;
  servingStatus: string;
  adServingOptimizationStatus: string;
  advertisingChannelType: string;
  advertisingChannelSubType?: string;
  trackingUrlTemplate?: string;
  urlCustomParameters: Array<{
    key: string;
    value: string;
  }>;
  realTimeBiddingEnabled: boolean;
  networkSettings: {
    targetGoogleSearch: boolean;
    targetSearchNetwork: boolean;
    targetContentNetwork: boolean;
    targetPartnerSearchNetwork: boolean;
  };
  startDate: string;
  endDate?: string;
  finalUrlSuffix?: string;
  frequencyCaps: Array<{
    key: {
      level: string;
      eventType: string;
      timeUnit: string;
      timeLength: number;
    };
    cap: number;
  }>;
  videoBrandSafetySuitability: string;
  vanityPharma: {
    vanityPharmaDisplayUrlMode: string;
    vanityPharmaText: string;
  };
  selectiveOptimization: {
    conversionActions: string[];
  };
  optimizationGoalTypes: string[];
  trackingSetting?: {
    trackingUrl: string;
  };
  paymentMode: string;
  optimizationScore?: number;
  excludedParentAssetFieldTypes: string[];
  urlExpansionOptOut: boolean;
  performanceMaxUpgrade?: {
    performanceMaxCampaign: string;
    preUpgradeCampaign: string;
    status: string;
  };
  hotelSetting?: {
    hotelCenterId: string;
  };
  localCampaignSetting?: {
    locationSourceType: string;
  };
  travelCampaignSetting?: {
    travelAccountId: string;
  };
  biddingStrategy?: string;
  biddingStrategyType: string;
  accessibleBiddingStrategy: string;
  biddingStrategySystemStatus: string;
  commission?: {
    commissionRateMicros: number;
  };
  manualCpc?: {
    enhancedCpcEnabled: boolean;
  };
  manualCpm?: {};
  manualCpv?: {};
  maximizeConversions?: {
    targetCpa?: number;
    targetCpaMicros?: number;
    cpcBidCeilingMicros?: number;
    cpcBidFloorMicros?: number;
  };
  maximizeConversionValue?: {
    targetRoas?: number;
    cpcBidCeilingMicros?: number;
    cpcBidFloorMicros?: number;
  };
  targetCpa?: {
    targetCpaMicros?: number;
    cpcBidCeilingMicros?: number;
    cpcBidFloorMicros?: number;
  };
  targetImpressionShare?: {
    location: string;
    locationFractionMicros?: number;
    cpcBidCeilingMicros?: number;
  };
  targetRoas?: {
    targetRoas?: number;
    cpcBidCeilingMicros?: number;
    cpcBidFloorMicros?: number;
  };
  targetSpend?: {
    targetSpendMicros?: number;
    cpcBidCeilingMicros?: number;
  };
  percentCpc?: {
    cpcBidCeilingMicros?: number;
    enhancedCpcEnabled?: boolean;
  };
  targetCpm?: {};
}

export interface GoogleAdsAdGroup {
  resourceName: string;
  id: string;
  name: string;
  status: string;
  type: string;
  adRotationMode: string;
  trackingUrlTemplate?: string;
  urlCustomParameters: Array<{
    key: string;
    value: string;
  }>;
  campaign: string;
  cpcBidMicros?: number;
  cpmBidMicros?: number;
  targetCpaMicros?: number;
  cpvBidMicros?: number;
  targetCpmMicros?: number;
  targetRoas?: number;
  percentCpcBidMicros?: number;
  explorerAutoOptimizerSetting?: {
    optIn: boolean;
  };
  displayCustomBidDimension: string;
  finalUrlSuffix?: string;
  targetingDimension?: string;
  effectiveTargetCpaMicros?: number;
  effectiveTargetCpaSource: string;
  effectiveTargetRoas?: number;
  effectiveTargetRoasSource: string;
  labels: string[];
  excludedParentAssetFieldTypes: string[];
  biddingStrategy?: string;
  biddingStrategyType: string;
  biddingStrategySystemStatus: string;
}

export interface GoogleAdsKeyword {
  resourceName: string;
  adGroup: string;
  criterionId: string;
  status: string;
  keyword: {
    text: string;
    matchType: string;
  };
  urlCustomParameters: Array<{
    key: string;
    value: string;
  }>;
  finalUrls: string[];
  finalMobileUrls: string[];
  finalUrlSuffix?: string;
  trackingUrlTemplate?: string;
  cpcBidMicros?: number;
  cpmBidMicros?: number;
  cpvBidMicros?: number;
  percentCpcBidMicros?: number;
  effectiveCpcBidMicros?: number;
  effectiveCpcBidSource: string;
  effectiveCpmBidMicros?: number;
  effectiveCpmBidSource: string;
  effectiveCpvBidMicros?: number;
  effectiveCpvBidSource: string;
  effectivePercentCpcBidMicros?: number;
  effectivePercentCpcBidSource: string;
  finalAppUrls: Array<{
    osType: string;
    url: string;
  }>;
  labels: string[];
  bid?: number;
  negativeKeyword?: boolean;
  systemServingStatus: string;
  approvalStatus: string;
  disapprovalReasons: string[];
  qualityInfo?: {
    qualityScore?: number;
    creativeQualityScore: string;
    postClickQualityScore: string;
    searchPredictedCtr: string;
  };
  positionEstimates?: {
    firstPageCpcMicros?: number;
    firstPositionCpcMicros?: number;
    topOfPageCpcMicros?: number;
    estimatedAddClicksAtFirstPositionCpc?: number;
    estimatedAddCostAtFirstPositionCpc?: number;
  };
}

export interface GoogleAdsReport {
  results: any[];
  fieldMask: string;
  nextPageToken?: string;
  totalResultsCount?: number;
}

export interface GoogleAdsMetrics {
  impressions?: number;
  clicks?: number;
  ctr?: number;
  averageCpc?: number;
  cost?: number;
  conversions?: number;
  conversionRate?: number;
  costPerConversion?: number;
  conversionValue?: number;
  valuePerConversion?: number;
  allConversions?: number;
  allConversionValue?: number;
  allConversionRate?: number;
  costPerAllConversion?: number;
  valuePerAllConversion?: number;
  crossDeviceConversions?: number;
  viewThroughConversions?: number;
  gmailForwards?: number;
  gmailSaves?: number;
  gmailSecondaryClicks?: number;
  searchImpressionShare?: number;
  searchBudgetLostImpressionShare?: number;
  searchRankLostImpressionShare?: number;
  searchTopImpressionShare?: number;
  searchAbsoluteTopImpressionShare?: number;
  contentImpressionShare?: number;
  contentBudgetLostImpressionShare?: number;
  contentRankLostImpressionShare?: number;
  absoluteTopImpressionPercentage?: number;
  topImpressionPercentage?: number;
  interactionRate?: number;
  interactions?: number;
  interactionEventTypes: string[];
  videoQuartile25Rate?: number;
  videoQuartile50Rate?: number;
  videoQuartile75Rate?: number;
  videoQuartile100Rate?: number;
  videoViewRate?: number;
  videoViews?: number;
  viewThroughConversionsFromLocationAssetClickToCall?: number;
  viewThroughConversionsFromLocationAssetDirections?: number;
  viewThroughConversionsFromLocationAssetMenu?: number;
  viewThroughConversionsFromLocationAssetOrder?: number;
  viewThroughConversionsFromLocationAssetOtherEngagement?: number;
  viewThroughConversionsFromLocationAssetStoreVisits?: number;
  viewThroughConversionsFromLocationAssetWebsite?: number;
}

export interface GoogleAdsError {
  errorCode: {
    [key: string]: string;
  };
  message: string;
  trigger?: {
    stringValue?: string;
  };
  location?: {
    fieldPathElements: Array<{
      fieldName: string;
      index?: number;
    }>;
  };
  details?: {
    [key: string]: any;
  };
}

export interface GoogleAdsResponse<T = any> {
  results?: T[];
  nextPageToken?: string;
  totalResultsCount?: number;
  fieldMask?: string;
  error?: GoogleAdsError;
}
