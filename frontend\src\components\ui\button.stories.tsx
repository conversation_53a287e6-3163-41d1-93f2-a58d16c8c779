import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { fn } from '@storybook/test'
import { Button } from './button'

/**
 * Button component following RFC-004 UI/UX Design System
 * 
 * The Button component is a fundamental UI element that supports multiple variants,
 * sizes, and states. It's designed specifically for the Turkish Natural Stone
 * Marketplace with accessibility and natural stone theming in mind.
 */
const meta = {
  title: 'UI/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
The Button component provides a consistent interface for user interactions across the platform.
It supports multiple variants that align with the natural stone marketplace branding.

## Features
- 6 variants: primary, secondary, outline, ghost, link, destructive
- 5 sizes: sm, md, lg, xl, icon
- Hover animations with transform effects
- Focus indicators for accessibility
- Support for asChild pattern with Radix UI Slot
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline', 'ghost', 'link', 'destructive'],
      description: 'Visual style variant of the button',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl', 'icon'],
      description: 'Size of the button',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the button is disabled',
    },
    asChild: {
      control: 'boolean',
      description: 'Render as child component (Radix UI Slot pattern)',
    },
    children: {
      control: 'text',
      description: 'Button content',
    },
  },
  args: {
    onClick: fn(),
    children: 'Button',
  },
} satisfies Meta<typeof Button>

export default meta
type Story = StoryObj<typeof meta>

/**
 * Default button with primary variant and medium size
 */
export const Default: Story = {
  args: {
    children: 'Default Button',
  },
}

/**
 * All button variants showcasing the natural stone theme
 */
export const Variants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="link">Link</Button>
      <Button variant="destructive">Destructive</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Different visual styles available for the button component.',
      },
    },
  },
}

/**
 * All button sizes from small to extra large
 */
export const Sizes: Story = {
  render: () => (
    <div className="flex flex-wrap items-center gap-4">
      <Button size="sm">Small</Button>
      <Button size="md">Medium</Button>
      <Button size="lg">Large</Button>
      <Button size="xl">Extra Large</Button>
      <Button size="icon">🔍</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Different sizes available for various use cases.',
      },
    },
  },
}

/**
 * Button states including disabled and loading
 */
export const States: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button>Normal</Button>
      <Button disabled>Disabled</Button>
      <Button className="opacity-75 cursor-wait">Loading</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Different states the button can be in.',
      },
    },
  },
}

/**
 * Buttons with icons demonstrating marketplace-specific use cases
 */
export const WithIcons: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button>
        🔍 Ürün Ara
      </Button>
      <Button variant="outline">
        📋 Teklif Al
      </Button>
      <Button variant="secondary">
        🏭 Üretici Ol
      </Button>
      <Button variant="ghost">
        ❤️ Favoriler
      </Button>
      <Button size="icon" variant="outline">
        🔄
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Buttons with icons for common marketplace actions.',
      },
    },
  },
}

/**
 * Responsive button behavior on different screen sizes
 */
export const Responsive: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="block sm:hidden">
        <Button className="w-full">Mobile: Full Width</Button>
      </div>
      <div className="hidden sm:block md:hidden">
        <Button>Tablet: Auto Width</Button>
      </div>
      <div className="hidden md:block">
        <Button size="lg">Desktop: Large Size</Button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Responsive behavior across different screen sizes.',
      },
    },
  },
}

/**
 * Button as a link using the asChild pattern
 */
export const AsLink: Story = {
  render: () => (
    <Button asChild>
      <a href="/products" target="_blank" rel="noopener noreferrer">
        Ürünleri Görüntüle
      </a>
    </Button>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Using the button component as a link with the asChild pattern.',
      },
    },
  },
}

/**
 * Marketplace-specific button combinations
 */
export const MarketplaceActions: Story = {
  render: () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="font-semibold">Product Actions</h3>
        <div className="flex gap-2">
          <Button variant="primary">Teklif Al</Button>
          <Button variant="outline">3D Görüntüle</Button>
          <Button variant="ghost" size="icon">❤️</Button>
        </div>
      </div>
      
      <div className="space-y-2">
        <h3 className="font-semibold">Navigation Actions</h3>
        <div className="flex gap-2">
          <Button variant="secondary">Geri</Button>
          <Button variant="primary">Devam Et</Button>
        </div>
      </div>
      
      <div className="space-y-2">
        <h3 className="font-semibold">Destructive Actions</h3>
        <div className="flex gap-2">
          <Button variant="outline">İptal</Button>
          <Button variant="destructive">Sil</Button>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Common button combinations used throughout the marketplace.',
      },
    },
  },
}

/**
 * Accessibility demonstration
 */
export const Accessibility: Story = {
  render: () => (
    <div className="space-y-4">
      <Button aria-label="Close dialog">×</Button>
      <Button aria-describedby="help-text">
        Submit Form
      </Button>
      <div id="help-text" className="text-sm text-gray-600">
        This will submit your quote request
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Accessibility features including ARIA labels and descriptions.',
      },
    },
  },
}
