/**
 * 3D Product View Types
 * Type definitions for the 3D visualization system
 */

export enum AssetType {
  MODEL_3D = 'MODEL_3D',
  TEXTURE = 'TEXTURE',
  MATERIAL = 'MATERIAL',
  ENVIRONMENT = 'ENVIRONMENT'
}

export enum AssetFormat {
  GLB = 'GLB',
  GLTF = 'GLTF',
  FBX = 'FBX',
  OBJ = 'OBJ',
  JPG = 'JPG',
  PNG = 'PNG',
  WEBP = 'WEBP',
  HDR = 'HDR',
  EXR = 'EXR'
}

export enum AssetQuality {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  ULTRA = 'ULTRA'
}

export enum ProcessingStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export interface Asset3D {
  id: string;
  productId?: string;
  name: string;
  description?: string;
  type: AssetType;
  format: AssetFormat;
  quality: AssetQuality;
  
  // File Information
  originalFileName: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  
  // 3D Model Specific
  vertices?: number;
  faces?: number;
  materials?: number;
  textures?: number;
  animations?: string[];
  
  // Texture Specific
  width?: number;
  height?: number;
  channels?: number;
  
  // Processing Information
  processingStatus: ProcessingStatus;
  processingLog?: string;
  processedAt?: Date;
  
  // Optimization Variants
  variants?: Asset3DVariant[];
  
  // Metadata
  metadata?: Record<string, any>;
  tags: string[];
  
  // Usage Statistics
  downloadCount: number;
  viewCount: number;
  lastAccessedAt?: Date;
  
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
}

export interface Asset3DVariant {
  id: string;
  assetId: string;
  quality: AssetQuality;
  
  // File Information
  fileName: string;
  filePath: string;
  fileSize: number;
  
  // 3D Model Specific (for optimized versions)
  vertices?: number;
  faces?: number;
  lodLevel?: number; // Level of Detail
  
  // Texture Specific (for different resolutions)
  width?: number;
  height?: number;
  
  // Compression
  compressionRatio?: number;
  
  // Processing
  processingStatus: ProcessingStatus;
  processedAt?: Date;
  
  createdAt: Date;
  updatedAt: Date;
}

export interface MaterialDefinition {
  id: string;
  name: string;
  description?: string;
  
  // PBR Material Properties
  baseColor?: string; // Hex color
  metallic?: number;
  roughness?: number;
  normal?: number;
  emission?: string; // Hex color
  emissionIntensity?: number;
  
  // Texture Maps
  albedoMapId?: string;
  normalMapId?: string;
  roughnessMapId?: string;
  metallicMapId?: string;
  emissionMapId?: string;
  heightMapId?: string;
  occlusionMapId?: string;
  
  // Physical Properties
  density?: number;
  hardness?: number;
  porosity?: number;
  
  // Texture Tiling
  tilingU?: number;
  tilingV?: number;
  
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
}

export interface ViewerConfiguration {
  id: string;
  productId: string;
  
  // Camera Settings
  cameraPosition: { x: number; y: number; z: number };
  cameraTarget: { x: number; y: number; z: number };
  cameraFov: number;
  
  // Lighting
  ambientLightColor: string;
  ambientLightIntensity: number;
  directionalLightColor: string;
  directionalLightIntensity: number;
  directionalLightPosition: { x: number; y: number; z: number };
  
  // Environment
  environmentMapId?: string;
  backgroundType: 'color' | 'environment' | 'transparent';
  backgroundColor: string;
  
  // Controls
  enableOrbitControls: boolean;
  enableZoom: boolean;
  enablePan: boolean;
  enableRotate: boolean;
  autoRotate: boolean;
  autoRotateSpeed: number;
  
  // Performance
  enableShadows: boolean;
  shadowMapSize: number;
  enableAntialiasing: boolean;
  pixelRatio: number;
  
  // Annotations
  enableAnnotations: boolean;
  annotations?: Annotation[];
  
  createdAt: Date;
  updatedAt: Date;
}

export interface Annotation {
  id: string;
  position: { x: number; y: number; z: number };
  title: string;
  description?: string;
  type: 'info' | 'warning' | 'feature' | 'dimension';
  visible: boolean;
}

export interface ViewerSession {
  id: string;
  sessionId: string;
  productId: string;
  userId?: string;
  
  // Session Data
  viewDuration: number; // seconds
  interactionCount: number;
  zoomCount: number;
  rotationCount: number;
  annotationViews: number;
  
  // Device Information
  userAgent?: string;
  deviceType?: 'mobile' | 'tablet' | 'desktop';
  screenResolution?: string;
  
  // Performance Metrics
  loadTime?: number; // milliseconds
  frameRate?: number;
  memoryUsage?: number; // MB
  
  startedAt: Date;
  endedAt?: Date;
}

export interface AssetUploadRequest {
  productId?: string;
  name: string;
  description?: string;
  type: AssetType;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface AssetProcessingOptions {
  generateVariants: boolean;
  qualities: AssetQuality[];
  optimizeForWeb: boolean;
  generateThumbnails: boolean;
  compressDraco?: boolean;
  textureCompression?: 'none' | 'basis' | 'astc' | 'etc2';
}

export interface ModelOptimizationResult {
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  originalVertices: number;
  optimizedVertices: number;
  processingTime: number;
  variants: Asset3DVariant[];
}

export interface TextureOptimizationResult {
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  originalResolution: { width: number; height: number };
  optimizedResolution: { width: number; height: number };
  format: AssetFormat;
  variants: Asset3DVariant[];
}

export interface ViewerAnalytics {
  totalSessions: number;
  averageViewDuration: number;
  averageInteractions: number;
  deviceBreakdown: Record<string, number>;
  popularProducts: Array<{
    productId: string;
    viewCount: number;
    averageDuration: number;
  }>;
  performanceMetrics: {
    averageLoadTime: number;
    averageFrameRate: number;
    averageMemoryUsage: number;
  };
}

export interface AssetLibrary {
  models: Asset3D[];
  textures: Asset3D[];
  materials: MaterialDefinition[];
  environments: Asset3D[];
  totalSize: number;
  totalAssets: number;
}

export interface ViewerCapabilities {
  webgl: boolean;
  webgl2: boolean;
  webgpu: boolean;
  maxTextureSize: number;
  maxVertexAttributes: number;
  supportedFormats: AssetFormat[];
  deviceMemory?: number;
  hardwareConcurrency?: number;
}

export interface LoadingProgress {
  stage: 'downloading' | 'parsing' | 'processing' | 'rendering';
  progress: number; // 0-100
  message: string;
  bytesLoaded?: number;
  bytesTotal?: number;
}

export interface ViewerError {
  code: string;
  message: string;
  details?: any;
  recoverable: boolean;
}

export interface CameraState {
  position: { x: number; y: number; z: number };
  target: { x: number; y: number; z: number };
  zoom: number;
  fov: number;
}

export interface LightingState {
  ambientIntensity: number;
  directionalIntensity: number;
  directionalPosition: { x: number; y: number; z: number };
  shadowsEnabled: boolean;
}

export interface MaterialState {
  materialId: string;
  properties: Record<string, any>;
  textureOverrides?: Record<string, string>;
}

export interface ViewerState {
  camera: CameraState;
  lighting: LightingState;
  materials: MaterialState[];
  annotations: Annotation[];
  performance: {
    frameRate: number;
    memoryUsage: number;
    drawCalls: number;
  };
}

// API Response Types
export interface AssetUploadResponse {
  success: boolean;
  assetId: string;
  uploadUrl?: string;
  message?: string;
}

export interface AssetProcessingResponse {
  success: boolean;
  jobId: string;
  estimatedTime?: number;
  message?: string;
}

export interface ViewerConfigResponse {
  success: boolean;
  configuration: ViewerConfiguration;
  assets: Asset3D[];
  materials: MaterialDefinition[];
}

export interface ViewerSessionResponse {
  success: boolean;
  sessionId: string;
  configuration: ViewerConfiguration;
  capabilities: ViewerCapabilities;
}
