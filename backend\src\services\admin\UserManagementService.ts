// RFC-501: User Management Service Implementation
import { PrismaClient, UserType, UserStatus } from '@prisma/client';
import { createClient, RedisClientType } from 'redis';

export interface UserSearchCriteria {
  query?: string;
  userType?: UserType;
  status?: UserStatus;
  countryCode?: string;
  dateFrom?: Date;
  dateTo?: Date;
  page?: number;
  limit?: number;
}

export interface UserDetails {
  id: string;
  email: string;
  userType: UserType;
  status: UserStatus;
  createdAt: Date;
  lastLoginAt?: Date;
  profile?: any;
  activityHistory: UserActivity[];
  orderHistory: OrderSummary[];
  paymentHistory: PaymentSummary[];
  supportTickets: SupportTicket[];
  riskScore: number;
}

export interface UserActivity {
  id: string;
  action: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  details?: any;
}

export interface OrderSummary {
  id: string;
  status: string;
  totalAmount: number;
  createdAt: Date;
  productName?: string;
}

export interface PaymentSummary {
  id: string;
  amount: number;
  status: string;
  method: string;
  createdAt: Date;
}

export interface SupportTicket {
  id: string;
  subject: string;
  status: string;
  priority: string;
  createdAt: Date;
  lastUpdated: Date;
}

export interface UserStatistics {
  totalUsers: number;
  usersByType: { [key: string]: number };
  usersByStatus: { [key: string]: number };
  usersByCountry: { [key: string]: number };
  registrationTrend: { date: string; count: number }[];
  activeUsersTrend: { date: string; count: number }[];
}

export class UserManagementService {
  private prisma: PrismaClient;
  private redis: RedisClientType;

  constructor() {
    this.prisma = new PrismaClient();
    this.redis = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    this.redis.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });
  }

  async connect(): Promise<void> {
    if (!this.redis.isOpen) {
      await this.redis.connect();
    }
  }

  async disconnect(): Promise<void> {
    if (this.redis.isOpen) {
      await this.redis.disconnect();
    }
    await this.prisma.$disconnect();
  }

  // Search users with advanced criteria
  async searchUsers(criteria: UserSearchCriteria): Promise<{
    users: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    await this.connect();
    
    const page = criteria.page || 1;
    const limit = criteria.limit || 20;
    const skip = (page - 1) * limit;

    const where: any = {};

    if (criteria.query) {
      where.OR = [
        { email: { contains: criteria.query, mode: 'insensitive' } },
        { profile: { companyName: { contains: criteria.query, mode: 'insensitive' } } },
        { profile: { contactPerson: { contains: criteria.query, mode: 'insensitive' } } }
      ];
    }

    if (criteria.userType) {
      where.userType = criteria.userType;
    }

    if (criteria.status) {
      where.status = criteria.status;
    }

    if (criteria.countryCode) {
      where.profile = { countryCode: criteria.countryCode };
    }

    if (criteria.dateFrom || criteria.dateTo) {
      where.createdAt = {};
      if (criteria.dateFrom) {
        where.createdAt.gte = criteria.dateFrom;
      }
      if (criteria.dateTo) {
        where.createdAt.lte = criteria.dateTo;
      }
    }

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        include: {
          profile: true
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      this.prisma.user.count({ where })
    ]);

    return {
      users,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  // Get detailed user information
  async getUserDetails(userId: string): Promise<UserDetails | null> {
    await this.connect();
    
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true,
        // orders: { // Remove if not in schema
        //   take: 10,
        //   orderBy: { createdAt: 'desc' },
        //   include: {
        //     orderItems: {
        //       include: {
        //         product: true
        //       }
        //     }
        //   }
        // },
        payments: {
          take: 10,
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    if (!user) return null;

    // Get activity history from audit logs
    const activityHistory = await this.prisma.auditLog.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 20
    });

    // Get support tickets (would be from support system)
    const supportTickets: SupportTicket[] = [];

    // Calculate risk score (simplified)
    const riskScore = await this.calculateUserRiskScore(userId);

    return {
      id: user.id,
      email: user.email,
      userType: user.userType,
      status: user.status,
      createdAt: user.createdAt,
      lastLoginAt: user.lastLoginAt || undefined,
      // profile: user.profile, // Remove if not in schema
      activityHistory: activityHistory.map(log => ({
        id: log.id,
        action: log.action,
        timestamp: log.createdAt,
        // details: log.details // Remove if not in schema
      })),
      // orderHistory: user.orders.map(order => ({ // Remove if not in schema
      //   id: order.id,
      //   status: order.status,
      //   totalAmount: order.totalAmount,
      //   createdAt: order.createdAt,
      //   productName: order.orderItems[0]?.product?.name
      // })),
      // paymentHistory: user.payments.map(payment => ({ // Remove if not in schema
      //   id: payment.id,
      //   amount: payment.amount,
      //   status: payment.status,
      //   method: payment.paymentMethod,
      //   createdAt: payment.createdAt
      // }))
      orderHistory: [], // Add empty array to satisfy interface
      paymentHistory: [], // Add empty array to satisfy interface
      supportTickets,
      riskScore
    };
  }

  // Update user status
  async updateUserStatus(userId: string, status: UserStatus, adminId: string, reason?: string): Promise<void> {
    await this.connect();
    
    await this.prisma.user.update({
      where: { id: userId },
      data: { status }
    });

    // Log the action
    await this.prisma.auditLog.create({
      data: {
        userId: adminId,
        action: `UPDATE_USER_STATUS_${status}`,
        resource: 'USER',
        resourceId: userId,
        newValues: { reason, newStatus: status }
      }
    });

    // Send notification to user
    await this.prisma.notification.create({
      data: {
        userId,
        title: `Account Status Updated`,
        message: `Your account status has been updated to ${status.toLowerCase()}${reason ? `. Reason: ${reason}` : ''}`,
        notificationType: 'SYSTEM_ANNOUNCEMENT'
      }
    });

    // Clear user cache
    await this.redis.del(`user_details_${userId}`);
  }

  // Suspend user account
  async suspendUser(userId: string, adminId: string, reason: string, duration?: number): Promise<void> {
    await this.connect();
    
    await this.updateUserStatus(userId, 'SUSPENDED', adminId, reason);

    // If duration is specified, schedule reactivation
    if (duration) {
      const reactivationDate = new Date(Date.now() + duration * 24 * 60 * 60 * 1000);
      
      // Store reactivation schedule in Redis
      await this.redis.setEx(
        `user_reactivation_${userId}`,
        duration * 24 * 60 * 60,
        JSON.stringify({ adminId, reason, reactivationDate })
      );
    }
  }

  // Get user statistics
  async getUserStatistics(): Promise<UserStatistics> {
    await this.connect();
    
    // Check cache first
    const cached = await this.redis.get('user_statistics');
    if (cached) {
      return JSON.parse(cached);
    }

    const [
      totalUsers,
      usersByType,
      usersByStatus,
      usersByCountry
    ] = await Promise.all([
      this.prisma.user.count(),
      this.prisma.user.groupBy({
        by: ['userType'],
        _count: true
      }),
      this.prisma.user.groupBy({
        by: ['status'],
        _count: true
      }),
      this.prisma.userProfile.groupBy({
        by: ['countryCode'],
        _count: true,
        where: { countryCode: { not: '' } }
      })
    ]);

    // Get registration trend for last 30 days
    const registrationTrend = await this.getRegistrationTrend(30);
    const activeUsersTrend = await this.getActiveUsersTrend(30);

    const statistics: UserStatistics = {
      totalUsers,
      usersByType: usersByType.reduce((acc, item) => {
        acc[item.userType] = item._count;
        return acc;
      }, {} as { [key: string]: number }),
      usersByStatus: usersByStatus.reduce((acc, item) => {
        acc[item.status] = item._count;
        return acc;
      }, {} as { [key: string]: number }),
      usersByCountry: usersByCountry.reduce((acc, item) => {
        acc[item.countryCode || 'Unknown'] = typeof item._count === 'number' ? item._count : 0;
        return acc;
      }, {} as { [key: string]: number }),
      registrationTrend,
      activeUsersTrend
    };

    // Cache for 10 minutes
    await this.redis.setEx('user_statistics', 600, JSON.stringify(statistics));

    return statistics;
  }

  // Calculate user risk score
  private async calculateUserRiskScore(userId: string): Promise<number> {
    // Simplified risk calculation
    let riskScore = 0;

    // Check for failed payments
    const failedPayments = await this.prisma.payment.count({
      where: { userId, status: 'FAILED' }
    });
    riskScore += failedPayments * 10;

    // Check for disputes
    const disputes = await this.prisma.dispute.count({
      where: { 
        OR: [
          { customerId: userId },
          { producerId: userId }
        ]
      }
    });
    riskScore += disputes * 20;

    // Check account age (newer accounts are riskier)
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { createdAt: true }
    });

    if (user) {
      const accountAge = Date.now() - user.createdAt.getTime();
      const daysOld = accountAge / (1000 * 60 * 60 * 24);
      if (daysOld < 30) {
        riskScore += 15;
      }
    }

    return Math.min(riskScore, 100); // Cap at 100
  }

  // Get registration trend
  private async getRegistrationTrend(days: number): Promise<{ date: string; count: number }[]> {
    const trend = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const count = await this.prisma.user.count({
        where: {
          createdAt: {
            gte: startOfDay,
            lte: endOfDay
          }
        }
      });

      trend.push({
        date: startOfDay.toISOString().split('T')[0],
        count
      });
    }

    return trend;
  }

  // Get active users trend
  private async getActiveUsersTrend(days: number): Promise<{ date: string; count: number }[]> {
    const trend = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const count = await this.prisma.user.count({
        where: {
          lastLoginAt: {
            gte: startOfDay,
            lte: endOfDay
          }
        }
      });

      trend.push({
        date: startOfDay.toISOString().split('T')[0],
        count
      });
    }

    return trend;
  }
}
