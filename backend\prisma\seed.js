"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Starting database seeding...');
    const saltRounds = 12;
    const passwordHash = await bcryptjs_1.default.hash('password', saltRounds);
    const customer = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            passwordHash,
            userType: client_1.UserType.customer,
            status: client_1.UserStatus.ACTIVE,
            companyName: 'Test Müşteri Şirketi',
            emailVerified: true,
            profile: {
                create: {
                    companyName: 'Test Müşteri Şirketi',
                    countryCode: 'TR',
                    contactPerson: 'Test Kullanıcı',
                    phone: '+90 ************',
                    address: {
                        street: 'Test Caddesi No:123',
                        city: 'İstanbul',
                        country: 'Türkiye',
                        postalCode: '34000'
                    },
                    website: 'https://testmusteri.com'
                }
            }
        },
        include: {
            profile: true
        }
    });
    const producer = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            passwordHash,
            userType: client_1.UserType.producer,
            status: client_1.UserStatus.ACTIVE,
            companyName: 'Test Üretici Şirketi',
            emailVerified: true,
            profile: {
                create: {
                    companyName: 'Test Üretici Şirketi',
                    countryCode: 'TR',
                    contactPerson: 'Test Üretici',
                    phone: '+90 ************',
                    address: {
                        street: 'Organize Sanayi Bölgesi 1. Cadde No:45',
                        city: 'Afyon',
                        country: 'Türkiye',
                        postalCode: '03000'
                    },
                    taxNumber: '1234567890',
                    tradeRegistryNumber: 'TR-123456',
                    website: 'https://testuretici.com',
                    productionCapacity: 1000,
                    businessDescription: 'Türkiye\'nin önde gelen doğal taş üreticilerinden biri',
                    offersCustomManufacturing: true,
                    customManufacturingDetails: 'Özel ölçü ve tasarım üretimi yapılmaktadır',
                    certificates: {
                        certifications: ['ISO 9001', 'CE Belgesi'],
                        quarries: [
                            {
                                name: 'Afyon Beyaz Mermer Ocağı',
                                location: 'Afyon/İscehisar',
                                capacity: '500 m³/ay',
                                stoneTypes: ['Afyon Beyaz']
                            }
                        ],
                        factories: [
                            {
                                name: 'Ana Üretim Tesisi',
                                location: 'Afyon Organize Sanayi Bölgesi',
                                capacity: '1000 m³/ay',
                                machinery: ['Köprü Kesim', 'Cilalama', 'Kalibrasyon']
                            }
                        ]
                    }
                }
            }
        },
        include: {
            profile: true
        }
    });
    const admin = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            passwordHash,
            userType: client_1.UserType.admin,
            status: client_1.UserStatus.ACTIVE,
            companyName: 'Platform Yönetimi',
            emailVerified: true,
            profile: {
                create: {
                    companyName: 'Platform Yönetimi',
                    countryCode: 'TR',
                    contactPerson: 'Admin Kullanıcı',
                    phone: '+90 ************',
                    address: {
                        street: 'Admin Caddesi No:1',
                        city: 'İstanbul',
                        country: 'Türkiye',
                        postalCode: '34000'
                    },
                    website: 'https://naturalstone-marketplace.com'
                }
            }
        },
        include: {
            profile: true
        }
    });
    const pendingProducer = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            passwordHash,
            userType: client_1.UserType.producer,
            status: client_1.UserStatus.PENDING,
            companyName: 'Bekleyen Üretici Şirketi',
            emailVerified: true,
            profile: {
                create: {
                    companyName: 'Bekleyen Üretici Şirketi',
                    countryCode: 'TR',
                    contactPerson: 'Bekleyen Üretici',
                    phone: '+90 ************',
                    address: {
                        street: 'Sanayi Caddesi No:67',
                        city: 'Denizli',
                        country: 'Türkiye',
                        postalCode: '20000'
                    },
                    taxNumber: '9876543210',
                    tradeRegistryNumber: 'TR-654321',
                    website: 'https://bekleyenuretici.com',
                    productionCapacity: 500,
                    businessDescription: 'Traverten ve oniks konusunda uzman üretici',
                    offersCustomManufacturing: false,
                    certificates: {
                        certifications: ['ISO 14001'],
                        quarries: [
                            {
                                name: 'Denizli Traverten Ocağı',
                                location: 'Denizli/Pamukkale',
                                capacity: '300 m³/ay',
                                stoneTypes: ['Denizli Traverten']
                            }
                        ],
                        factories: [
                            {
                                name: 'Traverten İşleme Tesisi',
                                location: 'Denizli Organize Sanayi Bölgesi',
                                capacity: '500 m³/ay',
                                machinery: ['Traverten Kesim', 'Yüzey İşleme']
                            }
                        ]
                    }
                }
            }
        },
        include: {
            profile: true
        }
    });
    console.log('✅ Database seeding completed successfully!');
    console.log('📊 Created users:');
    console.log(`   👤 Customer: ${customer.email} (ID: ${customer.id})`);
    console.log(`   🏭 Producer: ${producer.email} (ID: ${producer.id})`);
    console.log(`   👨‍💼 Admin: ${admin.email} (ID: ${admin.id})`);
    console.log(`   ⏳ Pending Producer: ${pendingProducer.email} (ID: ${pendingProducer.id})`);
    console.log('🔑 Default password for all users: "password"');
}
main()
    .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map