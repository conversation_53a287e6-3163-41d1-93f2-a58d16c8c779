import { PrismaClient } from '@prisma/client';

export class ProducerApprovalService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async connect(): Promise<void> {
    // Prisma handles connection automatically
  }

  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }

  // Get pending producer registrations
  async getPendingApprovals(): Promise<any[]> {
    console.log('getPendingApprovals - Starting...');
    
    try {
      const users = await this.prisma.user.findMany({
        where: {
          userType: 'producer',
          status: 'PENDING'
        },
        include: {
          profile: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      console.log(`getPendingApprovals - Found users: ${users.length}`);

      const applications = users.map(user => ({
        id: user.id,
        companyName: user.companyName || '',
        contactPerson: user.profile?.contactPerson || '',
        phone: user.profile?.phone || '',
        email: user.email,
        address: user.profile?.address,
        countryCode: user.profile?.countryCode || '',
        businessDescription: user.profile?.businessDescription || '',
        productionCapacity: user.profile?.productionCapacity || 0,
        certificates: user.profile?.certificates,
        bankInformation: user.profile?.bankInformation,
        status: 'pending' as const,
        submittedAt: user.createdAt,
        documents: this.generateDocumentsFromProfile(user.profile),
        facilities: this.generateFacilitiesFromProfile(user.profile),
        // Ek bilgiler
        companyAddress: user.profile?.companyAddress,
        companyPhone: user.profile?.companyPhone,
        companyEmail: user.profile?.companyEmail,
        website: user.profile?.website,
        foundedYear: user.profile?.foundedYear,
        employeeCount: user.profile?.employeeCount,
        productCategories: user.profile?.productCategories || [],
        certifications: user.profile?.certifications || [],
        hasQuarry: user.profile?.hasQuarry || false,
        quarries: user.profile?.quarries || [],
        factories: user.profile?.factories || [],
        providesCustomManufacturing: user.profile?.offersCustomManufacturing || false,
        customManufacturingDetails: user.profile?.customManufacturingDetails,
        companyDescription: user.profile?.businessDescription
      }));

      console.log(`getPendingApprovals - Returning ${applications.length} applications`);
      return applications;
    } catch (error) {
      console.error('getPendingApprovals - Error:', error);
      throw error;
    }
  }

  // Helper method to generate documents from profile data
  private generateDocumentsFromProfile(profile: any): any[] {
    const documents: any[] = [];
    
    // Test için geçici belgeler oluştur
    const docs = profile?.verificationDocuments || {};

    // Vergi Levhası - her zaman ekle
    documents.push({
      id: `tax_cert_${profile?.userId || 'unknown'}`,
      type: 'tax_certificate',
      url: docs.taxCertificate || '/uploads/sample-tax-certificate.pdf',
      status: docs.taxCertificate ? 'verified' : 'pending',
      verifiedAt: docs.taxCertificate ? profile?.createdAt : null
    });

    // Ticaret Sicil Belgesi - her zaman ekle
    documents.push({
      id: `trade_reg_${profile?.userId || 'unknown'}`,
      type: 'trade_registry',
      url: docs.tradeRegistry || '/uploads/sample-trade-registry.pdf',
      status: docs.tradeRegistry ? 'verified' : 'pending',
      verifiedAt: docs.tradeRegistry ? profile?.createdAt : null
    });

    // Maden İşletme Ruhsatı - ocak sahibi ise ekle
    if (profile?.hasQuarry) {
      documents.push({
        id: `mining_license_${profile?.userId || 'unknown'}`,
        type: 'mining_license',
        url: docs.miningLicense || '/uploads/sample-mining-license.pdf',
        status: docs.miningLicense ? 'verified' : 'pending',
        verifiedAt: docs.miningLicense ? profile?.createdAt : null
      });
    }

    // Üretim Kapasite Raporu - her zaman ekle
    documents.push({
      id: `capacity_report_${profile?.userId || 'unknown'}`,
      type: 'capacity_report',
      url: docs.capacityReport || '/uploads/sample-capacity-report.pdf',
      status: docs.capacityReport ? 'verified' : 'pending',
      verifiedAt: docs.capacityReport ? profile?.createdAt : null
    });

    // Sanayi Sicil Belgesi - her zaman ekle
    documents.push({
      id: `industry_reg_${profile?.userId || 'unknown'}`,
      type: 'industry_registry',
      url: docs.industryRegistry || '/uploads/sample-industry-registry.pdf',
      status: docs.industryRegistry ? 'verified' : 'pending',
      verifiedAt: docs.industryRegistry ? profile?.createdAt : null
    });

    return documents;
  }

  // Helper method to generate facilities from profile data
  private generateFacilitiesFromProfile(profile: any): any[] {
    const facilities: any[] = [];
    
    // Ocak bilgileri
    if (profile?.quarries) {
      const quarries = Array.isArray(profile.quarries) ? profile.quarries : JSON.parse(profile.quarries || '[]');
      quarries.forEach((quarry: any, index: number) => {
        facilities.push({
          id: `quarry_${profile?.userId || 'unknown'}_${index}`,
          name: quarry.name || 'Ocak',
          address: quarry.address || quarry.location || '',
          type: 'quarry',
          status: 'verified',
          inspectionDate: profile?.createdAt,
          capacity: quarry.capacity || 'Belirtilmemiş',
          description: quarry.description || ''
        });
      });
    }

    // Fabrika bilgileri
    if (profile?.factories) {
      const factories = Array.isArray(profile.factories) ? profile.factories : JSON.parse(profile.factories || '[]');
      factories.forEach((factory: any, index: number) => {
        facilities.push({
          id: `factory_${profile?.userId || 'unknown'}_${index}`,
          name: factory.name || 'Fabrika',
          address: factory.address || factory.location || '',
          type: 'factory',
          status: 'verified',
          inspectionDate: profile?.createdAt,
          capacity: factory.capacity || 'Belirtilmemiş',
          description: factory.description || ''
        });
      });
    }

    // Certificates JSON'dan da tesis bilgileri alabilir
    if (profile?.certificates) {
      const certs = profile.certificates;
      
      if (certs.quarries && Array.isArray(certs.quarries)) {
        certs.quarries.forEach((quarry: any, index: number) => {
          const existingQuarry = facilities.find(f => f.name === quarry.name && f.type === 'quarry');
          if (!existingQuarry) {
            facilities.push({
              id: `cert_quarry_${profile?.userId || 'unknown'}_${index}`,
              name: quarry.name || 'Ocak',
              address: quarry.location || '',
              type: 'quarry',
              status: 'verified',
              inspectionDate: profile?.createdAt,
              capacity: quarry.capacity || 'Belirtilmemiş',
              description: quarry.stoneTypes ? `Taş Türleri: ${quarry.stoneTypes.join(', ')}` : ''
            });
          }
        });
      }

      if (certs.factories && Array.isArray(certs.factories)) {
        certs.factories.forEach((factory: any, index: number) => {
          const existingFactory = facilities.find(f => f.name === factory.name && f.type === 'factory');
          if (!existingFactory) {
            facilities.push({
              id: `cert_factory_${profile?.userId || 'unknown'}_${index}`,
              name: factory.name || 'Fabrika',
              address: factory.location || '',
              type: 'factory',
              status: 'verified',
              inspectionDate: profile?.createdAt,
              capacity: factory.capacity || 'Belirtilmemiş',
              description: factory.machinery ? `Makineler: ${factory.machinery.join(', ')}` : ''
            });
          }
        });
      }
    }

    return facilities;
  }

  async getApprovedProducers(): Promise<any[]> {
    const users = await this.prisma.user.findMany({
      where: {
        userType: 'producer',
        status: 'APPROVED'
      },
      include: {
        profile: true
      }
    });

    return users.map(user => ({
      id: user.id,
      companyName: user.companyName,
      email: user.email,
      status: user.status,
      approvedAt: user.profile?.verifiedAt,
      profile: user.profile
    }));
  }

  async approveProducer(applicationId: string, approvalData: any): Promise<void> {
    await this.prisma.user.update({
      where: { id: applicationId },
      data: {
        status: 'approved',
        profile: {
          update: {
            verificationStatus: 'APPROVED',
            verifiedAt: new Date(),
            verifiedBy: approvalData.adminId
          }
        }
      }
    });
  }

  async rejectApplication(applicationId: string, reason: string, adminId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: applicationId },
      data: {
        status: 'rejected',
        profile: {
          update: {
            verificationStatus: 'REJECTED',
            verifiedBy: adminId
          }
        }
      }
    });
  }
}
