// RFC-501: Producer Approval Service Implementation
import { PrismaClient } from '@prisma/client';

export interface ProducerRegistration {
  id: string;
  userId: string;
  companyName: string;
  companyType: string;
  taxNumber: string;
  tradeRegistryNumber: string;
  contactPerson: string;
  phone: string;
  address: any;
  countryCode: string;
  businessDescription: string;
  productionCapacity: number;
  certificates: any;
  bankInformation: any;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Date;
  documents: DocumentVerification[];
  facilities: FacilityInspection[];
}

export interface DocumentVerification {
  id: string;
  type: 'tax_certificate' | 'trade_registry' | 'iso_certificate' | 'bank_statement';
  url: string;
  status: 'pending' | 'verified' | 'rejected';
  verifiedBy?: string;
  verifiedAt?: Date;
  rejectionReason?: string;
}

export interface FacilityInspection {
  id: string;
  type: 'quarry' | 'factory';
  name: string;
  address: string;
  googleMapsLink?: string;
  coordinates?: string;
  status: 'pending' | 'scheduled' | 'completed' | 'approved' | 'rejected';
  scheduledDate?: Date;
  inspectorId?: string;
  inspectionReport?: any;
}

export interface ApprovalData {
  approved: boolean;
  notes?: string;
  conditions?: string[];
  approvedBy: string;
}

export class ProducerApprovalService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async connect(): Promise<void> {
    // Redis removed - only PostgreSQL connection needed
    // Prisma handles connection automatically
  }

  async disconnect(): Promise<void> {
    // Redis removed - only disconnect Prisma
    await this.prisma.$disconnect();
  }

  // Get pending producer registrations
  async getPendingApprovals(): Promise<ProducerRegistration[]> {
    console.log('getPendingApprovals - Starting database query');

    // Get from database directly (Redis removed for stability)
    const pendingUsers = await this.prisma.user.findMany({
      where: {
        userType: 'producer',
        status: 'PENDING'
      },
      include: {
        profile: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    console.log('getPendingApprovals - Found users:', pendingUsers.length);

    const registrations: ProducerRegistration[] = pendingUsers.map((user: any) => ({
      id: user.id,
      userId: user.id,
      companyName: user.profile?.companyName || '',
      companyType: user.profile?.companyType || '',
      taxNumber: user.profile?.taxNumber || '',
      tradeRegistryNumber: user.profile?.tradeRegistryNumber || '',
      contactPerson: user.profile?.contactPerson || '',
      phone: user.profile?.phone || '',
      address: user.profile?.address,
      countryCode: user.profile?.countryCode || '',
      businessDescription: user.profile?.businessDescription || '',
      productionCapacity: user.profile?.productionCapacity || 0,
      certificates: user.profile?.certificates,
      bankInformation: user.profile?.bankInformation,
      status: 'pending',
      submittedAt: user.createdAt,
      documents: [], // Would be populated from documents table
      facilities: [] // Would be populated from producer_locations table
    }));

    console.log('getPendingApprovals - Returning registrations:', registrations.length);
    return registrations;
  }

  // Review producer application
  async reviewApplication(applicationId: string): Promise<ProducerRegistration | null> {
    const user = await this.prisma.user.findUnique({
      where: { id: applicationId },
      include: {
        profile: true
      }
    });

    if (!user || !user.profile) return null;

    return {
      id: user.id,
      userId: user.id,
      companyName: user.profile.companyName,
      companyType: user.profile.companyType || '',
      taxNumber: user.profile.taxNumber || '',
      tradeRegistryNumber: user.profile.tradeRegistryNumber || '',
      contactPerson: user.profile.contactPerson || '',
      phone: user.profile.phone || '',
      address: user.profile.address,
      countryCode: user.profile.countryCode,
      businessDescription: user.profile.businessDescription || '',
      productionCapacity: user.profile.productionCapacity || 0,
      certificates: user.profile.certificates,
      bankInformation: user.profile.bankInformation,
      status: user.status.toLowerCase() as any,
      submittedAt: user.createdAt,
      documents: [],
      facilities: []
    };
  }

  // Request additional documents
  async requestAdditionalDocuments(applicationId: string, documents: string[]): Promise<void> {
    
    // Create notification for producer
    await this.prisma.notification.create({
      data: {
        userId: applicationId,
        title: 'Additional Documents Required',
        message: `Please provide the following documents: ${documents.join(', ')}`,
        notificationType: 'SYSTEM_ANNOUNCEMENT'
      }
    });

    // Cache cleared (Redis removed)
  }

  // Schedule facility inspection
  async scheduleInspection(applicationId: string, inspectionData: {
    facilityId: string;
    scheduledDate: Date;
    inspectorId: string;
    notes?: string;
  }): Promise<void> {

    // Update facility inspection status
    // This would update the producer_locations table
    
    // Create notification
    await this.prisma.notification.create({
      data: {
        userId: applicationId,
        title: 'Facility Inspection Scheduled',
        message: `Your facility inspection has been scheduled for ${inspectionData.scheduledDate.toLocaleDateString()}`,
        notificationType: 'SYSTEM_ANNOUNCEMENT'
      }
    });

    // Cache cleared (Redis removed)
  }

  // Approve producer
  async approveProducer(applicationId: string, approvalData: ApprovalData): Promise<void> {

    // Update user status
    await this.prisma.user.update({
      where: { id: applicationId },
      data: {
        status: 'ACTIVE'
      }
    });

    // Update profile verification
    await this.prisma.userProfile.update({
      where: { userId: applicationId },
      data: {
        verificationStatus: 'VERIFIED',
        verifiedAt: new Date(),
        verifiedBy: approvalData.approvedBy
      }
    });

    // Create approval notification
    await this.prisma.notification.create({
      data: {
        userId: applicationId,
        title: 'Producer Application Approved',
        message: 'Congratulations! Your producer application has been approved. You can now start listing your products.',
        notificationType: 'SYSTEM_ANNOUNCEMENT'
      }
    });

    // Log approval action (optional - skip if admin user not in User table)
    try {
      await this.prisma.auditLog.create({
        data: {
          userId: approvalData.approvedBy,
          action: 'APPROVE_PRODUCER',
          resource: 'USER',
          resourceId: applicationId,
          newValues: {
            notes: approvalData.notes,
            conditions: approvalData.conditions
          }
        }
      });
    } catch (error) {
      console.warn('Could not create audit log for approval (admin user not in User table):', error.message);
    }

    // Cache cleared (Redis removed)

    // Send approval email
    await this.sendApprovalEmail(applicationId);
  }

  // Send approval email notification
  private async sendApprovalEmail(applicationId: string): Promise<void> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: applicationId },
        include: { profile: true }
      });

      if (!user) return;

      const emailSubject = 'Üretici Üyeliğiniz Onaylandı - Doğal Taş Marketplace';
      const emailBody = `
        Sayın ${user.profile?.companyName || user.email},

        Tebrikler! Doğal Taş Marketplace platformundaki üretici üyelik başvurunuz onaylanmıştır.

        Artık platformumuza giriş yaparak:
        • Ürünlerinizi listeleyebilir
        • Teklif taleplerini görüntüleyebilir
        • Siparişlerinizi yönetebilir
        • Satış analizlerinizi takip edebilirsiniz

        Giriş yapmak için: ${process.env.FRONTEND_URL || 'http://localhost:3000'}/producer/login

        Herhangi bir sorunuz olursa destek ekibimizle iletişime geçebilirsiniz.

        İyi çalışmalar dileriz,
        Doğal Taş Marketplace Ekibi
      `;

      // Mock email sending - gerçek implementasyonda SendGrid, AWS SES vb. kullanılacak
      console.log(`📧 Approval email sent to: ${user.email}`);
      console.log(`Subject: ${emailSubject}`);
      console.log(`Body: ${emailBody}`);

    } catch (error) {
      console.error('Error sending approval email:', error);
    }
  }

  // Reject producer application
  async rejectApplication(applicationId: string, reason: string, rejectedBy: string): Promise<void> {

    // Update user status
    await this.prisma.user.update({
      where: { id: applicationId },
      data: {
        status: 'SUSPENDED'
      }
    });

    // Update profile verification
    await this.prisma.userProfile.update({
      where: { userId: applicationId },
      data: {
        verificationStatus: 'REJECTED',
        verifiedAt: new Date(),
        verifiedBy: rejectedBy
      }
    });

    // Create rejection notification
    await this.prisma.notification.create({
      data: {
        userId: applicationId,
        title: 'Producer Application Rejected',
        message: `Your producer application has been rejected. Reason: ${reason}`,
        notificationType: 'SYSTEM_ANNOUNCEMENT'
      }
    });

    // Log rejection action (optional - skip if admin user not in User table)
    try {
      await this.prisma.auditLog.create({
        data: {
          userId: rejectedBy,
          action: 'REJECT_PRODUCER',
          resource: 'USER',
          resourceId: applicationId,
          newValues: { reason }
        }
      });
    } catch (error) {
      console.warn('Could not create audit log for rejection (admin user not in User table):', error.message);
    }

    // Cache cleared (Redis removed)

    // Send rejection email
    await this.sendRejectionEmail(applicationId, reason);
  }

  // Send rejection email notification
  private async sendRejectionEmail(applicationId: string, reason: string): Promise<void> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: applicationId },
        include: { profile: true }
      });

      if (!user) return;

      const emailSubject = 'Üretici Üyelik Başvurunuz Hakkında - Doğal Taş Marketplace';
      const emailBody = `
        Sayın ${user.profile?.companyName || user.email},

        Doğal Taş Marketplace platformundaki üretici üyelik başvurunuz incelenmiştir.

        Maalesef başvurunuz aşağıdaki nedenle reddedilmiştir:
        ${reason}

        Eksiklikleri tamamladıktan sonra yeniden başvuru yapabilirsiniz.

        Herhangi bir sorunuz olursa destek ekibimizle iletişime geçebilirsiniz.

        Saygılarımızla,
        Doğal Taş Marketplace Ekibi
      `;

      // Mock email sending - gerçek implementasyonda SendGrid, AWS SES vb. kullanılacak
      console.log(`📧 Rejection email sent to: ${user.email}`);
      console.log(`Subject: ${emailSubject}`);
      console.log(`Body: ${emailBody}`);

    } catch (error) {
      console.error('Error sending rejection email:', error);
    }
  }

  // Get approval statistics
  async getApprovalStatistics(): Promise<{
    pendingCount: number;
    approvedThisMonth: number;
    rejectedThisMonth: number;
    averageApprovalTime: number; // in days
  }> {

    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [pendingCount, approvedThisMonth, rejectedThisMonth] = await Promise.all([
      this.prisma.user.count({
        where: {
          userType: 'producer',
          status: 'PENDING'
        }
      }),
      this.prisma.userProfile.count({
        where: {
          verificationStatus: 'VERIFIED',
          verifiedAt: {
            gte: startOfMonth
          }
        }
      }),
      this.prisma.userProfile.count({
        where: {
          verificationStatus: 'REJECTED',
          verifiedAt: {
            gte: startOfMonth
          }
        }
      })
    ]);

    // Calculate average approval time (simplified)
    const averageApprovalTime = 3; // Would calculate from actual data

    return {
      pendingCount,
      approvedThisMonth,
      rejectedThisMonth,
      averageApprovalTime
    };
  }
}
