'use client';

import React from 'react';
import CancelledRequestsPage from '@/components/dashboard/pages/CancelledRequestsPage';
import { useRouter } from 'next/navigation';

export default function CustomerCancelledRequestsPage() {
  const router = useRouter();
  
  const handleNavigate = (route: string) => {
    router.push(route);
  };

  return <CancelledRequestsPage onNavigate={handleNavigate} />;
}
