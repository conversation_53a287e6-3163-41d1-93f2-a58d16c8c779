'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ClipboardDocumentListIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  EyeIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  BeakerIcon
} from '@heroicons/react/24/outline';
import { useQuote, QuoteRequest } from '@/contexts/quote-context';
import { SampleProvider } from '@/contexts/sample-context';
import SampleRequestModal from '@/components/ui/sample-request-modal';

interface ActiveRequestsPageProps {
  onNavigate?: (route: string) => void;
}

const ActiveRequestsPage: React.FC<ActiveRequestsPageProps> = ({ onNavigate }) => {
  const { customerQuoteRequests, isLoading, updateQuoteRequestStatus, getQuotesByRequestId, acceptQuote, rejectQuote } = useQuote();
  const [cancelModalOpen, setCancelModalOpen] = React.useState(false);
  const [selectedRequest, setSelectedRequest] = React.useState<QuoteRequest | null>(null);
  const [cancelReason, setCancelReason] = React.useState('');
  const [expandedRequests, setExpandedRequests] = React.useState<Set<string>>(new Set());
  const [sampleModalOpen, setSampleModalOpen] = React.useState(false);
  const [selectedQuoteForSample, setSelectedQuoteForSample] = React.useState<any>(null);

  // Sadece aktif talepleri filtrele
  const activeRequests = customerQuoteRequests.filter(request =>
    ['pending', 'quoted'].includes(request.status)
  );

  const handleCancelRequest = (request: QuoteRequest) => {
    setSelectedRequest(request);
    setCancelModalOpen(true);
  };

  const confirmCancelRequest = async () => {
    if (!selectedRequest || !cancelReason.trim()) {
      alert('Lütfen iptal sebebini belirtin.');
      return;
    }

    try {
      // Backend'e iptal isteği gönder ve durumu güncelle
      console.log('Cancelling request:', selectedRequest.id, 'Reason:', cancelReason);

      // Talep durumunu 'cancelled' olarak güncelle
      await updateQuoteRequestStatus(selectedRequest.id, 'cancelled');

      // Modal'ı kapat ve formu temizle
      setCancelModalOpen(false);
      setSelectedRequest(null);
      setCancelReason('');

      alert('Talep başarıyla iptal edildi.');

      // İptal edilen talepler sayfasına yönlendir
      onNavigate?.('/customer/requests/cancelled');
    } catch (error) {
      console.error('Error cancelling request:', error);
      alert('Talep iptal edilirken hata oluştu.');
    }
  };

  const handleAcceptQuote = async (quoteId: string, request: QuoteRequest) => {
    try {
      // Ön ödeme işlemi simülasyonu
      const confirmed = confirm('Teklifi kabul edip ön ödeme yapmak istediğinizden emin misiniz?');
      if (!confirmed) return;

      // Backend'e kabul isteği gönder
      await acceptQuote(quoteId);
      console.log('Accepting quote:', quoteId, 'for request:', request.id);

      // Sipariş oluştur ve ongoing orders sayfasına yönlendir
      onNavigate?.('/customer/orders/ongoing');

      alert('Teklif kabul edildi ve sipariş oluşturuldu!');
    } catch (error) {
      console.error('Error accepting quote:', error);
      alert('Teklif kabul edilirken hata oluştu.');
    }
  };

  const handleRejectQuote = async (quoteId: string) => {
    try {
      const confirmed = confirm('Bu teklifi reddetmek istediğinizden emin misiniz?');
      if (!confirmed) return;

      await rejectQuote(quoteId);
      alert('Teklif reddedildi.');
    } catch (error) {
      console.error('Error rejecting quote:', error);
      alert('Teklif reddedilirken hata oluştu.');
    }
  };

  const handleRequestSample = (quote: any, request: QuoteRequest) => {
    setSelectedQuoteForSample({ quote, request });
    setSampleModalOpen(true);
  };

  const toggleRequestExpansion = (requestId: string) => {
    setExpandedRequests(prev => {
      const newSet = new Set(prev);
      if (newSet.has(requestId)) {
        newSet.delete(requestId);
      } else {
        newSet.add(requestId);
      }
      return newSet;
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'quoted':
        return <ChatBubbleLeftRightIcon className="h-5 w-5 text-blue-500" />;
      default:
        return <ClipboardDocumentListIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor';
      case 'quoted':
        return 'Teklif Alındı';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'quoted':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Aktif Talepler</h1>
          <p className="text-gray-600 mt-1">Bekleyen ve teklif alınan talepleriniz</p>
        </div>
        <div className="bg-blue-50 px-4 py-2 rounded-lg">
          <p className="text-sm text-blue-600 font-medium">
            {activeRequests.length} aktif talep
          </p>
        </div>
      </div>

      {/* Navigation Links */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => onNavigate?.('/customer/requests/active')}
            className="py-2 px-1 border-b-2 border-stone-500 text-stone-600 font-medium text-sm whitespace-nowrap"
          >
            Aktif Talepler
            {activeRequests.length > 0 && (
              <span className="ml-2 py-0.5 px-2 rounded-full text-xs bg-stone-100 text-stone-600">
                {activeRequests.length}
              </span>
            )}
          </button>
          <button
            onClick={() => onNavigate?.('/customer/requests/cancelled')}
            className="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm whitespace-nowrap"
          >
            İptal Edilen
            {customerQuoteRequests.filter(r => ['rejected', 'cancelled'].includes(r.status)).length > 0 && (
              <span className="ml-2 py-0.5 px-2 rounded-full text-xs bg-gray-100 text-gray-600">
                {customerQuoteRequests.filter(r => ['rejected', 'cancelled'].includes(r.status)).length}
              </span>
            )}
          </button>
        </nav>
      </div>

      {/* Requests List */}
      {activeRequests.length > 0 ? (
        <div className="space-y-6">
          {activeRequests.map((request, index) => (
            <motion.div
              key={request.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200"
            >
              {/* Request Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(request.status)}
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(request.status)}`}>
                      {getStatusLabel(request.status)}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {request.id}. Talep
                    </h3>
                    <p className="text-sm text-gray-600">
                      {request.createdAt && new Date(request.createdAt).toLocaleDateString('tr-TR')}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Alınan Teklif</p>
                  <p className="text-lg font-semibold text-blue-600">
                    {request.quotes?.length || 0}
                  </p>
                </div>
              </div>

              {/* Products */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Ürünler</h4>
                <div className="space-y-2">
                  {request.products.map((product) => (
                    <div key={product.id} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                      <div>
                        <p className="font-medium text-gray-900">{product.productName}</p>
                        <p className="text-sm text-gray-600">{product.productCategory}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {product.specifications?.length || 0} farklı ebat
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {product.specifications?.[0]?.area ? `${product.specifications[0].area} m²` : 'Belirtilmemiş'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {product.specifications?.[0]?.thickness ? `${product.specifications[0].thickness} cm` : ''}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Message */}
              {request.message && (
                <div className="mb-4 bg-blue-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-900 mb-2">Mesaj</h4>
                  <p className="text-sm text-blue-800">{request.message}</p>
                </div>
              )}

              {/* Received Quotes */}
              {request.status === 'quoted' && (
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-900">
                      Gelen Teklifler ({getQuotesByRequestId(request.id).length})
                    </h4>
                    <button
                      onClick={() => toggleRequestExpansion(request.id)}
                      className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                    >
                      {expandedRequests.has(request.id) ? 'Gizle' : 'Teklifleri Göster'}
                    </button>
                  </div>

                  {expandedRequests.has(request.id) && (
                    <div className="space-y-3">
                      {getQuotesByRequestId(request.id).map((quote) => (
                        <div key={quote.id} className="bg-green-50 border border-green-200 rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <h5 className="font-medium text-green-900">{quote.producerCompany}</h5>
                                <span className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full">
                                  {quote.status === 'pending' ? 'Bekliyor' : quote.status}
                                </span>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                  <span className="text-green-600">Toplam Tutar:</span>
                                  <span className="ml-2 font-medium">
                                    {quote.totalAmount.toLocaleString()} {quote.currency}
                                  </span>
                                </div>
                                <div>
                                  <span className="text-green-600">Geçerlilik:</span>
                                  <span className="ml-2 font-medium">
                                    {quote.validUntil.toLocaleDateString('tr-TR')}
                                  </span>
                                </div>
                                <div>
                                  <span className="text-green-600">Teklif Tarihi:</span>
                                  <span className="ml-2 font-medium">
                                    {quote.createdAt.toLocaleDateString('tr-TR')}
                                  </span>
                                </div>
                              </div>
                              {quote.terms && (
                                <div className="mt-2">
                                  <span className="text-green-600 text-sm">Şartlar:</span>
                                  <p className="text-sm text-green-800 mt-1">{quote.terms}</p>
                                </div>
                              )}
                            </div>
                            <div className="flex flex-col gap-2 ml-4">
                              <button
                                onClick={() => handleRequestSample(quote, request)}
                                className="px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200 flex items-center gap-1"
                              >
                                <BeakerIcon className="h-4 w-4" />
                                Numune İste
                              </button>
                              <button
                                onClick={() => handleAcceptQuote(quote.id, request)}
                                className="px-3 py-1 text-sm font-medium text-white bg-green-600 rounded hover:bg-green-700"
                              >
                                Kabul Et
                              </button>
                              <button
                                onClick={() => handleRejectQuote(quote.id)}
                                className="px-3 py-1 text-sm font-medium text-red-700 bg-red-100 rounded hover:bg-red-200"
                              >
                                Reddet
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}



              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => onNavigate?.(`/customer/requests/detail/${request.id}`)}
                    className="flex items-center space-x-2 text-blue-600 hover:text-blue-700"
                  >
                    <EyeIcon className="h-4 w-4" />
                    <span className="text-sm font-medium">Detay</span>
                  </button>
                  {request.status === 'quoted' && (
                    <button
                      className="flex items-center space-x-2 text-green-600 hover:text-green-700"
                    >
                      <ChatBubbleLeftRightIcon className="h-4 w-4" />
                      <span className="text-sm font-medium">Üreticiye Soru Sor</span>
                    </button>
                  )}
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleCancelRequest(request)}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-red-700 bg-red-100 rounded-lg hover:bg-red-200"
                  >
                    <XMarkIcon className="h-4 w-4" />
                    <span>İptal Et</span>
                  </button>

                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <ClipboardDocumentListIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Aktif talebiniz bulunmuyor
          </h3>
          <p className="text-gray-600 mb-6">
            Yeni bir teklif talebi oluşturarak başlayabilirsiniz.
          </p>
          <button
            onClick={() => onNavigate?.('/customer/dashboard')}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Yeni Teklif İste
          </button>
        </div>
      )}

      {/* Cancel Modal */}
      {cancelModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center mb-4">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-500 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Talebi İptal Et</h3>
            </div>

            <p className="text-gray-600 mb-4">
              Bu talebi iptal etmek istediğinizden emin misiniz? İptal sebebinizi belirtiniz:
            </p>

            <textarea
              value={cancelReason}
              onChange={(e) => setCancelReason(e.target.value)}
              placeholder="İptal sebebini yazınız..."
              className="w-full p-3 border border-gray-300 rounded-lg resize-none h-24 mb-4"
              required
            />

            <div className="flex items-center justify-end space-x-3">
              <button
                onClick={() => {
                  setCancelModalOpen(false);
                  setSelectedRequest(null);
                  setCancelReason('');
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
              >
                Vazgeç
              </button>
              <button
                onClick={confirmCancelRequest}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700"
              >
                İptal Et
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Sample Request Modal */}
      {sampleModalOpen && selectedQuoteForSample && (
        <SampleRequestModal
          isOpen={sampleModalOpen}
          onClose={() => {
            setSampleModalOpen(false);
            setSelectedQuoteForSample(null);
          }}
          quoteRequestId={selectedQuoteForSample.request.id}
          quoteId={selectedQuoteForSample.quote.id}
          customerId="1" // Should be actual customer ID
          producerId={selectedQuoteForSample.quote.producerId}
          products={selectedQuoteForSample.request.products.map((p: any) => ({
            id: p.productId,
            name: p.productName,
            category: p.productCategory
          }))}
        />
      )}
    </div>
  );
};

const ActiveRequestsPageWithProvider: React.FC<ActiveRequestsPageProps> = (props) => {
  return (
    <SampleProvider>
      <ActiveRequestsPage {...props} />
    </SampleProvider>
  );
};

export default ActiveRequestsPageWithProvider;
