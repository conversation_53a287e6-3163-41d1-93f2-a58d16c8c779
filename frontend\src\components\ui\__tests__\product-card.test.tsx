import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { axe, toHaveNoViolations } from 'jest-axe'
import ProductCard from '../product-card'

expect.extend(toHaveNoViolations)

const mockProduct = {
  id: 'test-product-1',
  name: 'Carrara Beyaz Mermer',
  category: 'Mermer',
  price: { min: 45, max: 65, currency: '$', unit: 'm²' },
  image: '/test-image.jpg',
  rating: 4.8,
  reviewCount: 127,
  location: 'Afyon, Türkiye',
  producer: 'ABC Mermer Ltd.',
  features: ['Cilalı', '60x60cm', '2cm Kalınlık', 'Extra Feature']
}

describe('ProductCard Component', () => {
  // Basic rendering tests
  describe('Rendering', () => {
    it('renders with required product data', () => {
      render(<ProductCard product={mockProduct} />)
      
      expect(screen.getByText('Carrara Beyaz Mermer')).toBeInTheDocument()
      expect(screen.getByText('Mermer')).toBeInTheDocument()
      expect(screen.getByText('$45-65')).toBeInTheDocument()
      expect(screen.getByText('/m²')).toBeInTheDocument()
    })

    it('renders product image with correct attributes', () => {
      render(<ProductCard product={mockProduct} />)
      const image = screen.getByRole('img')
      
      expect(image).toHaveAttribute('src', '/test-image.jpg')
      expect(image).toHaveAttribute('alt', 'Carrara Beyaz Mermer')
      expect(image).toHaveAttribute('loading', 'lazy')
    })

    it('renders rating and review count when provided', () => {
      render(<ProductCard product={mockProduct} />)
      
      expect(screen.getByText('(4.8)')).toBeInTheDocument()
      expect(screen.getByText('127 değerlendirme')).toBeInTheDocument()
      
      // Check for star ratings
      const stars = screen.getAllByText('⭐')
      expect(stars).toHaveLength(5) // 5 stars total
    })

    it('renders location and producer when provided', () => {
      render(<ProductCard product={mockProduct} />)
      
      expect(screen.getByText('📍')).toBeInTheDocument()
      expect(screen.getByText('Afyon, Türkiye')).toBeInTheDocument()
      expect(screen.getByText('🏭')).toBeInTheDocument()
      expect(screen.getByText('ABC Mermer Ltd.')).toBeInTheDocument()
    })

    it('renders features correctly', () => {
      render(<ProductCard product={mockProduct} />)
      
      expect(screen.getByText('Cilalı')).toBeInTheDocument()
      expect(screen.getByText('60x60cm')).toBeInTheDocument()
      expect(screen.getByText('2cm Kalınlık')).toBeInTheDocument()
      expect(screen.getByText('+1 daha')).toBeInTheDocument() // Shows +1 more for 4th feature
    })
  })

  // Interaction tests
  describe('Interactions', () => {
    it('calls onViewDetails when card is clicked', async () => {
      const user = userEvent.setup()
      const handleViewDetails = jest.fn()
      
      render(<ProductCard product={mockProduct} onViewDetails={handleViewDetails} />)
      
      const card = screen.getByRole('button', { name: /carrara beyaz mermer/i })
      await user.click(card)
      
      expect(handleViewDetails).toHaveBeenCalledWith('test-product-1')
    })

    it('calls onRequestQuote when quote button is clicked', async () => {
      const user = userEvent.setup()
      const handleRequestQuote = jest.fn()
      
      render(<ProductCard product={mockProduct} onRequestQuote={handleRequestQuote} />)
      
      const quoteButton = screen.getByRole('button', { name: /teklif al/i })
      await user.click(quoteButton)
      
      expect(handleRequestQuote).toHaveBeenCalledWith('test-product-1')
    })

    it('calls onToggleFavorite when favorite button is clicked', async () => {
      const user = userEvent.setup()
      const handleToggleFavorite = jest.fn()
      
      render(<ProductCard product={mockProduct} onToggleFavorite={handleToggleFavorite} />)
      
      // Hover to show the favorite button
      const card = screen.getByRole('button', { name: /carrara beyaz mermer/i })
      await user.hover(card)
      
      const favoriteButton = screen.getByRole('button', { name: /🤍/i })
      await user.click(favoriteButton)
      
      expect(handleToggleFavorite).toHaveBeenCalledWith('test-product-1')
    })

    it('shows favorite as filled when isFavorite is true', () => {
      render(<ProductCard product={mockProduct} isFavorite={true} />)
      
      // The favorite button should show filled heart
      expect(screen.getByText('❤️')).toBeInTheDocument()
    })

    it('prevents event bubbling on button clicks', async () => {
      const user = userEvent.setup()
      const handleViewDetails = jest.fn()
      const handleRequestQuote = jest.fn()
      
      render(
        <ProductCard 
          product={mockProduct} 
          onViewDetails={handleViewDetails}
          onRequestQuote={handleRequestQuote}
        />
      )
      
      const quoteButton = screen.getByRole('button', { name: /teklif al/i })
      await user.click(quoteButton)
      
      // Only quote handler should be called, not view details
      expect(handleRequestQuote).toHaveBeenCalledTimes(1)
      expect(handleViewDetails).not.toHaveBeenCalled()
    })
  })

  // 3D Viewer tests
  describe('3D Viewer', () => {
    it('shows 3D viewer button when show3DViewer is true', () => {
      render(<ProductCard product={mockProduct} show3DViewer={true} />)
      
      expect(screen.getByText('🔄 3D Görünüm')).toBeInTheDocument()
    })

    it('hides 3D viewer button when show3DViewer is false', () => {
      render(<ProductCard product={mockProduct} show3DViewer={false} />)
      
      expect(screen.queryByText('🔄 3D Görünüm')).not.toBeInTheDocument()
    })

    it('3D viewer button prevents event bubbling', async () => {
      const user = userEvent.setup()
      const handleViewDetails = jest.fn()
      
      render(
        <ProductCard 
          product={mockProduct} 
          onViewDetails={handleViewDetails}
          show3DViewer={true}
        />
      )
      
      // Hover to show 3D button
      const card = screen.getByRole('button', { name: /carrara beyaz mermer/i })
      await user.hover(card)
      
      const viewer3DButton = screen.getByRole('button', { name: /3d görünüm/i })
      await user.click(viewer3DButton)
      
      // View details should not be called
      expect(handleViewDetails).not.toHaveBeenCalled()
    })
  })

  // Price display tests
  describe('Price Display', () => {
    it('displays price range correctly', () => {
      render(<ProductCard product={mockProduct} />)
      
      expect(screen.getByText('$45-65')).toBeInTheDocument()
      expect(screen.getByText('/m²')).toBeInTheDocument()
    })

    it('displays single price when min equals max', () => {
      const singlePriceProduct = {
        ...mockProduct,
        price: { min: 50, max: 50, currency: '$', unit: 'm²' }
      }
      
      render(<ProductCard product={singlePriceProduct} />)
      
      expect(screen.getByText('$50')).toBeInTheDocument()
      expect(screen.queryByText('$50-50')).not.toBeInTheDocument()
    })

    it('handles different currencies', () => {
      const euroProduct = {
        ...mockProduct,
        price: { min: 40, max: 60, currency: '€', unit: 'm²' }
      }
      
      render(<ProductCard product={euroProduct} />)
      
      expect(screen.getByText('€40-60')).toBeInTheDocument()
    })
  })

  // Hover effects tests
  describe('Hover Effects', () => {
    it('applies hover styles on mouse enter', async () => {
      const user = userEvent.setup()
      render(<ProductCard product={mockProduct} />)
      
      const card = screen.getByRole('button', { name: /carrara beyaz mermer/i })
      
      await user.hover(card)
      
      // Check if hover classes are applied
      expect(card).toHaveClass('group')
      expect(card).toHaveClass('hover:transform')
    })

    it('shows overlay actions on hover', async () => {
      const user = userEvent.setup()
      render(<ProductCard product={mockProduct} show3DViewer={true} />)
      
      const card = screen.getByRole('button', { name: /carrara beyaz mermer/i })
      
      await user.hover(card)
      
      // 3D viewer button should become visible
      const viewer3DButton = screen.getByRole('button', { name: /3d görünüm/i })
      expect(viewer3DButton).toHaveClass('group-hover:opacity-100')
    })
  })

  // Accessibility tests
  describe('Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(<ProductCard product={mockProduct} />)
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('has proper ARIA labels and roles', () => {
      render(<ProductCard product={mockProduct} />)
      
      const card = screen.getByRole('button')
      expect(card).toHaveAttribute('role', 'button')
      
      const image = screen.getByRole('img')
      expect(image).toHaveAttribute('alt', 'Carrara Beyaz Mermer')
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      const handleViewDetails = jest.fn()
      
      render(<ProductCard product={mockProduct} onViewDetails={handleViewDetails} />)
      
      const card = screen.getByRole('button')
      
      await user.tab()
      expect(card).toHaveFocus()
      
      await user.keyboard('{Enter}')
      expect(handleViewDetails).toHaveBeenCalledWith('test-product-1')
    })

    it('has proper focus indicators', () => {
      render(<ProductCard product={mockProduct} />)
      
      const card = screen.getByRole('button')
      expect(card).toHaveClass('focus-visible:outline-none')
    })
  })

  // Edge cases
  describe('Edge Cases', () => {
    it('handles missing optional data gracefully', () => {
      const minimalProduct = {
        id: 'minimal',
        name: 'Basic Product',
        category: 'Stone',
        price: { min: 10, max: 20, currency: '$', unit: 'm²' },
        image: '/basic.jpg'
      }
      
      render(<ProductCard product={minimalProduct} />)
      
      expect(screen.getByText('Basic Product')).toBeInTheDocument()
      expect(screen.queryByText('⭐')).not.toBeInTheDocument()
      expect(screen.queryByText('📍')).not.toBeInTheDocument()
    })

    it('handles long product names correctly', () => {
      const longNameProduct = {
        ...mockProduct,
        name: 'Very Long Product Name That Should Be Truncated Properly'
      }
      
      render(<ProductCard product={longNameProduct} />)
      
      const title = screen.getByText('Very Long Product Name That Should Be Truncated Properly')
      expect(title).toHaveClass('line-clamp-2')
    })

    it('handles many features correctly', () => {
      const manyFeaturesProduct = {
        ...mockProduct,
        features: ['Feature 1', 'Feature 2', 'Feature 3', 'Feature 4', 'Feature 5']
      }
      
      render(<ProductCard product={manyFeaturesProduct} />)
      
      expect(screen.getByText('Feature 1')).toBeInTheDocument()
      expect(screen.getByText('Feature 2')).toBeInTheDocument()
      expect(screen.getByText('Feature 3')).toBeInTheDocument()
      expect(screen.getByText('+2 daha')).toBeInTheDocument()
    })

    it('forwards ref correctly', () => {
      const ref = React.createRef<HTMLDivElement>()
      render(<ProductCard ref={ref} product={mockProduct} />)
      
      expect(ref.current).toBeInstanceOf(HTMLDivElement)
    })
  })
})
