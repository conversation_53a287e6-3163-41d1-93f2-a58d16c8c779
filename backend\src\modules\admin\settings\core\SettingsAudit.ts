// Settings Audit Service - RFC-012
import { SettingsAuditLog, SettingsCategory, SettingValue } from '../types/settings.types';

export interface AuditLogEntry {
  settingId: string;
  category: SettingsCategory;
  key: string;
  oldValue: SettingValue;
  newValue: SettingValue;
  changedBy: string;
  changeReason?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface AuditQueryOptions {
  category?: SettingsCategory;
  key?: string;
  changedBy?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

export class SettingsAuditService {
  private auditLogs: SettingsAuditLog[] = [];

  /**
   * Log a setting change
   */
  async logChange(entry: AuditLogEntry): Promise<void> {
    try {
      const auditLog: SettingsAuditLog = {
        id: this.generateId(),
        settingId: entry.settingId,
        category: entry.category,
        key: entry.key,
        oldValue: entry.oldValue,
        newValue: entry.newValue,
        changedBy: entry.changedBy,
        changeReason: entry.changeReason,
        ipAddress: entry.ipAddress,
        userAgent: entry.userAgent,
        createdAt: new Date()
      };

      // In a real implementation, this would be saved to database
      this.auditLogs.push(auditLog);

      console.log('Settings change logged:', {
        category: entry.category,
        key: entry.key,
        changedBy: entry.changedBy,
        timestamp: auditLog.createdAt
      });

    } catch (error) {
      console.error('Error logging settings change:', error);
      throw new Error('Failed to log settings change');
    }
  }

  /**
   * Get audit logs
   */
  async getAuditLogs(options: AuditQueryOptions = {}): Promise<{
    logs: SettingsAuditLog[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      let filteredLogs = [...this.auditLogs];

      // Apply filters
      if (options.category) {
        filteredLogs = filteredLogs.filter(log => log.category === options.category);
      }

      if (options.key) {
        filteredLogs = filteredLogs.filter(log => log.key === options.key);
      }

      if (options.changedBy) {
        filteredLogs = filteredLogs.filter(log => log.changedBy === options.changedBy);
      }

      if (options.startDate) {
        filteredLogs = filteredLogs.filter(log => log.createdAt >= options.startDate!);
      }

      if (options.endDate) {
        filteredLogs = filteredLogs.filter(log => log.createdAt <= options.endDate!);
      }

      // Sort by creation date (newest first)
      filteredLogs.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      const total = filteredLogs.length;
      const limit = options.limit || 50;
      const offset = options.offset || 0;

      // Apply pagination
      const paginatedLogs = filteredLogs.slice(offset, offset + limit);
      const hasMore = offset + limit < total;

      return {
        logs: paginatedLogs,
        total,
        hasMore
      };

    } catch (error) {
      console.error('Error fetching audit logs:', error);
      throw new Error('Failed to fetch audit logs');
    }
  }

  /**
   * Get audit log by ID
   */
  async getAuditLogById(id: string): Promise<SettingsAuditLog | null> {
    try {
      const log = this.auditLogs.find(log => log.id === id);
      return log || null;
    } catch (error) {
      console.error('Error fetching audit log:', error);
      throw new Error('Failed to fetch audit log');
    }
  }

  /**
   * Get audit summary
   */
  async getAuditSummary(days: number = 30): Promise<{
    totalChanges: number;
    changesByCategory: Record<string, number>;
    changesByUser: Record<string, number>;
    recentChanges: SettingsAuditLog[];
  }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const recentLogs = this.auditLogs.filter(log => log.createdAt >= cutoffDate);

      // Count changes by category
      const changesByCategory: Record<string, number> = {};
      recentLogs.forEach(log => {
        changesByCategory[log.category] = (changesByCategory[log.category] || 0) + 1;
      });

      // Count changes by user
      const changesByUser: Record<string, number> = {};
      recentLogs.forEach(log => {
        changesByUser[log.changedBy] = (changesByUser[log.changedBy] || 0) + 1;
      });

      // Get most recent changes
      const recentChanges = recentLogs
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, 10);

      return {
        totalChanges: recentLogs.length,
        changesByCategory,
        changesByUser,
        recentChanges
      };

    } catch (error) {
      console.error('Error generating audit summary:', error);
      throw new Error('Failed to generate audit summary');
    }
  }

  /**
   * Export audit logs
   */
  async exportAuditLogs(options: AuditQueryOptions = {}): Promise<any> {
    try {
      const { logs } = await this.getAuditLogs(options);

      return {
        exportDate: new Date().toISOString(),
        totalRecords: logs.length,
        filters: options,
        logs: logs.map(log => ({
          id: log.id,
          category: log.category,
          key: log.key,
          oldValue: this.sanitizeValue(log.oldValue),
          newValue: this.sanitizeValue(log.newValue),
          changedBy: log.changedBy,
          changeReason: log.changeReason,
          ipAddress: log.ipAddress,
          createdAt: log.createdAt.toISOString()
        }))
      };

    } catch (error) {
      console.error('Error exporting audit logs:', error);
      throw new Error('Failed to export audit logs');
    }
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldLogs(retentionDays: number = 365): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const initialCount = this.auditLogs.length;
      this.auditLogs = this.auditLogs.filter(log => log.createdAt >= cutoffDate);
      const deletedCount = initialCount - this.auditLogs.length;

      console.log(`Cleaned up ${deletedCount} old audit logs`);
      return deletedCount;

    } catch (error) {
      console.error('Error cleaning up audit logs:', error);
      throw new Error('Failed to cleanup audit logs');
    }
  }

  /**
   * Get setting change history
   */
  async getSettingHistory(category: SettingsCategory, key: string): Promise<SettingsAuditLog[]> {
    try {
      const history = this.auditLogs
        .filter(log => log.category === category && log.key === key)
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      return history;

    } catch (error) {
      console.error('Error fetching setting history:', error);
      throw new Error('Failed to fetch setting history');
    }
  }

  /**
   * Sanitize sensitive values for export
   */
  private sanitizeValue(value: SettingValue): any {
    if (typeof value === 'string' && this.isSensitiveValue(value)) {
      return '[REDACTED]';
    }
    return value;
  }

  /**
   * Check if value is sensitive
   */
  private isSensitiveValue(value: string): boolean {
    const sensitivePatterns = [
      /password/i,
      /secret/i,
      /key/i,
      /token/i,
      /api/i
    ];

    return sensitivePatterns.some(pattern => pattern.test(value));
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Initialize with mock data for testing
   */
  initializeMockData(): void {
    const mockLogs: SettingsAuditLog[] = [
      {
        id: 'audit_1',
        settingId: '1',
        category: 'PLATFORM',
        key: 'siteName',
        oldValue: 'Old Site Name',
        newValue: 'Doğal Taş Pazaryeri',
        changedBy: 'admin_user_1',
        changeReason: 'Site rebranding',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0...',
        createdAt: new Date(Date.now() - 86400000) // 1 day ago
      },
      {
        id: 'audit_2',
        settingId: '3',
        category: 'BUSINESS',
        key: 'commissionRateM2',
        oldValue: 0.8,
        newValue: 1.0,
        changedBy: 'admin_user_1',
        changeReason: 'Commission rate adjustment',
        ipAddress: '*************',
        createdAt: new Date(Date.now() - 43200000) // 12 hours ago
      },
      {
        id: 'audit_3',
        settingId: '2',
        category: 'PLATFORM',
        key: 'maintenanceMode',
        oldValue: true,
        newValue: false,
        changedBy: 'admin_user_2',
        changeReason: 'Maintenance completed',
        ipAddress: '*************',
        createdAt: new Date(Date.now() - 3600000) // 1 hour ago
      }
    ];

    this.auditLogs = mockLogs;
  }
}
