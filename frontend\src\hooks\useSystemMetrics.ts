// Real-time System Metrics Hook
'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { systemService, SystemMetrics, SystemAlert, ServiceStatus } from '@/services/systemService';

interface UseSystemMetricsOptions {
  refreshInterval?: number; // milliseconds
  autoRefresh?: boolean;
  onError?: (error: Error) => void;
}

interface SystemMetricsState {
  metrics: SystemMetrics | null;
  alerts: SystemAlert[];
  services: ServiceStatus[];
  isLoading: boolean;
  lastUpdated: Date | null;
  error: Error | null;
}

export function useSystemMetrics(options: UseSystemMetricsOptions = {}) {
  const {
    refreshInterval = 0, // Default: no auto-refresh
    autoRefresh = false, // Default: manual refresh only
    onError
  } = options;

  const [state, setState] = useState<SystemMetricsState>({
    metrics: null,
    alerts: [],
    services: [],
    isLoading: false,
    lastUpdated: null,
    error: null
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isLoadingRef = useRef(false);

  const loadSystemData = useCallback(async (showLoading = true) => {
    if (isLoadingRef.current) return;
    
    isLoadingRef.current = true;
    
    if (showLoading) {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
    }

    try {
      const [metricsData, alertsData, servicesData] = await Promise.all([
        systemService.getSystemMetrics(),
        systemService.getSystemAlerts(20),
        systemService.getServiceStatus()
      ]);

      setState(prev => ({
        ...prev,
        metrics: metricsData,
        alerts: alertsData,
        services: servicesData,
        lastUpdated: new Date(),
        isLoading: false,
        error: null
      }));
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error('Unknown error');

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorObj
      }));

      // Stop auto-refresh on error to prevent spam
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      if (onError) {
        onError(errorObj);
      }
    } finally {
      isLoadingRef.current = false;
    }
  }, [onError]);

  const refresh = useCallback(() => {
    return loadSystemData(true);
  }, [loadSystemData]);

  const clearAlerts = useCallback(async () => {
    try {
      await systemService.clearSystemAlerts();
      setState(prev => ({ ...prev, alerts: [] }));
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error('Failed to clear alerts');
      setState(prev => ({ ...prev, error: errorObj }));
      if (onError) {
        onError(errorObj);
      }
    }
  }, [onError]);

  const clearCache = useCallback(async () => {
    try {
      await systemService.clearCache();
      // Refresh data after clearing cache
      await loadSystemData(false);
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error('Failed to clear cache');
      setState(prev => ({ ...prev, error: errorObj }));
      if (onError) {
        onError(errorObj);
      }
    }
  }, [loadSystemData, onError]);

  const optimizeDatabase = useCallback(async () => {
    try {
      await systemService.optimizeDatabase();
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error('Failed to optimize database');
      setState(prev => ({ ...prev, error: errorObj }));
      if (onError) {
        onError(errorObj);
      }
    }
  }, [onError]);

  // Start auto-refresh
  const startAutoRefresh = useCallback(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Only start if explicitly enabled and interval is valid
    if (autoRefresh === true && refreshInterval > 0) {
      console.log('Starting auto-refresh with interval:', refreshInterval);
      intervalRef.current = setInterval(() => {
        loadSystemData(false); // Don't show loading for auto-refresh
      }, refreshInterval);
    } else {
      console.log('Auto-refresh disabled or invalid interval');
    }
  }, [autoRefresh, refreshInterval, loadSystemData]);

  // Stop auto-refresh
  const stopAutoRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // Initialize and cleanup - NO AUTO REFRESH
  useEffect(() => {
    // Only load data once on mount
    loadSystemData(true);
    console.log('useSystemMetrics initialized - MANUAL MODE ONLY');

    return () => {
      stopAutoRefresh();
    };
  }, []); // Empty dependency array - run only once

  // NO AUTO-REFRESH LOGIC - Completely disabled

  // Calculate system status
  const systemStatus = state.metrics ? systemService.getSystemStatus(state.metrics) : 'unknown';

  // Check if system needs attention
  const needsAttention = state.metrics ? systemService.needsAttention(state.metrics) : false;

  // Get active alerts count
  const activeAlertsCount = state.alerts.filter(alert => !alert.resolved).length;

  // Get critical alerts count
  const criticalAlertsCount = state.alerts.filter(
    alert => !alert.resolved && (alert.severity === 'critical' || alert.type === 'error')
  ).length;

  return {
    // State
    ...state,
    systemStatus,
    needsAttention,
    activeAlertsCount,
    criticalAlertsCount,

    // Actions
    refresh,
    clearAlerts,
    clearCache,
    optimizeDatabase,
    startAutoRefresh,
    stopAutoRefresh,

    // Utilities
    formatUptime: systemService.formatUptime,
    getStatusColor: systemService.getStatusColor,
    getStatusVariant: systemService.getStatusVariant,
    formatBytes: systemService.formatBytes
  };
}

// Hook for system health monitoring with alerts
export function useSystemHealth(options: UseSystemMetricsOptions = {}) {
  const systemMetrics = useSystemMetrics({
    autoRefresh: false, // Default: manual refresh only
    refreshInterval: 0,
    ...options
  });
  
  // Monitor for critical conditions
  useEffect(() => {
    if (systemMetrics.metrics && systemMetrics.needsAttention) {
      console.warn('System needs attention:', {
        cpu: systemMetrics.metrics.cpu.usage,
        memory: systemMetrics.metrics.memory.percentage,
        disk: systemMetrics.metrics.disk.percentage
      });
    }
  }, [systemMetrics.metrics, systemMetrics.needsAttention]);

  return systemMetrics;
}

// Hook for real-time metrics with history
export function useSystemMetricsHistory(historyLength = 20, options: UseSystemMetricsOptions = {}) {
  const [metricsHistory, setMetricsHistory] = useState<SystemMetrics[]>([]);
  const systemMetrics = useSystemMetrics({
    autoRefresh: false, // Default: manual refresh only
    refreshInterval: 0,
    ...options
  });

  // Add new metrics to history
  useEffect(() => {
    if (systemMetrics.metrics) {
      setMetricsHistory(prev => {
        const newHistory = [...prev, systemMetrics.metrics!];
        return newHistory.slice(-historyLength); // Keep only last N entries
      });
    }
  }, [systemMetrics.metrics, historyLength]);

  return {
    ...systemMetrics,
    metricsHistory,
    clearHistory: () => setMetricsHistory([])
  };
}
