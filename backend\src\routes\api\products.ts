import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { authMiddleware } from '../../middleware/authMiddleware';
import { asyncHandler } from '../../middleware/errorHandler';
import multer from 'multer';
import path from 'path';

const router = Router();
const prisma = new PrismaClient();

// Configure multer for image uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/products/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Validation schemas
const querySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  category: z.string().optional(),
  subcategory: z.string().optional(),
  origin: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'createdAt', 'basePrice']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  isActive: z.coerce.boolean().optional(),
  status: z.enum(['DRAFT', 'PENDING', 'APPROVED', 'REJECTED']).optional(),
  producerId: z.string().optional()
});

const createProductSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  description: z.string().optional(),
  categoryId: z.string().min(1, 'Category is required'),
  subcategory: z.string().optional(),
  origin: z.string().min(1, 'Origin is required'),
  specifications: z.object({
    stoneType: z.string().optional(),
    finish: z.string().optional(),
    hardness: z.string().optional(),
    density: z.string().optional(),
    waterAbsorption: z.string().optional(),
    freezeThawResistance: z.string().optional(),
    color: z.string().optional(),
    pattern: z.string().optional(),
  }).optional(),
  dimensions: z.object({
    length: z.number().optional(),
    width: z.number().optional(),
    thickness: z.number().optional(),
    unit: z.string().default('cm')
  }).optional(),
  basePrice: z.number().optional(),
  currency: z.string().default('USD'),
  minimumOrderQuantity: z.number().optional(),
  productionTimeDays: z.number().optional(),
  quarryLocationId: z.string().optional(),
  factoryLocationId: z.string().optional(),
  searchKeywords: z.array(z.string()).default([])
});

const updateProductSchema = createProductSchema.partial();

/**
 * @swagger
 * /api/products:
 *   get:
 *     summary: Get all products with filtering and pagination
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, createdAt, basePrice]
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *     responses:
 *       200:
 *         description: Products retrieved successfully
 */
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const query = querySchema.parse(req.query);
  const { page, limit, category, subcategory, origin, search, sortBy, sortOrder, isActive, status, producerId } = query;
  
  const skip = (page - 1) * limit;
  const where: any = {};

  // Apply filters
  if (category) where.categoryId = category;
  if (subcategory) where.subcategory = subcategory;
  if (origin) where.origin = origin;
  if (isActive !== undefined) where.isActive = isActive;
  if (status) where.status = status;
  if (producerId) where.producerId = producerId;

  // Search functionality
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
      { origin: { contains: search, mode: 'insensitive' } },
      { searchKeywords: { has: search } }
    ];
  }

  const [products, total] = await Promise.all([
    prisma.product.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sortBy]: sortOrder },
      include: {
        producer: {
          select: {
            id: true,
            companyName: true,
            profile: {
              select: {
                city: true,
                country: true
              }
            }
          }
        },
        images: {
          select: {
            id: true,
            url: true,
            alt: true,
            isPrimary: true
          },
          orderBy: { isPrimary: 'desc' }
        },
        _count: {
          select: {
            quoteRequests: true,
            reviews: true
          }
        }
      }
    }),
    prisma.product.count({ where })
  ]);

  const totalPages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      products,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @swagger
 * /api/products/{id}:
 *   get:
 *     summary: Get product by ID
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Product retrieved successfully
 *       404:
 *         description: Product not found
 */
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      producer: {
        select: {
          id: true,
          companyName: true,
          profile: {
            select: {
              city: true,
              country: true,
              phone: true,
              website: true
            }
          }
        }
      },
      images: {
        orderBy: { isPrimary: 'desc' }
      },
      priceLists: {
        where: { isActive: true }
      },
      blockProducts: {
        where: { isAvailable: true }
      },
      reviews: {
        include: {
          customer: {
            select: {
              firstName: true,
              lastName: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      },
      _count: {
        select: {
          quoteRequests: true,
          reviews: true
        }
      }
    }
  });

  if (!product) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Product not found',
        statusCode: 404
      }
    });
  }

  res.json({
    success: true,
    data: product
  });
}));

/**
 * @swagger
 * /api/products:
 *   post:
 *     summary: Create a new product
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               categoryId:
 *                 type: string
 *               origin:
 *                 type: string
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *     responses:
 *       201:
 *         description: Product created successfully
 *       403:
 *         description: Only producers can create products
 */
router.post('/', authMiddleware, upload.array('images', 10), asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'producer') {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Only producers can create products',
        statusCode: 403
      }
    });
  }

  const productData = createProductSchema.parse(req.body);
  const files = req.files as Express.Multer.File[];

  // Create product
  const product = await prisma.product.create({
    data: {
      ...productData,
      specifications: productData.specifications || {},
      dimensions: productData.dimensions || {},
      slug: productData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '') + '-' + Date.now(),
      producerId: user.id,
      status: 'PENDING', // Requires admin approval
      isActive: false
    }
  });

  // Handle image uploads
  if (files && files.length > 0) {
    const imagePromises = files.map((file, index) => 
      prisma.productImage.create({
        data: {
          productId: product.id,
          url: `/uploads/products/${file.filename}`,
          alt: `${product.name} - Image ${index + 1}`,
          isPrimary: index === 0 // First image is primary
        }
      })
    );
    
    await Promise.all(imagePromises);
  }

  res.status(201).json({
    success: true,
    data: product,
    message: 'Product created successfully and pending approval'
  });
}));

/**
 * @swagger
 * /api/products/{id}:
 *   put:
 *     summary: Update product
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Product updated successfully
 *       403:
 *         description: Unauthorized
 *       404:
 *         description: Product not found
 */
router.put('/:id', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const user = req.user;

  if (!user || user.userType !== 'producer') {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Only producers can update products',
        statusCode: 403
      }
    });
  }

  const existingProduct = await prisma.product.findUnique({
    where: { id }
  });

  if (!existingProduct) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Product not found',
        statusCode: 404
      }
    });
  }

  if (existingProduct.producerId !== user.id) {
    return res.status(403).json({
      success: false,
      error: {
        message: 'You can only update your own products',
        statusCode: 403
      }
    });
  }

  const updateData = updateProductSchema.parse(req.body);

  const updatedProduct = await prisma.product.update({
    where: { id },
    data: {
      ...updateData,
      specifications: updateData.specifications || (existingProduct.specifications as any),
      dimensions: updateData.dimensions || (existingProduct.dimensions as any),
      slug: updateData.name ?
        updateData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '') + '-' + Date.now() :
        existingProduct.slug,
      status: 'PENDING' // Requires re-approval after update
    },
    include: {
      producer: {
        select: {
          id: true,
          companyName: true
        }
      },
      images: true
    }
  });

  res.json({
    success: true,
    data: updatedProduct,
    message: 'Product updated successfully'
  });
}));

/**
 * @swagger
 * /api/products/{id}:
 *   delete:
 *     summary: Delete product
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Product deleted successfully
 *       403:
 *         description: Unauthorized
 *       404:
 *         description: Product not found
 */
router.delete('/:id', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const user = req.user;

  if (!user || (user.userType !== 'producer' && user.userType !== 'admin')) {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Only producers and admins can delete products',
        statusCode: 403
      }
    });
  }

  const existingProduct = await prisma.product.findUnique({
    where: { id }
  });

  if (!existingProduct) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Product not found',
        statusCode: 404
      }
    });
  }

  // Producers can only delete their own products
  if (user.userType === 'producer' && existingProduct.producerId !== user.id) {
    return res.status(403).json({
      success: false,
      error: {
        message: 'You can only delete your own products',
        statusCode: 403
      }
    });
  }

  await prisma.product.delete({
    where: { id }
  });

  res.json({
    success: true,
    message: 'Product deleted successfully'
  });
}));

/**
 * @swagger
 * /api/products/categories:
 *   get:
 *     summary: Get product categories
 *     tags: [Products]
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 */
router.get('/categories', asyncHandler(async (req: Request, res: Response) => {
  // Get unique categories from products
  const categories = await prisma.product.groupBy({
    by: ['categoryId'],
    where: {
      isActive: true,
      status: 'APPROVED'
    },
    _count: {
      categoryId: true
    }
  });

  // Get category details (assuming you have a categories table)
  const categoryDetails = await Promise.all(
    categories.map(async (cat) => {
      try {
        // Note: Category table might not exist, using categoryId directly
        const categoryName = cat.categoryId;
        return {
          id: cat.categoryId,
          name: categoryName || 'Unknown',
          productCount: cat._count.categoryId
        };
      } catch {
        return {
          id: cat.categoryId,
          name: cat.categoryId,
          productCount: cat._count.categoryId
        };
      }
    })
  );

  res.json({
    success: true,
    data: categoryDetails
  });
}));

/**
 * @swagger
 * /api/products/{id}/images:
 *   post:
 *     summary: Add images to product
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *     responses:
 *       200:
 *         description: Images added successfully
 */
router.post('/:id/images', authMiddleware, upload.array('images', 10), asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const user = req.user;
  const files = req.files as Express.Multer.File[];

  if (!user || user.userType !== 'producer') {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Only producers can add product images',
        statusCode: 403
      }
    });
  }

  const product = await prisma.product.findUnique({
    where: { id }
  });

  if (!product) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Product not found',
        statusCode: 404
      }
    });
  }

  if (product.producerId !== user.id) {
    return res.status(403).json({
      success: false,
      error: {
        message: 'You can only add images to your own products',
        statusCode: 403
      }
    });
  }

  if (!files || files.length === 0) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'No images uploaded',
        statusCode: 400
      }
    });
  }

  // Get current image count to determine if this should be primary
  const currentImageCount = await prisma.productImage.count({
    where: { productId: id }
  });

  const imagePromises = files.map((file, index) =>
    prisma.productImage.create({
      data: {
        productId: id,
        url: `/uploads/products/${file.filename}`,
        alt: `${product.name} - Image ${currentImageCount + index + 1}`,
        isPrimary: currentImageCount === 0 && index === 0 // First image is primary if no images exist
      }
    })
  );

  const images = await Promise.all(imagePromises);

  res.json({
    success: true,
    data: images,
    message: `${images.length} image(s) added successfully`
  });
}));

export default router;
