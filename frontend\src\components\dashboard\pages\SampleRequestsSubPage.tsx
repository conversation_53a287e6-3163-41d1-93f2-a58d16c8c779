'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSample } from '@/contexts/sample-context';
import {
  SampleRequestCard,
  SampleRequestEmptyState,
  SampleRequestFilter
} from '@/components/sample-requests';

interface SampleRequestsSubPageProps {
  status: 'pending' | 'payment_required' | 'approved' | 'shipped' | 'delivered' | 'evaluated';
  title: string;
  description: string;
}

const SampleRequestsSubPage: React.FC<SampleRequestsSubPageProps> = ({
  status,
  title,
  description
}) => {
  const router = useRouter();
  const { sampleRequests, getSampleRequestsByCustomer, isLoading } = useSample();
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<{ start: Date | null; end: Date | null }>({ start: null, end: null });

  useEffect(() => {
    // Load customer sample requests
    getSampleRequestsByCustomer('1'); // Should be actual customer ID
  }, []);

  // Filter requests based on status, search term, and date range
  const filteredRequests = React.useMemo(() => {
    let filtered = sampleRequests;

    // Filter by status
    if (status === 'payment_required') {
      filtered = filtered.filter(request => 
        request.status === 'approved_pending_payment' || request.status === 'payment_required'
      );
    } else {
      filtered = filtered.filter(request => request.status === status);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(request =>
        request.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.requestedProducts.some((product: any) =>
          product.productName.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Filter by date range
    if (dateRange.start || dateRange.end) {
      filtered = filtered.filter(request => {
        const requestDate = new Date(request.createdAt);
        if (dateRange.start && requestDate < dateRange.start) return false;
        if (dateRange.end && requestDate > dateRange.end) return false;
        return true;
      });
    }

    return filtered;
  }, [sampleRequests, status, searchTerm, dateRange]);

  const handleNavigate = (route: string) => {
    router.push(route);
  };

  const handleViewDetail = (requestId: string) => {
    console.log('View detail for request:', requestId);
  };

  const handlePayment = (requestId: string) => {
    console.log('Handle payment for request:', requestId);
  };

  const handleEvaluate = (requestId: string) => {
    console.log('Handle evaluate for request:', requestId);
  };

  const handleOrder = (requestId: string) => {
    console.log('Handle order for request:', requestId);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Numune talepleri yükleniyor...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          <p className="text-gray-600 mt-1">{description}</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => handleNavigate('/customer/requests/samples')}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            Tüm Numuneler
          </button>
        </div>
      </div>

      {/* Filter Component */}
      <SampleRequestFilter
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        dateRange={dateRange}
        onDateRangeChange={setDateRange}
      />

      {/* Sample Requests List */}
      {filteredRequests.length > 0 ? (
        <div className="space-y-4">
          {filteredRequests.map((request, index) => (
            <SampleRequestCard
              key={request.id}
              request={request}
              onNavigate={handleNavigate}
              onViewDetail={handleViewDetail}
              onPayment={handlePayment}
              onEvaluate={handleEvaluate}
              onOrder={handleOrder}
            />
          ))}
        </div>
      ) : (
        <SampleRequestEmptyState
          activeTab={status}
          tabLabel={title}
          onNavigate={handleNavigate}
        />
      )}
    </div>
  );
};

export default SampleRequestsSubPage;
