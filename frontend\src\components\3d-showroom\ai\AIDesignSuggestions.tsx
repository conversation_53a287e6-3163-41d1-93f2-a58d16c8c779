'use client';

import React, { useState, useCallback } from 'react';
import { 
  <PERSON><PERSON>les, 
  Wand2, 
  Palette, 
  Home, 
  RefreshCw, 
  Heart,
  Download,
  Share2,
  Eye,
  TrendingUp
} from 'lucide-react';
import { ProductPlacement, ShowroomConfiguration } from '../core/ShowroomEngine';
import { DEFAULT_GROUT_SETTINGS } from '../utils/grout';

export interface AIDesignSuggestion {
  id: string;
  name: string;
  description: string;
  style: 'modern' | 'klasik' | 'rustik' | 'minimalist' | 'lüks' | 'endüstriyel';
  room: 'living' | 'kitchen' | 'bathroom' | 'bedroom' | 'outdoor';
  products: Omit<ProductPlacement, 'id'>[];
  thumbnail: string;
  popularity: number;
  tags: string[];
  difficulty: 'kolay' | 'orta' | 'zor';
}

interface AIDesignSuggestionsProps {
  currentProducts: ProductPlacement[];
  currentRoom: ShowroomConfiguration['room'];
  availableProducts: any[];
  onApplySuggestion: (suggestion: AIDesignSuggestion) => void;
  onAddToFavorites?: (suggestion: AIDesignSuggestion) => void;
  className?: string;
}

// Mock AI suggestions - In real implementation, this would come from AI service
const MOCK_SUGGESTIONS: AIDesignSuggestion[] = [
  {
    id: 'modern-living-1',
    name: 'Modern Salon Tasarımı',
    description: 'Beyaz mermer ve gri granit kombinasyonu ile modern ve şık bir salon tasarımı',
    style: 'modern',
    room: 'living',
    products: [
      {
        productId: 'marble-carrara-white',
        position: { x: 0, y: 0, z: 0 },
        rotation: 0,
        scale: { width: 60, height: 60 },
        area: 3.6,
        pattern: 'standard',
        opacity: 1,
        visible: true,
        groutSettings: { ...DEFAULT_GROUT_SETTINGS, width: 1, color: '#f8f9fa' }
      },
      {
        productId: 'granite-absolute-black',
        position: { x: 2, y: 0, z: 2 },
        rotation: 45,
        scale: { width: 40, height: 40 },
        area: 1.6,
        pattern: 'diagonal',
        opacity: 1,
        visible: true,
        groutSettings: { ...DEFAULT_GROUT_SETTINGS, width: 2, color: '#2d3748' }
      }
    ],
    thumbnail: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop',
    popularity: 95,
    tags: ['modern', 'şık', 'kontrast'],
    difficulty: 'orta'
  },
  {
    id: 'classic-kitchen-1',
    name: 'Klasik Mutfak Düzeni',
    description: 'Traverten ve oniks detayları ile sıcak ve davetkar mutfak tasarımı',
    style: 'klasik',
    room: 'kitchen',
    products: [
      {
        productId: 'travertine-classic-beige',
        position: { x: -1, y: 0, z: -1 },
        rotation: 0,
        scale: { width: 40, height: 40 },
        area: 1.6,
        pattern: 'herringbone',
        opacity: 1,
        visible: true,
        groutSettings: { ...DEFAULT_GROUT_SETTINGS, width: 3, color: '#d4a574' }
      },
      {
        productId: 'onyx-green-pakistan',
        position: { x: 1, y: 0, z: 1 },
        rotation: 0,
        scale: { width: 30, height: 60 },
        area: 1.8,
        pattern: 'standard',
        opacity: 1,
        visible: true,
        groutSettings: { ...DEFAULT_GROUT_SETTINGS, width: 1, color: '#38a169' }
      }
    ],
    thumbnail: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=200&fit=crop',
    popularity: 88,
    tags: ['klasik', 'sıcak', 'doğal'],
    difficulty: 'kolay'
  },
  {
    id: 'luxury-bathroom-1',
    name: 'Lüks Banyo Konsepti',
    description: 'Altın sarısı kireçtaşı ve yeşil oniks ile lüks banyo tasarımı',
    style: 'lüks',
    room: 'bathroom',
    products: [
      {
        productId: 'limestone-jerusalem-gold',
        position: { x: 0, y: 0, z: 0 },
        rotation: 0,
        scale: { width: 40, height: 60 },
        area: 2.4,
        pattern: 'brick',
        opacity: 1,
        visible: true,
        groutSettings: { ...DEFAULT_GROUT_SETTINGS, width: 2, color: '#f6d55c', style: 'raised' }
      }
    ],
    thumbnail: 'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=300&h=200&fit=crop',
    popularity: 92,
    tags: ['lüks', 'altın', 'premium'],
    difficulty: 'zor'
  },
  {
    id: 'minimalist-bedroom-1',
    name: 'Minimalist Yatak Odası',
    description: 'Sade beyaz granit ile minimalist ve huzurlu yatak odası',
    style: 'minimalist',
    room: 'bedroom',
    products: [
      {
        productId: 'granite-kashmir-white',
        position: { x: 0, y: 0, z: 0 },
        rotation: 0,
        scale: { width: 60, height: 60 },
        area: 3.6,
        pattern: 'standard',
        opacity: 1,
        visible: true,
        groutSettings: { ...DEFAULT_GROUT_SETTINGS, width: 0.5, color: '#ffffff', style: 'flat' }
      }
    ],
    thumbnail: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop',
    popularity: 85,
    tags: ['minimalist', 'sade', 'huzur'],
    difficulty: 'kolay'
  }
];

export const AIDesignSuggestions: React.FC<AIDesignSuggestionsProps> = ({
  currentProducts,
  currentRoom,
  availableProducts,
  onApplySuggestion,
  onAddToFavorites,
  className = ''
}) => {
  const [selectedStyle, setSelectedStyle] = useState<string>('all');
  const [isGenerating, setIsGenerating] = useState(false);
  const [suggestions, setSuggestions] = useState<AIDesignSuggestion[]>(MOCK_SUGGESTIONS);

  // Filter suggestions based on current room and selected style
  const filteredSuggestions = suggestions.filter(suggestion => {
    const roomMatch = suggestion.room === currentRoom;
    const styleMatch = selectedStyle === 'all' || suggestion.style === selectedStyle;
    return roomMatch && styleMatch;
  });

  // Generate new suggestions (mock implementation)
  const generateNewSuggestions = useCallback(async () => {
    setIsGenerating(true);
    
    // Simulate AI generation delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // In real implementation, this would call your AI service
    // const newSuggestions = await aiService.generateSuggestions({
    //   currentProducts,
    //   room: currentRoom,
    //   style: selectedStyle,
    //   availableProducts
    // });
    
    // For now, just shuffle existing suggestions
    const shuffled = [...MOCK_SUGGESTIONS].sort(() => Math.random() - 0.5);
    setSuggestions(shuffled);
    
    setIsGenerating(false);
  }, [currentProducts, currentRoom, selectedStyle, availableProducts]);

  // Apply suggestion to scene
  const handleApplySuggestion = useCallback((suggestion: AIDesignSuggestion) => {
    onApplySuggestion(suggestion);
  }, [onApplySuggestion]);

  const styles = ['all', 'modern', 'klasik', 'rustik', 'minimalist', 'lüks', 'endüstriyel'];

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <Sparkles size={16} className="text-amber-500" />
            AI Tasarım Önerileri
          </h3>
          
          <button
            onClick={generateNewSuggestions}
            disabled={isGenerating}
            className="flex items-center gap-1 px-3 py-1 text-xs bg-amber-100 text-amber-700 rounded-lg hover:bg-amber-200 transition-colors disabled:opacity-50"
          >
            <RefreshCw size={12} className={isGenerating ? 'animate-spin' : ''} />
            {isGenerating ? 'Üretiliyor...' : 'Yeni Öneriler'}
          </button>
        </div>

        {/* Style Filter */}
        <div className="flex gap-1 overflow-x-auto">
          {styles.map(style => (
            <button
              key={style}
              onClick={() => setSelectedStyle(style)}
              className={`px-3 py-1 text-xs rounded-full whitespace-nowrap transition-colors ${
                selectedStyle === style
                  ? 'bg-amber-500 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {style === 'all' ? 'Tümü' : style.charAt(0).toUpperCase() + style.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Suggestions List */}
      <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
        {isGenerating ? (
          <div className="text-center py-8">
            <Wand2 className="w-8 h-8 mx-auto mb-2 text-amber-500 animate-pulse" />
            <p className="text-sm text-gray-600">AI yeni tasarımlar üretiyor...</p>
          </div>
        ) : filteredSuggestions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Palette size={32} className="mx-auto mb-2 opacity-50" />
            <p className="text-sm">Bu mekan için öneri bulunamadı</p>
            <button
              onClick={generateNewSuggestions}
              className="text-xs text-amber-600 hover:text-amber-700 mt-2"
            >
              Yeni öneriler üret
            </button>
          </div>
        ) : (
          filteredSuggestions.map((suggestion) => (
            <div
              key={suggestion.id}
              className="border border-gray-200 rounded-lg overflow-hidden hover:border-amber-300 transition-colors"
            >
              {/* Thumbnail */}
              <div className="relative h-24 bg-gray-100">
                <img
                  src={suggestion.thumbnail}
                  alt={suggestion.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 right-2 flex gap-1">
                  <span className="px-2 py-1 bg-black/70 text-white text-xs rounded">
                    {suggestion.popularity}%
                  </span>
                  <span className={`px-2 py-1 text-xs rounded ${
                    suggestion.difficulty === 'kolay' ? 'bg-green-100 text-green-700' :
                    suggestion.difficulty === 'orta' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-red-100 text-red-700'
                  }`}>
                    {suggestion.difficulty}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-3">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">{suggestion.name}</h4>
                    <p className="text-xs text-gray-600 mt-1">{suggestion.description}</p>
                  </div>
                </div>

                {/* Tags */}
                <div className="flex gap-1 mb-3">
                  {suggestion.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <button
                    onClick={() => handleApplySuggestion(suggestion)}
                    className="flex-1 px-3 py-2 bg-amber-500 text-white text-xs rounded-lg hover:bg-amber-600 transition-colors flex items-center justify-center gap-1"
                  >
                    <Wand2 size={12} />
                    Uygula
                  </button>
                  
                  {onAddToFavorites && (
                    <button
                      onClick={() => onAddToFavorites(suggestion)}
                      className="px-3 py-2 border border-gray-300 text-gray-600 text-xs rounded-lg hover:bg-gray-50 transition-colors"
                      title="Favorilere Ekle"
                    >
                      <Heart size={12} />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default AIDesignSuggestions;
