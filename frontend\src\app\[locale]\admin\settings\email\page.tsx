'use client';

import React from 'react';
import { SettingsProvider } from '@/contexts/settings-context';
import AdminSettingsSubPage from '@/components/admin/pages/AdminSettingsSubPage';

export default function AdminSettingsEmailPage() {
  return (
    <SettingsProvider>
      <AdminSettingsSubPage 
        type="email"
        title="Email Ayarları"
        description="SMTP ayarları ve email şablonlarını yönetin"
      />
    </SettingsProvider>
  );
}
