'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { withQuarryAccess } from '@/components/blocks/QuarryAccessControl'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Package,
  Ruler,
  DollarSign,
  MapPin,
  Calendar,
  Building2,
  Archive,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'

function ProducerBlocksPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedCategory, setSelectedCategory] = React.useState('all')
  const [selectedStatus, setSelectedStatus] = React.useState('all')

  // Mock blocks data - gerçek uygulamada API'den gelecek
  const mockBlocks = [
    {
      id: '1',
      name: '<PERSON><PERSON>on Beyaz Mermer Blok - A Kalite',
      category: 'Mermer',
      quarry: 'Afyon Merkez Ocağı',
      dimensions: {
        length: 2.5,
        width: 1.8,
        height: 1.2,
        volume: 5.4,
        weight: 14.58
      },
      quality: 'A',
      color: 'Beyaz',
      pattern: 'Düz',
      pricing: {
        pricePerTon: 1200,
        pricePerM3: 3240,
        currency: 'USD'
      },
      stock: {
        available: true,
        quantity: 1,
        location: 'Afyon Depo'
      },
      status: 'active',
      createdAt: '2025-06-20',
      images: ['/api/placeholder/300/200'],
      views: 45,
      inquiries: 8
    },
    {
      id: '2',
      name: 'Denizli Traverten Blok - B Kalite',
      category: 'Traverten',
      quarry: 'Denizli Ana Ocak',
      dimensions: {
        length: 3.0,
        width: 2.0,
        height: 1.5,
        volume: 9.0,
        weight: 24.3
      },
      quality: 'B',
      color: 'Bej',
      pattern: 'Damarlı',
      pricing: {
        pricePerTon: 800,
        pricePerM3: 2160,
        currency: 'USD'
      },
      stock: {
        available: true,
        quantity: 2,
        location: 'Denizli Depo'
      },
      status: 'active',
      createdAt: '2025-06-18',
      images: ['/api/placeholder/300/200'],
      views: 32,
      inquiries: 5
    },
    {
      id: '3',
      name: 'Muğla Granit Blok - A Kalite',
      category: 'Granit',
      quarry: 'Muğla Granit Ocağı',
      dimensions: {
        length: 2.2,
        width: 1.5,
        height: 1.0,
        volume: 3.3,
        weight: 8.91
      },
      quality: 'A',
      color: 'Siyah',
      pattern: 'Düz',
      pricing: {
        pricePerTon: 1500,
        pricePerM3: 4050,
        currency: 'USD'
      },
      stock: {
        available: false,
        quantity: 0,
        location: 'Satıldı'
      },
      status: 'sold',
      createdAt: '2025-06-15',
      images: ['/api/placeholder/300/200'],
      views: 67,
      inquiries: 12
    },
    {
      id: '4',
      name: 'Eskişehir Andezit Blok - B Kalite',
      category: 'Andezit',
      quarry: 'Eskişehir Andezit Ocağı',
      dimensions: {
        length: 2.8,
        width: 1.6,
        height: 1.3,
        volume: 5.824,
        weight: 15.72
      },
      quality: 'B',
      color: 'Gri',
      pattern: 'Volkanik',
      pricing: {
        pricePerTon: 600,
        pricePerM3: 1620,
        currency: 'USD'
      },
      stock: {
        available: true,
        quantity: 1,
        location: 'Eskişehir Depo'
      },
      status: 'draft',
      createdAt: '2025-06-25',
      images: ['/api/placeholder/300/200'],
      views: 12,
      inquiries: 2
    }
  ]

  const categories = ['all', 'Mermer', 'Granit', 'Traverten', 'Oniks', 'Andezit', 'Bazalt']
  const statuses = [
    { value: 'all', label: 'Tümü' },
    { value: 'active', label: 'Aktif' },
    { value: 'draft', label: 'Taslak' },
    { value: 'sold', label: 'Satıldı' }
  ]

  const filteredBlocks = React.useMemo(() => {
    return mockBlocks.filter(block => {
      const matchesSearch = block.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           block.quarry.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || block.category === selectedCategory
      const matchesStatus = selectedStatus === 'all' || block.status === selectedStatus
      
      return matchesSearch && matchesCategory && matchesStatus
    })
  }, [searchTerm, selectedCategory, selectedStatus])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      case 'sold':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-3 h-3" />
      case 'draft':
        return <Clock className="w-3 h-3" />
      case 'sold':
        return <Package className="w-3 h-3" />
      default:
        return <AlertTriangle className="w-3 h-3" />
    }
  }

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'A':
        return 'bg-emerald-100 text-emerald-800'
      case 'B':
        return 'bg-blue-100 text-blue-800'
      case 'C':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const totalBlocks = mockBlocks.length
  const activeBlocks = mockBlocks.filter(b => b.status === 'active').length
  const soldBlocks = mockBlocks.filter(b => b.status === 'sold').length
  const totalValue = mockBlocks
    .filter(b => b.status === 'active')
    .reduce((sum, block) => sum + (block.pricing.pricePerTon * block.dimensions.weight), 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Bloklarım</h1>
          <p className="text-gray-600">
            Blok ürünlerinizi yönetin ve yeni bloklar ekleyin
          </p>
        </div>
        <Button
          className="bg-orange-600 hover:bg-orange-700"
          onClick={() => router.push('/producer/blocks/add')}
        >
          <Plus className="w-4 h-4 mr-2" />
          Yeni Blok Ekle
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Package className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Blok</p>
              <p className="text-xl font-bold text-gray-900">{totalBlocks}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Aktif Blok</p>
              <p className="text-xl font-bold text-gray-900">{activeBlocks}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrendingUp className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Satılan Blok</p>
              <p className="text-xl font-bold text-gray-900">{soldBlocks}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-amber-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Değer</p>
              <p className="text-xl font-bold text-gray-900">${(totalValue / 1000).toFixed(0)}K</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Blok adı veya ocak ile ara..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          />
        </div>
        
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          {categories.map(category => (
            <option key={category} value={category}>
              {category === 'all' ? 'Tüm Kategoriler' : category}
            </option>
          ))}
        </select>

        <select
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          {statuses.map(status => (
            <option key={status.value} value={status.value}>
              {status.label}
            </option>
          ))}
        </select>
      </div>

      {/* Blocks Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredBlocks.map((block) => (
          <Card key={block.id} className="overflow-hidden">
            <div className="relative">
              <img
                src={block.images[0]}
                alt={block.name}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-2 left-2">
                <Badge variant="outline" className="bg-white/90">
                  {block.category}
                </Badge>
              </div>
              <div className="absolute top-2 right-2">
                <Badge className={getStatusColor(block.status)}>
                  {getStatusIcon(block.status)}
                  <span className="ml-1">
                    {block.status === 'active' ? 'Aktif' :
                     block.status === 'draft' ? 'Taslak' :
                     block.status === 'sold' ? 'Satıldı' : block.status}
                  </span>
                </Badge>
              </div>
              <div className="absolute bottom-2 left-2">
                <Badge className={getQualityColor(block.quality)}>
                  {block.quality} Kalite
                </Badge>
              </div>
            </div>

            <div className="p-6">
              <div className="space-y-4">
                {/* Block Info */}
                <div>
                  <h3 className="font-semibold text-lg text-gray-900 mb-1">
                    {block.name}
                  </h3>
                  <p className="text-sm text-gray-600 flex items-center gap-1">
                    <Building2 className="w-3 h-3" />
                    {block.quarry}
                  </p>
                </div>

                {/* Dimensions */}
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Boyutlar:</span>
                      <span className="font-medium">
                        {block.dimensions.length}×{block.dimensions.width}×{block.dimensions.height}m
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Hacim:</span>
                      <span className="font-medium">{block.dimensions.volume} m³</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Ağırlık:</span>
                      <span className="font-medium">{block.dimensions.weight} ton</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Renk:</span>
                      <span className="font-medium">{block.color}</span>
                    </div>
                  </div>
                </div>

                {/* Pricing */}
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-600">Fiyat</p>
                    <p className="text-lg font-bold text-gray-900">
                      ${block.pricing.pricePerTon}/ton
                    </p>
                    <p className="text-xs text-gray-500">
                      ${block.pricing.pricePerM3}/m³
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">Toplam Değer</p>
                    <p className="text-lg font-bold text-orange-600">
                      ${(block.pricing.pricePerTon * block.dimensions.weight).toLocaleString()}
                    </p>
                  </div>
                </div>

                {/* Stock & Location */}
                <div className="flex justify-between items-center text-sm">
                  <div className="flex items-center gap-1">
                    <MapPin className="w-3 h-3 text-gray-400" />
                    <span className="text-gray-600">{block.stock.location}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Archive className="w-3 h-3 text-gray-400" />
                    <span className="text-gray-600">
                      {block.stock.available ? `${block.stock.quantity} adet` : 'Stok yok'}
                    </span>
                  </div>
                </div>

                {/* Stats */}
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>{block.views} görüntülenme</span>
                  <span>{block.inquiries} talep</span>
                  <span>{new Date(block.createdAt).toLocaleDateString('tr-TR')}</span>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => router.push(`/producer/blocks/${block.id}`)}
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    Görüntüle
                  </Button>
                  {block.status !== 'sold' && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => router.push(`/producer/blocks/${block.id}/edit`)}
                    >
                      <Edit className="w-4 h-4 mr-1" />
                      Düzenle
                    </Button>
                  )}
                  {block.status === 'draft' && (
                    <Button
                      size="sm"
                      className="flex-1 bg-orange-600 hover:bg-orange-700"
                    >
                      Yayınla
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {filteredBlocks.length === 0 && (
        <div className="text-center py-12">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Blok bulunamadı</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || selectedCategory !== 'all' || selectedStatus !== 'all'
              ? 'Arama kriterlerinize uygun blok bulunmuyor.'
              : 'Henüz hiç blok eklememişsiniz.'}
          </p>
          <Button
            className="bg-orange-600 hover:bg-orange-700"
            onClick={() => router.push('/producer/blocks/add')}
          >
            <Plus className="w-4 h-4 mr-2" />
            İlk Bloğunuzu Ekleyin
          </Button>
        </div>
      )}
    </div>
  )
}

export default withQuarryAccess(ProducerBlocksPage)
