groups:
  - name: natural_stone_marketplace_alerts
    rules:
      # High CPU usage
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes on {{ $labels.instance }}"

      # Disk space low
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Disk space is running low"
          description: "Disk space is below 10% on {{ $labels.instance }} filesystem {{ $labels.mountpoint }}"

      # Service down
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.job }} service is down on {{ $labels.instance }}"

      # High HTTP error rate
      - alert: HighHTTPErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High HTTP error rate"
          description: "HTTP error rate is above 5% for {{ $labels.job }}"

      # Database connection issues
      - alert: DatabaseConnectionHigh
        expr: pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connections"
          description: "PostgreSQL has more than 80 active connections"

      # Redis memory usage high
      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is above 90%"

      # Application response time high
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time"
          description: "95th percentile response time is above 2 seconds for {{ $labels.job }}"

      # SSL certificate expiring
      - alert: SSLCertificateExpiring
        expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 30
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate for {{ $labels.instance }} expires in less than 30 days"

      # Elasticsearch cluster health
      - alert: ElasticsearchClusterNotHealthy
        expr: elasticsearch_cluster_health_status{color="red"} == 1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Elasticsearch cluster is not healthy"
          description: "Elasticsearch cluster health is RED"

      # High number of failed login attempts
      - alert: HighFailedLoginAttempts
        expr: increase(failed_login_attempts_total[5m]) > 50
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "High number of failed login attempts"
          description: "More than 50 failed login attempts in the last 5 minutes"

      # Backup failure
      - alert: BackupFailed
        expr: time() - last_successful_backup_timestamp > 86400 * 2
        for: 1h
        labels:
          severity: critical
        annotations:
          summary: "Backup has failed"
          description: "No successful backup in the last 2 days"

      # Queue size growing
      - alert: QueueSizeGrowing
        expr: increase(queue_size[10m]) > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Queue size is growing rapidly"
          description: "Queue size increased by more than 1000 items in 10 minutes"

  - name: business_metrics_alerts
    rules:
      # Low user activity
      - alert: LowUserActivity
        expr: rate(user_actions_total[1h]) < 10
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "Low user activity detected"
          description: "User activity is below normal levels"

      # High order cancellation rate
      - alert: HighOrderCancellationRate
        expr: rate(orders_cancelled_total[1h]) / rate(orders_created_total[1h]) * 100 > 20
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "High order cancellation rate"
          description: "Order cancellation rate is above 20%"

      # Payment processing issues
      - alert: PaymentProcessingIssues
        expr: rate(payment_failures_total[5m]) > 5
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Payment processing issues"
          description: "High rate of payment failures detected"
