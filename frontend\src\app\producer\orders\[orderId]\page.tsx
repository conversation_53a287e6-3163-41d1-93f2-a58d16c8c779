'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Package, 
  CreditCard, 
  Truck, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  User,
  Building,
  Calendar,
  DollarSign,
  FileText,
  MapPin,
  Factory,
  PlayCircle,
  PauseCircle,
  ArrowRight
} from 'lucide-react';
import { ShippingPlanModal } from '@/components/shipping/ShippingPlanModal';
import toast from 'react-hot-toast';

interface DeliverySchedule {
  id: string;
  deliveryNumber: number;
  quantity: number;
  unit: string;
  scheduledDate: string;
  status: 'pending' | 'in_production' | 'ready' | 'shipped' | 'delivered';
  payment: {
    id: string;
    amount: number;
    currency: string;
    status: 'pending' | 'received' | 'completed';
    receivedAt?: string;
  };
  trackingNumber?: string;
  estimatedDelivery?: string;
}

interface ProducerOrderDetail {
  id: string;
  orderNumber: string;
  productName: string;
  totalQuantity: number;
  unit: string;
  totalAmount: number; // Net amount after commission
  currency: string;
  status: string;
  createdAt: string;
  customer: {
    name: string;
    company: string;
    email: string;
    phone: string;
  };
  deliverySchedules: DeliverySchedule[];
  overallProgress: number;
  productionNotes?: string;
  shippingAddress: string;
}

export default function ProducerOrderDetailPage() {
  const params = useParams();
  const router = useRouter();
  const orderId = params.orderId as string;
  const [order, setOrder] = useState<ProducerOrderDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [showShippingPlanModal, setShowShippingPlanModal] = useState(false);

  useEffect(() => {
    fetchOrderDetail();
  }, [orderId]);

  const fetchOrderDetail = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      // Create different mock data based on orderId
      const mockOrders: { [key: string]: ProducerOrderDetail } = {
        'ORD-001': {
          id: 'ORD-001',
          orderNumber: 'ORD-001',
          productName: 'Beyaz Mermer - Afyon',
          totalQuantity: 300,
          unit: 'm²',
          totalAmount: 14250, // Net amount after commission (15000 - 750)
          currency: 'USD',
          status: 'in_production',
          createdAt: '2025-06-25T10:00:00Z',
          customer: {
            name: 'ABC İnşaat Ltd.',
            company: 'ABC İnşaat Ltd.',
            email: '<EMAIL>',
            phone: '+90 ************'
          },
          deliverySchedules: [
            {
              id: 'del_001',
              deliveryNumber: 1,
              quantity: 150,
              unit: 'm²',
              scheduledDate: '2025-07-05',
              status: 'delivered',
              payment: {
                id: 'pay_001',
                amount: 7125, // Net amount after commission
                currency: 'USD',
                status: 'completed',
                receivedAt: '2025-07-06T10:15:00Z'
              },
              trackingNumber: 'TRK123456789',
              estimatedDelivery: '2025-07-07'
            },
            {
              id: 'del_002',
              deliveryNumber: 2,
              quantity: 150,
              unit: 'm²',
              scheduledDate: '2025-07-10',
              status: 'ready',
              payment: {
                id: 'pay_002',
                amount: 7125, // Net amount after commission
                currency: 'USD',
                status: 'received',
                receivedAt: '2025-07-08T09:20:00Z'
              }
            }
          ],
          overallProgress: 75,
          productionNotes: 'Kalınlık: 2 cm, Yüzey: Cilalı, Ambalaj: Kasalı',
          shippingAddress: 'İstanbul Sanayi Sitesi, Blok A, No: 15, Beylikdüzü/İstanbul'
        },
        'default': {
          id: orderId,
          orderNumber: orderId.startsWith('ORD-') ? orderId : `ORD-${orderId.slice(-6).toUpperCase()}`,
          productName: 'Beyaz Mermer - Afyon',
          totalQuantity: 500,
          unit: 'm²',
          totalAmount: 71250, // Net amount after commission (75000 - 3750)
          currency: 'TRY',
          status: 'in_production',
          createdAt: '2024-01-15T10:00:00Z',
          customer: {
            name: 'Ahmet Yılmaz',
            company: 'Marble Export Ltd.',
            email: '<EMAIL>',
            phone: '+90 212 123 45 67'
          },
          deliverySchedules: [
            {
              id: 'del_001',
              deliveryNumber: 1,
              quantity: 250,
              unit: 'm²',
              scheduledDate: '2024-02-01',
              status: 'delivered',
              payment: {
                id: 'pay_001',
                amount: 35625, // Net amount after commission
                currency: 'TRY',
                status: 'completed',
                receivedAt: '2024-02-02T10:15:00Z'
              },
              trackingNumber: 'TRK123456789',
              estimatedDelivery: '2024-02-03'
            },
            {
              id: 'del_002',
              deliveryNumber: 2,
              quantity: 250,
              unit: 'm²',
              scheduledDate: '2024-02-15',
              status: 'ready',
              payment: {
                id: 'pay_002',
                amount: 35625, // Net amount after commission
                currency: 'TRY',
                status: 'received',
                receivedAt: '2024-02-10T09:20:00Z'
              }
            }
          ],
          overallProgress: 60,
          productionNotes: 'Özel kesim talebi var. Müşteri ile koordine edildi.',
          shippingAddress: 'İstanbul Sanayi Sitesi, Blok A, No: 15, Beylikdüzü/İstanbul'
        }
      };

      const mockOrder = mockOrders[orderId] || mockOrders['default'];
      setOrder(mockOrder);
    } catch (error) {
      console.error('Error fetching order:', error);
      toast.error('Sipariş detayları yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateDeliveryStatus = async (deliveryId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/producer/orders/${orderId}/deliveries/${deliveryId}/status`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      });

      if (response.ok) {
        toast.success('Teslimat durumu güncellendi');
        await fetchOrderDetail();
      } else {
        throw new Error('Status update failed');
      }
    } catch (error) {
      console.error('Error updating delivery status:', error);
      toast.error('Durum güncellenirken hata oluştu');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Bekliyor', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      in_production: { label: 'Üretimde', color: 'bg-purple-100 text-purple-800', icon: Factory },
      ready: { label: 'Hazır', color: 'bg-blue-100 text-blue-800', icon: Package },
      shipped: { label: 'Kargoda', color: 'bg-orange-100 text-orange-800', icon: Truck },
      delivered: { label: 'Teslim Edildi', color: 'bg-green-100 text-green-800', icon: CheckCircle },
      received: { label: 'Alındı', color: 'bg-green-100 text-green-800', icon: CheckCircle },
      completed: { label: 'Tamamlandı', color: 'bg-green-100 text-green-800', icon: CheckCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Sipariş detayları yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Sipariş bulunamadı</h3>
        <p className="text-gray-600">Bu sipariş mevcut değil veya erişim yetkiniz bulunmuyor.</p>
        <Button onClick={() => router.back()} className="mt-4">
          Geri Dön
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Sipariş Detayı</h1>
          <p className="text-gray-600 mt-1">{order.orderNumber}</p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            onClick={() => setShowShippingPlanModal(true)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Truck className="w-4 h-4" />
            Sevkiyat Planı
          </Button>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(order.totalAmount, order.currency)}
            </div>
            <p className="text-sm text-gray-500">Net tutar</p>
            {getStatusBadge(order.status)}
          </div>
        </div>
      </div>

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Sipariş Özeti
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Ürün Bilgileri</h4>
              <p className="text-sm text-gray-600">Ürün: {order.productName}</p>
              <p className="text-sm text-gray-600">Toplam Miktar: {order.totalQuantity} {order.unit}</p>
              <p className="text-sm text-gray-600">Sipariş Tarihi: {formatDate(order.createdAt)}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Müşteri Bilgileri</h4>
              <p className="text-sm text-gray-600 flex items-center gap-1">
                <Building className="w-4 h-4" />
                {order.customer.company}
              </p>
              <p className="text-sm text-gray-600">{order.customer.name}</p>
              <p className="text-sm text-gray-600">{order.customer.phone}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Üretim İlerlemesi</h4>
              <Progress value={order.overallProgress} className="mb-2" />
              <p className="text-sm text-gray-600">%{order.overallProgress} tamamlandı</p>
            </div>
          </div>
          
          {order.productionNotes && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-1">Üretim Notları</h4>
              <p className="text-sm text-blue-800">{order.productionNotes}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delivery Schedules and Payments */}
      <Tabs defaultValue="deliveries" className="space-y-4">
        <TabsList>
          <TabsTrigger value="deliveries">Teslimat Programı</TabsTrigger>
          <TabsTrigger value="payments">Ödeme Takibi</TabsTrigger>
          <TabsTrigger value="shipping">Sevkiyat Bilgileri</TabsTrigger>
        </TabsList>

        <TabsContent value="deliveries" className="space-y-4">
          {order.deliverySchedules.map((delivery) => (
            <Card key={delivery.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    {delivery.deliveryNumber}. Teslimat - {delivery.quantity} {delivery.unit}
                  </CardTitle>
                  {getStatusBadge(delivery.status)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Planlanan Tarih</p>
                    <p className="text-sm text-gray-600 flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {formatDate(delivery.scheduledDate)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Ödeme Durumu</p>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(delivery.payment.status)}
                      <span className="text-sm text-gray-600">
                        {formatCurrency(delivery.payment.amount, delivery.payment.currency)}
                      </span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Kargo Takip</p>
                    <p className="text-sm text-gray-600">
                      {delivery.trackingNumber || 'Henüz kargoya verilmedi'}
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 mt-4">
                  {delivery.status === 'pending' && (
                    <Button 
                      onClick={() => handleUpdateDeliveryStatus(delivery.id, 'in_production')}
                      size="sm"
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      <PlayCircle className="w-4 h-4 mr-2" />
                      Üretime Başla
                    </Button>
                  )}
                  
                  {delivery.status === 'in_production' && (
                    <Button 
                      onClick={() => handleUpdateDeliveryStatus(delivery.id, 'ready')}
                      size="sm"
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Hazır Olarak İşaretle
                    </Button>
                  )}
                  
                  {delivery.status === 'ready' && (
                    <Button 
                      onClick={() => handleUpdateDeliveryStatus(delivery.id, 'shipped')}
                      size="sm"
                      className="bg-orange-600 hover:bg-orange-700"
                    >
                      <Truck className="w-4 h-4 mr-2" />
                      Kargoya Ver
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="w-5 h-5" />
                Ödeme Geçmişi
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.deliverySchedules.map((delivery) => (
                  <div key={delivery.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">
                        {delivery.deliveryNumber}. Teslimat Ödemesi
                      </h4>
                      {getStatusBadge(delivery.payment.status)}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-900">Net Tutar:</span>
                        <span className="ml-2">
                          {formatCurrency(delivery.payment.amount, delivery.payment.currency)}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-900">Alındı:</span>
                        <span className="ml-2">
                          {delivery.payment.receivedAt ? formatDate(delivery.payment.receivedAt) : '-'}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-900">Durum:</span>
                        <span className="ml-2">
                          {delivery.payment.status === 'completed' ? 'Hesabınıza geçti' : 
                           delivery.payment.status === 'received' ? 'İşleniyor' : 'Bekliyor'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="shipping" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Teslimat Adresi
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700">{order.shippingAddress}</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="w-5 h-5" />
                Sevkiyat Planı
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.deliverySchedules.map((delivery) => (
                  <div key={delivery.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">Sevkiyat #{delivery.deliveryNumber}</h4>
                      {getStatusBadge(delivery.status)}
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Miktar:</span>
                        <span className="ml-2 font-medium">{delivery.quantity} {delivery.unit}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Tarih:</span>
                        <span className="ml-2 font-medium">{formatDate(delivery.scheduledDate)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Takip No:</span>
                        <span className="ml-2 font-medium">{delivery.trackingNumber || '-'}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Shipping Plan Modal */}
      <ShippingPlanModal
        isOpen={showShippingPlanModal}
        onClose={() => setShowShippingPlanModal(false)}
        orderId={order.orderNumber}
        userType="producer"
        initialSchedules={order.deliverySchedules.map(d => ({
          id: d.id,
          deliveryNumber: d.deliveryNumber,
          quantity: d.quantity,
          unit: d.unit,
          proposedDate: d.scheduledDate,
          status: 'approved',
          proposedBy: 'producer' as const,
          producerNotes: `${d.deliveryNumber}. teslimat planı`
        }))}
        onSavePlan={(schedules) => {
          console.log('Shipping plan saved:', schedules);
          // Here you would save the plan via API
        }}
      />
    </div>
  );
}
