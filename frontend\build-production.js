#!/usr/bin/env node

/**
 * Frontend Production Build Script
 * Builds the Next.js frontend for production deployment
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Frontend Production Build Process...\n');

// Step 1: Clean previous build
console.log('🧹 Cleaning previous build...');
if (fs.existsSync('.next')) {
  fs.rmSync('.next', { recursive: true, force: true });
}
if (fs.existsSync('out')) {
  fs.rmSync('out', { recursive: true, force: true });
}
console.log('✅ Clean completed\n');

// Step 2: Install production dependencies
console.log('📦 Installing production dependencies...');
try {
  execSync('npm ci --only=production', { stdio: 'inherit' });
  console.log('✅ Dependencies installed\n');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

// Step 3: Type checking
console.log('🔍 Running type checking...');
try {
  execSync('npx tsc --noEmit', { stdio: 'inherit' });
  console.log('✅ Type checking passed\n');
} catch (error) {
  console.warn('⚠️ Type checking completed with warnings\n');
}

// Step 4: Linting
console.log('🔧 Running ESLint...');
try {
  execSync('npx eslint . --ext .ts,.tsx --fix', { stdio: 'inherit' });
  console.log('✅ Linting completed\n');
} catch (error) {
  console.warn('⚠️ Linting completed with warnings\n');
}

// Step 5: Build Next.js application
console.log('🏗️ Building Next.js application...');
try {
  // Set production environment
  process.env.NODE_ENV = 'production';
  execSync('npx next build', { 
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'production' }
  });
  console.log('✅ Next.js build completed\n');
} catch (error) {
  console.error('❌ Failed to build Next.js application:', error.message);
  process.exit(1);
}

// Step 6: Bundle analysis (optional)
if (process.env.ANALYZE === 'true') {
  console.log('📊 Running bundle analysis...');
  try {
    execSync('ANALYZE=true npx next build', { 
      stdio: 'inherit',
      env: { ...process.env, ANALYZE: 'true' }
    });
    console.log('✅ Bundle analysis completed\n');
  } catch (error) {
    console.warn('⚠️ Bundle analysis failed:', error.message);
  }
}

// Step 7: Create production start script
console.log('📝 Creating production start script...');
const startScript = `#!/usr/bin/env node

// Production startup script for Next.js
const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

const dev = false;
const hostname = process.env.HOSTNAME || 'localhost';
const port = process.env.PORT || 3000;

const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  }).listen(port, (err) => {
    if (err) throw err;
    console.log(\`🚀 Frontend server ready on http://\${hostname}:\${port}\`);
  });
});
`;

fs.writeFileSync('start-production.js', startScript);
console.log('✅ Production start script created\n');

// Step 8: Copy necessary files for production
console.log('📁 Preparing production files...');
const filesToCopy = [
  'package.json',
  'package-lock.json',
  '.env.production',
  'next.config.js',
  'public'
];

// Create production directory structure
const prodDir = 'production-build';
if (fs.existsSync(prodDir)) {
  fs.rmSync(prodDir, { recursive: true, force: true });
}
fs.mkdirSync(prodDir, { recursive: true });

// Copy .next build output
if (fs.existsSync('.next')) {
  fs.cpSync('.next', path.join(prodDir, '.next'), { recursive: true });
  console.log('✅ Copied .next build output');
}

// Copy necessary files
filesToCopy.forEach(file => {
  if (fs.existsSync(file)) {
    const destPath = path.join(prodDir, file);
    if (fs.statSync(file).isDirectory()) {
      fs.cpSync(file, destPath, { recursive: true });
    } else {
      fs.copyFileSync(file, destPath);
    }
    console.log(`✅ Copied ${file}`);
  }
});

// Copy start script
fs.copyFileSync('start-production.js', path.join(prodDir, 'start.js'));
console.log('✅ Copied production start script\n');

// Step 9: Create Docker-ready structure
console.log('🐳 Creating Docker-ready structure...');
const dockerignore = `node_modules
.next
.git
.env.local
.env.development
.env.test
*.log
coverage
.nyc_output
.DS_Store
Thumbs.db
`;

fs.writeFileSync(path.join(prodDir, '.dockerignore'), dockerignore);

const dockerfile = `FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci --only=production && npm cache clean --force

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=deps /app/node_modules ./node_modules
COPY . .

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "start.js"]
`;

fs.writeFileSync(path.join(prodDir, 'Dockerfile'), dockerfile);
console.log('✅ Docker files created\n');

// Step 10: Validate build
console.log('🔍 Validating production build...');
const requiredFiles = [
  path.join(prodDir, '.next'),
  path.join(prodDir, 'package.json'),
  path.join(prodDir, 'start.js'),
  path.join(prodDir, 'Dockerfile')
];

let buildValid = true;
requiredFiles.forEach(file => {
  if (!fs.existsSync(file)) {
    console.error(`❌ Missing required file: ${file}`);
    buildValid = false;
  }
});

if (buildValid) {
  console.log('✅ Build validation passed\n');
  console.log('🎉 Frontend production build completed successfully!');
  console.log('\n📋 Next steps:');
  console.log('1. Update .env.production with your production values');
  console.log('2. Deploy the production-build directory to your server');
  console.log('3. Run: cd production-build && node start.js');
  console.log('4. Or use Docker: docker build -t frontend . && docker run -p 3000:3000 frontend');
} else {
  console.error('❌ Build validation failed');
  process.exit(1);
}
