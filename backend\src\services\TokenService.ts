/**
 * JWT Token Service with Refresh Token Support
 * Implements secure token generation, validation, and rotation
 */

import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { PrismaClient } from '@prisma/client';
import { env } from '../config/environment';
import { logInfo, logWarn, logError } from '../utils/logger';

const prisma = new PrismaClient();

export interface TokenPayload {
  userId: string;
  email: string;
  userType: 'customer' | 'producer' | 'admin';
  sessionId?: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  refreshExpiresIn: number;
}

export interface RefreshTokenData {
  id: string;
  userId: string;
  tokenHash: string;
  expiresAt: Date;
  isRevoked: boolean;
  sessionId: string;
  userAgent?: string;
  ipAddress?: string;
  createdAt: Date;
}

export class TokenService {
  private readonly ACCESS_TOKEN_EXPIRY = '15m';
  private readonly REFRESH_TOKEN_EXPIRY = '7d';
  private readonly MAX_REFRESH_TOKENS_PER_USER = 5;

  /**
   * Generate a new token pair (access + refresh)
   */
  async generateTokenPair(
    payload: TokenPayload,
    userAgent?: string,
    ipAddress?: string
  ): Promise<TokenPair> {
    try {
      // Generate session ID if not provided
      const sessionId = payload.sessionId || this.generateSessionId();
      
      // Create access token
      const accessToken = jwt.sign(
        {
          ...payload,
          sessionId,
          type: 'access'
        },
        env.JWT_SECRET,
        { 
          expiresIn: this.ACCESS_TOKEN_EXPIRY,
          issuer: 'natural-stone-marketplace',
          audience: 'api'
        }
      );

      // Generate refresh token
      const refreshTokenValue = this.generateRefreshToken();
      const refreshTokenHash = this.hashToken(refreshTokenValue);

      // Calculate expiry dates
      const accessExpiresIn = 15 * 60; // 15 minutes in seconds
      const refreshExpiresIn = 7 * 24 * 60 * 60; // 7 days in seconds
      const refreshExpiresAt = new Date(Date.now() + refreshExpiresIn * 1000);

      // Clean up old refresh tokens for this user
      await this.cleanupOldRefreshTokens(payload.userId);

      // Store refresh token in database
      await prisma.refreshToken.create({
        data: {
          userId: payload.userId,
          tokenHash: refreshTokenHash,
          expiresAt: refreshExpiresAt,
          sessionId,
          userAgent,
          ipAddress,
          isRevoked: false
        }
      });

      logInfo('Token pair generated', {
        userId: payload.userId,
        sessionId,
        userAgent,
        ipAddress
      });

      return {
        accessToken,
        refreshToken: refreshTokenValue,
        expiresIn: accessExpiresIn,
        refreshExpiresIn
      };

    } catch (error) {
      logError('Failed to generate token pair', error as Error, {
        userId: payload.userId
      });
      throw new Error('Token generation failed');
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(
    refreshToken: string,
    userAgent?: string,
    ipAddress?: string
  ): Promise<TokenPair> {
    try {
      const refreshTokenHash = this.hashToken(refreshToken);

      // Find and validate refresh token
      const storedToken = await prisma.refreshToken.findFirst({
        where: {
          tokenHash: refreshTokenHash,
          isRevoked: false,
          expiresAt: {
            gt: new Date()
          }
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              userType: true,
              status: true
            }
          }
        }
      });

      if (!storedToken) {
        logWarn('Invalid or expired refresh token', {
          tokenHash: refreshTokenHash.substring(0, 8) + '...',
          userAgent,
          ipAddress
        });
        throw new Error('Invalid or expired refresh token');
      }

      // Check if user is still active
      if (storedToken.user.status !== 'ACTIVE') {
        logWarn('Refresh token used by inactive user', {
          userId: storedToken.userId,
          status: storedToken.user.status
        });
        throw new Error('User account is not active');
      }

      // Revoke the used refresh token (token rotation)
      await prisma.refreshToken.update({
        where: { id: storedToken.id },
        data: { isRevoked: true }
      });

      // Generate new token pair
      const newTokenPair = await this.generateTokenPair(
        {
          userId: storedToken.user.id,
          email: storedToken.user.email,
          userType: storedToken.user.userType as 'customer' | 'producer' | 'admin',
          sessionId: storedToken.sessionId
        },
        userAgent,
        ipAddress
      );

      logInfo('Access token refreshed', {
        userId: storedToken.userId,
        sessionId: storedToken.sessionId,
        oldTokenId: storedToken.id
      });

      return newTokenPair;

    } catch (error) {
      logError('Failed to refresh access token', error as Error, {
        userAgent,
        ipAddress
      });
      throw error;
    }
  }

  /**
   * Revoke refresh token
   */
  async revokeRefreshToken(refreshToken: string): Promise<void> {
    try {
      const refreshTokenHash = this.hashToken(refreshToken);

      const result = await prisma.refreshToken.updateMany({
        where: {
          tokenHash: refreshTokenHash,
          isRevoked: false
        },
        data: {
          isRevoked: true
        }
      });

      if (result.count > 0) {
        logInfo('Refresh token revoked', {
          tokenHash: refreshTokenHash.substring(0, 8) + '...'
        });
      }

    } catch (error) {
      logError('Failed to revoke refresh token', error as Error);
      throw new Error('Token revocation failed');
    }
  }

  /**
   * Revoke all refresh tokens for a user
   */
  async revokeAllUserTokens(userId: string): Promise<void> {
    try {
      await prisma.refreshToken.updateMany({
        where: {
          userId,
          isRevoked: false
        },
        data: {
          isRevoked: true
        }
      });

      logInfo('All user tokens revoked', { userId });

    } catch (error) {
      logError('Failed to revoke all user tokens', error as Error, { userId });
      throw new Error('Token revocation failed');
    }
  }

  /**
   * Verify access token
   */
  verifyAccessToken(token: string): TokenPayload {
    try {
      const decoded = jwt.verify(token, env.JWT_SECRET, {
        issuer: 'natural-stone-marketplace',
        audience: 'api'
      }) as any;

      if (decoded.type !== 'access') {
        throw new Error('Invalid token type');
      }

      return {
        userId: decoded.userId,
        email: decoded.email,
        userType: decoded.userType,
        sessionId: decoded.sessionId
      };

    } catch (error) {
      logWarn('Access token verification failed', {
        error: (error as Error).message
      });
      throw new Error('Invalid access token');
    }
  }

  /**
   * Generate cryptographically secure refresh token
   */
  private generateRefreshToken(): string {
    return crypto.randomBytes(64).toString('hex');
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Hash token for secure storage
   */
  private hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Clean up old refresh tokens for a user
   */
  private async cleanupOldRefreshTokens(userId: string): Promise<void> {
    try {
      // Get all active tokens for user, ordered by creation date
      const activeTokens = await prisma.refreshToken.findMany({
        where: {
          userId,
          isRevoked: false,
          expiresAt: {
            gt: new Date()
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // If we have too many tokens, revoke the oldest ones
      if (activeTokens.length >= this.MAX_REFRESH_TOKENS_PER_USER) {
        const tokensToRevoke = activeTokens.slice(this.MAX_REFRESH_TOKENS_PER_USER - 1);
        const tokenIds = tokensToRevoke.map(token => token.id);

        await prisma.refreshToken.updateMany({
          where: {
            id: {
              in: tokenIds
            }
          },
          data: {
            isRevoked: true
          }
        });

        logInfo('Old refresh tokens cleaned up', {
          userId,
          revokedCount: tokenIds.length
        });
      }

    } catch (error) {
      logError('Failed to cleanup old refresh tokens', error as Error, { userId });
      // Don't throw error, this is cleanup operation
    }
  }

  /**
   * Clean up expired refresh tokens (should be run periodically)
   */
  async cleanupExpiredTokens(): Promise<void> {
    try {
      const result = await prisma.refreshToken.deleteMany({
        where: {
          OR: [
            {
              expiresAt: {
                lt: new Date()
              }
            },
            {
              isRevoked: true,
              createdAt: {
                lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
              }
            }
          ]
        }
      });

      if (result.count > 0) {
        logInfo('Expired refresh tokens cleaned up', {
          deletedCount: result.count
        });
      }

    } catch (error) {
      logError('Failed to cleanup expired tokens', error as Error);
    }
  }
}

// Export singleton instance
export const tokenService = new TokenService();
