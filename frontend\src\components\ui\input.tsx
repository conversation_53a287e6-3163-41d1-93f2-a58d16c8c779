import * as React from "react"
import { cn } from "@/lib/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean
  helperText?: string
}

/**
 * Input component following RFC-004 UI/UX Design System
 * Natural Stone Marketplace themed input with accessibility support
 */
const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, helperText, ...props }, ref) => {
    return (
      <div className="w-full">
        <input
          type={type}
          className={cn(
            [
              // Base styles
              "flex w-full px-3 py-3 text-base font-normal",
              "bg-[var(--bg-primary)] text-[var(--text-primary)]",
              "border border-[var(--gray-300)] rounded-[var(--radius-md)]",
              "transition-[border-color] duration-200 ease-in-out",

              // Focus styles
              "focus:outline-none focus:border-[var(--primary-stone)]",
              "focus:shadow-[0_0_0_3px_rgb(139_115_85_/_0.1)]",

              // Placeholder styles
              "placeholder:text-[var(--gray-400)]",

              // File input styles
              "file:border-0 file:bg-transparent file:text-sm file:font-medium",
              "file:text-[var(--text-primary)]",

              // Disabled styles
              "disabled:cursor-not-allowed disabled:opacity-50",
              "disabled:bg-[var(--gray-100)]",

              // Mobile optimization (prevent zoom on iOS)
              "text-base sm:text-sm",

              // Error state
              error && [
                "border-[var(--error)]",
                "focus:border-[var(--error)]",
                "focus:shadow-[0_0_0_3px_rgb(239_68_68_/_0.1)]"
              ],

              // Invalid state
              "invalid:border-[var(--error)]"
            ].filter(Boolean).flat(),
            className
          )}
          ref={ref}
          {...props}
        />
        {helperText && (
          <p className={cn(
            "mt-1 text-sm",
            error ? "text-[var(--error)]" : "text-[var(--text-secondary)]"
          )}>
            {helperText}
          </p>
        )}
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input }
