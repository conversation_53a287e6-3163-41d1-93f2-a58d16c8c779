'use client'

import * as React from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  Eye, 
  TrendingUp,
  TrendingDown,
  BarChart3,
  Brain,
  Target,
  Calendar,
  DollarSign,
  Package,
  Users,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

interface PredictionModelModalProps {
  isOpen: boolean
  onClose: () => void
  onGeneratePrediction: (predictionConfig: any) => Promise<boolean>
  historicalData: any
}

export function PredictionModelModal({
  isOpen,
  onClose,
  onGeneratePrediction,
  historicalData
}: PredictionModelModalProps) {
  const [predictionConfig, setPredictionConfig] = React.useState({
    metric: 'revenue',
    period: '3months',
    model: 'linear',
    confidence: '80',
    includeSeasonality: true,
    includeMarketTrends: false,
    includeExternalFactors: false
  })
  const [isLoading, setIsLoading] = React.useState(false)
  const [step, setStep] = React.useState(1) // 1: Configuration, 2: Model Analysis, 3: Results

  const handleGeneratePrediction = async () => {
    setIsLoading(true)
    try {
      const success = await onGeneratePrediction(predictionConfig)
      if (success) {
        setStep(3)
        setTimeout(() => {
          onClose()
        }, 3000)
      }
    } catch (error) {
      console.error('Error generating prediction:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getModelDescription = (model: string) => {
    switch (model) {
      case 'linear':
        return 'Basit doğrusal trend analizi. Hızlı ve güvenilir.'
      case 'polynomial':
        return 'Karmaşık trend değişikliklerini yakalar. Orta seviye doğruluk.'
      case 'exponential':
        return 'Üstel büyüme/azalış modellemesi. Yüksek volatilite için uygun.'
      case 'seasonal':
        return 'Mevsimsel değişiklikleri dikkate alır. En kapsamlı model.'
      default:
        return ''
    }
  }

  const getPredictionAccuracy = () => {
    const baseAccuracy = {
      linear: 75,
      polynomial: 82,
      exponential: 78,
      seasonal: 88
    }
    
    let accuracy = baseAccuracy[predictionConfig.model as keyof typeof baseAccuracy]
    
    if (predictionConfig.includeSeasonality) accuracy += 5
    if (predictionConfig.includeMarketTrends) accuracy += 3
    if (predictionConfig.includeExternalFactors) accuracy += 2
    
    return Math.min(accuracy, 95)
  }

  const generateMockPredictions = () => {
    const currentValue = historicalData[predictionConfig.metric]?.slice(-1)[0] || 25000
    const predictions = []
    
    for (let i = 1; i <= 6; i++) {
      const trend = predictionConfig.model === 'exponential' ? 1.15 : 1.08
      const seasonality = predictionConfig.includeSeasonality ? (Math.sin(i * Math.PI / 6) * 0.1 + 1) : 1
      const noise = (Math.random() - 0.5) * 0.1 + 1
      
      const value = Math.round(currentValue * Math.pow(trend, i) * seasonality * noise)
      predictions.push({
        month: i,
        value,
        confidence: getPredictionAccuracy()
      })
    }
    
    return predictions
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(value)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            AI Destekli Tahmin Modeli
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step 1: Configuration */}
          {step === 1 && (
            <>
              {/* Model Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Tahmin Parametreleri
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="metric">Tahmin Edilecek Metrik</Label>
                      <Select 
                        value={predictionConfig.metric} 
                        onValueChange={(value) => setPredictionConfig(prev => ({ ...prev, metric: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="revenue">Gelir</SelectItem>
                          <SelectItem value="orders">Sipariş Sayısı</SelectItem>
                          <SelectItem value="customers">Müşteri Sayısı</SelectItem>
                          <SelectItem value="growth">Büyüme Oranı</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="period">Tahmin Dönemi</Label>
                      <Select 
                        value={predictionConfig.period} 
                        onValueChange={(value) => setPredictionConfig(prev => ({ ...prev, period: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1month">1 Ay</SelectItem>
                          <SelectItem value="3months">3 Ay</SelectItem>
                          <SelectItem value="6months">6 Ay</SelectItem>
                          <SelectItem value="1year">1 Yıl</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="model">Tahmin Modeli</Label>
                    <Select 
                      value={predictionConfig.model} 
                      onValueChange={(value) => setPredictionConfig(prev => ({ ...prev, model: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="linear">Doğrusal Regresyon</SelectItem>
                        <SelectItem value="polynomial">Polinom Regresyon</SelectItem>
                        <SelectItem value="exponential">Üstel Model</SelectItem>
                        <SelectItem value="seasonal">Mevsimsel Model</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-gray-600 mt-1">
                      {getModelDescription(predictionConfig.model)}
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="confidence">Güven Aralığı</Label>
                    <Select 
                      value={predictionConfig.confidence} 
                      onValueChange={(value) => setPredictionConfig(prev => ({ ...prev, confidence: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="70">%70</SelectItem>
                        <SelectItem value="80">%80</SelectItem>
                        <SelectItem value="90">%90</SelectItem>
                        <SelectItem value="95">%95</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* Advanced Options */}
              <Card>
                <CardHeader>
                  <CardTitle>Gelişmiş Seçenekler</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="includeSeasonality"
                      checked={predictionConfig.includeSeasonality}
                      onChange={(e) => setPredictionConfig(prev => ({ ...prev, includeSeasonality: e.target.checked }))}
                      className="text-blue-600"
                    />
                    <Label htmlFor="includeSeasonality">Mevsimsel değişiklikleri dahil et</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="includeMarketTrends"
                      checked={predictionConfig.includeMarketTrends}
                      onChange={(e) => setPredictionConfig(prev => ({ ...prev, includeMarketTrends: e.target.checked }))}
                      className="text-blue-600"
                    />
                    <Label htmlFor="includeMarketTrends">Pazar trendlerini analiz et</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="includeExternalFactors"
                      checked={predictionConfig.includeExternalFactors}
                      onChange={(e) => setPredictionConfig(prev => ({ ...prev, includeExternalFactors: e.target.checked }))}
                      className="text-blue-600"
                    />
                    <Label htmlFor="includeExternalFactors">Dış faktörleri dikkate al</Label>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={onClose}>
                  İptal
                </Button>
                <Button onClick={() => setStep(2)}>
                  Model Analizi
                </Button>
              </div>
            </>
          )}

          {/* Step 2: Model Analysis */}
          {step === 2 && (
            <>
              <Card className="bg-purple-50 border-purple-200">
                <CardHeader>
                  <CardTitle className="text-purple-800">Model Analizi</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="font-medium text-purple-700">Seçilen Model:</span>
                      <p className="text-purple-900">{predictionConfig.model}</p>
                    </div>
                    <div>
                      <span className="font-medium text-purple-700">Tahmin Doğruluğu:</span>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-100 text-green-800">
                          %{getPredictionAccuracy()}
                        </Badge>
                        {getPredictionAccuracy() > 85 && (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        )}
                      </div>
                    </div>
                    <div>
                      <span className="font-medium text-purple-700">Veri Kalitesi:</span>
                      <Badge className="bg-blue-100 text-blue-800">Yüksek</Badge>
                    </div>
                    <div>
                      <span className="font-medium text-purple-700">Güven Aralığı:</span>
                      <p className="text-purple-900">%{predictionConfig.confidence}</p>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg border">
                    <h4 className="font-medium mb-2">Geçmiş Veri Analizi</h4>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">
                          {historicalData.revenue?.length || 6}
                        </p>
                        <p className="text-gray-600">Veri Noktası</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">
                          +{Math.round(Math.random() * 20 + 10)}%
                        </p>
                        <p className="text-gray-600">Ortalama Büyüme</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-purple-600">
                          {Math.round(Math.random() * 15 + 5)}%
                        </p>
                        <p className="text-gray-600">Volatilite</p>
                      </div>
                    </div>
                  </div>

                  {getPredictionAccuracy() < 80 && (
                    <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-yellow-800">Uyarı</h4>
                          <p className="text-yellow-700 text-sm">
                            Model doğruluğu %80'in altında. Daha fazla geçmiş veri veya farklı model seçimi önerilir.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>
                  Geri Dön
                </Button>
                <Button 
                  onClick={handleGeneratePrediction}
                  disabled={isLoading}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Tahmin Oluşturuluyor...
                    </>
                  ) : (
                    <>
                      <Eye className="w-4 h-4 mr-2" />
                      Tahmin Oluştur
                    </>
                  )}
                </Button>
              </div>
            </>
          )}

          {/* Step 3: Results */}
          {step === 3 && (
            <>
              <Card className="bg-green-50 border-green-200">
                <CardHeader>
                  <CardTitle className="text-green-800 flex items-center gap-2">
                    <CheckCircle className="w-5 h-5" />
                    Tahmin Sonuçları
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {generateMockPredictions().slice(0, 3).map((prediction, index) => (
                        <div key={index} className="bg-white p-4 rounded-lg border text-center">
                          <div className="flex items-center justify-center gap-1 mb-2">
                            <Calendar className="w-4 h-4 text-gray-500" />
                            <span className="text-sm text-gray-600">
                              {prediction.month} ay sonra
                            </span>
                          </div>
                          <p className="text-2xl font-bold text-green-700">
                            {predictionConfig.metric === 'revenue' 
                              ? formatCurrency(prediction.value)
                              : prediction.value.toLocaleString()
                            }
                          </p>
                          <div className="flex items-center justify-center gap-1 mt-1">
                            <TrendingUp className="w-3 h-3 text-green-500" />
                            <span className="text-xs text-green-600">
                              %{prediction.confidence} güven
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="bg-white p-4 rounded-lg border">
                      <h4 className="font-medium mb-3">Önemli Bulgular</h4>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-start gap-2">
                          <TrendingUp className="w-4 h-4 text-green-500 mt-0.5" />
                          <span>Gelecek 3 ayda %{Math.round(Math.random() * 15 + 10)} büyüme bekleniyor</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Target className="w-4 h-4 text-blue-500 mt-0.5" />
                          <span>En yüksek performans {Math.floor(Math.random() * 3 + 2)}. ayda öngörülüyor</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <BarChart3 className="w-4 h-4 text-purple-500 mt-0.5" />
                          <span>Mevsimsel etkiler {predictionConfig.includeSeasonality ? 'dahil edildi' : 'dahil edilmedi'}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="text-center">
                <p className="text-sm text-gray-600 mb-4">
                  Tahmin raporu analiz paneline eklendi ve indirilebilir.
                </p>
                <Button onClick={onClose} className="bg-green-600 hover:bg-green-700">
                  Tamam
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
