'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  CheckCircle,
  DollarSign,
  Calendar,
  User,
  Package,
  MessageSquare,
  ArrowRight
} from 'lucide-react'
import { MessageModal } from '@/components/ui/message-modal'
import { RequestDetailsModal } from '@/components/ui/request-details-modal'

// Mock data for accepted requests
const mockAcceptedRequests = [
  {
    id: '3',
    customerName: 'DEF Yapı',
    customerEmail: '<EMAIL>',
    productName: 'Granit Blok',
    quantity: 50,
    unit: 'ton',
    requestDate: '2025-06-26',
    deadline: '2025-06-28',
    status: 'accepted',
    description: 'Anıt projesi için yüksek kaliteli granit blok.',
    deliveryLocation: 'Afyon, Türkiye',
    targetPrice: 750,
    myQuote: {
      price: 800,
      currency: 'USD',
      deliveryTime: '20 gün',
      notes: 'Premium kalite granit',
      quotedDate: '2025-06-27',
      acceptedDate: '2025-06-28'
    },
    specifications: {
      thickness: 'Blok',
      surface: 'Ham',
      packaging: 'Özel ambalaj',
      delivery: 'Fabrika teslim'
    },
    orderNumber: 'ORD-2025-001'
  }
]

export default function AcceptedRequests() {
  const { producer } = useProducerAuth()
  const [selectedRequest, setSelectedRequest] = React.useState<any>(null)
  const [isMessageModalOpen, setIsMessageModalOpen] = React.useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = React.useState(false)

  const handleSendMessage = (request: any) => {
    setSelectedRequest(request)
    setIsMessageModalOpen(true)
  }

  const handleViewDetails = (request: any) => {
    setSelectedRequest(request)
    setIsDetailsModalOpen(true)
  }

  const handleViewOrder = (request: any) => {
    // Navigate to order details
    window.location.href = `/producer/orders?order=${request.orderNumber}`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Kabul Edilen Teklifler</h1>
        <p className="text-gray-600">
          Müşteri tarafından kabul edilen ve siparişe dönüşen teklifler
        </p>
      </div>

      {/* Accepted Requests List */}
      <div className="space-y-4">
        {mockAcceptedRequests.map((request) => (
          <Card key={request.id} className="overflow-hidden border-green-200">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">
                      <CheckCircle className="w-4 h-4" />
                      Kabul Edildi
                    </div>
                    <span className="text-sm text-gray-500">#{request.id}</span>
                    <span className="text-xs text-green-600 font-medium">
                      Sipariş No: {request.orderNumber}
                    </span>
                    <span className="text-xs text-gray-600">
                      Kabul: {request.myQuote?.acceptedDate}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <User className="w-4 h-4" />
                        Müşteri
                      </div>
                      <div className="font-medium">{request.customerName}</div>
                      <div className="text-sm text-gray-500">{request.customerEmail}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <Package className="w-4 h-4" />
                        Ürün
                      </div>
                      <div className="font-medium">{request.productName}</div>
                      <div className="text-sm text-gray-500">{request.quantity} {request.unit}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <DollarSign className="w-4 h-4" />
                        Kabul Edilen Fiyat
                      </div>
                      <div className="font-medium text-green-600">
                        ${request.myQuote?.price} / {request.unit}
                      </div>
                      <div className="text-sm text-gray-500">
                        Toplam: ${((request.myQuote?.price || 0) * request.quantity).toLocaleString()}
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <Calendar className="w-4 h-4" />
                        Teslimat
                      </div>
                      <div className="font-medium">{request.myQuote?.deliveryTime}</div>
                      <div className="text-sm text-gray-500">{request.deliveryLocation}</div>
                    </div>
                  </div>

                  {/* Accepted Quote Details */}
                  <div className="mb-4 p-4 bg-green-50 rounded-lg border border-green-200">
                    <h4 className="font-medium text-green-800 mb-2">Kabul Edilen Teklif Detayları</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-green-600">Birim Fiyat:</span>
                        <span className="ml-2 font-medium">${request.myQuote?.price} / {request.unit}</span>
                      </div>
                      <div>
                        <span className="text-green-600">Toplam Tutar:</span>
                        <span className="ml-2 font-medium">
                          ${((request.myQuote?.price || 0) * request.quantity).toLocaleString()}
                        </span>
                      </div>
                      <div>
                        <span className="text-green-600">Teslimat Süresi:</span>
                        <span className="ml-2 font-medium">{request.myQuote?.deliveryTime}</span>
                      </div>
                    </div>
                    {request.myQuote?.notes && (
                      <div className="mt-2">
                        <span className="text-green-600 text-sm">Teklif Notları:</span>
                        <p className="text-sm text-green-700 mt-1">{request.myQuote.notes}</p>
                      </div>
                    )}
                  </div>

                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-2">Proje Açıklaması:</p>
                    <p className="text-sm">{request.description}</p>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Kalınlık:</span>
                      <span className="ml-2 font-medium">{request.specifications.thickness}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Yüzey:</span>
                      <span className="ml-2 font-medium">{request.specifications.surface}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Ambalaj:</span>
                      <span className="ml-2 font-medium">{request.specifications.packaging}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Teslimat:</span>
                      <span className="ml-2 font-medium">{request.specifications.delivery}</span>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col gap-2 ml-4">
                  <Button
                    className="bg-green-600 hover:bg-green-700"
                    onClick={() => handleViewOrder(request)}
                  >
                    <ArrowRight className="w-4 h-4 mr-1" />
                    Siparişi Görüntüle
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSendMessage(request)}
                  >
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Müşteriyle İletişim
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetails(request)}
                  >
                    Detayları Gör
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {mockAcceptedRequests.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <CheckCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Kabul edilen teklif bulunamadı
            </h3>
            <p className="text-gray-600">
              Henüz kabul edilen teklifiniz bulunmuyor.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Message Modal */}
      {isMessageModalOpen && selectedRequest && (
        <MessageModal
          isOpen={isMessageModalOpen}
          onClose={() => {
            setIsMessageModalOpen(false)
            setSelectedRequest(null)
          }}
          request={selectedRequest}
        />
      )}

      {/* Details Modal */}
      {isDetailsModalOpen && selectedRequest && (
        <RequestDetailsModal
          isOpen={isDetailsModalOpen}
          onClose={() => {
            setIsDetailsModalOpen(false)
            setSelectedRequest(null)
          }}
          request={selectedRequest}
        />
      )}
    </div>
  )
}
