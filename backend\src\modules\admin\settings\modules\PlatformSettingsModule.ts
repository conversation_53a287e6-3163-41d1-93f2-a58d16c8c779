import { SettingsModule } from '../core/SettingsModule';
import { SettingsCategory } from '../types/settings.types';

export class PlatformSettingsModule extends SettingsModule {
  protected category: SettingsCategory = 'PLATFORM';

  protected getDefaultSettings() {
    return {
      siteName: {
        key: 'site_name',
        value: 'Natural Stone Marketplace',
        type: 'string',
        category: this.category,
        description: 'Platform name displayed across the site'
      },
      siteDescription: {
        key: 'site_description',
        value: 'Turkey\'s leading natural stone marketplace',
        type: 'string',
        category: this.category,
        description: 'Site description for SEO'
      },
      maintenanceMode: {
        key: 'maintenance_mode',
        value: false,
        type: 'boolean',
        category: this.category,
        description: 'Enable maintenance mode'
      },
      maintenanceMessage: {
        key: 'maintenance_message',
        value: 'Site is under maintenance. Please check back later.',
        type: 'string',
        category: this.category,
        description: 'Message shown during maintenance'
      },
      defaultLanguage: {
        key: 'default_language',
        value: 'tr',
        type: 'string',
        category: this.category,
        description: 'Default platform language'
      },
      supportedLanguages: {
        key: 'supported_languages',
        value: ['tr', 'en'],
        type: 'array',
        category: this.category,
        description: 'Supported platform languages'
      },
      timezone: {
        key: 'timezone',
        value: 'Europe/Istanbul',
        type: 'string',
        category: this.category,
        description: 'Platform timezone'
      },
      currency: {
        key: 'default_currency',
        value: 'USD',
        type: 'string',
        category: this.category,
        description: 'Default currency'
      },
      supportedCurrencies: {
        key: 'supported_currencies',
        value: ['USD', 'EUR', 'TRY'],
        type: 'array',
        category: this.category,
        description: 'Supported currencies'
      }
    };
  }

  async validateSetting(key: string, value: any): Promise<boolean> {
    switch (key) {
      case 'site_name':
        return typeof value === 'string' && value.length > 0;
      case 'maintenance_mode':
        return typeof value === 'boolean';
      case 'default_language':
        return typeof value === 'string' && ['tr', 'en'].includes(value);
      case 'supported_languages':
        return Array.isArray(value) && value.every(lang => typeof lang === 'string');
      case 'default_currency':
        return typeof value === 'string' && ['USD', 'EUR', 'TRY'].includes(value);
      case 'supported_currencies':
        return Array.isArray(value) && value.every(curr => typeof curr === 'string');
      default:
        return true;
    }
  }
}
