/**
 * Asset Controller
 * Handles HTTP requests for 3D asset management
 */

import { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import { AssetManager } from '../services/AssetManager';
import { OptimizationService } from '../services/OptimizationService';

import { AssetType, AssetQuality, AssetFormat, AssetProcessingOptions } from '../types';

const assetManager = new AssetManager();
const optimizationService = new OptimizationService();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = process.env.ASSET_UPLOAD_DIR || './uploads/3d';
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.glb', '.gltf', '.fbx', '.obj', '.jpg', '.jpeg', '.png', '.webp', '.hdr', '.exr'];
    const ext = path.extname(file.originalname).toLowerCase();
    
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error(`Unsupported file type: ${ext}`));
    }
  }
});

export class AssetController {
  /**
   * Upload asset
   */
  static uploadAsset = [
    upload.single('file'),
    async (req: Request, res: Response) => {
      try {
        if (!req.file) {
          return res.status(400).json({
            success: false,
            message: 'No file uploaded'
          });
        }

        const { productId, name, description, tags, type } = req.body;

        if (!name) {
          return res.status(400).json({
            success: false,
            message: 'Asset name is required'
          });
        }

        const assetType = type || (req.file.mimetype.startsWith('image/') ? AssetType.TEXTURE : AssetType.MODEL_3D);
        
        const uploadRequest = {
          productId,
          name,
          description,
          type: assetType,
          tags: tags ? JSON.parse(tags) : []
        };

        const processingOptions: AssetProcessingOptions = {
          generateVariants: true,
          qualities: [AssetQuality.LOW, AssetQuality.MEDIUM, AssetQuality.HIGH],
          optimizeForWeb: true,
          generateThumbnails: true,
          compressDraco: assetType === AssetType.MODEL_3D,
          textureCompression: assetType === AssetType.TEXTURE ? 'basis' : 'none'
        };

        const asset = await assetManager.uploadAsset(req.file, uploadRequest, processingOptions);

        res.json({
          success: true,
          data: asset,
          message: 'Asset uploaded successfully'
        });
      } catch (error) {
        console.error('Asset upload error:', error);
        res.status(500).json({
          success: false,
          message: error instanceof Error ? error.message : 'Upload failed'
        });
      }
    }
  ];

  /**
   * Get asset by ID
   */
  static async getAsset(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const asset = await assetManager.getAsset(id);

      if (!asset) {
        return res.status(404).json({
          success: false,
          message: 'Asset not found'
        });
      }

      res.json({
        success: true,
        data: asset
      });
    } catch (error) {
      console.error('Get asset error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get asset'
      });
    }
  }

  /**
   * Get assets by product ID
   */
  static async getProductAssets(req: Request, res: Response) {
    try {
      const { productId } = req.params;
      const assets = await assetManager.getAssetsByProduct(productId);

      res.json({
        success: true,
        data: assets
      });
    } catch (error) {
      console.error('Get product assets error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get product assets'
      });
    }
  }

  /**
   * Download asset
   */
  static async downloadAsset(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { quality } = req.query;

      const asset = await assetManager.getAsset(id);
      if (!asset) {
        return res.status(404).json({
          success: false,
          message: 'Asset not found'
        });
      }

      let filePath = asset.filePath;

      // Use variant if quality is specified
      if (quality && asset.variants) {
        const variant = asset.variants.find(v => v.quality === quality);
        if (variant) {
          filePath = variant.filePath;
        }
      }

      // Set appropriate headers
      res.setHeader('Content-Type', asset.mimeType);
      res.setHeader('Content-Disposition', `attachment; filename="${asset.fileName}"`);
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1 year cache

      // Stream file
      res.sendFile(path.resolve(filePath));
    } catch (error) {
      console.error('Download asset error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to download asset'
      });
    }
  }

  /**
   * Delete asset
   */
  static async deleteAsset(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const success = await assetManager.deleteAsset(id);

      if (!success) {
        return res.status(404).json({
          success: false,
          message: 'Asset not found'
        });
      }

      res.json({
        success: true,
        message: 'Asset deleted successfully'
      });
    } catch (error) {
      console.error('Delete asset error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete asset'
      });
    }
  }

  /**
   * Optimize asset
   */
  static async optimizeAsset(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { qualities, enableDraco, generateLOD } = req.body;

      const asset = await assetManager.getAsset(id);
      if (!asset) {
        return res.status(404).json({
          success: false,
          message: 'Asset not found'
        });
      }

      const outputDir = path.dirname(asset.filePath);
      
      let result;
      if (asset.type === AssetType.MODEL_3D) {
        result = await optimizationService.optimizeModel(asset.filePath, outputDir, {
          qualities: qualities || [AssetQuality.LOW, AssetQuality.MEDIUM, AssetQuality.HIGH],
          enableDraco: enableDraco !== false,
          generateLOD: generateLOD !== false
        });
      } else if (asset.type === AssetType.TEXTURE) {
        result = await optimizationService.optimizeTextures(asset.filePath, outputDir, {
          qualities: qualities || [AssetQuality.LOW, AssetQuality.MEDIUM, AssetQuality.HIGH],
          formats: [AssetFormat.WEBP, AssetFormat.JPG],
          generateMipmaps: true
        });
      } else {
        return res.status(400).json({
          success: false,
          message: 'Asset type not supported for optimization'
        });
      }

      res.json({
        success: true,
        data: result,
        message: 'Asset optimized successfully'
      });
    } catch (error) {
      console.error('Optimize asset error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to optimize asset'
      });
    }
  }

  /**
   * Get asset processing status
   */
  static async getProcessingStatus(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const asset = await assetManager.getAsset(id);

      if (!asset) {
        return res.status(404).json({
          success: false,
          message: 'Asset not found'
        });
      }

      res.json({
        success: true,
        data: {
          status: asset.processingStatus,
          progress: asset.processingStatus === 'COMPLETED' ? 100 : 
                   asset.processingStatus === 'PROCESSING' ? 50 : 0,
          message: asset.processingLog || 'Processing...'
        }
      });
    } catch (error) {
      console.error('Get processing status error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get processing status'
      });
    }
  }

  /**
   * Get asset library
   */
  static async getAssetLibrary(req: Request, res: Response) {
    try {
      const { type, quality, limit = 50, offset = 0 } = req.query;

      // This would be implemented with proper database queries
      // For now, return a mock response
      res.json({
        success: true,
        data: {
          assets: [],
          total: 0,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string)
        }
      });
    } catch (error) {
      console.error('Get asset library error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get asset library'
      });
    }
  }

  /**
   * Bulk upload assets
   */
  static bulkUpload = [
    upload.array('files', 10), // Max 10 files
    async (req: Request, res: Response) => {
      try {
        const files = req.files as Express.Multer.File[];
        if (!files || files.length === 0) {
          return res.status(400).json({
            success: false,
            message: 'No files uploaded'
          });
        }

        const { productId } = req.body;
        const results = [];

        for (const file of files) {
          try {
            const assetType = file.mimetype.startsWith('image/') ? AssetType.TEXTURE : AssetType.MODEL_3D;
            
            const uploadRequest = {
              productId,
              name: path.parse(file.originalname).name,
              type: assetType,
              tags: []
            };

            const processingOptions: AssetProcessingOptions = {
              generateVariants: true,
              qualities: [AssetQuality.MEDIUM, AssetQuality.HIGH],
              optimizeForWeb: true,
              generateThumbnails: true
            };

            const asset = await assetManager.uploadAsset(file, uploadRequest, processingOptions);
            results.push({ success: true, asset });
          } catch (error) {
            results.push({ 
              success: false, 
              filename: file.originalname,
              error: error instanceof Error ? error.message : 'Upload failed'
            });
          }
        }

        res.json({
          success: true,
          data: results,
          message: `Processed ${files.length} files`
        });
      } catch (error) {
        console.error('Bulk upload error:', error);
        res.status(500).json({
          success: false,
          message: 'Bulk upload failed'
        });
      }
    }
  ];
}

export default AssetController;
