'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { withQuarryAccess } from '@/components/blocks/QuarryAccessControl'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft,
  Package,
  MapPin,
  Upload,
  Plus,
  Minus,
  Save,
  Eye,
  AlertTriangle,
  CheckCircle,
  Building2,
  Ruler,
  DollarSign,
  Camera,
  FileText,
  Info
} from 'lucide-react'

interface BlockFormData {
  // Temel Bilgiler
  name: string
  category: string
  description: string
  quarryId: string
  
  // Blok Özellikleri
  dimensions: {
    length: number // metre
    width: number  // metre
    height: number // metre
    volume: number // m³ (otomatik hesaplanır)
    weight: number // ton (yaklaşık)
  }
  
  // Kalite ve Özellikler
  quality: 'A' | 'B' | 'C'
  color: string
  pattern: string
  defects: string[]
  
  // Fiyat Bilgileri
  pricing: {
    pricePerTon: number
    pricePerM3: number
    currency: 'USD' | 'EUR' | 'TRY'
    minOrder: number // ton
  }
  
  // Medya
  media: {
    coverImage?: File
    blockImages: File[]
    videos: File[]
  }
  
  // Stok Bilgileri
  stock: {
    available: boolean
    quantity: number // adet
    location: string
    notes: string
  }
  
  // Durum
  status: 'draft' | 'active' | 'sold'
}

function AddBlockPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = React.useState(1)
  const [formData, setFormData] = React.useState<BlockFormData>({
    name: '',
    category: '',
    description: '',
    quarryId: '',
    dimensions: {
      length: '',
      width: '',
      height: '',
      volume: 0,
      weight: 0
    },
    quality: 'A',
    color: '',
    pattern: '',
    defects: [],
    pricing: {
      pricePerTon: 0,
      pricePerM3: 0,
      currency: 'USD',
      minOrder: 1
    },
    media: {
      blockImages: [],
      videos: []
    },
    stock: {
      available: true,
      quantity: 1,
      location: '',
      notes: ''
    },
    status: 'draft'
  })

  // Mock quarries data - gerçek uygulamada API'den gelecek
  const mockQuarries = [
    {
      id: '1',
      name: 'Afyon Merkez Ocağı',
      location: 'Afyon Merkez',
      stoneTypes: ['Afyon Beyaz Mermer', 'Afyon Sugar Mermer'],
      isOwner: true
    },
    {
      id: '2',
      name: 'Afyon Güney Ocağı',
      location: 'Afyon Güney',
      stoneTypes: ['Afyon Krem Mermer'],
      isOwner: true
    }
  ]

  const categories = ['Mermer', 'Granit', 'Traverten', 'Oniks', 'Andezit', 'Bazalt']
  const qualities = [
    { value: 'A', label: 'A Kalite', description: 'En yüksek kalite, minimum kusur' },
    { value: 'B', label: 'B Kalite', description: 'İyi kalite, küçük kusurlar olabilir' },
    { value: 'C', label: 'C Kalite', description: 'Standart kalite, belirgin kusurlar' }
  ]

  const steps = [
    { id: 1, title: 'Ocak Seçimi', description: 'Blokun çıkarıldığı ocağı seçin' },
    { id: 2, title: 'Temel Bilgiler', description: 'Blok adı ve kategorisi' },
    { id: 3, title: 'Boyutlar', description: 'Blok ölçüleri ve hacim' },
    { id: 4, title: 'Kalite & Özellikler', description: 'Kalite sınıfı ve görsel özellikler' },
    { id: 5, title: 'Fiyatlandırma', description: 'Fiyat ve satış koşulları' },
    { id: 6, title: 'Medya', description: 'Fotoğraf ve video yükleme' },
    { id: 7, title: 'Stok & Konum', description: 'Stok durumu ve lokasyon' },
    { id: 8, title: 'Önizleme', description: 'Son kontrol ve kaydetme' }
  ]

  // Hacim hesaplama
  React.useEffect(() => {
    const length = parseFloat(formData.dimensions.length) || 0
    const width = parseFloat(formData.dimensions.width) || 0
    const height = parseFloat(formData.dimensions.height) || 0

    if (length > 0 && width > 0 && height > 0) {
      const volume = length * width * height
      const weight = volume * 2.7 // Ortalama taş yoğunluğu (ton/m³)

      setFormData(prev => ({
        ...prev,
        dimensions: {
          ...prev.dimensions,
          volume: Math.round(volume * 100) / 100,
          weight: Math.round(weight * 100) / 100
        }
      }))
    }
  }, [formData.dimensions.length, formData.dimensions.width, formData.dimensions.height])

  // Fiyat hesaplama
  React.useEffect(() => {
    const { pricePerTon } = formData.pricing
    const { volume } = formData.dimensions
    
    if (pricePerTon > 0 && volume > 0) {
      const pricePerM3 = (pricePerTon * 2.7) // Ortalama yoğunluk
      setFormData(prev => ({
        ...prev,
        pricing: {
          ...prev.pricing,
          pricePerM3: Math.round(pricePerM3 * 100) / 100
        }
      }))
    }
  }, [formData.pricing.pricePerTon, formData.dimensions.volume])

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSave = async (status: 'draft' | 'active') => {
    try {
      const blockData = {
        ...formData,
        status,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      console.log('Saving block:', blockData)
      
      // API call would go here
      // await saveBlock(blockData)
      
      router.push('/producer/blocks')
    } catch (error) {
      console.error('Error saving block:', error)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Ocak Seçimi</h2>
              <p className="text-gray-600">
                Blokun çıkarıldığı ocağı seçin. Sadece sahip olduğunuz ocaklar görüntülenir.
              </p>
            </div>

            <div className="space-y-4">
              {mockQuarries.map((quarry) => (
                <Card 
                  key={quarry.id} 
                  className={`p-4 cursor-pointer transition-all ${
                    formData.quarryId === quarry.id 
                      ? 'border-orange-500 bg-orange-50' 
                      : 'hover:border-gray-300'
                  }`}
                  onClick={() => setFormData(prev => ({ ...prev, quarryId: quarry.id }))}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Building2 className="w-6 h-6 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{quarry.name}</h3>
                        <p className="text-sm text-gray-600 flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {quarry.location}
                        </p>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {quarry.stoneTypes.map((stone, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {stone}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    {quarry.isOwner && (
                      <Badge className="bg-green-100 text-green-800">
                        Ocak Sahibi
                      </Badge>
                    )}
                  </div>
                </Card>
              ))}
            </div>

            {mockQuarries.length === 0 && (
              <div className="text-center py-12">
                <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Ocak Bulunamadı</h3>
                <p className="text-gray-600 mb-4">
                  Blok satabilmek için önce bir ocağa sahip olmanız gerekiyor.
                </p>
                <Button variant="outline" onClick={() => router.push('/producer/quarries/add')}>
                  Ocak Ekle
                </Button>
              </div>
            )}
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Temel Bilgiler</h2>
              <p className="text-gray-600">
                Blokun temel bilgilerini girin
              </p>
            </div>

            <Card className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Blok Adı *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Örn: Afyon Beyaz Mermer Blok - A Kalite"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kategori *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  >
                    <option value="">Kategori Seçin</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Açıklama
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Blok hakkında detaylı bilgi verin..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>
              </div>
            </Card>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Blok Boyutları</h2>
              <p className="text-gray-600">
                Blokun ölçülerini metre cinsinden girin
              </p>
            </div>

            <Card className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Uzunluk (m) *
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.dimensions.length}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      dimensions: { ...prev.dimensions, length: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Genişlik (m) *
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.dimensions.width}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      dimensions: { ...prev.dimensions, width: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Yükseklik (m) *
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.dimensions.height}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      dimensions: { ...prev.dimensions, height: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>
              </div>

              {/* Otomatik Hesaplamalar */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-3">Otomatik Hesaplamalar</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Hacim:</span>
                    <span className="font-medium">{formData.dimensions.volume} m³</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Yaklaşık Ağırlık:</span>
                    <span className="font-medium">{formData.dimensions.weight} ton</span>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Kalite ve Özellikler</h2>
              <p className="text-gray-600">
                Blokun kalite sınıfı ve görsel özelliklerini belirtin
              </p>
            </div>

            <Card className="p-6">
              <div className="space-y-6">
                {/* Kalite Seçimi */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Kalite Sınıfı *
                  </label>
                  <div className="space-y-3">
                    {qualities.map((quality) => (
                      <div
                        key={quality.value}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                          formData.quality === quality.value
                            ? 'border-orange-500 bg-orange-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setFormData(prev => ({ ...prev, quality: quality.value as 'A' | 'B' | 'C' }))}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900">{quality.label}</h4>
                            <p className="text-sm text-gray-600">{quality.description}</p>
                          </div>
                          <div className={`w-4 h-4 rounded-full border-2 ${
                            formData.quality === quality.value
                              ? 'border-orange-500 bg-orange-500'
                              : 'border-gray-300'
                          }`} />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Renk */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Renk
                  </label>
                  <input
                    type="text"
                    value={formData.color}
                    onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                    placeholder="Örn: Beyaz, Krem, Gri"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>

                {/* Desen */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Desen/Yapı
                  </label>
                  <input
                    type="text"
                    value={formData.pattern}
                    onChange={(e) => setFormData(prev => ({ ...prev, pattern: e.target.value }))}
                    placeholder="Örn: Düz, Damarlı, Bulutlu"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>
              </div>
            </Card>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Fiyatlandırma</h2>
              <p className="text-gray-600">
                Blokun satış fiyatını belirleyin
              </p>
            </div>

            <Card className="p-6">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Ton Başına Fiyat *
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        min="0"
                        value={formData.pricing.pricePerTon}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          pricing: { ...prev.pricing, pricePerTon: parseFloat(e.target.value) || 0 }
                        }))}
                        className="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        {formData.pricing.currency}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Para Birimi
                    </label>
                    <select
                      value={formData.pricing.currency}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        pricing: { ...prev.pricing, currency: e.target.value as 'USD' | 'EUR' | 'TRY' }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    >
                      <option value="USD">USD ($)</option>
                      <option value="EUR">EUR (€)</option>
                      <option value="TRY">TRY (₺)</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Minimum Sipariş (ton)
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={formData.pricing.minOrder}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      pricing: { ...prev.pricing, minOrder: parseInt(e.target.value) || 1 }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>

                {/* Otomatik Hesaplamalar */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-3">Fiyat Hesaplamaları</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">M³ Başına Fiyat:</span>
                      <span className="font-medium">{formData.pricing.currency} {formData.pricing.pricePerM3}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Bu Blokun Toplam Değeri:</span>
                      <span className="font-medium text-orange-600">
                        {formData.pricing.currency} {(formData.pricing.pricePerTon * formData.dimensions.weight).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )

      case 6:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Medya Yükleme</h2>
              <p className="text-gray-600">
                Blokun fotoğraflarını ve videolarını yükleyin
              </p>
            </div>

            <Card className="p-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kapak Fotoğrafı *
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">Kapak fotoğrafını yükleyin</p>
                    <input type="file" accept="image/*" className="hidden" />
                    <Button variant="outline">Dosya Seç</Button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ek Fotoğraflar
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Camera className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">Blokun farklı açılardan fotoğrafları</p>
                    <input type="file" accept="image/*" multiple className="hidden" />
                    <Button variant="outline">Fotoğrafları Seç</Button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Video (Opsiyonel)
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">Blok tanıtım videosu</p>
                    <input type="file" accept="video/*" className="hidden" />
                    <Button variant="outline">Video Seç</Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )

      case 7:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Stok ve Konum</h2>
              <p className="text-gray-600">
                Blokun stok durumu ve bulunduğu konumu belirtin
              </p>
            </div>

            <Card className="p-6">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Stok Durumu
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="stockStatus"
                          checked={formData.stock.available}
                          onChange={() => setFormData(prev => ({
                            ...prev,
                            stock: { ...prev.stock, available: true }
                          }))}
                          className="mr-2"
                        />
                        Stokta Mevcut
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="stockStatus"
                          checked={!formData.stock.available}
                          onChange={() => setFormData(prev => ({
                            ...prev,
                            stock: { ...prev.stock, available: false }
                          }))}
                          className="mr-2"
                        />
                        Satıldı / Mevcut Değil
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Adet
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={formData.stock.quantity}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        stock: { ...prev.stock, quantity: parseInt(e.target.value) || 0 }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bulunduğu Konum *
                  </label>
                  <input
                    type="text"
                    value={formData.stock.location}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      stock: { ...prev.stock, location: e.target.value }
                    }))}
                    placeholder="Örn: Afyon Depo, Fabrika Sahası"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notlar
                  </label>
                  <textarea
                    value={formData.stock.notes}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      stock: { ...prev.stock, notes: e.target.value }
                    }))}
                    placeholder="Blok hakkında ek notlar..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>
              </div>
            </Card>
          </div>
        )

      case 8:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Önizleme ve Kaydet</h2>
              <p className="text-gray-600">
                Blok bilgilerini kontrol edin ve kaydedin
              </p>
            </div>

            <Card className="p-6">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Temel Bilgiler</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Blok Adı:</span>
                        <span className="font-medium">{formData.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Kategori:</span>
                        <span className="font-medium">{formData.category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Kalite:</span>
                        <span className="font-medium">{formData.quality} Kalite</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Boyutlar</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Ölçüler:</span>
                        <span className="font-medium">
                          {formData.dimensions.length}×{formData.dimensions.width}×{formData.dimensions.height}m
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Hacim:</span>
                        <span className="font-medium">{formData.dimensions.volume} m³</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Ağırlık:</span>
                        <span className="font-medium">{formData.dimensions.weight} ton</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Fiyatlandırma</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Ton Fiyatı:</span>
                        <span className="font-medium">{formData.pricing.currency} {formData.pricing.pricePerTon}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Toplam Değer:</span>
                        <span className="font-medium text-orange-600">
                          {formData.pricing.currency} {(formData.pricing.pricePerTon * formData.dimensions.weight).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Stok</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Durum:</span>
                        <span className="font-medium">
                          {formData.stock.available ? 'Stokta' : 'Mevcut Değil'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Konum:</span>
                        <span className="font-medium">{formData.stock.location}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )

      default:
        return <div>Step {currentStep} content</div>
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Geri
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">Yeni Blok Ekle</h1>
            </div>

            <div className="text-sm text-gray-500">
              Adım {currentStep} / {steps.length}
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center flex-1">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep >= step.id
                    ? 'bg-orange-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step.id}
                </div>
                <div className="ml-3 flex-1">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-orange-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-500">{step.description}</p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-full h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-orange-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderStepContent()}

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
          >
            Önceki
          </Button>

          <div className="flex gap-3">
            {currentStep === steps.length ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => handleSave('draft')}
                >
                  Taslak Kaydet
                </Button>
                <Button
                  onClick={() => handleSave('active')}
                  className="bg-orange-600 hover:bg-orange-700"
                >
                  Yayınla
                </Button>
              </>
            ) : (
              <Button
                onClick={handleNext}
                className="bg-orange-600 hover:bg-orange-700"
                disabled={
                  (currentStep === 1 && !formData.quarryId) ||
                  (currentStep === 2 && (!formData.name || !formData.category))
                }
              >
                Sonraki
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default withQuarryAccess(AddBlockPage)
