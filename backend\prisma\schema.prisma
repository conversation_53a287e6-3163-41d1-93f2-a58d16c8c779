// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserType {
  producer
  customer
  admin
}

enum UserStatus {
  PENDING
  ACTIVE
  SUSPENDED
  BANNED
}

enum VerificationStatus {
  PENDING
  VERIFIED
  REJECTED
}

enum ProductStatus {
  DRAFT
  PENDING
  APPROVED
  REJECTED
}

enum BidRequestStatus {
  ACTIVE
  CLOSED
  CANCELLED
}

enum BidStatus {
  SUBMITTED
  SELECTED
  REJECTED
  EXPIRED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PRODUCTION
  SHIPPED
  DELIVERED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  RECEIPT_UPLOADED
  COMPLETED
  FAILED
  REFUNDED
}

enum EscrowStatus {
  PENDING
  HELD
  RELEASED
  REFUNDED
}

enum CompanyType {
  PRODUCER
  IMPORTER
  DISTRIBUTOR
  WAREHOUSE
  APPLICATOR
  ARCHITECT
  CONSTRUCTION
}

enum LocationType {
  QUARRY
  FACTORY
}

enum SurfaceFinish {
  RAW
  HONED
  POLISHED
  BRUSHED
  SANDBLASTED
  FLAMED
  ANTIQUE
  FILLED
}

enum PriceListType {
  DIMENSIONAL
  SURFACE_FINISH
  SLAB
}

enum PackagingType {
  PALLET
  CRATE
  BULK
}

enum DeliveryType {
  FACTORY
  PORT
  DESTINATION
}

enum DeliveryTerms {
  FOB
  CIF
  DDP
  EXW
}

enum Unit {
  M2
  TON
  PIECE
  M3
}

enum PaymentMethod {
  CREDIT_CARD
  BANK_TRANSFER
  PAYPAL
  LETTER_OF_CREDIT
}

enum PaymentType {
  FULL_PAYMENT
  ADVANCE_PAYMENT
  DELIVERY_PAYMENT
  FINAL_PAYMENT
  COMMISSION_PAYMENT
}

enum MediaType {
  IMAGE
  VIDEO
  THREE_SIXTY_VIEW
  THREE_D_MODEL
}

enum NotificationType {
  BID_RECEIVED
  BID_SELECTED
  PAYMENT_RECEIVED
  PAYMENT_RECEIPT_UPLOADED
  PAYMENT_APPROVED
  PAYMENT_REJECTED
  ORDER_CONFIRMED
  ORDER_SHIPPED
  ORDER_DELIVERED
  SYSTEM_ANNOUNCEMENT
  QUOTE_ACCEPTED
  QUOTE_REJECTED
}

enum OrderTrackingStatus {
  ORDER_CONFIRMED
  PRODUCTION_STARTED
  QUALITY_CONTROL
  PACKAGING
  READY_FOR_SHIPMENT
  SHIPPED
  IN_TRANSIT
  CUSTOMS_CLEARANCE
  DELIVERED
}

enum AINewsCategory {
  MARKET_PRICES
  TECHNOLOGY
  REGULATIONS
  EVENTS
  COMPETITION
}

enum Sentiment {
  POSITIVE
  NEGATIVE
  NEUTRAL
}

enum EmailCampaignStatus {
  DRAFT
  SCHEDULED
  SENDING
  SENT
  CANCELLED
}

enum ChatbotIntent {
  PRODUCT_INQUIRY
  PRODUCT_SPECIFICATIONS
  PRODUCT_PRICING
  PRODUCT_AVAILABILITY
  ORDER_STATUS
  ORDER_TRACKING
  ORDER_MODIFICATION
  ORDER_CANCELLATION
  BID_PROCESS
  BID_STATUS
  BID_REQUIREMENTS
  ACCOUNT_SETUP
  PROFILE_UPDATE
  VERIFICATION_STATUS
  PAYMENT_METHODS
  PAYMENT_STATUS
  REFUND_REQUEST
  TECHNICAL_ISSUE
  PLATFORM_USAGE
  GREETING
  GOODBYE
  HUMAN_HANDOFF
  COMPLAINT
  UNKNOWN
}

enum ChatbotMessageRole {
  USER
  ASSISTANT
  SYSTEM
}

enum ConversationStatus {
  ACTIVE
  ESCALATED
  CLOSED
  EXPIRED
}

enum AgentStatus {
  AVAILABLE
  BUSY
  OFFLINE
}

enum SampleRequestStatus {
  PENDING
  APPROVED
  REJECTED
  PREPARING
  SHIPPED
  DELIVERED
  EVALUATED
}

enum AssetType {
  MODEL_3D
  TEXTURE
  MATERIAL
  ENVIRONMENT
}

enum AssetFormat {
  GLB
  GLTF
  FBX
  OBJ
  JPG
  PNG
  WEBP
  HDR
  EXR
}

enum AssetQuality {
  LOW
  MEDIUM
  HIGH
  ULTRA
}

enum ProcessingStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

// Models
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  passwordHash      String
  password          String?   // For backward compatibility
  firstName         String?
  lastName          String?
  companyName       String?
  userType          UserType
  status            UserStatus @default(PENDING)
  emailVerified     Boolean   @default(false)
  twoFactorEnabled  Boolean   @default(false)
  lastLoginAt       DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  profile               UserProfile?
  products              Product[]
  bidRequests           BidRequest[]
  bids                  Bid[]
  customerOrders        Order[]   @relation("CustomerOrders")
  producerOrders        Order[]   @relation("ProducerOrders")
  payments              Payment[]
  verifiedPayments      Payment[] @relation("PaymentVerifier")
  notifications         Notification[]
  auditLogs             AuditLog[]
  stoneAnalysisReports  StoneAnalysisReport[]
  orderTracking         OrderTracking[]
  customerReviews       QualityReview[] @relation("CustomerReviews")
  producerReviews       QualityReview[] @relation("ProducerReviews")
  customerDisputes      Dispute[] @relation("CustomerDisputes")
  producerDisputes      Dispute[] @relation("ProducerDisputes")
  chatbotConversations  ChatbotConversation[]
  chatbotMessages       ChatbotMessage[]
  chatbotFeedback       ChatbotFeedback[]
  agentProfile          ChatbotAgent?
  viewerSessions        ViewerSession[]
  customerSampleRequests SampleRequest[] @relation("CustomerSampleRequests")
  producerSampleRequests SampleRequest[] @relation("ProducerSampleRequests")
  customerQuoteRequests QuoteRequest[] @relation("CustomerQuoteRequests")
  producerQuotes        Quote[] @relation("ProducerQuotes")
  customerQuotes        Quote[] @relation("CustomerQuotes")
  customerEscrows       EscrowAccount[] @relation("CustomerEscrows")
  producerEscrows       EscrowAccount[] @relation("ProducerEscrows")
  refreshTokens         RefreshToken[]


  @@map("users")
}

model UserProfile {
  id                          String            @id @default(cuid())
  userId                      String            @unique
  companyName                 String
  companyType                 CompanyType?
  taxNumber                   String?
  tradeRegistryNumber         String?
  contactPerson               String?
  phone                       String?
  address                     Json?
  city                        String?
  country                     String?
  state                       String?
  postalCode                  String?
  businessType                String?
  website                     String?
  countryCode                 String
  businessDescription         String?
  productionCapacity          Int?
  serviceCountries            String[]

  // Producer Specific Fields (PRD requirements)
  productionCapacityReport    Json?
  certificates                Json?
  bankInformation             Json?
  offersCustomManufacturing   Boolean           @default(false)
  customManufacturingDetails  String?
  companyIntroduction         String?

  verificationStatus          VerificationStatus @default(PENDING)
  verificationDocuments       Json?
  verifiedAt                  DateTime?
  verifiedBy                  String?
  createdAt                   DateTime          @default(now())
  updatedAt                   DateTime          @updatedAt

  // Relations
  user                        User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  producerLocations           ProducerLocation[]

  @@map("user_profiles")
}

model ProductCategory {
  id          String    @id @default(cuid())
  name        String
  description String?
  parentId    String?
  slug        String    @unique
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  parent              ProductCategory? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children            ProductCategory[] @relation("CategoryHierarchy")
  products            Product[]
  stoneAnalysisReports StoneAnalysisReport[]

  @@map("product_categories")
}

model Product {
  id                    String        @id @default(cuid())
  producerId            String
  name                  String
  description           String?
  categoryId            String
  specifications        Json
  dimensions            Json?
  surfaceFinish         SurfaceFinish @default(RAW)

  // Analysis Report Reference
  analysisReportId      String?

  basePrice             Decimal?      @db.Decimal(10, 2)
  currency              String        @default("USD")
  minimumOrderQuantity  Int?
  productionTimeDays    Int?

  // Location References
  quarryLocationId      String?
  factoryLocationId     String?

  status                ProductStatus @default(DRAFT)
  isActive              Boolean       @default(true)
  featured              Boolean       @default(false)
  slug                  String        @unique
  searchKeywords        String[]
  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt

  // Relations
  producer              User          @relation(fields: [producerId], references: [id])
  category              ProductCategory @relation(fields: [categoryId], references: [id])
  analysisReport        StoneAnalysisReport? @relation(fields: [analysisReportId], references: [id])
  quarryLocation        ProducerLocation? @relation("QuarryLocation", fields: [quarryLocationId], references: [id])
  factoryLocation       ProducerLocation? @relation("FactoryLocation", fields: [factoryLocationId], references: [id])

  images                ProductImage[]
  media                 ProductMedia[]
  priceLists            ProductPriceList[]
  blockProducts         BlockProduct[]
  bidRequests           BidRequestProduct[]
  orderItems            OrderItem[]
  assets3D              Asset3D[]
  materials             MaterialDefinition[] @relation("ProductMaterial")
  viewerConfiguration   ViewerConfiguration?
  viewerSessions        ViewerSession[]
  quoteRequests         QuoteRequest[] @relation("ProductQuoteRequests")
  quotes                Quote[] @relation("QuoteProducts")
  reviews               QualityReview[] @relation("ProductReviews")

  @@map("products")
}

model ProductImage {
  id          String   @id @default(cuid())
  productId   String
  url         String
  alt         String?  // Alternative name for altText
  altText     String?
  isPrimary   Boolean  @default(false)
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())

  // Relations
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model BidRequest {
  id              String           @id @default(cuid())
  customerId      String
  anonymousId     String           @unique
  title           String
  description     String
  specifications  Json
  quantity        Int
  unit            String
  deliveryAddress Json
  deliveryDate    DateTime?
  deliveryTerms   String?
  bidDeadline     DateTime
  autoClose       Boolean          @default(true)
  maxSuppliers    Int              @default(10)
  status          BidRequestStatus @default(ACTIVE)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  // Relations
  customer        User             @relation(fields: [customerId], references: [id])
  bids            Bid[]
  products        BidRequestProduct[]
  orders          Order[]

  @@map("bid_requests")
}

model BidRequestProduct {
  id            String     @id @default(cuid())
  bidRequestId  String
  productId     String
  createdAt     DateTime   @default(now())

  // Relations
  bidRequest    BidRequest @relation(fields: [bidRequestId], references: [id], onDelete: Cascade)
  product       Product    @relation(fields: [productId], references: [id])

  @@unique([bidRequestId, productId])
  @@map("bid_request_products")
}

model Bid {
  id              String    @id @default(cuid())
  bidRequestId    String
  producerId      String
  anonymousId     String    @unique
  unitPrice       Decimal   @db.Decimal(10, 2)
  totalPrice      Decimal   @db.Decimal(12, 2)
  currency        String    @default("USD")
  deliveryTimeDays Int
  paymentTerms    String?
  validityDays    Int       @default(30)
  notes           String?
  attachments     Json?
  status          BidStatus @default(SUBMITTED)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  bidRequest      BidRequest @relation(fields: [bidRequestId], references: [id])
  producer        User       @relation(fields: [producerId], references: [id])
  orders          Order[]

  @@unique([bidRequestId, producerId])
  @@map("bids")
}

model Order {
  id                    String      @id @default(cuid())
  orderNumber           String      @unique
  customerId            String
  producerId            String
  bidId                 String?
  bidRequestId          String?
  quoteId               String?
  items                 Json
  subtotal              Decimal     @db.Decimal(12, 2)
  taxAmount             Decimal     @db.Decimal(10, 2) @default(0)
  shippingCost          Decimal     @db.Decimal(10, 2) @default(0)
  totalAmount           Decimal     @db.Decimal(12, 2)
  currency              String      @default("USD")
  paymentMethod         String?     // Added missing paymentMethod field
  notes                 String?

  // Platform Commission (PRD requirement)
  totalM2               Decimal?    @db.Decimal(10, 2)
  totalTons             Decimal?    @db.Decimal(10, 3)
  platformCommission    Decimal?    @db.Decimal(10, 2)

  deliveryAddress       Json
  shippingAddress       Json?             // Added missing shippingAddress field
  deliveryTerms         DeliveryTerms?
  estimatedDeliveryDate DateTime?
  trackingNumber        String?     // Added missing trackingNumber field
  estimatedDelivery     DateTime?   // Added missing estimatedDelivery field
  status                OrderStatus @default(PENDING)
  confirmedAt           DateTime?
  shippedAt             DateTime?
  deliveredAt           DateTime?
  createdAt             DateTime    @default(now())
  updatedAt             DateTime    @updatedAt

  // Relations
  customer              User        @relation("CustomerOrders", fields: [customerId], references: [id])
  producer              User        @relation("ProducerOrders", fields: [producerId], references: [id])
  bid                   Bid?        @relation(fields: [bidId], references: [id])
  bidRequest            BidRequest? @relation(fields: [bidRequestId], references: [id])
  quote                 Quote?      @relation("QuoteOrders", fields: [quoteId], references: [id])
  payments              Payment[]
  tracking              OrderTracking[]
  reviews               QualityReview[]
  commissions           Commission[]
  dispute               Dispute?
  orderItems            OrderItem[]
  shipments             Shipment[]  @relation("OrderShipments")
  escrowAccount         EscrowAccount?
  deliverySchedules     DeliverySchedule[]
  paymentSchedules      PaymentSchedule[]
  paymentTracking       PaymentTracking[]

  @@map("orders")
}

model DeliverySchedule {
  id                    String        @id @default(cuid())
  orderId               String
  deliveryNumber        Int
  quantity              Decimal       @db.Decimal(10, 2)
  unit                  String
  scheduledDate         DateTime
  status                DeliveryStatus @default(PENDING)
  trackingNumber        String?
  estimatedDelivery     DateTime?
  actualDelivery        DateTime?
  notes                 String?
  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt

  // Relations
  order                 Order         @relation(fields: [orderId], references: [id])
  payment               Payment?      @relation("DeliveryPayment")

  @@unique([orderId, deliveryNumber])
  @@map("delivery_schedules")
}

enum DeliveryStatus {
  PENDING
  IN_PRODUCTION
  READY
  SHIPPED
  DELIVERED
  CANCELLED
}

model Shipment {
  id                    String    @id @default(cuid())
  orderId               String
  trackingNumber        String?
  status                String    @default("PENDING")
  estimatedDelivery     DateTime?
  actualDelivery        DateTime?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // Relations
  order                 Order     @relation("OrderShipments", fields: [orderId], references: [id])

  @@map("shipments")
}

// Order items model for detailed order tracking
model OrderItem {
  id            String  @id @default(cuid())
  orderId       String
  productId     String
  quantity      Int
  unitPrice     Decimal @db.Decimal(10, 2)
  totalPrice    Decimal @db.Decimal(12, 2)
  specifications Json?
  dimensions    Json?   // Added missing dimensions field

  // Relations
  order         Order   @relation(fields: [orderId], references: [id])
  product       Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model Payment {
  id                    String        @id @default(cuid())
  orderId               String
  userId                String
  amount                Decimal       @db.Decimal(12, 2)
  currency              String        @default("USD")
  paymentMethod         PaymentMethod

  // Bank Transfer Support (PRD requirement)
  bankTransferReceiptUrl String?
  bankTransferVerified  Boolean       @default(false)
  verifiedBy            String?
  verifiedAt            DateTime?

  // Additional fields for bank transfer
  receiptUrl            String?
  receiptUploadedAt     DateTime?
  bankReference         String?
  referenceCode         String?
  dueDate               DateTime?
  rejectionReason       String?

  // Delivery Schedule relation
  deliveryScheduleId    String?       @unique

  // Multi-Delivery Payment Support (RFC-015)
  deliveryPackageId     String?
  packageSequence       Int?
  isMultiDelivery       Boolean       @default(false)
  parentPaymentId       String?
  scheduledDate         DateTime?
  paymentStage          String        @default("SINGLE")
  remainingAmount       Decimal?      @db.Decimal(12, 2)

  escrowStatus          EscrowStatus  @default(PENDING)
  escrowAccountId       String?
  escrowReleasedAt      DateTime?
  escrowRefundedAt      DateTime?
  escrowReleasedBy      String?
  escrowHoldReason      String?
  paymentType           PaymentType   @default(FULL_PAYMENT)
  failureReason         String?
  bankTransferReceipt   Json?
  gatewayProvider       String?
  gatewayTransactionId  String?
  gatewayResponse       Json?
  status                PaymentStatus @default(PENDING)
  paidAt                DateTime?
  releasedAt            DateTime?
  refundedAt            DateTime?
  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt

  // Relations
  order                 Order         @relation(fields: [orderId], references: [id])
  user                  User          @relation(fields: [userId], references: [id])
  verifier              User?         @relation("PaymentVerifier", fields: [verifiedBy], references: [id])
  deliverySchedule      DeliverySchedule? @relation("DeliveryPayment", fields: [deliveryScheduleId], references: [id])
  commissions           Commission[]
  paymentSchedules      PaymentSchedule[]
  paymentTracking       PaymentTracking[]

  @@map("payments")
}

model Notification {
  id                  String           @id @default(cuid())
  userId              String
  title               String
  message             String
  notificationType    NotificationType
  relatedEntityType   String?
  relatedEntityId     String?

  // Delivery Channels (PRD requirement)
  emailSent           Boolean          @default(false)
  pushSent            Boolean          @default(false)
  whatsappSent        Boolean          @default(false)
  telegramSent        Boolean          @default(false)

  data                Json?
  isRead              Boolean          @default(false)
  status              String           @default("UNREAD") // For backward compatibility
  readAt              DateTime?
  createdAt           DateTime         @default(now())

  // Relations
  user                User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model AuditLog {
  id          String   @id @default(cuid())
  userId      String?
  action      String
  resource    String
  resourceId  String?
  oldValues   Json?
  newValues   Json?
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  // Relations
  user        User?    @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

// RFC-003: Additional Tables for PRD Requirements

model ProducerLocation {
  id            String       @id @default(cuid())
  producerId    String
  locationType  LocationType
  name          String
  address       String
  googleMapsLink String?
  coordinates   String?      // PostGIS point for lat/lng
  description   String?
  capacity      Int?
  isActive      Boolean      @default(true)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  // Relations
  producer      UserProfile  @relation(fields: [producerId], references: [userId], onDelete: Cascade)
  products      Product[]    @relation("QuarryLocation")
  factoryProducts Product[]  @relation("FactoryLocation")

  @@map("producer_locations")
}

model StoneAnalysisReport {
  id                    String          @id @default(cuid())
  producerId            String
  productCategoryId     String?
  reportName            String
  reportDate            DateTime
  laboratoryName        String?

  // Technical Properties (PRD requirements)
  density               Decimal?        @db.Decimal(5, 2)
  hardness              Decimal?        @db.Decimal(5, 2)
  waterAbsorption       Decimal?        @db.Decimal(5, 2)
  freezeThawResistance  Decimal?        @db.Decimal(5, 2)
  compressiveStrength   Decimal?        @db.Decimal(8, 2)
  flexuralStrength      Decimal?        @db.Decimal(8, 2)

  additionalProperties  Json?
  reportDocumentUrl     String?
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt

  // Relations
  producer              User            @relation(fields: [producerId], references: [id])
  productCategory       ProductCategory? @relation(fields: [productCategoryId], references: [id])
  products              Product[]

  @@map("stone_analysis_reports")
}

model ProductPriceList {
  id              String        @id @default(cuid())
  productId       String
  listType        PriceListType

  // Dimensional Products (Ebatlı Ürün)
  thicknessCm     Decimal?      @db.Decimal(5, 2)
  widthCm         Decimal?      @db.Decimal(8, 2)
  lengthCm        Decimal?      @db.Decimal(8, 2)
  surfaceFinish   SurfaceFinish?
  packaging       PackagingType @default(PALLET)
  deliveryType    DeliveryType  @default(FACTORY)

  // Pricing
  pricePerM2      Decimal?      @db.Decimal(10, 2)
  currency        String        @default("USD")

  // Surface Finish Pricing
  canProduce      Boolean       @default(true)

  // Slab Products
  slabSpecifications Json?

  isActive        Boolean       @default(true)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  product         Product       @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_price_lists")
}

model BlockProduct {
  id            String    @id @default(cuid())
  productId     String

  // Block Dimensions (cm)
  widthCm       Decimal   @db.Decimal(8, 2)
  lengthCm      Decimal   @db.Decimal(8, 2)
  heightCm      Decimal   @db.Decimal(8, 2)

  // Weight and Pricing
  weightTons    Decimal?  @db.Decimal(8, 3)
  pricePerTon   Decimal   @db.Decimal(10, 2)
  currency      String    @default("USD")

  // Availability
  stockQuantity Int       @default(0)
  isAvailable   Boolean   @default(true)

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  product       Product   @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("block_products")
}

model ProductMedia {
  id          String    @id @default(cuid())
  productId   String
  mediaType   MediaType
  fileUrl     String
  fileSize    Int?
  mimeType    String?
  width       Int?
  height      Int?
  duration    Int?      // For videos, in seconds
  title       String?
  altText     String?
  sortOrder   Int       @default(0)
  isPrimary   Boolean   @default(false)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  product     Product   @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_media")
}



model OrderTracking {
  id              String              @id @default(cuid())
  orderId         String
  status          OrderTrackingStatus
  description     String?
  location        String?
  estimatedDate   DateTime?
  actualDate      DateTime?
  notes           String?
  attachments     Json?
  carrierName     String?
  trackingNumber  String?
  createdAt       DateTime            @default(now())
  createdBy       String?

  // Relations
  order           Order               @relation(fields: [orderId], references: [id], onDelete: Cascade)
  creator         User?               @relation(fields: [createdBy], references: [id])

  @@map("order_tracking")
}

model QualityReview {
  id              String    @id @default(cuid())
  orderId         String
  customerId      String
  producerId      String
  productId       String?   // Added missing productId field
  overallRating   Int       // 1-5
  qualityRating   Int       // 1-5
  deliveryRating  Int       // 1-5
  serviceRating   Int       // 1-5
  title           String?
  comment         String?
  pros            String?
  cons            String?
  reviewImages    Json?
  isVerified      Boolean   @default(false)
  isPublished     Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  order           Order     @relation(fields: [orderId], references: [id])
  customer        User      @relation("CustomerReviews", fields: [customerId], references: [id])
  producer        User      @relation("ProducerReviews", fields: [producerId], references: [id])
  product         Product?  @relation("ProductReviews", fields: [productId], references: [id])

  @@map("quality_reviews")
}

model AINewsArticle {
  id              String        @id @default(cuid())
  title           String
  content         String
  summary         String?
  sourceUrl       String?
  sourceName      String?
  category        AINewsCategory
  language        String        @default("tr")
  sentiment       Sentiment?
  keywords        String[]
  entities        Json?
  relevanceScore  Decimal?      @db.Decimal(3, 2)
  publishedAt     DateTime?
  processedAt     DateTime      @default(now())
  createdAt       DateTime      @default(now())

  @@map("ai_news_articles")
}

model EmailCampaign {
  id                      String              @id @default(cuid())
  name                    String
  subject                 String
  content                 String
  templateId              String?
  targetCountries         String[]
  targetUserTypes         UserType[]
  targetSegments          Json?
  aiOptimized             Boolean             @default(false)
  optimalSendTime         DateTime?
  personalizationEnabled  Boolean             @default(false)
  status                  EmailCampaignStatus @default(DRAFT)
  scheduledAt             DateTime?
  sentAt                  DateTime?
  totalRecipients         Int                 @default(0)
  deliveredCount          Int                 @default(0)
  openedCount             Int                 @default(0)
  clickedCount            Int                 @default(0)
  createdAt               DateTime            @default(now())
  updatedAt               DateTime            @updatedAt

  @@map("email_campaigns")
}

// Commission tracking model for admin dashboard
model Commission {
  id              String   @id @default(cuid())
  paymentId       String
  orderId         String?
  amount          Float
  rate            Float?   // Percentage rate (optional for backward compatibility)

  // M² based commission fields
  totalM2         Float?   // Total m² processed
  commissionPerM2 Float?   // Commission per m² (e.g., $1.00)
  calculationMethod String @default("PERCENTAGE") // PERCENTAGE or PER_M2

  calculatedAt    DateTime @default(now())

  // Relations
  payment         Payment  @relation(fields: [paymentId], references: [id])
  order           Order?   @relation(fields: [orderId], references: [id])

  @@map("commissions")
}

// Dispute model for order disputes
model Dispute {
  id          String   @id @default(cuid())
  orderId     String   @unique
  customerId  String?
  producerId  String?
  initiatedBy String   // User ID who initiated the dispute
  reason      String
  description String
  evidence    Json?    // Evidence attachments, descriptions
  status      String   @default("OPEN") // OPEN, INVESTIGATING, RESOLVED, CLOSED
  resolution  String?  // Admin resolution
  resolvedBy  String?  // Admin who resolved
  resolvedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  order       Order    @relation(fields: [orderId], references: [id])
  customer    User?    @relation("CustomerDisputes", fields: [customerId], references: [id])
  producer    User?    @relation("ProducerDisputes", fields: [producerId], references: [id])

  @@map("disputes")
}

// RFC-601: AI Chatbot System Tables

model ChatbotConversation {
  id                    String             @id @default(cuid())
  sessionId             String             @unique
  userId                String?
  language              String             @default("en")
  userType              String?            // 'producer' | 'customer' | 'guest'

  // Context and State
  currentIntent         ChatbotIntent?
  escalationLevel       Int                @default(0)
  escalationReason      String?
  humanAgentId          String?

  // Analytics
  messageCount          Int                @default(0)
  averageConfidence     Decimal?           @db.Decimal(3, 2)
  sentimentScore        Decimal?           @db.Decimal(3, 2)
  resolvedIssues        String[]
  unresolvedIssues      String[]

  // Status and Timing
  status                ConversationStatus @default(ACTIVE)
  startedAt             DateTime           @default(now())
  lastActivityAt        DateTime           @default(now())
  endedAt               DateTime?
  escalatedAt           DateTime?

  // Relations
  user                  User?              @relation(fields: [userId], references: [id])
  agent                 ChatbotAgent?      @relation(fields: [humanAgentId], references: [id])
  messages              ChatbotMessage[]
  feedback              ChatbotFeedback[]

  @@map("chatbot_conversations")
}

model ChatbotMessage {
  id                    String                @id @default(cuid())
  conversationId        String
  role                  ChatbotMessageRole
  content               String
  language              String?

  // AI Analysis
  intent                ChatbotIntent?
  confidence            Decimal?              @db.Decimal(3, 2)
  entities              Json?                 // Extracted entities
  sentiment             Decimal?              @db.Decimal(3, 2)

  // Metadata
  processingTimeMs      Int?
  modelUsed             String?
  tokenCount            Int?

  createdAt             DateTime              @default(now())

  // Relations
  conversation          ChatbotConversation   @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user                  User?                 @relation(fields: [userId], references: [id])
  feedback              ChatbotFeedback[]

  userId                String?

  @@map("chatbot_messages")
}

model ChatbotFeedback {
  id                    String              @id @default(cuid())
  conversationId        String
  messageId             String
  userId                String?
  rating                Int                 // 1-5 scale
  feedback              String?
  category              String?             // 'helpful', 'accurate', 'relevant', etc.

  createdAt             DateTime            @default(now())

  // Relations
  conversation          ChatbotConversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  message               ChatbotMessage      @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user                  User?               @relation(fields: [userId], references: [id])

  @@map("chatbot_feedback")
}

model ChatbotAgent {
  id                    String              @id @default(cuid())
  userId                String              @unique
  name                  String
  email                 String
  languages             String[]            // Supported languages
  expertise             String[]            // Areas of expertise

  // Availability and Load
  status                AgentStatus         @default(OFFLINE)
  currentLoad           Int                 @default(0)
  maxConcurrentChats    Int                 @default(5)

  // Performance Metrics
  totalConversations    Int                 @default(0)
  averageResponseTime   Int?                // in seconds
  customerSatisfaction  Decimal?            @db.Decimal(3, 2)
  resolutionRate        Decimal?            @db.Decimal(3, 2)

  // Schedule
  workingHours          Json?               // Working hours configuration
  timezone              String?

  isActive              Boolean             @default(true)
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt

  // Relations
  user                  User                @relation(fields: [userId], references: [id])
  conversations         ChatbotConversation[]

  @@map("chatbot_agents")
}

model ChatbotKnowledgeBase {
  id                    String              @id @default(cuid())
  category              String              // 'faq', 'product', 'process', 'policy'
  subcategory           String?
  language              String              @default("en")

  // Content
  title                 String
  content               String
  keywords              String[]
  tags                  String[]

  // Metadata
  priority              Int                 @default(0)
  isActive              Boolean             @default(true)
  version               Int                 @default(1)

  // Usage Analytics
  accessCount           Int                 @default(0)
  lastAccessedAt        DateTime?

  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt
  createdBy             String?

  @@map("chatbot_knowledge_base")
}

model ChatbotAnalytics {
  id                    String              @id @default(cuid())
  date                  DateTime            @db.Date

  // Conversation Metrics
  totalConversations    Int                 @default(0)
  newConversations      Int                 @default(0)
  escalatedConversations Int               @default(0)
  completedConversations Int               @default(0)

  // Message Metrics
  totalMessages         Int                 @default(0)
  userMessages          Int                 @default(0)
  botMessages           Int                 @default(0)
  averageMessagesPerConversation Decimal?  @db.Decimal(5, 2)

  // Performance Metrics
  averageResponseTime   Decimal?            @db.Decimal(8, 2) // in milliseconds
  averageConfidence     Decimal?            @db.Decimal(3, 2)
  averageSentiment      Decimal?            @db.Decimal(3, 2)
  resolutionRate        Decimal?            @db.Decimal(3, 2)

  // User Satisfaction
  totalFeedback         Int                 @default(0)
  averageRating         Decimal?            @db.Decimal(3, 2)
  positiveRatings       Int                 @default(0)
  negativeRatings       Int                 @default(0)

  // Language Distribution
  languageStats         Json?               // Language usage statistics

  // Intent Distribution
  intentStats           Json?               // Intent frequency statistics

  // Escalation Metrics
  escalationRate        Decimal?            @db.Decimal(3, 2)
  escalationReasons     Json?               // Escalation reason statistics

  createdAt             DateTime            @default(now())

  @@unique([date])
  @@map("chatbot_analytics")
}

// RFC-701: 3D Product View Tables

model Asset3D {
  id                    String             @id @default(cuid())
  productId             String?
  name                  String
  description           String?
  type                  AssetType
  format                AssetFormat
  quality               AssetQuality

  // File Information
  originalFileName      String
  fileName              String
  filePath              String
  fileSize              BigInt
  mimeType              String

  // 3D Model Specific
  vertices              Int?
  faces                 Int?
  materials             Int?
  textures              Int?
  animations            String[]           // Animation names

  // Texture Specific
  width                 Int?
  height                Int?
  channels              Int?

  // Processing Information
  processingStatus      ProcessingStatus   @default(PENDING)
  processingLog         String?
  processedAt           DateTime?

  // Optimization Variants
  variants              Asset3DVariant[]

  // Metadata
  metadata              Json?              // Additional properties
  tags                  String[]

  // Usage Statistics
  downloadCount         Int                @default(0)
  viewCount             Int                @default(0)
  lastAccessedAt        DateTime?

  // Relations
  product               Product?           @relation(fields: [productId], references: [id])

  // Material texture relations
  albedoMaterials       MaterialDefinition[] @relation("AlbedoMap")
  normalMaterials       MaterialDefinition[] @relation("NormalMap")
  roughnessMaterials    MaterialDefinition[] @relation("RoughnessMap")
  metallicMaterials     MaterialDefinition[] @relation("MetallicMap")
  emissionMaterials     MaterialDefinition[] @relation("EmissionMap")
  heightMaterials       MaterialDefinition[] @relation("HeightMap")
  occlusionMaterials    MaterialDefinition[] @relation("OcclusionMap")

  // Environment map relations
  environmentConfigs    ViewerConfiguration[] @relation("EnvironmentMap")

  isActive              Boolean            @default(true)
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  createdBy             String?

  @@map("assets_3d")
}

model Asset3DVariant {
  id                    String             @id @default(cuid())
  assetId               String
  quality               AssetQuality

  // File Information
  fileName              String
  filePath              String
  fileSize              BigInt

  // 3D Model Specific (for optimized versions)
  vertices              Int?
  faces                 Int?
  lodLevel              Int?               // Level of Detail

  // Texture Specific (for different resolutions)
  width                 Int?
  height                Int?

  // Compression
  compressionRatio      Decimal?           @db.Decimal(5, 2)

  // Processing
  processingStatus      ProcessingStatus   @default(PENDING)
  processedAt           DateTime?

  // Relations
  asset                 Asset3D            @relation(fields: [assetId], references: [id], onDelete: Cascade)

  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt

  @@map("asset_3d_variants")
}

model MaterialDefinition {
  id                    String             @id @default(cuid())
  name                  String
  description           String?

  // PBR Material Properties
  baseColor             String?            // Hex color
  metallic              Decimal?           @db.Decimal(3, 2)
  roughness             Decimal?           @db.Decimal(3, 2)
  normal                Decimal?           @db.Decimal(3, 2)
  emission              String?            // Hex color
  emissionIntensity     Decimal?           @db.Decimal(5, 2)

  // Texture Maps
  albedoMapId           String?
  normalMapId           String?
  roughnessMapId        String?
  metallicMapId         String?
  emissionMapId         String?
  heightMapId           String?
  occlusionMapId        String?

  // Physical Properties
  density               Decimal?           @db.Decimal(8, 3)
  hardness              Decimal?           @db.Decimal(3, 1)
  porosity              Decimal?           @db.Decimal(5, 2)

  // Texture Tiling
  tilingU               Decimal?           @db.Decimal(5, 2) @default(1.0)
  tilingV               Decimal?           @db.Decimal(5, 2) @default(1.0)

  // Relations
  albedoMap             Asset3D?           @relation("AlbedoMap", fields: [albedoMapId], references: [id])
  normalMap             Asset3D?           @relation("NormalMap", fields: [normalMapId], references: [id])
  roughnessMap          Asset3D?           @relation("RoughnessMap", fields: [roughnessMapId], references: [id])
  metallicMap           Asset3D?           @relation("MetallicMap", fields: [metallicMapId], references: [id])
  emissionMap           Asset3D?           @relation("EmissionMap", fields: [emissionMapId], references: [id])
  heightMap             Asset3D?           @relation("HeightMap", fields: [heightMapId], references: [id])
  occlusionMap          Asset3D?           @relation("OcclusionMap", fields: [occlusionMapId], references: [id])

  products              Product[]          @relation("ProductMaterial")

  isActive              Boolean            @default(true)
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  createdBy             String?

  @@map("material_definitions")
}

model ViewerConfiguration {
  id                    String             @id @default(cuid())
  productId             String             @unique

  // Camera Settings
  cameraPosition        Json               // {x, y, z}
  cameraTarget          Json               // {x, y, z}
  cameraFov             Decimal            @db.Decimal(5, 2) @default(75)

  // Lighting
  ambientLightColor     String             @default("#ffffff")
  ambientLightIntensity Decimal            @db.Decimal(3, 2) @default(0.4)
  directionalLightColor String             @default("#ffffff")
  directionalLightIntensity Decimal        @db.Decimal(3, 2) @default(1.0)
  directionalLightPosition Json            // {x, y, z}

  // Environment
  environmentMapId      String?
  backgroundType        String             @default("color") // color, environment, transparent
  backgroundColor       String             @default("#f0f0f0")

  // Controls
  enableOrbitControls   Boolean            @default(true)
  enableZoom            Boolean            @default(true)
  enablePan             Boolean            @default(true)
  enableRotate          Boolean            @default(true)
  autoRotate            Boolean            @default(false)
  autoRotateSpeed       Decimal            @db.Decimal(3, 2) @default(2.0)

  // Performance
  enableShadows         Boolean            @default(true)
  shadowMapSize         Int                @default(1024)
  enableAntialiasing    Boolean            @default(true)
  pixelRatio            Decimal            @db.Decimal(3, 2) @default(1.0)

  // Annotations
  enableAnnotations     Boolean            @default(true)
  annotations           Json?              // Array of annotation objects

  // Relations
  product               Product            @relation(fields: [productId], references: [id], onDelete: Cascade)
  environmentMap        Asset3D?           @relation("EnvironmentMap", fields: [environmentMapId], references: [id])

  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt

  @@map("viewer_configurations")
}

model ViewerSession {
  id                    String             @id @default(cuid())
  sessionId             String             @unique
  productId             String
  userId                String?

  // Session Data
  viewDuration          Int                @default(0) // seconds
  interactionCount      Int                @default(0)
  zoomCount             Int                @default(0)
  rotationCount         Int                @default(0)
  annotationViews       Int                @default(0)

  // Device Information
  userAgent             String?
  deviceType            String?            // mobile, tablet, desktop
  screenResolution      String?

  // Performance Metrics
  loadTime              Int?               // milliseconds
  frameRate             Decimal?           @db.Decimal(5, 2)
  memoryUsage           Int?               // MB

  // Relations
  product               Product            @relation(fields: [productId], references: [id])
  user                  User?              @relation(fields: [userId], references: [id])

  startedAt             DateTime           @default(now())
  endedAt               DateTime?

  @@map("viewer_sessions")
}

// RFC-014: Sample Request System Tables

model SampleRequest {
  id                    String              @id @default(cuid())
  quoteRequestId        String
  quoteId               String
  customerId            String
  producerId            String

  // Sample Details
  requestedProducts     Json                // Array of products with specifications
  sampleSpecifications  Json?               // Special sample requirements
  deliveryAddress       Json                // Customer delivery address

  // Status Tracking
  status                SampleRequestStatus @default(PENDING)
  adminNotes            String?
  rejectionReason       String?

  // Producer Information
  producerResponse      Json?               // Producer's response
  preparationTimeDays   Int?
  shippingInfo          Json?               // Shipping/tracking information

  // Customer Evaluation
  customerEvaluation    Json?               // Customer feedback
  willOrder             Boolean?            // Will customer place order?

  // Timestamps
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt
  approvedAt            DateTime?
  shippedAt             DateTime?
  deliveredAt           DateTime?
  evaluatedAt           DateTime?

  // Relations
  customer              User                @relation("CustomerSampleRequests", fields: [customerId], references: [id])
  producer              User                @relation("ProducerSampleRequests", fields: [producerId], references: [id])
  tracking              SampleRequestTracking[]

  @@map("sample_requests")
}

model SampleRequestTracking {
  id                    String              @id @default(cuid())
  sampleRequestId       String
  status                SampleRequestStatus
  notes                 String?
  createdBy             String?             // admin_id, customer_id, producer_id
  createdByType         String?             // 'admin', 'customer', 'producer'
  createdAt             DateTime            @default(now())

  // Relations
  sampleRequest         SampleRequest       @relation(fields: [sampleRequestId], references: [id], onDelete: Cascade)

  @@map("sample_request_tracking")
}

// Missing models for quote system
model QuoteRequest {
  id                    String              @id @default(cuid())
  customerId            String
  productId             String
  requestNumber         String              @unique
  specifications        Json
  quantity              Int
  unit                  String
  deliveryAddress       Json
  deliveryDate          DateTime?
  urgency               String?             // Added missing urgency field
  notes                 String?
  status                String              @default("ACTIVE")
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt

  // Relations
  customer              User                @relation("CustomerQuoteRequests", fields: [customerId], references: [id])
  product               Product             @relation("ProductQuoteRequests", fields: [productId], references: [id])
  quotes                Quote[]

  @@map("quote_requests")
}

model Quote {
  id                    String              @id @default(cuid())
  quoteRequestId        String
  producerId            String
  customerId            String              // Added missing customerId field
  productId             String              // Added missing productId field
  quoteNumber           String              @unique
  unitPrice             Decimal             @db.Decimal(10, 2)
  totalPrice            Decimal             @db.Decimal(12, 2)
  currency              String              @default("USD")
  deliveryTimeDays      Int
  paymentTerms          String?
  validityDays          Int                 @default(30)
  validUntil            DateTime?           // Added missing validUntil field
  items                 Json?               // Added missing items field
  notes                 String?
  status                String              @default("PENDING")
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt

  // Relations
  quoteRequest          QuoteRequest        @relation(fields: [quoteRequestId], references: [id])
  producer              User                @relation("ProducerQuotes", fields: [producerId], references: [id])
  customer              User                @relation("CustomerQuotes", fields: [customerId], references: [id])
  product               Product             @relation("QuoteProducts", fields: [productId], references: [id])
  orders                Order[]             @relation("QuoteOrders")

  @@map("quotes")
}

// Performance log model
model PerformanceLog {
  id                    String              @id @default(cuid())
  endpoint              String
  method                String
  responseTime          Int                 // milliseconds
  statusCode            Int
  userId                String?
  userAgent             String?
  ipAddress             String?
  createdAt             DateTime            @default(now())

  @@map("performance_logs")
}

// RFC-016: Escrow Payment System Tables

model EscrowAccount {
  id                    String              @id @default(cuid())
  orderId               String              @unique
  customerId            String
  producerId            String

  // Financial Details
  totalAmount           Decimal             @db.Decimal(12, 2)
  platformCommission    Decimal             @db.Decimal(10, 2)
  producerAmount        Decimal             @db.Decimal(12, 2)
  currency              String              @default("TRY")

  // Bank Transfer Information
  referenceCode         String              @unique
  bankTransferReceipt   Json?               // Bank transfer receipt details

  // Status and Timestamps
  status                EscrowStatus        @default(PENDING)
  customerPaidAt        DateTime?
  producerNotifiedAt    DateTime?
  customerApprovedAt    DateTime?
  producerPaidAt        DateTime?

  // Dispute Information
  disputeReason         String?
  resolvedBy            String?
  resolvedAt            DateTime?

  // Production Proof
  productionProof       Json?               // Images, documents proving production

  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt

  // Relations
  order                 Order               @relation(fields: [orderId], references: [id])
  customer              User                @relation("CustomerEscrows", fields: [customerId], references: [id])
  producer              User                @relation("ProducerEscrows", fields: [producerId], references: [id])
  transactionLogs       EscrowTransactionLog[]

  @@map("escrow_accounts")
}

// Commission Tracking Table (PRD: Platform komisyon takibi)
model CommissionTracking {
  id                    String              @id @default(cuid())
  orderId               String
  paymentId             String?

  // Commission Details
  totalOrderAmount      Decimal             @db.Decimal(12, 2)
  m2Quantity            Decimal?            @db.Decimal(10, 2)
  tonQuantity           Decimal?            @db.Decimal(10, 2)

  // Commission Calculation (PRD: m² başına $1, ton başına $10)
  m2Commission          Decimal             @db.Decimal(10, 2) @default(0)
  tonCommission         Decimal             @db.Decimal(10, 2) @default(0)
  totalCommission       Decimal             @db.Decimal(10, 2)

  // Commission Rates
  m2Rate                Decimal             @db.Decimal(5, 2) @default(1.00)  // $1 per m²
  tonRate               Decimal             @db.Decimal(5, 2) @default(10.00) // $10 per ton

  // Status and Timing
  status                String              @default("PENDING") // PENDING, COLLECTED, FAILED
  collectedAt           DateTime?
  failureReason         String?

  // Relations
  customerId            String
  producerId            String

  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt

  @@map("commission_tracking")
}

// Payment Schedule for Multi-Delivery Orders (RFC-015)
model PaymentSchedule {
  id                    String              @id @default(cuid())
  orderId               String
  deliveryPackageId     String
  packageSequence       Int
  paymentType           PaymentType
  amount                Decimal             @db.Decimal(12, 2)
  currency              String              @default("USD")
  scheduledDate         DateTime
  dueDate               DateTime?
  status                String              @default("PENDING")
  paymentId             String?
  notes                 String?

  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt

  // Relations
  order                 Order               @relation(fields: [orderId], references: [id])
  payment               Payment?            @relation(fields: [paymentId], references: [id])

  @@map("payment_schedules")
}

// Payment Tracking for Status Changes
model PaymentTracking {
  id                    String              @id @default(cuid())
  paymentId             String
  orderId               String
  previousStatus        String?
  newStatus             String
  changedBy             String?
  changeReason          String?
  metadata              Json?

  createdAt             DateTime            @default(now())

  // Relations
  payment               Payment             @relation(fields: [paymentId], references: [id])
  order                 Order               @relation(fields: [orderId], references: [id])

  @@map("payment_tracking")
}

// Escrow Transaction Log
model EscrowTransactionLog {
  id                    String              @id @default(cuid())
  escrowAccountId       String
  transactionType       String              // HOLD, RELEASE, REFUND, COMMISSION_DEDUCT
  amount                Decimal             @db.Decimal(12, 2)
  currency              String              @default("USD")
  description           String?
  performedBy           String?
  metadata              Json?

  createdAt             DateTime            @default(now())

  // Relations
  escrowAccount         EscrowAccount       @relation(fields: [escrowAccountId], references: [id])

  @@map("escrow_transaction_log")
}

// Refresh Token Model for JWT Security
model RefreshToken {
  id          String   @id @default(cuid())
  userId      String
  tokenHash   String   @unique // SHA256 hash of the actual token
  sessionId   String   // Session identifier
  expiresAt   DateTime
  isRevoked   Boolean  @default(false)
  userAgent   String?  // Browser/app info
  ipAddress   String?  // IP address for security tracking
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([userId])
  @@index([tokenHash])
  @@index([expiresAt])
  @@index([sessionId])
  @@map("refresh_tokens")
}

// Admin model for admin panel access
model Admin {
  id                String              @id @default(cuid())
  email             String              @unique
  password          String
  name              String
  role              String              @default("admin") // admin, super_admin
  isActive          Boolean             @default(true)
  lastLoginAt       DateTime?
  lastLoginIp       String?
  failedLoginAttempts Int               @default(0)
  lockedUntil       DateTime?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  passwordResetTokens PasswordResetToken[]
  loginAttempts       LoginAttempt[]

  // Indexes
  @@index([email])
  @@index([isActive])
  @@map("admins")
}

// Password reset tokens for admin password recovery
model PasswordResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  adminId   String
  expiresAt DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())

  // Relations
  admin Admin @relation(fields: [adminId], references: [id], onDelete: Cascade)

  // Indexes
  @@index([token])
  @@index([adminId])
  @@index([expiresAt])
  @@map("password_reset_tokens")
}

// Login attempts tracking for security
model LoginAttempt {
  id        String   @id @default(cuid())
  adminId   String?
  email     String
  ip        String
  userAgent String?
  success   Boolean
  createdAt DateTime @default(now())

  // Relations
  admin Admin? @relation(fields: [adminId], references: [id], onDelete: SetNull)

  // Indexes
  @@index([email])
  @@index([ip])
  @@index([createdAt])
  @@map("login_attempts")
}
