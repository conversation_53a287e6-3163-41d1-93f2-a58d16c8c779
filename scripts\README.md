# Scripts Dizini
# Türkiye Doğal Taş Marketplace Platformu

Bu dizin, proje geliştirme sürecinde kullanılan otomatik script'leri i<PERSON>erir.

## 📁 Mevcut Script'ler

### 🔄 update-documentation.ps1
**Amaç**: <PERSON>je dokümantasyonunu otomatik olarak günceller  
**Platform**: Windows PowerShell  
**Durum**: ✅ Aktif  

#### Özellikler:
- PRD.md, README.md ve RFC dosyalarını otomatik günceller
- Versiyon numaralarını ve tarihleri günceller
- CHANGELOG.md dosyasına otomatik giriş ekler
- PowerShell uyumlu komut syntax'ı kullanır

#### Kullanım:
```powershell
# Basit kullanım
.\scripts\update-documentation.ps1

# Özel açıklama ile
.\scripts\update-documentation.ps1 -Description "UI basitleştirmesi tamamlandı"

# Bel<PERSON>li değişiklik türü ile
.\scripts\update-documentation.ps1 -ChangeType "Feature" -Description "Yeni 3D görüntüleyici eklendi"

# Özel versiyon ile
.\scripts\update-documentation.ps1 -Version "2.4.0" -Description "Major release"
```

#### Parametreler:
- `-ChangeType`: Değişiklik türü (default: "general")
- `-Description`: Değişiklik açıklaması (default: "Genel güncelleme")
- `-Version`: Versiyon numarası (default: "auto")

## 🛠️ PowerShell Komut Kullanımı

### ✅ Doğru Kullanım:
```powershell
# Sıralı komut çalıştırma
npm install; npm run build; npm start

# Koşullu çalıştırma
npm install; if ($?) { npm run build }; if ($?) { npm start }

# Script çalıştırma
.\scripts\update-documentation.ps1; git add .; git commit -m "docs: güncelleme"
```

### ❌ Yanlış Kullanım:
```bash
# Bu PowerShell'de çalışmaz (Bash syntax'ı)
npm install && npm run build && npm start
```

## 📋 Geliştirme Workflow'u

### 1. Değişiklik Sonrası Dokümantasyon Güncelleme:
```powershell
# Değişiklik yaptıktan sonra
.\scripts\update-documentation.ps1 -Description "Yaptığınız değişiklik açıklaması"

# Git commit
git add .
git commit -m "docs: değişiklik açıklaması"
git push origin main
```

### 2. Yeni Özellik Ekleme:
```powershell
# Özellik geliştirme
npm run dev

# Test
npm run test

# Dokümantasyon güncelleme
.\scripts\update-documentation.ps1 -ChangeType "Feature" -Description "Yeni özellik: XYZ"

# Commit ve push
git add .; git commit -m "feat: yeni özellik XYZ"; git push
```

### 3. Bug Fix:
```powershell
# Bug fix
npm run fix

# Test
npm run test

# Dokümantasyon güncelleme
.\scripts\update-documentation.ps1 -ChangeType "Bugfix" -Description "Düzeltme: ABC sorunu"

# Commit
git add .; git commit -m "fix: ABC sorunu düzeltildi"
```

## 🔧 Script Geliştirme Kuralları

### PowerShell Script Standartları:
1. **Encoding**: UTF-8 kullanın
2. **Parametre Validation**: Tüm parametreleri validate edin
3. **Error Handling**: Try-catch blokları kullanın
4. **Logging**: Write-Host ile renkli çıktı verin
5. **Help**: Comment-based help ekleyin

### Örnek Script Şablonu:
```powershell
<#
.SYNOPSIS
    Script açıklaması
.DESCRIPTION
    Detaylı açıklama
.PARAMETER ParameterName
    Parametre açıklaması
.EXAMPLE
    .\script.ps1 -Parameter "value"
#>

param(
    [Parameter(Mandatory=$false)]
    [string]$ParameterName = "default"
)

try {
    Write-Host "Script başlatılıyor..." -ForegroundColor Green
    
    # Script logic here
    
    Write-Host "Script tamamlandı!" -ForegroundColor Green
} catch {
    Write-Host "Hata: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
```

## 📊 Otomatik Güncelleme Sistemi

### Güncellenen Dosyalar:
- `PRD.md` - Product Requirements Document
- `README.md` - Proje ana dokümantasyonu
- `RFC/*.md` - Tüm RFC dosyaları
- `CHANGELOG.md` - Değişiklik geçmişi

### Güncellenen Alanlar:
- **Tarih Bilgileri**: Son güncelleme tarihleri
- **Versiyon Numaraları**: Otomatik versiyon artırımı
- **Changelog Girişleri**: Yeni değişiklik kayıtları
- **Cross-References**: RFC'ler arası bağlantılar

## 🚀 Gelecek Planları

### Planlanan Script'ler:
- `setup-development.ps1` - Geliştirme ortamı kurulumu
- `run-tests.ps1` - Otomatik test çalıştırma
- `deploy-staging.ps1` - Staging ortamına deploy
- `backup-database.ps1` - Veritabanı yedekleme
- `generate-docs.ps1` - API dokümantasyonu oluşturma

### İyileştirmeler:
- Git hooks entegrasyonu
- CI/CD pipeline entegrasyonu
- Slack/Teams bildirim desteği
- Otomatik PR oluşturma

## 📞 Destek

### Script Sorunları:
- **Execution Policy Hatası**: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
- **Path Bulunamadı**: Script'i proje kök dizininden çalıştırın
- **Permission Denied**: PowerShell'i yönetici olarak çalıştırın

### İletişim:
- **Teknik Destek**: <EMAIL>
- **Script Geliştirme**: <EMAIL>
- **Dokümantasyon**: <EMAIL>

---

**Son Güncelleme**: 2025-06-29  
**Versiyon**: 1.0  
**Platform**: Windows PowerShell 5.1+
