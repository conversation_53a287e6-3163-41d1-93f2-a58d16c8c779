// Order Status Types
export type OrderStatus = 
  | 'pending-payment'      // Ön ödeme bekliyor
  | 'payment-confirmed'    // Ödeme onaylandı
  | 'in-production'        // Üretimde
  | 'ready-for-shipment'   // Sevkiyat hazır
  | 'shipped'              // Ka<PERSON>ya verildi
  | 'delivered'            // Teslim edildi
  | 'completed'            // Tamamlandı
  | 'cancelled'            // İptal edildi
  | 'refunded'             // İade edildi

export type PaymentStatus = 'pending' | 'partial' | 'completed' | 'refunded'
export type PaymentMethod = 'bank-transfer' | 'credit-card' | 'cash' | 'check'

// Order Product Interface
export interface OrderProduct {
  id: string
  productId: string
  productName: string
  productImage: string
  category: string
  specifications: OrderProductSpec[]
}

export interface OrderProductSpec {
  id: string
  thickness: string
  width: string
  length: string
  surface: string
  packaging: string
  delivery: string
  quantity: number
  unit: string
  unitPrice: number
  totalPrice: number
  currency: string
}

// Pricing Interface
export interface OrderPricing {
  subtotal: number
  taxRate: number
  taxAmount: number
  shippingCost: number
  discountAmount: number
  totalAmount: number
  currency: string
}

// Payment Interface
export interface PaymentInfo {
  status: PaymentStatus
  method: PaymentMethod
  advancePayment: {
    amount: number
    paidAmount: number
    dueDate: Date
    paidDate?: Date
    status: 'pending' | 'completed'
  }
  remainingPayment: {
    amount: number
    paidAmount: number
    dueDate: Date
    paidDate?: Date
    status: 'pending' | 'completed'
  }
  totalPaid: number
  totalDue: number
}

// Delivery Interface
export interface DeliveryInfo {
  method: 'factory' | 'port' | 'door-to-door'
  address: {
    company: string
    street: string
    city: string
    state: string
    country: string
    postalCode: string
    phone: string
    contactPerson: string
  }
  estimatedDate: Date
  actualDate?: Date
  trackingNumber?: string
  carrier?: string
  notes?: string
}

// Order Event Interface
export interface OrderEvent {
  id: string
  type: 'status-change' | 'payment' | 'production' | 'shipment' | 'delivery' | 'note'
  title: string
  description: string
  timestamp: Date
  userId: string
  userName: string
  metadata?: Record<string, any>
}

// Order Document Interface
export interface OrderDocument {
  id: string
  type: 'invoice' | 'receipt' | 'shipping-label' | 'delivery-note' | 'contract' | 'other'
  name: string
  url: string
  uploadedAt: Date
  uploadedBy: string
  size: number
}

// Customer Interface
export interface OrderCustomer {
  id: string
  companyName: string
  contactPerson: string
  email: string
  phone: string
  address: {
    street: string
    city: string
    state: string
    country: string
    postalCode: string
  }
}

// Producer Interface
export interface OrderProducer {
  id: string
  companyName: string
  contactPerson: string
  email: string
  phone: string
  factoryAddress: {
    street: string
    city: string
    state: string
    country: string
    postalCode: string
  }
}

// Main Order Interface
export interface Order {
  id: string
  orderNumber: string
  customerId: string
  customer: OrderCustomer
  producerId: string
  producer: OrderProducer
  quoteRequestId: string
  acceptedQuoteId: string
  status: OrderStatus
  products: OrderProduct[]
  pricing: OrderPricing
  payment: PaymentInfo
  delivery: DeliveryInfo
  timeline: OrderEvent[]
  documents: OrderDocument[]
  notes?: string
  createdAt: Date
  updatedAt: Date
  estimatedCompletionDate?: Date
  actualCompletionDate?: Date
}

// Order Summary for List View
export interface OrderSummary {
  id: string
  orderNumber: string
  customerName: string
  producerName: string
  productCount: number
  totalAmount: number
  currency: string
  status: OrderStatus
  createdAt: Date
  updatedAt: Date
  dueDate?: Date
}

// Order Statistics
export interface OrderStats {
  totalOrders: number
  pendingPayment: number
  inProduction: number
  completed: number
  totalRevenue: number
  monthlyRevenue: number
  averageOrderValue: number
  topCustomers: Array<{
    id: string
    name: string
    orderCount: number
    totalSpent: number
  }>
  topProducers: Array<{
    id: string
    name: string
    orderCount: number
    totalEarned: number
  }>
}
