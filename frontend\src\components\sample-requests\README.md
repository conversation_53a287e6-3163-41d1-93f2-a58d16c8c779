# Sample Request Modüler Bileşenler

Bu dizin, numune talep sayfası için modüler bileşenleri içerir. Tasar<PERSON>m değiştirilmeden mevcut işlevsellik korunarak modüler yapıya dönüştürülmüştür.

## Bileşenler

### 1. SampleRequestTabs
- **Dosya**: `SampleRequestTabs.tsx`
- **Amaç**: Numune talep durumları için tab sistemi
- **Props**: `SampleRequestTabsProps`
- **Özellikler**:
  - Aktif tab vurgulama
  - Tab başına sayı gösterimi
  - Responsive tasarım

### 2. SampleRequestCard
- **Dosya**: `SampleRequestCard.tsx`
- **Amaç**: Her numune talebi için kart görünümü
- **Props**: `SampleRequestCardProps`
- **Özellikler**:
  - Durum ikonları ve etiketleri
  - Ürün bilgileri gösterimi
  - Teslimat adresi
  - Durum mesajları
  - <PERSON>ks<PERSON><PERSON> buton<PERSON>ı
  - Framer Motion animasyonları

### 3. SampleRequestEmptyState
- **Dosya**: `SampleRequestEmptyState.tsx`
- **Amaç**: Numune talebi olmadığında gösterilen boş durum
- **Props**: `SampleRequestEmptyStateProps`
- **Özellikler**:
  - Dinamik mesaj gösterimi
  - Aktif taleplere yönlendirme butonu

### 4. SampleRequestFilter
- **Dosya**: `SampleRequestFilter.tsx`
- **Amaç**: Numune taleplerini filtreleme ve arama
- **Props**: `SampleRequestFilterProps`
- **Özellikler**:
  - Metin arama
  - Tarih aralığı filtresi
  - Durum filtresi
  - Filtreleri temizleme
  - Genişletilebilir filtre paneli

## Yardımcı Modüller

### 1. sample-status-utils.tsx
- **Amaç**: Numune durumları için ikon, etiket ve renk yönetimi
- **Fonksiyonlar**:
  - `getSampleStatusIcon()`: Durum ikonu
  - `getSampleStatusLabel()`: Durum etiketi
  - `getSampleStatusColor()`: Durum rengi
  - `getSampleStatusConfig()`: Tam durum konfigürasyonu
  - `getAllSampleStatuses()`: Tüm durumlar
  - `getStatusPriority()`: Durum önceliği

### 2. sample-request.ts (Types)
- **Amaç**: TypeScript tip tanımları
- **Interface'ler**:
  - `SampleRequestTabConfig`
  - `SampleRequestCardProps`
  - `SampleRequestTabsProps`
  - `SampleRequestEmptyStateProps`
  - `SampleRequestFilterProps`

## Kullanım

```tsx
import {
  SampleRequestTabs,
  SampleRequestCard,
  SampleRequestEmptyState,
  SampleRequestFilter,
  SampleRequestTabConfig
} from '@/components/sample-requests';

// Ana sayfa bileşeninde kullanım
const SampleRequestsPage = () => {
  // ... state ve logic

  return (
    <div>
      <SampleRequestFilter {...filterProps} />
      <SampleRequestTabs {...tabProps} />
      {requests.length > 0 ? (
        requests.map(request => (
          <SampleRequestCard key={request.id} request={request} {...cardProps} />
        ))
      ) : (
        <SampleRequestEmptyState {...emptyStateProps} />
      )}
    </div>
  );
};
```

## Özellikler

### ✅ Tamamlanan
- Modüler bileşen yapısı
- TypeScript tip güvenliği
- Mevcut tasarımın korunması
- Tüm işlevselliğin korunması
- Gelişmiş filtreleme sistemi
- Responsive tasarım
- Animasyonlar (Framer Motion)
- Index dosyası ile kolay import

### 🔄 Gelecek Geliştirmeler
- Unit testler
- Storybook entegrasyonu
- Daha gelişmiş filtreleme seçenekleri
- Bulk işlemler
- Export/Import işlevselliği

## Dosya Yapısı

```
frontend/src/components/sample-requests/
├── index.ts                    # Ana export dosyası
├── README.md                   # Bu dosya
├── SampleRequestTabs.tsx       # Tab sistemi
├── SampleRequestCard.tsx       # Numune kartı
├── SampleRequestEmptyState.tsx # Boş durum
└── SampleRequestFilter.tsx     # Filtreleme sistemi

frontend/src/types/
└── sample-request.ts           # TypeScript tipleri

frontend/src/utils/
└── sample-status-utils.tsx     # Durum yardımcıları
```

## Test

Modüler yapının çalıştığını test etmek için:

1. `http://localhost:3000/customer/requests/samples` sayfasını ziyaret edin
2. Tüm tab'ların çalıştığını kontrol edin
3. Filtreleme işlevselliğini test edin
4. Numune kartlarındaki butonları test edin
5. Boş durum görünümünü test edin

## Notlar

- Tüm bileşenler mevcut tasarımı korur
- Performans optimizasyonu için React.useMemo kullanılır
- Framer Motion animasyonları korunur
- TypeScript tip güvenliği sağlanır
- Modüler yapı gelecek geliştirmeleri kolaylaştırır
