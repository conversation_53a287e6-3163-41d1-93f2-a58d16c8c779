'use client'

import * as React from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { X, Plus, Trash2, Upload, Package } from 'lucide-react'

interface StockItem {
  id: string
  images: Array<{ file?: File; url?: string }>
  metraj: number
  thickness: number
  width: number
  length: number
  price: number
  currency: 'USD' | 'EUR' | 'TL'
}

interface AddStockModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (stockItems: StockItem[]) => void
  productName: string
  isLoading?: boolean
}

const generateId = () => Math.random().toString(36).substr(2, 9)

export function AddStockModal({
  isOpen,
  onClose,
  onSave,
  productName,
  isLoading = false
}: AddStockModalProps) {
  const [stockItems, setStockItems] = React.useState<StockItem[]>([
    {
      id: generateId(),
      images: [],
      metraj: 0,
      thickness: 2,
      width: 60,
      length: 60,
      price: 0,
      currency: 'USD'
    }
  ])
  const [error, setError] = React.useState('')

  const addStockItem = () => {
    setStockItems(prev => [...prev, {
      id: generateId(),
      images: [],
      metraj: 0,
      thickness: 2,
      width: 60,
      length: 60,
      price: 0,
      currency: 'USD'
    }])
  }

  const removeStockItem = (id: string) => {
    if (stockItems.length <= 1) {
      setError('En az bir stok ürün olmalıdır')
      return
    }
    setStockItems(prev => prev.filter(item => item.id !== id))
    setError('')
  }

  const updateStockItem = (id: string, field: keyof StockItem, value: any) => {
    setStockItems(prev => prev.map(item => {
      const updatedItem = { ...item, [field]: value }
      // Eğer images alanı yoksa boş array ekle
      if (!updatedItem.images) {
        updatedItem.images = []
      }
      return item.id === id ? updatedItem : item
    }))
    setError('')
  }

  // Mevcut stok verilerini yeni formata migrate et
  React.useEffect(() => {
    setStockItems(prev => prev.map(item => {
      if (!item.images) {
        return { ...item, images: [] }
      }
      return item
    }))
  }, [])

  const handleImageUpload = (id: string, files: FileList) => {
    const stockItem = stockItems.find(item => item.id === id)
    if (!stockItem) return

    // images array'i yoksa boş array oluştur
    if (!stockItem.images) {
      stockItem.images = []
    }

    const currentImages = stockItem.images.length
    const maxImages = 5
    const availableSlots = maxImages - currentImages

    if (availableSlots <= 0) {
      setError('En fazla 5 resim yükleyebilirsiniz')
      return
    }

    const filesToAdd = Array.from(files).slice(0, availableSlots)
    const newImages = filesToAdd.map(file => ({
      file,
      url: URL.createObjectURL(file)
    }))

    updateStockItem(id, 'images', [...stockItem.images, ...newImages])
    setError('')
  }

  const removeImage = (stockId: string, imageIndex: number) => {
    const stockItem = stockItems.find(item => item.id === stockId)
    if (!stockItem || !stockItem.images) return

    const updatedImages = stockItem.images.filter((_, index) => index !== imageIndex)
    updateStockItem(stockId, 'images', updatedImages)
  }

  const handleSubmit = () => {
    // Validation
    for (let i = 0; i < stockItems.length; i++) {
      const item = stockItems[i]
      if (!item.images || item.images.length === 0) {
        setError(`${i + 1}. stok ürün için en az 1 resim zorunludur`)
        return
      }
      if (item.metraj <= 0) {
        setError(`${i + 1}. stok ürün için metraj 0'dan büyük olmalıdır`)
        return
      }
      if (item.price <= 0) {
        setError(`${i + 1}. stok ürün için fiyat 0'dan büyük olmalıdır`)
        return
      }
    }

    onSave(stockItems)
  }

  const handleClose = () => {
    setStockItems([{
      id: generateId(),
      images: [],
      metraj: 0,
      thickness: 2,
      width: 60,
      length: 60,
      price: 0,
      currency: 'USD'
    }])
    setError('')
    onClose()
  }

  React.useEffect(() => {
    if (isOpen) {
      setStockItems([{
        id: generateId(),
        metraj: 0,
        thickness: 2,
        width: 60,
        length: 60,
        price: 0,
        currency: 'USD'
      }])
      setError('')
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <Package className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Stok Ekle</h2>
              <p className="text-sm text-gray-600">{productName}</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isLoading}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <p className="text-sm text-amber-800">
              <strong>Not:</strong> Eklediğiniz stok ürünler admin onayına gönderilecek. 
              Onaylandıktan sonra müşteriler tarafından görülebilir olacaktır.
            </p>
          </div>

          {/* Stock Items Table */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Resim</th>
                  <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Metraj (m²)</th>
                  <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Kalınlık (cm)</th>
                  <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">En (cm)</th>
                  <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Boy (cm)</th>
                  <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Fiyat</th>
                  <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Para Birimi</th>
                  <th className="border border-gray-300 px-3 py-2 text-center text-sm font-medium">İşlem</th>
                </tr>
              </thead>
              <tbody>
                {stockItems.map((item, index) => (
                  <tr key={item.id}>
                    <td className="border border-gray-300 px-3 py-2">
                      <div className="flex flex-col items-center gap-2">
                        {/* Yüklenen Resimler */}
                        {(item.images && item.images.length > 0) ? (
                          <div className="grid grid-cols-2 gap-1 max-w-32">
                            {item.images.map((image, imgIndex) => (
                              <div key={imgIndex} className="relative">
                                <img
                                  src={image.url}
                                  alt={`Stok ${index + 1} - Resim ${imgIndex + 1}`}
                                  className="w-14 h-14 object-cover rounded border"
                                />
                                <button
                                  type="button"
                                  onClick={() => removeImage(item.id, imgIndex)}
                                  className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full text-xs flex items-center justify-center hover:bg-red-600"
                                  disabled={isLoading}
                                >
                                  ×
                                </button>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="w-16 h-16 bg-gray-100 rounded border flex items-center justify-center">
                            <Upload className="w-6 h-6 text-gray-400" />
                          </div>
                        )}

                        {/* Resim Yükleme */}
                        {(!item.images || item.images.length < 5) && (
                          <input
                            type="file"
                            accept="image/*"
                            multiple
                            onChange={(e) => {
                              if (e.target.files) handleImageUpload(item.id, e.target.files)
                            }}
                            className="text-xs"
                            disabled={isLoading}
                          />
                        )}

                        <div className="text-xs text-gray-500">
                          {(item.images?.length || 0)}/5 resim
                        </div>
                      </div>
                    </td>
                    <td className="border border-gray-300 px-3 py-2">
                      <Input
                        type="number"
                        value={item.metraj}
                        onChange={(e) => updateStockItem(item.id, 'metraj', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.01"
                        className="w-20"
                        disabled={isLoading}
                      />
                    </td>
                    <td className="border border-gray-300 px-3 py-2">
                      <Input
                        type="number"
                        value={item.thickness}
                        onChange={(e) => updateStockItem(item.id, 'thickness', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className="w-20"
                        disabled={isLoading}
                      />
                    </td>
                    <td className="border border-gray-300 px-3 py-2">
                      <Input
                        type="number"
                        value={item.width}
                        onChange={(e) => updateStockItem(item.id, 'width', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className="w-20"
                        disabled={isLoading}
                      />
                    </td>
                    <td className="border border-gray-300 px-3 py-2">
                      <Input
                        type="number"
                        value={item.length}
                        onChange={(e) => updateStockItem(item.id, 'length', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className="w-20"
                        disabled={isLoading}
                      />
                    </td>
                    <td className="border border-gray-300 px-3 py-2">
                      <Input
                        type="number"
                        value={item.price}
                        onChange={(e) => updateStockItem(item.id, 'price', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.01"
                        className="w-24"
                        disabled={isLoading}
                      />
                    </td>
                    <td className="border border-gray-300 px-3 py-2">
                      <Select
                        value={item.currency}
                        onValueChange={(value) => updateStockItem(item.id, 'currency', value)}
                        disabled={isLoading}
                      >
                        <SelectTrigger className="w-20">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="TL">TL</SelectItem>
                        </SelectContent>
                      </Select>
                    </td>
                    <td className="border border-gray-300 px-3 py-2 text-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeStockItem(item.id)}
                        className="text-red-600 hover:text-red-700"
                        disabled={stockItems.length <= 1 || isLoading}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Add Stock Item Button */}
          <div className="mt-4 flex justify-center">
            <Button
              variant="outline"
              onClick={addStockItem}
              disabled={isLoading}
              className="bg-blue-50 hover:bg-blue-100"
            >
              <Plus className="w-4 h-4 mr-2" />
              Stok Ürün Ekle ({stockItems.length})
            </Button>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            İptal
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isLoading ? 'Kaydediliyor...' : 'Stok Ekle'}
          </Button>
        </div>
      </div>
    </div>
  )
}
