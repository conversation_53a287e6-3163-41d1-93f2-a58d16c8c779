'use client';

import React, { useRef, useEffect, useState, useMemo } from 'react';
import { useFrame, useLoader } from '@react-three/fiber';
import * as THREE from 'three';
import { 
  SurfaceFinishType, 
  SurfaceFinishName 
} from '../../types/3d';

interface PBRMaterialManagerProps {
  productId: string;
  surfaceFinish: SurfaceFinishName;
  surfaceFinishConfig: SurfaceFinishType;
  materialProperties?: {
    roughness: number;
    metallic: number;
    normalIntensity: number;
    displacementScale: number;
    emissive: number;
  };
  onMaterialReady?: (material: THREE.MeshStandardMaterial) => void;
  children: React.ReactNode;
}

interface TextureSet {
  albedo: THREE.Texture | null;
  normal: THREE.Texture | null;
  roughness: THREE.Texture | null;
  metallic: THREE.Texture | null;
  ao: THREE.Texture | null;
  displacement: THREE.Texture | null;
}

export const PBRMaterialManager: React.FC<PBRMaterialManagerProps> = ({
  productId,
  surfaceFinish,
  surfaceFinishConfig,
  materialProperties,
  onMaterialReady,
  children
}) => {
  const materialRef = useRef<THREE.MeshStandardMaterial>(null);
  const [textureSet, setTextureSet] = useState<TextureSet>({
    albedo: null,
    normal: null,
    roughness: null,
    metallic: null,
    ao: null,
    displacement: null
  });
  const [isLoading, setIsLoading] = useState(true);

  // Generate texture URLs based on product and surface finish
  const getTextureUrls = (productId: string, finish: SurfaceFinishName) => {
    const baseUrl = `/assets/textures/products/${productId}`;
    return {
      albedo: `${baseUrl}/${finish}/albedo.jpg`,
      normal: `${baseUrl}/${finish}/normal.jpg`,
      roughness: `${baseUrl}/${finish}/roughness.jpg`,
      metallic: `${baseUrl}/${finish}/metallic.jpg`,
      ao: `${baseUrl}/${finish}/ao.jpg`,
      displacement: `${baseUrl}/${finish}/displacement.jpg`
    };
  };

  // Load textures
  const loadTextures = async (urls: ReturnType<typeof getTextureUrls>) => {
    const loader = new THREE.TextureLoader();
    const texturePromises: Promise<THREE.Texture>[] = [];
    
    try {
      setIsLoading(true);
      
      // Load all textures
      const [albedo, normal, roughness, metallic, ao, displacement] = await Promise.allSettled([
        loader.loadAsync(urls.albedo).catch(() => null),
        loader.loadAsync(urls.normal).catch(() => null),
        loader.loadAsync(urls.roughness).catch(() => null),
        loader.loadAsync(urls.metallic).catch(() => null),
        loader.loadAsync(urls.ao).catch(() => null),
        loader.loadAsync(urls.displacement).catch(() => null)
      ]);

      // Configure textures
      const configureTexture = (texture: THREE.Texture | null) => {
        if (!texture) return null;
        
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(1, 1);
        texture.flipY = false;
        
        // Enable anisotropic filtering for better quality
        texture.anisotropy = 16;
        
        return texture;
      };

      const newTextureSet: TextureSet = {
        albedo: configureTexture(albedo.status === 'fulfilled' ? albedo.value : null),
        normal: configureTexture(normal.status === 'fulfilled' ? normal.value : null),
        roughness: configureTexture(roughness.status === 'fulfilled' ? roughness.value : null),
        metallic: configureTexture(metallic.status === 'fulfilled' ? metallic.value : null),
        ao: configureTexture(ao.status === 'fulfilled' ? ao.value : null),
        displacement: configureTexture(displacement.status === 'fulfilled' ? displacement.value : null)
      };

      setTextureSet(newTextureSet);
    } catch (error) {
      console.error('Failed to load textures:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Create PBR material
  const createPBRMaterial = useMemo(() => {
    const material = new THREE.MeshStandardMaterial({
      // Base properties
      color: new THREE.Color(0xffffff),
      roughness: materialProperties?.roughness ?? surfaceFinishConfig.roughness,
      metalness: materialProperties?.metallic ?? surfaceFinishConfig.metallic,
      
      // Textures
      map: textureSet.albedo,
      normalMap: textureSet.normal,
      roughnessMap: textureSet.roughness,
      metalnessMap: textureSet.metallic,
      aoMap: textureSet.ao,
      displacementMap: textureSet.displacement,
      
      // Normal map intensity
      normalScale: new THREE.Vector2(
        materialProperties?.normalIntensity ?? surfaceFinishConfig.normalIntensity,
        materialProperties?.normalIntensity ?? surfaceFinishConfig.normalIntensity
      ),
      
      // Displacement scale
      displacementScale: materialProperties?.displacementScale ?? 0.1,
      
      // Emissive
      emissive: new THREE.Color(0x000000),
      emissiveIntensity: materialProperties?.emissive ?? 0,
      
      // Other properties
      transparent: false,
      alphaTest: 0.5,
      side: THREE.FrontSide,
      
      // Enable shadows
      shadowSide: THREE.FrontSide
    });

    // Configure AO map if available
    if (textureSet.ao) {
      material.aoMapIntensity = 1.0;
    }

    return material;
  }, [textureSet, surfaceFinishConfig, materialProperties]);

  // Update material properties when they change
  useEffect(() => {
    if (materialRef.current && materialProperties) {
      const material = materialRef.current;
      
      // Animate property changes
      const startRoughness = material.roughness;
      const startMetalness = material.metalness;
      const startNormalScale = material.normalScale.x;
      
      const targetRoughness = materialProperties.roughness;
      const targetMetalness = materialProperties.metallic;
      const targetNormalScale = materialProperties.normalIntensity;
      
      let progress = 0;
      const duration = 500; // ms
      const startTime = Date.now();
      
      const animate = () => {
        const elapsed = Date.now() - startTime;
        progress = Math.min(elapsed / duration, 1);
        
        // Ease-in-out cubic
        const easedProgress = progress < 0.5 
          ? 4 * progress * progress * progress 
          : (progress - 1) * (2 * progress - 2) * (2 * progress - 2) + 1;
        
        material.roughness = startRoughness + (targetRoughness - startRoughness) * easedProgress;
        material.metalness = startMetalness + (targetMetalness - startMetalness) * easedProgress;
        material.normalScale.setScalar(
          startNormalScale + (targetNormalScale - startNormalScale) * easedProgress
        );
        
        material.needsUpdate = true;
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };
      
      animate();
    }
  }, [materialProperties]);

  // Load textures when surface finish changes
  useEffect(() => {
    const urls = getTextureUrls(productId, surfaceFinish);
    loadTextures(urls);
  }, [productId, surfaceFinish]);

  // Notify parent when material is ready
  useEffect(() => {
    if (materialRef.current && !isLoading && onMaterialReady) {
      onMaterialReady(materialRef.current);
    }
  }, [createPBRMaterial, isLoading, onMaterialReady]);

  return (
    <group>
      {/* Material provider */}
      <meshStandardMaterial
        ref={materialRef}
        attach="material"
        {...createPBRMaterial}
      />
      
      {children}
    </group>
  );
};

// PBR Shader Material for advanced effects
export const AdvancedPBRMaterial: React.FC<{
  surfaceFinish: SurfaceFinishType;
  materialProperties?: any;
}> = ({ surfaceFinish, materialProperties }) => {
  const materialRef = useRef<THREE.ShaderMaterial>(null);

  // Custom PBR vertex shader
  const vertexShader = `
    varying vec3 vWorldPosition;
    varying vec3 vWorldNormal;
    varying vec2 vUv;
    varying vec3 vTangent;
    varying vec3 vBitangent;
    
    attribute vec3 tangent;
    
    void main() {
      vUv = uv;
      
      vec4 worldPosition = modelMatrix * vec4(position, 1.0);
      vWorldPosition = worldPosition.xyz;
      
      vWorldNormal = normalize(normalMatrix * normal);
      vTangent = normalize(normalMatrix * tangent);
      vBitangent = cross(vWorldNormal, vTangent);
      
      gl_Position = projectionMatrix * viewMatrix * worldPosition;
    }
  `;

  // Custom PBR fragment shader
  const fragmentShader = `
    uniform sampler2D albedoMap;
    uniform sampler2D normalMap;
    uniform sampler2D roughnessMap;
    uniform sampler2D metallicMap;
    uniform sampler2D aoMap;
    
    uniform vec3 cameraPosition;
    uniform vec3 lightPosition;
    uniform vec3 lightColor;
    uniform float lightIntensity;
    
    uniform float roughness;
    uniform float metallic;
    uniform float normalIntensity;
    
    varying vec3 vWorldPosition;
    varying vec3 vWorldNormal;
    varying vec2 vUv;
    varying vec3 vTangent;
    varying vec3 vBitangent;
    
    // PBR functions
    vec3 getNormalFromMap() {
      vec3 tangentNormal = texture2D(normalMap, vUv).xyz * 2.0 - 1.0;
      tangentNormal.xy *= normalIntensity;
      
      mat3 tbn = mat3(vTangent, vBitangent, vWorldNormal);
      return normalize(tbn * tangentNormal);
    }
    
    float distributionGGX(vec3 N, vec3 H, float roughness) {
      float a = roughness * roughness;
      float a2 = a * a;
      float NdotH = max(dot(N, H), 0.0);
      float NdotH2 = NdotH * NdotH;
      
      float num = a2;
      float denom = (NdotH2 * (a2 - 1.0) + 1.0);
      denom = 3.14159265 * denom * denom;
      
      return num / denom;
    }
    
    float geometrySchlickGGX(float NdotV, float roughness) {
      float r = (roughness + 1.0);
      float k = (r * r) / 8.0;
      
      float num = NdotV;
      float denom = NdotV * (1.0 - k) + k;
      
      return num / denom;
    }
    
    float geometrySmith(vec3 N, vec3 V, vec3 L, float roughness) {
      float NdotV = max(dot(N, V), 0.0);
      float NdotL = max(dot(N, L), 0.0);
      float ggx2 = geometrySchlickGGX(NdotV, roughness);
      float ggx1 = geometrySchlickGGX(NdotL, roughness);
      
      return ggx1 * ggx2;
    }
    
    vec3 fresnelSchlick(float cosTheta, vec3 F0) {
      return F0 + (1.0 - F0) * pow(clamp(1.0 - cosTheta, 0.0, 1.0), 5.0);
    }
    
    void main() {
      vec3 albedo = texture2D(albedoMap, vUv).rgb;
      float roughnessValue = texture2D(roughnessMap, vUv).r * roughness;
      float metallicValue = texture2D(metallicMap, vUv).r * metallic;
      float ao = texture2D(aoMap, vUv).r;
      
      vec3 N = getNormalFromMap();
      vec3 V = normalize(cameraPosition - vWorldPosition);
      vec3 L = normalize(lightPosition - vWorldPosition);
      vec3 H = normalize(V + L);
      
      vec3 F0 = vec3(0.04);
      F0 = mix(F0, albedo, metallicValue);
      
      // Cook-Torrance BRDF
      float NDF = distributionGGX(N, H, roughnessValue);
      float G = geometrySmith(N, V, L, roughnessValue);
      vec3 F = fresnelSchlick(max(dot(H, V), 0.0), F0);
      
      vec3 kS = F;
      vec3 kD = vec3(1.0) - kS;
      kD *= 1.0 - metallicValue;
      
      vec3 numerator = NDF * G * F;
      float denominator = 4.0 * max(dot(N, V), 0.0) * max(dot(N, L), 0.0) + 0.0001;
      vec3 specular = numerator / denominator;
      
      float NdotL = max(dot(N, L), 0.0);
      vec3 Lo = (kD * albedo / 3.14159265 + specular) * lightColor * lightIntensity * NdotL;
      
      vec3 ambient = vec3(0.03) * albedo * ao;
      vec3 color = ambient + Lo;
      
      // HDR tonemapping
      color = color / (color + vec3(1.0));
      // Gamma correction
      color = pow(color, vec3(1.0/2.2));
      
      gl_FragColor = vec4(color, 1.0);
    }
  `;

  const uniforms = useMemo(() => ({
    albedoMap: { value: null },
    normalMap: { value: null },
    roughnessMap: { value: null },
    metallicMap: { value: null },
    aoMap: { value: null },
    
    cameraPosition: { value: new THREE.Vector3() },
    lightPosition: { value: new THREE.Vector3(10, 10, 5) },
    lightColor: { value: new THREE.Color(0xffffff) },
    lightIntensity: { value: 1.0 },
    
    roughness: { value: surfaceFinish.roughness },
    metallic: { value: surfaceFinish.metallic },
    normalIntensity: { value: surfaceFinish.normalIntensity }
  }), [surfaceFinish]);

  useFrame(({ camera }) => {
    if (materialRef.current) {
      materialRef.current.uniforms.cameraPosition.value.copy(camera.position);
    }
  });

  return (
    <shaderMaterial
      ref={materialRef}
      vertexShader={vertexShader}
      fragmentShader={fragmentShader}
      uniforms={uniforms}
      transparent={false}
    />
  );
};
