'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from './card'
import { Button } from './button'
import { Badge } from './badge'
import { 
  DollarSign,
  Calculator,
  Truck,
  MapPin,
  Calendar,
  Percent,
  Tag,
  Info
} from 'lucide-react'

interface PricingStepProps {
  formData: any
  setFormData: (data: any) => void
}

export function PricingStep({ formData, setFormData }: PricingStepProps) {
  const [pricingData, setPricingData] = React.useState({
    subtotal: 0,
    taxRate: 0.20, // %20 KDV
    taxAmount: 0,
    shippingCost: 0,
    discountAmount: 0,
    discountReason: '',
    totalAmount: 0,
    currency: 'USD',
    deliveryMethod: 'factory',
    deliveryAddress: {
      street: '',
      city: '',
      state: '',
      country: 'Türkiye',
      postalCode: '',
      contactPerson: '',
      phone: ''
    },
    estimatedDeliveryDate: '',
    paymentTerms: 'advance-50',
    specialInstructions: '',
    ...formData.pricing
  })

  // Calculate subtotal from products
  React.useEffect(() => {
    if (formData.products && formData.products.length > 0) {
      const subtotal = formData.products.reduce((total: number, product: any) => 
        total + product.specifications.reduce((productTotal: number, spec: any) => 
          productTotal + spec.totalPrice, 0
        ), 0
      )
      
      const taxAmount = subtotal * pricingData.taxRate
      const totalAmount = subtotal + taxAmount + pricingData.shippingCost - pricingData.discountAmount
      
      const updatedPricing = {
        ...pricingData,
        subtotal,
        taxAmount,
        totalAmount
      }
      
      setPricingData(updatedPricing)
      setFormData({ ...formData, pricing: updatedPricing })
    }
  }, [formData.products, pricingData.taxRate, pricingData.shippingCost, pricingData.discountAmount])

  const updatePricing = (field: string, value: any) => {
    const updatedPricing = { ...pricingData, [field]: value }
    
    // Recalculate totals
    if (field === 'taxRate' || field === 'shippingCost' || field === 'discountAmount') {
      const taxAmount = updatedPricing.subtotal * updatedPricing.taxRate
      updatedPricing.taxAmount = taxAmount
      updatedPricing.totalAmount = updatedPricing.subtotal + taxAmount + updatedPricing.shippingCost - updatedPricing.discountAmount
    }
    
    setPricingData(updatedPricing)
    setFormData({ ...formData, pricing: updatedPricing })
  }

  const updateDeliveryAddress = (field: string, value: string) => {
    const updatedAddress = { ...pricingData.deliveryAddress, [field]: value }
    const updatedPricing = { ...pricingData, deliveryAddress: updatedAddress }
    setPricingData(updatedPricing)
    setFormData({ ...formData, pricing: updatedPricing })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: pricingData.currency,
      minimumFractionDigits: 2
    }).format(amount)
  }

  const deliveryMethods = [
    { value: 'factory', label: 'Fabrika Teslim', description: 'Müşteri fabrikadan alır' },
    { value: 'port', label: 'Liman Teslim', description: 'En yakın limana teslim' },
    { value: 'door-to-door', label: 'Kapıya Teslim', description: 'Müşteri adresine teslim' }
  ]

  const paymentTermsOptions = [
    { value: 'advance-100', label: '%100 Peşin', description: 'Tüm ödeme peşin' },
    { value: 'advance-50', label: '%50 Peşin + %50 Teslimatta', description: 'Yarı peşin, yarı teslimatta' },
    { value: 'advance-30', label: '%30 Peşin + %70 Teslimatta', description: '30% peşin, 70% teslimatta' },
    { value: 'custom', label: 'Özel Ödeme Planı', description: 'Özel ödeme koşulları' }
  ]

  if (!formData.products || formData.products.length === 0) {
    return (
      <div className="text-center py-12">
        <Calculator className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Fiyatlandırma</h3>
        <p className="text-gray-600">Fiyatlandırma yapabilmek için önce ürün seçmelisiniz.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Pricing Calculation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="w-5 h-5" />
            Fiyat Hesaplama
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column - Calculations */}
            <div className="space-y-4">
              <div className="flex justify-between items-center py-2">
                <span className="text-gray-700">Ara Toplam:</span>
                <span className="font-semibold">{formatCurrency(pricingData.subtotal)}</span>
              </div>
              
              <div className="flex justify-between items-center py-2">
                <div className="flex items-center gap-2">
                  <span className="text-gray-700">KDV:</span>
                  <div className="flex items-center gap-1">
                    <input
                      type="number"
                      min="0"
                      max="1"
                      step="0.01"
                      value={pricingData.taxRate}
                      onChange={(e) => updatePricing('taxRate', parseFloat(e.target.value) || 0)}
                      className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                    <Percent className="w-4 h-4 text-gray-400" />
                  </div>
                </div>
                <span className="font-semibold">{formatCurrency(pricingData.taxAmount)}</span>
              </div>
              
              <div className="flex justify-between items-center py-2">
                <div className="flex items-center gap-2">
                  <span className="text-gray-700">Kargo/Nakliye:</span>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={pricingData.shippingCost}
                    onChange={(e) => updatePricing('shippingCost', parseFloat(e.target.value) || 0)}
                    className="w-24 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="0.00"
                  />
                </div>
                <span className="font-semibold">{formatCurrency(pricingData.shippingCost)}</span>
              </div>
              
              <div className="flex justify-between items-center py-2">
                <div className="flex items-center gap-2">
                  <span className="text-gray-700">İndirim:</span>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={pricingData.discountAmount}
                    onChange={(e) => updatePricing('discountAmount', parseFloat(e.target.value) || 0)}
                    className="w-24 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="0.00"
                  />
                </div>
                <span className="font-semibold text-red-600">-{formatCurrency(pricingData.discountAmount)}</span>
              </div>
              
              {pricingData.discountAmount > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">İndirim Sebebi</label>
                  <input
                    type="text"
                    value={pricingData.discountReason}
                    onChange={(e) => updatePricing('discountReason', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="İndirim sebebini belirtin"
                  />
                </div>
              )}
              
              <hr />
              
              <div className="flex justify-between items-center py-2 text-lg font-bold">
                <span className="text-gray-900">Genel Toplam:</span>
                <span className="text-green-600">{formatCurrency(pricingData.totalAmount)}</span>
              </div>
            </div>

            {/* Right Column - Currency and Terms */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Para Birimi</label>
                <select
                  value={pricingData.currency}
                  onChange={(e) => updatePricing('currency', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="USD">USD - Amerikan Doları</option>
                  <option value="EUR">EUR - Euro</option>
                  <option value="TRY">TRY - Türk Lirası</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Ödeme Koşulları</label>
                <select
                  value={pricingData.paymentTerms}
                  onChange={(e) => updatePricing('paymentTerms', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {paymentTermsOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  {paymentTermsOptions.find(o => o.value === pricingData.paymentTerms)?.description}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delivery Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="w-5 h-5" />
            Teslimat Bilgileri
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Teslimat Yöntemi</label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {deliveryMethods.map(method => (
                <div
                  key={method.value}
                  className={`p-3 border-2 rounded-lg cursor-pointer transition-all ${
                    pricingData.deliveryMethod === method.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => updatePricing('deliveryMethod', method.value)}
                >
                  <h4 className="font-medium text-gray-900">{method.label}</h4>
                  <p className="text-sm text-gray-600">{method.description}</p>
                </div>
              ))}
            </div>
          </div>

          {pricingData.deliveryMethod === 'door-to-door' && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Teslimat Adresi</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Adres</label>
                  <input
                    type="text"
                    value={pricingData.deliveryAddress.street}
                    onChange={(e) => updateDeliveryAddress('street', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Sokak, cadde, bina no"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Şehir</label>
                  <input
                    type="text"
                    value={pricingData.deliveryAddress.city}
                    onChange={(e) => updateDeliveryAddress('city', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Şehir"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Posta Kodu</label>
                  <input
                    type="text"
                    value={pricingData.deliveryAddress.postalCode}
                    onChange={(e) => updateDeliveryAddress('postalCode', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="34000"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">İletişim Kişisi</label>
                  <input
                    type="text"
                    value={pricingData.deliveryAddress.contactPerson}
                    onChange={(e) => updateDeliveryAddress('contactPerson', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Teslim alacak kişi"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Telefon</label>
                  <input
                    type="tel"
                    value={pricingData.deliveryAddress.phone}
                    onChange={(e) => updateDeliveryAddress('phone', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="+90 5XX XXX XX XX"
                  />
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tahmini Teslimat Tarihi</label>
              <input
                type="date"
                value={pricingData.estimatedDeliveryDate}
                onChange={(e) => updatePricing('estimatedDeliveryDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Özel Talimatlar</label>
            <textarea
              value={pricingData.specialInstructions}
              onChange={(e) => updatePricing('specialInstructions', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Teslimat veya sipariş hakkında özel notlar..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Summary */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Info className="w-5 h-5 text-blue-600" />
              <span className="font-medium text-blue-900">Fiyatlandırma Özeti</span>
            </div>
            <div className="text-right">
              <p className="text-sm text-blue-700">
                {deliveryMethods.find(m => m.value === pricingData.deliveryMethod)?.label} • 
                {paymentTermsOptions.find(p => p.value === pricingData.paymentTerms)?.label}
              </p>
              <p className="text-lg font-bold text-blue-900">
                {formatCurrency(pricingData.totalAmount)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
