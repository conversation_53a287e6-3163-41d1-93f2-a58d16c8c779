import apiService from './api';
import { User, LoginForm, RegisterForm, ApiResponse } from '@/types';

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
}

class AuthService {
  // Login user
  async login(credentials: LoginForm): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/api/auth/login', credentials);
    
    if (response.success && response.data) {
      // Store tokens
      apiService.setAuthToken(response.data.accessToken);
      this.setRefreshToken(response.data.refreshToken);
      
      return response.data;
    }
    
    throw new Error(response.error?.message || 'Login failed');
  }

  // Register user
  async register(userData: RegisterForm): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/api/auth/register', userData);
    
    if (response.success && response.data) {
      // Store tokens
      apiService.setAuthToken(response.data.accessToken);
      this.setRefreshToken(response.data.refreshToken);
      
      return response.data;
    }
    
    throw new Error(response.error?.message || 'Registration failed');
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await apiService.post('/api/auth/logout');
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Clear tokens
      apiService.clearAuthToken();
      this.clearRefreshToken();
    }
  }

  // Refresh access token
  async refreshToken(): Promise<string> {
    const refreshToken = this.getRefreshToken();
    
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await apiService.post<RefreshTokenResponse>('/api/auth/refresh', {
      refreshToken,
    });

    if (response.success && response.data) {
      apiService.setAuthToken(response.data.accessToken);
      return response.data.accessToken;
    }

    throw new Error(response.error?.message || 'Token refresh failed');
  }

  // Get current user
  async getCurrentUser(): Promise<User> {
    const response = await apiService.get<User>('/api/users/profile');
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.error?.message || 'Failed to get user profile');
  }

  // Verify email
  async verifyEmail(token: string): Promise<void> {
    const response = await apiService.post('/api/auth/verify-email', { token });
    
    if (!response.success) {
      throw new Error(response.error?.message || 'Email verification failed');
    }
  }

  // Forgot password
  async forgotPassword(email: string): Promise<void> {
    const response = await apiService.post('/api/auth/forgot-password', { email });
    
    if (!response.success) {
      throw new Error(response.error?.message || 'Failed to send reset email');
    }
  }

  // Reset password
  async resetPassword(token: string, password: string): Promise<void> {
    const response = await apiService.post('/api/auth/reset-password', {
      token,
      password,
    });
    
    if (!response.success) {
      throw new Error(response.error?.message || 'Password reset failed');
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    if (typeof window === 'undefined') return false;
    
    const token = localStorage.getItem('auth_token');
    return !!token;
  }

  // Get stored refresh token
  private getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null;
    
    return localStorage.getItem('refresh_token');
  }

  // Set refresh token
  private setRefreshToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('refresh_token', token);
    }
  }

  // Clear refresh token
  private clearRefreshToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('refresh_token');
    }
  }

  // Get user from token (decode JWT)
  getUserFromToken(): User | null {
    if (typeof window === 'undefined') return null;
    
    const token = localStorage.getItem('auth_token');
    if (!token) return null;

    try {
      // Simple JWT decode (without verification)
      const payload = JSON.parse(atob(token.split('.')[1]));
      
      // Check if token is expired
      if (payload.exp && payload.exp < Date.now() / 1000) {
        return null;
      }

      return {
        id: payload.userId,
        email: payload.email,
        userType: payload.userType,
        status: payload.status,
        emailVerified: payload.emailVerified || false,
        createdAt: payload.createdAt || '',
        updatedAt: payload.updatedAt || '',
      };
    } catch (error) {
      console.error('Failed to decode token:', error);
      return null;
    }
  }
}

// Create singleton instance
const authService = new AuthService();

export default authService;
