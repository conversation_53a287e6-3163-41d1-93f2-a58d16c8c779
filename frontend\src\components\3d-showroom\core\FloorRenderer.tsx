'use client';

import React, { useMemo } from 'react';
import * as THREE from 'three';
import { ProductPlacement } from './ShowroomEngine';

// Product color mapping
const getProductColor = (productId: string): string => {
  const colorMap: Record<string, string> = {
    'marble-carrara-white': '#f8f9fa',
    'marble-emperador-dark': '#8b4513',
    'granite-absolute-black': '#2d3748',
    'granite-kashmir-white': '#e2e8f0',
    'travertine-classic-beige': '#f7fafc',
    'travertine-noce': '#d4a574',
    'onyx-green-pakistan': '#38a169',
    'limestone-jerusalem-gold': '#f6d55c'
  };

  return colorMap[productId] || '#e2e8f0';
};

interface FloorRendererProps {
  products: ProductPlacement[];
  roomDimensions: { width: number; depth: number };
  showGrid?: boolean;
}

export const FloorRenderer: React.FC<FloorRendererProps> = ({
  products,
  roomDimensions,
  showGrid = false
}) => {
  // Create a grid of tiles based on products
  const tileGrid = useMemo(() => {
    if (products.length === 0) return [];

    const tiles: Array<{
      id: string;
      position: [number, number, number];
      scale: { width: number; height: number };
      product: ProductPlacement;
    }> = [];

    products.forEach((product) => {
      const tileWidth = product.scale.width / 100; // Convert cm to meters
      const tileHeight = product.scale.height / 100;
      
      // Calculate how many tiles fit in the area around the product
      const tilesX = Math.ceil(roomDimensions.width / tileWidth);
      const tilesZ = Math.ceil(roomDimensions.depth / tileHeight);
      
      const startX = -roomDimensions.width / 2;
      const startZ = -roomDimensions.depth / 2;

      for (let x = 0; x < tilesX; x++) {
        for (let z = 0; z < tilesZ; z++) {
          const posX = startX + (x * tileWidth) + (tileWidth / 2);
          const posZ = startZ + (z * tileHeight) + (tileHeight / 2);
          
          // Only add tiles that are within room bounds
          if (Math.abs(posX) <= roomDimensions.width / 2 && 
              Math.abs(posZ) <= roomDimensions.depth / 2) {
            tiles.push({
              id: `tile-${product.id}-${x}-${z}`,
              position: [posX, 0.005, posZ],
              scale: { width: tileWidth, height: tileHeight },
              product
            });
          }
        }
      }
    });

    return tiles;
  }, [products, roomDimensions]);

  // Create geometry for a single tile
  const tileGeometry = useMemo(() => {
    return new THREE.BoxGeometry(1, 0.01, 1);
  }, []);

  return (
    <group>
      {tileGrid.map((tile) => (
        <mesh
          key={tile.id}
          position={tile.position}
          scale={[tile.scale.width, 1, tile.scale.height]}
          geometry={tileGeometry}
          receiveShadow
          castShadow
        >
          <meshStandardMaterial
            color={getProductColor(tile.product.productId)}
            roughness={0.3}
            metalness={0.1}
            transparent={tile.product.opacity < 1}
            opacity={tile.product.opacity}
          />
        </mesh>
      ))}

      {/* Grid lines for visualization */}
      {showGrid && (
        <group>
          {/* Horizontal lines */}
          {Array.from({ length: Math.ceil(roomDimensions.depth) + 1 }, (_, i) => {
            const z = -roomDimensions.depth / 2 + i;
            return (
              <mesh
                key={`grid-h-${i}`}
                position={[0, 0.01, z]}
                rotation={[0, 0, Math.PI / 2]}
              >
                <cylinderGeometry args={[0.005, 0.005, roomDimensions.width]} />
                <meshBasicMaterial color="#cccccc" transparent opacity={0.3} />
              </mesh>
            );
          })}

          {/* Vertical lines */}
          {Array.from({ length: Math.ceil(roomDimensions.width) + 1 }, (_, i) => {
            const x = -roomDimensions.width / 2 + i;
            return (
              <mesh
                key={`grid-v-${i}`}
                position={[x, 0.01, 0]}
                rotation={[Math.PI / 2, 0, 0]}
              >
                <cylinderGeometry args={[0.005, 0.005, roomDimensions.depth]} />
                <meshBasicMaterial color="#cccccc" transparent opacity={0.3} />
              </mesh>
            );
          })}
        </group>
      )}
    </group>
  );
};

export default FloorRenderer;
