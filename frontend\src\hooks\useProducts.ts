'use client';

import { useState, useEffect, useCallback } from 'react';

export interface Product {
  id: string;
  name: string;
  category: string;
  image: string;
  images?: string[];
  dimensions: { 
    width: number; 
    height: number; 
    thickness: number; 
  };
  available: boolean;
  description?: string;
  country?: string;
  surfaceFinishes?: string[];
  patterns?: string[];
  priceRange?: {
    min: number;
    max: number;
    currency: string;
  };
}

export interface ProductFilters {
  category?: string;
  search?: string;
  available?: boolean;
  minPrice?: number;
  maxPrice?: number;
}

// Mock data - In real implementation, this would come from your API
const MOCK_PRODUCTS: Product[] = [
  {
    id: 'marble-carrara-white',
    name: '<PERSON>ara Beyaz Mermer',
    category: 'Mermer',
    image: 'https://images.unsplash.com/photo-1615971677499-5467cbab01c0?w=400&h=400&fit=crop',
    images: [
      '/images/products/marble-carrara-1.jpg',
      '/images/products/marble-carrara-2.jpg',
      '/images/products/marble-carrara-3.jpg'
    ],
    dimensions: { width: 60, height: 60, thickness: 2 },
    available: true,
    description: '<PERSON><PERSON>ya\'dan ithal edilen yü<PERSON><PERSON> kaliteli Carrara mermeri',
    country: 'İtalya',
    surfaceFinishes: ['ham', 'honlu', 'cilalı'],
    patterns: ['standard', 'chess']
  },
  {
    id: 'marble-emperador-dark',
    name: 'Emperador Koyu Mermer',
    category: 'Mermer',
    image: 'https://images.unsplash.com/photo-1541123437800-1bb1317badc2?w=400&h=400&fit=crop',
    images: [
      '/images/products/marble-emperador-1.jpg',
      '/images/products/marble-emperador-2.jpg'
    ],
    dimensions: { width: 60, height: 30, thickness: 2 },
    available: true,
    description: 'İspanya\'dan ithal edilen koyu tonlarda Emperador mermeri',
    country: 'İspanya',
    surfaceFinishes: ['ham', 'honlu', 'cilalı', 'fırçalı'],
    patterns: ['standard', 'herringbone']
  },
  {
    id: 'granite-absolute-black',
    name: 'Absolute Black Granit',
    category: 'Granit',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop',
    images: [
      '/images/products/granite-black-1.jpg',
      '/images/products/granite-black-2.jpg',
      '/images/products/granite-black-3.jpg'
    ],
    dimensions: { width: 80, height: 80, thickness: 3 },
    available: true,
    description: 'Hindistan\'dan ithal edilen saf siyah granit',
    country: 'Hindistan',
    surfaceFinishes: ['ham', 'honlu', 'cilalı'],
    patterns: ['standard', 'diagonal']
  },
  {
    id: 'granite-kashmir-white',
    name: 'Kashmir White Granit',
    category: 'Granit',
    image: 'https://images.unsplash.com/photo-1615971677499-5467cbab01c0?w=400&h=400&fit=crop',
    images: [
      '/images/products/granite-white-1.jpg',
      '/images/products/granite-white-2.jpg'
    ],
    dimensions: { width: 60, height: 60, thickness: 3 },
    available: true,
    description: 'Hindistan Kashmir bölgesinden beyaz granit',
    country: 'Hindistan',
    surfaceFinishes: ['ham', 'honlu', 'cilalı'],
    patterns: ['standard', 'chess']
  },
  {
    id: 'travertine-classic-beige',
    name: 'Klasik Bej Traverten',
    category: 'Traverten',
    image: 'https://images.unsplash.com/photo-1541123437800-1bb1317badc2?w=400&h=400&fit=crop',
    images: [
      '/images/products/travertine-classic-1.jpg',
      '/images/products/travertine-classic-2.jpg'
    ],
    dimensions: { width: 40, height: 40, thickness: 1.5 },
    available: true,
    description: 'Denizli\'den çıkarılan klasik bej traverten',
    country: 'Türkiye',
    surfaceFinishes: ['ham', 'honlu', 'cilalı', 'eskitme'],
    patterns: ['standard', 'herringbone', 'diagonal']
  },
  {
    id: 'travertine-noce',
    name: 'Noce Traverten',
    category: 'Traverten',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop',
    images: [
      '/images/products/travertine-noce-1.jpg',
      '/images/products/travertine-noce-2.jpg'
    ],
    dimensions: { width: 60, height: 40, thickness: 2 },
    available: true,
    description: 'Denizli\'den çıkarılan koyu tonlarda noce traverten',
    country: 'Türkiye',
    surfaceFinishes: ['ham', 'honlu', 'cilalı'],
    patterns: ['standard', 'chess', 'herringbone']
  },
  {
    id: 'onyx-green-pakistan',
    name: 'Pakistan Yeşil Oniks',
    category: 'Oniks',
    image: 'https://images.unsplash.com/photo-1541123437800-1bb1317badc2?w=400&h=400&fit=crop',
    images: [
      '/images/products/onyx-green-1.jpg',
      '/images/products/onyx-green-2.jpg'
    ],
    dimensions: { width: 30, height: 60, thickness: 2 },
    available: true,
    description: 'Pakistan\'dan ithal edilen yeşil oniks',
    country: 'Pakistan',
    surfaceFinishes: ['ham', 'cilalı'],
    patterns: ['standard']
  },
  {
    id: 'limestone-jerusalem-gold',
    name: 'Jerusalem Gold Kireçtaşı',
    category: 'Kireçtaşı',
    image: 'https://images.unsplash.com/photo-1615971677499-5467cbab01c0?w=400&h=400&fit=crop',
    images: [
      '/images/products/limestone-jerusalem-1.jpg'
    ],
    dimensions: { width: 40, height: 60, thickness: 2.5 },
    available: true,
    description: 'İsrail\'den ithal edilen altın sarısı kireçtaşı',
    country: 'İsrail',
    surfaceFinishes: ['ham', 'honlu', 'eskitme'],
    patterns: ['standard', 'diagonal']
  }
];

export const useProducts = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Simulate API call
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // In real implementation, this would be:
        // const response = await fetch('/api/products');
        // const data = await response.json();
        // setProducts(data);
        
        setProducts(MOCK_PRODUCTS);
        setError(null);
      } catch (err) {
        setError('Ürünler yüklenirken hata oluştu');
        console.error('Error fetching products:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Filter products
  const filterProducts = useCallback((filters: ProductFilters) => {
    return products.filter(product => {
      if (filters.category && filters.category !== 'all' && product.category !== filters.category) {
        return false;
      }
      
      if (filters.search && !product.name.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }
      
      if (filters.available !== undefined && product.available !== filters.available) {
        return false;
      }
      
      return true;
    });
  }, [products]);

  // Get product by ID
  const getProductById = useCallback((id: string) => {
    return products.find(product => product.id === id);
  }, [products]);

  // Get products by category
  const getProductsByCategory = useCallback((category: string) => {
    return products.filter(product => product.category === category);
  }, [products]);

  // Get unique categories
  const getCategories = useCallback(() => {
    return Array.from(new Set(products.map(product => product.category)));
  }, [products]);

  // Get available products only
  const getAvailableProducts = useCallback(() => {
    return products.filter(product => product.available);
  }, [products]);

  return {
    products,
    loading,
    error,
    filterProducts,
    getProductById,
    getProductsByCategory,
    getCategories,
    getAvailableProducts,
    // Utility functions
    totalProducts: products.length,
    availableProducts: products.filter(p => p.available).length,
    categoriesCount: new Set(products.map(p => p.category)).size
  };
};

export default useProducts;
