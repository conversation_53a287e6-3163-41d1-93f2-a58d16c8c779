/**
 * Modern CSRF Protection Middleware
 * Implements Double Submit Cookie pattern for CSRF protection
 */

import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { logWarn, logError } from '../utils/logger';

export interface CSRFRequest extends Request {
  csrfToken?: string;
}

/**
 * CSRF Protection Configuration
 */
export interface CSRFConfig {
  cookieName?: string;
  headerName?: string;
  tokenLength?: number;
  cookieOptions?: {
    httpOnly?: boolean;
    secure?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
    maxAge?: number;
  };
  ignoreMethods?: string[];
  skipRoutes?: string[];
}

const defaultConfig: Required<CSRFConfig> = {
  cookieName: 'csrf-token',
  headerName: 'x-csrf-token',
  tokenLength: 32,
  cookieOptions: {
    httpOnly: false, // Frontend needs to read this
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  },
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS'],
  skipRoutes: ['/api/health', '/api/auth/login', '/api/auth/register']
};

/**
 * Generate a cryptographically secure random token
 */
function generateToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Verify CSRF token using constant-time comparison
 */
function verifyToken(token1: string, token2: string): boolean {
  if (!token1 || !token2 || token1.length !== token2.length) {
    return false;
  }
  
  // Use crypto.timingSafeEqual for constant-time comparison
  try {
    const buffer1 = Buffer.from(token1, 'hex');
    const buffer2 = Buffer.from(token2, 'hex');
    return crypto.timingSafeEqual(buffer1, buffer2);
  } catch (error) {
    return false;
  }
}

/**
 * CSRF Protection Middleware Factory
 */
export function createCSRFProtection(config: CSRFConfig = {}): (req: CSRFRequest, res: Response, next: NextFunction) => void {
  const finalConfig = { ...defaultConfig, ...config };
  
  return (req: CSRFRequest, res: Response, next: NextFunction) => {
    const method = req.method.toUpperCase();
    const path = req.path;
    
    // Skip CSRF protection for safe methods
    if (finalConfig.ignoreMethods.includes(method)) {
      return next();
    }
    
    // Skip CSRF protection for specific routes
    console.log('CSRF Check - Path:', path, 'Skip routes:', finalConfig.skipRoutes);
    if (finalConfig.skipRoutes.some(route => path.startsWith(route))) {
      console.log('CSRF SKIPPED for path:', path);
      return next();
    }
    
    // Get existing token from cookie
    let cookieToken = req.cookies[finalConfig.cookieName];
    
    // Generate new token if none exists
    if (!cookieToken) {
      cookieToken = generateToken(finalConfig.tokenLength);
      res.cookie(finalConfig.cookieName, cookieToken, finalConfig.cookieOptions);
    }
    
    // For state-changing methods, verify the token
    if (!finalConfig.ignoreMethods.includes(method)) {
      const headerToken = req.headers[finalConfig.headerName] as string;
      const bodyToken = req.body?._csrf;
      const queryToken = req.query._csrf as string;
      
      // Get token from header, body, or query
      const submittedToken = headerToken || bodyToken || queryToken;
      
      if (!submittedToken) {
        logWarn('CSRF token missing', {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          path: req.path,
          method: req.method
        });
        
        return res.status(403).json({
          success: false,
          error: 'CSRF token required',
          code: 'CSRF_TOKEN_MISSING'
        });
      }
      
      if (!verifyToken(cookieToken, submittedToken)) {
        logError('CSRF token mismatch', new Error('CSRF token verification failed'), {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          path: req.path,
          method: req.method,
          cookieToken: cookieToken.substring(0, 8) + '...',
          submittedToken: submittedToken.substring(0, 8) + '...'
        });
        
        return res.status(403).json({
          success: false,
          error: 'Invalid CSRF token',
          code: 'CSRF_TOKEN_INVALID'
        });
      }
    }
    
    // Add token to request for use in templates/responses
    req.csrfToken = cookieToken;
    
    // Add token to response locals for template rendering
    res.locals.csrfToken = cookieToken;
    
    next();
  };
}

/**
 * Middleware to provide CSRF token endpoint
 */
export function csrfTokenEndpoint(req: CSRFRequest, res: Response): void {
  const token = req.csrfToken || generateToken();
  
  // Set cookie if not already set
  if (!req.cookies[defaultConfig.cookieName]) {
    res.cookie(defaultConfig.cookieName, token, defaultConfig.cookieOptions);
  }
  
  res.json({
    success: true,
    data: {
      csrfToken: token
    }
  });
}

/**
 * Express route handler to get CSRF token
 */
export function getCSRFToken(req: CSRFRequest, res: Response): void {
  csrfTokenEndpoint(req, res);
}

/**
 * Default CSRF protection middleware
 */
export const csrfProtection = createCSRFProtection();

/**
 * CSRF protection for API routes only
 */
export const apiCSRFProtection = createCSRFProtection({
  skipRoutes: [
    '/api/health',
    '/api/auth/login',
    '/api/auth/register',
    '/api/csrf-token'
  ]
});

/**
 * Strict CSRF protection (no skipped routes)
 */
export const strictCSRFProtection = createCSRFProtection({
  skipRoutes: []
});
