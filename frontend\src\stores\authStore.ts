import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { User, LoginForm, RegisterForm } from '@/types';
import authService from '@/services/auth';

interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (credentials: LoginForm) => Promise<void>;
  register: (userData: RegisterForm) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,

        // Actions
        login: async (credentials: LoginForm) => {
          try {
            set({ isLoading: true, error: null });
            
            const authResponse = await authService.login(credentials);
            
            set({
              user: authResponse.user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          } catch (error) {
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: error instanceof Error ? error.message : 'Login failed',
            });
            throw error;
          }
        },

        register: async (userData: RegisterForm) => {
          try {
            set({ isLoading: true, error: null });
            
            const authResponse = await authService.register(userData);
            
            set({
              user: authResponse.user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          } catch (error) {
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: error instanceof Error ? error.message : 'Registration failed',
            });
            throw error;
          }
        },

        logout: async () => {
          try {
            set({ isLoading: true });
            
            await authService.logout();
            
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: null,
            });
          } catch (error) {
            // Even if logout fails, clear local state
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: null,
            });
          }
        },

        refreshToken: async () => {
          try {
            await authService.refreshToken();
            
            // Get updated user info
            const user = await authService.getCurrentUser();
            
            set({
              user,
              isAuthenticated: true,
              error: null,
            });
          } catch (error) {
            set({
              user: null,
              isAuthenticated: false,
              error: error instanceof Error ? error.message : 'Token refresh failed',
            });
            throw error;
          }
        },

        getCurrentUser: async () => {
          try {
            set({ isLoading: true, error: null });
            
            const user = await authService.getCurrentUser();
            
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          } catch (error) {
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to get user',
            });
            throw error;
          }
        },

        clearError: () => {
          set({ error: null });
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },
      }),
      {
        name: 'auth-store',
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    {
      name: 'auth-store',
    }
  )
);

// Initialize auth state from stored token
export const initializeAuth = () => {
  const { getCurrentUser } = useAuthStore.getState();
  
  if (authService.isAuthenticated()) {
    // Try to get current user info
    getCurrentUser().catch(() => {
      // If failed, user will be logged out automatically
    });
  }
};
