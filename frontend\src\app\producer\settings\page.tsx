'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  User,
  Building2,
  Bell,
  Save,
  Edit,
  AlertTriangle,
  Factory,
  MapPin,
  Upload,
  Plus,
  Trash2,
  CheckCircle,
  Clock,
  X
} from 'lucide-react'

export default function ProducerSettings() {
  const { producer } = useProducerAuth()
  const [isEditing, setIsEditing] = React.useState(false)
  const [showAddQuarry, setShowAddQuarry] = React.useState(false)
  const [quarries, setQuarries] = React.useState([])
  const [newQuarry, setNewQuarry] = React.useState({
    name: '',
    location: '',
    address: '',
    googleMapsLink: '',
    ownershipType: 'owner',
    documents: []
  })
  const [factories, setFactories] = React.useState([])
  const [showAddFactory, setShowAddFactory] = React.useState(false)
  const [newFactory, setNewFactory] = React.useState({
    name: '',
    address: '',
    city: '',
    capacity: '',
    googleMapsLink: '',
    isActive: true
  })
  const [documents, setDocuments] = React.useState([])
  const [showUploadDocument, setShowUploadDocument] = React.useState(false)
  const [formData, setFormData] = React.useState({
    // Temel Bilgiler
    name: producer?.name || '',
    email: producer?.email || '',
    phone: producer?.phone || '',
    companyName: producer?.companyName || '',

    // Şirket Bilgileri
    taxNumber: '',
    tradeRegistryNumber: '',
    companyAddress: '',
    companyPhone: '',
    companyEmail: '',
    website: '',
    foundedYear: '',
    employeeCount: '',

    // Üretim Bilgileri
    productionCapacity: '',
    productCategories: [],
    providesCustomManufacturing: false,
    customManufacturingDetails: '',
    companyDescription: '',

    // Lokasyonlar/Fabrikalar
    factories: [],

    // Belgeler
    documents: [],

    // Bildirim Ayarları
    notifications: {
      email: true,
      push: true
    }
  })

  const handleSave = async () => {
    try {
      console.log('Saving producer settings:', formData)

      // Admin'e bildirim gönder
      await fetch('/api/admin/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'PRODUCER_PROFILE_UPDATE',
          message: `${formData.companyName} şirketi profil bilgilerini güncelledi.`,
          producerId: producer?.id
        })
      })

      setIsEditing(false)
      alert('Bilgileriniz başarıyla güncellendi. Admin onayı sonrası aktif olacaktır.')
    } catch (error) {
      console.error('Error saving settings:', error)
      alert('Bilgiler kaydedilirken bir hata oluştu.')
    }
  }

  const handleAddQuarry = () => {
    if (newQuarry.name && newQuarry.location && newQuarry.address) {
      const quarry = {
        id: Date.now().toString(),
        ...newQuarry,
        status: 'pending',
        isActive: true
      }
      setQuarries(prev => [...prev, quarry])

      // Producer context'ini güncelle - ocak eklendi
      updateProducerQuarryStatus(true)

      setNewQuarry({
        name: '',
        location: '',
        address: '',
        googleMapsLink: '',
        ownershipType: 'owner',
        documents: []
      })
      setShowAddQuarry(false)
      console.log('Ocak eklendi, admin onayı bekleniyor:', quarry)

      // Başarı mesajı göster
      alert('Ocak başarıyla eklendi! Admin onayı sonrası blok satışı yapabileceksiniz.')
    }
  }

  const handleRemoveQuarry = (quarryId: string) => {
    setQuarries(prev => {
      const updatedQuarries = prev.filter(q => q.id !== quarryId)

      // Eğer hiç ocak kalmadıysa hasQuarry'yi false yap
      if (updatedQuarries.length === 0) {
        updateProducerQuarryStatus(false)
      }

      return updatedQuarries
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-4 h-4" />
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'rejected':
        return <X className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  const updateProducerQuarryStatus = (hasQuarry: boolean) => {
    try {
      // LocalStorage'daki producer bilgisini güncelle
      const storedProducer = localStorage.getItem('producer')
      if (storedProducer) {
        const producer = JSON.parse(storedProducer)
        producer.hasQuarry = hasQuarry
        localStorage.setItem('producer', JSON.stringify(producer))

        // Cookie'yi de güncelle
        document.cookie = `producer=${encodeURIComponent(JSON.stringify(producer))}; path=/; max-age=${7 * 24 * 60 * 60}`

        console.log('Producer quarry status updated:', hasQuarry)
      }
    } catch (error) {
      console.error('Error updating producer quarry status:', error)
    }
  }

  const handleAddFactory = () => {
    if (newFactory.name && newFactory.address && newFactory.city) {
      const factory = {
        id: Date.now().toString(),
        ...newFactory,
        createdAt: new Date().toISOString()
      }
      setFactories(prev => [...prev, factory])
      setNewFactory({
        name: '',
        address: '',
        city: '',
        capacity: '',
        googleMapsLink: '',
        isActive: true
      })
      setShowAddFactory(false)
      alert('Fabrika başarıyla eklendi!')
    }
  }

  const handleRemoveFactory = (factoryId: string) => {
    setFactories(prev => prev.filter(f => f.id !== factoryId))
  }

  const handleUploadDocument = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      Array.from(files).forEach(file => {
        const document = {
          id: Date.now().toString() + Math.random(),
          name: file.name,
          type: file.type,
          size: file.size,
          uploadedAt: new Date().toISOString(),
          status: 'uploaded'
        }
        setDocuments(prev => [...prev, document])
      })
      alert('Belgeler başarıyla yüklendi!')
    }
  }

  const handleRemoveDocument = (documentId: string) => {
    setDocuments(prev => prev.filter(d => d.id !== documentId))
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Hesap Ayarları</h1>
          <p className="text-gray-600">Hesap bilgilerinizi yönetin</p>
          {!producer?.isApproved && (
            <div className="mt-2 p-2 bg-yellow-100 border border-yellow-300 rounded-md">
              <p className="text-sm text-yellow-800">
                <AlertTriangle className="w-4 h-4 inline mr-1" />
                Hesabınız onay bekliyor. Eksik bilgileri tamamlayın.
              </p>
            </div>
          )}
        </div>
        <div className="flex gap-2">
          {isEditing && (
            <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
              <Save className="w-4 h-4 mr-2" />
              Kaydet
            </Button>
          )}
          <Button
            onClick={() => setIsEditing(!isEditing)}
            variant={isEditing ? "outline" : "default"}
            className={isEditing ? "" : "bg-amber-600 hover:bg-amber-700"}
          >
            <Edit className="w-4 h-4 mr-2" />
            {isEditing ? 'İptal' : 'Düzenle'}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Temel Bilgiler
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ad Soyad
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                E-posta
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Telefon
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Şirket Adı
              </label>
              <input
                type="text"
                value={formData.companyName}
                onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5" />
            Şirket Bilgileri
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Vergi Numarası
              </label>
              <input
                type="text"
                value={formData.taxNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, taxNumber: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ticaret Sicil No
              </label>
              <input
                type="text"
                value={formData.tradeRegistryNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, tradeRegistryNumber: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Şirket Adresi
              </label>
              <textarea
                value={formData.companyAddress}
                onChange={(e) => setFormData(prev => ({ ...prev, companyAddress: e.target.value }))}
                disabled={!isEditing}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Şirket Telefonu
              </label>
              <input
                type="tel"
                value={formData.companyPhone}
                onChange={(e) => setFormData(prev => ({ ...prev, companyPhone: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Şirket E-postası
              </label>
              <input
                type="email"
                value={formData.companyEmail}
                onChange={(e) => setFormData(prev => ({ ...prev, companyEmail: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Web Sitesi
              </label>
              <input
                type="url"
                value={formData.website}
                onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
                placeholder="https://www.sirketiniz.com"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Kuruluş Yılı
              </label>
              <input
                type="number"
                value={formData.foundedYear}
                onChange={(e) => setFormData(prev => ({ ...prev, foundedYear: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
                min="1900"
                max={new Date().getFullYear()}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Çalışan Sayısı
              </label>
              <input
                type="number"
                value={formData.employeeCount}
                onChange={(e) => setFormData(prev => ({ ...prev, employeeCount: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
                min="1"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Şirket Açıklaması
              </label>
              <textarea
                value={formData.companyDescription}
                onChange={(e) => setFormData(prev => ({ ...prev, companyDescription: e.target.value }))}
                disabled={!isEditing}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
                placeholder="Şirketiniz hakkında kısa bir açıklama yazın..."
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Üretim Bilgileri */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Factory className="w-5 h-5" />
            Üretim Bilgileri
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Üretim Kapasitesi (m²/ay)
              </label>
              <input
                type="number"
                value={formData.productionCapacity}
                onChange={(e) => setFormData(prev => ({ ...prev, productionCapacity: e.target.value }))}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
                placeholder="Aylık üretim kapasitesi"
              />
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <input
                  type="checkbox"
                  checked={formData.providesCustomManufacturing}
                  onChange={(e) => setFormData(prev => ({ ...prev, providesCustomManufacturing: e.target.checked }))}
                  disabled={!isEditing}
                  className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                />
                Özel Üretim Hizmeti Veriyorum
              </label>
              {formData.providesCustomManufacturing && (
                <textarea
                  value={formData.customManufacturingDetails}
                  onChange={(e) => setFormData(prev => ({ ...prev, customManufacturingDetails: e.target.value }))}
                  disabled={!isEditing}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:bg-gray-50"
                  placeholder="Özel üretim hizmetleriniz hakkında detay verin..."
                />
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Ocak Yönetimi */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MapPin className="w-5 h-5" />
              Ocak Yönetimi
            </div>
            {isEditing && (
              <Button
                onClick={() => setShowAddQuarry(true)}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Ocak Ekle
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {quarries.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MapPin className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>Henüz ocak eklenmemiş</p>
              <p className="text-sm">Blok satışı yapabilmek için ocak eklemeniz gerekiyor</p>
            </div>
          ) : (
            <div className="space-y-4">
              {quarries.map((quarry) => (
                <div key={quarry.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{quarry.name}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs flex items-center gap-1 ${getStatusColor(quarry.status)}`}>
                          {getStatusIcon(quarry.status)}
                          {quarry.status === 'approved' ? 'Onaylandı' :
                           quarry.status === 'pending' ? 'Onay Bekliyor' : 'Reddedildi'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-1">
                        <MapPin className="w-4 h-4 inline mr-1" />
                        {quarry.location}
                      </p>
                      <p className="text-sm text-gray-600">{quarry.address}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        Sahiplik: {quarry.ownershipType === 'owner' ? 'Sahibi' : 'Kiracı'}
                      </p>
                    </div>
                    {isEditing && (
                      <Button
                        onClick={() => handleRemoveQuarry(quarry.id)}
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Ocak Ekleme Modalı */}
          {showAddQuarry && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                <h3 className="text-lg font-semibold mb-4">Yeni Ocak Ekle</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ocak Adı *
                    </label>
                    <input
                      type="text"
                      value={newQuarry.name}
                      onChange={(e) => setNewQuarry(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                      placeholder="Ocak adını girin"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Lokasyon *
                    </label>
                    <input
                      type="text"
                      value={newQuarry.location}
                      onChange={(e) => setNewQuarry(prev => ({ ...prev, location: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                      placeholder="İl/İlçe"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Adres *
                    </label>
                    <textarea
                      value={newQuarry.address}
                      onChange={(e) => setNewQuarry(prev => ({ ...prev, address: e.target.value }))}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                      placeholder="Detaylı adres"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Google Maps Linki
                    </label>
                    <input
                      type="url"
                      value={newQuarry.googleMapsLink}
                      onChange={(e) => setNewQuarry(prev => ({ ...prev, googleMapsLink: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                      placeholder="https://maps.google.com/..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Sahiplik Durumu
                    </label>
                    <select
                      value={newQuarry.ownershipType}
                      onChange={(e) => setNewQuarry(prev => ({ ...prev, ownershipType: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    >
                      <option value="owner">Sahibi</option>
                      <option value="renter">Kiracı</option>
                    </select>
                  </div>
                </div>
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    onClick={() => setShowAddQuarry(false)}
                    variant="outline"
                  >
                    İptal
                  </Button>
                  <Button
                    onClick={handleAddQuarry}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    Ekle
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Fabrikalar */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Factory className="w-5 h-5" />
              Fabrikalar
            </div>
            {isEditing && (
              <Button
                onClick={() => setShowAddFactory(true)}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Fabrika Ekle
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {factories.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Factory className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>Henüz fabrika eklenmemiş</p>
              <p className="text-sm">Fabrika bilgilerinizi ekleyerek üretim kapasitelerinizi belirtin</p>
            </div>
          ) : (
            <div className="space-y-4">
              {factories.map((factory) => (
                <div key={factory.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{factory.name}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs ${factory.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                          {factory.isActive ? 'Aktif' : 'Pasif'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-1">
                        <MapPin className="w-4 h-4 inline mr-1" />
                        {factory.city}
                      </p>
                      <p className="text-sm text-gray-600 mb-1">{factory.address}</p>
                      {factory.capacity && (
                        <p className="text-sm text-gray-600 mb-1">
                          Kapasite: {factory.capacity} m²/ay
                        </p>
                      )}
                      {factory.googleMapsLink && (
                        <a
                          href={factory.googleMapsLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          Haritada Görüntüle
                        </a>
                      )}
                    </div>
                    {isEditing && (
                      <Button
                        onClick={() => handleRemoveFactory(factory.id)}
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Fabrika Ekleme Modalı */}
          {showAddFactory && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                <h3 className="text-lg font-semibold mb-4">Yeni Fabrika Ekle</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Fabrika Adı *
                    </label>
                    <input
                      type="text"
                      value={newFactory.name}
                      onChange={(e) => setNewFactory(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                      placeholder="Fabrika adını girin"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Şehir *
                    </label>
                    <input
                      type="text"
                      value={newFactory.city}
                      onChange={(e) => setNewFactory(prev => ({ ...prev, city: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                      placeholder="Şehir"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Adres *
                    </label>
                    <textarea
                      value={newFactory.address}
                      onChange={(e) => setNewFactory(prev => ({ ...prev, address: e.target.value }))}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                      placeholder="Detaylı adres"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Üretim Kapasitesi (m²/ay)
                    </label>
                    <input
                      type="number"
                      value={newFactory.capacity}
                      onChange={(e) => setNewFactory(prev => ({ ...prev, capacity: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                      placeholder="Aylık üretim kapasitesi"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Google Maps Linki
                    </label>
                    <input
                      type="url"
                      value={newFactory.googleMapsLink}
                      onChange={(e) => setNewFactory(prev => ({ ...prev, googleMapsLink: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                      placeholder="https://maps.google.com/..."
                    />
                  </div>
                  <div>
                    <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                      <input
                        type="checkbox"
                        checked={newFactory.isActive}
                        onChange={(e) => setNewFactory(prev => ({ ...prev, isActive: e.target.checked }))}
                        className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                      />
                      Aktif Fabrika
                    </label>
                  </div>
                </div>
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    onClick={() => setShowAddFactory(false)}
                    variant="outline"
                  >
                    İptal
                  </Button>
                  <Button
                    onClick={handleAddFactory}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Ekle
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Belgeler */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              Belgeler
            </div>
            {isEditing && (
              <div>
                <input
                  type="file"
                  id="document-upload"
                  multiple
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  onChange={handleUploadDocument}
                  className="hidden"
                />
                <Button
                  onClick={() => document.getElementById('document-upload')?.click()}
                  size="sm"
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Belge Yükle
                </Button>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {documents.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Upload className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>Henüz belge yüklenmemiş</p>
              <p className="text-sm">Şirket belgelerinizi yükleyerek hesap onay sürecinizi hızlandırın</p>
            </div>
          ) : (
            <div className="space-y-3">
              {documents.map((document) => (
                <div key={document.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      {document.type.includes('pdf') ? (
                        <span className="text-red-600 font-bold text-xs">PDF</span>
                      ) : document.type.includes('image') ? (
                        <span className="text-blue-600 font-bold text-xs">IMG</span>
                      ) : (
                        <span className="text-green-600 font-bold text-xs">DOC</span>
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{document.name}</p>
                      <p className="text-xs text-gray-500">
                        {(document.size / 1024 / 1024).toFixed(2)} MB •
                        {new Date(document.uploadedAt).toLocaleDateString('tr-TR')}
                      </p>
                    </div>
                  </div>
                  {isEditing && (
                    <Button
                      onClick={() => handleRemoveDocument(document.id)}
                      size="sm"
                      variant="outline"
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Gerekli Belgeler:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Maden İşletme Ruhsatı</li>
              <li>• Ticaret Sicil Belgesi</li>
              <li>• Vergi Levhası</li>
              <li>• Üretim Kapasite Raporu</li>
              <li>• Sanayi Sicil Belgesi</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="w-5 h-5" />
            Bildirim Ayarları
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  E-posta Bildirimleri
                </label>
                <p className="text-xs text-gray-500">
                  Yeni teklifler hakkında e-posta alın
                </p>
              </div>
              <input
                type="checkbox"
                checked={formData.notifications.email}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  notifications: { ...prev.notifications, email: e.target.checked }
                }))}
                disabled={!isEditing}
                className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Tarayıcı Bildirimleri
                </label>
                <p className="text-xs text-gray-500">
                  Anlık bildirimler alın
                </p>
              </div>
              <input
                type="checkbox"
                checked={formData.notifications.push}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  notifications: { ...prev.notifications, push: e.target.checked }
                }))}
                disabled={!isEditing}
                className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
