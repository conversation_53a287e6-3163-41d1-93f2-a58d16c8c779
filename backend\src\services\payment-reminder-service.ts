import cron from 'node-cron';

interface PendingPayment {
  id: string;
  sampleRequestId: string;
  customerId: string;
  amount: number;
  currency: string;
  status: string;
  createdAt: Date;
  daysWaiting?: number;
}

class PaymentReminderService {
  private static instance: PaymentReminderService;
  private isRunning = false;

  private constructor() {}

  static getInstance(): PaymentReminderService {
    if (!PaymentReminderService.instance) {
      PaymentReminderService.instance = new PaymentReminderService();
    }
    return PaymentReminderService.instance;
  }

  // Start the payment reminder cron job
  start() {
    if (this.isRunning) {
      console.log('⚠️ Payment reminder service is already running');
      return;
    }

    // Run every day at 10:00 AM
    cron.schedule('0 10 * * *', async () => {
      console.log('🔔 Running payment reminder check...');
      await this.checkPendingPayments();
    });

    // Run every 6 hours for urgent reminders
    cron.schedule('0 */6 * * *', async () => {
      console.log('🔔 Running urgent payment reminder check...');
      await this.checkUrgentPayments();
    });

    this.isRunning = true;
    console.log('✅ Payment reminder service started');
  }

  stop() {
    this.isRunning = false;
    console.log('⏹️ Payment reminder service stopped');
  }

  // Check for pending payments and send reminders
  async checkPendingPayments() {
    try {
      const response = await fetch('http://localhost:8001/api/samples/payments/pending');
      if (!response.ok) {
        console.error('Failed to fetch pending payments');
        return;
      }

      const result = await response.json();
      const pendingPayments: PendingPayment[] = (result as any).data || [];

      for (const payment of pendingPayments) {
        const daysWaiting = this.calculateDaysWaiting(payment.createdAt);
        
        // Send reminder after 1, 3, 7, and 14 days
        if ([1, 3, 7, 14].includes(daysWaiting)) {
          await this.sendPaymentReminder(payment, daysWaiting);
        }

        // Auto-cancel after 30 days
        if (daysWaiting >= 30) {
          await this.autoCancelPayment(payment);
        }
      }

      console.log(`📊 Checked ${pendingPayments.length} pending payments`);
    } catch (error) {
      console.error('Error checking pending payments:', error);
    }
  }

  // Check for urgent payments (more than 7 days)
  async checkUrgentPayments() {
    try {
      const response = await fetch('http://localhost:8001/api/samples/payments/pending');
      if (!response.ok) return;

      const result = await response.json();
      const pendingPayments: PendingPayment[] = (result as any).data || [];

      for (const payment of pendingPayments) {
        const daysWaiting = this.calculateDaysWaiting(payment.createdAt);
        
        // Send urgent reminder every 6 hours for payments older than 7 days
        if (daysWaiting > 7 && daysWaiting < 30) {
          await this.sendUrgentPaymentReminder(payment, daysWaiting);
        }
      }
    } catch (error) {
      console.error('Error checking urgent payments:', error);
    }
  }

  // Calculate days waiting since payment was created
  private calculateDaysWaiting(createdAt: Date): number {
    const now = new Date();
    const created = new Date(createdAt);
    const diffTime = Math.abs(now.getTime() - created.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }

  // Send payment reminder notification
  private async sendPaymentReminder(payment: PendingPayment, daysWaiting: number) {
    try {
      await fetch('http://localhost:8001/api/notifications/payment-reminder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sampleRequestId: payment.sampleRequestId,
          customerId: payment.customerId,
          amount: payment.amount,
          daysWaiting
        }),
      });

      console.log(`📧 Payment reminder sent for ${payment.sampleRequestId} (${daysWaiting} days)`);
    } catch (error) {
      console.error('Error sending payment reminder:', error);
    }
  }

  // Send urgent payment reminder
  private async sendUrgentPaymentReminder(payment: PendingPayment, daysWaiting: number) {
    try {
      await fetch('http://localhost:8001/api/notifications/payment-reminder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sampleRequestId: payment.sampleRequestId,
          customerId: payment.customerId,
          amount: payment.amount,
          daysWaiting,
          urgent: true
        }),
      });

      console.log(`🚨 Urgent payment reminder sent for ${payment.sampleRequestId} (${daysWaiting} days)`);
    } catch (error) {
      console.error('Error sending urgent payment reminder:', error);
    }
  }

  // Auto-cancel payment after 30 days
  private async autoCancelPayment(payment: PendingPayment) {
    try {
      await fetch(`http://localhost:8001/api/samples/payment/${payment.id}/cancel`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: 'Auto-cancelled after 30 days of inactivity'
        }),
      });

      console.log(`❌ Auto-cancelled payment for ${payment.sampleRequestId} after 30 days`);
    } catch (error) {
      console.error('Error auto-cancelling payment:', error);
    }
  }

  // Manual trigger for testing
  async triggerReminders() {
    console.log('🔔 Manually triggering payment reminders...');
    await this.checkPendingPayments();
    await this.checkUrgentPayments();
  }

  // Get reminder statistics
  async getReminderStats() {
    try {
      const response = await fetch('http://localhost:8001/api/samples/payments/pending');
      if (!response.ok) return null;

      const result = await response.json();
      const pendingPayments: PendingPayment[] = (result as any).data || [];

      const stats = {
        total: pendingPayments.length,
        byDaysWaiting: {
          '1-3': 0,
          '4-7': 0,
          '8-14': 0,
          '15-30': 0,
          '30+': 0
        },
        urgent: 0,
        autoCancel: 0
      };

      for (const payment of pendingPayments) {
        const days = this.calculateDaysWaiting(payment.createdAt);
        
        if (days <= 3) stats.byDaysWaiting['1-3']++;
        else if (days <= 7) stats.byDaysWaiting['4-7']++;
        else if (days <= 14) stats.byDaysWaiting['8-14']++;
        else if (days <= 30) stats.byDaysWaiting['15-30']++;
        else stats.byDaysWaiting['30+']++;

        if (days > 7 && days < 30) stats.urgent++;
        if (days >= 30) stats.autoCancel++;
      }

      return stats;
    } catch (error) {
      console.error('Error getting reminder stats:', error);
      return null;
    }
  }
}

export default PaymentReminderService;
