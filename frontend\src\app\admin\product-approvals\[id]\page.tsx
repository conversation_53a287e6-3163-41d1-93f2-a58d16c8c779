'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useProducts } from '@/contexts/products-context'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  CheckCircle,
  XCircle,
  ArrowLeft,
  Calendar,
  User,
  MapPin,
  Package,
  FileText,
  Image as ImageIcon,
  Video,
  DollarSign,
  Ruler,
  Settings,
  BarChart3,
  Clock,
  Building2,
  Layers,
  Palette,
  Truck,
  FlaskConical,
  Warehouse,
  TrendingUp
} from 'lucide-react'

interface ProductDetailPageProps {
  params: {
    id: string
  }
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
  const router = useRouter()
  const { products, updateProduct } = useProducts()
  const [rejectionReason, setRejectionReason] = React.useState('')
  const [showRejectModal, setShowRejectModal] = React.useState(false)

  const product = products.find(p => p.id === params.id)

  if (!product) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Ürün bulunamadı</h3>
          <p className="text-gray-600 mb-4">Aradığınız ürün mevcut değil.</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Geri Dön
          </Button>
        </div>
      </div>
    )
  }

  const handleApprove = async () => {
    try {
      updateProduct(product.id, {
        approvalStatus: 'approved',
        reviewedAt: new Date(),
        reviewedBy: 'Admin'
      })

      alert('Ürün başarıyla onaylandı! Artık müşteriler tarafından görülebilir.')
      router.push('/admin/product-approvals')
    } catch (error) {
      alert('Onaylama işlemi sırasında bir hata oluştu.')
    }
  }

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      alert('Red sebebi gereklidir.')
      return
    }

    try {
      updateProduct(product.id, {
        approvalStatus: 'rejected',
        rejectionReason: rejectionReason,
        reviewedAt: new Date(),
        reviewedBy: 'Admin'
      })

      alert('Ürün reddedildi ve üreticiye bildirim gönderildi.')
      router.push('/admin/product-approvals')
    } catch (error) {
      alert('Reddetme işlemi sırasında bir hata oluştu.')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Beklemede</Badge>
      case 'approved':
        return <Badge variant="secondary" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Onaylandı</Badge>
      case 'rejected':
        return <Badge variant="secondary" className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Reddedildi</Badge>
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Geri Dön
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
            <p className="text-gray-600 mt-1">Ürün Detayları ve Onay İşlemleri</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          {getStatusBadge(product.approvalStatus)}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* Left Column - Product Details */}
        <div className="xl:col-span-2 space-y-6">
          {/* 1. Temel Bilgiler */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Package className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-semibold">1. Temel Bilgiler</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Ürün Adı</label>
                  <p className="text-gray-900 font-medium">{product.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Kategori</label>
                  <p className="text-gray-900">{product.category}</p>
                </div>
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-600">Açıklama</label>
                  <p className="text-gray-900">{product.description}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Menşei</label>
                  <p className="text-gray-900">{product.origin || 'Türkiye'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Durum</label>
                  <p className="text-gray-900">{product.status}</p>
                </div>
              </div>
            </div>
          </Card>

          {/* 2. Medya Yükleme Alanı */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <ImageIcon className="w-5 h-5 text-purple-600" />
                <h3 className="text-lg font-semibold">2. Medya Yükleme Alanı</h3>
              </div>
              
              {/* Kapak Resmi */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-2">Kapak Resmi</h4>
                <div className="w-full h-64 bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={product.image || product.media?.coverImage?.url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>

              {/* Plaka Resimleri */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-2">Plaka Resimleri (3 adet)</h4>
                <div className="grid grid-cols-3 gap-4">
                  {[1, 2, 3].map((index) => (
                    <div key={index} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={product.media?.slabImages?.[index - 1]?.url || product.image || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'}
                        alt={`Plaka ${index}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Mokap Resimleri */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-2">Mokap Resimleri (3 adet)</h4>
                <div className="grid grid-cols-3 gap-4">
                  {[1, 2, 3].map((index) => (
                    <div key={index} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={product.media?.mockupImages?.[index - 1]?.url || product.image || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'}
                        alt={`Mokap ${index}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Video */}
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Tanıtım Videosu (10 saniye)</h4>
                <div className="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                  {product.media?.video?.url ? (
                    <video
                      src={product.media.video.url}
                      controls
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <div className="text-center text-gray-500">
                      <Video className="w-12 h-12 mx-auto mb-2" />
                      <p>Video yüklenmemiş</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card>

          {/* 3. Teknik Özellikler */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Settings className="w-5 h-5 text-green-600" />
                <h3 className="text-lg font-semibold">3. Teknik Özellikler</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {product.technicalSpecs && Object.entries(product.technicalSpecs).map(([key, value]) => (
                  <div key={key}>
                    <label className="text-sm font-medium text-gray-600 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </label>
                    <p className="text-gray-900">{String(value)}</p>
                  </div>
                ))}
                {(!product.technicalSpecs || Object.keys(product.technicalSpecs).length === 0) && (
                  <p className="text-gray-500 col-span-2">Teknik özellik bilgisi girilmemiş</p>
                )}
              </div>
            </div>
          </Card>

          {/* 4. Ebatlı Ürün Fiyat Listesi */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <DollarSign className="w-5 h-5 text-amber-600" />
                <h3 className="text-lg font-semibold">4. Ebatlı Ürün Fiyat Listesi</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Sıra</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Kalınlık (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">En (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Boy (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Ambalaj</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Teslimat</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Fiyat (m²)</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Para Birimi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {product.dimensionPrices && product.dimensionPrices.length > 0 ? (
                      product.dimensionPrices.map((price: any, index: number) => (
                        <tr key={index}>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{index + 1}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{price.thickness}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{price.width}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{price.length}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{price.surfaceFinish}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{price.packaging}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{price.delivery}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{price.price}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{price.currency}</td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={9} className="border border-gray-300 px-3 py-2 text-sm text-gray-500 text-center">
                          Ebatlı ürün fiyat listesi girilmemiş
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </Card>

          {/* 5. Yüzey İşlemi Fiyat Listesi */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Palette className="w-5 h-5 text-indigo-600" />
                <h3 className="text-lg font-semibold">5. Yüzey İşlemi Fiyat Listesi</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Sıra</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Fiyat</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Para Birimi</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Durum</th>
                    </tr>
                  </thead>
                  <tbody>
                    {product.surfaceFinishPrices && product.surfaceFinishPrices.length > 0 ? (
                      product.surfaceFinishPrices.map((finish: any, index: number) => (
                        <tr key={index}>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{index + 1}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{finish.finish}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{finish.price}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{finish.currency}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">
                            <Badge variant={finish.available ? "default" : "secondary"}>
                              {finish.available ? "Yaparım" : "Yapamam"}
                            </Badge>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="border border-gray-300 px-3 py-2 text-sm text-gray-500 text-center">
                          Yüzey işlemi fiyat listesi girilmemiş
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </Card>

          {/* 6. Plaka Ürün Fiyat Listesi */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Layers className="w-5 h-5 text-teal-600" />
                <h3 className="text-lg font-semibold">6. Plaka Ürün Fiyat Listesi</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Sıra</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Kalınlık (cm)</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Ambalaj</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Teslimat</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Fiyat (m²)</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">Para Birimi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {product.slabPrices && product.slabPrices.length > 0 ? (
                      product.slabPrices.map((slab: any, index: number) => (
                        <tr key={index}>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{index + 1}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{slab.thickness}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{slab.surfaceFinish}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{slab.packaging}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{slab.delivery}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{slab.price}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm">{slab.currency}</td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={7} className="border border-gray-300 px-3 py-2 text-sm text-gray-500 text-center">
                          Plaka ürün fiyat listesi girilmemiş
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </Card>
        </div>

        {/* Continue with remaining steps */}
        <div className="xl:col-span-2 space-y-6">
          {/* 7. Analiz Raporları */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <FlaskConical className="w-5 h-5 text-red-600" />
                <h3 className="text-lg font-semibold">7. Analiz Raporları</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {product.analysisReports && product.analysisReports.length > 0 ? (
                  product.analysisReports.map((report: any, index: number) => (
                    <div key={index} className="border border-gray-300 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">{report.name}</h4>
                      <p className="text-sm text-gray-600 mb-2">{report.description}</p>
                      <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4 text-blue-600" />
                        <a href={report.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline text-sm">
                          Raporu Görüntüle
                        </a>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 col-span-2">Analiz raporu yüklenmemiş</p>
                )}
              </div>
            </div>
          </Card>



          {/* 8. Stok Bilgileri */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Warehouse className="w-5 h-5 text-cyan-600" />
                <h3 className="text-lg font-semibold">8. Stok Bilgileri</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Mevcut Stok</label>
                  <p className="text-gray-900 font-medium">{product.stock || 0} {product.unit || 'm²'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Minimum Sipariş</label>
                  <p className="text-gray-900 font-medium">{product.minimumOrder || 'Belirtilmemiş'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Üretim Süresi</label>
                  <p className="text-gray-900 font-medium">{product.productionTime || 'Belirtilmemiş'}</p>
                </div>
              </div>
            </div>
          </Card>

          {/* 8. Teslimat Bilgileri */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Truck className="w-5 h-5 text-emerald-600" />
                <h3 className="text-lg font-semibold">8. Teslimat Bilgileri</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Teslimat Şekli</label>
                  <p className="text-gray-900">{product.deliveryMethod || 'Fabrika Teslim'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Teslimat Süresi</label>
                  <p className="text-gray-900">{product.deliveryTime || 'Belirtilmemiş'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Ambalaj Türü</label>
                  <p className="text-gray-900">{product.packagingType || 'Paletüstü'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Özel Notlar</label>
                  <p className="text-gray-900">{product.deliveryNotes || 'Yok'}</p>
                </div>
              </div>
            </div>
          </Card>

          {/* 9. Ürün Durumu */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Settings className="w-5 h-5 text-gray-600" />
                <h3 className="text-lg font-semibold">9. Ürün Durumu</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Durum</label>
                  <div className="flex items-center gap-2">
                    <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>
                      {product.status === 'active' ? 'Aktif' : product.status === 'draft' ? 'Taslak' : 'Pasif'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Onay Durumu</label>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(product.approvalStatus)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Oluşturma Tarihi</label>
                  <p className="text-gray-900">{new Date(product.createdAt).toLocaleDateString('tr-TR')}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Son Güncelleme</label>
                  <p className="text-gray-900">{product.updatedAt ? new Date(product.updatedAt).toLocaleDateString('tr-TR') : 'Yok'}</p>
                </div>
              </div>
            </div>
          </Card>

          {/* 10. Özet ve Notlar */}
          <Card>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <FileText className="w-5 h-5 text-slate-600" />
                <h3 className="text-lg font-semibold">10. Özet ve Notlar</h3>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Ürün Özeti</label>
                  <p className="text-gray-900">{product.summary || product.description || 'Özet bilgisi girilmemiş'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Özel Notlar</label>
                  <p className="text-gray-900">{product.specialNotes || 'Özel not bulunmuyor'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Üretici Yorumları</label>
                  <p className="text-gray-900">{product.producerComments || 'Yorum bulunmuyor'}</p>
                </div>
                {product.rejectionReason && (
                  <div>
                    <label className="text-sm font-medium text-red-600">Red Sebebi</label>
                    <p className="text-red-800 bg-red-50 p-3 rounded border border-red-200">{product.rejectionReason}</p>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>

        {/* Right Column - Actions & Info */}
        <div className="space-y-6">
          {/* Action Buttons */}
          {product.approvalStatus === 'pending' && (
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Onay İşlemleri</h3>
                <div className="space-y-3">
                  <Button
                    className="w-full bg-green-600 hover:bg-green-700"
                    onClick={handleApprove}
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Ürünü Onayla
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full text-red-600 border-red-600 hover:bg-red-50"
                    onClick={() => setShowRejectModal(true)}
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    Ürünü Reddet
                  </Button>
                </div>
              </div>
            </Card>
          )}

          {/* Ocak Bilgileri */}
          {product.quarry && (
            <Card>
              <div className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Building2 className="w-5 h-5 text-blue-600" />
                  <h3 className="text-lg font-semibold">Ocak Bilgileri</h3>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Ocak Adı</label>
                    <p className="text-gray-900 font-medium">{product.quarry.name}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Ocak Sahibi Firma</label>
                      <p className="text-gray-900">{product.quarry.owner || 'Belirtilmemiş'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Kuruluş Yılı</label>
                      <p className="text-gray-900">{product.quarry.establishedYear || 'Belirtilmemiş'}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Şehir</label>
                      <p className="text-gray-900">{product.quarry.location?.city || product.quarry.location || 'Belirtilmemiş'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">İlçe</label>
                      <p className="text-gray-900">{product.quarry.location?.district || 'Belirtilmemiş'}</p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">Adres</label>
                    <p className="text-gray-900">{product.quarry.address || product.quarry.location?.address || 'Belirtilmemiş'}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Kapasite</label>
                      <p className="text-gray-900">{product.quarry.capacity || 'Belirtilmemiş'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Telefon</label>
                      <p className="text-gray-900">{product.quarry.contactInfo?.phone || 'Belirtilmemiş'}</p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">E-posta</label>
                    <p className="text-gray-900">{product.quarry.contactInfo?.email || 'Belirtilmemiş'}</p>
                  </div>

                  {product.quarry.googleMapsLink && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Google Maps</label>
                      <a
                        href={product.quarry.googleMapsLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline block"
                      >
                        Haritada Görüntüle
                      </a>
                    </div>
                  )}

                  {/* Ocaktan Çıkan Taşlar */}
                  <div>
                    <label className="text-sm font-medium text-gray-600 mb-2 block">Bu Ocaktan Çıkan Taşlar</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {product.quarry.stones && product.quarry.stones.length > 0 ? (
                        product.quarry.stones.map((stone: any, index: number) => (
                          <div key={index} className="border border-gray-200 rounded-lg p-3">
                            <img
                              src={stone.image || product.image}
                              alt={stone.name}
                              className="w-full h-20 object-cover rounded mb-2"
                            />
                            <p className="text-sm font-medium text-gray-900">{stone.name}</p>
                            <p className="text-xs text-gray-600">{stone.category}</p>
                            {stone.producerCount && (
                              <p className="text-xs text-blue-600 mt-1">{stone.producerCount} üretici</p>
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="col-span-full">
                          <div className="border border-gray-200 rounded-lg p-4 text-center">
                            <p className="text-sm text-gray-500">Bu ocaktan çıkan diğer taşlar henüz sisteme eklenmemiş</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {/* Product Info */}
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Ürün Bilgileri</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <User className="w-4 h-4 text-gray-500" />
                  <span className="text-gray-600">Üretici:</span>
                  <span className="font-medium">{product.producer}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <span className="text-gray-600">Gönderim:</span>
                  <span className="font-medium">
                    {product.submittedAt ? new Date(product.submittedAt).toLocaleDateString('tr-TR') : new Date(product.createdAt).toLocaleDateString('tr-TR')}
                  </span>
                </div>
              </div>
            </div>
          </Card>

          {/* Technical Specs */}
          {product.technicalSpecs && (
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Teknik Özellikler</h3>
                <div className="space-y-2">
                  {Object.entries(product.technicalSpecs).map(([key, value]) => (
                    <div key={key} className="flex justify-between text-sm">
                      <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                      <span className="font-medium">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* Reject Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Ürünü Reddet</h3>
            <p className="text-gray-600 mb-4">
              <strong>{product.name}</strong> ürününü neden reddediyorsunuz?
            </p>
            <textarea
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder="Red sebebini açıklayın..."
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
              rows={4}
            />
            <div className="flex gap-3 mt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setShowRejectModal(false)
                  setRejectionReason('')
                }}
                className="flex-1"
              >
                İptal
              </Button>
              <Button
                onClick={handleReject}
                disabled={!rejectionReason.trim()}
                className="flex-1 bg-red-600 hover:bg-red-700"
              >
                Reddet
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
