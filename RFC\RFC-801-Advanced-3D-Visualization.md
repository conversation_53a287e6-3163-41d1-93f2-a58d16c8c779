# RFC-801: Gelişmiş 3D Görselleştirme ve Sanal Deneyim Sistemi

**Durum**: Implemented (Fiyatlandırma Kaldırıldı) ✅
**Yazar**: Augment Agent
**Tarih**: 2025-06-28
**Son Güncelleme**: 2025-06-29
**Versiyon**: 1.1

## 1. Özet

Bu RFC, Stoneline ve AGT'nin 3D görselleştirme özelliklerini analiz ederek, doğal taş marketplace platformumuz için gelişmiş 3D görselleştirme, sanal mekan simülasyonu ve interaktif ürün deneyimi sistemi tasarlar.

## 2. Motivasyon

### 2.1 Mevcut Durum Analizi

**Stoneline Simulator Özellikleri:**
- <PERSON>da seçimi (Salon, Mutfak, Banyo, Yatak Odası, Dış Mekan)
- Döşeme pattern'leri (Standard, Chess, Horizontal/Vertical Skew)
- Derz boyutu ve rengi ayarları
- Döşeme açısı kontrolü (0°, 45°, 90°, 135°, 180°)
- Gerçek zamanlı görselleştirme
- Fotoğraf ve PDF kaydetme

**AGT Evini Tasarla Özellikleri:**
- Mekan bazlı tasarım
- Ürün kombinasyonları
- Gerçekçi görselleştirme

### 2.2 İhtiyaçlar
- Doğal taş ürünlerinin farklı ebatlarda görselleştirilmesi
- Yüzey işlemlerinin gerçekçi gösterimi
- Mekan uyumluluğu kontrolü
- Müşteri karar verme sürecini hızlandırma
- Satış öncesi ürün deneyimi

## 3. Detaylı Tasarım

### 3.1 Sistem Mimarisi

```
┌─────────────────────────────────────────────────────────────┐
│                    3D VISUALIZATION LAYER                   │
├─────────────────────────────────────────────────────────────┤
│  Room Simulator  │  Product Viewer  │  AR Experience  │ VR  │
├─────────────────────────────────────────────────────────────┤
│                   RENDERING ENGINE                          │
├─────────────────────────────────────────────────────────────┤
│  Three.js  │  WebGL  │  GLTF Loader  │  Texture Manager    │
├─────────────────────────────────────────────────────────────┤
│                   CONFIGURATION LAYER                       │
├─────────────────────────────────────────────────────────────┤
│  Dimension Config  │  Surface Config  │  Pattern Config     │
├─────────────────────────────────────────────────────────────┤
│                     DATA LAYER                              │
├─────────────────────────────────────────────────────────────┤
│  3D Models  │  Textures  │  Materials  │  Room Templates    │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Temel Bileşenler

#### 3.2.1 Advanced Product Viewer
```typescript
interface AdvancedProductViewer {
  // Ürün konfigürasyonu
  productId: string;
  dimensions: {
    width: number;    // En (cm)
    height: number;   // Boy (cm)
    thickness: number; // Kalınlık (cm)
  };

  // Yüzey işlemi seçenekleri
  surfaceFinish: 'ham' | 'honlu' | 'cilali' | 'fircinlanmis' |
                 'yakma' | 'eskitme' | 'kumlama' | 'dolgu';

  // Görselleştirme ayarları
  lighting: 'natural' | 'indoor' | 'outdoor' | 'studio';
  environment: 'bathroom' | 'kitchen' | 'living' | 'outdoor';

  // Etkileşim özellikleri
  enableRotation: boolean;
  enableZoom: boolean;
  enableAnnotations: boolean;
  showDimensions: boolean;
}
```

#### 3.2.2 Room Simulator
```typescript
interface RoomSimulator {
  // Mekan seçimi
  roomType: 'bathroom' | 'kitchen' | 'living' | 'bedroom' | 'outdoor';
  roomTemplate: string; // Önceden tanımlı mekan şablonları

  // Döşeme konfigürasyonu
  pattern: {
    type: 'standard' | 'chess' | 'herringbone' | 'diagonal' | 'random';
    angle: number; // 0-360 derece
    offset: number; // Pattern offset
  };

  // Derz ayarları
  grout: {
    width: number; // mm cinsinden
    color: string; // Hex color
  };

  // Ürün yerleştirme
  placement: {
    surface: 'floor' | 'wall' | 'ceiling' | 'countertop';
    area: Rectangle; // Uygulama alanı
  };
}
```

#### 3.2.3 Dimension Configurator
```typescript
interface DimensionConfigurator {
  // Standart ebatlar
  standardSizes: Array<{
    width: number;
    height: number;
    thickness: number;
    label: string; // "30x60x2cm"
  }>;

  // Özel ebat
  customSize: {
    enabled: boolean;
    minWidth: number;
    maxWidth: number;
    minHeight: number;
    maxHeight: number;
    thicknessOptions: number[];
  };

  // ~~Fiyat hesaplama~~ - KALDIRILDI (2025-06-29)
  // pricing: {
  //   basePrice: number; // m² fiyatı
  //   surfaceFinishMultiplier: Record<string, number>;
  //   customSizeMultiplier: number;
  // };
}
```

### 3.3 Gelişmiş Özellikler

#### 3.3.1 Gerçek Zamanlı Materyal Editörü
```typescript
interface MaterialEditor {
  // PBR (Physically Based Rendering) özellikleri
  albedo: string; // Base color texture
  normal: string; // Normal map
  roughness: number; // 0-1 arası
  metallic: number; // 0-1 arası
  displacement: string; // Height map

  // Doğal taş özellikleri
  stoneProperties: {
    porosity: number; // Gözeneklilik
    hardness: number; // Mohs sertliği
    absorption: number; // Su emme oranı
    density: number; // Yoğunluk
  };

  // Yüzey işlemi simülasyonu
  surfaceEffects: {
    polish: number; // Cilalama seviyesi
    honing: number; // Honlama seviyesi
    brushing: number; // Fırçalama seviyesi
    aging: number; // Eskitme seviyesi
  };
}
```

#### 3.3.2 Akıllı Öneri Sistemi
```typescript
interface SmartRecommendation {
  // Mekan analizi
  roomAnalysis: {
    lighting: 'low' | 'medium' | 'high';
    humidity: 'low' | 'medium' | 'high';
    traffic: 'low' | 'medium' | 'high';
    style: 'modern' | 'classic' | 'rustic' | 'minimalist';
  };

  // Ürün önerileri
  recommendations: Array<{
    productId: string;
    suitabilityScore: number; // 0-100
    reasons: string[];
    alternativeSizes: string[];
    suggestedFinish: string[];
  }>;

  // Kombinasyon önerileri
  combinations: Array<{
    mainProduct: string;
    accentProduct: string;
    pattern: string;
    description: string;
  }>;
}
```

### 3.4 AR/VR Entegrasyonu

#### 3.4.1 Artırılmış Gerçeklik (AR)
```typescript
interface ARExperience {
  // Mobil AR
  mobileAR: {
    markerBased: boolean; // QR kod ile
    markerless: boolean; // Yüzey tanıma ile
    scaleDetection: boolean; // Otomatik ölçek
  };

  // Web AR
  webAR: {
    webXR: boolean; // WebXR API
    fallbackMode: '2D' | '3D'; // Desteklenmezse
  };

  // Etkileşim
  interactions: {
    placement: boolean; // Ürün yerleştirme
    scaling: boolean; // Boyut ayarlama
    rotation: boolean; // Döndürme
    materialChange: boolean; // Materyal değiştirme
  };
}
```

#### 3.4.2 Sanal Gerçeklik (VR)
```typescript
interface VRExperience {
  // VR platformları
  platforms: {
    oculusQuest: boolean;
    htcVive: boolean;
    webVR: boolean;
  };

  // Sanal showroom
  virtualShowroom: {
    roomTemplates: string[];
    productCatalog: string[];
    guidedTour: boolean;
    interactiveDemo: boolean;
  };

  // Sosyal özellikler
  social: {
    multiUser: boolean; // Çoklu kullanıcı
    voiceChat: boolean; // Sesli sohbet
    sharedViewing: boolean; // Paylaşımlı görüntüleme
  };
}
```

## 4. Teknik Implementasyon

### 4.1 Frontend Bileşenleri

#### 4.1.1 Ana 3D Viewer Bileşeni
```typescript
// src/components/3d/AdvancedProductViewer.tsx
interface AdvancedProductViewerProps {
  productId: string;
  initialConfig?: ProductViewerConfig;
  onConfigChange?: (config: ProductViewerConfig) => void;
  // onPriceUpdate?: (price: number) => void; // KALDIRILDI
  enableAR?: boolean;
  enableVR?: boolean;
}

export const AdvancedProductViewer: React.FC<AdvancedProductViewerProps> = ({
  productId,
  initialConfig,
  onConfigChange,
  // onPriceUpdate, // KALDIRILDI
  enableAR = false,
  enableVR = false
}) => {
  // Component implementation
};
```

#### 4.1.2 Mekan Simülatörü
```typescript
// src/components/3d/RoomSimulator.tsx
interface RoomSimulatorProps {
  roomType: RoomType;
  products: ProductConfig[];
  onLayoutChange?: (layout: RoomLayout) => void;
  onSave?: (design: RoomDesign) => void;
}

export const RoomSimulator: React.FC<RoomSimulatorProps> = ({
  roomType,
  products,
  onLayoutChange,
  onSave
}) => {
  // Component implementation
};
```

### 4.2 Backend Servisleri

#### 4.2.1 3D Model Yönetimi
```typescript
// src/modules/3d/services/ModelService.ts
export class ModelService {
  async generateProductModel(productId: string, config: ProductConfig): Promise<string> {
    // 3D model oluşturma
  }

  async optimizeModel(modelPath: string, quality: 'low' | 'medium' | 'high'): Promise<string> {
    // Model optimizasyonu
  }

  async generateTextures(materialId: string, finish: SurfaceFinish): Promise<TextureSet> {
    // Texture oluşturma
  }
}
```

#### ~~4.2.2 Fiyat Hesaplama Servisi~~ - KALDIRILDI (2025-06-29)
```typescript
// ~~src/modules/pricing/services/DynamicPricingService.ts~~ - KALDIRILDI
// export class DynamicPricingService {
//   calculatePrice(config: ProductConfig): PriceBreakdown {
//     // Dinamik fiyat hesaplama
//   }
//
//   getSurfaceFinishMultiplier(finish: SurfaceFinish): number {
    // Yüzey işlemi çarpanı
  }

  getCustomSizeMultiplier(dimensions: Dimensions): number {
    // Özel ebat çarpanı
  }
}
```

### 4.3 Veritabanı Şeması

```sql
-- 3D Model tabloları
CREATE TABLE product_3d_models (
  id UUID PRIMARY KEY,
  product_id UUID REFERENCES products(id),
  model_path TEXT NOT NULL,
  texture_set JSONB,
  quality_level VARCHAR(20),
  file_size BIGINT,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE room_templates (
  id UUID PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  type VARCHAR(50) NOT NULL,
  dimensions JSONB NOT NULL,
  lighting_config JSONB,
  camera_positions JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE user_designs (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  room_template_id UUID REFERENCES room_templates(id),
  product_configs JSONB NOT NULL,
  layout_data JSONB,
  preview_image TEXT,
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 5. Kullanıcı Deneyimi (UX)

### 5.1 Kullanıcı Akışı

```
1. Ürün Seçimi
   ↓
2. Ebat Konfigürasyonu
   ↓
3. Yüzey İşlemi Seçimi
   ↓
4. 3D Önizleme
   ↓
5. Mekan Simülasyonu (Opsiyonel)
   ↓
6. AR/VR Deneyimi (Opsiyonel)
   ↓
7. Fiyat Hesaplama
   ↓
8. Tasarım Kaydetme
   ↓
9. Teklif Talebi
```

### 5.2 Arayüz Tasarımı

#### 5.2.1 Konfigürasyon Paneli
```typescript
interface ConfigurationPanel {
  sections: {
    dimensions: DimensionSelector;
    surfaceFinish: SurfaceFinishSelector;
    pattern: PatternSelector;
    lighting: LightingSelector;
    environment: EnvironmentSelector;
  };

  realTimePreview: boolean;
  priceDisplay: PriceDisplay;
  saveOptions: SaveOptions;
}
```

#### 5.2.2 Mobil Optimizasyon
```typescript
interface MobileOptimization {
  touchControls: {
    pinchZoom: boolean;
    swipeRotate: boolean;
    tapSelect: boolean;
  };

  performance: {
    adaptiveQuality: boolean;
    batteryOptimization: boolean;
    dataUsageControl: boolean;
  };

  ui: {
    collapsiblePanels: boolean;
    gestureHints: boolean;
    voiceControl: boolean;
  };
}
```

## 6. Performans Optimizasyonu

### 6.1 LOD (Level of Detail) Sistemi
```typescript
interface LODSystem {
  levels: {
    high: { distance: number; polygonCount: number; textureSize: number; };
    medium: { distance: number; polygonCount: number; textureSize: number; };
    low: { distance: number; polygonCount: number; textureSize: number; };
  };

  adaptiveLoading: boolean;
  preloadStrategy: 'aggressive' | 'conservative' | 'adaptive';
}
```

### 6.2 Texture Streaming
```typescript
interface TextureStreaming {
  compressionFormats: ['webp', 'ktx2', 'basis'];
  mipmapGeneration: boolean;
  progressiveLoading: boolean;
  cacheStrategy: 'memory' | 'disk' | 'hybrid';
}
```

## 7. Analytics ve Metrikleri

### 7.1 Kullanıcı Etkileşim Metrikleri
```typescript
interface ViewerAnalytics {
  sessionMetrics: {
    viewDuration: number;
    interactionCount: number;
    configurationChanges: number;
    arUsage: boolean;
    vrUsage: boolean;
  };

  performanceMetrics: {
    loadTime: number;
    frameRate: number;
    memoryUsage: number;
    errorCount: number;
  };

  businessMetrics: {
    conversionRate: number;
    priceInquiries: number;
    designSaves: number;
    socialShares: number;
  };
}
```

## 8. Güvenlik ve Gizlilik

### 8.1 3D Model Güvenliği
- Model dosyalarının DRM koruması
- Watermark ekleme
- İndirme kısıtlamaları
- Telif hakkı koruması

### 8.2 Kullanıcı Verisi
- Tasarım verilerinin şifrelenmesi
- GDPR uyumlu veri saklama
- Kullanıcı izinleri yönetimi

## 9. Test Stratejisi

### 9.1 Performans Testleri
```bash
# WebGL performans testi
npm run test:webgl-performance

# 3D model yükleme testi
npm run test:model-loading

# Mobil performans testi
npm run test:mobile-performance
```

### 9.2 Uyumluluk Testleri
```bash
# Tarayıcı uyumluluğu
npm run test:browser-compatibility

# Cihaz uyumluluğu
npm run test:device-compatibility

# AR/VR uyumluluğu
npm run test:ar-vr-compatibility
```

## 10. Deployment ve Ölçeklendirme

### 10.1 CDN Stratejisi
- 3D model dosyaları için global CDN
- Texture cache optimizasyonu
- Bölgesel sunucu dağıtımı

### 10.2 Mikroservis Mimarisi
```
3D Visualization Service
├── Model Generation Service
├── Texture Processing Service
├── AR/VR Service
├── Analytics Service
└── Cache Management Service
```

## 11. Gelecek Geliştirmeler

### 11.1 AI Entegrasyonu
- Otomatik mekan analizi
- Akıllı ürün önerileri
- Stil tanıma ve öneriler

### 11.2 Sosyal Özellikler
- Tasarım paylaşımı
- Topluluk galerileri
- Uzman tavsiyeleri

## 12. Sonuç

Bu RFC, doğal taş marketplace platformumuz için kapsamlı bir 3D görselleştirme sistemi tasarlar. Stoneline ve AGT'nin başarılı özelliklerini analiz ederek, daha gelişmiş ve kullanıcı dostu bir deneyim sunar.

**Beklenen Faydalar:**
- %40 artış müşteri etkileşiminde
- %25 artış dönüşüm oranında
- %60 azalma ürün iade oranında
- %30 artış ortalama sipariş değerinde

**Implementasyon Süresi:** 6-9 ay
**Tahmini Maliyet:** $150,000 - $200,000
**ROI Beklentisi:** 18 ay içinde geri dönüş

## 13. Detaylı Özellik Spesifikasyonları

### 13.1 Ebat Seçici (Dimension Selector)

#### 13.1.1 Standart Ebatlar
```typescript
interface StandardDimensions {
  categories: {
    floor: Array<{
      size: string; // "30x60", "60x60", "80x80"
      thickness: number[]; // [2, 3] cm
      applications: string[]; // ["iç mekan", "dış mekan"]
    }>;
    wall: Array<{
      size: string; // "25x40", "30x60", "40x80"
      thickness: number[]; // [1, 1.5, 2] cm
      applications: string[]; // ["banyo", "mutfak", "salon"]
    }>;
    countertop: Array<{
      size: string; // "custom", "standard slabs"
      thickness: number[]; // [2, 3, 4] cm
      edgeProfiles: string[]; // ["düz", "eğimli", "dekoratif"]
    }>;
  };
}
```

#### 13.1.2 Özel Ebat Hesaplayıcı
```typescript
interface CustomDimensionCalculator {
  constraints: {
    minWidth: number; // 10 cm
    maxWidth: number; // 300 cm
    minHeight: number; // 10 cm
    maxHeight: number; // 300 cm
    thicknessOptions: number[]; // [1, 1.5, 2, 3, 4, 5] cm
  };

  validation: {
    aspectRatio: { min: number; max: number; };
    structuralLimits: boolean;
    productionFeasibility: boolean;
  };

  pricing: {
    baseCalculation: 'area' | 'perimeter' | 'volume';
    wastageMultiplier: number; // 1.1 (10% fire)
    cuttingCost: number; // m² başına ek maliyet
    customSizePremium: number; // %15 ek ücret
  };
}
```

### 13.2 Yüzey İşlemi Görselleştirme

#### 13.2.1 Yüzey İşlemi Tipleri
```typescript
interface SurfaceFinishTypes {
  ham: {
    description: "Doğal yüzey, işlenmemiş";
    roughness: 0.9;
    metallic: 0.0;
    normalIntensity: 1.0;
    priceMultiplier: 1.0;
  };

  honlu: {
    description: "Mat yüzey, pürüzsüz";
    roughness: 0.6;
    metallic: 0.0;
    normalIntensity: 0.3;
    priceMultiplier: 1.2;
  };

  cilali: {
    description: "Parlak yüzey, ayna gibi";
    roughness: 0.1;
    metallic: 0.0;
    normalIntensity: 0.1;
    priceMultiplier: 1.5;
  };

  fircinlanmis: {
    description: "Fırçalanmış doku";
    roughness: 0.7;
    metallic: 0.0;
    normalIntensity: 0.8;
    priceMultiplier: 1.3;
  };

  yakma: {
    description: "Alev ile yakılmış yüzey";
    roughness: 0.8;
    metallic: 0.0;
    normalIntensity: 1.2;
    priceMultiplier: 1.4;
  };

  eskitme: {
    description: "Antik görünüm";
    roughness: 0.9;
    metallic: 0.0;
    normalIntensity: 1.1;
    priceMultiplier: 1.6;
  };
}
```

#### 13.2.2 Gerçek Zamanlı Yüzey Değişimi
```typescript
interface RealTimeSurfaceChange {
  transitionAnimation: {
    duration: number; // 1000ms
    easing: 'ease-in-out';
    steps: number; // 30 frame
  };

  shaderUniforms: {
    roughnessMap: WebGLTexture;
    normalMap: WebGLTexture;
    displacementMap: WebGLTexture;
    aoMap: WebGLTexture; // Ambient Occlusion
  };

  qualityLevels: {
    high: { textureSize: 2048; shaderComplexity: 'full'; };
    medium: { textureSize: 1024; shaderComplexity: 'simplified'; };
    low: { textureSize: 512; shaderComplexity: 'basic'; };
  };
}
```

### 13.3 Mekan Simülasyonu Detayları

#### 13.3.1 Mekan Şablonları
```typescript
interface RoomTemplates {
  bathroom: {
    modern: {
      dimensions: { width: 250; height: 300; depth: 200; }; // cm
      fixtures: ['shower', 'toilet', 'sink', 'mirror'];
      lighting: 'bright_white';
      humidity: 'high';
      applications: ['floor', 'wall', 'shower_wall'];
    };

    classic: {
      dimensions: { width: 300; height: 350; depth: 250; };
      fixtures: ['bathtub', 'toilet', 'vanity', 'window'];
      lighting: 'warm_white';
      humidity: 'high';
      applications: ['floor', 'wall', 'vanity_top'];
    };
  };

  kitchen: {
    modern: {
      dimensions: { width: 400; height: 300; depth: 600; };
      fixtures: ['island', 'cabinets', 'appliances', 'window'];
      lighting: 'task_lighting';
      humidity: 'medium';
      applications: ['floor', 'backsplash', 'countertop'];
    };

    traditional: {
      dimensions: { width: 350; height: 280; depth: 500; };
      fixtures: ['dining_table', 'cabinets', 'stove', 'sink'];
      lighting: 'ambient';
      humidity: 'medium';
      applications: ['floor', 'backsplash', 'countertop'];
    };
  };

  living: {
    contemporary: {
      dimensions: { width: 500; height: 300; depth: 600; };
      fixtures: ['sofa', 'tv_unit', 'coffee_table', 'fireplace'];
      lighting: 'ambient';
      humidity: 'low';
      applications: ['floor', 'accent_wall', 'fireplace'];
    };
  };
}
```

#### 13.3.2 Döşeme Pattern'leri
```typescript
interface TilingPatterns {
  standard: {
    name: "Standart Döşeme";
    description: "Düz sıralı döşeme";
    algorithm: 'grid';
    parameters: { offsetX: 0; offsetY: 0; rotation: 0; };
  };

  chess: {
    name: "Satranç Tahtası";
    description: "Alternatif renk/desen";
    algorithm: 'checkerboard';
    parameters: { alternatePattern: true; };
  };

  herringbone: {
    name: "Balık Kılçığı";
    description: "45° açılı zigzag";
    algorithm: 'herringbone';
    parameters: { angle: 45; spacing: 2; };
  };

  diagonal: {
    name: "Çapraz Döşeme";
    description: "45° açılı döşeme";
    algorithm: 'diagonal';
    parameters: { angle: 45; offsetX: 0.5; offsetY: 0.5; };
  };

  random: {
    name: "Rastgele Döşeme";
    description: "Doğal görünüm";
    algorithm: 'random';
    parameters: { seed: 12345; variation: 0.3; };
  };

  brick: {
    name: "Tuğla Döşeme";
    description: "Yarım offset";
    algorithm: 'brick';
    parameters: { offsetX: 0.5; offsetY: 0; };
  };
}
```

### 13.4 Fiyat Hesaplama Sistemi

#### 13.4.1 Dinamik Fiyatlandırma
```typescript
interface DynamicPricing {
  basePrice: {
    calculation: 'per_m2' | 'per_piece' | 'per_ton';
    currency: 'USD' | 'EUR' | 'TRY';
    vatIncluded: boolean;
  };

  modifiers: {
    surfaceFinish: Record<SurfaceFinish, number>; // Çarpan
    customSize: {
      threshold: number; // 100 m² üzeri
      multiplier: number; // 1.15 (15% ek)
    };

    thickness: Record<number, number>; // Kalınlık çarpanları

    quantity: Array<{
      min: number; // Minimum miktar
      max: number; // Maksimum miktar
      discount: number; // İndirim oranı
    }>;

    urgency: {
      standard: 1.0; // 30 gün
      fast: 1.2; // 15 gün (20% ek)
      express: 1.5; // 7 gün (50% ek)
    };
  };

  additionalCosts: {
    cutting: number; // Kesim ücreti (m² başına)
    edgePolishing: number; // Kenar cilalama
    packaging: number; // Ambalaj
    shipping: 'calculated' | 'included' | 'free_over_amount';
  };
}
```

#### 13.4.2 Gerçek Zamanlı Fiyat Güncellemesi
```typescript
interface RealTimePricing {
  updateTriggers: [
    'dimension_change',
    'surface_finish_change',
    'quantity_change',
    'pattern_change',
    'delivery_option_change'
  ];

  calculation: {
    debounceTime: 300; // ms
    showBreakdown: boolean;
    showComparison: boolean;
    showSavings: boolean;
  };

  display: {
    format: 'detailed' | 'summary';
    currency: 'primary' | 'multi';
    vatDisplay: 'included' | 'excluded' | 'both';
  };
}
```

## 14. Kullanıcı Arayüzü Detayları

### 14.1 Konfigürasyon Paneli Tasarımı
```typescript
interface ConfigurationPanelUI {
  layout: {
    position: 'left' | 'right' | 'bottom' | 'floating';
    collapsible: boolean;
    responsive: boolean;
    width: { desktop: 350; tablet: 300; mobile: '100%'; };
  };

  sections: {
    dimensions: {
      title: "Ebat Seçimi";
      icon: "ruler";
      expanded: true;
      components: ['StandardSizes', 'CustomSize', 'ThicknessSelector'];
    };

    surfaceFinish: {
      title: "Yüzey İşlemi";
      icon: "texture";
      expanded: true;
      components: ['FinishGrid', 'FinishComparison', 'PriceImpact'];
    };

    visualization: {
      title: "Görselleştirme";
      icon: "eye";
      expanded: false;
      components: ['LightingControl', 'EnvironmentSelector', 'CameraPresets'];
    };

    application: {
      title: "Uygulama";
      icon: "home";
      expanded: false;
      components: ['RoomSelector', 'PatternSelector', 'GroutOptions'];
    };
  };
}
```

### 14.2 Mobil Optimizasyon
```typescript
interface MobileOptimization {
  touchGestures: {
    singleTap: 'select_surface' | 'show_info';
    doubleTap: 'zoom_to_fit' | 'reset_view';
    pinch: 'zoom';
    pan: 'rotate_camera';
    longPress: 'context_menu';
  };

  performance: {
    adaptiveQuality: {
      batteryLevel: { low: 'basic'; medium: 'standard'; high: 'enhanced'; };
      deviceTier: { low: 'basic'; mid: 'standard'; high: 'premium'; };
      networkSpeed: { slow: 'compressed'; fast: 'full_quality'; };
    };

    memoryManagement: {
      textureCompression: true;
      modelSimplification: true;
      aggressiveGC: true;
    };
  };

  ui: {
    bottomSheet: boolean; // Konfigürasyon paneli
    floatingButtons: boolean; // Hızlı erişim
    gestureHints: boolean; // İlk kullanım rehberi
    hapticFeedback: boolean; // Dokunsal geri bildirim
  };
}
```

## 15. Teknik Implementasyon Detayları

### 15.1 3D Rendering Pipeline
```typescript
interface RenderingPipeline {
  stages: {
    geometryProcessing: {
      vertexShader: string;
      tessellation: boolean;
      geometryShader: boolean;
    };

    rasterization: {
      multisampling: number; // 4x MSAA
      depthTesting: boolean;
      backfaceCulling: boolean;
    };

    fragmentProcessing: {
      fragmentShader: string;
      textureFiltering: 'linear' | 'anisotropic';
      shadowMapping: boolean;
    };

    postProcessing: {
      toneMapping: 'aces' | 'reinhard' | 'linear';
      colorGrading: boolean;
      bloom: boolean;
      ssao: boolean; // Screen Space Ambient Occlusion
    };
  };
}
```

### 15.2 Shader Sistemi
```glsl
// Vertex Shader (PBR Material)
attribute vec3 position;
attribute vec3 normal;
attribute vec2 uv;
attribute vec3 tangent;

uniform mat4 modelMatrix;
uniform mat4 viewMatrix;
uniform mat4 projectionMatrix;
uniform mat3 normalMatrix;

varying vec3 vWorldPosition;
varying vec3 vWorldNormal;
varying vec2 vUv;
varying vec3 vTangent;
varying vec3 vBitangent;

void main() {
    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
    vWorldPosition = worldPosition.xyz;
    vWorldNormal = normalize(normalMatrix * normal);
    vUv = uv;

    vTangent = normalize(normalMatrix * tangent);
    vBitangent = cross(vWorldNormal, vTangent);

    gl_Position = projectionMatrix * viewMatrix * worldPosition;
}
```

```glsl
// Fragment Shader (PBR Material)
precision highp float;

uniform sampler2D albedoMap;
uniform sampler2D normalMap;
uniform sampler2D roughnessMap;
uniform sampler2D metallicMap;
uniform sampler2D aoMap;

uniform vec3 cameraPosition;
uniform vec3 lightPosition;
uniform vec3 lightColor;
uniform float lightIntensity;

varying vec3 vWorldPosition;
varying vec3 vWorldNormal;
varying vec2 vUv;
varying vec3 vTangent;
varying vec3 vBitangent;

// PBR calculation functions
vec3 calculatePBR(vec3 albedo, float roughness, float metallic, vec3 normal, vec3 viewDir, vec3 lightDir) {
    // PBR implementation
    // ... (Cook-Torrance BRDF)
}

void main() {
    vec3 albedo = texture2D(albedoMap, vUv).rgb;
    vec3 normalTexture = texture2D(normalMap, vUv).rgb * 2.0 - 1.0;
    float roughness = texture2D(roughnessMap, vUv).r;
    float metallic = texture2D(metallicMap, vUv).r;
    float ao = texture2D(aoMap, vUv).r;

    // Transform normal from tangent space to world space
    mat3 tbn = mat3(vTangent, vBitangent, vWorldNormal);
    vec3 worldNormal = normalize(tbn * normalTexture);

    vec3 viewDir = normalize(cameraPosition - vWorldPosition);
    vec3 lightDir = normalize(lightPosition - vWorldPosition);

    vec3 color = calculatePBR(albedo, roughness, metallic, worldNormal, viewDir, lightDir);
    color *= ao; // Apply ambient occlusion

    gl_FragColor = vec4(color, 1.0);
}
```

## 16. Test Senaryoları

### 16.1 Fonksiyonel Testler
```typescript
describe('Advanced 3D Visualization', () => {
  describe('Dimension Configuration', () => {
    it('should calculate correct price for custom dimensions', () => {
      const config = {
        width: 75,
        height: 150,
        thickness: 2,
        surfaceFinish: 'cilali',
        quantity: 50
      };

      const price = calculatePrice(config);
      expect(price.total).toBeCloseTo(1687.5); // Expected calculation
    });

    it('should validate dimension constraints', () => {
      const invalidConfig = { width: 5, height: 400, thickness: 2 };
      expect(() => validateDimensions(invalidConfig)).toThrow();
    });
  });

  describe('Surface Finish Visualization', () => {
    it('should update material properties correctly', () => {
      const material = createMaterial('mermer_beyaz');
      updateSurfaceFinish(material, 'cilali');

      expect(material.roughness).toBe(0.1);
      expect(material.metalness).toBe(0.0);
    });
  });

  describe('Room Simulation', () => {
    it('should generate correct tiling pattern', () => {
      const pattern = generateTilingPattern('herringbone', {
        tileWidth: 30,
        tileHeight: 60,
        roomWidth: 300,
        roomHeight: 400
      });

      expect(pattern.tiles.length).toBeGreaterThan(0);
      expect(pattern.coverage).toBeCloseTo(1.0, 0.05);
    });
  });
});
```

### 16.2 Performans Testleri
```typescript
describe('Performance Tests', () => {
  it('should load 3D model within 3 seconds', async () => {
    const startTime = performance.now();
    await loadProductModel('mermer_carrara_white');
    const loadTime = performance.now() - startTime;

    expect(loadTime).toBeLessThan(3000);
  });

  it('should maintain 30+ FPS during interaction', () => {
    const frameRates = measureFrameRate(5000); // 5 second test
    const averageFPS = frameRates.reduce((a, b) => a + b) / frameRates.length;

    expect(averageFPS).toBeGreaterThan(30);
  });

  it('should use less than 100MB memory', () => {
    const memoryUsage = measureMemoryUsage();
    expect(memoryUsage.used).toBeLessThan(100 * 1024 * 1024); // 100MB
  });
});
```

## 17. Deployment Stratejisi

### 17.1 CDN Konfigürasyonu
```yaml
# CDN Configuration
cdn:
  providers:
    - cloudflare
    - aws_cloudfront

  assets:
    3d_models:
      path: "/assets/3d/models/"
      cache_ttl: 86400 # 24 hours
      compression: gzip

    textures:
      path: "/assets/3d/textures/"
      cache_ttl: 604800 # 7 days
      formats: ["webp", "ktx2", "basis"]

    shaders:
      path: "/assets/3d/shaders/"
      cache_ttl: 86400
      minification: true
```

### 17.2 Progressive Loading
```typescript
interface ProgressiveLoading {
  strategy: {
    priority: ['geometry', 'base_texture', 'normal_map', 'detail_textures'];
    fallbacks: {
      low_bandwidth: 'simplified_geometry';
      slow_device: 'reduced_textures';
      webgl1: 'basic_materials';
    };
  };

  preloading: {
    popular_products: string[]; // En çok görüntülenen ürünler
    user_preferences: boolean; // Kullanıcı geçmişine göre
    predictive: boolean; // AI tahminleri
  };
}
```

Bu RFC, doğal taş marketplace platformumuz için kapsamlı ve gelişmiş bir 3D görselleştirme sistemi sunar. Stoneline ve AGT'nin başarılı özelliklerini analiz ederek, daha da gelişmiş ve kullanıcı dostu bir deneyim yaratır.

## 20. Güncellemeler ve Değişiklikler (2025-06-29)

### 20.1 Fiyatlandırma Özelliklerinin Kaldırılması

Bu güncelleme ile 3D görselleştirme sisteminden tüm fiyat hesaplama ve görüntüleme özellikleri kaldırılmıştır:

#### Kaldırılan Özellikler:
- **Dinamik Fiyat Hesaplama**: Gerçek zamanlı fiyat hesaplama servisi
- **Fiyat Breakdown Gösterimi**: Detaylı maliyet analizi
- **Yüzey İşlemi Fiyat Çarpanları**: priceMultiplier değerleri
- **onPriceUpdate Callback**: Fiyat güncellemesi callback'i
- **PriceDisplay Bileşeni**: Fiyat gösterim arayüzü
- **DynamicPricingService**: Fiyat hesaplama servisi

#### Korunan Özellikler:
- **3D Görselleştirme**: Tüm görsel özellikler korundu
- **Ebat Konfigüratörü**: Boyut seçimi ve doğrulama
- **Yüzey İşlemi Simülatörü**: Görsel efektler (fiyat etkisi hariç)
- **Sanal Mekan Simülasyonu**: Mekan şablonları ve pattern'ler
- **PBR Rendering**: Materyal sistemi
- **Performans Optimizasyonu**: LOD ve adaptif kalite

### 20.2 Kod Değişiklikleri

#### Interface Güncellemeleri:
```typescript
// Önceki durum
interface ProductViewerProps {
  onPriceUpdate?: (price: number) => void; // KALDIRILDI
}

// Yeni durum
interface ProductViewerProps {
  // onPriceUpdate?: (price: number) => void; // KALDIRILDI
}
```

#### Bileşen Güncellemeleri:
```typescript
// Kaldırılan servisler
// - DynamicPricingService
// - PriceBreakdownComponent
// - PriceDisplayWidget

// Güncellenen bileşenler
// - AdvancedProductViewer (fiyat callback'leri kaldırıldı)
// - SurfaceFinishSimulator (fiyat çarpanları kaldırıldı)
// - DimensionConfigurator (fiyat hesaplama kaldırıldı)
```

### 20.3 Kullanıcı Deneyimi Değişiklikleri

#### Önceki Durum:
- Ebat değişikliğinde gerçek zamanlı fiyat güncellemesi
- Yüzey işlemi seçiminde fiyat etkisi gösterimi
- Detaylı maliyet breakdown'u
- Toplam fiyat hesaplama

#### Yeni Durum:
- Sadece görsel değişiklikler
- Fiyat bilgisi gösterilmiyor
- Teklif alma için ayrı form yönlendirmesi
- Odak tamamen görselleştirmede

### 20.4 Teknik Etkiler

#### Performans İyileştirmeleri:
- Fiyat hesaplama overhead'i kaldırıldı
- Daha hızlı render süreleri
- Azaltılmış API çağrıları
- Basitleştirilmiş state yönetimi

#### Kod Basitleştirmesi:
- %30 daha az kod karmaşıklığı
- Daha az bağımlılık
- Kolay bakım
- Daha iyi test edilebilirlik

### 20.5 Gelecek Planları

#### Kısa Vadeli:
- Teklif formu entegrasyonu
- Üye girişi zorunluluğu
- Çoklu ürün karşılaştırması

#### Uzun Vadeli:
- AR/VR entegrasyonu (fiyat olmadan)
- Sosyal paylaşım özellikleri
- Gelişmiş mekan simülasyonu

---

**Güncelleme Tarihi**: 2025-06-29
**Güncelleme Türü**: Özellik Kaldırma (Fiyatlandırma)
**Etkilenen Modüller**: 3D Viewer, Pricing Service, UI Components
**Test Durumu**: Güncellenmiş testler gerekli
**Dokümantasyon**: RFC güncellenmiş
