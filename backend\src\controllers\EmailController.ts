import { Request, Response } from 'express';
import { EmailService } from '../services/EmailService';
import { createEmailConfig } from '../config/email';
import { asyncHandler } from '../middleware/errorHandler';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Validation schemas
const sendEmailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  cc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  bcc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  subject: z.string().min(1),
  html: z.string().optional(),
  text: z.string().optional(),
  templateName: z.string().optional(),
  templateData: z.any().optional(),
});

const sendBulkEmailSchema = z.object({
  recipients: z.array(z.object({
    email: z.string().email(),
    name: z.string().optional(),
    customData: z.any().optional(),
  })),
  templateName: z.string(),
  templateData: z.any(),
  subject: z.string(),
});

const testEmailSchema = z.object({
  to: z.string().email(),
  templateName: z.string().optional(),
});

export class EmailController {
  private emailService: EmailService;

  constructor() {
    const emailConfig = createEmailConfig();
    this.emailService = new EmailService(emailConfig);
  }

  /**
   * Send single email
   * @route POST /api/email/send
   * @access Private (Admin only)
   */
  sendEmail = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const emailData = sendEmailSchema.parse(req.body);

    await this.emailService.sendEmail(emailData);

    res.json({
      success: true,
      message: 'Email sent successfully'
    });
  });

  /**
   * Send bulk emails
   * @route POST /api/email/send-bulk
   * @access Private (Admin only)
   */
  sendBulkEmail = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const { recipients, templateName, templateData, subject } = sendBulkEmailSchema.parse(req.body);

    const results = [];
    
    for (const recipient of recipients) {
      try {
        const personalizedData = {
          ...templateData,
          ...recipient.customData,
          recipientName: recipient.name,
          recipientEmail: recipient.email,
        };

        await this.emailService.sendEmail({
          to: recipient.email,
          subject,
          templateName,
          templateData: personalizedData,
        });

        results.push({
          email: recipient.email,
          status: 'sent',
        });
      } catch (error) {
        results.push({
          email: recipient.email,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    const successCount = results.filter(r => r.status === 'sent').length;
    const failureCount = results.filter(r => r.status === 'failed').length;

    res.json({
      success: true,
      data: {
        totalSent: successCount,
        totalFailed: failureCount,
        results,
      },
      message: `Bulk email completed: ${successCount} sent, ${failureCount} failed`
    });
  });

  /**
   * Test email configuration
   * @route POST /api/email/test
   * @access Private (Admin only)
   */
  testEmail = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const { to, templateName } = testEmailSchema.parse(req.body);

    try {
      if (templateName) {
        // Test with template
        await this.emailService.sendEmail({
          to,
          subject: 'Email Service Test',
          templateName,
          templateData: {
            customerName: 'Test User',
            orderNumber: 'TEST-001',
            amount: 1000,
            currency: 'TRY',
            productName: 'Test Product',
            producerName: 'Test Producer',
            referenceCode: 'TEST123',
            bankInfo: {
              bankName: 'Test Bank',
              iban: '**************************',
              accountHolder: 'Test Account',
            },
          },
        });
      } else {
        // Test basic email
        await this.emailService.sendEmail({
          to,
          subject: 'Email Service Test',
          html: '<h1>Email Service Test</h1><p>This is a test email to verify email service configuration.</p>',
          text: 'Email Service Test - This is a test email to verify email service configuration.',
        });
      }

      res.json({
        success: true,
        message: 'Test email sent successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to send test email',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * Get email templates list
   * @route GET /api/email/templates
   * @access Private (Admin only)
   */
  getTemplates = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const templates = [
      {
        name: 'payment-instructions',
        description: 'Ödeme talimatları emaili',
        category: 'escrow',
        variables: ['customerName', 'orderNumber', 'amount', 'currency', 'referenceCode', 'bankInfo', 'productName'],
      },
      {
        name: 'payment-confirmation',
        description: 'Ödeme onay emaili',
        category: 'escrow',
        variables: ['customerName', 'orderNumber', 'amount', 'currency', 'productName'],
      },
      {
        name: 'production-start',
        description: 'Üretime başlama bildirimi',
        category: 'escrow',
        variables: ['producerName', 'orderNumber', 'amount', 'currency', 'productName'],
      },
      {
        name: 'goods-ready',
        description: 'Ürün hazır bildirimi',
        category: 'escrow',
        variables: ['customerName', 'orderNumber', 'productName', 'producerName'],
      },
      {
        name: 'payment-release',
        description: 'Ödeme yapıldı bildirimi',
        category: 'escrow',
        variables: ['producerName', 'orderNumber', 'amount', 'currency', 'productName'],
      },
      {
        name: 'dispute-notification',
        description: 'Anlaşmazlık bildirimi',
        category: 'escrow',
        variables: ['customerName', 'producerName', 'orderNumber', 'reason', 'productName'],
      },
      {
        name: 'welcome',
        description: 'Hoş geldin emaili',
        category: 'auth',
        variables: ['name', 'userType'],
      },
      {
        name: 'password-reset',
        description: 'Şifre sıfırlama emaili',
        category: 'auth',
        variables: ['name', 'resetLink'],
      },
    ];

    res.json({
      success: true,
      data: { templates }
    });
  });

  /**
   * Get email statistics
   * @route GET /api/email/stats
   * @access Private (Admin only)
   */
  getEmailStats = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    // This would typically come from email service provider's analytics
    // For now, we'll return mock data
    const stats = {
      totalSent: 1250,
      totalDelivered: 1180,
      totalOpened: 890,
      totalClicked: 340,
      deliveryRate: 94.4,
      openRate: 75.4,
      clickRate: 38.2,
      bounceRate: 5.6,
      recentActivity: [
        { date: '2024-01-10', sent: 45, delivered: 43, opened: 32 },
        { date: '2024-01-09', sent: 38, delivered: 36, opened: 28 },
        { date: '2024-01-08', sent: 52, delivered: 49, opened: 35 },
      ],
      topTemplates: [
        { name: 'payment-instructions', sent: 320, openRate: 85.2 },
        { name: 'payment-confirmation', sent: 280, openRate: 78.9 },
        { name: 'goods-ready', sent: 245, openRate: 82.1 },
      ],
    };

    res.json({
      success: true,
      data: stats
    });
  });

  /**
   * Send escrow notification emails
   * @route POST /api/email/escrow-notification
   * @access Private (System only)
   */
  sendEscrowNotification = asyncHandler(async (req: Request, res: Response) => {
    const { type, escrowId, recipientEmail, data } = req.body;

    if (!type || !escrowId || !recipientEmail) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    try {
      switch (type) {
        case 'payment-instructions':
          await this.emailService.sendPaymentInstructions(recipientEmail, data);
          break;
        case 'payment-confirmation':
          await this.emailService.sendPaymentConfirmation(recipientEmail, data);
          break;
        case 'production-start':
          await this.emailService.sendProductionStartNotification(recipientEmail, data);
          break;
        case 'goods-ready':
          await this.emailService.sendGoodsReadyNotification(recipientEmail, data);
          break;
        case 'payment-release':
          await this.emailService.sendPaymentReleaseNotification(recipientEmail, data);
          break;
        case 'dispute':
          await this.emailService.sendDisputeNotification(recipientEmail, data);
          break;
        default:
          return res.status(400).json({
            success: false,
            error: 'Invalid notification type'
          });
      }

      res.json({
        success: true,
        message: 'Escrow notification sent successfully'
      });
    } catch (error) {
      console.error('Escrow notification error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to send escrow notification'
      });
    }
  });
}
