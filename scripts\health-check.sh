#!/bin/bash

# Health Check Script for AI Marketing System
# This script performs comprehensive health checks for all system components

set -e

# Configuration
BACKEND_URL=${BACKEND_URL:-http://localhost:3001}
FRONTEND_URL=${FRONTEND_URL:-http://localhost:3000}
TIMEOUT=${HEALTH_CHECK_TIMEOUT:-10}
VERBOSE=${VERBOSE:-false}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    if [ "$VERBOSE" = "true" ]; then
        echo -e "${BLUE}ℹ️  $1${NC}"
    fi
}

# Function to check HTTP endpoint
check_http_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    print_info "Checking $description at $url"
    
    local response
    local status_code
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" --max-time "$TIMEOUT" "$url" 2>/dev/null || echo "HTTPSTATUS:000")
    status_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    
    if [ "$status_code" = "$expected_status" ]; then
        print_success "$description is healthy (HTTP $status_code)"
        return 0
    else
        print_error "$description is unhealthy (HTTP $status_code)"
        return 1
    fi
}

# Function to check JSON API endpoint
check_json_endpoint() {
    local url=$1
    local description=$2
    local required_field=$3
    
    print_info "Checking $description JSON response at $url"
    
    local response
    response=$(curl -s --max-time "$TIMEOUT" "$url" 2>/dev/null || echo "{}")
    
    if echo "$response" | jq -e ".$required_field" > /dev/null 2>&1; then
        print_success "$description JSON response is valid"
        return 0
    else
        print_error "$description JSON response is invalid or missing field: $required_field"
        return 1
    fi
}

# Function to check database connectivity
check_database() {
    print_info "Checking database connectivity"
    
    if check_http_endpoint "$BACKEND_URL/health/db" "Database Connection"; then
        return 0
    else
        return 1
    fi
}

# Function to check Redis connectivity
check_redis() {
    print_info "Checking Redis connectivity"
    
    if check_http_endpoint "$BACKEND_URL/health/redis" "Redis Connection"; then
        return 0
    else
        return 1
    fi
}

# Function to check AI Marketing system
check_ai_marketing() {
    print_info "Checking AI Marketing system"
    
    # Check system stats endpoint
    if check_json_endpoint "$BACKEND_URL/api/admin/ai-marketing/stats" "AI Marketing Stats" "systemHealth"; then
        # Check if AI Marketing is actually running
        local ai_health
        ai_health=$(curl -s --max-time "$TIMEOUT" "$BACKEND_URL/api/admin/ai-marketing/system/health" 2>/dev/null || echo "{}")
        
        if echo "$ai_health" | jq -e '.health.overall' > /dev/null 2>&1; then
            local overall_health
            overall_health=$(echo "$ai_health" | jq -r '.health.overall')
            
            if [ "$overall_health" = "true" ]; then
                print_success "AI Marketing system is healthy"
                return 0
            else
                print_warning "AI Marketing system reports unhealthy status"
                return 1
            fi
        else
            print_error "AI Marketing system health check failed"
            return 1
        fi
    else
        return 1
    fi
}

# Function to check external API integrations
check_external_apis() {
    print_info "Checking external API integrations"
    
    local api_health
    api_health=$(curl -s --max-time "$TIMEOUT" "$BACKEND_URL/api/admin/ai-marketing/system/health" 2>/dev/null || echo "{}")
    
    if echo "$api_health" | jq -e '.health' > /dev/null 2>&1; then
        local trademap_health
        local linkedin_health
        local googleads_health
        
        trademap_health=$(echo "$api_health" | jq -r '.health.tradeMap // false')
        linkedin_health=$(echo "$api_health" | jq -r '.health.linkedIn // false')
        googleads_health=$(echo "$api_health" | jq -r '.health.googleAds // false')
        
        local api_issues=0
        
        if [ "$trademap_health" = "true" ]; then
            print_success "Trade Map API is healthy"
        else
            print_warning "Trade Map API is unhealthy"
            api_issues=$((api_issues + 1))
        fi
        
        if [ "$linkedin_health" = "true" ]; then
            print_success "LinkedIn API is healthy"
        else
            print_warning "LinkedIn API is unhealthy"
            api_issues=$((api_issues + 1))
        fi
        
        if [ "$googleads_health" = "true" ]; then
            print_success "Google Ads API is healthy"
        else
            print_warning "Google Ads API is unhealthy"
            api_issues=$((api_issues + 1))
        fi
        
        if [ $api_issues -eq 0 ]; then
            print_success "All external APIs are healthy"
            return 0
        elif [ $api_issues -lt 3 ]; then
            print_warning "Some external APIs are unhealthy ($api_issues/3)"
            return 1
        else
            print_error "All external APIs are unhealthy"
            return 1
        fi
    else
        print_error "Unable to check external API health"
        return 1
    fi
}

# Function to check system resources
check_system_resources() {
    print_info "Checking system resources"
    
    # Check memory usage
    if command -v free > /dev/null 2>&1; then
        local memory_usage
        memory_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
        
        if [ "$memory_usage" -lt 90 ]; then
            print_success "Memory usage is acceptable ($memory_usage%)"
        else
            print_warning "High memory usage ($memory_usage%)"
        fi
    fi
    
    # Check disk usage
    if command -v df > /dev/null 2>&1; then
        local disk_usage
        disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
        
        if [ "$disk_usage" -lt 90 ]; then
            print_success "Disk usage is acceptable ($disk_usage%)"
        else
            print_warning "High disk usage ($disk_usage%)"
        fi
    fi
    
    return 0
}

# Main health check function
main() {
    local exit_code=0
    local checks_passed=0
    local total_checks=0
    
    echo "🏥 AI Marketing System Health Check"
    echo "=================================="
    echo "Timestamp: $(date)"
    echo "Backend URL: $BACKEND_URL"
    echo "Frontend URL: $FRONTEND_URL"
    echo ""
    
    # Core application checks
    echo "🔍 Core Application Checks:"
    
    total_checks=$((total_checks + 1))
    if check_http_endpoint "$BACKEND_URL/health" "Backend Health"; then
        checks_passed=$((checks_passed + 1))
    else
        exit_code=1
    fi
    
    total_checks=$((total_checks + 1))
    if check_http_endpoint "$FRONTEND_URL/api/health" "Frontend Health"; then
        checks_passed=$((checks_passed + 1))
    else
        exit_code=1
    fi
    
    # Database and cache checks
    echo ""
    echo "🗄️ Database and Cache Checks:"
    
    total_checks=$((total_checks + 1))
    if check_database; then
        checks_passed=$((checks_passed + 1))
    else
        exit_code=1
    fi
    
    total_checks=$((total_checks + 1))
    if check_redis; then
        checks_passed=$((checks_passed + 1))
    else
        exit_code=1
    fi
    
    # AI Marketing system checks
    echo ""
    echo "🧠 AI Marketing System Checks:"
    
    total_checks=$((total_checks + 1))
    if check_ai_marketing; then
        checks_passed=$((checks_passed + 1))
    else
        exit_code=1
    fi
    
    total_checks=$((total_checks + 1))
    if check_external_apis; then
        checks_passed=$((checks_passed + 1))
    else
        # External API failures are warnings, not critical
        checks_passed=$((checks_passed + 1))
    fi
    
    # System resource checks
    echo ""
    echo "💻 System Resource Checks:"
    
    total_checks=$((total_checks + 1))
    if check_system_resources; then
        checks_passed=$((checks_passed + 1))
    else
        # Resource warnings are not critical
        checks_passed=$((checks_passed + 1))
    fi
    
    # Summary
    echo ""
    echo "📊 Health Check Summary:"
    echo "========================"
    echo "Checks passed: $checks_passed/$total_checks"
    
    if [ $exit_code -eq 0 ]; then
        print_success "Overall system health: HEALTHY"
    else
        print_error "Overall system health: UNHEALTHY"
    fi
    
    echo "Completed at: $(date)"
    
    exit $exit_code
}

# Handle script arguments
case "${1:-}" in
    --verbose|-v)
        VERBOSE=true
        main
        ;;
    --help|-h)
        echo "AI Marketing System Health Check Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --verbose, -v    Enable verbose output"
        echo "  --help, -h       Show this help message"
        echo ""
        echo "Environment Variables:"
        echo "  BACKEND_URL              Backend URL (default: http://localhost:3001)"
        echo "  FRONTEND_URL             Frontend URL (default: http://localhost:3000)"
        echo "  HEALTH_CHECK_TIMEOUT     Request timeout in seconds (default: 10)"
        echo "  VERBOSE                  Enable verbose output (default: false)"
        ;;
    *)
        main
        ;;
esac
