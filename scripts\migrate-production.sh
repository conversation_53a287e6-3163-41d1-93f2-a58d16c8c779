#!/bin/bash

# Natural Stone Marketplace - Production Database Migration Script
# This script safely migrates the database to production

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
DB_NAME="natural_stone_marketplace_prod"
DB_USER="production_user"
BACKUP_DIR="/var/backups/natural-stone-marketplace/db"
MIGRATION_LOG="/var/log/natural-stone-marketplace/migration.log"

echo -e "${BLUE}🗄️  Starting database migration for production${NC}"

# Create backup directory
mkdir -p $BACKUP_DIR
mkdir -p $(dirname $MIGRATION_LOG)

# Function to log messages
log_message() {
    echo "$(date): $1" >> $MIGRATION_LOG
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo "$(date): WARNING: $1" >> $MIGRATION_LOG
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo "$(date): ERROR: $1" >> $MIGRATION_LOG
    echo -e "${RED}❌ $1${NC}"
}

# Check if database exists
if mysql -u root -p -e "USE $DB_NAME;" 2>/dev/null; then
    log_warning "Database $DB_NAME already exists"
    
    # Create backup before migration
    BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"
    log_message "Creating database backup: $BACKUP_FILE"
    mysqldump -u root -p $DB_NAME > $BACKUP_FILE
    log_message "Database backup completed"
else
    log_message "Creating new database: $DB_NAME"
    mysql -u root -p -e "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    # Create production user
    log_message "Creating database user: $DB_USER"
    mysql -u root -p -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY 'STRONG_PRODUCTION_PASSWORD_HERE';"
    mysql -u root -p -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';"
    mysql -u root -p -e "FLUSH PRIVILEGES;"
fi

# Navigate to backend directory
cd /var/www/natural-stone-marketplace/backend

# Install Prisma CLI if not installed
if ! command -v prisma &> /dev/null; then
    log_message "Installing Prisma CLI..."
    npm install -g prisma
fi

# Generate Prisma client
log_message "Generating Prisma client..."
npx prisma generate

# Run database migrations
log_message "Running Prisma migrations..."
npx prisma migrate deploy

# Seed database with initial data
if [ -f "prisma/seed.ts" ]; then
    log_message "Seeding database with initial data..."
    npx prisma db seed
else
    log_warning "No seed file found, skipping database seeding"
fi

# Run AI Marketing System migrations
log_message "Running AI Marketing System migrations..."
if [ -f "prisma/migrations/20241211_ai_marketing_system.sql" ]; then
    mysql -u $DB_USER -p $DB_NAME < prisma/migrations/20241211_ai_marketing_system.sql
    log_message "AI Marketing System tables created"
else
    log_warning "AI Marketing migration file not found"
fi

# Create indexes for performance
log_message "Creating performance indexes..."
mysql -u $DB_USER -p $DB_NAME << EOF
-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_producer ON products(producerId);
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_quotes_status ON quotes(status);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_customer ON orders(customerId);
CREATE INDEX IF NOT EXISTS idx_orders_producer ON orders(producerId);
CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications(userId);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(isRead);

-- AI Marketing indexes
CREATE INDEX IF NOT EXISTS idx_ai_leads_campaign ON ai_leads(campaign_id);
CREATE INDEX IF NOT EXISTS idx_ai_leads_confidence ON ai_leads(ai_confidence_score);
CREATE INDEX IF NOT EXISTS idx_ai_leads_country ON ai_leads(country);
CREATE INDEX IF NOT EXISTS idx_ai_market_analysis_country ON ai_market_analysis(country);
CREATE INDEX IF NOT EXISTS idx_ai_market_analysis_opportunity ON ai_market_analysis(opportunity_score);
EOF

log_message "Performance indexes created"

# Verify migration
log_message "Verifying database migration..."
TABLES=$(mysql -u $DB_USER -p $DB_NAME -e "SHOW TABLES;" | wc -l)
if [ $TABLES -gt 10 ]; then
    log_message "Database migration verified successfully ($TABLES tables)"
else
    log_error "Database migration verification failed (only $TABLES tables found)"
    exit 1
fi

# Set proper permissions
log_message "Setting database permissions..."
mysql -u root -p -e "GRANT SELECT, INSERT, UPDATE, DELETE ON $DB_NAME.* TO '$DB_USER'@'localhost';"
mysql -u root -p -e "FLUSH PRIVILEGES;"

# Create database maintenance script
log_message "Creating database maintenance script..."
cat > /var/www/natural-stone-marketplace/scripts/db-maintenance.sh << 'EOF'
#!/bin/bash
# Database maintenance script
# Run daily via cron

DB_NAME="natural_stone_marketplace_prod"
BACKUP_DIR="/var/backups/natural-stone-marketplace/db"

# Create daily backup
BACKUP_FILE="$BACKUP_DIR/daily_backup_$(date +%Y%m%d).sql"
mysqldump -u production_user -p $DB_NAME > $BACKUP_FILE

# Compress backup
gzip $BACKUP_FILE

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

# Optimize tables
mysql -u production_user -p $DB_NAME -e "OPTIMIZE TABLE products, orders, quotes, users, notifications;"

# Update statistics
mysql -u production_user -p $DB_NAME -e "ANALYZE TABLE products, orders, quotes, users, notifications;"

echo "$(date): Database maintenance completed" >> /var/log/natural-stone-marketplace/maintenance.log
EOF

chmod +x /var/www/natural-stone-marketplace/scripts/db-maintenance.sh

# Add to crontab
(crontab -l 2>/dev/null; echo "0 3 * * * /var/www/natural-stone-marketplace/scripts/db-maintenance.sh") | crontab -

log_message "Database maintenance script created and scheduled"

# Final verification
log_message "Running final database verification..."
mysql -u $DB_USER -p $DB_NAME -e "SELECT COUNT(*) as user_count FROM users;" > /dev/null
mysql -u $DB_USER -p $DB_NAME -e "SELECT COUNT(*) as product_count FROM products;" > /dev/null

echo -e "${GREEN}"
echo "=================================================="
echo "🎉 DATABASE MIGRATION COMPLETED SUCCESSFULLY!"
echo "=================================================="
echo "Database: $DB_NAME"
echo "User: $DB_USER"
echo "Backup location: $BACKUP_DIR"
echo "Log file: $MIGRATION_LOG"
echo "=================================================="
echo -e "${NC}"

log_message "Database migration completed successfully"
