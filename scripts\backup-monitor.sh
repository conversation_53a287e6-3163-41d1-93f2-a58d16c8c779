#!/bin/bash

# Backup Monitoring Script
# Türkiye Doğal <PERSON>eri - Backup Health Monitor

set -e

# Configuration
BACKUP_DIR="./backups"
LOG_FILE="./logs/backup-monitor-$(date +%Y%m%d).log"
ALERT_EMAIL=${ALERT_EMAIL:-"<EMAIL>"}
BACKUP_RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}
MIN_BACKUP_SIZE=${MIN_BACKUP_SIZE:-1048576}  # 1MB minimum

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Create logs directory
mkdir -p "$(dirname "$LOG_FILE")"

log_info "=== BACKUP MONITORING STARTED ==="

# Function to check backup directory
check_backup_directory() {
    log_info "Checking backup directory..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_error "Backup directory does not exist: $BACKUP_DIR"
        return 1
    fi
    
    # Check directory permissions
    if [[ ! -w "$BACKUP_DIR" ]]; then
        log_error "Backup directory is not writable: $BACKUP_DIR"
        return 1
    fi
    
    # Check available space
    local available_space=$(df "$BACKUP_DIR" | awk 'NR==2 {print $4}')
    local available_gb=$((available_space / 1024 / 1024))
    
    if [[ $available_gb -lt 5 ]]; then
        log_warning "Low disk space in backup directory: ${available_gb}GB available"
    else
        log_success "Backup directory check passed: ${available_gb}GB available"
    fi
}

# Function to check recent backups
check_recent_backups() {
    log_info "Checking recent backups..."
    
    local today=$(date +%Y%m%d)
    local yesterday=$(date -d "yesterday" +%Y%m%d 2>/dev/null || date -v-1d +%Y%m%d)
    
    # Check for today's database backup
    local today_db_backup=$(find "$BACKUP_DIR" -name "*$today*.sql.gz" | head -1)
    if [[ -n "$today_db_backup" ]]; then
        log_success "Today's database backup found: $(basename "$today_db_backup")"
    else
        # Check for yesterday's backup
        local yesterday_db_backup=$(find "$BACKUP_DIR" -name "*$yesterday*.sql.gz" | head -1)
        if [[ -n "$yesterday_db_backup" ]]; then
            log_warning "No today's database backup, but yesterday's backup exists"
        else
            log_error "No recent database backup found"
            return 1
        fi
    fi
    
    # Check for code backups
    local recent_code_backup=$(find "$BACKUP_DIR" -name "code-backup-*.tar.gz" -mtime -2 | head -1)
    if [[ -n "$recent_code_backup" ]]; then
        log_success "Recent code backup found: $(basename "$recent_code_backup")"
    else
        log_warning "No recent code backup found"
    fi
}

# Function to verify backup integrity
verify_backup_integrity() {
    log_info "Verifying backup integrity..."
    
    local corrupted_count=0
    local total_count=0
    
    # Check database backups
    while IFS= read -r -d '' backup_file; do
        ((total_count++))
        
        if [[ -f "$backup_file" ]]; then
            # Check file size
            local file_size=$(stat -f%z "$backup_file" 2>/dev/null || stat -c%s "$backup_file" 2>/dev/null)
            if [[ $file_size -lt $MIN_BACKUP_SIZE ]]; then
                log_error "Backup file too small: $(basename "$backup_file") (${file_size} bytes)"
                ((corrupted_count++))
                continue
            fi
            
            # Check gzip integrity
            if gzip -t "$backup_file" 2>/dev/null; then
                log_success "Backup integrity verified: $(basename "$backup_file")"
            else
                log_error "Corrupted backup detected: $(basename "$backup_file")"
                ((corrupted_count++))
            fi
        fi
    done < <(find "$BACKUP_DIR" -name "*.sql.gz" -print0)
    
    if [[ $corrupted_count -eq 0 ]]; then
        log_success "All $total_count backups passed integrity check"
    else
        log_error "$corrupted_count out of $total_count backups are corrupted"
        return 1
    fi
}

# Function to check backup age and cleanup
check_backup_age() {
    log_info "Checking backup age and cleanup..."
    
    local old_backups_count=0
    local cleaned_count=0
    
    # Find old backups
    while IFS= read -r -d '' old_backup; do
        ((old_backups_count++))
        
        if rm "$old_backup"; then
            log_info "Removed old backup: $(basename "$old_backup")"
            ((cleaned_count++))
        else
            log_error "Failed to remove old backup: $(basename "$old_backup")"
        fi
    done < <(find "$BACKUP_DIR" -name "*.sql.gz" -o -name "*.tar.gz" -mtime +$BACKUP_RETENTION_DAYS -print0)
    
    if [[ $old_backups_count -eq 0 ]]; then
        log_success "No old backups to clean up"
    else
        log_success "Cleaned up $cleaned_count out of $old_backups_count old backups"
    fi
}

# Function to check S3 backup sync
check_s3_backup() {
    log_info "Checking S3 backup sync..."
    
    if [[ -z "$BACKUP_S3_BUCKET" ]]; then
        log_warning "S3 backup not configured"
        return 0
    fi
    
    # Check if AWS CLI is available
    if ! command -v aws &> /dev/null; then
        log_warning "AWS CLI not available, skipping S3 check"
        return 0
    fi
    
    # List recent S3 backups
    local s3_backups=$(aws s3 ls "s3://$BACKUP_S3_BUCKET/database-backups/" --recursive | tail -5)
    if [[ -n "$s3_backups" ]]; then
        log_success "S3 backups found:"
        echo "$s3_backups" | while read -r line; do
            log_info "  $line"
        done
    else
        log_warning "No S3 backups found"
    fi
}

# Function to test backup restoration
test_backup_restoration() {
    log_info "Testing backup restoration (dry run)..."
    
    # Find latest backup
    local latest_backup=$(find "$BACKUP_DIR" -name "backup-*.sql.gz" | sort -r | head -1)
    
    if [[ -z "$latest_backup" ]]; then
        log_error "No backup found for restoration test"
        return 1
    fi
    
    # Test decompression
    if gzip -t "$latest_backup"; then
        log_success "Backup decompression test passed"
    else
        log_error "Backup decompression test failed"
        return 1
    fi
    
    # Test SQL syntax (basic check)
    local temp_sql="/tmp/backup_test.sql"
    if gunzip -c "$latest_backup" > "$temp_sql"; then
        # Check for basic SQL structure
        if grep -q "CREATE TABLE\|INSERT INTO\|COPY" "$temp_sql"; then
            log_success "Backup SQL structure test passed"
        else
            log_warning "Backup SQL structure test failed - no tables or data found"
        fi
        rm -f "$temp_sql"
    else
        log_error "Failed to extract backup for testing"
        return 1
    fi
}

# Function to generate backup report
generate_backup_report() {
    log_info "Generating backup report..."
    
    local report_file="./logs/backup-report-$(date +%Y%m%d).html"
    local backup_count=$(find "$BACKUP_DIR" -name "*.sql.gz" | wc -l)
    local total_size=$(du -sh "$BACKUP_DIR" | cut -f1)
    local latest_backup=$(find "$BACKUP_DIR" -name "backup-*.sql.gz" | sort -r | head -1)
    local latest_backup_date=""
    
    if [[ -n "$latest_backup" ]]; then
        latest_backup_date=$(stat -f%Sm -t%Y-%m-%d "$latest_backup" 2>/dev/null || stat -c%y "$latest_backup" | cut -d' ' -f1)
    fi
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Backup Health Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Backup Health Report</h1>
        <p><strong>Generated:</strong> $(date)</p>
        <p><strong>Backup Directory:</strong> $BACKUP_DIR</p>
    </div>
    
    <div class="section">
        <h2>Backup Statistics</h2>
        <div class="metric">
            <strong>Total Backups:</strong> $backup_count
        </div>
        <div class="metric">
            <strong>Total Size:</strong> $total_size
        </div>
        <div class="metric">
            <strong>Latest Backup:</strong> $latest_backup_date
        </div>
        <div class="metric">
            <strong>Retention Period:</strong> $BACKUP_RETENTION_DAYS days
        </div>
    </div>
    
    <div class="section">
        <h2>Recent Backups</h2>
        <ul>
EOF
    
    # Add recent backups to report
    find "$BACKUP_DIR" -name "backup-*.sql.gz" | sort -r | head -5 | while read -r backup; do
        local backup_name=$(basename "$backup")
        local backup_size=$(du -h "$backup" | cut -f1)
        local backup_date=$(stat -f%Sm -t%Y-%m-%d "$backup" 2>/dev/null || stat -c%y "$backup" | cut -d' ' -f1)
        echo "            <li>$backup_name ($backup_size) - $backup_date</li>" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF
        </ul>
    </div>
    
    <div class="section">
        <h2>Health Status</h2>
        <p>See log file for detailed health check results: $LOG_FILE</p>
    </div>
</body>
</html>
EOF
    
    log_success "Backup report generated: $report_file"
}

# Function to send alerts
send_alert() {
    local alert_type=$1
    local message=$2
    
    log_info "Sending $alert_type alert: $message"
    
    # Create alert file for external processing
    local alert_file="./logs/backup-alert-$(date +%Y%m%d-%H%M%S).json"
    cat > "$alert_file" << EOF
{
    "type": "$alert_type",
    "message": "$message",
    "timestamp": "$(date -Iseconds)",
    "service": "backup-monitor",
    "severity": "$(if [[ "$alert_type" == "error" ]]; then echo "high"; else echo "medium"; fi)"
}
EOF
    
    # If email is configured, send email alert
    if command -v mail &> /dev/null && [[ -n "$ALERT_EMAIL" ]]; then
        echo "$message" | mail -s "Backup Alert - $alert_type" "$ALERT_EMAIL"
        log_success "Email alert sent to $ALERT_EMAIL"
    fi
}

# Main monitoring function
main() {
    local exit_code=0
    local alerts=()
    
    # Run all checks
    if ! check_backup_directory; then
        alerts+=("Backup directory check failed")
        exit_code=1
    fi
    
    if ! check_recent_backups; then
        alerts+=("Recent backup check failed")
        exit_code=1
    fi
    
    if ! verify_backup_integrity; then
        alerts+=("Backup integrity check failed")
        exit_code=1
    fi
    
    check_backup_age
    check_s3_backup
    
    if ! test_backup_restoration; then
        alerts+=("Backup restoration test failed")
        exit_code=1
    fi
    
    # Generate report
    generate_backup_report
    
    # Send alerts if any issues found
    if [[ ${#alerts[@]} -gt 0 ]]; then
        for alert in "${alerts[@]}"; do
            send_alert "error" "$alert"
        done
        log_error "Backup monitoring completed with ${#alerts[@]} issues"
    else
        log_success "Backup monitoring completed successfully - all checks passed"
    fi
    
    log_info "=== BACKUP MONITORING COMPLETED ==="
    
    exit $exit_code
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0"
        echo ""
        echo "Environment variables:"
        echo "  BACKUP_DIR              Backup directory path [default: ./backups]"
        echo "  ALERT_EMAIL             Email for alerts [default: <EMAIL>]"
        echo "  BACKUP_RETENTION_DAYS   Backup retention period [default: 30]"
        echo "  MIN_BACKUP_SIZE         Minimum backup size in bytes [default: 1048576]"
        echo "  BACKUP_S3_BUCKET        S3 bucket for backup sync [optional]"
        exit 0
        ;;
esac

# Run main function
main "$@"
