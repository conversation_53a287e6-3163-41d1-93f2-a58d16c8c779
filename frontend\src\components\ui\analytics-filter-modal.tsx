'use client'

import * as React from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { 
  Filter, 
  Calendar,
  DollarSign,
  Package,
  Users,
  TrendingUp,
  BarChart3,
  CheckCircle,
  X
} from 'lucide-react'

interface AnalyticsFilterModalProps {
  isOpen: boolean
  onClose: () => void
  onApplyFilters: (filters: any) => Promise<boolean>
  currentFilters: any
}

export function AnalyticsFilterModal({
  isOpen,
  onClose,
  onApplyFilters,
  currentFilters
}: AnalyticsFilterModalProps) {
  const [filters, setFilters] = React.useState({
    dateRange: 'custom',
    startDate: '',
    endDate: '',
    metrics: ['revenue', 'orders', 'customers'],
    productCategories: [],
    customerSegments: [],
    minOrderValue: '',
    maxOrderValue: '',
    orderStatus: 'all',
    paymentStatus: 'all',
    region: 'all',
    comparisonPeriod: 'none'
  })
  const [isLoading, setIsLoading] = React.useState(false)

  React.useEffect(() => {
    if (isOpen) {
      // Set default dates
      const endDate = new Date()
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - 3)
      
      setFilters(prev => ({
        ...prev,
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        ...currentFilters
      }))
    }
  }, [isOpen, currentFilters])

  const handleApplyFilters = async () => {
    setIsLoading(true)
    try {
      const success = await onApplyFilters(filters)
      if (success) {
        onClose()
      }
    } catch (error) {
      console.error('Error applying filters:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleResetFilters = () => {
    const endDate = new Date()
    const startDate = new Date()
    startDate.setMonth(startDate.getMonth() - 3)
    
    setFilters({
      dateRange: 'custom',
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      metrics: ['revenue', 'orders', 'customers'],
      productCategories: [],
      customerSegments: [],
      minOrderValue: '',
      maxOrderValue: '',
      orderStatus: 'all',
      paymentStatus: 'all',
      region: 'all',
      comparisonPeriod: 'none'
    })
  }

  const handleMetricToggle = (metric: string) => {
    setFilters(prev => ({
      ...prev,
      metrics: prev.metrics.includes(metric)
        ? prev.metrics.filter(m => m !== metric)
        : [...prev.metrics, metric]
    }))
  }

  const handleCategoryToggle = (category: string) => {
    setFilters(prev => ({
      ...prev,
      productCategories: prev.productCategories.includes(category)
        ? prev.productCategories.filter(c => c !== category)
        : [...prev.productCategories, category]
    }))
  }

  const handleSegmentToggle = (segment: string) => {
    setFilters(prev => ({
      ...prev,
      customerSegments: prev.customerSegments.includes(segment)
        ? prev.customerSegments.filter(s => s !== segment)
        : [...prev.customerSegments, segment]
    }))
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.productCategories.length > 0) count++
    if (filters.customerSegments.length > 0) count++
    if (filters.minOrderValue || filters.maxOrderValue) count++
    if (filters.orderStatus !== 'all') count++
    if (filters.paymentStatus !== 'all') count++
    if (filters.region !== 'all') count++
    if (filters.comparisonPeriod !== 'none') count++
    return count
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Gelişmiş Analiz Filtreleri
            {getActiveFiltersCount() > 0 && (
              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                {getActiveFiltersCount()} aktif filtre
              </span>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Date Range */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Tarih Aralığı
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="dateRange">Dönem Seçimi</Label>
                <Select 
                  value={filters.dateRange} 
                  onValueChange={(value) => setFilters(prev => ({ ...prev, dateRange: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1week">Son 1 Hafta</SelectItem>
                    <SelectItem value="1month">Son 1 Ay</SelectItem>
                    <SelectItem value="3months">Son 3 Ay</SelectItem>
                    <SelectItem value="6months">Son 6 Ay</SelectItem>
                    <SelectItem value="1year">Son 1 Yıl</SelectItem>
                    <SelectItem value="custom">Özel Tarih Aralığı</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {filters.dateRange === 'custom' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startDate">Başlangıç Tarihi</Label>
                    <input
                      id="startDate"
                      type="date"
                      value={filters.startDate}
                      onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
                      className="w-full p-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <Label htmlFor="endDate">Bitiş Tarihi</Label>
                    <input
                      id="endDate"
                      type="date"
                      value={filters.endDate}
                      onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
                      className="w-full p-2 border rounded-md"
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Metrics Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Gösterilecek Metrikler
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[
                  { id: 'revenue', label: 'Gelir', icon: DollarSign },
                  { id: 'orders', label: 'Siparişler', icon: Package },
                  { id: 'customers', label: 'Müşteriler', icon: Users },
                  { id: 'growth', label: 'Büyüme', icon: TrendingUp }
                ].map((metric) => (
                  <div key={metric.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={metric.id}
                      checked={filters.metrics.includes(metric.id)}
                      onCheckedChange={() => handleMetricToggle(metric.id)}
                    />
                    <Label htmlFor={metric.id} className="flex items-center gap-2">
                      <metric.icon className="w-4 h-4" />
                      {metric.label}
                    </Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Product Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Ürün Kategorileri</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[
                  'Mermer', 'Traverten', 'Granit', 'Oniks', 
                  'Kireçtaşı', 'Bazalt', 'Andezit', 'Plaka'
                ].map((category) => (
                  <div key={category} className="flex items-center space-x-2">
                    <Checkbox
                      id={category}
                      checked={filters.productCategories.includes(category)}
                      onCheckedChange={() => handleCategoryToggle(category)}
                    />
                    <Label htmlFor={category}>{category}</Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Customer Segments */}
          <Card>
            <CardHeader>
              <CardTitle>Müşteri Segmentleri</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {[
                  'Yeni Müşteriler', 'Sadık Müşteriler', 'VIP Müşteriler',
                  'Kurumsal', 'Bireysel', 'Toptan'
                ].map((segment) => (
                  <div key={segment} className="flex items-center space-x-2">
                    <Checkbox
                      id={segment}
                      checked={filters.customerSegments.includes(segment)}
                      onCheckedChange={() => handleSegmentToggle(segment)}
                    />
                    <Label htmlFor={segment}>{segment}</Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Order Value Range */}
          <Card>
            <CardHeader>
              <CardTitle>Sipariş Tutarı Aralığı</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="minOrderValue">Minimum Tutar (USD)</Label>
                  <Input
                    id="minOrderValue"
                    type="number"
                    value={filters.minOrderValue}
                    onChange={(e) => setFilters(prev => ({ ...prev, minOrderValue: e.target.value }))}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="maxOrderValue">Maksimum Tutar (USD)</Label>
                  <Input
                    id="maxOrderValue"
                    type="number"
                    value={filters.maxOrderValue}
                    onChange={(e) => setFilters(prev => ({ ...prev, maxOrderValue: e.target.value }))}
                    placeholder="Sınırsız"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Status Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Durum Filtreleri</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="orderStatus">Sipariş Durumu</Label>
                  <Select 
                    value={filters.orderStatus} 
                    onValueChange={(value) => setFilters(prev => ({ ...prev, orderStatus: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tümü</SelectItem>
                      <SelectItem value="pending">Beklemede</SelectItem>
                      <SelectItem value="production">Üretimde</SelectItem>
                      <SelectItem value="shipped">Kargoda</SelectItem>
                      <SelectItem value="completed">Tamamlandı</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="paymentStatus">Ödeme Durumu</Label>
                  <Select 
                    value={filters.paymentStatus} 
                    onValueChange={(value) => setFilters(prev => ({ ...prev, paymentStatus: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tümü</SelectItem>
                      <SelectItem value="paid">Ödendi</SelectItem>
                      <SelectItem value="pending">Beklemede</SelectItem>
                      <SelectItem value="partial">Kısmi</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="region">Bölge</Label>
                  <Select 
                    value={filters.region} 
                    onValueChange={(value) => setFilters(prev => ({ ...prev, region: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tüm Bölgeler</SelectItem>
                      <SelectItem value="domestic">Yurtiçi</SelectItem>
                      <SelectItem value="international">Yurtdışı</SelectItem>
                      <SelectItem value="europe">Avrupa</SelectItem>
                      <SelectItem value="asia">Asya</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Comparison */}
          <Card>
            <CardHeader>
              <CardTitle>Karşılaştırma</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="comparisonPeriod">Karşılaştırma Dönemi</Label>
                <Select 
                  value={filters.comparisonPeriod} 
                  onValueChange={(value) => setFilters(prev => ({ ...prev, comparisonPeriod: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Karşılaştırma Yok</SelectItem>
                    <SelectItem value="previous">Önceki Dönem</SelectItem>
                    <SelectItem value="year_ago">Geçen Yıl</SelectItem>
                    <SelectItem value="quarter_ago">Önceki Çeyrek</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-between">
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose}>
                İptal
              </Button>
              <Button variant="outline" onClick={handleResetFilters}>
                <X className="w-4 h-4 mr-2" />
                Sıfırla
              </Button>
            </div>
            <Button 
              onClick={handleApplyFilters}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Uygulanıyor...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Filtreleri Uygula
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
