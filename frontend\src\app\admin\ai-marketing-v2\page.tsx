'use client';

import React, { useState, useEffect } from 'react';
import { 
  ChartBarIcon, 
  RocketLaunchIcon, 
  GlobeAltIcon, 
  EnvelopeIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface DashboardStats {
  total_campaigns: number;
  total_leads: number;
  total_value: number;
  campaign_statistics: any[];
  lead_statistics: any[];
  top_countries: any[];
  market_opportunities: any[];
  ai_metrics: any[];
}

interface Campaign {
  id: string;
  name: string;
  type: string;
  status: string;
  target_countries: string[];
  performance_metrics: any;
  created_at: string;
}

const AIMarketingDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardStats | null>(null);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'campaigns' | 'leads' | 'analysis'>('overview');

  useEffect(() => {
    loadDashboardData();
    loadCampaigns();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Mock data for demo
      const mockData: DashboardStats = {
        total_campaigns: 12,
        total_leads: 1847,
        total_value: 2450000,
        campaign_statistics: [],
        lead_statistics: [],
        top_countries: [
          { country: 'Germany', lead_count: 245, total_value: 850000, avg_confidence: 0.87 },
          { country: 'USA', lead_count: 198, total_value: 720000, avg_confidence: 0.82 },
          { country: 'Italy', lead_count: 156, total_value: 480000, avg_confidence: 0.79 },
          { country: 'France', lead_count: 134, total_value: 400000, avg_confidence: 0.75 }
        ],
        market_opportunities: [
          { country: 'Spain', product_category: 'Natural Stone', analysis_type: 'import_potential', opportunity_score: 0.89 },
          { country: 'Netherlands', product_category: 'Marble', analysis_type: 'export_opportunity', opportunity_score: 0.84 },
          { country: 'Belgium', product_category: 'Granite', analysis_type: 'competition_analysis', opportunity_score: 0.78 }
        ],
        ai_metrics: []
      };
      setDashboardData(mockData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      toast.error('Dashboard verisi yüklenemedi');
    }
  };

  const loadCampaigns = async () => {
    try {
      // Mock campaigns for demo
      const mockCampaigns: Campaign[] = [
        {
          id: '1',
          name: 'European Market Expansion',
          type: 'lead_generation',
          status: 'active',
          target_countries: ['Germany', 'France', 'Italy'],
          performance_metrics: { leads_generated: 245 },
          created_at: '2024-12-10'
        },
        {
          id: '2',
          name: 'US Construction Market',
          type: 'market_analysis',
          status: 'completed',
          target_countries: ['USA'],
          performance_metrics: { analysis_score: 0.87 },
          created_at: '2024-12-09'
        },
        {
          id: '3',
          name: 'Asian Market Research',
          type: 'lead_generation',
          status: 'draft',
          target_countries: ['Japan', 'South Korea', 'Singapore'],
          performance_metrics: {},
          created_at: '2024-12-11'
        }
      ];
      setCampaigns(mockCampaigns);
    } catch (error) {
      console.error('Failed to load campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  const createCampaign = async (campaignData: any) => {
    try {
      toast.success('AI kampanya oluşturuldu (Demo)');
      // In real implementation, make API call
      const newCampaign: Campaign = {
        id: Date.now().toString(),
        name: campaignData.name,
        type: campaignData.type,
        status: 'draft',
        target_countries: campaignData.target_countries,
        performance_metrics: {},
        created_at: new Date().toISOString()
      };
      setCampaigns(prev => [newCampaign, ...prev]);
    } catch (error) {
      console.error('Failed to create campaign:', error);
      toast.error('Kampanya oluşturulamadı');
    }
  };

  const startLeadGeneration = async (campaignId: string) => {
    try {
      toast.success('AI lead generation başlatıldı (Demo)');
      // Update campaign status
      setCampaigns(prev => 
        prev.map(campaign => 
          campaign.id === campaignId 
            ? { ...campaign, status: 'active' }
            : campaign
        )
      );
    } catch (error) {
      console.error('Failed to start lead generation:', error);
      toast.error('Lead generation başlatılamadı');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">AI Marketing Dashboard Yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <SparklesIcon className="h-8 w-8 text-blue-600 mr-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">AI Marketing System</h1>
                  <p className="text-sm text-gray-600">Yapay zeka destekli pazarlama ve lead generation</p>
                </div>
              </div>
              <button
                onClick={() => {
                  const campaignData = {
                    name: `AI Campaign ${Date.now()}`,
                    type: 'lead_generation',
                    target_countries: ['Turkey', 'Germany', 'USA'],
                    target_industries: ['construction', 'architecture'],
                    target_keywords: ['natural stone', 'marble']
                  };
                  createCampaign(campaignData);
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
              >
                <RocketLaunchIcon className="h-5 w-5 mr-2" />
                Yeni AI Kampanya
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', name: 'Genel Bakış', icon: ChartBarIcon },
              { id: 'campaigns', name: 'Kampanyalar', icon: RocketLaunchIcon },
              { id: 'leads', name: 'Lead\'ler', icon: UserGroupIcon },
              { id: 'analysis', name: 'Pazar Analizi', icon: GlobeAltIcon }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                <tab.icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Overview Tab */}
        {activeTab === 'overview' && dashboardData && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <RocketLaunchIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Toplam Kampanya</p>
                    <p className="text-2xl font-semibold text-gray-900">{dashboardData.total_campaigns}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <UserGroupIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Toplam Lead</p>
                    <p className="text-2xl font-semibold text-gray-900">{dashboardData.total_leads}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CurrencyDollarIcon className="h-8 w-8 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Toplam Değer</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      ${dashboardData.total_value?.toLocaleString() || 0}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <TrendingUpIcon className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">AI Performans</p>
                    <p className="text-2xl font-semibold text-gray-900">87%</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Top Countries */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">En İyi Ülkeler</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {dashboardData.top_countries.map((country: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                          <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{country.country}</p>
                          <p className="text-sm text-gray-500">{country.lead_count} lead</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">${country.total_value?.toLocaleString()}</p>
                        <p className="text-sm text-gray-500">Güven: {(country.avg_confidence * 100).toFixed(0)}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Market Opportunities */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Pazar Fırsatları</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {dashboardData.market_opportunities.map((opportunity: any, index: number) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{opportunity.country}</h4>
                        <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                          {(opportunity.opportunity_score * 100).toFixed(0)}% Fırsat
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">{opportunity.product_category}</p>
                      <p className="text-sm text-gray-500 mt-1">{opportunity.analysis_type}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Campaigns Tab */}
        {activeTab === 'campaigns' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">AI Kampanyalar</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {campaigns.map((campaign) => (
                    <div key={campaign.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900">{campaign.name}</h4>
                          <p className="text-sm text-gray-600">{campaign.type}</p>
                          <p className="text-sm text-gray-500">
                            Hedef: {campaign.target_countries?.join(', ')}
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className={`px-2.5 py-0.5 rounded text-xs font-medium ${
                            campaign.status === 'active' ? 'bg-green-100 text-green-800' :
                            campaign.status === 'draft' ? 'bg-gray-100 text-gray-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {campaign.status}
                          </span>
                          {campaign.status === 'draft' && (
                            <button
                              onClick={() => startLeadGeneration(campaign.id)}
                              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
                            >
                              Başlat
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Leads Tab */}
        {activeTab === 'leads' && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">AI Generated Leads</h3>
            </div>
            <div className="p-6">
              <div className="text-center py-8">
                <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Lead listesi burada görünecek...</p>
                <p className="text-sm text-gray-400 mt-2">AI kampanyalarınız çalıştığında lead'ler burada listelenecek</p>
              </div>
            </div>
          </div>
        )}

        {/* Analysis Tab */}
        {activeTab === 'analysis' && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Pazar Analizi</h3>
            </div>
            <div className="p-6">
              <div className="text-center py-8">
                <GlobeAltIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Pazar analizi burada görünecek...</p>
                <p className="text-sm text-gray-400 mt-2">AI market analizi sonuçları burada gösterilecek</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIMarketingDashboard;
