'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  X,
  Search,
  Users,
  Mail,
  Globe,
  ArrowRight
} from 'lucide-react';

interface CountryEmailList {
  id: string;
  countryCode: string;
  countryName: string;
  flag: string;
  totalSubscribers: number;
  activeSubscribers: number;
  lastUpdated: Date;
  segments: number;
}

interface CountrySelectionModalProps {
  countries: CountryEmailList[];
  onClose: () => void;
  onSelect: (country: CountryEmailList) => void;
}

export default function CountrySelectionModal({ countries, onClose, onSelect }: CountrySelectionModalProps) {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredCountries = countries.filter(country =>
    country.countryName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    country.countryCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectCountry = (country: CountryEmailList) => {
    onSelect(country);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Hedef Ülke Seçin</h2>
            <p className="text-gray-600 mt-1">
              Kampanya göndermek istediğiniz ülkeyi seçin
            </p>
          </div>
          <Button variant="ghost" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>

        <div className="p-6">
          {/* Search */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Ülke ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Countries List */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filteredCountries.length === 0 ? (
              <div className="text-center py-8">
                <Globe className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">Aradığınız ülke bulunamadı</p>
              </div>
            ) : (
              filteredCountries.map((country) => {
                const activeRate = (country.activeSubscribers / country.totalSubscribers) * 100;
                
                return (
                  <div
                    key={country.id}
                    className="p-4 border rounded-lg hover:border-blue-500 hover:bg-blue-50 cursor-pointer transition-colors group"
                    onClick={() => handleSelectCountry(country)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <span className="text-3xl">{country.flag}</span>
                        <div>
                          <h3 className="font-medium text-gray-900 group-hover:text-blue-900">
                            {country.countryName}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {country.countryCode} • {country.segments} segment
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-6">
                        <div className="text-center">
                          <div className="flex items-center space-x-1 text-sm text-gray-600">
                            <Users className="w-4 h-4" />
                            <span>{country.totalSubscribers.toLocaleString()}</span>
                          </div>
                          <p className="text-xs text-gray-500">Toplam</p>
                        </div>
                        
                        <div className="text-center">
                          <div className="flex items-center space-x-1 text-sm text-green-600">
                            <Mail className="w-4 h-4" />
                            <span>{country.activeSubscribers.toLocaleString()}</span>
                          </div>
                          <p className="text-xs text-gray-500">Aktif ({activeRate.toFixed(0)}%)</p>
                        </div>
                        
                        <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-blue-500" />
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>

          {/* Quick Stats */}
          <div className="mt-6 pt-4 border-t">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-blue-600">{countries.length}</p>
                <p className="text-sm text-gray-600">Toplam Ülke</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-green-600">
                  {countries.reduce((sum, c) => sum + c.totalSubscribers, 0).toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">Toplam Abone</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-purple-600">
                  {countries.reduce((sum, c) => sum + c.activeSubscribers, 0).toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">Aktif Abone</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-2 p-6 border-t bg-gray-50">
          <Button variant="outline" onClick={onClose}>
            İptal
          </Button>
        </div>
      </div>
    </div>
  );
}
