"use client"

import * as React from "react"
import { Navigation } from "@/components/ui/navigation"
import { Container } from "@/components/ui/container"

import { Button } from "@/components/ui/button"
import ProductCard from "@/components/ui/product-card"
import { VisuallyHidden } from "@/components/ui/visually-hidden"
import { Product3DViewerModal } from "@/components/ui/3d-viewer-modal"
import { QuoteRequestModal } from "@/components/ui/quote-request-modal"
import { FavoritesProvider, useFavorites } from "@/contexts/favorites-context"
import { useAuth } from "@/contexts/auth-context"

// Test verileri kaldırıldı - gerçek veriler API'den gelecek
const products: any[] = []

const navigationLinks = [
  { name: "Ana Sayfa", href: "/" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/products", active: true },
  { name: "3D Sanal Showroom", href: "/3d-showroom" },
  { name: "<PERSON><PERSON><PERSON>", href: "/news" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/about" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/contact" }
]

function ProductsContent() {
  const { addToFavorites, removeFromFavorites, isFavorite } = useFavorites()
  const { isAuthenticated, user, showLoginModal, showCustomerRegisterModal, logout } = useAuth()
  const [selectedCategory, setSelectedCategory] = React.useState<string>("")
  const [selectedType, setSelectedType] = React.useState<string>("")
  const [selectedProduct, setSelectedProduct] = React.useState<typeof products[0] | null>(null)
  const [is3DViewerOpen, setIs3DViewerOpen] = React.useState(false)
  const [isQuoteModalOpen, setIsQuoteModalOpen] = React.useState(false)
  const [searchQuery, setSearchQuery] = React.useState<string>("")

  const handleToggleFavorite = (productId: string) => {
    const product = products.find(p => p.id === productId)
    if (!product) return

    if (isFavorite(productId)) {
      removeFromFavorites(productId)
      console.log(`Removed ${product.name} from favorites`)
    } else {
      addToFavorites(product)
      console.log(`Added ${product.name} to favorites`)
    }
  }

  const handleView3D = (productId: string) => {
    const product = products.find(p => p.id === productId)
    if (product) {
      setSelectedProduct(product)
      setIs3DViewerOpen(true)
    }
  }

  const handleRequestQuote = (productId: string) => {
    // Auth kontrolü ProductCard'da yapılıyor, buraya geldiğinde zaten giriş yapmış
    const product = products.find(p => p.id === productId)
    if (product) {
      setSelectedProduct(product)
      setIsQuoteModalOpen(true)
    }
  }

  const filteredProducts = React.useMemo(() => {
    return products.filter(product => {
      // Category filter
      if (selectedCategory && product.category.toLowerCase() !== selectedCategory.toLowerCase()) {
        return false
      }

      // Type filter (ebatlı/blok)
      if (selectedType && product.type !== selectedType) {
        return false
      }

      // Search filter
      if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false
      }

      return true
    })
  }, [selectedCategory, selectedType, searchQuery])

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50">
        <Navigation
          brand={{
            name: "Türkiye Doğal Taş Pazarı",
            href: "/"
          }}
          links={navigationLinks}
          actions={
            <div className="flex items-center gap-2">
              {!isAuthenticated ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={showLoginModal}
                  >
                    Giriş Yap
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={showCustomerRegisterModal}
                  >
                    Kayıt Ol
                  </Button>
                </>
              ) : (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600 max-w-[200px] truncate">
                    Hoş geldiniz, {user?.company || user?.name}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={logout}
                  >
                    Çıkış Yap
                  </Button>
                </div>
              )}
            </div>
          }
        />

        <main id="main-content">
          <Container className="py-8">
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-stone-900 mb-4">
                Doğal Taş Ürünleri
              </h1>
              <p className="text-lg text-stone-600 max-w-2xl">
                Türkiye'nin en kaliteli doğal taş ürünlerini keşfedin. 
                Mermer, granit, traverten ve oniks çeşitlerimizle projelerinizi hayata geçirin.
              </p>
            </div>

            {/* Filters Section */}
            <div className="mb-8 p-6 bg-white rounded-lg shadow-sm border border-stone-200">
              <h2 className="text-lg font-semibold text-stone-900 mb-4">Filtreler</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Search */}
                <div>
                  <label className="block text-sm font-medium text-stone-700 mb-2">
                    Ürün Ara
                  </label>
                  <input
                    type="text"
                    placeholder="Ürün adı ile ara..."
                    className="w-full px-3 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-stone-700 mb-2">
                    Kategori
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                  >
                    <option value="">Tüm Kategoriler</option>
                    <option value="mermer">Mermer</option>
                    <option value="granit">Granit</option>
                    <option value="traverten">Traverten</option>
                    <option value="oniks">Oniks</option>
                    <option value="andezit">Andezit</option>
                  </select>
                </div>

                {/* Type */}
                <div>
                  <label className="block text-sm font-medium text-stone-700 mb-2">
                    Ürün Tipi
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                    value={selectedType}
                    onChange={(e) => setSelectedType(e.target.value)}
                  >
                    <option value="">Tüm Tipler</option>
                    <option value="ebatlı">Ebatlı Ürünler</option>
                    <option value="blok">Blok Ürünler</option>
                  </select>
                </div>
              </div>

              {/* Filter Summary */}
              <div className="mt-4 flex items-center justify-between">
                <div className="text-sm text-stone-600">
                  {filteredProducts.length} ürün bulundu
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedCategory("")
                    setSelectedType("")
                    setSearchQuery("")
                  }}
                >
                  Filtreleri Temizle
                </Button>
              </div>
            </div>

            {/* Products Grid */}
            {filteredProducts.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12">
                {filteredProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    isFavorite={isFavorite(product.id)}
                    onRequestQuote={() => handleRequestQuote(product.id)}
                    onToggleFavorite={() => handleToggleFavorite(product.id)}
                    onView3D={() => handleView3D(product.id)}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-16 bg-white rounded-lg shadow-sm border border-stone-200 mb-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold text-stone-700 mb-2">
                  Ürün Bulunamadı
                </h3>
                <p className="text-stone-600 mb-6 max-w-md mx-auto">
                  Aradığınız kriterlere uygun ürün bulunamadı. Filtreleri değiştirerek tekrar deneyin.
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedCategory("")
                    setSelectedType("")
                    setSearchQuery("")
                  }}
                >
                  Filtreleri Temizle
                </Button>
              </div>
            )}

            {/* 3D Viewer Modal */}
            <Product3DViewerModal
              isOpen={is3DViewerOpen}
              onClose={() => setIs3DViewerOpen(false)}
              product={selectedProduct}
            />

            {/* Quote Request Modal */}
            <QuoteRequestModal
              isOpen={isQuoteModalOpen}
              onClose={() => setIsQuoteModalOpen(false)}
              product={selectedProduct}
            />
          </Container>
        </main>
      </div>
    </>
  )
}

export default function ProductsPage() {
  return (
    <FavoritesProvider>
      <ProductsContent />
    </FavoritesProvider>
  )
}
