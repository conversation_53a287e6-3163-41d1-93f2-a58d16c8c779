import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { as<PERSON><PERSON><PERSON><PERSON> } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/authMiddleware';
import { AIMarketingService } from '../services/AIMarketingService';
import { z } from 'zod';

const prisma = new PrismaClient();
const aiMarketingService = new AIMarketingService();

// Validation schemas
const createCampaignSchema = z.object({
  name: z.string().min(1).max(255),
  type: z.enum(['lead_generation', 'email_campaign', 'market_analysis', 'retargeting']),
  target_countries: z.array(z.string()).min(1),
  target_industries: z.array(z.string()).optional(),
  target_keywords: z.array(z.string()).optional(),
  budget_limit: z.number().positive().optional(),
  start_date: z.string().datetime().optional(),
  end_date: z.string().datetime().optional()
});

const leadGenerationSchema = z.object({
  target_countries: z.array(z.string()).min(1),
  target_industries: z.array(z.string()).min(1),
  keywords: z.array(z.string()).min(1),
  max_leads: z.number().int().min(1).max(1000).default(100),
  confidence_threshold: z.number().min(0).max(1).default(0.7)
});

const marketAnalysisSchema = z.object({
  country: z.string().min(1),
  product_category: z.string().min(1),
  analysis_type: z.enum(['import_potential', 'export_opportunity', 'competition_analysis', 'price_analysis'])
});

export class AIMarketingController {
  // Create AI Campaign
  createCampaign = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user!;
    const validatedData = createCampaignSchema.parse(req.body);

    // Only admin users can create AI campaigns
    if (user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Only admin users can create AI campaigns'
      });
    }

    const campaign = await prisma.$queryRaw`
      INSERT INTO ai_campaigns (
        name, type, target_countries, target_industries, target_keywords,
        budget_limit, start_date, end_date, created_by, status
      ) VALUES (
        ${validatedData.name}, ${validatedData.type}, 
        ${JSON.stringify(validatedData.target_countries)},
        ${JSON.stringify(validatedData.target_industries || [])},
        ${JSON.stringify(validatedData.target_keywords || [])},
        ${validatedData.budget_limit || null},
        ${validatedData.start_date || null},
        ${validatedData.end_date || null},
        ${user.id}, 'draft'
      )
    `;

    res.status(201).json({
      success: true,
      message: 'AI campaign created successfully',
      data: { campaign }
    });
  });

  // Get AI Campaigns
  getCampaigns = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user!;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const status = req.query.status as string;
    const type = req.query.type as string;

    if (user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    let whereClause = '';
    const params: any[] = [];

    if (status) {
      whereClause += ' WHERE status = ?';
      params.push(status);
    }

    if (type) {
      whereClause += whereClause ? ' AND type = ?' : ' WHERE type = ?';
      params.push(type);
    }

    const offset = (page - 1) * limit;
    params.push(limit, offset);

    const campaigns = await prisma.$queryRawUnsafe(`
      SELECT * FROM ai_campaigns 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `, ...params);

    const totalQuery = await prisma.$queryRawUnsafe(`
      SELECT COUNT(*) as total FROM ai_campaigns ${whereClause}
    `, ...params.slice(0, -2));

    const total = (totalQuery as any)[0].total;

    res.json({
      success: true,
      data: {
        campaigns,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  });

  // Start Lead Generation
  startLeadGeneration = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user!;
    const { campaignId } = req.params;
    const validatedData = leadGenerationSchema.parse(req.body);

    if (user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Check if campaign exists and belongs to user
    const campaign = await prisma.$queryRaw`
      SELECT * FROM ai_campaigns 
      WHERE id = ${campaignId} AND created_by = ${user.id}
    `;

    if (!campaign || (campaign as any).length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    // Update campaign status to active
    await prisma.$queryRaw`
      UPDATE ai_campaigns 
      SET status = 'active', start_date = NOW()
      WHERE id = ${campaignId}
    `;

    // Start lead generation in background
    const leadParams = {
      targetCountries: validatedData.target_countries,
      targetIndustries: validatedData.target_industries,
      keywords: validatedData.keywords,
      maxLeads: validatedData.max_leads,
      confidenceThreshold: validatedData.confidence_threshold
    };

    aiMarketingService.generateLeads(campaignId, leadParams)
      .then(leads => {
        console.log(`✅ Lead generation completed for campaign ${campaignId}: ${leads.length} leads`);
      })
      .catch(error => {
        console.error(`❌ Lead generation failed for campaign ${campaignId}:`, error);
      });

    res.json({
      success: true,
      message: 'Lead generation started successfully',
      data: {
        campaignId,
        status: 'active',
        estimated_completion: '15-30 minutes'
      }
    });
  });

  // Get Campaign Leads
  getCampaignLeads = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user!;
    const { campaignId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const status = req.query.status as string;
    const country = req.query.country as string;

    if (user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    let whereClause = `WHERE campaign_id = '${campaignId}'`;
    const params: any[] = [];

    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }

    if (country) {
      whereClause += ' AND country = ?';
      params.push(country);
    }

    const offset = (page - 1) * limit;
    params.push(limit, offset);

    const leads = await prisma.$queryRawUnsafe(`
      SELECT * FROM ai_leads 
      ${whereClause}
      ORDER BY ai_confidence_score DESC, created_at DESC
      LIMIT ? OFFSET ?
    `, ...params);

    const totalQuery = await prisma.$queryRawUnsafe(`
      SELECT COUNT(*) as total FROM ai_leads ${whereClause}
    `, ...params.slice(0, -2));

    const total = (totalQuery as any)[0].total;

    // Get lead statistics
    const stats = await prisma.$queryRaw`
      SELECT 
        status,
        COUNT(*) as count,
        AVG(ai_confidence_score) as avg_confidence,
        SUM(estimated_value) as total_value
      FROM ai_leads 
      WHERE campaign_id = ${campaignId}
      GROUP BY status
    `;

    res.json({
      success: true,
      data: {
        leads,
        statistics: stats,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  });

  // Start Market Analysis
  startMarketAnalysis = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user!;
    const validatedData = marketAnalysisSchema.parse(req.body);

    if (user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Start market analysis
    const analysisParams = {
      country: validatedData.country,
      productCategory: validatedData.product_category,
      analysisType: validatedData.analysis_type
    };

    const analysis = await aiMarketingService.analyzeMarket(analysisParams);

    res.json({
      success: true,
      message: 'Market analysis completed successfully',
      data: { analysis }
    });
  });

  // Get Market Analysis
  getMarketAnalysis = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user!;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const country = req.query.country as string;
    const analysisType = req.query.analysis_type as string;

    if (user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    let whereClause = '';
    const params: any[] = [];

    if (country) {
      whereClause += ' WHERE country = ?';
      params.push(country);
    }

    if (analysisType) {
      whereClause += whereClause ? ' AND analysis_type = ?' : ' WHERE analysis_type = ?';
      params.push(analysisType);
    }

    const offset = (page - 1) * limit;
    params.push(limit, offset);

    const analyses = await prisma.$queryRawUnsafe(`
      SELECT * FROM ai_market_analysis 
      ${whereClause}
      ORDER BY opportunity_score DESC, created_at DESC
      LIMIT ? OFFSET ?
    `, ...params);

    const totalQuery = await prisma.$queryRawUnsafe(`
      SELECT COUNT(*) as total FROM ai_market_analysis ${whereClause}
    `, ...params.slice(0, -2));

    const total = (totalQuery as any)[0].total;

    res.json({
      success: true,
      data: {
        analyses,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  });

  // Get AI Dashboard Data
  getDashboard = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user!;

    if (user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Get campaign statistics
    const campaignStats = await prisma.$queryRaw`
      SELECT 
        status,
        COUNT(*) as count,
        AVG(CASE WHEN performance_metrics IS NOT NULL 
            THEN JSON_EXTRACT(performance_metrics, '$.leads_generated') 
            ELSE 0 END) as avg_leads
      FROM ai_campaigns 
      GROUP BY status
    `;

    // Get lead statistics
    const leadStats = await prisma.$queryRaw`
      SELECT 
        status,
        COUNT(*) as count,
        AVG(ai_confidence_score) as avg_confidence,
        SUM(estimated_value) as total_value
      FROM ai_leads 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      GROUP BY status
    `;

    // Get top performing countries
    const topCountries = await prisma.$queryRaw`
      SELECT 
        country,
        COUNT(*) as lead_count,
        AVG(ai_confidence_score) as avg_confidence,
        SUM(estimated_value) as total_value
      FROM ai_leads 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      GROUP BY country
      ORDER BY total_value DESC
      LIMIT 10
    `;

    // Get recent market opportunities
    const marketOpportunities = await prisma.$queryRaw`
      SELECT * FROM ai_market_analysis 
      WHERE opportunity_score > 0.7
      ORDER BY created_at DESC
      LIMIT 5
    `;

    // Get AI performance metrics
    const aiMetrics = await prisma.$queryRaw`
      SELECT 
        metric_type,
        AVG(metric_value) as avg_value,
        COUNT(*) as count
      FROM ai_performance_metrics 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      GROUP BY metric_type
    `;

    res.json({
      success: true,
      data: {
        campaign_statistics: campaignStats,
        lead_statistics: leadStats,
        top_countries: topCountries,
        market_opportunities: marketOpportunities,
        ai_metrics: aiMetrics,
        summary: {
          total_campaigns: (campaignStats as any).reduce((sum: number, stat: any) => sum + stat.count, 0),
          total_leads: (leadStats as any).reduce((sum: number, stat: any) => sum + stat.count, 0),
          total_value: (leadStats as any).reduce((sum: number, stat: any) => sum + (stat.total_value || 0), 0)
        }
      }
    });
  });

  // Update Lead Status
  updateLeadStatus = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user!;
    const { leadId } = req.params;
    const { status, notes } = req.body;

    if (user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const validStatuses = ['new', 'contacted', 'qualified', 'converted', 'rejected'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    await prisma.$queryRaw`
      UPDATE ai_leads 
      SET status = ${status}, 
          notes = ${notes || null},
          last_contact_date = CASE WHEN ${status} = 'contacted' THEN NOW() ELSE last_contact_date END,
          contact_attempts = CASE WHEN ${status} = 'contacted' THEN contact_attempts + 1 ELSE contact_attempts END
      WHERE id = ${leadId}
    `;

    res.json({
      success: true,
      message: 'Lead status updated successfully'
    });
  });

  // Export Country Email Lists
  exportCountryEmails = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = req.user!;
    const { country } = req.params;
    const format = req.query.format as string || 'json';

    if (user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const emails = await prisma.$queryRaw`
      SELECT 
        country, industry, company_name, contact_person, 
        email, phone, website, company_size, verified
      FROM country_email_lists 
      WHERE country = ${country} AND verified = true
      ORDER BY engagement_score DESC
    `;

    if (format === 'csv') {
      // Convert to CSV format
      const csvHeader = 'Country,Industry,Company,Contact,Email,Phone,Website,Size,Verified\n';
      const csvData = (emails as any[]).map(row => 
        `${row.country},${row.industry},${row.company_name},${row.contact_person},${row.email},${row.phone},${row.website},${row.company_size},${row.verified}`
      ).join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${country}_emails.csv"`);
      res.send(csvHeader + csvData);
    } else {
      res.json({
        success: true,
        data: {
          country,
          total_emails: (emails as any[]).length,
          emails
        }
      });
    }
  });
}
