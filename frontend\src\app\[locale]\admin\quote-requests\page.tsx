'use client'

import React from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  Building2,
  Package,
  Users,
  Clock,
  DollarSign,
  Eye,
  TrendingUp,
  Calendar,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Target
} from 'lucide-react'
import { AdminQuoteRequestDetailsModal } from '@/components/ui/admin-quote-request-details-modal'

export default function AdminQuoteRequestsPage() {
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedStatus, setSelectedStatus] = React.useState('all')
  const [selectedTab, setSelectedTab] = React.useState('active')
  const [selectedRequest, setSelectedRequest] = React.useState<any>(null)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = React.useState(false)

  // Mock teklif talep verileri - gerçek uygulamada API'den gelecek
  const mockQuoteRequests = [
    {
      id: 'QR-001',
      customerName: 'İnşaat A.Ş.',
      customerContact: 'Ahmet Yılmaz',
      customerEmail: '<EMAIL>',
      customerPhone: '+90 ************',
      productName: 'Afyon Beyaz Mermer',
      productImage: '/api/placeholder/300/200',
      dimension: '60x120x2cm',
      quantity: 150,
      unit: 'm²',
      requestDate: '2025-06-30',
      deadline: '2025-07-05',
      status: 'active',
      quotesReceived: 3,
      products: [
        {
          id: 'prod-1',
          name: 'Afyon Beyaz Mermer',
          image: '/api/placeholder/300/200',
          category: 'Mermer',
          specifications: [
            {
              thickness: '2',
              width: '60',
              length: '120',
              surface: 'Cilalı',
              packaging: 'Kasalı',
              delivery: 'Fabrika',
              area: '100',
              targetPrice: '100',
              currency: 'USD'
            },
            {
              thickness: '3',
              width: '80',
              length: '160',
              surface: 'Honlu',
              packaging: 'Bandıllı',
              delivery: 'Fabrika',
              area: '50',
              targetPrice: '110',
              currency: 'USD'
            }
          ]
        }
      ],
      quotes: [
        {
          id: 'quote-1',
          producerName: 'Afyon Doğal Taş A.Ş.',
          producerEmail: '<EMAIL>',
          producerPhone: '+90 ************',
          price: 115,
          currency: 'USD',
          deliveryTime: '15 gün',
          responseTime: '2 saat',
          status: 'submitted',
          quotedDate: '2025-06-30',
          notes: 'Premium kalite Afyon beyaz mermer. Fabrika teslim fiyatıdır.'
        },
        {
          id: 'quote-2',
          producerName: 'Premium Stone Co.',
          producerEmail: '<EMAIL>',
          producerPhone: '+90 ************',
          price: 120,
          currency: 'USD',
          deliveryTime: '20 gün',
          responseTime: '4 saat',
          status: 'submitted',
          quotedDate: '2025-06-30',
          notes: 'A+ kalite mermer. Özel ambalaj dahil.'
        },
        {
          id: 'quote-3',
          producerName: 'Marmara Mermer Ltd.',
          producerEmail: '<EMAIL>',
          producerPhone: '+90 ************',
          price: 110,
          currency: 'USD',
          deliveryTime: '18 gün',
          responseTime: '6 saat',
          status: 'submitted',
          quotedDate: '2025-07-01',
          notes: 'Ekonomik çözüm. Nakliye ayrı hesaplanır.'
        }
      ],
      bestPrice: 110,
      avgPrice: 115,
      notes: 'Acil proje için gerekli. Kalite çok önemli.',
      message: 'Yeni otel projemiz için kaliteli beyaz mermer arıyoruz. Fiyat ve teslimat süresi önemli.'
    },
    {
      id: 'QR-002',
      customerName: 'Villa Projeleri A.Ş.',
      customerContact: 'Mehmet Özkan',
      customerEmail: '<EMAIL>',
      customerPhone: '+90 ************',
      productName: 'Oniks Yeşil',
      productImage: '/api/placeholder/300/200',
      dimension: '80x160x3cm',
      quantity: 100,
      unit: 'm²',
      requestDate: '2025-06-29',
      deadline: '2025-07-10',
      status: 'pending',
      quotesReceived: 1,
      products: [
        {
          id: 'prod-2',
          name: 'Oniks Yeşil',
          image: '/api/placeholder/300/200',
          category: 'Oniks',
          specifications: [
            {
              thickness: '3',
              width: '80',
              length: '160',
              surface: 'Cilalı',
              packaging: 'Özel ambalaj',
              delivery: 'Liman',
              area: '60',
              targetPrice: '120',
              currency: 'USD'
            },
            {
              thickness: '2',
              width: '60',
              length: '120',
              surface: 'Cilalı',
              packaging: 'Özel ambalaj',
              delivery: 'Liman',
              area: '40',
              targetPrice: '115',
              currency: 'USD'
            }
          ]
        },
        {
          id: 'prod-3',
          name: 'Oniks Beyaz',
          image: '/api/placeholder/300/200',
          category: 'Oniks',
          specifications: [
            {
              thickness: '2',
              width: '40',
              length: '80',
              surface: 'Cilalı',
              packaging: 'Özel ambalaj',
              delivery: 'Liman',
              area: '30',
              targetPrice: '130',
              currency: 'USD'
            }
          ]
        }
      ],
      quotes: [
        {
          id: 'quote-4',
          producerName: 'Premium Stone Co.',
          producerEmail: '<EMAIL>',
          producerPhone: '+90 ************',
          price: 125,
          currency: 'USD',
          deliveryTime: '25 gün',
          responseTime: '3 saat',
          status: 'submitted',
          quotedDate: '2025-06-29',
          notes: 'Özel seçim oniks. Işık geçirgenliği mükemmel.'
        }
      ],
      bestPrice: 125,
      avgPrice: 125,
      notes: 'Kalite çok önemli. Lüks villa projesi.',
      message: 'Lüks villa projesi için özel oniks arıyoruz. Işık geçirgenliği önemli.'
    },
    {
      id: 'QR-003',
      customerName: 'Dekorasyon Ltd.',
      customerContact: 'Fatma Kaya',
      customerEmail: '<EMAIL>',
      customerPhone: '+90 ************',
      productName: 'Traverten Bej',
      productImage: '/api/placeholder/300/200',
      dimension: '40x80x2cm',
      quantity: 200,
      unit: 'm²',
      requestDate: '2025-06-28',
      deadline: '2025-07-08',
      status: 'completed',
      quotesReceived: 4,
      products: [
        {
          id: 'prod-3',
          name: 'Traverten Bej',
          image: '/api/placeholder/300/200',
          category: 'Traverten',
          specifications: [
            {
              thickness: '2',
              width: '40',
              length: '80',
              surface: 'Honlu',
              packaging: 'Bandıllı',
              delivery: 'Fabrika',
              area: '200',
              targetPrice: '80',
              currency: 'USD'
            }
          ]
        }
      ],
      quotes: [
        {
          id: 'quote-5',
          producerName: 'Denizli Taş Ltd.',
          producerEmail: '<EMAIL>',
          producerPhone: '+90 ************',
          price: 85,
          currency: 'USD',
          deliveryTime: '12 gün',
          responseTime: '1 saat',
          status: 'accepted',
          quotedDate: '2025-06-28',
          notes: 'Denizli traverten. Fabrika teslim. Hızlı teslimat.'
        },
        {
          id: 'quote-6',
          producerName: 'Pamukkale Stone',
          producerEmail: '<EMAIL>',
          producerPhone: '+90 ************',
          price: 88,
          currency: 'USD',
          deliveryTime: '15 gün',
          responseTime: '3 saat',
          status: 'rejected',
          quotedDate: '2025-06-28',
          notes: 'Premium traverten. Özel işlem uygulanmış.'
        },
        {
          id: 'quote-7',
          producerName: 'Anadolu Traverten',
          producerEmail: '<EMAIL>',
          producerPhone: '+90 ************',
          price: 87,
          currency: 'USD',
          deliveryTime: '14 gün',
          responseTime: '2 saat',
          status: 'rejected',
          quotedDate: '2025-06-29',
          notes: 'Standart kalite traverten.'
        },
        {
          id: 'quote-8',
          producerName: 'Hierapolis Stone',
          producerEmail: '<EMAIL>',
          producerPhone: '+90 ************',
          price: 90,
          currency: 'USD',
          deliveryTime: '16 gün',
          responseTime: '4 saat',
          status: 'rejected',
          quotedDate: '2025-06-29',
          notes: 'Yüksek kalite traverten. Özel seçim.'
        }
      ],
      bestPrice: 85,
      avgPrice: 87.5,
      selectedQuote: 'Denizli Taş Ltd.',
      notes: 'Fiyat odaklı seçim. Hızlı teslimat gerekli.',
      message: 'Dekorasyon projesi için traverten arıyoruz. Fiyat ve teslimat süresi önemli.'
    },
    {
      id: 'QR-004',
      customerName: 'Otel Zinciri Ltd.',
      customerContact: 'Ayşe Demir',
      customerEmail: '<EMAIL>',
      customerPhone: '+90 ************',
      productName: 'Granit Siyah',
      productImage: '/api/placeholder/300/200',
      dimension: '60x120x2cm',
      quantity: 300,
      unit: 'm²',
      requestDate: '2025-06-27',
      deadline: '2025-07-02',
      status: 'expired',
      quotesReceived: 0,
      products: [
        {
          id: 'prod-4',
          name: 'Granit Siyah',
          image: '/api/placeholder/300/200',
          category: 'Granit',
          specifications: [
            {
              thickness: '2',
              width: '60',
              length: '120',
              surface: 'Cilalı',
              packaging: 'Kasalı',
              delivery: 'Liman',
              area: '300',
              targetPrice: '95',
              currency: 'USD'
            }
          ]
        }
      ],
      quotes: [],
      bestPrice: null,
      avgPrice: null,
      notes: 'Süre doldu, yeniden talep edilmeli. Acil proje.',
      message: 'Otel renovasyon projesi için siyah granit arıyoruz. Acil ihtiyaç.'
    }
  ]

  const statuses = ['all', 'active', 'pending', 'completed', 'expired']
  const statusLabels = {
    all: 'Tümü',
    active: 'Aktif',
    pending: 'Teklif Bekleniyor',
    completed: 'Tamamlandı',
    expired: 'Süresi Doldu'
  }

  const filteredRequests = mockQuoteRequests.filter(request => {
    const matchesSearch = request.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || request.status === selectedStatus
    
    return matchesSearch && matchesStatus
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="secondary" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Aktif</Badge>
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Beklemede</Badge>
      case 'completed':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800"><Target className="w-3 h-3 mr-1" />Tamamlandı</Badge>
      case 'expired':
        return <Badge variant="secondary" className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Süresi Doldu</Badge>
      default:
        return null
    }
  }

  const handleViewDetails = (request: any) => {
    setSelectedRequest(request)
    setIsDetailsModalOpen(true)
  }

  const totalRequests = mockQuoteRequests.length
  const activeRequests = mockQuoteRequests.filter(r => r.status === 'active').length
  const completedRequests = mockQuoteRequests.filter(r => r.status === 'completed').length
  const avgQuotesPerRequest = mockQuoteRequests.reduce((sum, r) => sum + r.quotesReceived, 0) / mockQuoteRequests.length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Güncel Talepler</h1>
          <p className="text-gray-600 mt-1">
            Tüm teklif taleplerini ve süreçlerini izleyin
          </p>
        </div>
        <Button>
          <TrendingUp className="w-4 h-4 mr-2" />
          Analiz Raporu
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MessageSquare className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Talep</p>
              <p className="text-xl font-bold text-gray-900">{totalRequests}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Aktif Talep</p>
              <p className="text-xl font-bold text-gray-900">{activeRequests}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 rounded-lg">
              <Target className="w-5 h-5 text-amber-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Tamamlanan</p>
              <p className="text-xl font-bold text-gray-900">{completedRequests}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Ort. Teklif</p>
              <p className="text-xl font-bold text-gray-900">{avgQuotesPerRequest.toFixed(1)}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Müşteri, ürün veya talep numarası ile ara..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
          />
        </div>
        
        <select
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
        >
          {statuses.map(status => (
            <option key={status} value={status}>
              {statusLabels[status as keyof typeof statusLabels]}
            </option>
          ))}
        </select>
      </div>

      {/* Quote Requests List */}
      <div className="space-y-4">
        {filteredRequests.map((request) => (
          <Card key={request.id} className="overflow-hidden">
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {/* Request Info */}
                <div className="lg:col-span-1">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-lg">{request.id}</h3>
                    {getStatusBadge(request.status)}
                  </div>
                  
                  <div className="space-y-2">
                    <div>
                      <button className="text-blue-600 hover:underline font-medium">
                        {request.customerName}
                      </button>
                      <p className="text-sm text-gray-600">{request.customerContact}</p>
                    </div>
                    
                    <div>
                      <p className="font-medium">{request.productName}</p>
                      <p className="text-sm text-gray-600">{request.dimension}</p>
                      <p className="text-sm text-gray-600">{request.quantity} {request.unit}</p>
                    </div>
                    
                    <div className="text-xs text-gray-500">
                      <p>Talep: {new Date(request.requestDate).toLocaleDateString('tr-TR')}</p>
                      <p>Deadline: {new Date(request.deadline).toLocaleDateString('tr-TR')}</p>
                    </div>
                  </div>
                </div>

                {/* Quote Summary */}
                <div className="lg:col-span-2">
                  <h4 className="font-medium mb-3">Gelen Teklifler ({request.quotesReceived})</h4>
                  
                  {request.quotes.length > 0 ? (
                    <div className="space-y-2">
                      {request.quotes.map((quote, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex-1">
                            <button className="text-blue-600 hover:underline font-medium">
                              {quote.producerName}
                            </button>
                            <p className="text-sm text-gray-600">
                              Yanıt: {quote.responseTime} • Teslimat: {quote.deliveryTime}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">${quote.price}/{quote.currency}</p>
                            <Badge 
                              variant={quote.status === 'accepted' ? 'default' : 
                                      quote.status === 'rejected' ? 'secondary' : 'outline'}
                              className={quote.status === 'accepted' ? 'bg-green-100 text-green-800' :
                                        quote.status === 'rejected' ? 'bg-red-100 text-red-800' : ''}
                            >
                              {quote.status === 'accepted' ? 'Kabul' : 
                               quote.status === 'rejected' ? 'Red' : 'Beklemede'}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      <AlertTriangle className="w-8 h-8 mx-auto mb-2" />
                      <p>Henüz teklif gelmedi</p>
                    </div>
                  )}
                </div>

                {/* Actions & Stats */}
                <div className="lg:col-span-1">
                  <div className="space-y-4">
                    {request.bestPrice && (
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <p className="text-sm text-gray-600">En İyi Fiyat</p>
                        <p className="text-xl font-bold text-green-600">${request.bestPrice}</p>
                        <p className="text-xs text-gray-500">Ort: ${request.avgPrice}</p>
                      </div>
                    )}
                    
                    <div className="space-y-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => handleViewDetails(request)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        Detayları Görüntüle
                      </Button>
                      
                      {request.status === 'active' && (
                        <Button variant="outline" size="sm" className="w-full">
                          <MessageSquare className="w-4 h-4 mr-2" />
                          Müşteri ile İletişim
                        </Button>
                      )}
                    </div>
                    
                    {request.notes && (
                      <div className="p-2 bg-yellow-50 rounded text-xs">
                        <p className="font-medium">Not:</p>
                        <p className="text-gray-600">{request.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {filteredRequests.length === 0 && (
        <div className="text-center py-12">
          <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Talep bulunamadı</h3>
          <p className="text-gray-600">Arama kriterlerinize uygun talep bulunmuyor.</p>
        </div>
      )}

      {/* Details Modal */}
      <AdminQuoteRequestDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        request={selectedRequest}
      />
    </div>
  )
}
