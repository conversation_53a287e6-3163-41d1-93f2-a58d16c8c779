'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  MessageSquare,
  AlertTriangle,
  Filter,
  Calendar,
  User,
  Mail,
  Share2,
  TrendingUp
} from 'lucide-react';

interface ApprovalRequest {
  id: string;
  type: 'email_campaign' | 'social_post' | 'ad_campaign' | 'content_piece';
  title: string;
  content: any;
  requestedBy: string;
  requestedAt: Date;
  status: 'pending' | 'approved' | 'rejected';
  reviewedBy?: string;
  reviewedAt?: Date;
  comments?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  platform?: string;
  estimatedReach?: number;
}

export default function ApprovalsPage() {
  const [approvals, setApprovals] = useState<ApprovalRequest[]>([]);
  const [selectedApproval, setSelectedApproval] = useState<ApprovalRequest | null>(null);
  const [reviewComments, setReviewComments] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchApprovals();
  }, []);

  const fetchApprovals = async () => {
    setLoading(true);
    try {
      // Data will be loaded from API
      const mockApprovals: ApprovalRequest[] = [];

      setApprovals(mockApprovals);
    } catch (error) {
      console.error('Error fetching approvals:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (approvalId: string) => {
    try {
      const approval = approvals.find(a => a.id === approvalId);
      if (approval) {
        approval.status = 'approved';
        approval.reviewedBy = 'Admin User';
        approval.reviewedAt = new Date();
        approval.comments = reviewComments;
        setApprovals([...approvals]);
        setReviewComments('');
        setSelectedApproval(null);
      }
    } catch (error) {
      console.error('Error approving request:', error);
    }
  };

  const handleReject = async (approvalId: string) => {
    try {
      const approval = approvals.find(a => a.id === approvalId);
      if (approval) {
        approval.status = 'rejected';
        approval.reviewedBy = 'Admin User';
        approval.reviewedAt = new Date();
        approval.comments = reviewComments;
        setApprovals([...approvals]);
        setReviewComments('');
        setSelectedApproval(null);
      }
    } catch (error) {
      console.error('Error rejecting request:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Onay Bekliyor</Badge>;
      case 'approved':
        return <Badge variant="default" className="bg-green-100 text-green-800">Onaylandı</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Reddedildi</Badge>;
      default:
        return <Badge variant="outline">Bilinmiyor</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Badge variant="destructive">Acil</Badge>;
      case 'high':
        return <Badge variant="default" className="bg-orange-100 text-orange-800">Yüksek</Badge>;
      case 'medium':
        return <Badge variant="secondary">Orta</Badge>;
      case 'low':
        return <Badge variant="outline">Düşük</Badge>;
      default:
        return <Badge variant="outline">Bilinmiyor</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email_campaign':
        return <Mail className="w-4 h-4 text-blue-600" />;
      case 'social_post':
        return <Share2 className="w-4 h-4 text-purple-600" />;
      case 'ad_campaign':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'content_piece':
        return <MessageSquare className="w-4 h-4 text-orange-600" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTypeName = (type: string) => {
    switch (type) {
      case 'email_campaign':
        return 'Email Kampanyası';
      case 'social_post':
        return 'Sosyal Medya Paylaşımı';
      case 'ad_campaign':
        return 'Reklam Kampanyası';
      case 'content_piece':
        return 'İçerik Parçası';
      default:
        return type;
    }
  };

  const filteredApprovals = approvals.filter(approval => {
    if (filterStatus === 'all') return true;
    return approval.status === filterStatus;
  });

  const pendingCount = approvals.filter(a => a.status === 'pending').length;
  const approvedCount = approvals.filter(a => a.status === 'approved').length;
  const rejectedCount = approvals.filter(a => a.status === 'rejected').length;
  const urgentCount = approvals.filter(a => a.priority === 'urgent' && a.status === 'pending').length;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <span className="text-lg">Onay verileri yükleniyor...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI İçerik Onay Sistemi</h1>
          <p className="text-gray-600 mt-1">
            AI tarafından üretilen içerikleri inceleyin ve onaylayın
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {urgentCount > 0 && (
            <Badge variant="destructive" className="animate-pulse">
              {urgentCount} Acil Onay
            </Badge>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Onay Bekleyen</p>
                <p className="text-2xl font-bold">{pendingCount}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Onaylanan</p>
                <p className="text-2xl font-bold">{approvedCount}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Reddedilen</p>
                <p className="text-2xl font-bold">{rejectedCount}</p>
              </div>
              <XCircle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Acil Onay</p>
                <p className="text-2xl font-bold">{urgentCount}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Approvals List */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Onay İstekleri</CardTitle>
                <div className="flex items-center space-x-2">
                  <Filter className="w-4 h-4 text-gray-400" />
                  <select 
                    value={filterStatus} 
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="text-sm border rounded px-2 py-1"
                  >
                    <option value="all">Tümü</option>
                    <option value="pending">Bekleyen</option>
                    <option value="approved">Onaylanan</option>
                    <option value="rejected">Reddedilen</option>
                  </select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredApprovals.map((approval) => (
                  <div 
                    key={approval.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedApproval?.id === approval.id 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedApproval(approval)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        {getTypeIcon(approval.type)}
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">{approval.title}</h3>
                          <p className="text-sm text-gray-500 mt-1">{getTypeName(approval.type)}</p>
                          <div className="flex items-center space-x-4 mt-2">
                            <div className="flex items-center space-x-1">
                              <User className="w-3 h-3 text-gray-400" />
                              <span className="text-xs text-gray-500">{approval.requestedBy}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Calendar className="w-3 h-3 text-gray-400" />
                              <span className="text-xs text-gray-500">
                                {approval.requestedAt.toLocaleString('tr-TR')}
                              </span>
                            </div>
                            {approval.estimatedReach && (
                              <div className="flex items-center space-x-1">
                                <Eye className="w-3 h-3 text-gray-400" />
                                <span className="text-xs text-gray-500">
                                  {approval.estimatedReach.toLocaleString()} erişim
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-2">
                        {getStatusBadge(approval.status)}
                        {getPriorityBadge(approval.priority)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Approval Detail */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>İnceleme Paneli</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedApproval ? (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">{selectedApproval.title}</h3>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                        {JSON.stringify(selectedApproval.content, null, 2)}
                      </pre>
                    </div>
                  </div>

                  {selectedApproval.status === 'pending' && (
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          İnceleme Yorumu
                        </label>
                        <Textarea
                          value={reviewComments}
                          onChange={(e) => setReviewComments(e.target.value)}
                          placeholder="Onay/red sebebinizi yazın..."
                          rows={3}
                        />
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          onClick={() => handleApprove(selectedApproval.id)}
                          className="flex-1"
                        >
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Onayla
                        </Button>
                        <Button 
                          variant="destructive"
                          onClick={() => handleReject(selectedApproval.id)}
                          className="flex-1"
                        >
                          <XCircle className="w-4 h-4 mr-2" />
                          Reddet
                        </Button>
                      </div>
                    </div>
                  )}

                  {selectedApproval.status !== 'pending' && selectedApproval.comments && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        İnceleme Yorumu
                      </label>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <p className="text-sm text-gray-700">{selectedApproval.comments}</p>
                        <div className="flex items-center space-x-2 mt-2 text-xs text-gray-500">
                          <User className="w-3 h-3" />
                          <span>{selectedApproval.reviewedBy}</span>
                          <span>•</span>
                          <span>{selectedApproval.reviewedAt?.toLocaleString('tr-TR')}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Eye className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">İncelemek için bir onay isteği seçin</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
