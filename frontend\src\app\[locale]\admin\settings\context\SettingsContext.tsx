'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Types
export type SettingsCategory = 'platform' | 'security' | 'business' | 'notification' | 'system' | 'integration';

export interface AdminSetting {
  id: string;
  category: SettingsCategory;
  key: string;
  value: any;
  dataType: 'string' | 'number' | 'boolean' | 'json' | 'array';
  description?: string;
  isSensitive: boolean;
  requiresRestart: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SettingUpdate {
  category: SettingsCategory;
  key: string;
  value: any;
  changeReason?: string;
}

interface SettingsState {
  settings: Record<string, AdminSetting>;
  loading: boolean;
  error: string | null;
  hasUnsavedChanges: boolean;
  pendingChanges: Record<string, any>;
}

type SettingsAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_SETTINGS'; payload: AdminSetting[] }
  | { type: 'UPDATE_SETTING'; payload: { category: string; key: string; value: any } }
  | { type: 'SAVE_SUCCESS' }
  | { type: 'RESET_CATEGORY'; payload: string }
  | { type: 'CLEAR_CHANGES' };

interface SettingsContextType {
  settings: Record<string, AdminSetting>;
  loading: boolean;
  error: string | null;
  hasUnsavedChanges: boolean;
  updateSetting: (category: SettingsCategory, key: string, value: any) => void;
  saveSettings: () => Promise<void>;
  resetCategory: (category: SettingsCategory) => Promise<void>;
  exportSettings: () => Promise<void>;
  importSettings: (file: File) => Promise<void>;
  getSettingValue: (category: SettingsCategory, key: string) => any;
  getCategorySettings: (category: SettingsCategory) => AdminSetting[];
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

const settingsReducer = (state: SettingsState, action: SettingsAction): SettingsState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'SET_SETTINGS':
      const settingsMap = action.payload.reduce((acc, setting) => {
        acc[`${setting.category}.${setting.key}`] = setting;
        return acc;
      }, {} as Record<string, AdminSetting>);
      return { 
        ...state, 
        settings: settingsMap, 
        loading: false, 
        error: null,
        hasUnsavedChanges: false,
        pendingChanges: {}
      };
    
    case 'UPDATE_SETTING':
      const settingKey = `${action.payload.category}.${action.payload.key}`;
      const currentSetting = state.settings[settingKey];
      
      if (!currentSetting) return state;
      
      const updatedSetting = {
        ...currentSetting,
        value: action.payload.value,
        updatedAt: new Date()
      };
      
      return {
        ...state,
        settings: {
          ...state.settings,
          [settingKey]: updatedSetting
        },
        hasUnsavedChanges: true,
        pendingChanges: {
          ...state.pendingChanges,
          [settingKey]: action.payload.value
        }
      };
    
    case 'SAVE_SUCCESS':
      return {
        ...state,
        hasUnsavedChanges: false,
        pendingChanges: {},
        error: null
      };
    
    case 'RESET_CATEGORY':
      // Reset all settings in a category to their default values
      const resetSettings = { ...state.settings };
      Object.keys(resetSettings).forEach(key => {
        if (key.startsWith(`${action.payload}.`)) {
          // In a real implementation, you would reset to default values from schema
          resetSettings[key] = {
            ...resetSettings[key],
            updatedAt: new Date()
          };
        }
      });
      
      return {
        ...state,
        settings: resetSettings,
        hasUnsavedChanges: true
      };
    
    case 'CLEAR_CHANGES':
      return {
        ...state,
        hasUnsavedChanges: false,
        pendingChanges: {}
      };
    
    default:
      return state;
  }
};

export const SettingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(settingsReducer, {
    settings: {},
    loading: true,
    error: null,
    hasUnsavedChanges: false,
    pendingChanges: {}
  });

  // Fetch settings on mount
  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Mock API call - replace with actual API
      const response = await fetch('http://localhost:8002/api/admin/settings', {
        headers: {
          'Authorization': 'Bearer admin-token' // Mock token
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }
      
      const data = await response.json();
      dispatch({ type: 'SET_SETTINGS', payload: data.data || [] });
      
    } catch (error) {
      console.error('Error fetching settings:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Ayarlar yüklenirken hata oluştu' });
      
      // Load mock data for development
      loadMockSettings();
    }
  };

  const loadMockSettings = () => {
    const mockSettings: AdminSetting[] = [
      {
        id: '1',
        category: 'platform',
        key: 'siteName',
        value: 'Doğal Taş Pazaryeri',
        dataType: 'string',
        description: 'Site adı',
        isSensitive: false,
        requiresRestart: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '2',
        category: 'platform',
        key: 'maintenanceMode',
        value: false,
        dataType: 'boolean',
        description: 'Bakım modu durumu',
        isSensitive: false,
        requiresRestart: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '3',
        category: 'business',
        key: 'commissionRateM2',
        value: 1.0,
        dataType: 'number',
        description: 'M² başına komisyon oranı ($)',
        isSensitive: false,
        requiresRestart: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '4',
        category: 'business',
        key: 'commissionRateTon',
        value: 10.0,
        dataType: 'number',
        description: 'Ton başına komisyon oranı ($)',
        isSensitive: false,
        requiresRestart: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '5',
        category: 'security',
        key: 'passwordMinLength',
        value: 8,
        dataType: 'number',
        description: 'Minimum şifre uzunluğu',
        isSensitive: false,
        requiresRestart: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    dispatch({ type: 'SET_SETTINGS', payload: mockSettings });
  };

  const updateSetting = (category: SettingsCategory, key: string, value: any) => {
    dispatch({ type: 'UPDATE_SETTING', payload: { category, key, value } });
  };

  const saveSettings = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Prepare updates from pending changes
      const updates: SettingUpdate[] = Object.entries(state.pendingChanges).map(([settingKey, value]) => {
        const [category, key] = settingKey.split('.');
        return {
          category: category as SettingsCategory,
          key,
          value,
          changeReason: 'Admin panel update'
        };
      });

      const response = await fetch('http://localhost:8002/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ settings: updates })
      });

      if (!response.ok) {
        throw new Error('Failed to save settings');
      }

      const result = await response.json();
      
      if (result.requiresRestart) {
        alert('Bazı ayarlar sistem yeniden başlatılması gerektiriyor.');
      }

      dispatch({ type: 'SAVE_SUCCESS' });
      
    } catch (error) {
      console.error('Error saving settings:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Ayarlar kaydedilirken hata oluştu' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const resetCategory = async (category: SettingsCategory) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const response = await fetch('http://localhost:8002/api/admin/settings/reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ category })
      });

      if (!response.ok) {
        throw new Error('Failed to reset settings');
      }

      dispatch({ type: 'RESET_CATEGORY', payload: category });
      await fetchSettings(); // Reload settings
      
    } catch (error) {
      console.error('Error resetting settings:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Ayarlar sıfırlanırken hata oluştu' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const exportSettings = async () => {
    try {
      const response = await fetch('http://localhost:8002/api/admin/settings/export', {
        headers: {
          'Authorization': 'Bearer admin-token'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to export settings');
      }

      const result = await response.json();
      
      // Download the file
      const blob = new Blob([JSON.stringify(result.data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = result.filename || 'settings-export.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error exporting settings:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Ayarlar dışa aktarılırken hata oluştu' });
    }
  };

  const importSettings = async (file: File) => {
    try {
      const text = await file.text();
      const settingsData = JSON.parse(text);

      const response = await fetch('http://localhost:8002/api/admin/settings/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ settings: settingsData })
      });

      if (!response.ok) {
        throw new Error('Failed to import settings');
      }

      await fetchSettings(); // Reload settings
      
    } catch (error) {
      console.error('Error importing settings:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Ayarlar içe aktarılırken hata oluştu' });
    }
  };

  const getSettingValue = (category: SettingsCategory, key: string): any => {
    const setting = state.settings[`${category}.${key}`];
    return setting ? setting.value : null;
  };

  const getCategorySettings = (category: SettingsCategory): AdminSetting[] => {
    return Object.values(state.settings).filter(setting => setting.category === category);
  };

  const contextValue: SettingsContextType = {
    settings: state.settings,
    loading: state.loading,
    error: state.error,
    hasUnsavedChanges: state.hasUnsavedChanges,
    updateSetting,
    saveSettings,
    resetCategory,
    exportSettings,
    importSettings,
    getSettingValue,
    getCategorySettings
  };

  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = (): SettingsContextType => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};
