// RFC-501: Main Admin Dashboard Component
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Users,
  ShoppingCart,
  DollarSign,
  Clock,
  Activity,
  UserCheck,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Settings,
  FileText,
  MessageSquare,
  CreditCard,
  Factory
} from 'lucide-react';

interface KPICard {
  id: string;
  title: string;
  value: number | string;
  previousValue?: number | string;
  changePercentage?: number;
  trend: 'up' | 'down' | 'stable';
  format: 'number' | 'currency' | 'percentage' | 'duration';
  color: 'green' | 'red' | 'blue' | 'yellow';
  icon: string;
  drillDownUrl?: string;
}

interface DashboardOverview {
  kpiCards: KPICard[];
  realtimeMetrics: RealtimeMetric[];
  alertsPanel: AlertsPanel;
  quickActions: QuickAction[];
  systemHealth: SystemHealth;
}

interface RealtimeMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  trend: number[];
}

interface AlertsPanel {
  criticalAlerts: Alert[];
  warningAlerts: Alert[];
  infoAlerts: Alert[];
  totalCount: number;
}

interface Alert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  actionUrl?: string;
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  url: string;
  badge?: number;
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  responseTime: number;
  errorRate: number;
  activeUsers: number;
  databaseStatus: 'connected' | 'disconnected' | 'slow';
  redisStatus: 'connected' | 'disconnected' | 'slow';
}

const iconMap: { [key: string]: React.ComponentType<any> } = {
  Users,
  UserCheck,
  ShoppingCart,
  DollarSign,
  Clock,
  Activity,
  CreditCard,
  AlertTriangle,
  Settings,
  FileText,
  MessageSquare
};

export default function AdminDashboard() {
  const [dashboardData, setDashboardData] = useState<DashboardOverview | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
    
    // Set up real-time updates every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Mock data - gerçek uygulamada API'den gelecek
      const mockData: DashboardOverview = {
        kpiCards: [
          {
            id: 'total-users',
            title: 'Toplam Kullanıcı',
            value: 1247,
            previousValue: 1180,
            changePercentage: 5.7,
            trend: 'up',
            format: 'number',
            color: 'blue',
            icon: 'Users',
            drillDownUrl: '/admin/customers'
          },
          {
            id: 'active-orders',
            title: 'Aktif Siparişler',
            value: 89,
            previousValue: 76,
            changePercentage: 17.1,
            trend: 'up',
            format: 'number',
            color: 'green',
            icon: 'ShoppingCart'
          },
          {
            id: 'monthly-revenue',
            title: 'Aylık Gelir',
            value: 245000,
            previousValue: 220000,
            changePercentage: 11.4,
            trend: 'up',
            format: 'currency',
            color: 'green',
            icon: 'DollarSign'
          },
          {
            id: 'pending-approvals',
            title: 'Bekleyen Onaylar',
            value: 12,
            previousValue: 8,
            changePercentage: 50.0,
            trend: 'up',
            format: 'number',
            color: 'yellow',
            icon: 'Clock'
          },
          {
            id: 'conversion-rate',
            title: 'Dönüşüm Oranı',
            value: 24.8,
            previousValue: 22.1,
            changePercentage: 12.2,
            trend: 'up',
            format: 'percentage',
            color: 'blue',
            icon: 'Activity'
          },
          {
            id: 'verified-producers',
            title: 'Onaylı Üreticiler',
            value: 156,
            previousValue: 148,
            changePercentage: 5.4,
            trend: 'up',
            format: 'number',
            color: 'green',
            icon: 'UserCheck'
          }
        ],
        realtimeMetrics: [
          {
            id: 'active-sessions',
            name: 'Aktif Oturumlar',
            value: 234,
            unit: 'kullanıcı',
            timestamp: new Date(),
            trend: [180, 195, 210, 225, 234]
          },
          {
            id: 'quote-requests',
            name: 'Günlük Teklif Talepleri',
            value: 45,
            unit: 'talep',
            timestamp: new Date(),
            trend: [32, 38, 41, 43, 45]
          }
        ],
        alertsPanel: {
          criticalAlerts: [
            {
              id: 'alert-1',
              type: 'critical',
              title: 'Düşük Stok Uyarısı',
              message: '3 ürünün stoku kritik seviyede',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
              acknowledged: false,
              actionUrl: '/admin/products'
            }
          ],
          warningAlerts: [
            {
              id: 'alert-2',
              type: 'warning',
              title: 'Bekleyen Ürün Onayları',
              message: '12 ürün onay bekliyor',
              timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
              acknowledged: false,
              actionUrl: '/admin/product-approvals'
            },
            {
              id: 'alert-3',
              type: 'warning',
              title: 'Yavaş Yanıt Süreleri',
              message: '2 üretici 24 saatten uzun süredir yanıt vermiyor',
              timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
              acknowledged: false
            }
          ],
          infoAlerts: [
            {
              id: 'alert-4',
              type: 'info',
              title: 'Yeni Müşteri Kaydı',
              message: 'Son 24 saatte 8 yeni müşteri kaydı',
              timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
              acknowledged: true
            }
          ],
          totalCount: 3
        },
        quickActions: [
          {
            id: 'approve-products',
            title: 'Ürün Onayları',
            description: 'Bekleyen ürün onaylarını incele',
            icon: 'FileText',
            url: '/admin/product-approvals',
            badge: 12
          },
          {
            id: 'manage-customers',
            title: 'Müşteri Yönetimi',
            description: 'Müşteri profillerini yönet',
            icon: 'Users',
            url: '/admin/customers'
          },
          {
            id: 'manage-producers',
            title: 'Üretici Yönetimi',
            description: 'Üretici profillerini yönet',
            icon: 'Factory',
            url: '/admin/producers'
          },
          {
            id: 'quote-requests',
            title: 'Güncel Talepler',
            description: 'Teklif taleplerini izle',
            icon: 'MessageSquare',
            url: '/admin/quote-requests',
            badge: 8
          },
          {
            id: 'system-settings',
            title: 'Sistem Ayarları',
            description: 'Platform ayarlarını düzenle',
            icon: 'Settings',
            url: '/admin/settings'
          }
        ],
        systemHealth: {
          status: 'healthy',
          uptime: 99.8,
          responseTime: 245,
          errorRate: 0.02,
          activeUsers: 234,
          databaseStatus: 'connected',
          redisStatus: 'connected'
        }
      };

      setDashboardData(mockData);
      setError(null);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('Dashboard verisi yüklenirken hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const formatValue = (value: number | string, format: string): string => {
    if (typeof value === 'string') return value;
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(value);
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'number':
        return new Intl.NumberFormat('en-US').format(value);
      default:
        return value.toString();
    }
  };

  const getTrendIcon = (trend: string, changePercentage?: number) => {
    if (trend === 'up' || (changePercentage && changePercentage > 0)) {
      return <TrendingUp className="w-4 h-4 text-green-600" />;
    } else if (trend === 'down' || (changePercentage && changePercentage < 0)) {
      return <TrendingDown className="w-4 h-4 text-red-600" />;
    }
    return null;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
        return 'text-green-600';
      case 'warning':
      case 'slow':
        return 'text-yellow-600';
      case 'critical':
      case 'disconnected':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'info':
        return <CheckCircle className="w-4 h-4 text-blue-600" />;
      default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Dashboard yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="m-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!dashboardData) {
    return (
      <Alert className="m-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>No dashboard data available</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Yönetici Paneli</h1>
          <p className="text-muted-foreground">
            Marketplace platformunuzu izleyin ve yönetin
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge
            variant={dashboardData.systemHealth.status === 'healthy' ? 'default' : 'destructive'}
            className="text-sm"
          >
            Sistem {dashboardData.systemHealth.status === 'healthy' ? 'Sağlıklı' : dashboardData.systemHealth.status === 'warning' ? 'Uyarı' : 'Kritik'}
          </Badge>
          <Button variant="outline" onClick={fetchDashboardData}>
            Yenile
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {dashboardData.kpiCards.map((kpi) => {
          const IconComponent = iconMap[kpi.icon];
          return (
            <Card key={kpi.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">{kpi.title}</p>
                    <p className="text-2xl font-bold">
                      {formatValue(kpi.value, kpi.format)}
                    </p>
                    {kpi.changePercentage !== undefined && (
                      <div className="flex items-center space-x-1 text-sm">
                        {getTrendIcon(kpi.trend, kpi.changePercentage)}
                        <span className={
                          kpi.changePercentage > 0 ? 'text-green-600' : 
                          kpi.changePercentage < 0 ? 'text-red-600' : 'text-gray-600'
                        }>
                          {Math.abs(kpi.changePercentage).toFixed(1)}%
                        </span>
                      </div>
                    )}
                  </div>
                  {IconComponent && (
                    <IconComponent className={`w-8 h-8 text-${kpi.color}-600`} />
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
          <TabsTrigger value="alerts">
            Uyarılar
            {dashboardData.alertsPanel.totalCount > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {dashboardData.alertsPanel.totalCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="metrics">Gerçek Zamanlı Metrikler</TabsTrigger>
          <TabsTrigger value="actions">Hızlı İşlemler</TabsTrigger>
          <TabsTrigger value="system">Sistem Sağlığı</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Hızlı İşlemler</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {dashboardData.quickActions.map((action) => {
                  const IconComponent = iconMap[action.icon];
                  return (
                    <div key={action.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center space-x-3">
                        {IconComponent && <IconComponent className="w-5 h-5 text-blue-600" />}
                        <div>
                          <p className="font-medium">{action.title}</p>
                          <p className="text-sm text-muted-foreground">{action.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {action.badge && action.badge > 0 && (
                          <Badge variant="secondary">{action.badge}</Badge>
                        )}
                        <Button variant="outline" size="sm">
                          Git
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>

            {/* Recent Alerts */}
            <Card>
              <CardHeader>
                <CardTitle>Son Uyarılar</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {[
                  ...dashboardData.alertsPanel.criticalAlerts.slice(0, 2),
                  ...dashboardData.alertsPanel.warningAlerts.slice(0, 2),
                  ...dashboardData.alertsPanel.infoAlerts.slice(0, 1)
                ].map((alert) => (
                  <div key={alert.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1">
                      <p className="font-medium">{alert.title}</p>
                      <p className="text-sm text-muted-foreground">{alert.message}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {new Date(alert.timestamp).toLocaleString('tr-TR')}
                      </p>
                    </div>
                  </div>
                ))}
                {dashboardData.alertsPanel.totalCount === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-600" />
                    <p>Aktif uyarı yok</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Other tab contents would be implemented here */}
        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle>Sistem Uyarıları</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Uyarı yönetim arayüzü yakında geliyor...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics">
          <Card>
            <CardHeader>
              <CardTitle>Gerçek Zamanlı Metrikler</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Gerçek zamanlı metrik dashboard'u yakında geliyor...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="actions">
          <Card>
            <CardHeader>
              <CardTitle>Yönetici İşlemleri</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Yönetici işlemleri paneli yakında geliyor...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system">
          <Card>
            <CardHeader>
              <CardTitle>Sistem Sağlığı</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Veritabanı Durumu</p>
                  <p className={`text-sm ${getStatusColor(dashboardData.systemHealth.databaseStatus)}`}>
                    {dashboardData.systemHealth.databaseStatus === 'connected' ? 'Bağlı' : dashboardData.systemHealth.databaseStatus === 'slow' ? 'Yavaş' : 'Bağlantı Kesildi'}
                  </p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Redis Durumu</p>
                  <p className={`text-sm ${getStatusColor(dashboardData.systemHealth.redisStatus)}`}>
                    {dashboardData.systemHealth.redisStatus === 'connected' ? 'Bağlı' : dashboardData.systemHealth.redisStatus === 'slow' ? 'Yavaş' : 'Bağlantı Kesildi'}
                  </p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Çalışma Süresi</p>
                  <p className="text-sm">{dashboardData.systemHealth.uptime}%</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Aktif Kullanıcılar</p>
                  <p className="text-sm">{dashboardData.systemHealth.activeUsers}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
