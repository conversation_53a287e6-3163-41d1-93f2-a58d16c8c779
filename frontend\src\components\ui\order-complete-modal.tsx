'use client'

import * as React from 'react'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { CheckCircle, Package, Truck, Calendar, AlertCircle } from 'lucide-react'

interface OrderCompleteModalProps {
  isOpen: boolean
  onClose: () => void
  order: any
}

const deliveryMethods = [
  { value: 'factory_pickup', label: '<PERSON><PERSON><PERSON> (Müşteri Alır)' },
  { value: 'delivery', label: 'Teslimat (Kargo/Nakliye)' },
  { value: 'partial_delivery', label: 'Kısmi Teslimat' }
]

export function OrderCompleteModal({
  isOpen,
  onClose,
  order
}: OrderCompleteModalProps) {
  const [deliveryMethod, setDeliveryMethod] = React.useState('')
  const [deliveryDate, setDeliveryDate] = React.useState('')
  const [trackingNumber, setTrackingNumber] = React.useState('')
  const [completionNotes, setCompletionNotes] = React.useState('')
  const [qualityCheckPassed, setQualityCheckPassed] = React.useState(false)
  const [isLoading, setIsLoading] = React.useState(false)

  const handleCompleteOrder = async () => {
    if (!deliveryMethod) {
      alert('Lütfen teslimat yöntemini seçin')
      return
    }

    if (!deliveryDate) {
      alert('Lütfen teslimat tarihini belirtin')
      return
    }

    if (!qualityCheckPassed) {
      alert('Kalite kontrolü onaylanmadan sipariş tamamlanamaz')
      return
    }

    setIsLoading(true)
    try {
      // Here you would complete the order in your backend
      console.log('Completing order:', {
        orderId: order?.id,
        deliveryMethod,
        deliveryDate,
        trackingNumber,
        completionNotes,
        qualityCheckPassed
      })

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Show success message
      alert('Sipariş başarıyla tamamlandı!')
      
      // Reset form and close modal
      resetForm()
      onClose()
    } catch (error) {
      console.error('Error completing order:', error)
      alert('Sipariş tamamlanırken bir hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const resetForm = () => {
    setDeliveryMethod('')
    setDeliveryDate('')
    setTrackingNumber('')
    setCompletionNotes('')
    setQualityCheckPassed(false)
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  if (!order) return null

  // Get today's date for minimum date selection
  const today = new Date().toISOString().split('T')[0]

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            Siparişi Tamamla
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Summary */}
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <h3 className="font-semibold text-green-800 mb-2 flex items-center gap-2">
              <Package className="w-4 h-4" />
              Sipariş Özeti
            </h3>
            <div className="text-sm space-y-1">
              <div><span className="font-medium">Sipariş:</span> #{order.id}</div>
              <div><span className="font-medium">Müşteri:</span> {order.customerName}</div>
              <div><span className="font-medium">Ürün:</span> {order.productName}</div>
              <div><span className="font-medium">Miktar:</span> {order.quantity} m²</div>
            </div>
          </div>

          {/* Production Status Check */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-800 mb-3">Üretim Durumu</h3>
            <div className="space-y-2">
              {order.productionStages?.map((stage: any, index: number) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <CheckCircle className={`w-4 h-4 ${stage.completed ? 'text-green-600' : 'text-gray-400'}`} />
                  <span className={stage.completed ? 'text-green-700' : 'text-gray-500'}>
                    {stage.stage}
                  </span>
                  {stage.completed && stage.date && (
                    <span className="text-xs text-gray-500">({stage.date})</span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Quality Check */}
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-semibold text-yellow-800 mb-2">Kalite Kontrolü</h3>
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={qualityCheckPassed}
                    onChange={(e) => setQualityCheckPassed(e.target.checked)}
                    className="rounded border-yellow-300 text-yellow-600 focus:ring-yellow-500"
                  />
                  <span className="text-sm text-yellow-700">
                    Kalite kontrolü yapıldı ve ürün standartlara uygun
                  </span>
                </label>
              </div>
            </div>
          </div>

          {/* Delivery Method */}
          <div>
            <Label htmlFor="deliveryMethod" className="text-sm font-medium text-gray-700">
              Teslimat Yöntemi *
            </Label>
            <Select value={deliveryMethod} onValueChange={setDeliveryMethod}>
              <SelectTrigger className="mt-1 h-10">
                <SelectValue placeholder="Teslimat yöntemini seçin" />
              </SelectTrigger>
              <SelectContent className="z-50">
                {deliveryMethods.map((method) => (
                  <SelectItem key={method.value} value={method.value} className="py-2">
                    {method.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Delivery Date */}
          <div>
            <Label htmlFor="deliveryDate" className="text-sm font-medium text-gray-700">
              Teslimat Tarihi *
            </Label>
            <Input
              id="deliveryDate"
              type="date"
              value={deliveryDate}
              onChange={(e) => setDeliveryDate(e.target.value)}
              min={today}
              className="mt-1"
              disabled={isLoading}
            />
          </div>

          {/* Tracking Number (if delivery selected) */}
          {deliveryMethod === 'delivery' && (
            <div>
              <Label htmlFor="trackingNumber" className="text-sm font-medium text-gray-700">
                Kargo Takip Numarası
              </Label>
              <Input
                id="trackingNumber"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                placeholder="Kargo takip numarasını girin"
                className="mt-1"
                disabled={isLoading}
              />
            </div>
          )}

          {/* Completion Notes */}
          <div>
            <Label htmlFor="completionNotes" className="text-sm font-medium text-gray-700">
              Tamamlama Notları
            </Label>
            <Textarea
              id="completionNotes"
              placeholder="Sipariş tamamlama ile ilgili notlar..."
              value={completionNotes}
              onChange={(e) => setCompletionNotes(e.target.value)}
              rows={3}
              className="mt-1"
              disabled={isLoading}
            />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              İptal
            </Button>
            <Button
              onClick={handleCompleteOrder}
              disabled={!deliveryMethod || !deliveryDate || !qualityCheckPassed || isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Tamamlanıyor...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Siparişi Tamamla
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
