'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  FileText, 
  Calendar, 
  Clock, 
  Mail, 
  Settings, 
  Play, 
  Pause, 
  Trash2,
  Plus,
  Edit,
  Copy,
  Download,
  BarChart3,
  DollarSign,
  Users,
  Activity
} from 'lucide-react';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: 'financial' | 'business' | 'users' | 'system';
  chartType: 'line' | 'bar' | 'pie' | 'doughnut' | 'table';
  fields: string[];
  filters: any[];
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
    time: string;
    recipients: string[];
    enabled: boolean;
  };
  lastRun?: Date;
  nextRun?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface ReportTemplatesProps {
  onCreateReport: (template: ReportTemplate) => void;
  onEditTemplate: (template: ReportTemplate) => void;
}

const predefinedTemplates: ReportTemplate[] = [
  {
    id: 'financial-overview',
    name: 'Finansal Genel Bakış',
    description: 'Gelir, komisyon ve ödeme analizleri',
    category: 'financial',
    chartType: 'line',
    fields: ['revenue', 'commission', 'payment_amount'],
    filters: [],
    schedule: {
      frequency: 'daily',
      time: '09:00',
      recipients: ['<EMAIL>'],
      enabled: true
    },
    lastRun: new Date(Date.now() - 24 * 60 * 60 * 1000),
    nextRun: new Date(Date.now() + 24 * 60 * 60 * 1000),
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'business-performance',
    name: 'İş Performansı',
    description: 'Sipariş, teklif ve dönüşüm oranları',
    category: 'business',
    chartType: 'bar',
    fields: ['order_count', 'quote_count', 'conversion_rate'],
    filters: [],
    schedule: {
      frequency: 'weekly',
      time: '08:00',
      recipients: ['<EMAIL>'],
      enabled: true
    },
    lastRun: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    nextRun: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'user-analytics',
    name: 'Kullanıcı Analitiği',
    description: 'Kullanıcı büyümesi ve aktivite analizi',
    category: 'users',
    chartType: 'pie',
    fields: ['user_count', 'customer_count', 'producer_count'],
    filters: [],
    schedule: {
      frequency: 'monthly',
      time: '10:00',
      recipients: ['<EMAIL>'],
      enabled: false
    },
    isActive: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'system-health',
    name: 'Sistem Sağlığı',
    description: 'Sistem performansı ve kaynak kullanımı',
    category: 'system',
    chartType: 'line',
    fields: ['response_time', 'cpu_usage', 'memory_usage'],
    filters: [],
    schedule: {
      frequency: 'daily',
      time: '06:00',
      recipients: ['<EMAIL>'],
      enabled: true
    },
    lastRun: new Date(Date.now() - 24 * 60 * 60 * 1000),
    nextRun: new Date(Date.now() + 24 * 60 * 60 * 1000),
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  }
];

export default function ReportTemplates({ onCreateReport, onEditTemplate }: ReportTemplatesProps) {
  const [templates, setTemplates] = useState<ReportTemplate[]>(predefinedTemplates);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [scheduleConfig, setScheduleConfig] = useState({
    frequency: 'weekly' as const,
    time: '09:00',
    recipients: '',
    enabled: true
  });

  const categoryIcons = {
    financial: DollarSign,
    business: BarChart3,
    users: Users,
    system: Activity
  };

  const categoryColors = {
    financial: 'bg-green-100 text-green-800',
    business: 'bg-blue-100 text-blue-800',
    users: 'bg-purple-100 text-purple-800',
    system: 'bg-orange-100 text-orange-800'
  };

  const handleToggleSchedule = (templateId: string) => {
    setTemplates(prev => prev.map(template => 
      template.id === templateId 
        ? { 
            ...template, 
            schedule: template.schedule 
              ? { ...template.schedule, enabled: !template.schedule.enabled }
              : undefined
          }
        : template
    ));
  };

  const handleRunNow = (template: ReportTemplate) => {
    console.log('Running report now:', template.name);
    onCreateReport(template);
    
    // Update last run time
    setTemplates(prev => prev.map(t => 
      t.id === template.id 
        ? { ...t, lastRun: new Date() }
        : t
    ));
  };

  const handleEditSchedule = (template: ReportTemplate) => {
    setSelectedTemplate(template);
    if (template.schedule) {
      setScheduleConfig({
        frequency: template.schedule.frequency,
        time: template.schedule.time,
        recipients: template.schedule.recipients.join(', '),
        enabled: template.schedule.enabled
      });
    }
    setShowScheduleModal(true);
  };

  const handleSaveSchedule = () => {
    if (!selectedTemplate) return;

    const updatedTemplate = {
      ...selectedTemplate,
      schedule: {
        frequency: scheduleConfig.frequency,
        time: scheduleConfig.time,
        recipients: scheduleConfig.recipients.split(',').map(email => email.trim()),
        enabled: scheduleConfig.enabled
      },
      updatedAt: new Date()
    };

    setTemplates(prev => prev.map(t => 
      t.id === selectedTemplate.id ? updatedTemplate : t
    ));

    setShowScheduleModal(false);
    setSelectedTemplate(null);
  };

  const handleDeleteTemplate = (templateId: string) => {
    setTemplates(prev => prev.filter(t => t.id !== templateId));
  };

  const handleDuplicateTemplate = (template: ReportTemplate) => {
    const duplicated = {
      ...template,
      id: `${template.id}-copy-${Date.now()}`,
      name: `${template.name} (Kopya)`,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: false,
      schedule: template.schedule ? { ...template.schedule, enabled: false } : undefined
    };

    setTemplates(prev => [...prev, duplicated]);
  };

  const getNextRunText = (template: ReportTemplate) => {
    if (!template.schedule?.enabled || !template.nextRun) {
      return 'Zamanlanmamış';
    }

    const now = new Date();
    const diff = template.nextRun.getTime() - now.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} gün sonra`;
    } else if (hours > 0) {
      return `${hours} saat sonra`;
    } else {
      return 'Yakında';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Rapor Şablonları</h2>
          <p className="text-gray-600 mt-1">
            Hazır şablonları kullanın veya otomatik raporlar oluşturun
          </p>
        </div>
        <Button onClick={() => onEditTemplate({} as ReportTemplate)}>
          <Plus className="w-4 h-4 mr-2" />
          Yeni Şablon
        </Button>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map((template) => {
          const IconComponent = categoryIcons[template.category];
          return (
            <Card key={template.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${categoryColors[template.category]}`}>
                      <IconComponent className="w-5 h-5" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                    </div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Status and Schedule Info */}
                <div className="flex items-center justify-between">
                  <Badge variant={template.isActive ? "default" : "secondary"}>
                    {template.isActive ? 'Aktif' : 'Pasif'}
                  </Badge>
                  {template.schedule && (
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Clock className="w-3 h-3" />
                      {getNextRunText(template)}
                    </div>
                  )}
                </div>

                {/* Schedule Info */}
                {template.schedule && (
                  <div className="text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <span className="capitalize">{template.schedule.frequency}</span>
                      <span>• {template.schedule.time}</span>
                      {template.schedule.enabled ? (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          Zamanlanmış
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-gray-600">
                          Durdurulmuş
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* Last Run */}
                {template.lastRun && (
                  <div className="text-xs text-gray-500">
                    Son çalıştırma: {template.lastRun.toLocaleDateString('tr-TR')} {template.lastRun.toLocaleTimeString('tr-TR')}
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center gap-2 pt-2 border-t">
                  <Button size="sm" onClick={() => handleRunNow(template)}>
                    <Play className="w-3 h-3 mr-1" />
                    Çalıştır
                  </Button>
                  
                  <Button size="sm" variant="outline" onClick={() => onEditTemplate(template)}>
                    <Edit className="w-3 h-3 mr-1" />
                    Düzenle
                  </Button>
                  
                  <Button size="sm" variant="outline" onClick={() => handleDuplicateTemplate(template)}>
                    <Copy className="w-3 h-3 mr-1" />
                    Kopyala
                  </Button>

                  {template.schedule && (
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => handleEditSchedule(template)}
                    >
                      <Settings className="w-3 h-3 mr-1" />
                      Zamanlama
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Schedule Modal */}
      <Dialog open={showScheduleModal} onOpenChange={setShowScheduleModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Rapor Zamanlaması</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Sıklık</Label>
              <Select 
                value={scheduleConfig.frequency} 
                onValueChange={(value: any) => setScheduleConfig(prev => ({ ...prev, frequency: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Günlük</SelectItem>
                  <SelectItem value="weekly">Haftalık</SelectItem>
                  <SelectItem value="monthly">Aylık</SelectItem>
                  <SelectItem value="quarterly">Çeyreklik</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Saat</Label>
              <Input
                type="time"
                value={scheduleConfig.time}
                onChange={(e) => setScheduleConfig(prev => ({ ...prev, time: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Email Alıcıları</Label>
              <Input
                placeholder="<EMAIL>, <EMAIL>"
                value={scheduleConfig.recipients}
                onChange={(e) => setScheduleConfig(prev => ({ ...prev, recipients: e.target.value }))}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="enabled"
                checked={scheduleConfig.enabled}
                onCheckedChange={(checked) => setScheduleConfig(prev => ({ ...prev, enabled: !!checked }))}
              />
              <Label htmlFor="enabled">Zamanlamayı etkinleştir</Label>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setShowScheduleModal(false)}>
                İptal
              </Button>
              <Button onClick={handleSaveSchedule}>
                Kaydet
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
