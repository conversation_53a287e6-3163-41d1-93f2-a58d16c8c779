import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { fn } from '@storybook/test'
import ProductCard from './product-card'

const mockProduct = {
  id: 'carrara-marble-001',
  name: 'Carrara Beyaz Mermer',
  category: 'Mermer',
  price: { min: 45, max: 65, currency: '$', unit: 'm²' },
  image: 'https://images.unsplash.com/photo-1615971677499-5467cbab01c0?w=400&h=300&fit=crop',
  rating: 4.8,
  reviewCount: 127,
  location: 'Afyon, Türkiye',
  producer: 'ABC Mermer Ltd.',
  features: ['Cilalı', '60x60cm', '2cm Kalınlık', '<PERSON>üksek Kalite']
}

/**
 * ProductCard component for displaying natural stone products
 * 
 * A specialized card component designed for the Turkish Natural Stone Marketplace
 * that showcases product information with 3D viewing capabilities, ratings,
 * and marketplace-specific actions.
 */
const meta = {
  title: 'UI/ProductCard',
  component: ProductCard,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
The ProductCard component is specifically designed for displaying natural stone products
in the marketplace. It includes features like:

## Features
- Product image with lazy loading
- 3D viewer integration
- Rating and review display
- Location and producer information
- Feature tags with overflow handling
- Hover animations and overlay actions
- Favorite functionality
- Responsive design
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    product: {
      description: 'Product data object',
      control: 'object',
    },
    onViewDetails: {
      description: 'Callback when product details are requested',
      action: 'view-details',
    },
    onRequestQuote: {
      description: 'Callback when quote is requested',
      action: 'request-quote',
    },
    onToggleFavorite: {
      description: 'Callback when favorite is toggled',
      action: 'toggle-favorite',
    },
    isFavorite: {
      control: 'boolean',
      description: 'Whether the product is favorited',
    },
    show3DViewer: {
      control: 'boolean',
      description: 'Whether to show 3D viewer button',
    },
  },
  args: {
    product: mockProduct,
    onViewDetails: fn(),
    onRequestQuote: fn(),
    onToggleFavorite: fn(),
    isFavorite: false,
    show3DViewer: true,
  },
} satisfies Meta<typeof ProductCard>

export default meta
type Story = StoryObj<typeof meta>

/**
 * Default product card with all features
 */
export const Default: Story = {}

/**
 * Product card in favorite state
 */
export const Favorited: Story = {
  args: {
    isFavorite: true,
  },
}

/**
 * Product card without 3D viewer
 */
export const No3DViewer: Story = {
  args: {
    show3DViewer: false,
  },
}

/**
 * Product with minimal information
 */
export const MinimalProduct: Story = {
  args: {
    product: {
      id: 'basic-stone',
      name: 'Temel Doğal Taş',
      category: 'Granit',
      price: { min: 25, max: 25, currency: '$', unit: 'm²' },
      image: 'https://images.unsplash.com/photo-1615971677499-5467cbab01c0?w=400&h=300&fit=crop',
    },
  },
}

/**
 * Product with long name and many features
 */
export const LongContent: Story = {
  args: {
    product: {
      ...mockProduct,
      name: 'Çok Uzun Ürün Adı ile Birlikte Detaylı Açıklama ve Özellikler',
      features: [
        'Cilalı Yüzey',
        '60x60cm Ebat',
        '2cm Kalınlık',
        'Yüksek Kalite',
        'Su Geçirmez',
        'Donmaya Dayanıklı',
        'UV Direnci'
      ]
    },
  },
}

/**
 * Different stone categories
 */
export const StoneCategories: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl">
      <ProductCard
        product={{
          ...mockProduct,
          id: 'marble-1',
          name: 'Carrara Beyaz Mermer',
          category: 'Mermer',
          image: 'https://images.unsplash.com/photo-1615971677499-5467cbab01c0?w=400&h=300&fit=crop',
        }}
        onViewDetails={fn()}
        onRequestQuote={fn()}
        onToggleFavorite={fn()}
      />
      <ProductCard
        product={{
          ...mockProduct,
          id: 'granite-1',
          name: 'Siyah Granit',
          category: 'Granit',
          price: { min: 35, max: 55, currency: '$', unit: 'm²' },
          image: 'https://images.unsplash.com/photo-1615971677499-5467cbab01c0?w=400&h=300&fit=crop',
          location: 'Kayseri, Türkiye',
          producer: 'XYZ Granit A.Ş.',
        }}
        onViewDetails={fn()}
        onRequestQuote={fn()}
        onToggleFavorite={fn()}
      />
      <ProductCard
        product={{
          ...mockProduct,
          id: 'travertine-1',
          name: 'Bej Traverten',
          category: 'Traverten',
          price: { min: 25, max: 40, currency: '$', unit: 'm²' },
          image: 'https://images.unsplash.com/photo-1615971677499-5467cbab01c0?w=400&h=300&fit=crop',
          location: 'Denizli, Türkiye',
          producer: 'Traverten Pro Ltd.',
          rating: 4.6,
          reviewCount: 89,
        }}
        onViewDetails={fn()}
        onRequestQuote={fn()}
        onToggleFavorite={fn()}
      />
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'Different types of natural stone products in a grid layout.',
      },
    },
  },
}

/**
 * Price variations
 */
export const PriceVariations: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl">
      <ProductCard
        product={{
          ...mockProduct,
          id: 'budget-stone',
          name: 'Ekonomik Seçenek',
          price: { min: 15, max: 25, currency: '$', unit: 'm²' },
        }}
        onViewDetails={fn()}
        onRequestQuote={fn()}
        onToggleFavorite={fn()}
      />
      <ProductCard
        product={{
          ...mockProduct,
          id: 'premium-stone',
          name: 'Premium Kalite',
          price: { min: 85, max: 120, currency: '$', unit: 'm²' },
        }}
        onViewDetails={fn()}
        onRequestQuote={fn()}
        onToggleFavorite={fn()}
      />
      <ProductCard
        product={{
          ...mockProduct,
          id: 'fixed-price',
          name: 'Sabit Fiyat',
          price: { min: 50, max: 50, currency: '€', unit: 'm²' },
        }}
        onViewDetails={fn()}
        onRequestQuote={fn()}
        onToggleFavorite={fn()}
      />
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'Different pricing scenarios including ranges and fixed prices.',
      },
    },
  },
}

/**
 * Interactive states demonstration
 */
export const InteractiveStates: Story = {
  render: () => {
    const [favorites, setFavorites] = React.useState<string[]>([])
    
    const toggleFavorite = (productId: string) => {
      setFavorites(prev => 
        prev.includes(productId) 
          ? prev.filter(id => id !== productId)
          : [...prev, productId]
      )
    }
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl">
        <ProductCard
          product={{ ...mockProduct, id: 'interactive-1' }}
          isFavorite={favorites.includes('interactive-1')}
          onViewDetails={(id) => alert(`Viewing details for ${id}`)}
          onRequestQuote={(id) => alert(`Requesting quote for ${id}`)}
          onToggleFavorite={toggleFavorite}
        />
        <ProductCard
          product={{ ...mockProduct, id: 'interactive-2', name: 'İkinci Ürün' }}
          isFavorite={favorites.includes('interactive-2')}
          onViewDetails={(id) => alert(`Viewing details for ${id}`)}
          onRequestQuote={(id) => alert(`Requesting quote for ${id}`)}
          onToggleFavorite={toggleFavorite}
        />
      </div>
    )
  },
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'Interactive demonstration with working favorite toggle.',
      },
    },
  },
}

/**
 * Mobile responsive view
 */
export const MobileView: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
    docs: {
      description: {
        story: 'How the product card appears on mobile devices.',
      },
    },
  },
}

/**
 * Accessibility features
 */
export const Accessibility: Story = {
  args: {
    product: {
      ...mockProduct,
      name: 'Erişilebilir Ürün Kartı',
    },
  },
  parameters: {
    docs: {
      description: {
        story: `
This story demonstrates the accessibility features of the ProductCard:
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly content
- Focus indicators
- Semantic HTML structure
        `,
      },
    },
  },
}
