'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Search,
  Filter,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  User,
  Calendar,
  DollarSign,
  AlertTriangle
} from 'lucide-react'
import { BankTransferApprovalModal } from '@/components/admin/BankTransferApprovalModal'

// Mock data - gerçek API'den gelecek
const mockBankTransfers = [
  {
    id: 'bt_001',
    paymentId: 'pay_12345',
    customerName: 'Marble Export Ltd.',
    customerEmail: '<EMAIL>',
    amount: 15000,
    currency: 'USD',
    uploadedAt: '2025-01-02T10:30:00Z',
    status: 'pending',
    receiptUrl: '/receipts/receipt_001.pdf',
    bankDetails: {
      senderName: 'Marble Export Ltd.',
      senderAccount: 'TR33 0006 1005 1978 6457 8413 26',
      receiverAccount: 'TR64 0001 2009 4520 0058 0000 01',
      amount: 15000,
      transferDate: '2025-01-02',
      referenceNumber: 'REF123456789'
    },
    orderInfo: {
      orderId: 'ORD_001',
      productName: 'Beyaz Mermer - Afyon',
      quantity: '150 m²'
    }
  },
  {
    id: 'bt_002',
    paymentId: 'pay_12346',
    customerName: 'Stone Trading Co.',
    customerEmail: '<EMAIL>',
    amount: 8500,
    currency: 'USD',
    uploadedAt: '2025-01-02T09:15:00Z',
    status: 'pending',
    receiptUrl: '/receipts/receipt_002.pdf',
    bankDetails: {
      senderName: 'Stone Trading Co.',
      senderAccount: 'TR55 0004 6007 8901 2345 6789 01',
      receiverAccount: 'TR64 0001 2009 4520 0058 0000 01',
      amount: 8500,
      transferDate: '2025-01-02',
      referenceNumber: 'REF987654321'
    },
    orderInfo: {
      orderId: 'ORD_002',
      productName: 'Traverten - Denizli',
      quantity: '85 m²'
    }
  },
  {
    id: 'bt_003',
    paymentId: 'pay_12347',
    customerName: 'Natural Stone Inc.',
    customerEmail: '<EMAIL>',
    amount: 22000,
    currency: 'USD',
    uploadedAt: '2025-01-01T16:45:00Z',
    status: 'verified',
    receiptUrl: '/receipts/receipt_003.pdf',
    verifiedAt: '2025-01-02T08:30:00Z',
    verifiedBy: 'admin_001',
    bankDetails: {
      senderName: 'Natural Stone Inc.',
      senderAccount: 'TR77 0012 3456 7890 1234 5678 90',
      receiverAccount: 'TR64 0001 2009 4520 0058 0000 01',
      amount: 22000,
      transferDate: '2025-01-01',
      referenceNumber: 'REF456789123'
    },
    orderInfo: {
      orderId: 'ORD_003',
      productName: 'Granit - Çanakkale',
      quantity: '220 m²'
    }
  }
]

const mockStats = {
  pending: 12,
  verified: 45,
  rejected: 3,
  totalAmount: 125000
}

export default function BankTransfersPage() {
  const [selectedTransfer, setSelectedTransfer] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [transfers, setTransfers] = useState(mockBankTransfers)
  const [isLoading, setIsLoading] = useState(false)

  const handleQuickApprove = async (transferId: string) => {
    setIsLoading(true)
    try {
      // API çağrısı simülasyonu
      await new Promise(resolve => setTimeout(resolve, 1000))

      setTransfers(prev => prev.map(transfer =>
        transfer.id === transferId
          ? { ...transfer, status: 'verified', verifiedAt: new Date().toISOString(), verifiedBy: 'admin_001' }
          : transfer
      ))

      // Transfer approved successfully
    } catch (error) {
      // Error handling - could be logged to monitoring service
    } finally {
      setIsLoading(false)
    }
  }

  const handleQuickReject = async (transferId: string) => {
    const reason = prompt('Red sebebini belirtin:')
    if (!reason) return

    setIsLoading(true)
    try {
      // API çağrısı simülasyonu
      await new Promise(resolve => setTimeout(resolve, 1000))

      setTransfers(prev => prev.map(transfer =>
        transfer.id === transferId
          ? { ...transfer, status: 'rejected', rejectedAt: new Date().toISOString(), rejectedBy: 'admin_001', rejectionReason: reason }
          : transfer
      ))

      // Transfer rejected successfully
    } catch (error) {
      // Error handling - could be logged to monitoring service
    } finally {
      setIsLoading(false)
    }
  }

  const filteredTransfers = transfers.filter(transfer => {
    const matchesSearch = transfer.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transfer.orderInfo.orderId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || transfer.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Stats hesaplama
  const stats = {
    pending: transfers.filter(t => t.status === 'pending').length,
    verified: transfers.filter(t => t.status === 'verified').length,
    rejected: transfers.filter(t => t.status === 'rejected').length,
    totalAmount: transfers.reduce((sum, t) => sum + t.amount, 0)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Bekliyor</Badge>
      case 'verified':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Onaylandı</Badge>
      case 'rejected':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Reddedildi</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Banka Havalesi Onayları</h1>
          <p className="text-gray-600 mt-1">
            Müşteriler tarafından yüklenen ödeme makbuzlarını inceleyin ve onaylayın
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Rapor İndir
          </Button>
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Filtrele
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Bekleyen</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Onaylanan</p>
              <p className="text-2xl font-bold text-gray-900">{stats.verified}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <XCircle className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Reddedilen</p>
              <p className="text-2xl font-bold text-gray-900">{stats.rejected}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Tutar</p>
              <p className="text-2xl font-bold text-gray-900">${stats.totalAmount.toLocaleString()}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Müşteri adı veya sipariş numarası ile ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tüm Durumlar</option>
              <option value="pending">Bekleyen</option>
              <option value="verified">Onaylanan</option>
              <option value="rejected">Reddedilen</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Bank Transfers Table */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Müşteri
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sipariş
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tutar
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Yüklenme Tarihi
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Durum
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTransfers.map((transfer) => (
                <tr key={transfer.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <User className="h-5 w-5 text-gray-500" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {transfer.customerName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {transfer.customerEmail}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{transfer.orderInfo.orderId}</div>
                    <div className="text-sm text-gray-500">{transfer.orderInfo.productName}</div>
                    <div className="text-xs text-gray-400">{transfer.orderInfo.quantity}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      ${transfer.amount.toLocaleString()} {transfer.currency}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {formatDate(transfer.uploadedAt)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(transfer.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedTransfer(transfer)}
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        İncele
                      </Button>
                      {transfer.status === 'pending' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-green-600 border-green-600 hover:bg-green-50"
                            onClick={() => handleQuickApprove(transfer.id)}
                            disabled={isLoading}
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Onayla
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 border-red-600 hover:bg-red-50"
                            onClick={() => handleQuickReject(transfer.id)}
                            disabled={isLoading}
                          >
                            <XCircle className="w-4 h-4 mr-1" />
                            Reddet
                          </Button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Bank Transfer Approval Modal */}
      {selectedTransfer && (
        <BankTransferApprovalModal
          transfer={selectedTransfer}
          isOpen={!!selectedTransfer}
          onClose={() => setSelectedTransfer(null)}
          onApprove={async (transferId, reason) => {
            try {
              // API çağrısı simülasyonu
              await new Promise(resolve => setTimeout(resolve, 1000))

              setTransfers(prev => prev.map(transfer =>
                transfer.id === transferId
                  ? { ...transfer, status: 'verified', verifiedAt: new Date().toISOString(), verifiedBy: 'admin_001', approvalReason: reason }
                  : transfer
              ))

              // Transfer approved successfully
              setSelectedTransfer(null)
            } catch (error) {
              // Error handling - could be logged to monitoring service
            }
          }}
          onReject={async (transferId, reason) => {
            try {
              // API çağrısı simülasyonu
              await new Promise(resolve => setTimeout(resolve, 1000))

              setTransfers(prev => prev.map(transfer =>
                transfer.id === transferId
                  ? { ...transfer, status: 'rejected', rejectedAt: new Date().toISOString(), rejectedBy: 'admin_001', rejectionReason: reason }
                  : transfer
              ))

              // Transfer rejected successfully
              setSelectedTransfer(null)
            } catch (error) {
              // Error handling - could be logged to monitoring service
            }
          }}
        />
      )}
    </div>
  )
}
