'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { 
  Ruler, 
  RotateCw, 
  Square, 
  Maximize2, 
  Settings,
  Check,
  X,
  Info
} from 'lucide-react';

export interface DimensionPreset {
  name: string;
  width: number;
  height: number;
  thickness: number;
  popular: boolean;
  category: string;
}

export interface CustomDimensions {
  width: number;
  height: number;
  thickness: number;
  unit: 'cm' | 'm';
}

interface DimensionSelectorProps {
  productId: string;
  productName: string;
  currentDimensions: CustomDimensions;
  onDimensionsChange: (dimensions: CustomDimensions) => void;
  onConfirm: () => void;
  onCancel: () => void;
  className?: string;
}

// Popüler ebat presetleri
const DIMENSION_PRESETS: DimensionPreset[] = [
  // Mermer Presetleri
  { name: '60x60 Standart', width: 60, height: 60, thickness: 2, popular: true, category: 'Mermer' },
  { name: '80x80 Büyük', width: 80, height: 80, thickness: 2, popular: true, category: 'Mermer' },
  { name: '60x30 Dikdörtgen', width: 60, height: 30, thickness: 2, popular: true, category: 'Mermer' },
  { name: '120x60 Büyük Plaka', width: 120, height: 60, thickness: 3, popular: false, category: 'Mermer' },
  
  // Granit Presetleri
  { name: '60x60 Standart Granit', width: 60, height: 60, thickness: 3, popular: true, category: 'Granit' },
  { name: '80x80 Büyük Granit', width: 80, height: 80, thickness: 3, popular: true, category: 'Granit' },
  { name: '100x100 Jumbo', width: 100, height: 100, thickness: 3, popular: false, category: 'Granit' },
  
  // Traverten Presetleri
  { name: '40x40 Klasik', width: 40, height: 40, thickness: 1.5, popular: true, category: 'Traverten' },
  { name: '60x40 Dikdörtgen', width: 60, height: 40, thickness: 2, popular: true, category: 'Traverten' },
  { name: '80x40 Uzun', width: 80, height: 40, thickness: 2, popular: false, category: 'Traverten' },
  
  // Oniks Presetleri
  { name: '30x60 Oniks', width: 30, height: 60, thickness: 2, popular: true, category: 'Oniks' },
  { name: '40x80 Büyük Oniks', width: 40, height: 80, thickness: 2, popular: false, category: 'Oniks' },
  
  // Kireçtaşı Presetleri
  { name: '40x60 Kireçtaşı', width: 40, height: 60, thickness: 2.5, popular: true, category: 'Kireçtaşı' },
  { name: '50x50 Kare', width: 50, height: 50, thickness: 2, popular: true, category: 'Kireçtaşı' }
];

export const DimensionSelector: React.FC<DimensionSelectorProps> = ({
  productId,
  productName,
  currentDimensions,
  onDimensionsChange,
  onConfirm,
  onCancel,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'presets' | 'custom'>('presets');
  const [customDims, setCustomDims] = useState<CustomDimensions>(currentDimensions);
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);

  // Filter presets based on product category
  const productCategory = useMemo(() => {
    if (productId.includes('marble')) return 'Mermer';
    if (productId.includes('granite')) return 'Granit';
    if (productId.includes('travertine')) return 'Traverten';
    if (productId.includes('onyx')) return 'Oniks';
    if (productId.includes('limestone')) return 'Kireçtaşı';
    return 'Genel';
  }, [productId]);

  const relevantPresets = useMemo(() => {
    return DIMENSION_PRESETS.filter(preset => 
      preset.category === productCategory || preset.category === 'Genel'
    ).sort((a, b) => {
      if (a.popular && !b.popular) return -1;
      if (!a.popular && b.popular) return 1;
      return 0;
    });
  }, [productCategory]);

  // Handle preset selection
  const handlePresetSelect = useCallback((preset: DimensionPreset) => {
    const newDimensions: CustomDimensions = {
      width: preset.width,
      height: preset.height,
      thickness: preset.thickness,
      unit: 'cm'
    };
    setCustomDims(newDimensions);
    setSelectedPreset(preset.name);
    onDimensionsChange(newDimensions);
  }, [onDimensionsChange]);

  // Handle custom dimension change
  const handleCustomChange = useCallback((field: keyof CustomDimensions, value: number | string) => {
    const newDimensions = {
      ...customDims,
      [field]: typeof value === 'string' ? value : Math.max(0, value)
    };
    setCustomDims(newDimensions);
    setSelectedPreset(null);
    onDimensionsChange(newDimensions);
  }, [customDims, onDimensionsChange]);

  // Calculate area
  const area = useMemo(() => {
    const widthM = customDims.unit === 'cm' ? customDims.width / 100 : customDims.width;
    const heightM = customDims.unit === 'cm' ? customDims.height / 100 : customDims.height;
    return widthM * heightM;
  }, [customDims]);

  // Validate dimensions
  const isValid = useMemo(() => {
    return customDims.width > 0 && customDims.height > 0 && customDims.thickness > 0;
  }, [customDims]);

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
              <Ruler size={20} className="text-amber-500" />
              Ebat Seçimi
            </h3>
            <p className="text-sm text-gray-600 mt-1">{productName}</p>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={onCancel}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => setActiveTab('presets')}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === 'presets'
              ? 'text-amber-600 border-b-2 border-amber-600 bg-amber-50'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Square size={16} className="mx-auto mb-1" />
          Hazır Ebatlar
        </button>
        
        <button
          onClick={() => setActiveTab('custom')}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === 'custom'
              ? 'text-amber-600 border-b-2 border-amber-600 bg-amber-50'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Settings size={16} className="mx-auto mb-1" />
          Özel Ebat
        </button>
      </div>

      {/* Content */}
      <div className="p-4">
        {activeTab === 'presets' ? (
          <div className="space-y-3">
            <div className="text-xs text-gray-600 mb-3">
              {productCategory} kategorisi için popüler ebatlar:
            </div>
            
            <div className="grid grid-cols-1 gap-2 max-h-64 overflow-y-auto">
              {relevantPresets.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => handlePresetSelect(preset)}
                  className={`p-3 text-left border rounded-lg transition-colors ${
                    selectedPreset === preset.name
                      ? 'border-amber-500 bg-amber-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-sm text-gray-900 flex items-center gap-2">
                        {preset.name}
                        {preset.popular && (
                          <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">
                            Popüler
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-600 mt-1">
                        {preset.width}×{preset.height}×{preset.thickness} cm
                      </div>
                    </div>
                    
                    <div className="text-xs text-gray-500">
                      {((preset.width * preset.height) / 10000).toFixed(2)} m²
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Unit Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Birim</label>
              <div className="flex gap-2">
                <button
                  onClick={() => handleCustomChange('unit', 'cm')}
                  className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                    customDims.unit === 'cm'
                      ? 'bg-amber-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  Santimetre (cm)
                </button>
                <button
                  onClick={() => handleCustomChange('unit', 'm')}
                  className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                    customDims.unit === 'm'
                      ? 'bg-amber-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  Metre (m)
                </button>
              </div>
            </div>

            {/* Dimension Inputs */}
            <div className="grid grid-cols-3 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  En ({customDims.unit})
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.1"
                  value={customDims.width}
                  onChange={(e) => handleCustomChange('width', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Boy ({customDims.unit})
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.1"
                  value={customDims.height}
                  onChange={(e) => handleCustomChange('height', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Kalınlık ({customDims.unit})
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.1"
                  value={customDims.thickness}
                  onChange={(e) => handleCustomChange('thickness', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                />
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex gap-2">
              <button
                onClick={() => {
                  const temp = customDims.width;
                  handleCustomChange('width', customDims.height);
                  handleCustomChange('height', temp);
                }}
                className="flex items-center gap-1 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <RotateCw size={14} />
                Döndür
              </button>
              
              <button
                onClick={() => {
                  handleCustomChange('width', customDims.height);
                }}
                className="flex items-center gap-1 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <Square size={14} />
                Kare Yap
              </button>
            </div>
          </div>
        )}

        {/* Info Panel */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Info size={16} className="text-blue-500" />
            <span className="text-sm font-medium text-gray-900">Ebat Bilgileri</span>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Boyutlar:</span>
              <div className="font-medium">
                {customDims.width}×{customDims.height}×{customDims.thickness} {customDims.unit}
              </div>
            </div>
            
            <div>
              <span className="text-gray-600">Alan:</span>
              <div className="font-medium">{area.toFixed(3)} m²</div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 flex gap-3">
        <button
          onClick={onCancel}
          className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          İptal
        </button>
        
        <button
          onClick={onConfirm}
          disabled={!isValid}
          className="flex-1 px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
        >
          <Check size={16} />
          Uygula
        </button>
      </div>
    </div>
  );
};

export default DimensionSelector;
