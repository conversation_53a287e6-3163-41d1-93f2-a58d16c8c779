'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { PerformanceMonitorProps } from '../../types/3d';

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  onPerformanceUpdate,
  visible = true
}) => {
  const { gl, scene } = useThree();
  const [metrics, setMetrics] = useState({
    frameRate: 0,
    memoryUsage: 0,
    drawCalls: 0,
    triangles: 0,
    geometries: 0,
    textures: 0
  });

  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const fpsHistory = useRef<number[]>([]);

  // Performance monitoring
  useFrame(() => {
    frameCount.current++;
    const currentTime = performance.now();
    
    // Calculate FPS every second
    if (currentTime - lastTime.current >= 1000) {
      const fps = Math.round((frameCount.current * 1000) / (currentTime - lastTime.current));
      
      // Keep FPS history for smoothing
      fpsHistory.current.push(fps);
      if (fpsHistory.current.length > 10) {
        fpsHistory.current.shift();
      }
      
      const averageFps = fpsHistory.current.reduce((a, b) => a + b, 0) / fpsHistory.current.length;
      
      // Get renderer info
      const info = gl.info;
      const memory = info.memory;
      const render = info.render;
      
      // Estimate memory usage (rough calculation)
      const estimatedMemory = Math.round(
        (memory.geometries * 0.1 + memory.textures * 2 + scene.children.length * 0.05)
      );
      
      const newMetrics = {
        frameRate: Math.round(averageFps),
        memoryUsage: estimatedMemory,
        drawCalls: render.calls,
        triangles: render.triangles,
        geometries: memory.geometries,
        textures: memory.textures
      };
      
      setMetrics(newMetrics);
      onPerformanceUpdate?.(newMetrics);
      
      frameCount.current = 0;
      lastTime.current = currentTime;
    }
  });

  // Get performance status color
  const getPerformanceColor = (fps: number) => {
    if (fps >= 50) return 'text-green-600';
    if (fps >= 30) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceStatus = (fps: number) => {
    if (fps >= 50) return 'Excellent';
    if (fps >= 30) return 'Good';
    if (fps >= 20) return 'Fair';
    return 'Poor';
  };

  if (!visible) {
    return null;
  }

  return (
    <Html
      position={[0, 0, 0]}
      style={{
        position: 'fixed',
        top: '10px',
        left: '10px',
        pointerEvents: 'none',
        zIndex: 1000
      }}
    >
      <div className="bg-black bg-opacity-75 text-white p-3 rounded-lg font-mono text-xs space-y-1 min-w-48">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-600 pb-1 mb-2">
          <span className="font-semibold">Performance</span>
          <span className={`text-xs ${getPerformanceColor(metrics.frameRate)}`}>
            {getPerformanceStatus(metrics.frameRate)}
          </span>
        </div>

        {/* FPS */}
        <div className="flex justify-between">
          <span>FPS:</span>
          <span className={getPerformanceColor(metrics.frameRate)}>
            {metrics.frameRate}
          </span>
        </div>

        {/* Memory Usage */}
        <div className="flex justify-between">
          <span>Memory:</span>
          <span className={metrics.memoryUsage > 100 ? 'text-yellow-400' : 'text-green-400'}>
            {metrics.memoryUsage}MB
          </span>
        </div>

        {/* Draw Calls */}
        <div className="flex justify-between">
          <span>Draw Calls:</span>
          <span className={metrics.drawCalls > 100 ? 'text-yellow-400' : 'text-green-400'}>
            {metrics.drawCalls}
          </span>
        </div>

        {/* Triangles */}
        <div className="flex justify-between">
          <span>Triangles:</span>
          <span className={metrics.triangles > 100000 ? 'text-yellow-400' : 'text-green-400'}>
            {metrics.triangles.toLocaleString()}
          </span>
        </div>

        {/* Geometries */}
        <div className="flex justify-between">
          <span>Geometries:</span>
          <span className="text-blue-400">
            {metrics.geometries}
          </span>
        </div>

        {/* Textures */}
        <div className="flex justify-between">
          <span>Textures:</span>
          <span className="text-purple-400">
            {metrics.textures}
          </span>
        </div>

        {/* Performance Tips */}
        {metrics.frameRate < 30 && (
          <div className="mt-2 pt-2 border-t border-gray-600">
            <div className="text-yellow-400 text-xs">
              Performance Tips:
            </div>
            <ul className="text-xs text-gray-300 mt-1 space-y-1">
              {metrics.drawCalls > 50 && (
                <li>• Reduce draw calls</li>
              )}
              {metrics.triangles > 50000 && (
                <li>• Lower model quality</li>
              )}
              {metrics.textures > 10 && (
                <li>• Optimize textures</li>
              )}
              {metrics.memoryUsage > 50 && (
                <li>• Reduce memory usage</li>
              )}
            </ul>
          </div>
        )}

        {/* WebGL Info */}
        <div className="mt-2 pt-2 border-t border-gray-600 text-xs text-gray-400">
          <div>Renderer: {gl.capabilities.isWebGL2 ? 'WebGL2' : 'WebGL'}</div>
          <div>Max Texture: {gl.capabilities.maxTextureSize}px</div>
        </div>
      </div>
    </Html>
  );
};

export default PerformanceMonitor;
