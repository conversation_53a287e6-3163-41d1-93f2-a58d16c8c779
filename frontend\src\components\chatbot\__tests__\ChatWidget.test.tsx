/**
 * ChatWidget Component Tests
 * Unit tests for the ChatWidget React component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ChatWidget } from '../ChatWidget';
import { chatbotApi } from '../../../services/chatbotApi';

// Mock the chatbot API
jest.mock('../../../services/chatbotApi', () => ({
  chatbotApi: {
    startConversation: jest.fn(),
    sendMessage: jest.fn(),
    submitFeedback: jest.fn(),
    getDefaultSuggestions: jest.fn(),
  }
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Send: () => <div data-testid="send-icon" />,
  X: () => <div data-testid="x-icon" />,
  MessageCircle: () => <div data-testid="message-circle-icon" />,
  Minimize2: () => <div data-testid="minimize-icon" />,
  Maximize2: () => <div data-testid="maximize-icon" />,
  ThumbsUp: () => <div data-testid="thumbs-up-icon" />,
  ThumbsDown: () => <div data-testid="thumbs-down-icon" />,
}));

const mockChatbotApi = chatbotApi as jest.Mocked<typeof chatbotApi>;

describe('ChatWidget', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementations
    mockChatbotApi.startConversation.mockResolvedValue({
      sessionId: 'test-session-123',
      greeting: 'Hello! How can I help you today?'
    });
    
    mockChatbotApi.sendMessage.mockResolvedValue({
      success: true,
      sessionId: 'test-session-123',
      response: 'I can help you with that!',
      confidence: 0.9,
      intent: 'PRODUCT_INQUIRY' as any,
      entities: [],
      suggestions: ['Tell me about marble', 'Show pricing']
    });
    
    mockChatbotApi.getDefaultSuggestions.mockReturnValue([
      'Tell me about marble types',
      'How does bidding work?',
      'What are your delivery terms?'
    ]);
  });

  describe('Initial State', () => {
    it('should render chat button when closed', () => {
      render(<ChatWidget />);
      
      const chatButton = screen.getByLabelText('Open chat');
      expect(chatButton).toBeInTheDocument();
      expect(screen.getByTestId('message-circle-icon')).toBeInTheDocument();
    });

    it('should not show chat window initially', () => {
      render(<ChatWidget />);
      
      expect(screen.queryByText('AI Assistant')).not.toBeInTheDocument();
    });
  });

  describe('Opening Chat', () => {
    it('should open chat window when button is clicked', async () => {
      render(<ChatWidget />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      await waitFor(() => {
        expect(screen.getByText('AI Assistant')).toBeInTheDocument();
      });
      
      expect(mockChatbotApi.startConversation).toHaveBeenCalledWith(undefined, 'en');
    });

    it('should display greeting message after opening', async () => {
      render(<ChatWidget />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      await waitFor(() => {
        expect(screen.getByText('Hello! How can I help you today?')).toBeInTheDocument();
      });
    });

    it('should show default suggestions', async () => {
      render(<ChatWidget />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      await waitFor(() => {
        expect(screen.getByText('Tell me about marble types')).toBeInTheDocument();
        expect(screen.getByText('How does bidding work?')).toBeInTheDocument();
      });
    });
  });

  describe('Sending Messages', () => {
    beforeEach(async () => {
      render(<ChatWidget />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      await waitFor(() => {
        expect(screen.getByText('AI Assistant')).toBeInTheDocument();
      });
    });

    it('should send message when send button is clicked', async () => {
      const messageInput = screen.getByPlaceholderText('Type your message...');
      const sendButton = screen.getByLabelText('Send message');
      
      fireEvent.change(messageInput, { target: { value: 'Hello, I need help' } });
      fireEvent.click(sendButton);
      
      await waitFor(() => {
        expect(mockChatbotApi.sendMessage).toHaveBeenCalledWith(
          'test-session-123',
          'Hello, I need help',
          undefined
        );
      });
      
      expect(screen.getByText('Hello, I need help')).toBeInTheDocument();
      expect(screen.getByText('I can help you with that!')).toBeInTheDocument();
    });

    it('should send message when Enter key is pressed', async () => {
      const messageInput = screen.getByPlaceholderText('Type your message...');
      
      fireEvent.change(messageInput, { target: { value: 'Test message' } });
      fireEvent.keyPress(messageInput, { key: 'Enter', code: 'Enter' });
      
      await waitFor(() => {
        expect(mockChatbotApi.sendMessage).toHaveBeenCalledWith(
          'test-session-123',
          'Test message',
          undefined
        );
      });
    });

    it('should not send empty messages', () => {
      const messageInput = screen.getByPlaceholderText('Type your message...');
      const sendButton = screen.getByLabelText('Send message');
      
      fireEvent.change(messageInput, { target: { value: '   ' } });
      fireEvent.click(sendButton);
      
      expect(mockChatbotApi.sendMessage).not.toHaveBeenCalled();
    });

    it('should clear input after sending message', async () => {
      const messageInput = screen.getByPlaceholderText('Type your message...') as HTMLInputElement;
      const sendButton = screen.getByLabelText('Send message');
      
      fireEvent.change(messageInput, { target: { value: 'Test message' } });
      fireEvent.click(sendButton);
      
      await waitFor(() => {
        expect(messageInput.value).toBe('');
      });
    });
  });

  describe('Suggestions', () => {
    beforeEach(async () => {
      render(<ChatWidget />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      await waitFor(() => {
        expect(screen.getByText('AI Assistant')).toBeInTheDocument();
      });
    });

    it('should send message when suggestion is clicked', async () => {
      const suggestion = screen.getByText('Tell me about marble types');
      fireEvent.click(suggestion);
      
      await waitFor(() => {
        expect(mockChatbotApi.sendMessage).toHaveBeenCalledWith(
          'test-session-123',
          'Tell me about marble types',
          undefined
        );
      });
    });

    it('should update suggestions after receiving response', async () => {
      const messageInput = screen.getByPlaceholderText('Type your message...');
      const sendButton = screen.getByLabelText('Send message');
      
      fireEvent.change(messageInput, { target: { value: 'Hello' } });
      fireEvent.click(sendButton);
      
      await waitFor(() => {
        expect(screen.getByText('Tell me about marble')).toBeInTheDocument();
        expect(screen.getByText('Show pricing')).toBeInTheDocument();
      });
    });
  });

  describe('Feedback', () => {
    beforeEach(async () => {
      render(<ChatWidget />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      await waitFor(() => {
        expect(screen.getByText('AI Assistant')).toBeInTheDocument();
      });
      
      // Send a message to get a response with feedback buttons
      const messageInput = screen.getByPlaceholderText('Type your message...');
      const sendButton = screen.getByLabelText('Send message');
      
      fireEvent.change(messageInput, { target: { value: 'Test message' } });
      fireEvent.click(sendButton);
      
      await waitFor(() => {
        expect(screen.getByText('I can help you with that!')).toBeInTheDocument();
      });
    });

    it('should submit positive feedback when thumbs up is clicked', async () => {
      const thumbsUpButton = screen.getByLabelText('Good response');
      fireEvent.click(thumbsUpButton);
      
      await waitFor(() => {
        expect(mockChatbotApi.submitFeedback).toHaveBeenCalledWith(
          'test-session-123',
          expect.any(String),
          5
        );
      });
    });

    it('should submit negative feedback when thumbs down is clicked', async () => {
      const thumbsDownButton = screen.getByLabelText('Poor response');
      fireEvent.click(thumbsDownButton);
      
      await waitFor(() => {
        expect(mockChatbotApi.submitFeedback).toHaveBeenCalledWith(
          'test-session-123',
          expect.any(String),
          1
        );
      });
    });
  });

  describe('Chat Controls', () => {
    beforeEach(async () => {
      render(<ChatWidget />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      await waitFor(() => {
        expect(screen.getByText('AI Assistant')).toBeInTheDocument();
      });
    });

    it('should close chat when X button is clicked', () => {
      const closeButton = screen.getByLabelText('Close chat');
      fireEvent.click(closeButton);
      
      expect(screen.queryByText('AI Assistant')).not.toBeInTheDocument();
      expect(screen.getByLabelText('Open chat')).toBeInTheDocument();
    });

    it('should minimize chat when minimize button is clicked', () => {
      const minimizeButton = screen.getByLabelText('Minimize');
      fireEvent.click(minimizeButton);
      
      expect(screen.getByText('AI Assistant')).toBeInTheDocument();
      expect(screen.queryByPlaceholderText('Type your message...')).not.toBeInTheDocument();
    });

    it('should maximize chat when maximize button is clicked after minimizing', () => {
      const minimizeButton = screen.getByLabelText('Minimize');
      fireEvent.click(minimizeButton);
      
      const maximizeButton = screen.getByLabelText('Maximize');
      fireEvent.click(maximizeButton);
      
      expect(screen.getByPlaceholderText('Type your message...')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle conversation start errors', async () => {
      mockChatbotApi.startConversation.mockRejectedValue(new Error('Network error'));
      
      render(<ChatWidget />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      // Should not crash and should still show the chat interface
      await waitFor(() => {
        expect(screen.getByText('AI Assistant')).toBeInTheDocument();
      });
    });

    it('should handle message send errors', async () => {
      mockChatbotApi.sendMessage.mockRejectedValue(new Error('API error'));
      
      render(<ChatWidget />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      await waitFor(() => {
        expect(screen.getByText('AI Assistant')).toBeInTheDocument();
      });
      
      const messageInput = screen.getByPlaceholderText('Type your message...');
      const sendButton = screen.getByLabelText('Send message');
      
      fireEvent.change(messageInput, { target: { value: 'Test message' } });
      fireEvent.click(sendButton);
      
      await waitFor(() => {
        expect(screen.getByText('I apologize, but I encountered an error. Please try again.')).toBeInTheDocument();
      });
    });
  });

  describe('Escalation', () => {
    it('should show escalation message when escalated', async () => {
      mockChatbotApi.sendMessage.mockResolvedValue({
        success: true,
        sessionId: 'test-session-123',
        response: 'I understand your concern.',
        confidence: 0.9,
        intent: 'COMPLAINT' as any,
        entities: [],
        suggestions: [],
        escalated: true,
        agentId: 'agent-123',
        estimatedWaitTime: 5
      });
      
      render(<ChatWidget />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      await waitFor(() => {
        expect(screen.getByText('AI Assistant')).toBeInTheDocument();
      });
      
      const messageInput = screen.getByPlaceholderText('Type your message...');
      const sendButton = screen.getByLabelText('Send message');
      
      fireEvent.change(messageInput, { target: { value: 'I have a complaint' } });
      fireEvent.click(sendButton);
      
      await waitFor(() => {
        expect(screen.getByText(/You have been connected to a human agent/)).toBeInTheDocument();
        expect(screen.getByText(/Estimated wait time: 5 minutes/)).toBeInTheDocument();
      });
    });
  });

  describe('Props Configuration', () => {
    it('should use provided language', async () => {
      render(<ChatWidget language="tr" />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      await waitFor(() => {
        expect(mockChatbotApi.startConversation).toHaveBeenCalledWith(undefined, 'tr');
      });
    });

    it('should use provided userId', async () => {
      render(<ChatWidget userId="user123" />);
      
      const chatButton = screen.getByLabelText('Open chat');
      fireEvent.click(chatButton);
      
      await waitFor(() => {
        expect(mockChatbotApi.startConversation).toHaveBeenCalledWith('user123', 'en');
      });
    });

    it('should apply correct position classes', () => {
      const { rerender } = render(<ChatWidget position="bottom-left" />);
      
      expect(screen.getByLabelText('Open chat').closest('div')).toHaveClass('bottom-4', 'left-4');
      
      rerender(<ChatWidget position="bottom-right" />);
      
      expect(screen.getByLabelText('Open chat').closest('div')).toHaveClass('bottom-4', 'right-4');
    });
  });
});
