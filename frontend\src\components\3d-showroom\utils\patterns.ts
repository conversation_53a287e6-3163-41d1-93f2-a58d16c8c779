import * as THREE from 'three';

export type TilingPattern = 'standard' | 'chess' | 'herringbone' | 'diagonal' | 'brick' | 'hexagon';

export interface PatternConfig {
  name: string;
  displayName: string;
  description: string;
  rotation: number;
  scale: { x: number; y: number };
  offset: { x: number; y: number };
  textureRepeat: { x: number; y: number };
}

export const PATTERN_CONFIGS: Record<TilingPattern, PatternConfig> = {
  standard: {
    name: 'standard',
    displayName: 'Standart',
    description: 'Düz döşeme pattern',
    rotation: 0,
    scale: { x: 1, y: 1 },
    offset: { x: 0, y: 0 },
    textureRepeat: { x: 1, y: 1 }
  },
  chess: {
    name: 'chess',
    displayName: 'Satranç',
    description: 'Alternatif renk/desen pattern',
    rotation: 0,
    scale: { x: 1, y: 1 },
    offset: { x: 0, y: 0 },
    textureRepeat: { x: 2, y: 2 }
  },
  herringbone: {
    name: 'herringbone',
    displayName: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Herringbone pattern',
    rotation: 45,
    scale: { x: 0.5, y: 2 },
    offset: { x: 0.25, y: 0 },
    textureRepeat: { x: 4, y: 2 }
  },
  diagonal: {
    name: 'diagonal',
    displayName: 'Çapraz',
    description: 'Diagonal döşeme pattern',
    rotation: 45,
    scale: { x: 1, y: 1 },
    offset: { x: 0, y: 0 },
    textureRepeat: { x: 1.414, y: 1.414 }
  },
  brick: {
    name: 'brick',
    displayName: 'Tuğla',
    description: 'Tuğla döşeme pattern',
    rotation: 0,
    scale: { x: 2, y: 1 },
    offset: { x: 0.5, y: 0 },
    textureRepeat: { x: 2, y: 1 }
  },
  hexagon: {
    name: 'hexagon',
    displayName: 'Altıgen',
    description: 'Altıgen döşeme pattern',
    rotation: 0,
    scale: { x: 1, y: 1 },
    offset: { x: 0, y: 0 },
    textureRepeat: { x: 1, y: 1 }
  }
};

// Pattern texture generators
export class PatternGenerator {
  static createChessboardTexture(
    size: number = 512,
    tileSize: number = 64,
    color1: string = '#ffffff',
    color2: string = '#f0f0f0'
  ): THREE.CanvasTexture {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d')!;

    const tilesPerRow = size / tileSize;
    
    for (let x = 0; x < tilesPerRow; x++) {
      for (let y = 0; y < tilesPerRow; y++) {
        ctx.fillStyle = (x + y) % 2 === 0 ? color1 : color2;
        ctx.fillRect(x * tileSize, y * tileSize, tileSize, tileSize);
      }
    }

    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
    return texture;
  }

  static createHerringboneTexture(
    size: number = 512,
    tileWidth: number = 32,
    tileHeight: number = 128,
    color: string = '#f8f9fa',
    groutColor: string = '#e2e8f0',
    groutWidth: number = 2
  ): THREE.CanvasTexture {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d')!;

    // Fill background with grout color
    ctx.fillStyle = groutColor;
    ctx.fillRect(0, 0, size, size);

    // Draw herringbone pattern
    ctx.fillStyle = color;
    
    const patternWidth = tileWidth + tileHeight;
    const patternHeight = tileWidth + tileHeight;
    
    for (let x = 0; x < size; x += patternWidth) {
      for (let y = 0; y < size; y += patternHeight) {
        // Horizontal tile
        ctx.fillRect(
          x + groutWidth, 
          y + groutWidth, 
          tileHeight - groutWidth * 2, 
          tileWidth - groutWidth * 2
        );
        
        // Vertical tile
        ctx.fillRect(
          x + tileHeight + groutWidth, 
          y + groutWidth, 
          tileWidth - groutWidth * 2, 
          tileHeight - groutWidth * 2
        );
      }
    }

    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
    return texture;
  }

  static createBrickTexture(
    size: number = 512,
    brickWidth: number = 128,
    brickHeight: number = 64,
    color: string = '#f8f9fa',
    groutColor: string = '#e2e8f0',
    groutWidth: number = 2
  ): THREE.CanvasTexture {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d')!;

    // Fill background with grout color
    ctx.fillStyle = groutColor;
    ctx.fillRect(0, 0, size, size);

    // Draw brick pattern
    ctx.fillStyle = color;
    
    const rowHeight = brickHeight + groutWidth;
    
    for (let y = 0; y < size; y += rowHeight) {
      const isEvenRow = Math.floor(y / rowHeight) % 2 === 0;
      const offsetX = isEvenRow ? 0 : brickWidth / 2;
      
      for (let x = -brickWidth; x < size + brickWidth; x += brickWidth + groutWidth) {
        ctx.fillRect(
          x + offsetX + groutWidth, 
          y + groutWidth, 
          brickWidth - groutWidth, 
          brickHeight - groutWidth
        );
      }
    }

    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
    return texture;
  }

  static createDiagonalTexture(
    size: number = 512,
    tileSize: number = 64,
    color: string = '#f8f9fa',
    groutColor: string = '#e2e8f0',
    groutWidth: number = 2
  ): THREE.CanvasTexture {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d')!;

    // Fill background with grout color
    ctx.fillStyle = groutColor;
    ctx.fillRect(0, 0, size, size);

    // Rotate context for diagonal pattern
    ctx.save();
    ctx.translate(size / 2, size / 2);
    ctx.rotate(Math.PI / 4);
    ctx.translate(-size / 2, -size / 2);

    // Draw diagonal tiles
    ctx.fillStyle = color;
    
    const adjustedSize = size * Math.sqrt(2);
    const tilesPerRow = Math.ceil(adjustedSize / tileSize);
    
    for (let x = 0; x < tilesPerRow; x++) {
      for (let y = 0; y < tilesPerRow; y++) {
        ctx.fillRect(
          x * tileSize + groutWidth, 
          y * tileSize + groutWidth, 
          tileSize - groutWidth * 2, 
          tileSize - groutWidth * 2
        );
      }
    }

    ctx.restore();

    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
    return texture;
  }

  static createHexagonTexture(
    size: number = 512,
    hexSize: number = 32,
    color: string = '#f8f9fa',
    groutColor: string = '#e2e8f0',
    groutWidth: number = 2
  ): THREE.CanvasTexture {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d')!;

    // Fill background with grout color
    ctx.fillStyle = groutColor;
    ctx.fillRect(0, 0, size, size);

    // Draw hexagon pattern
    ctx.fillStyle = color;
    
    const hexWidth = hexSize * 2;
    const hexHeight = hexSize * Math.sqrt(3);
    const rowHeight = hexHeight * 0.75;
    
    for (let row = 0; row < size / rowHeight + 2; row++) {
      const y = row * rowHeight;
      const offsetX = (row % 2) * (hexWidth / 2);
      
      for (let col = 0; col < size / hexWidth + 2; col++) {
        const x = col * hexWidth + offsetX;
        
        this.drawHexagon(ctx, x, y, hexSize - groutWidth);
      }
    }

    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
    return texture;
  }

  private static drawHexagon(
    ctx: CanvasRenderingContext2D, 
    x: number, 
    y: number, 
    size: number
  ): void {
    ctx.beginPath();
    
    for (let i = 0; i < 6; i++) {
      const angle = (i * Math.PI) / 3;
      const px = x + size * Math.cos(angle);
      const py = y + size * Math.sin(angle);
      
      if (i === 0) {
        ctx.moveTo(px, py);
      } else {
        ctx.lineTo(px, py);
      }
    }
    
    ctx.closePath();
    ctx.fill();
  }

  // Apply pattern to material
  static applyPatternToMaterial(
    material: THREE.MeshStandardMaterial,
    pattern: TilingPattern,
    baseColor: string = '#f8f9fa',
    groutColor: string = '#e2e8f0'
  ): THREE.MeshStandardMaterial {
    const config = PATTERN_CONFIGS[pattern];
    
    let texture: THREE.CanvasTexture;
    
    switch (pattern) {
      case 'chess':
        texture = this.createChessboardTexture(512, 64, baseColor, groutColor);
        break;
      case 'herringbone':
        texture = this.createHerringboneTexture(512, 32, 128, baseColor, groutColor);
        break;
      case 'brick':
        texture = this.createBrickTexture(512, 128, 64, baseColor, groutColor);
        break;
      case 'diagonal':
        texture = this.createDiagonalTexture(512, 64, baseColor, groutColor);
        break;
      case 'hexagon':
        texture = this.createHexagonTexture(512, 32, baseColor, groutColor);
        break;
      default:
        // Standard pattern - solid color
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d')!;
        ctx.fillStyle = baseColor;
        ctx.fillRect(0, 0, 512, 512);
        texture = new THREE.CanvasTexture(canvas);
        break;
    }
    
    texture.repeat.set(config.textureRepeat.x, config.textureRepeat.y);
    texture.offset.set(config.offset.x, config.offset.y);
    
    material.map = texture;
    material.needsUpdate = true;
    
    return material;
  }
}

export default PatternGenerator;
