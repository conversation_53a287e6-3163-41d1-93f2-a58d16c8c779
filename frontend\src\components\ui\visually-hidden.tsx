import * as React from "react"
import { cn } from "@/lib/utils"

export interface VisuallyHiddenProps extends React.HTMLAttributes<HTMLSpanElement> {
  children: React.ReactNode
  focusable?: boolean
}

/**
 * VisuallyHidden component following RFC-004 UI/UX Design System
 * Hides content visually but keeps it available to screen readers
 */
const VisuallyHidden = React.forwardRef<HTMLSpanElement, VisuallyHiddenProps>(
  ({ className, children, focusable = false, ...props }, ref) => {
    return (
      <span
        ref={ref}
        className={cn(
          // Screen reader only styles
          "absolute w-px h-px p-0 -m-px overflow-hidden",
          "whitespace-nowrap border-0",
          
          // Clip path for better browser support
          "[clip:rect(0,0,0,0)]",
          
          // If focusable, show on focus
          focusable && [
            "focus:static focus:w-auto focus:h-auto",
            "focus:p-2 focus:m-0 focus:overflow-visible",
            "focus:whitespace-normal focus:clip-auto",
            "focus:bg-[var(--primary-stone)] focus:text-white",
            "focus:rounded-[var(--radius-md)] focus:shadow-lg"
          ],
          
          className
        )}
        {...props}
      >
        {children}
      </span>
    )
  }
)

VisuallyHidden.displayName = "VisuallyHidden"

export { VisuallyHidden }
