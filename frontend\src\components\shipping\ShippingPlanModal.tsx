'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Calendar, 
  Truck, 
  MessageSquare, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Plus,
  Edit,
  X,
  Send
} from 'lucide-react';
import toast from 'react-hot-toast';

interface ShippingSchedule {
  id: string;
  deliveryNumber: number;
  quantity: number;
  unit: string;
  proposedDate: string;
  status: 'proposed' | 'approved' | 'rejected' | 'negotiating';
  proposedBy: 'producer' | 'customer';
  notes?: string;
  customerNotes?: string;
  producerNotes?: string;
}

interface ShippingPlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderId: string;
  userType: 'customer' | 'producer';
  initialSchedules?: ShippingSchedule[];
  onSavePlan: (schedules: ShippingSchedule[]) => void;
}

export function ShippingPlanModal({
  isOpen,
  onClose,
  orderId,
  userType,
  initialSchedules = [],
  onSavePlan
}: ShippingPlanModalProps) {
  const [schedules, setSchedules] = useState<ShippingSchedule[]>(initialSchedules);
  const [newSchedule, setNewSchedule] = useState({
    quantity: '',
    date: '',
    notes: ''
  });
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [activeTab, setActiveTab] = useState<'plan' | 'chat'>('plan');

  if (!isOpen) return null;

  const addSchedule = () => {
    if (!newSchedule.quantity || !newSchedule.date) {
      toast.error('Miktar ve tarih alanları zorunludur');
      return;
    }

    const schedule: ShippingSchedule = {
      id: `schedule_${Date.now()}`,
      deliveryNumber: schedules.length + 1,
      quantity: parseFloat(newSchedule.quantity),
      unit: 'm²',
      proposedDate: newSchedule.date,
      status: 'proposed',
      proposedBy: userType,
      notes: newSchedule.notes,
      [userType === 'producer' ? 'producerNotes' : 'customerNotes']: newSchedule.notes
    };

    setSchedules([...schedules, schedule]);
    setNewSchedule({ quantity: '', date: '', notes: '' });
    toast.success('Sevkiyat planı eklendi');
  };

  const updateScheduleStatus = (scheduleId: string, status: ShippingSchedule['status'], notes?: string) => {
    setSchedules(schedules.map(schedule => 
      schedule.id === scheduleId 
        ? { 
            ...schedule, 
            status,
            [userType === 'producer' ? 'producerNotes' : 'customerNotes']: notes || schedule[userType === 'producer' ? 'producerNotes' : 'customerNotes']
          }
        : schedule
    ));
    
    const statusMessages = {
      approved: 'Sevkiyat planı onaylandı',
      rejected: 'Sevkiyat planı reddedildi',
      negotiating: 'Sevkiyat planı müzakere edilecek'
    };
    
    toast.success(statusMessages[status] || 'Durum güncellendi');
  };

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const message = {
      id: `msg_${Date.now()}`,
      sender: userType,
      message: newMessage,
      timestamp: new Date().toISOString(),
      type: 'text'
    };

    setChatMessages([...chatMessages, message]);
    setNewMessage('');
    toast.success('Mesaj gönderildi');
  };

  const getStatusBadge = (status: ShippingSchedule['status']) => {
    const statusConfig = {
      proposed: { label: 'Önerilen', color: 'bg-blue-100 text-blue-800', icon: Clock },
      approved: { label: 'Onaylandı', color: 'bg-green-100 text-green-800', icon: CheckCircle },
      rejected: { label: 'Reddedildi', color: 'bg-red-100 text-red-800', icon: X },
      negotiating: { label: 'Müzakere', color: 'bg-yellow-100 text-yellow-800', icon: MessageSquare }
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const canEditPlan = () => {
    return schedules.every(s => s.status === 'proposed' || s.status === 'negotiating');
  };

  const savePlan = () => {
    onSavePlan(schedules);
    toast.success('Sevkiyat planı kaydedildi');
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Truck className="w-5 h-5" />
              Sevkiyat Planı - {orderId}
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Tab Navigation */}
          <div className="flex space-x-4 border-b">
            <button
              onClick={() => setActiveTab('plan')}
              className={`py-2 px-4 border-b-2 font-medium text-sm ${
                activeTab === 'plan'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Calendar className="w-4 h-4 inline mr-2" />
              Sevkiyat Planı
            </button>
            <button
              onClick={() => setActiveTab('chat')}
              className={`py-2 px-4 border-b-2 font-medium text-sm ${
                activeTab === 'chat'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <MessageSquare className="w-4 h-4 inline mr-2" />
              Mesajlaşma ({chatMessages.length})
            </button>
          </div>
        </CardHeader>

        <CardContent className="overflow-y-auto max-h-[60vh]">
          {activeTab === 'plan' && (
            <div className="space-y-6">
              {/* Add New Schedule */}
              {canEditPlan() && (
                <Card className="border-dashed border-2 border-gray-300">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Plus className="w-5 h-5" />
                      Yeni Sevkiyat Ekle
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="quantity">Miktar (m²)</Label>
                        <Input
                          id="quantity"
                          type="number"
                          value={newSchedule.quantity}
                          onChange={(e) => setNewSchedule({...newSchedule, quantity: e.target.value})}
                          placeholder="Örn: 150"
                        />
                      </div>
                      <div>
                        <Label htmlFor="date">Sevkiyat Tarihi</Label>
                        <Input
                          id="date"
                          type="date"
                          value={newSchedule.date}
                          onChange={(e) => setNewSchedule({...newSchedule, date: e.target.value})}
                        />
                      </div>
                      <div className="flex items-end">
                        <Button onClick={addSchedule} className="w-full">
                          <Plus className="w-4 h-4 mr-2" />
                          Ekle
                        </Button>
                      </div>
                    </div>
                    <div className="mt-4">
                      <Label htmlFor="notes">Notlar (İsteğe bağlı)</Label>
                      <Textarea
                        id="notes"
                        value={newSchedule.notes}
                        onChange={(e) => setNewSchedule({...newSchedule, notes: e.target.value})}
                        placeholder="Sevkiyat ile ilgili özel notlarınız..."
                        rows={2}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Existing Schedules */}
              <div className="space-y-4">
                {schedules.map((schedule) => (
                  <Card key={schedule.id} className="border">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium">
                          {schedule.deliveryNumber}. Sevkiyat - {schedule.quantity} {schedule.unit}
                        </h4>
                        {getStatusBadge(schedule.status)}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                        <div>
                          <span className="text-sm text-gray-600">Önerilen Tarih:</span>
                          <span className="ml-2 font-medium">{formatDate(schedule.proposedDate)}</span>
                        </div>
                        <div>
                          <span className="text-sm text-gray-600">Öneren:</span>
                          <span className="ml-2 font-medium">
                            {schedule.proposedBy === 'producer' ? 'Üretici' : 'Müşteri'}
                          </span>
                        </div>
                      </div>

                      {/* Notes */}
                      {schedule.producerNotes && (
                        <div className="mb-2 p-2 bg-blue-50 rounded">
                          <span className="text-sm font-medium text-blue-800">Üretici Notu:</span>
                          <p className="text-sm text-blue-700">{schedule.producerNotes}</p>
                        </div>
                      )}
                      
                      {schedule.customerNotes && (
                        <div className="mb-2 p-2 bg-green-50 rounded">
                          <span className="text-sm font-medium text-green-800">Müşteri Notu:</span>
                          <p className="text-sm text-green-700">{schedule.customerNotes}</p>
                        </div>
                      )}

                      {/* Action Buttons */}
                      {schedule.status === 'proposed' && schedule.proposedBy !== userType && (
                        <div className="flex gap-2 mt-3">
                          <Button
                            size="sm"
                            onClick={() => updateScheduleStatus(schedule.id, 'approved')}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Onayla
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => updateScheduleStatus(schedule.id, 'rejected')}
                          >
                            <X className="w-4 h-4 mr-1" />
                            Reddet
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateScheduleStatus(schedule.id, 'negotiating')}
                          >
                            <MessageSquare className="w-4 h-4 mr-1" />
                            Müzakere Et
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>

              {schedules.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Truck className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Henüz sevkiyat planı oluşturulmamış</p>
                  <p className="text-sm">Yukarıdaki formu kullanarak sevkiyat planı ekleyebilirsiniz</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'chat' && (
            <div className="space-y-4">
              {/* Chat Messages */}
              <div className="border rounded-lg p-4 h-64 overflow-y-auto bg-gray-50">
                {chatMessages.length === 0 ? (
                  <div className="text-center text-gray-500 mt-8">
                    <MessageSquare className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                    <p>Henüz mesaj yok</p>
                    <p className="text-sm">Sevkiyat planı hakkında konuşmaya başlayın</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {chatMessages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.sender === userType ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg ${
                            message.sender === userType
                              ? 'bg-blue-600 text-white'
                              : 'bg-white border'
                          }`}
                        >
                          <p className="text-sm">{message.message}</p>
                          <p className={`text-xs mt-1 ${
                            message.sender === userType ? 'text-blue-100' : 'text-gray-500'
                          }`}>
                            {new Date(message.timestamp).toLocaleTimeString('tr-TR', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Message Input */}
              <div className="flex gap-2">
                <Input
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Mesajınızı yazın..."
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                />
                <Button onClick={sendMessage}>
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>

        {/* Footer */}
        <div className="border-t p-4 flex justify-between">
          <div className="text-sm text-gray-600">
            {userType === 'producer' ? 'Üretici' : 'Müşteri'} olarak görüntülüyorsunuz
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              İptal
            </Button>
            <Button onClick={savePlan}>
              Planı Kaydet
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
