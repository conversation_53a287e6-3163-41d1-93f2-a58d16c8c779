// Redis Manager - Optional Redis Support
import { createClient, RedisClientType } from 'redis';

export class RedisManager {
  private static instance: RedisManager;
  private client: RedisClientType | null = null;
  private isEnabled: boolean;
  private isConnected: boolean = false;

  private constructor() {
    this.isEnabled = process.env.REDIS_ENABLED === 'true';
    
    if (this.isEnabled) {
      this.client = createClient({
        url: process.env.REDIS_URL || `redis://${process.env.REDIS_HOST || 'localhost'}:${process.env.REDIS_PORT || 6379}`
      });
      
      this.client.on('error', (err) => {
        console.error('Redis Client Error:', err);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('✅ Redis connected successfully');
        this.isConnected = true;
      });

      this.client.on('disconnect', () => {
        console.log('⚠️ Redis disconnected');
        this.isConnected = false;
      });
    } else {
      console.log('⚠️ Redis disabled - using fallback mode');
    }
  }

  public static getInstance(): RedisManager {
    if (!RedisManager.instance) {
      RedisManager.instance = new RedisManager();
    }
    return RedisManager.instance;
  }

  public async connect(): Promise<void> {
    if (!this.isEnabled || !this.client) {
      return;
    }

    try {
      if (!this.client.isOpen) {
        await this.client.connect();
        this.isConnected = true;
      }
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      this.isConnected = false;
    }
  }

  public async disconnect(): Promise<void> {
    if (!this.isEnabled || !this.client) {
      return;
    }

    try {
      if (this.client.isOpen) {
        await this.client.disconnect();
        this.isConnected = false;
      }
    } catch (error) {
      console.error('Failed to disconnect from Redis:', error);
    }
  }

  public getClient(): RedisClientType | null {
    return this.isEnabled && this.isConnected ? this.client : null;
  }

  public isRedisEnabled(): boolean {
    return this.isEnabled;
  }

  public isRedisConnected(): boolean {
    return this.isEnabled && this.isConnected;
  }

  // Safe Redis operations with fallback
  public async safeSet(key: string, value: string, ttl?: number): Promise<boolean> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return false;
    }

    try {
      if (ttl) {
        await this.client.setEx(key, ttl, value);
      } else {
        await this.client.set(key, value);
      }
      return true;
    } catch (error) {
      console.error('Redis SET error:', error);
      return false;
    }
  }

  public async safeGet(key: string): Promise<string | null> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return null;
    }

    try {
      return await this.client.get(key);
    } catch (error) {
      console.error('Redis GET error:', error);
      return null;
    }
  }

  public async safeDel(key: string): Promise<boolean> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return false;
    }

    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error('Redis DEL error:', error);
      return false;
    }
  }

  public async safeZAdd(key: string, score: number, value: string): Promise<boolean> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return false;
    }

    try {
      await this.client.zAdd(key, { score, value });
      return true;
    } catch (error) {
      console.error('Redis ZADD error:', error);
      return false;
    }
  }

  public async safeZRange(key: string, start: number, stop: number): Promise<string[]> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return [];
    }

    try {
      return await this.client.zRange(key, start, stop);
    } catch (error) {
      console.error('Redis ZRANGE error:', error);
      return [];
    }
  }

  public async safePing(): Promise<boolean> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return false;
    }

    try {
      await this.client.ping();
      return true;
    } catch (error) {
      console.error('Redis PING error:', error);
      return false;
    }
  }

  // Pub/Sub operations
  public async safePublish(channel: string, message: string): Promise<boolean> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return false;
    }

    try {
      await this.client.publish(channel, message);
      return true;
    } catch (error) {
      console.error('Redis PUBLISH error:', error);
      return false;
    }
  }

  // Hash operations
  public async safeHSet(key: string, field: string, value: string): Promise<boolean> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return false;
    }

    try {
      await this.client.hSet(key, field, value);
      return true;
    } catch (error) {
      console.error('Redis HSET error:', error);
      return false;
    }
  }

  public async safeHGet(key: string, field: string): Promise<string | null> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return null;
    }

    try {
      const result = await this.client.hGet(key, field);
      return result ?? null;
    } catch (error) {
      console.error('Redis HGET error:', error);
      return null;
    }
  }

  public async safeHGetAll(key: string): Promise<Record<string, string>> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return {};
    }

    try {
      return await this.client.hGetAll(key);
    } catch (error) {
      console.error('Redis HGETALL error:', error);
      return {};
    }
  }

  // List operations
  public async safeLPush(key: string, ...values: string[]): Promise<boolean> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return false;
    }

    try {
      await this.client.lPush(key, values);
      return true;
    } catch (error) {
      console.error('Redis LPUSH error:', error);
      return false;
    }
  }

  public async safeLRange(key: string, start: number, stop: number): Promise<string[]> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return [];
    }

    try {
      return await this.client.lRange(key, start, stop);
    } catch (error) {
      console.error('Redis LRANGE error:', error);
      return [];
    }
  }

  // Set operations
  public async safeSAdd(key: string, ...members: string[]): Promise<boolean> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return false;
    }

    try {
      await this.client.sAdd(key, members);
      return true;
    } catch (error) {
      console.error('Redis SADD error:', error);
      return false;
    }
  }

  public async safeSMembers(key: string): Promise<string[]> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return [];
    }

    try {
      return await this.client.sMembers(key);
    } catch (error) {
      console.error('Redis SMEMBERS error:', error);
      return [];
    }
  }

  // Expire operation
  public async safeExpire(key: string, ttlSeconds: number): Promise<boolean> {
    if (!this.isEnabled || !this.client || !this.isConnected) {
      return false;
    }

    try {
      await this.client.expire(key, ttlSeconds);
      return true;
    } catch (error) {
      console.error('Redis EXPIRE error:', error);
      return false;
    }
  }
}

// Export singleton instance
export const redisManager = RedisManager.getInstance();
