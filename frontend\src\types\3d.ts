/**
 * 3D Viewer Types
 * Type definitions for the frontend 3D visualization components
 */

export enum AssetType {
  MODEL_3D = 'MODEL_3D',
  TEXTURE = 'TEXTURE',
  MATERIAL = 'MATERIAL',
  ENVIRONMENT = 'ENVIRONMENT'
}

export enum AssetFormat {
  GLB = 'GLB',
  GLTF = 'GLTF',
  FBX = 'FBX',
  OBJ = 'OBJ',
  JPG = 'JPG',
  PNG = 'PNG',
  WEBP = 'WEBP',
  HDR = 'HDR',
  EXR = 'EXR'
}

export enum AssetQuality {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  ULTRA = 'ULTRA'
}

export interface Asset3D {
  id: string;
  productId?: string;
  name: string;
  description?: string;
  type: AssetType;
  format: AssetFormat;
  quality: AssetQuality;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  vertices?: number;
  faces?: number;
  width?: number;
  height?: number;
  variants?: Asset3DVariant[];
  tags: string[];
  downloadCount: number;
  viewCount: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Asset3DVariant {
  id: string;
  assetId: string;
  quality: AssetQuality;
  fileName: string;
  filePath: string;
  fileSize: number;
  vertices?: number;
  faces?: number;
  lodLevel?: number;
  width?: number;
  height?: number;
  compressionRatio?: number;
}

export interface MaterialDefinition {
  id: string;
  name: string;
  description?: string;
  baseColor?: string;
  metallic?: number;
  roughness?: number;
  normal?: number;
  emission?: string;
  emissionIntensity?: number;
  albedoMapId?: string;
  normalMapId?: string;
  roughnessMapId?: string;
  metallicMapId?: string;
  emissionMapId?: string;
  heightMapId?: string;
  occlusionMapId?: string;
  density?: number;
  hardness?: number;
  porosity?: number;
  tilingU?: number;
  tilingV?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ViewerConfiguration {
  id: string;
  productId: string;
  cameraPosition: { x: number; y: number; z: number };
  cameraTarget: { x: number; y: number; z: number };
  cameraFov: number;
  ambientLightColor: string;
  ambientLightIntensity: number;
  directionalLightColor: string;
  directionalLightIntensity: number;
  directionalLightPosition: { x: number; y: number; z: number };
  environmentMapId?: string;
  backgroundType: 'color' | 'environment' | 'transparent';
  backgroundColor: string;
  enableOrbitControls: boolean;
  enableZoom: boolean;
  enablePan: boolean;
  enableRotate: boolean;
  autoRotate: boolean;
  autoRotateSpeed: number;
  enableShadows: boolean;
  shadowMapSize: number;
  enableAntialiasing: boolean;
  pixelRatio: number;
  enableAnnotations: boolean;
  annotations?: Annotation[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Annotation {
  id: string;
  position: { x: number; y: number; z: number };
  title: string;
  description?: string;
  type: 'info' | 'warning' | 'feature' | 'dimension';
  visible: boolean;
}

export interface ViewerSession {
  id: string;
  sessionId: string;
  productId: string;
  userId?: string;
  viewDuration: number;
  interactionCount: number;
  zoomCount: number;
  rotationCount: number;
  annotationViews: number;
  userAgent?: string;
  deviceType?: 'mobile' | 'tablet' | 'desktop';
  screenResolution?: string;
  loadTime?: number;
  frameRate?: number;
  memoryUsage?: number;
  startedAt: Date;
  endedAt?: Date;
}

export interface ViewerCapabilities {
  webgl: boolean;
  webgl2: boolean;
  webgpu: boolean;
  maxTextureSize: number;
  maxVertexAttributes: number;
  supportedFormats: AssetFormat[];
  deviceMemory?: number;
  hardwareConcurrency?: number;
}

export interface LoadingProgress {
  stage: 'downloading' | 'parsing' | 'processing' | 'rendering';
  progress: number;
  message: string;
  bytesLoaded?: number;
  bytesTotal?: number;
}

export interface ViewerError {
  code: string;
  message: string;
  details?: any;
  recoverable: boolean;
}

export interface CameraState {
  position: { x: number; y: number; z: number };
  target: { x: number; y: number; z: number };
  zoom: number;
  fov: number;
}

export interface LightingState {
  ambientIntensity: number;
  directionalIntensity: number;
  directionalPosition: { x: number; y: number; z: number };
  shadowsEnabled: boolean;
}

export interface ViewerState {
  camera: CameraState;
  lighting: LightingState;
  annotations: Annotation[];
  performance: {
    frameRate: number;
    memoryUsage: number;
    drawCalls: number;
  };
}

export interface ViewerProps {
  productId: string;
  assets: Asset3D[];
  configuration?: ViewerConfiguration;
  materials?: MaterialDefinition[];
  className?: string;
  width?: number;
  height?: number;
  onLoad?: () => void;
  onError?: (error: ViewerError) => void;
  onProgress?: (progress: LoadingProgress) => void;
  onCameraChange?: (camera: CameraState) => void;
  onAnnotationClick?: (annotation: Annotation) => void;
  enableControls?: boolean;
  enableAnnotations?: boolean;
  enablePerformanceMonitor?: boolean;
  quality?: AssetQuality;
}

export interface ViewerControlsProps {
  onCameraReset?: () => void;
  onToggleWireframe?: () => void;
  onToggleAnnotations?: () => void;
  onToggleAutoRotate?: () => void;
  onQualityChange?: (quality: AssetQuality) => void;
  onMaterialChange?: (materialId: string) => void;
  materials?: MaterialDefinition[];
  currentQuality?: AssetQuality;
  currentMaterial?: string;
  showWireframe?: boolean;
  showAnnotations?: boolean;
  autoRotate?: boolean;
}

export interface AnnotationMarkerProps {
  annotation: Annotation;
  onClick?: (annotation: Annotation) => void;
  visible?: boolean;
}

export interface PerformanceMonitorProps {
  onPerformanceUpdate?: (metrics: {
    frameRate: number;
    memoryUsage: number;
    drawCalls: number;
  }) => void;
  visible?: boolean;
}

export interface MaterialEditorProps {
  material: MaterialDefinition;
  onMaterialChange: (material: MaterialDefinition) => void;
  availableTextures: Asset3D[];
}

export interface ViewerAnalytics {
  totalSessions: number;
  averageViewDuration: number;
  averageInteractions: number;
  deviceBreakdown: Record<string, number>;
  popularProducts: Array<{
    productId: string;
    viewCount: number;
    averageDuration: number;
  }>;
  performanceMetrics: {
    averageLoadTime: number;
    averageFrameRate: number;
    averageMemoryUsage: number;
  };
}

// API Response Types
export interface ViewerConfigResponse {
  success: boolean;
  configuration: ViewerConfiguration;
  assets: Asset3D[];
  materials: MaterialDefinition[];
}

export interface ViewerSessionResponse {
  success: boolean;
  sessionId: string;
  configuration: ViewerConfiguration;
  capabilities: ViewerCapabilities;
}

export interface AssetUploadResponse {
  success: boolean;
  assetId: string;
  uploadUrl?: string;
  message?: string;
}

// Hook Types
export interface UseViewerReturn {
  configuration: ViewerConfiguration | null;
  assets: Asset3D[];
  materials: MaterialDefinition[];
  session: ViewerSession | null;
  isLoading: boolean;
  error: ViewerError | null;
  startSession: () => Promise<void>;
  endSession: () => Promise<void>;
  updateSession: (updates: Partial<ViewerSession>) => Promise<void>;
  loadAssets: () => Promise<void>;
  clearError: () => void;
}

export interface UseViewerControlsReturn {
  cameraState: CameraState;
  lightingState: LightingState;
  annotations: Annotation[];
  resetCamera: () => void;
  updateCamera: (camera: Partial<CameraState>) => void;
  updateLighting: (lighting: Partial<LightingState>) => void;
  toggleAnnotation: (annotationId: string) => void;
  addAnnotation: (annotation: Omit<Annotation, 'id'>) => void;
  removeAnnotation: (annotationId: string) => void;
}

export interface UsePerformanceReturn {
  frameRate: number;
  memoryUsage: number;
  drawCalls: number;
  isMonitoring: boolean;
  startMonitoring: () => void;
  stopMonitoring: () => void;
}

// Store Types (for Zustand)
export interface ViewerStore {
  // State
  currentProduct: string | null;
  configuration: ViewerConfiguration | null;
  assets: Asset3D[];
  materials: MaterialDefinition[];
  session: ViewerSession | null;
  isLoading: boolean;
  error: ViewerError | null;
  
  // Camera
  cameraState: CameraState;
  
  // Lighting
  lightingState: LightingState;
  
  // Annotations
  annotations: Annotation[];
  showAnnotations: boolean;
  
  // Performance
  performance: {
    frameRate: number;
    memoryUsage: number;
    drawCalls: number;
  };
  
  // Actions
  setProduct: (productId: string) => void;
  setConfiguration: (config: ViewerConfiguration) => void;
  setAssets: (assets: Asset3D[]) => void;
  setMaterials: (materials: MaterialDefinition[]) => void;
  setSession: (session: ViewerSession) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: ViewerError | null) => void;
  updateCamera: (camera: Partial<CameraState>) => void;
  updateLighting: (lighting: Partial<LightingState>) => void;
  updatePerformance: (performance: Partial<ViewerStore['performance']>) => void;
  toggleAnnotations: () => void;
  addAnnotation: (annotation: Omit<Annotation, 'id'>) => void;
  removeAnnotation: (annotationId: string) => void;
  reset: () => void;
}

// RFC-801: Advanced 3D Visualization Types

// Dimension Configuration
export interface DimensionConfiguration {
  standardSizes: StandardSize[];
  customSize: CustomSizeConfig;
  pricing: DimensionPricing;
}

export interface StandardSize {
  width: number;
  height: number;
  thickness: number;
  label: string;
  category: 'floor' | 'wall' | 'countertop';
  applications: string[];
}

export interface CustomSizeConfig {
  enabled: boolean;
  minWidth: number;
  maxWidth: number;
  minHeight: number;
  maxHeight: number;
  thicknessOptions: number[];
  validation: {
    aspectRatio: { min: number; max: number; };
    structuralLimits: boolean;
    productionFeasibility: boolean;
  };
}

export interface DimensionPricing {
  baseCalculation: 'area' | 'perimeter' | 'volume';
  wastageMultiplier: number;
  cuttingCost: number;
  customSizePremium: number;
}

// Surface Finish Configuration
export interface SurfaceFinishConfiguration {
  availableFinishes: SurfaceFinishType[];
  defaultFinish: SurfaceFinishName;
  transitionAnimation: {
    duration: number;
    easing: string;
    steps: number;
  };
}

export type SurfaceFinishName = 'ham' | 'honlu' | 'cilali' | 'fircinlanmis' | 'yakma' | 'eskitme' | 'kumlama' | 'dolgu';

export interface SurfaceFinishType {
  name: SurfaceFinishName;
  description: string;
  roughness: number;
  metallic: number;
  normalIntensity: number;
  priceMultiplier: number;
  shaderUniforms: {
    roughnessMap?: string;
    normalMap?: string;
    displacementMap?: string;
    aoMap?: string;
  };
}

// Room Simulation Configuration
export interface RoomSimulationConfiguration {
  roomTemplates: RoomTemplate[];
  tilingPatterns: TilingPattern[];
  groutOptions: GroutOption[];
}

export interface RoomTemplate {
  id: string;
  name: string;
  type: 'bathroom' | 'kitchen' | 'living' | 'bedroom' | 'outdoor';
  style: 'modern' | 'classic' | 'rustic' | 'minimalist';
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
  fixtures: string[];
  lighting: 'bright_white' | 'warm_white' | 'ambient' | 'task_lighting';
  humidity: 'low' | 'medium' | 'high';
  applications: ('floor' | 'wall' | 'ceiling' | 'countertop')[];
}

export interface TilingPattern {
  name: string;
  description: string;
  algorithm: 'grid' | 'checkerboard' | 'herringbone' | 'diagonal' | 'random' | 'brick';
  parameters: {
    offsetX?: number;
    offsetY?: number;
    rotation?: number;
    angle?: number;
    spacing?: number;
    alternatePattern?: boolean;
    seed?: number;
    variation?: number;
  };
}

export interface GroutOption {
  width: number; // mm
  color: string; // hex color
  material: 'standard' | 'epoxy' | 'urethane';
}

// Product Configuration for Advanced Viewer
export interface AdvancedProductConfig {
  productId: string;
  dimensions: {
    width: number;
    height: number;
    thickness: number;
  };
  surfaceFinish: SurfaceFinishName;
  pattern?: TilingPattern;
  grout?: GroutOption;
  room?: RoomTemplate;
  placement?: {
    surface: 'floor' | 'wall' | 'ceiling' | 'countertop';
    area: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  };
}

// Removed price calculation types
