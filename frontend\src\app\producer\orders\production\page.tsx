'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Factory,
  DollarSign,
  Calendar,
  User,
  Package,
  MessageSquare,
  CheckCircle,
  Clock,
  Play,
  Pause
} from 'lucide-react'
import { MessageModal } from '@/components/ui/message-modal'
// import { OrderDetailsModal } from '@/components/ui/order-details-modal' // Not needed anymore, using dedicated page
import { ProductionPauseModal } from '@/components/ui/production-pause-modal'
import { OrderCompleteModal } from '@/components/ui/order-complete-modal'


// Mock data for production orders
const mockProductionOrders = [
  {
    id: 'ORD-001',
    customerName: 'ABC İnşaat Ltd.',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: '<PERSON><PERSON>er',
    quantity: 300,
    unit: 'm²',
    orderDate: '2025-06-25',
    deliveryDate: '2025-07-10',
    status: 'production',
    totalValue: 15000,
    currency: 'USD',
    paymentStatus: 'partial',
    paidAmount: 4500,
    specifications: {
      thickness: '2 cm',
      surface: 'Cilalı',
      packaging: 'Kasalı',
      delivery: 'Fabrika teslim'
    },
    productionStages: [
      { stage: 'Sipariş Onayı', completed: true, date: '2025-06-25', notes: 'Sipariş onaylandı' },
      { stage: 'Üretim Başlangıcı', completed: true, date: '2025-06-26', notes: 'Üretim başladı' },
      { stage: 'Kesim İşlemi', completed: true, date: '2025-06-28', notes: 'Kesim tamamlandı' },
      { stage: 'Yüzey İşlemi', completed: false, date: null, notes: 'Cilalama işlemi devam ediyor' },
      { stage: 'Kalite Kontrol', completed: false, date: null, notes: '' },
      { stage: 'Ambalajlama', completed: false, date: null, notes: '' },
      { stage: 'Sevkiyat', completed: false, date: null, notes: '' }
    ],
    deliveryAddress: 'İstanbul, Türkiye',
    notes: 'Otel projesi için kaliteli işçilik gerekli'
  },
  {
    id: 'ORD-002',
    customerName: 'Ahmet Yılmaz',
    customerEmail: '<EMAIL>',
    customerPhone: '+90 ************',
    productName: 'Beyaz Mermer',
    quantity: 1200,
    unit: 'm²',
    orderDate: '2025-06-20',
    deliveryDate: '2025-07-15',
    status: 'production',
    totalValue: 60000,
    currency: 'USD',
    paymentStatus: 'partial',
    paidAmount: 30000,
    specifications: {
      thickness: '3 cm',
      surface: 'Cilalı',
      packaging: 'Kasalı',
      delivery: 'Fabrika teslim'
    },
    productionStages: [
      { stage: 'Sipariş Onayı', completed: true, date: '2025-06-20', notes: 'Sipariş onaylandı' },
      { stage: 'Üretim Başlangıcı', completed: true, date: '2025-06-21', notes: 'Üretim başladı' },
      { stage: 'Kesim İşlemi', completed: true, date: '2025-06-25', notes: 'Kesim tamamlandı' },
      { stage: 'Yüzey İşlemi', completed: true, date: '2025-06-30', notes: 'Cilalama tamamlandı' },
      { stage: 'Kalite Kontrol', completed: true, date: '2025-07-02', notes: 'Kalite kontrolü geçti' },
      { stage: 'Ambalajlama', completed: true, date: '2025-07-03', notes: 'Ambalajlama tamamlandı' },
      { stage: 'Sevkiyat', completed: false, date: null, notes: '' }
    ],
    deliveryAddress: 'Ankara, Türkiye',
    notes: 'Büyük metrajlı proje - Çoklu teslimat gerekebilir'
  }
]

export default function ProductionOrders() {
  const { producer } = useProducerAuth()
  const [selectedOrder, setSelectedOrder] = React.useState<any>(null)
  const [isMessageModalOpen, setIsMessageModalOpen] = React.useState(false)
  // const [isDetailsModalOpen, setIsDetailsModalOpen] = React.useState(false) // Not needed anymore
  const [isPauseModalOpen, setIsPauseModalOpen] = React.useState(false)
  const [isCompleteModalOpen, setIsCompleteModalOpen] = React.useState(false)


  const getCompletionPercentage = (stages: any[]) => {
    const completedStages = stages.filter(stage => stage.completed).length
    return Math.round((completedStages / stages.length) * 100)
  }

  const handleUpdateStage = (orderId: string, stageIndex: number, completed: boolean) => {
    console.log('Updating stage:', orderId, stageIndex, completed)
    // Here you would update the stage in your API
  }

  const handlePauseProduction = (order: any) => {
    setSelectedOrder(order)
    setIsPauseModalOpen(true)
  }

  const handleSendMessage = (order: any) => {
    setSelectedOrder(order)
    setIsMessageModalOpen(true)
  }

  const handleViewDetails = (order: any) => {
    // Navigate to detailed order page instead of modal
    window.location.href = `/producer/orders/${order.id}`;
  }

  const handleCompleteOrder = (order: any) => {
    setSelectedOrder(order)
    setIsCompleteModalOpen(true)
  }

  const isOrderReadyToComplete = (order: any) => {
    return order.productionStages.every((stage: any) => stage.completed)
  }



  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Üretimdeki Siparişler</h1>
        <p className="text-gray-600">
          Aktif olarak üretimi devam eden siparişler
        </p>
      </div>

      {/* Production Orders List */}
      <div className="space-y-4">
        {mockProductionOrders.map((order) => (
          <Card key={order.id} className="overflow-hidden border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-blue-100">
                      <Factory className="w-4 h-4" />
                      Üretimde
                    </div>
                    <span className="text-sm font-medium">#{order.id}</span>
                    <div className="px-2 py-1 rounded-full text-xs font-medium text-yellow-600 bg-yellow-100">
                      Kısmi Ödendi
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <User className="w-4 h-4" />
                        Müşteri
                      </div>
                      <div className="font-medium">{order.customerName}</div>
                      <div className="text-sm text-gray-500">{order.customerEmail}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <Package className="w-4 h-4" />
                        Ürün
                      </div>
                      <div className="font-medium">{order.productName}</div>
                      <div className="text-sm text-gray-500">{order.quantity} {order.unit}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <Calendar className="w-4 h-4" />
                        Tarihler
                      </div>
                      <div className="font-medium">Sipariş: {order.orderDate}</div>
                      <div className="text-sm text-gray-500">Teslimat: {order.deliveryDate}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <DollarSign className="w-4 h-4" />
                        Tutar
                      </div>
                      <div className="font-medium">{order.totalValue.toLocaleString()} {order.currency}</div>
                      <div className="text-sm text-gray-500">
                        Ödenen: {order.paidAmount.toLocaleString()} {order.currency}
                      </div>
                    </div>
                  </div>

                  {/* Production Progress */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Üretim İlerlemesi</span>
                      <span className="text-sm text-gray-500">{getCompletionPercentage(order.productionStages)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${getCompletionPercentage(order.productionStages)}%` }}
                      />
                    </div>
                  </div>

                  {/* Current Stage */}
                  <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="font-medium text-blue-800 mb-2">Mevcut Aşama</h4>
                    {(() => {
                      const currentStage = order.productionStages.find(stage => !stage.completed)
                      return currentStage ? (
                        <div className="flex items-center justify-between">
                          <div>
                            <span className="font-medium text-blue-700">{currentStage.stage}</span>
                            {currentStage.notes && (
                              <p className="text-sm text-blue-600 mt-1">{currentStage.notes}</p>
                            )}
                          </div>
                          <Button
                            size="sm"
                            onClick={() => {
                              const stageIndex = order.productionStages.findIndex(s => s === currentStage)
                              handleUpdateStage(order.id, stageIndex, true)
                            }}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Tamamla
                          </Button>
                        </div>
                      ) : (
                        <span className="font-medium text-green-600">Tüm aşamalar tamamlandı</span>
                      )
                    })()}
                  </div>

                  {/* Order Details */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                    <div>
                      <span className="text-gray-500">Kalınlık:</span>
                      <span className="ml-2 font-medium">{order.specifications.thickness}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Yüzey:</span>
                      <span className="ml-2 font-medium">{order.specifications.surface}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Ambalaj:</span>
                      <span className="ml-2 font-medium">{order.specifications.packaging}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Teslimat:</span>
                      <span className="ml-2 font-medium">{order.specifications.delivery}</span>
                    </div>
                  </div>

                  {order.notes && (
                    <div className="mb-4">
                      <p className="text-sm text-gray-600 mb-1">Özel Notlar:</p>
                      <p className="text-sm">{order.notes}</p>
                    </div>
                  )}
                </div>

                <div className="flex flex-col gap-2 ml-4">
                  {isOrderReadyToComplete(order) ? (
                    <Button
                      size="sm"
                      onClick={() => handleCompleteOrder(order)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Siparişi Tamamla
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePauseProduction(order)}
                    >
                      <Pause className="w-4 h-4 mr-1" />
                      Üretimi Duraklat
                    </Button>
                  )}



                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSendMessage(order)}
                  >
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Müşteriyle İletişim
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetails(order)}
                  >
                    Detayları Gör
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {mockProductionOrders.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Factory className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Üretimdeki sipariş bulunamadı
            </h3>
            <p className="text-gray-600">
              Şu anda üretimi devam eden siparişiniz bulunmuyor.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Production Pause Modal */}
      <ProductionPauseModal
        isOpen={isPauseModalOpen}
        onClose={() => setIsPauseModalOpen(false)}
        order={selectedOrder}
      />

      {/* Message Modal */}
      <MessageModal
        isOpen={isMessageModalOpen}
        onClose={() => setIsMessageModalOpen(false)}
        recipientName={selectedOrder?.customerName || ''}
        recipientType="customer"
        orderId={selectedOrder?.id}
      />

      {/* Order Details Modal - Removed, using dedicated page instead */}

      {/* Order Complete Modal */}
      <OrderCompleteModal
        isOpen={isCompleteModalOpen}
        onClose={() => setIsCompleteModalOpen(false)}
        order={selectedOrder}
      />


    </div>
  )
}
