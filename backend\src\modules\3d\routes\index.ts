/**
 * 3D Module Routes
 * API routes for 3D viewer functionality
 */

import { Router } from 'express';
import { AssetController } from '../controllers/AssetController';
import { ViewerController } from '../controllers/ViewerController';
import { MaterialController } from '../controllers/MaterialController';

const router = Router();

// Asset routes
router.post('/assets/upload', AssetController.uploadAsset);
router.post('/assets/bulk-upload', AssetController.bulkUpload);
router.get('/assets/:id', AssetController.getAsset);
router.get('/assets/product/:productId', AssetController.getProductAssets);
router.get('/assets/:id/download', AssetController.downloadAsset);
router.delete('/assets/:id', AssetController.deleteAsset);
router.post('/assets/:id/optimize', AssetController.optimizeAsset);
router.get('/assets/:id/status', AssetController.getProcessingStatus);
router.get('/assets', AssetController.getAssetLibrary);

// Viewer routes
router.get('/viewer/config/:productId', ViewerController.getConfiguration);
router.put('/viewer/config/:productId', ViewerController.updateConfiguration);
router.post('/viewer/session/start', ViewerController.startSession);
router.put('/viewer/session/:sessionId', ViewerController.updateSession);
router.post('/viewer/session/:sessionId/end', ViewerController.endSession);
router.get('/viewer/analytics', ViewerController.getAnalytics);

// Material routes
router.get('/materials/product/:productId', MaterialController.getProductMaterials);
router.post('/materials', MaterialController.createMaterial);
router.put('/materials/:id', MaterialController.updateMaterial);
router.delete('/materials/:id', MaterialController.deleteMaterial);
router.get('/materials/:id', MaterialController.getMaterial);

// Annotation routes
router.post('/viewer/:productId/annotations', ViewerController.addAnnotation);
router.put('/viewer/:productId/annotations/:annotationId', ViewerController.updateAnnotation);
router.delete('/viewer/:productId/annotations/:annotationId', ViewerController.deleteAnnotation);

export default router;
