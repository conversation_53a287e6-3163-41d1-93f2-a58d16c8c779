'use client'

import React from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from './card'
import { But<PERSON> } from './button'
import { Badge } from './badge'
import { 
  Search,
  Package,
  Plus,
  Minus,
  X,
  Check,
  Image as ImageIcon,
  Calculator,
  ShoppingCart
} from 'lucide-react'

// Mock product data (filtered by selected producer)
const mockProducts = [
  {
    id: 'prod-1',
    name: '<PERSON><PERSON><PERSON>az Mermer',
    category: 'Mermer',
    image: '/api/placeholder/300/200',
    producerId: 'prod-001',
    basePrice: 100,
    currency: 'USD',
    unit: 'm²',
    availableSpecs: [
      { thickness: '2', width: '60', length: '120', stock: 500 },
      { thickness: '3', width: '80', length: '160', stock: 300 },
      { thickness: '2', width: '40', length: '80', stock: 200 }
    ],
    surfaceOptions: ['Ham', 'Honlu', 'C<PERSON>lı', 'Fır<PERSON>lı'],
    packagingOptions: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>'],
    deliveryOptions: ['Fabrika', 'Liman', 'FOB']
  },
  {
    id: 'prod-2',
    name: '<PERSON><PERSON><PERSON> Traverten',
    category: 'Traverten',
    image: '/api/placeholder/300/200',
    producerId: 'prod-001',
    basePrice: 85,
    currency: 'USD',
    unit: 'm²',
    availableSpecs: [
      { thickness: '2', width: '40', length: '80', stock: 400 },
      { thickness: '3', width: '60', length: '120', stock: 250 }
    ],
    surfaceOptions: ['Ham', 'Honlu', 'Cilalı'],
    packagingOptions: ['Kasalı', 'Bandıllı'],
    deliveryOptions: ['Fabrika', 'Liman']
  },
  {
    id: 'prod-3',
    name: 'Oniks Yeşil',
    category: 'Oniks',
    image: '/api/placeholder/300/200',
    producerId: 'prod-002',
    basePrice: 150,
    currency: 'USD',
    unit: 'm²',
    availableSpecs: [
      { thickness: '2', width: '60', length: '120', stock: 100 },
      { thickness: '3', width: '80', length: '160', stock: 80 }
    ],
    surfaceOptions: ['Cilalı', 'Fırçalı'],
    packagingOptions: ['Özel ambalaj'],
    deliveryOptions: ['Liman', 'FOB']
  }
]

interface ProductSelectionStepProps {
  formData: any
  setFormData: (data: any) => void
}

interface SelectedProduct {
  productId: string
  productName: string
  category: string
  image: string
  specifications: Array<{
    id: string
    thickness: string
    width: string
    length: string
    surface: string
    packaging: string
    delivery: string
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
}

export function ProductSelectionStep({ formData, setFormData }: ProductSelectionStepProps) {
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedCategory, setSelectedCategory] = React.useState('all')
  const [selectedProducts, setSelectedProducts] = React.useState<SelectedProduct[]>(formData.products || [])

  // Filter products by selected producer
  const availableProducts = mockProducts.filter(product => 
    formData.producer ? product.producerId === formData.producer.id : true
  )

  const categories = ['all', ...Array.from(new Set(availableProducts.map(p => p.category)))]

  const filteredProducts = availableProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const addProduct = (product: any) => {
    const newProduct: SelectedProduct = {
      productId: product.id,
      productName: product.name,
      category: product.category,
      image: product.image,
      specifications: []
    }
    
    const updatedProducts = [...selectedProducts, newProduct]
    setSelectedProducts(updatedProducts)
    setFormData({ ...formData, products: updatedProducts })
  }

  const removeProduct = (productIndex: number) => {
    const updatedProducts = selectedProducts.filter((_, index) => index !== productIndex)
    setSelectedProducts(updatedProducts)
    setFormData({ ...formData, products: updatedProducts })
  }

  const addSpecification = (productIndex: number) => {
    const product = availableProducts.find(p => p.id === selectedProducts[productIndex].productId)
    if (!product) return

    const newSpec = {
      id: `spec-${Date.now()}`,
      thickness: product.availableSpecs[0]?.thickness || '2',
      width: product.availableSpecs[0]?.width || '60',
      length: product.availableSpecs[0]?.length || '120',
      surface: product.surfaceOptions[0] || 'Ham',
      packaging: product.packagingOptions[0] || 'Kasalı',
      delivery: product.deliveryOptions[0] || 'Fabrika',
      quantity: 1,
      unitPrice: product.basePrice,
      totalPrice: product.basePrice
    }

    const updatedProducts = [...selectedProducts]
    updatedProducts[productIndex].specifications.push(newSpec)
    setSelectedProducts(updatedProducts)
    setFormData({ ...formData, products: updatedProducts })
  }

  const removeSpecification = (productIndex: number, specIndex: number) => {
    const updatedProducts = [...selectedProducts]
    updatedProducts[productIndex].specifications.splice(specIndex, 1)
    setSelectedProducts(updatedProducts)
    setFormData({ ...formData, products: updatedProducts })
  }

  const updateSpecification = (productIndex: number, specIndex: number, field: string, value: any) => {
    const updatedProducts = [...selectedProducts]
    const spec = updatedProducts[productIndex].specifications[specIndex]
    
    spec[field as keyof typeof spec] = value
    
    // Recalculate total price when quantity or unit price changes
    if (field === 'quantity' || field === 'unitPrice') {
      spec.totalPrice = spec.quantity * spec.unitPrice
    }
    
    setSelectedProducts(updatedProducts)
    setFormData({ ...formData, products: updatedProducts })
  }

  const getTotalAmount = () => {
    return selectedProducts.reduce((total, product) => 
      total + product.specifications.reduce((productTotal, spec) => 
        productTotal + spec.totalPrice, 0
      ), 0
    )
  }

  const getTotalQuantity = () => {
    return selectedProducts.reduce((total, product) => 
      total + product.specifications.reduce((productTotal, spec) => 
        productTotal + spec.quantity, 0
      ), 0
    )
  }

  const isProductAlreadySelected = (productId: string) => {
    return selectedProducts.some(p => p.productId === productId)
  }

  return (
    <div className="space-y-6">
      {!formData.producer && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800">
            Ürün seçimi yapabilmek için önce bir üretici seçmelisiniz.
          </p>
        </div>
      )}

      {formData.producer && (
        <>
          {/* Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Ürün ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'Tüm Kategoriler' : category}
                </option>
              ))}
            </select>
          </div>

          {/* Available Products */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                Mevcut Ürünler ({formData.producer.companyName})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredProducts.map((product) => (
                  <Card 
                    key={product.id} 
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      isProductAlreadySelected(product.id) ? 'ring-2 ring-green-500 bg-green-50' : ''
                    }`}
                  >
                    <CardContent className="p-4">
                      <div className="aspect-video bg-gray-200 rounded-lg mb-3 flex items-center justify-center overflow-hidden">
                        {product.image ? (
                          <img 
                            src={product.image} 
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <ImageIcon className="w-8 h-8 text-gray-400" />
                        )}
                      </div>
                      
                      <h3 className="font-semibold text-gray-900 mb-1">{product.name}</h3>
                      <Badge variant="outline" className="mb-2">{product.category}</Badge>
                      
                      <div className="text-sm text-gray-600 mb-3">
                        <p>Başlangıç fiyat: <span className="font-semibold">${product.basePrice}/{product.unit}</span></p>
                        <p>{product.availableSpecs.length} farklı ebat</p>
                      </div>

                      <Button
                        size="sm"
                        className="w-full"
                        onClick={() => addProduct(product)}
                        disabled={isProductAlreadySelected(product.id)}
                      >
                        {isProductAlreadySelected(product.id) ? (
                          <>
                            <Check className="w-4 h-4 mr-2" />
                            Seçildi
                          </>
                        ) : (
                          <>
                            <Plus className="w-4 h-4 mr-2" />
                            Ürünü Ekle
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredProducts.length === 0 && (
                <div className="text-center py-8">
                  <Package className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-600">Bu üreticiye ait ürün bulunamadı.</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Selected Products */}
          {selectedProducts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="w-5 h-5" />
                  Seçilen Ürünler ({selectedProducts.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {selectedProducts.map((selectedProduct, productIndex) => {
                  const originalProduct = availableProducts.find(p => p.id === selectedProduct.productId)
                  if (!originalProduct) return null

                  return (
                    <div key={productIndex} className="border border-gray-200 rounded-lg p-4">
                      {/* Product Header */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                            {selectedProduct.image ? (
                              <img 
                                src={selectedProduct.image} 
                                alt={selectedProduct.productName}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <ImageIcon className="w-6 h-6 text-gray-400" />
                            )}
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">{selectedProduct.productName}</h4>
                            <Badge variant="outline">{selectedProduct.category}</Badge>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeProduct(productIndex)}
                        >
                          <X className="w-4 h-4 mr-1" />
                          Kaldır
                        </Button>
                      </div>

                      {/* Specifications */}
                      <div className="space-y-3">
                        {selectedProduct.specifications.map((spec, specIndex) => (
                          <div key={spec.id} className="grid grid-cols-1 md:grid-cols-8 gap-3 p-3 bg-gray-50 rounded-lg">
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">Kalınlık</label>
                              <select
                                value={spec.thickness}
                                onChange={(e) => updateSpecification(productIndex, specIndex, 'thickness', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              >
                                {originalProduct.availableSpecs.map(s => (
                                  <option key={s.thickness} value={s.thickness}>{s.thickness}cm</option>
                                ))}
                              </select>
                            </div>
                            
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">En</label>
                              <select
                                value={spec.width}
                                onChange={(e) => updateSpecification(productIndex, specIndex, 'width', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              >
                                {originalProduct.availableSpecs.map(s => (
                                  <option key={s.width} value={s.width}>{s.width}cm</option>
                                ))}
                              </select>
                            </div>
                            
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">Boy</label>
                              <select
                                value={spec.length}
                                onChange={(e) => updateSpecification(productIndex, specIndex, 'length', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              >
                                {originalProduct.availableSpecs.map(s => (
                                  <option key={s.length} value={s.length}>{s.length}cm</option>
                                ))}
                              </select>
                            </div>
                            
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">Yüzey</label>
                              <select
                                value={spec.surface}
                                onChange={(e) => updateSpecification(productIndex, specIndex, 'surface', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              >
                                {originalProduct.surfaceOptions.map(option => (
                                  <option key={option} value={option}>{option}</option>
                                ))}
                              </select>
                            </div>
                            
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">Miktar</label>
                              <input
                                type="number"
                                min="1"
                                value={spec.quantity}
                                onChange={(e) => updateSpecification(productIndex, specIndex, 'quantity', parseInt(e.target.value) || 1)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              />
                            </div>
                            
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">Birim Fiyat</label>
                              <input
                                type="number"
                                min="0"
                                step="0.01"
                                value={spec.unitPrice}
                                onChange={(e) => updateSpecification(productIndex, specIndex, 'unitPrice', parseFloat(e.target.value) || 0)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              />
                            </div>
                            
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">Toplam</label>
                              <div className="px-2 py-1 text-sm font-semibold text-green-600 bg-white border border-gray-300 rounded">
                                ${spec.totalPrice.toFixed(2)}
                              </div>
                            </div>
                            
                            <div className="flex items-end">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => removeSpecification(productIndex, specIndex)}
                              >
                                <Minus className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => addSpecification(productIndex)}
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Yeni Ebat Ekle
                        </Button>
                      </div>
                    </div>
                  )
                })}

                {/* Summary */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calculator className="w-5 h-5 text-blue-600" />
                      <span className="font-medium text-blue-900">Sipariş Özeti</span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-blue-700">
                        Toplam {getTotalQuantity()} m² • {selectedProducts.length} ürün
                      </p>
                      <p className="text-lg font-bold text-blue-900">
                        ${getTotalAmount().toFixed(2)}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  )
}
