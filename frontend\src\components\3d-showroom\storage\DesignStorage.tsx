'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { 
  Save, 
  FolderOpen, 
  Trash2, 
  Download, 
  Upload, 
  Star,
  StarOff,
  Calendar,
  Eye,
  Copy,
  Share2,
  X
} from 'lucide-react';
import { ProductPlacement, ShowroomConfiguration } from '../core/ShowroomEngine';

export interface SavedDesign {
  id: string;
  name: string;
  description: string;
  products: ProductPlacement[];
  configuration: ShowroomConfiguration;
  thumbnail: string;
  createdAt: string;
  updatedAt: string;
  isFavorite: boolean;
  tags: string[];
  totalArea: number;
  productCount: number;
}

interface DesignStorageProps {
  currentProducts: ProductPlacement[];
  currentConfiguration: ShowroomConfiguration;
  onLoadDesign: (design: SavedDesign) => void;
  onClose: () => void;
  className?: string;
}

export const DesignStorage: React.FC<DesignStorageProps> = ({
  currentProducts,
  currentConfiguration,
  onLoadDesign,
  onClose,
  className = ''
}) => {
  const [savedDesigns, setSavedDesigns] = useState<SavedDesign[]>([]);
  const [activeTab, setActiveTab] = useState<'save' | 'load'>('save');
  const [saveForm, setSaveForm] = useState({
    name: '',
    description: '',
    tags: ''
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'name' | 'area'>('date');
  const [filterFavorites, setFilterFavorites] = useState(false);

  // Load saved designs from localStorage on mount
  useEffect(() => {
    loadSavedDesigns();
  }, []);

  const loadSavedDesigns = useCallback(() => {
    try {
      const saved = localStorage.getItem('showroom-designs');
      if (saved) {
        const designs = JSON.parse(saved) as SavedDesign[];
        setSavedDesigns(designs);
      }
    } catch (error) {
      console.error('Tasarımlar yüklenirken hata:', error);
    }
  }, []);

  const saveDesign = useCallback(async () => {
    if (!saveForm.name.trim()) {
      alert('Lütfen tasarım adı girin');
      return;
    }

    const newDesign: SavedDesign = {
      id: `design-${Date.now()}`,
      name: saveForm.name.trim(),
      description: saveForm.description.trim(),
      products: currentProducts,
      configuration: currentConfiguration,
      thumbnail: await generateThumbnail(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isFavorite: false,
      tags: saveForm.tags.split(',').map(tag => tag.trim()).filter(Boolean),
      totalArea: currentConfiguration.totalArea,
      productCount: currentProducts.length
    };

    const updatedDesigns = [...savedDesigns, newDesign];
    setSavedDesigns(updatedDesigns);
    
    try {
      localStorage.setItem('showroom-designs', JSON.stringify(updatedDesigns));
      alert('Tasarım başarıyla kaydedildi!');
      setSaveForm({ name: '', description: '', tags: '' });
      setActiveTab('load');
    } catch (error) {
      alert('Tasarım kaydedilirken hata oluştu');
    }
  }, [saveForm, currentProducts, currentConfiguration, savedDesigns]);

  const generateThumbnail = useCallback(async (): Promise<string> => {
    // In a real implementation, this would capture the 3D scene
    // For now, return a placeholder
    return `data:image/svg+xml,${encodeURIComponent(`
      <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="150" fill="#f3f4f6"/>
        <rect x="20" y="20" width="160" height="110" fill="#e5e7eb" stroke="#d1d5db"/>
        <text x="100" y="80" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="12">
          ${currentProducts.length} Ürün
        </text>
        <text x="100" y="100" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="10">
          ${currentConfiguration.totalArea.toFixed(1)} m²
        </text>
      </svg>
    `)}`;
  }, [currentProducts.length, currentConfiguration.totalArea]);

  const deleteDesign = useCallback((designId: string) => {
    if (confirm('Bu tasarımı silmek istediğinizden emin misiniz?')) {
      const updatedDesigns = savedDesigns.filter(d => d.id !== designId);
      setSavedDesigns(updatedDesigns);
      localStorage.setItem('showroom-designs', JSON.stringify(updatedDesigns));
    }
  }, [savedDesigns]);

  const toggleFavorite = useCallback((designId: string) => {
    const updatedDesigns = savedDesigns.map(design =>
      design.id === designId 
        ? { ...design, isFavorite: !design.isFavorite }
        : design
    );
    setSavedDesigns(updatedDesigns);
    localStorage.setItem('showroom-designs', JSON.stringify(updatedDesigns));
  }, [savedDesigns]);

  const duplicateDesign = useCallback((design: SavedDesign) => {
    const duplicated: SavedDesign = {
      ...design,
      id: `design-${Date.now()}`,
      name: `${design.name} (Kopya)`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isFavorite: false
    };

    const updatedDesigns = [...savedDesigns, duplicated];
    setSavedDesigns(updatedDesigns);
    localStorage.setItem('showroom-designs', JSON.stringify(updatedDesigns));
  }, [savedDesigns]);

  const exportDesign = useCallback((design: SavedDesign) => {
    const dataStr = JSON.stringify(design, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${design.name.replace(/[^a-z0-9]/gi, '_')}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  }, []);

  const importDesign = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const design = JSON.parse(e.target?.result as string) as SavedDesign;
        design.id = `design-${Date.now()}`;
        design.name = `${design.name} (İçe Aktarılan)`;
        design.createdAt = new Date().toISOString();
        design.updatedAt = new Date().toISOString();

        const updatedDesigns = [...savedDesigns, design];
        setSavedDesigns(updatedDesigns);
        localStorage.setItem('showroom-designs', JSON.stringify(updatedDesigns));
        
        alert('Tasarım başarıyla içe aktarıldı!');
      } catch (error) {
        alert('Geçersiz dosya formatı');
      }
    };
    reader.readAsText(file);
    
    // Reset input
    event.target.value = '';
  }, [savedDesigns]);

  // Filter and sort designs
  const filteredDesigns = savedDesigns
    .filter(design => {
      const matchesSearch = design.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           design.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           design.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesFavorite = !filterFavorites || design.isFavorite;
      return matchesSearch && matchesFavorite;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'area':
          return b.totalArea - a.totalArea;
        default: // date
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
      }
    });

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Tasarım Yönetimi</h3>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <X size={20} />
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => setActiveTab('save')}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === 'save'
              ? 'text-amber-600 border-b-2 border-amber-600 bg-amber-50'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Save size={16} className="mx-auto mb-1" />
          Kaydet
        </button>
        
        <button
          onClick={() => setActiveTab('load')}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === 'load'
              ? 'text-amber-600 border-b-2 border-amber-600 bg-amber-50'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <FolderOpen size={16} className="mx-auto mb-1" />
          Yükle ({savedDesigns.length})
        </button>
      </div>

      {/* Content */}
      <div className="p-4">
        {activeTab === 'save' ? (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tasarım Adı *
              </label>
              <input
                type="text"
                value={saveForm.name}
                onChange={(e) => setSaveForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Örn: Modern Salon Tasarımı"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Açıklama
              </label>
              <textarea
                value={saveForm.description}
                onChange={(e) => setSaveForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Tasarım hakkında kısa açıklama..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Etiketler (virgülle ayırın)
              </label>
              <input
                type="text"
                value={saveForm.tags}
                onChange={(e) => setSaveForm(prev => ({ ...prev, tags: e.target.value }))}
                placeholder="modern, salon, mermer"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              />
            </div>

            <div className="bg-gray-50 rounded-lg p-3">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Mevcut Tasarım</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Ürün Sayısı:</span>
                  <div className="font-medium">{currentProducts.length}</div>
                </div>
                <div>
                  <span className="text-gray-600">Toplam Alan:</span>
                  <div className="font-medium">{currentConfiguration.totalArea.toFixed(2)} m²</div>
                </div>
                <div>
                  <span className="text-gray-600">Mekan:</span>
                  <div className="font-medium capitalize">{currentConfiguration.room}</div>
                </div>
                <div>
                  <span className="text-gray-600">Işık:</span>
                  <div className="font-medium capitalize">{currentConfiguration.lighting.preset}</div>
                </div>
              </div>
            </div>

            <button
              onClick={saveDesign}
              disabled={!saveForm.name.trim()}
              className="w-full px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              <Save size={16} />
              Tasarımı Kaydet
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Search and Filters */}
            <div className="flex gap-2">
              <input
                type="text"
                placeholder="Tasarım ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              />
              
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              >
                <option value="date">Tarihe Göre</option>
                <option value="name">İsme Göre</option>
                <option value="area">Alana Göre</option>
              </select>
              
              <button
                onClick={() => setFilterFavorites(!filterFavorites)}
                className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                  filterFavorites
                    ? 'bg-amber-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Star size={16} />
              </button>
            </div>

            {/* Import Button */}
            <div className="flex gap-2">
              <label className="flex-1 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors cursor-pointer text-sm font-medium flex items-center justify-center gap-2">
                <Upload size={16} />
                Tasarım İçe Aktar
                <input
                  type="file"
                  accept=".json"
                  onChange={importDesign}
                  className="hidden"
                />
              </label>
            </div>

            {/* Designs List */}
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {filteredDesigns.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <FolderOpen size={32} className="mx-auto mb-2 opacity-50" />
                  <p className="text-sm">
                    {searchTerm ? 'Arama kriterine uygun tasarım bulunamadı' : 'Henüz kaydedilmiş tasarım yok'}
                  </p>
                </div>
              ) : (
                filteredDesigns.map((design) => (
                  <div
                    key={design.id}
                    className="border border-gray-200 rounded-lg p-3 hover:border-gray-300 transition-colors"
                  >
                    <div className="flex items-start gap-3">
                      <img
                        src={design.thumbnail}
                        alt={design.name}
                        className="w-16 h-12 object-cover rounded bg-gray-100"
                      />
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {design.name}
                          </h4>
                          {design.isFavorite && (
                            <Star size={14} className="text-amber-500 fill-current" />
                          )}
                        </div>
                        
                        {design.description && (
                          <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                            {design.description}
                          </p>
                        )}
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>{design.productCount} ürün</span>
                          <span>{design.totalArea.toFixed(1)} m²</span>
                          <span>{new Date(design.updatedAt).toLocaleDateString('tr-TR')}</span>
                        </div>
                        
                        {design.tags.length > 0 && (
                          <div className="flex gap-1 mt-2">
                            {design.tags.slice(0, 3).map((tag, index) => (
                              <span
                                key={index}
                                className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                              >
                                {tag}
                              </span>
                            ))}
                            {design.tags.length > 3 && (
                              <span className="text-xs text-gray-500">+{design.tags.length - 3}</span>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex flex-col gap-1">
                        <button
                          onClick={() => onLoadDesign(design)}
                          className="p-1 text-green-600 hover:bg-green-50 rounded transition-colors"
                          title="Yükle"
                        >
                          <Eye size={14} />
                        </button>
                        
                        <button
                          onClick={() => toggleFavorite(design.id)}
                          className="p-1 text-amber-600 hover:bg-amber-50 rounded transition-colors"
                          title="Favorilere Ekle/Çıkar"
                        >
                          {design.isFavorite ? <Star size={14} /> : <StarOff size={14} />}
                        </button>
                        
                        <button
                          onClick={() => duplicateDesign(design)}
                          className="p-1 text-blue-600 hover:bg-blue-50 rounded transition-colors"
                          title="Kopyala"
                        >
                          <Copy size={14} />
                        </button>
                        
                        <button
                          onClick={() => exportDesign(design)}
                          className="p-1 text-purple-600 hover:bg-purple-50 rounded transition-colors"
                          title="Dışa Aktar"
                        >
                          <Download size={14} />
                        </button>
                        
                        <button
                          onClick={() => deleteDesign(design.id)}
                          className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                          title="Sil"
                        >
                          <Trash2 size={14} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DesignStorage;
