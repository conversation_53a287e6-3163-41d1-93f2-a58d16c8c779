/**
 * Chatbot Routes
 * Defines API routes for chatbot functionality
 */

import { Router } from 'express';
import { ChatbotController, chatbotValidation } from '../controllers/ChatbotController';
import { authMiddleware } from '../../../middleware/authMiddleware';
import { AuthenticatedRequest } from '../../../types/express';

const router = Router();
const chatbotController = new ChatbotController();

/**
 * @swagger
 * components:
 *   schemas:
 *     ChatMessage:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         role:
 *           type: string
 *           enum: [user, assistant, system]
 *         content:
 *           type: string
 *         timestamp:
 *           type: string
 *           format: date-time
 *         language:
 *           type: string
 *         intent:
 *           type: string
 *         confidence:
 *           type: number
 *         entities:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ExtractedEntity'
 *     
 *     ExtractedEntity:
 *       type: object
 *       properties:
 *         type:
 *           type: string
 *         value:
 *           type: string
 *         confidence:
 *           type: number
 *         start:
 *           type: number
 *         end:
 *           type: number
 *     
 *     ChatbotResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         sessionId:
 *           type: string
 *         response:
 *           type: string
 *         confidence:
 *           type: number
 *         intent:
 *           type: string
 *         entities:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ExtractedEntity'
 *         suggestions:
 *           type: array
 *           items:
 *             type: string
 *         escalated:
 *           type: boolean
 *         agentId:
 *           type: string
 *         estimatedWaitTime:
 *           type: number
 */

/**
 * @swagger
 * /api/chatbot/start:
 *   post:
 *     summary: Start new conversation
 *     tags: [Chatbot]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: Optional user ID for authenticated users
 *               language:
 *                 type: string
 *                 description: Preferred language (default: en)
 *                 example: en
 *     responses:
 *       200:
 *         description: Conversation started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     sessionId:
 *                       type: string
 *                     greeting:
 *                       type: string
 *       400:
 *         description: Validation error
 *       500:
 *         description: Server error
 */
router.post('/start', 
  chatbotValidation.startConversation,
  chatbotController.startConversation.bind(chatbotController)
);

/**
 * @swagger
 * /api/chatbot/message:
 *   post:
 *     summary: Send message to chatbot
 *     tags: [Chatbot]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *               - message
 *             properties:
 *               sessionId:
 *                 type: string
 *                 description: Conversation session ID
 *               message:
 *                 type: string
 *                 description: User message
 *                 maxLength: 1000
 *               userId:
 *                 type: string
 *                 description: Optional user ID for authenticated users
 *     responses:
 *       200:
 *         description: Message processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/ChatbotResponse'
 *       400:
 *         description: Validation error
 *       500:
 *         description: Server error
 */
router.post('/message',
  chatbotValidation.sendMessage,
  chatbotController.sendMessage.bind(chatbotController)
);

/**
 * @swagger
 * /api/chatbot/conversations/{sessionId}/history:
 *   get:
 *     summary: Get conversation history
 *     tags: [Chatbot]
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Conversation session ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Maximum number of messages to return
 *     responses:
 *       200:
 *         description: Conversation history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     sessionId:
 *                       type: string
 *                     messages:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ChatMessage'
 *                     count:
 *                       type: number
 *       400:
 *         description: Validation error
 *       404:
 *         description: Conversation not found
 *       500:
 *         description: Server error
 */
router.get('/conversations/:sessionId/history',
  chatbotValidation.getHistory,
  chatbotController.getConversationHistory.bind(chatbotController)
);

/**
 * @swagger
 * /api/chatbot/feedback:
 *   post:
 *     summary: Submit feedback for chatbot response
 *     tags: [Chatbot]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *               - messageId
 *               - rating
 *             properties:
 *               sessionId:
 *                 type: string
 *                 description: Conversation session ID
 *               messageId:
 *                 type: string
 *                 description: Message ID being rated
 *               rating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 description: Rating from 1 (poor) to 5 (excellent)
 *               feedback:
 *                 type: string
 *                 maxLength: 500
 *                 description: Optional feedback text
 *     responses:
 *       200:
 *         description: Feedback submitted successfully
 *       400:
 *         description: Validation error
 *       500:
 *         description: Server error
 */
router.post('/feedback',
  chatbotValidation.submitFeedback,
  chatbotController.submitFeedback.bind(chatbotController)
);

/**
 * @swagger
 * /api/chatbot/conversations/{sessionId}/stats:
 *   get:
 *     summary: Get conversation statistics
 *     tags: [Chatbot]
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Conversation session ID
 *     responses:
 *       200:
 *         description: Conversation statistics retrieved successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Conversation not found
 *       500:
 *         description: Server error
 */
router.get('/conversations/:sessionId/stats',
  chatbotValidation.getConversationStats,
  chatbotController.getConversationStats.bind(chatbotController)
);

/**
 * @swagger
 * /api/chatbot/conversations/{sessionId}:
 *   delete:
 *     summary: Delete conversation
 *     tags: [Chatbot]
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Conversation session ID
 *     responses:
 *       200:
 *         description: Conversation deleted successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Conversation not found
 *       500:
 *         description: Server error
 */
router.delete('/conversations/:sessionId',
  chatbotValidation.deleteConversation,
  chatbotController.deleteConversation.bind(chatbotController)
);

/**
 * @swagger
 * /api/chatbot/health:
 *   get:
 *     summary: Health check for chatbot service
 *     tags: [Chatbot]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 timestamp:
 *                   type: string
 *                 version:
 *                   type: string
 *       500:
 *         description: Service is unhealthy
 */
router.get('/health',
  chatbotController.healthCheck.bind(chatbotController)
);

// Admin routes (require authentication and admin role)
/**
 * @swagger
 * /api/chatbot/analytics:
 *   get:
 *     summary: Get chatbot analytics (Admin only)
 *     tags: [Chatbot Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Analytics retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden (admin role required)
 *       500:
 *         description: Server error
 */
router.get('/analytics',
  authMiddleware,
  // TODO: Add admin role check middleware
  chatbotController.getAnalytics.bind(chatbotController)
);

/**
 * @swagger
 * /api/chatbot/conversations:
 *   get:
 *     summary: Get all active conversations (Admin only)
 *     tags: [Chatbot Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Active conversations retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden (admin role required)
 *       500:
 *         description: Server error
 */
router.get('/conversations',
  authMiddleware,
  // TODO: Add admin role check middleware
  chatbotController.getActiveConversations.bind(chatbotController)
);

/**
 * @swagger
 * /api/chatbot/escalation-criteria:
 *   get:
 *     summary: Get escalation criteria (Admin only)
 *     tags: [Chatbot Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Escalation criteria retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden (admin role required)
 *       500:
 *         description: Server error
 *   put:
 *     summary: Update escalation criteria (Admin only)
 *     tags: [Chatbot Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lowConfidenceThreshold:
 *                 type: number
 *               negativeSentimentThreshold:
 *                 type: number
 *               multipleUnresolvedIssues:
 *                 type: number
 *               conversationLength:
 *                 type: number
 *     responses:
 *       200:
 *         description: Escalation criteria updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden (admin role required)
 *       500:
 *         description: Server error
 */
router.get('/escalation-criteria',
  authMiddleware,
  // TODO: Add admin role check middleware
  chatbotController.getEscalationCriteria.bind(chatbotController)
);

router.put('/escalation-criteria',
  authMiddleware,
  // TODO: Add admin role check middleware
  chatbotController.updateEscalationCriteria.bind(chatbotController)
);

/**
 * @swagger
 * /api/chatbot/agents:
 *   get:
 *     summary: Get available agents (Admin only)
 *     tags: [Chatbot Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Available agents retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden (admin role required)
 *       500:
 *         description: Server error
 */
router.get('/agents',
  authMiddleware,
  // TODO: Add admin role check middleware
  chatbotController.getAvailableAgents.bind(chatbotController)
);

/**
 * @swagger
 * /api/chatbot/agents/{agentId}/availability:
 *   put:
 *     summary: Update agent availability (Admin only)
 *     tags: [Chatbot Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: agentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - available
 *             properties:
 *               available:
 *                 type: boolean
 *                 description: Agent availability status
 *     responses:
 *       200:
 *         description: Agent availability updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden (admin role required)
 *       404:
 *         description: Agent not found
 *       500:
 *         description: Server error
 */
router.put('/agents/:agentId/availability',
  authMiddleware,
  // TODO: Add admin role check middleware
  chatbotValidation.updateAgentAvailability,
  chatbotController.updateAgentAvailability.bind(chatbotController)
);

export default router;
