"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./card"
import { Badge } from "./badge"
import { 
  Clock,
  CheckCircle,
  Package,
  CreditCard,
  Truck,
  MessageSquare,
  FileText,
  User,
  Calendar,
  AlertTriangle,
  XCircle,
  DollarSign,
  Edit
} from "lucide-react"
import { OrderEvent } from "@/types/orders"

interface OrderTimelineProps {
  events: OrderEvent[]
  className?: string
}

export function OrderTimeline({ events, className = "" }: OrderTimelineProps) {
  const getEventIcon = (type: string) => {
    switch (type) {
      case 'status-change':
        return CheckCircle
      case 'payment':
        return DollarSign
      case 'production':
        return Package
      case 'shipment':
        return Truck
      case 'delivery':
        return CheckCircle
      case 'note':
        return MessageSquare
      default:
        return Clock
    }
  }

  const getEventColor = (type: string) => {
    switch (type) {
      case 'status-change':
        return 'bg-blue-100 text-blue-600 border-blue-200'
      case 'payment':
        return 'bg-green-100 text-green-600 border-green-200'
      case 'production':
        return 'bg-orange-100 text-orange-600 border-orange-200'
      case 'shipment':
        return 'bg-purple-100 text-purple-600 border-purple-200'
      case 'delivery':
        return 'bg-green-100 text-green-600 border-green-200'
      case 'note':
        return 'bg-gray-100 text-gray-600 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200'
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('tr-TR', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  const sortedEvents = [...events].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  )

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Sipariş Geçmişi
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>
          
          {/* Timeline Events */}
          <div className="space-y-6">
            {sortedEvents.map((event, index) => {
              const Icon = getEventIcon(event.type)
              const colorClass = getEventColor(event.type)
              
              return (
                <div key={event.id} className="relative flex items-start gap-4">
                  {/* Event Icon */}
                  <div className={`
                    relative z-10 flex items-center justify-center w-12 h-12 rounded-full border-2
                    ${colorClass}
                  `}>
                    <Icon className="w-5 h-5" />
                  </div>
                  
                  {/* Event Content */}
                  <div className="flex-1 min-w-0 pb-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="text-sm font-semibold text-gray-900 mb-1">
                          {event.title}
                        </h4>
                        <p className="text-sm text-gray-600 mb-2">
                          {event.description}
                        </p>
                        
                        {/* Event Metadata */}
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <User className="w-3 h-3" />
                            <span>{event.userName}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            <span>{formatDate(event.timestamp)}</span>
                          </div>
                        </div>
                        
                        {/* Additional Metadata */}
                        {event.metadata && Object.keys(event.metadata).length > 0 && (
                          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                            <h5 className="text-xs font-medium text-gray-700 mb-2">Ek Bilgiler:</h5>
                            <div className="space-y-1">
                              {Object.entries(event.metadata).map(([key, value]) => (
                                <div key={key} className="flex justify-between text-xs">
                                  <span className="text-gray-600 capitalize">{key}:</span>
                                  <span className="text-gray-900 font-medium">{String(value)}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      
                      {/* Event Type Badge */}
                      <Badge variant="outline" className="ml-2">
                        {event.type === 'status-change' && 'Durum Değişikliği'}
                        {event.type === 'payment' && 'Ödeme'}
                        {event.type === 'production' && 'Üretim'}
                        {event.type === 'shipment' && 'Sevkiyat'}
                        {event.type === 'delivery' && 'Teslimat'}
                        {event.type === 'note' && 'Not'}
                      </Badge>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
          
          {/* Empty State */}
          {sortedEvents.length === 0 && (
            <div className="text-center py-8">
              <Clock className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600">Henüz sipariş geçmişi bulunmuyor.</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Compact Timeline for smaller spaces
export function OrderTimelineCompact({ events, className = "" }: OrderTimelineProps) {
  const getEventIcon = (type: string) => {
    switch (type) {
      case 'status-change':
        return CheckCircle
      case 'payment':
        return DollarSign
      case 'production':
        return Package
      case 'shipment':
        return Truck
      case 'delivery':
        return CheckCircle
      case 'note':
        return MessageSquare
      default:
        return Clock
    }
  }

  const getEventColor = (type: string) => {
    switch (type) {
      case 'status-change':
        return 'text-blue-600'
      case 'payment':
        return 'text-green-600'
      case 'production':
        return 'text-orange-600'
      case 'shipment':
        return 'text-purple-600'
      case 'delivery':
        return 'text-green-600'
      case 'note':
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('tr-TR', {
      day: '2-digit',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  const sortedEvents = [...events].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  ).slice(0, 5) // Show only last 5 events

  return (
    <div className={`space-y-3 ${className}`}>
      <h4 className="text-sm font-medium text-gray-900 mb-3">Son Aktiviteler</h4>
      {sortedEvents.map((event) => {
        const Icon = getEventIcon(event.type)
        const colorClass = getEventColor(event.type)
        
        return (
          <div key={event.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
            <Icon className={`w-4 h-4 mt-0.5 ${colorClass}`} />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900">{event.title}</p>
              <p className="text-xs text-gray-600 mt-1">{event.description}</p>
              <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                <span>{event.userName}</span>
                <span>•</span>
                <span>{formatDate(event.timestamp)}</span>
              </div>
            </div>
          </div>
        )
      })}
      
      {sortedEvents.length === 0 && (
        <div className="text-center py-4">
          <Clock className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600">Henüz aktivite bulunmuyor.</p>
        </div>
      )}
    </div>
  )
}
