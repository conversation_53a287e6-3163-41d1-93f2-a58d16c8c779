'use client'

import * as React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  CheckCircle,
  Clock,
  Play,
  Pause,
  User,
  Calendar,
  Package,
  AlertCircle,
  Edit
} from 'lucide-react'
import { DeliveryPackage, ProductionSchedule } from '@/types/multi-delivery'
import { getPackageProgress } from '@/data/mock-multi-delivery'

interface ProductionScheduleCardProps {
  deliveryPackage: DeliveryPackage
  onUpdateStage: (packageId: string, stageId: string, status: ProductionSchedule['status']) => void
  onPauseProduction: (packageId: string) => void
  onViewDetails: (packageId: string) => void
  onEditPackage?: (pkg: DeliveryPackage) => void
}

export function ProductionScheduleCard({
  deliveryPackage,
  onUpdateStage,
  onPauseProduction,
  onViewDetails,
  onEditPackage
}: ProductionScheduleCardProps) {
  const progress = getPackageProgress(deliveryPackage)
  
  const getStatusColor = (status: ProductionSchedule['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'paused':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: ProductionSchedule['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'in_progress':
        return <Play className="w-4 h-4" />
      case 'paused':
        return <Pause className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  const getPackageStatusColor = (status: DeliveryPackage['productionStatus']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'paused':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  return (
    <Card className={`overflow-hidden ${getPackageStatusColor(deliveryPackage.productionStatus)}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Package className="w-5 h-5" />
            Paket #{deliveryPackage.packageNumber}
          </CardTitle>
          <Badge className={getStatusColor(deliveryPackage.productionStatus)}>
            {deliveryPackage.productionStatus === 'completed' && 'Tamamlandı'}
            {deliveryPackage.productionStatus === 'in_progress' && 'Üretimde'}
            {deliveryPackage.productionStatus === 'paused' && 'Duraklatıldı'}
            {deliveryPackage.productionStatus === 'pending' && 'Bekliyor'}
          </Badge>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <span className="font-medium">Miktar:</span> {deliveryPackage.quantity} m²
          </div>
          <div>
            <span className="font-medium">Tutar:</span> ${deliveryPackage.amount.toLocaleString()}
          </div>
          <div>
            <span className="font-medium">Başlangıç:</span> {formatDate(deliveryPackage.productionStartDate)}
          </div>
          <div>
            <span className="font-medium">Bitiş:</span> {formatDate(deliveryPackage.productionEndDate)}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">İlerleme</span>
            <span className="text-sm text-gray-600">
              {progress.completedStages}/{progress.totalStages} aşama
            </span>
          </div>
          <Progress value={progress.progressPercentage} className="h-2" />
          <div className="text-xs text-gray-500 mt-1">
            %{progress.progressPercentage} tamamlandı
          </div>
        </div>

        {/* Current Stage */}
        {progress.currentStage && (
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-800 mb-2 flex items-center gap-2">
              <Play className="w-4 h-4" />
              Mevcut Aşama
            </h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium text-blue-700">{progress.currentStage.stageName}</span>
                <Button
                  size="sm"
                  onClick={() => onUpdateStage(deliveryPackage.id, progress.currentStage!.id, 'completed')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Tamamla
                </Button>
              </div>
              
              {progress.currentStage.assignedWorker && (
                <div className="flex items-center gap-2 text-sm text-blue-600">
                  <User className="w-4 h-4" />
                  <span>{progress.currentStage.assignedWorker}</span>
                </div>
              )}
              
              {progress.currentStage.plannedEndDate && (
                <div className="flex items-center gap-2 text-sm text-blue-600">
                  <Calendar className="w-4 h-4" />
                  <span>Planlanan Bitiş: {formatDate(progress.currentStage.plannedEndDate)}</span>
                </div>
              )}
              
              {progress.currentStage.notes && (
                <p className="text-sm text-blue-600">{progress.currentStage.notes}</p>
              )}
            </div>
          </div>
        )}

        {/* Production Stages List */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Üretim Aşamaları</h4>
          <div className="space-y-2">
            {deliveryPackage.productionSchedules
              .sort((a, b) => a.stageOrder - b.stageOrder)
              .map((stage) => (
                <div
                  key={stage.id}
                  className={`flex items-center justify-between p-2 rounded border ${
                    stage.status === 'completed' ? 'bg-green-50 border-green-200' :
                    stage.status === 'in_progress' ? 'bg-blue-50 border-blue-200' :
                    stage.status === 'paused' ? 'bg-orange-50 border-orange-200' :
                    'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <Badge className={getStatusColor(stage.status)} variant="outline">
                      {getStatusIcon(stage.status)}
                    </Badge>
                    <div>
                      <span className="font-medium text-sm">{stage.stageName}</span>
                      {stage.assignedWorker && (
                        <p className="text-xs text-gray-600">{stage.assignedWorker}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    {stage.actualEndDate ? (
                      <span className="text-xs text-green-600">
                        {formatDate(stage.actualEndDate)}
                      </span>
                    ) : stage.plannedEndDate ? (
                      <span className="text-xs text-gray-500">
                        {formatDate(stage.plannedEndDate)}
                      </span>
                    ) : null}
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Delays Warning */}
        {deliveryPackage.productionStatus === 'paused' && (
          <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
            <div className="flex items-start gap-2">
              <AlertCircle className="w-5 h-5 text-orange-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium text-orange-800">Üretim Duraklatıldı</p>
                <p className="text-sm text-orange-700 mt-1">
                  Bu paket üretimi duraklatılmış durumda. Teslimat tarihi etkilenebilir.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2 border-t">
          {deliveryPackage.productionStatus === 'in_progress' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPauseProduction(deliveryPackage.id)}
              className="flex-1"
            >
              <Pause className="w-4 h-4 mr-1" />
              Duraklat
            </Button>
          )}

          {onEditPackage && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEditPackage(deliveryPackage)}
              className="flex-1"
            >
              <Edit className="w-4 h-4 mr-1" />
              Düzenle
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={() => onViewDetails(deliveryPackage.id)}
            className="flex-1"
          >
            Detayları Gör
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
