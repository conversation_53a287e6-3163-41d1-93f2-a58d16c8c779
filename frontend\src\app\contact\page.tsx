'use client';

import React from 'react';
import { Navigation } from "@/components/ui/navigation";
import { Button } from "@/components/ui/button";
import { useSettings } from '@/contexts/settings-context';
import { useAuth } from '@/contexts/auth-context';

// Metadata moved to layout or separate metadata file

const navigationLinks = [
  { name: "<PERSON> Sayfa", href: "/" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/products" },
  { name: "3D Sanal Showroom", href: "/3d-showroom" },
  { name: "<PERSON><PERSON><PERSON>", href: "/news" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>", href: "/about" },
  { name: "<PERSON>let<PERSON>ş<PERSON>", href: "/contact", active: true }
];

// Gerçek iletişim bilgileri admin ayarlarından gelecek - şimdilik boş array
const contactInfo: any[] = [];

// Gerçek departman bilgileri admin ayarlarından gelecek - şimdilik boş array
const departments: any[] = [];

export default function ContactPage() {
  const { settings, isLoading } = useSettings();
  const { user, isAuthenticated, logout, showLoginModal, showCustomerRegisterModal } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50">
      <Navigation
        brand={{
          name: "Türkiye Doğal Taş Pazarı",
          href: "/"
        }}
        links={navigationLinks}
        actions={
          <div className="flex items-center gap-2">
            {!isAuthenticated ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={showLoginModal}
                >
                  Giriş Yap
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={showCustomerRegisterModal}
                >
                  Kayıt Ol
                </Button>
              </>
            ) : (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 max-w-[200px] truncate">
                  Hoş geldiniz, {user?.company || user?.name}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={logout}
                >
                  Çıkış Yap
                </Button>
              </div>
            )}
          </div>
        }
      />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-stone-900 mb-4">
            İletişim
          </h1>
          <p className="text-xl text-stone-600 max-w-3xl mx-auto">
            Size nasıl yardımcı olabiliriz? Sorularınız için bize ulaşın, 
            uzman ekibimiz en kısa sürede size dönüş yapacaktır.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {/* Contact Form */}
          <div className="bg-white rounded-lg p-8 shadow-sm border border-stone-200">
            <h2 className="text-2xl font-bold text-stone-900 mb-6">
              Bize Mesaj Gönderin
            </h2>
            <form className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-stone-700 mb-2">
                    Ad Soyad *
                  </label>
                  <input
                    type="text"
                    required
                    className="w-full px-3 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    placeholder="Adınız ve soyadınız"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-stone-700 mb-2">
                    E-posta *
                  </label>
                  <input
                    type="email"
                    required
                    className="w-full px-3 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-stone-700 mb-2">
                    Telefon
                  </label>
                  <input
                    type="tel"
                    className="w-full px-3 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    placeholder="+90 5XX XXX XX XX"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-stone-700 mb-2">
                    Konu
                  </label>
                  <select className="w-full px-3 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500">
                    <option value="">Konu seçin</option>
                    <option value="sales">Satış Bilgileri</option>
                    <option value="support">Teknik Destek</option>
                    <option value="producer">Üretici Başvurusu</option>
                    <option value="partnership">İş Ortaklığı</option>
                    <option value="other">Diğer</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-stone-700 mb-2">
                  Mesajınız *
                </label>
                <textarea
                  required
                  rows={6}
                  className="w-full px-3 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  placeholder="Mesajınızı buraya yazın..."
                />
              </div>

              <Button type="submit" variant="primary" size="lg" className="w-full">
                Mesaj Gönder
              </Button>
            </form>
          </div>

          {/* Contact Info */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-stone-200">
              <h3 className="text-xl font-semibold text-stone-900 mb-4">
                Hızlı İletişim
              </h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="text-2xl">📞</div>
                  <div>
                    <div className="font-medium text-stone-900">Telefon</div>
                    <div className="text-stone-600">
                      {isLoading ? 'Yükleniyor...' : settings.contact.phone}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-2xl">📧</div>
                  <div>
                    <div className="font-medium text-stone-900">E-posta</div>
                    <div className="text-stone-600">
                      {isLoading ? 'Yükleniyor...' : settings.contact.email}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-2xl">💬</div>
                  <div>
                    <div className="font-medium text-stone-900">WhatsApp</div>
                    <div className="text-stone-600">
                      {isLoading ? 'Yükleniyor...' : settings.contact.whatsappNumber}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-2xl">⏰</div>
                  <div>
                    <div className="font-medium text-stone-900">Çalışma Saatleri</div>
                    <div className="text-stone-600">
                      {isLoading ? 'Yükleniyor...' : settings.contact.workingHours}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Departments */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-stone-200">
              <h3 className="text-xl font-semibold text-stone-900 mb-4">
                Departmanlar
              </h3>
              <div className="space-y-4">
                {departments.map((dept, index) => (
                  <div key={index} className="border-b border-stone-100 last:border-b-0 pb-3 last:pb-0">
                    <div className="flex items-start gap-3">
                      <div className="text-xl">{dept.icon}</div>
                      <div className="flex-1">
                        <div className="font-medium text-stone-900">{dept.name}</div>
                        <div className="text-sm text-stone-600 mb-1">{dept.description}</div>
                        <div className="text-sm text-amber-600">{dept.email}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Office Locations */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-stone-900 mb-12">
            Ofislerimiz
          </h2>
          <div className="grid md:grid-cols-3 gap-6">
            {contactInfo.map((office, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm border border-stone-200 text-center">
                <div className="text-4xl mb-4">{office.icon}</div>
                <h3 className="text-xl font-semibold text-stone-900 mb-3">
                  {office.title}
                </h3>
                <div className="space-y-2 text-stone-600">
                  <p className="text-sm">{office.address}</p>
                  <p className="text-sm">{office.city}</p>
                  <p className="text-sm font-medium text-amber-600">{office.phone}</p>
                  <p className="text-sm text-amber-600">{office.email}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Map Section */}
        <div className="bg-white rounded-lg p-8 shadow-sm border border-stone-200">
          <h2 className="text-2xl font-bold text-stone-900 mb-6 text-center">
            Konum
          </h2>
          <div className="aspect-video bg-gradient-to-br from-stone-200 to-stone-300 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4">🗺️</div>
              <p className="text-stone-600">
                Harita entegrasyonu yakında eklenecektir.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
