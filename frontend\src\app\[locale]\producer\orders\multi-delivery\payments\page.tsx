'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, DollarSign, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react'
import { PaymentScheduleCard } from '@/components/ui/payment-schedule-card'
import { mockMultiDeliveryOrder } from '@/data/mock-multi-delivery'

export default function PaymentSchedulePage() {
  const { producer } = useProducerAuth()
  const [order, setOrder] = React.useState(mockMultiDeliveryOrder)

  const handleRecordPayment = (packageId: string, paymentId: string) => {
    console.log('Recording payment:', { packageId, paymentId })
    
    setOrder(prevOrder => ({
      ...prevOrder,
      deliveryPackages: prevOrder.deliveryPackages.map(pkg => {
        if (pkg.id === packageId) {
          return {
            ...pkg,
            payments: pkg.payments.map(payment => {
              if (payment.id === paymentId) {
                return {
                  ...payment,
                  status: 'paid' as const,
                  paidDate: new Date().toISOString().split('T')[0],
                  transactionId: `TXN-${Date.now()}`,
                  paymentMethod: 'bank_transfer'
                }
              }
              return payment
            })
          }
        }
        return pkg
      })
    }))

    alert('Ödeme kaydedildi!')
  }

  const handleSendReminder = (packageId: string, paymentId: string) => {
    console.log('Sending payment reminder:', { packageId, paymentId })
    
    const pkg = order.deliveryPackages.find(p => p.id === packageId)
    const payment = pkg?.payments.find(p => p.id === paymentId)
    
    if (payment) {
      alert(`Ödeme hatırlatması gönderildi!\n\nTutar: $${payment.amount.toLocaleString()}\nVade: ${new Date(payment.dueDate).toLocaleDateString('tr-TR')}`)
    }
  }

  const handleViewInvoice = (packageId: string, paymentId: string) => {
    console.log('Viewing invoice:', { packageId, paymentId })
    
    const pkg = order.deliveryPackages.find(p => p.id === packageId)
    const payment = pkg?.payments.find(p => p.id === paymentId)
    
    if (payment) {
      alert(`Fatura Detayları:\n\nPaket: #${pkg?.packageNumber}\nTutar: $${payment.amount.toLocaleString()}\nTip: ${payment.paymentType}\nDurum: ${payment.status}\nVade: ${new Date(payment.dueDate).toLocaleDateString('tr-TR')}`)
    }
  }

  const handleGoBack = () => {
    window.location.href = '/producer/orders/multi-delivery'
  }

  // Overall payment statistics
  const overallPaymentStats = React.useMemo(() => {
    const allPayments = order.deliveryPackages.flatMap(pkg => pkg.payments)
    const totalAmount = order.totalAmount
    const paidAmount = allPayments
      .filter(p => p.status === 'paid')
      .reduce((sum, p) => sum + p.amount, 0)
    const pendingAmount = allPayments
      .filter(p => p.status === 'pending')
      .reduce((sum, p) => sum + p.amount, 0)
    const overdueAmount = allPayments
      .filter(p => p.status === 'overdue')
      .reduce((sum, p) => sum + p.amount, 0)
    
    return {
      totalAmount,
      paidAmount,
      pendingAmount,
      overdueAmount,
      remainingAmount: totalAmount - paidAmount,
      paymentPercentage: Math.round((paidAmount / totalAmount) * 100),
      totalPayments: allPayments.length,
      paidPayments: allPayments.filter(p => p.status === 'paid').length,
      pendingPayments: allPayments.filter(p => p.status === 'pending').length,
      overduePayments: allPayments.filter(p => p.status === 'overdue').length
    }
  }, [order])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleGoBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Geri Dön
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Ödeme Takibi</h1>
          <p className="text-gray-600">
            Sipariş #{order.id} - Paket bazlı ödeme planı ve takibi
          </p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex gap-4 border-b">
        <Button
          variant="ghost"
          onClick={() => window.location.href = '/producer/orders/multi-delivery'}
        >
          Üretim Takibi
        </Button>
        <Button
          variant="ghost"
          onClick={() => window.location.href = '/producer/orders/multi-delivery/delivery'}
        >
          Teslimat Takvimi
        </Button>
        <Button
          variant="ghost"
          className="border-b-2 border-blue-600 text-blue-600"
        >
          Ödeme Takibi
        </Button>
      </div>

      {/* Overall Payment Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Genel Ödeme Durumu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-blue-800">Toplam Tutar</span>
              </div>
              <p className="text-2xl font-bold text-blue-900">${overallPaymentStats.totalAmount.toLocaleString()}</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="font-medium text-green-800">Ödenen</span>
              </div>
              <p className="text-2xl font-bold text-green-900">${overallPaymentStats.paidAmount.toLocaleString()}</p>
              <p className="text-sm text-green-700">%{overallPaymentStats.paymentPercentage}</p>
            </div>
            
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="w-5 h-5 text-yellow-600" />
                <span className="font-medium text-yellow-800">Bekleyen</span>
              </div>
              <p className="text-2xl font-bold text-yellow-900">${overallPaymentStats.pendingAmount.toLocaleString()}</p>
              <p className="text-sm text-yellow-700">{overallPaymentStats.pendingPayments} ödeme</p>
            </div>
            
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                <span className="font-medium text-red-800">Gecikmiş</span>
              </div>
              <p className="text-2xl font-bold text-red-900">${overallPaymentStats.overdueAmount.toLocaleString()}</p>
              <p className="text-sm text-red-700">{overallPaymentStats.overduePayments} ödeme</p>
            </div>
          </div>

          {/* Progress Bar */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">Ödeme İlerlemesi</span>
              <span className="text-sm text-gray-600">
                ${overallPaymentStats.paidAmount.toLocaleString()} / ${overallPaymentStats.totalAmount.toLocaleString()}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-green-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${overallPaymentStats.paymentPercentage}%` }}
              ></div>
            </div>
            <div className="text-sm text-gray-500 mt-1">
              %{overallPaymentStats.paymentPercentage} tamamlandı
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overdue Alert */}
      {overallPaymentStats.overdueAmount > 0 && (
        <div className="bg-red-50 p-4 rounded-lg border border-red-200">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-6 h-6 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-red-800 mb-1">Gecikmiş Ödemeler!</h3>
              <p className="text-red-700">
                ${overallPaymentStats.overdueAmount.toLocaleString()} tutarında {overallPaymentStats.overduePayments} adet gecikmiş ödeme bulunmaktadır. 
                Müşterilerle iletişime geçerek ödeme durumunu takip edin.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Payment Packages */}
      <div>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Paket Ödemeleri</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {order.deliveryPackages
            .sort((a, b) => a.packageNumber - b.packageNumber)
            .map((pkg) => (
              <PaymentScheduleCard
                key={pkg.id}
                deliveryPackage={pkg}
                onRecordPayment={handleRecordPayment}
                onSendReminder={handleSendReminder}
                onViewInvoice={handleViewInvoice}
              />
            ))}
        </div>
      </div>

      {/* Development Info */}
      <div className="bg-green-50 p-4 rounded-lg border border-green-200">
        <h3 className="font-semibold text-green-800 mb-2">💰 Ödeme Yönetimi</h3>
        <p className="text-sm text-green-700">
          Bu sayfa paket bazlı ödeme takibi ve yönetimi için oluşturulmuştur.
        </p>
        <div className="mt-3 text-xs text-green-600">
          <p><strong>Özellikler:</strong></p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Paket bazlı ödeme planlaması</li>
            <li>Ödeme durumu takibi</li>
            <li>Gecikmiş ödeme uyarıları</li>
            <li>Ödeme hatırlatması gönderme</li>
            <li>Fatura görüntüleme</li>
            <li>Genel ödeme istatistikleri</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
