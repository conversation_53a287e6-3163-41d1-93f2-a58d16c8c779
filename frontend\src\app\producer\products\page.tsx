'use client'

import * as React from 'react'
import { useRouter } from 'next/navigation'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { useProducts } from '@/contexts/products-context'
import { useStock } from '@/contexts/stock-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Package,
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Archive
} from 'lucide-react'
import { ProductFormModal } from '@/components/ui/product-form-modal'
import { DeleteProductModal } from '@/components/ui/delete-product-modal'
import { AddStockModal } from '@/components/ui/add-stock-modal'

// Mock data temizlendi - gerçek veriler API'den gelecek

export default function ProducerProducts() {
  const router = useRouter()
  const { producer } = useProducerAuth()
  const { products, addProduct, updateProduct, deleteProduct } = useProducts()
  const { addStockItems } = useStock()
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedCategory, setSelectedCategory] = React.useState('all')
  const [selectedApprovalStatus, setSelectedApprovalStatus] = React.useState('all')
  const [isProductModalOpen, setIsProductModalOpen] = React.useState(false)
  const [editingProduct, setEditingProduct] = React.useState<any>(null)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = React.useState(false)
  const [deletingProduct, setDeletingProduct] = React.useState<any>(null)
  const [isDeleting, setIsDeleting] = React.useState(false)
  const [isStockModalOpen, setIsStockModalOpen] = React.useState(false)
  const [stockProduct, setStockProduct] = React.useState<any>(null)
  const [isAddingStock, setIsAddingStock] = React.useState(false)

  // Onay kontrolü
  if (!producer?.isApproved) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="p-6 max-w-md mx-auto text-center">
          <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Hesap Onayı Bekleniyor</h2>
          <p className="text-gray-600 mb-6">
            Bu sayfaya erişebilmek için hesabınızın admin tarafından onaylanması gerekiyor.
          </p>
          <div className="flex justify-center gap-4">
            <Button
              onClick={() => router.push('/producer/settings')}
              className="bg-amber-500 text-white hover:bg-amber-600"
            >
              Ayarları Düzenle
            </Button>
            <Button
              onClick={() => router.push('/producer/dashboard')}
              variant="outline"
            >
              Dashboard'a Dön
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  const categories = ['all', 'Mermer', 'Traverten', 'Granit', 'Oniks']
  const approvalStatuses = [
    { value: 'all', label: 'Tüm Durumlar' },
    { value: 'pending', label: 'Admin Onayında' },
    { value: 'approved', label: 'Onaylandı' },
    { value: 'rejected', label: 'Reddedildi' },
    { value: 'draft', label: 'Taslak/Pasif' }
  ]

  const getApprovalStatusBadge = (approvalStatus: string, rejectionReason?: string) => {
    switch (approvalStatus) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Admin Onayında</Badge>
      case 'approved':
        return <Badge variant="secondary" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Onaylandı</Badge>
      case 'rejected':
        return <Badge variant="secondary" className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Reddedildi</Badge>
      default:
        return null
    }
  }

  // Mock ve gerçek ürünleri birleştir
  const allProducts = [...mockProducts, ...products]

  const filteredProducts = allProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory

    // Onay durumu filtresi
    let matchesApprovalStatus = false
    if (selectedApprovalStatus === 'all') {
      matchesApprovalStatus = true
    } else if (selectedApprovalStatus === 'draft') {
      // Taslak/Pasif: approvalStatus olmayan veya draft/inactive status'u olan
      matchesApprovalStatus = !product.approvalStatus || product.status === 'draft' || product.status === 'inactive'
    } else {
      matchesApprovalStatus = product.approvalStatus === selectedApprovalStatus
    }

    return matchesSearch && matchesCategory && matchesApprovalStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100'
      case 'draft':
        return 'text-yellow-600 bg-yellow-100'
      case 'inactive':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktif'
      case 'draft':
        return 'Taslak'
      case 'inactive':
        return 'Pasif'
      default:
        return status
    }
  }

  const handleProductSave = async (productData: any) => {
    try {
      console.log('Saving product:', productData)

      if (editingProduct) {
        // Düzenleme modu - mevcut ürünü güncelle
        const updates = {
          ...productData,
          // Eğer durum aktif ise ve önceden reddedilmişse, tekrar pending yap
          approvalStatus: productData.status === 'active' && editingProduct.approvalStatus === 'rejected'
            ? 'pending'
            : productData.approvalStatus || editingProduct.approvalStatus,
          rejectionReason: productData.status === 'active' && editingProduct.approvalStatus === 'rejected'
            ? undefined
            : editingProduct.rejectionReason,
          submittedAt: productData.status === 'active' && editingProduct.approvalStatus === 'rejected'
            ? new Date()
            : editingProduct.submittedAt
        }
        updateProduct(editingProduct.id, updates)
      } else {
        // Yeni ürün ekleme
        const newProduct = {
          ...productData,
          id: Date.now().toString(), // Basit ID üretimi
          createdAt: new Date().toISOString().split('T')[0],
          stock: 0,
          unit: 'm²',
          basePrice: 0,
          currency: 'USD',
          producer: producer?.companyName || 'Test Üretici',
          description: productData.description || 'Ürün açıklaması',
          image: productData.media?.coverImage?.url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'
        }
        addProduct(newProduct)
      }

      setIsProductModalOpen(false)
      setEditingProduct(null)
      return true
    } catch (error) {
      console.error('Error saving product:', error)
      return false
    }
  }

  const handleEditProduct = (product: any) => {
    setEditingProduct(product)
    setIsProductModalOpen(true)
  }

  const handleAddProduct = () => {
    router.push('/producer/products/add')
  }

  const handleViewProduct = (product: any) => {
    router.push(`/producer/products/${product.id}`)
  }

  const handleDeleteProduct = (product: any) => {
    setDeletingProduct(product)
    setIsDeleteModalOpen(true)
  }

  const confirmDeleteProduct = async (reason: string) => {
    if (!deletingProduct) return

    setIsDeleting(true)
    try {
      deleteProduct(deletingProduct.id, reason)
      setIsDeleteModalOpen(false)
      setDeletingProduct(null)
    } catch (error) {
      console.error('Error deleting product:', error)
      alert('Ürün silinirken bir hata oluştu')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleAddStock = (product: any) => {
    setStockProduct(product)
    setIsStockModalOpen(true)
  }

  const confirmAddStock = async (stockItems: any[]) => {
    if (!stockProduct) return

    setIsAddingStock(true)
    try {
      addStockItems(
        stockProduct.id,
        stockProduct.name,
        producer?.companyName || 'Bilinmeyen Üretici',
        stockItems
      )
      alert('Stok ürünler admin onayına gönderildi!')
      setIsStockModalOpen(false)
      setStockProduct(null)
    } catch (error) {
      console.error('Error adding stock:', error)
      alert('Stok eklenirken bir hata oluştu')
    } finally {
      setIsAddingStock(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Ürünlerim</h1>
          <p className="text-gray-600">
            Ürünlerinizi yönetin ve yeni ürünler ekleyin
          </p>
        </div>
        <Button
          className="bg-amber-600 hover:bg-amber-700"
          onClick={handleAddProduct}
        >
          <Plus className="w-4 h-4 mr-2" />
          Yeni Ürün Ekle
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Ürün ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div className="md:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'Tüm Kategoriler' : category}
                  </option>
                ))}
              </select>
            </div>

            {/* Approval Status Filter */}
            <div className="md:w-48">
              <select
                value={selectedApprovalStatus}
                onChange={(e) => setSelectedApprovalStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                {approvalStatuses.map(status => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProducts.map((product) => (
          <Card key={product.id} className="overflow-hidden">
            <div className="aspect-video bg-gray-200 relative">
              <img
                src={product.image}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwTDEyNSA3NUgxNzVMMTUwIDEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE1IDIwQzE3Ljc2MTQgMjAgMjAgMTcuNzYxNCAyMCAxNUMyMCAxMi4yMzg2IDE3Ljc2MTQgMTAgMTUgMTBDMTIuMjM4NiAxMCAxMCAxMi4yMzg2IDEwIDE1QzEwIDE3Ljc2MTQgMTIuMjM4NiAyMCAxNSAyMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+Cjwvc3ZnPgo='
                }}
              />
              <div className="absolute top-2 right-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
                  {getStatusText(product.status)}
                </span>
              </div>
            </div>
            
            <CardContent className="p-4">
              <div className="space-y-2">
                <h3 className="font-semibold text-lg">{product.name}</h3>
                <p className="text-sm text-gray-600">{product.category}</p>

                {/* Onay Durumu */}
                {product.approvalStatus && (
                  <div className="mb-2">
                    {getApprovalStatusBadge(product.approvalStatus, product.rejectionReason)}
                  </div>
                )}

                {/* Red Sebebi */}
                {product.approvalStatus === 'rejected' && product.rejectionReason && (
                  <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-red-800">Red Sebebi:</p>
                        <p className="text-sm text-red-700">{product.rejectionReason}</p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Stok:</span>
                  <span className="font-medium">{product.stock} {product.unit}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Fiyat:</span>
                  <span className="font-medium">${product.basePrice} / {product.unit}</span>
                </div>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Oluşturulma:</span>
                  <span>{product.createdAt}</span>
                </div>
              </div>
              
              <div className="space-y-2 mt-4">
                <div className="flex gap-2">
                  {product.approvalStatus === 'rejected' ? (
                    <Button
                      size="sm"
                      className="flex-1 bg-amber-600 hover:bg-amber-700"
                      onClick={() => handleEditProduct(product)}
                    >
                      <Edit className="w-4 h-4 mr-1" />
                      Düzenle ve Tekrar Gönder
                    </Button>
                  ) : (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => handleViewProduct(product)}
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        Görüntüle
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => handleEditProduct(product)}
                      >
                        <Edit className="w-4 h-4 mr-1" />
                        Düzenle
                      </Button>
                    </>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                    onClick={() => handleDeleteProduct(product)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>

                {product.approvalStatus !== 'rejected' && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full bg-blue-50 hover:bg-blue-100 text-blue-700"
                    onClick={() => handleAddStock(product)}
                  >
                    <Archive className="w-4 h-4 mr-1" />
                    Stok Ekle
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredProducts.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Ürün bulunamadı
            </h3>
            <p className="text-gray-600 mb-4">
              Arama kriterlerinize uygun ürün bulunamadı.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm('')
                setSelectedCategory('all')
              }}
            >
              Filtreleri Temizle
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-amber-600">
              {mockProducts.length}
            </div>
            <div className="text-sm text-gray-600">Toplam Ürün</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {mockProducts.filter(p => p.status === 'active').length}
            </div>
            <div className="text-sm text-gray-600">Aktif Ürün</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {mockProducts.filter(p => p.status === 'draft').length}
            </div>
            <div className="text-sm text-gray-600">Taslak Ürün</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {categories.length - 1}
            </div>
            <div className="text-sm text-gray-600">Kategori</div>
          </CardContent>
        </Card>
      </div>

      {/* Product Form Modal */}
      <ProductFormModal
        isOpen={isProductModalOpen}
        onClose={() => {
          setIsProductModalOpen(false)
          setEditingProduct(null)
        }}
        onSave={handleProductSave}
        product={editingProduct}
        mode={editingProduct ? 'edit' : 'create'}
      />

      {/* Delete Product Modal */}
      <DeleteProductModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false)
          setDeletingProduct(null)
        }}
        onConfirm={confirmDeleteProduct}
        productName={deletingProduct?.name || ''}
        isLoading={isDeleting}
      />

      {/* Add Stock Modal */}
      <AddStockModal
        isOpen={isStockModalOpen}
        onClose={() => {
          setIsStockModalOpen(false)
          setStockProduct(null)
        }}
        onSave={confirmAddStock}
        productName={stockProduct?.name || ''}
        isLoading={isAddingStock}
      />
    </div>
  )
}
