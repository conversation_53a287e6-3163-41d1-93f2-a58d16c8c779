module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/index.ts',
    '!src/tests/**',
    '!src/**/__tests__/**',
    'src/modules/ai-marketing/**/*.ts',
    '!src/modules/ai-marketing/**/*.d.ts',
    '!src/modules/ai-marketing/**/__tests__/**',
    '!src/modules/ai-marketing/demo/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],
  // setupFilesAfterEnv: [
  //   '<rootDir>/src/modules/ai-marketing/__tests__/setup.ts'
  // ],

  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 75,
      lines: 80,
      statements: 80
    },
    './src/modules/ai-marketing/': {
      branches: 75,
      functions: 80,
      lines: 85,
      statements: 85
    }
  },
  testTimeout: 30000,
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  }
};
