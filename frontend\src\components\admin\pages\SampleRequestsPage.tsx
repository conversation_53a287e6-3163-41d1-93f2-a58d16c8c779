'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BeakerIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  ChartBarIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { useSample } from '@/contexts/sample-context';

interface AdminSampleRequestsPageProps {
  onNavigate?: (route: string) => void;
}

const AdminSampleRequestsPage: React.FC<AdminSampleRequestsPageProps> = ({ onNavigate }) => {
  const { getAllSampleRequests, isLoading } = useSample();
  const [sampleData, setSampleData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<'pending' | 'all' | 'approved' | 'rejected'>('pending');
  const [selectedSample, setSelectedSample] = useState<any>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  useEffect(() => {
    loadSampleRequests();
  }, [activeTab]);

  const loadSampleRequests = async () => {
    try {
      const data = await getAllSampleRequests({
        status: activeTab === 'all' ? undefined : activeTab,
        page: 1,
        limit: 50
      });
      setSampleData(data);
    } catch (error) {
      console.error('Error loading sample requests:', error);
    }
  };

  const handleViewDetail = (sample: any) => {
    setSelectedSample(sample);
    setShowDetailModal(true);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'approved':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Onay Bekliyor';
      case 'approved':
        return 'Onaylandı';
      case 'rejected':
        return 'Reddedildi';
      case 'preparing':
        return 'Hazırlanıyor';
      case 'shipped':
        return 'Gönderildi';
      case 'delivered':
        return 'Teslim Edildi';
      case 'evaluated':
        return 'Değerlendirildi';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'preparing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'evaluated':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const tabs = [
    { key: 'pending', label: 'Onay Bekleyen', count: sampleData?.stats?.pending || 0 },
    { key: 'all', label: 'Tümü', count: sampleData?.stats?.total || 0 },
    { key: 'approved', label: 'Onaylanan', count: sampleData?.stats?.approved || 0 },
    { key: 'rejected', label: 'Reddedilen', count: sampleData?.stats?.rejected || 0 }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Numune talepleri yükleniyor...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Numune Talep Takibi</h1>
          <p className="text-gray-600 mt-1">Müşteri ve üretici numune süreçlerini takip edin</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
            <ChartBarIcon className="h-5 w-5" />
            <span>İstatistikler</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
            <FunnelIcon className="h-5 w-5" />
            <span>Filtrele</span>
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      {sampleData?.stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-8 w-8 text-yellow-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Onay Bekleyen</p>
                <p className="text-2xl font-bold text-gray-900">{sampleData.stats.pending}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Onaylanan</p>
                <p className="text-2xl font-bold text-gray-900">{sampleData.stats.approved}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircleIcon className="h-8 w-8 text-red-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Reddedilen</p>
                <p className="text-2xl font-bold text-gray-900">{sampleData.stats.rejected}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BeakerIcon className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Toplam</p>
                <p className="text-2xl font-bold text-gray-900">{sampleData.stats.total}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                  activeTab === tab.key
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Sample Requests Table */}
      {sampleData?.samples && sampleData.samples.length > 0 ? (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Talep ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Müşteri
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ürünler
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Durum
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tarih
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sampleData.samples.map((sample: any, index: number) => (
                  <motion.tr
                    key={sample.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        #{sample.id.slice(-6)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">Müşteri #{sample.customerId}</div>
                      <div className="text-sm text-gray-500">{sample.deliveryAddress.name}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">
                        {sample.requestedProducts.length} ürün
                      </div>
                      <div className="text-sm text-gray-500">
                        {sample.requestedProducts.slice(0, 2).map((p: any) => p.productName).join(', ')}
                        {sample.requestedProducts.length > 2 && '...'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(sample.status)}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(sample.status)}`}>
                          {getStatusLabel(sample.status)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(sample.createdAt).toLocaleDateString('tr-TR')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => handleViewDetail(sample)}
                        className="text-blue-600 hover:text-blue-900 flex items-center space-x-1"
                      >
                        <EyeIcon className="h-4 w-4" />
                        <span>Detay</span>
                      </button>
                      <span className="text-gray-500 text-xs ml-2">
                        {sample.status === 'pending' ? 'Üretici onayı bekleniyor' : 'Süreç devam ediyor'}
                      </span>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <BeakerIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {activeTab === 'pending' ? 'Üretici onayı bekleyen numune talebi bulunmuyor' : 'Numune talebi bulunmuyor'}
          </h3>
          <p className="text-gray-600">
            Müşteriler teklif aldıktan sonra numune talep edebilir. Üreticiler direkt onay/red verir.
          </p>
        </div>
      )}

      {/* Detail Modal */}
      {showDetailModal && selectedSample && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Numune Talebi #{selectedSample.id.slice(-6)}
                </h3>
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircleIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Müşteri</label>
                    <p className="text-sm text-gray-900">Müşteri #{selectedSample.customerId}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Üretici</label>
                    <p className="text-sm text-gray-900">Üretici #{selectedSample.producerId}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Durum</label>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedSample.status)}`}>
                      {getStatusLabel(selectedSample.status)}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Talep Tarihi</label>
                    <p className="text-sm text-gray-900">{new Date(selectedSample.createdAt).toLocaleDateString('tr-TR')}</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Talep Edilen Ürünler</label>
                  <div className="space-y-2">
                    {selectedSample.requestedProducts.map((product: any, idx: number) => (
                      <div key={idx} className="bg-gray-50 rounded-lg p-3">
                        <p className="font-medium text-gray-900">{product.productName}</p>
                        <p className="text-sm text-gray-600">{product.specifications} - {product.sampleSize}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Teslimat Adresi</label>
                  <div className="bg-blue-50 rounded-lg p-3">
                    <p className="text-sm text-blue-900">{selectedSample.deliveryAddress.name}</p>
                    <p className="text-sm text-blue-800">{selectedSample.deliveryAddress.address}</p>
                    <p className="text-sm text-blue-800">{selectedSample.deliveryAddress.city}, {selectedSample.deliveryAddress.country}</p>
                    <p className="text-sm text-blue-800">{selectedSample.deliveryAddress.phone}</p>
                  </div>
                </div>

                {selectedSample.sampleSpecifications && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Özel Gereksinimler</label>
                    <p className="text-sm text-gray-900 bg-gray-50 rounded-lg p-3">{selectedSample.sampleSpecifications}</p>
                  </div>
                )}

                <div className="bg-yellow-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-yellow-900 mb-2">Bilgi</h4>
                  <p className="text-sm text-yellow-800">
                    Bu numune talebi üretici tarafından onaylanacak/reddedilecektir. Admin sadece süreci takip eder.
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  Kapat
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminSampleRequestsPage;
