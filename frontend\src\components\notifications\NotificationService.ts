// Notification Service - Bildirim yönetimi için utility sınıfı

export interface Notification {
  id: number;
  type: 'payment_reminder' | 'delivery_reminder' | 'payment_overdue' | 'delivery_status' | 'expense_reminder' | 'check_due' | 'general';
  title: string;
  message: string;
  date: string;
  read: boolean;
  priority: 'high' | 'medium' | 'low';
  relatedId?: string;
  relatedType?: 'sale' | 'expense' | 'check';
  actionUrl?: string;
}

export interface NotificationSettings {
  systemNotifications: boolean;
  emailNotifications: boolean;
  salesPaymentReminders: boolean;
  salesDeliveryReminders: boolean;
  salesStatusUpdates: boolean;
  salesOverduePayments: boolean;
  expenseReminders: boolean;
  expenseRecurringReminders: boolean;
  checkDueReminders: boolean;
  checkAdvanceReminders: boolean;
  checkAdvanceDays: number;
  quietHoursEnabled: boolean;
  quietHoursStart: string;
  quietHoursEnd: string;
  weekendNotifications: boolean;
  emailAddress: string;
}

class NotificationService {
  private notifications: Notification[] = [];
  private settings: NotificationSettings;

  constructor() {
    this.settings = this.getDefaultSettings();
    this.loadNotifications();
  }

  private getDefaultSettings(): NotificationSettings {
    return {
      systemNotifications: true,
      emailNotifications: true,
      salesPaymentReminders: true,
      salesDeliveryReminders: true,
      salesStatusUpdates: true,
      salesOverduePayments: true,
      expenseReminders: true,
      expenseRecurringReminders: true,
      checkDueReminders: true,
      checkAdvanceReminders: true,
      checkAdvanceDays: 3,
      quietHoursEnabled: true,
      quietHoursStart: '22:00',
      quietHoursEnd: '08:00',
      weekendNotifications: false,
      emailAddress: ''
    };
  }

  private loadNotifications() {
    // Mock data - gerçek uygulamada localStorage veya API'den gelecek
    this.notifications = [
      {
        id: 1,
        type: 'check_due',
        title: 'Çek Vade Hatırlatması',
        message: 'ÇEK-001 numaralı çekin vadesi bugün (15.02.2024). Tutar: $800',
        date: new Date().toISOString(),
        read: false,
        priority: 'high',
        relatedId: 'sale-2',
        relatedType: 'sale',
        actionUrl: '/customer/analytics/sales/view/2'
      },
      {
        id: 2,
        type: 'delivery_reminder',
        title: 'Teslimat Hatırlatması',
        message: 'Beyaz Mermer ürününün ABC İnşaat Ltd. firmasına teslimatı bugün planlandı.',
        date: new Date(Date.now() - 60000).toISOString(),
        read: false,
        priority: 'medium',
        relatedId: 'sale-1',
        relatedType: 'sale',
        actionUrl: '/customer/analytics/sales/view/1'
      },
      {
        id: 3,
        type: 'expense_reminder',
        title: 'Düzenli Gider Hatırlatması',
        message: 'Aylık depo kirası ödemesi yarın (16.02.2024) vadesi geliyor. Tutar: $800',
        date: new Date(Date.now() - 120000).toISOString(),
        read: false,
        priority: 'medium',
        relatedId: 'expense-3',
        relatedType: 'expense',
        actionUrl: '/customer/analytics/expenses/view/3'
      }
    ];
  }

  // Bildirim oluşturma
  createNotification(notification: Omit<Notification, 'id' | 'date' | 'read'>): Notification {
    const newNotification: Notification = {
      ...notification,
      id: Date.now(),
      date: new Date().toISOString(),
      read: false
    };

    this.notifications.unshift(newNotification);
    this.saveNotifications();
    return newNotification;
  }

  // Satış için bildirim oluşturma
  createSaleNotification(type: 'payment_reminder' | 'delivery_reminder' | 'payment_overdue', saleData: any) {
    let title = '';
    let message = '';
    let priority: 'high' | 'medium' | 'low' = 'medium';

    switch (type) {
      case 'payment_reminder':
        title = 'Ödeme Hatırlatması';
        message = `${saleData.orderNumber} numaralı siparişin ödemesi yakında vadesi geliyor.`;
        priority = 'medium';
        break;
      case 'delivery_reminder':
        title = 'Teslimat Hatırlatması';
        message = `${saleData.productName} ürününün ${saleData.companyName} firmasına teslimatı bugün planlandı.`;
        priority = 'medium';
        break;
      case 'payment_overdue':
        title = 'Geciken Ödeme';
        message = `${saleData.orderNumber} numaralı siparişin ödemesi gecikmiş durumda.`;
        priority = 'high';
        break;
    }

    return this.createNotification({
      type,
      title,
      message,
      priority,
      relatedId: saleData.id,
      relatedType: 'sale',
      actionUrl: `/customer/analytics/sales/view/${saleData.id}`
    });
  }

  // Çek için bildirim oluşturma
  createCheckNotification(checkData: any, saleData: any) {
    const title = 'Çek Vade Hatırlatması';
    const message = `${checkData.checkNumber} numaralı çekin vadesi bugün (${new Date(checkData.dueDate).toLocaleDateString('tr-TR')}). Tutar: $${checkData.amount}`;
    
    return this.createNotification({
      type: 'check_due',
      title,
      message,
      priority: 'high',
      relatedId: saleData.id,
      relatedType: 'sale',
      actionUrl: `/customer/analytics/sales/view/${saleData.id}`
    });
  }

  // Gider için bildirim oluşturma
  createExpenseNotification(expenseData: any) {
    const title = expenseData.isRecurring ? 'Düzenli Gider Hatırlatması' : 'Gider Hatırlatması';
    const message = `${expenseData.title} ödemesi yakında vadesi geliyor. Tutar: $${expenseData.amount}`;
    
    return this.createNotification({
      type: 'expense_reminder',
      title,
      message,
      priority: 'medium',
      relatedId: expenseData.id,
      relatedType: 'expense',
      actionUrl: `/customer/analytics/expenses/view/${expenseData.id}`
    });
  }

  // Tüm bildirimleri getir
  getNotifications(): Notification[] {
    return this.notifications;
  }

  // Okunmamış bildirimleri getir
  getUnreadNotifications(): Notification[] {
    return this.notifications.filter(n => !n.read);
  }

  // Okunmamış bildirim sayısı
  getUnreadCount(): number {
    return this.getUnreadNotifications().length;
  }

  // Bildirimi okundu işaretle
  markAsRead(id: number): void {
    const notification = this.notifications.find(n => n.id === id);
    if (notification) {
      notification.read = true;
      this.saveNotifications();
    }
  }

  // Tüm bildirimleri okundu işaretle
  markAllAsRead(): void {
    this.notifications.forEach(n => n.read = true);
    this.saveNotifications();
  }

  // Bildirimi sil
  deleteNotification(id: number): void {
    this.notifications = this.notifications.filter(n => n.id !== id);
    this.saveNotifications();
  }

  // Ayarları getir
  getSettings(): NotificationSettings {
    return this.settings;
  }

  // Ayarları güncelle
  updateSettings(newSettings: Partial<NotificationSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
  }

  // Bildirim gönderilip gönderilmeyeceğini kontrol et
  shouldSendNotification(type: string): boolean {
    const now = new Date();
    
    // Sessiz saatler kontrolü
    if (this.settings.quietHoursEnabled) {
      const currentTime = now.toTimeString().slice(0, 5);
      if (currentTime >= this.settings.quietHoursStart || currentTime <= this.settings.quietHoursEnd) {
        return false;
      }
    }

    // Hafta sonu kontrolü
    if (!this.settings.weekendNotifications) {
      const dayOfWeek = now.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) { // Pazar veya Cumartesi
        return false;
      }
    }

    // Tip bazlı kontrol
    switch (type) {
      case 'payment_reminder':
        return this.settings.salesPaymentReminders;
      case 'delivery_reminder':
        return this.settings.salesDeliveryReminders;
      case 'check_due':
        return this.settings.checkDueReminders;
      case 'expense_reminder':
        return this.settings.expenseReminders;
      default:
        return this.settings.systemNotifications;
    }
  }

  private saveNotifications(): void {
    // Gerçek uygulamada localStorage veya API'ye kaydet
    if (typeof window !== 'undefined') {
      localStorage.setItem('notifications', JSON.stringify(this.notifications));
    }
  }

  private saveSettings(): void {
    // Gerçek uygulamada localStorage veya API'ye kaydet
    if (typeof window !== 'undefined') {
      localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
    }
  }
}

// Singleton instance
export const notificationService = new NotificationService();
export default NotificationService;
