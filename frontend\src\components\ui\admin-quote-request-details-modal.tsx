"use client"

import * as React from "react"
import { But<PERSON> } from "./button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "./card"
import { Badge } from "./badge"
import { 
  X, 
  User, 
  Package, 
  Calendar, 
  DollarSign, 
  MapPin,
  FileText,
  Clock,
  Building2,
  Phone,
  Mail,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Target,
  Eye,
  ExternalLink
} from "lucide-react"

interface AdminQuoteRequestDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  request: any
}

export function AdminQuoteRequestDetailsModal({ 
  isOpen, 
  onClose, 
  request 
}: AdminQuoteRequestDetailsModalProps) {
  if (!isOpen || !request) return null

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      case 'expired':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktif'
      case 'pending':
        return 'Beklemede'
      case 'completed':
        return 'Tamamlandı'
      case 'expired':
        return 'Süresi Doldu'
      default:
        return status
    }
  }

  const getQuoteStatusColor = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      case 'submitted':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getQuoteStatusText = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'Kabul Edildi'
      case 'rejected':
        return 'Reddedildi'
      case 'submitted':
        return 'Gönderildi'
      default:
        return status
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      {/* Full Screen Modal */}
      <div className="bg-white rounded-lg shadow-xl w-full h-full max-w-7xl max-h-[95vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Teklif Talebi Detayları</h2>
              <p className="text-gray-600">
                {request.id} - {request.customerName}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Badge className={getStatusColor(request.status)}>
              {getStatusText(request.status)}
            </Badge>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-6">
            {/* Customer & Request Overview */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Customer Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 h-5" />
                    Müşteri Bilgileri
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Şirket Adı</label>
                        <p className="text-base font-medium">{request.customerName}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">İletişim Kişisi</label>
                        <p className="text-base">{request.customerContact}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">E-posta</label>
                        <p className="text-base">{request.customerEmail}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Telefon</label>
                        <p className="text-base">{request.customerPhone}</p>
                      </div>
                    </div>
                    {request.message && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Müşteri Mesajı</label>
                        <p className="text-base bg-blue-50 p-3 rounded">{request.message}</p>
                      </div>
                    )}
                    <div className="pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => window.open(`/admin/customers/${request.customerId || '1'}`, '_blank')}
                      >
                        <Building2 className="w-4 h-4 mr-2" />
                        Müşteri Profilini Görüntüle
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Request Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="w-5 h-5" />
                    Talep Bilgileri
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Talep Tarihi</label>
                        <p className="text-base">{new Date(request.requestDate).toLocaleDateString('tr-TR')}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Son Tarih</label>
                        <p className="text-base">{new Date(request.deadline).toLocaleDateString('tr-TR')}</p>
                      </div>
                    </div>
                    {request.notes && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Notlar</label>
                        <p className="text-base bg-yellow-50 p-2 rounded">{request.notes}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Product Details - Improved for Multiple Products/Specifications */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  Talep Edilen Ürünler ve Ebatlar
                </CardTitle>
              </CardHeader>
              <CardContent>
                {request.products ? request.products.map((product: any, productIndex: number) => (
                  <div key={productIndex} className="mb-8 last:mb-0">
                    {/* Product Header */}
                    <div className="flex items-center gap-4 mb-4 p-4 bg-blue-50 rounded-lg">
                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                        {product.image ? (
                          <img
                            src={product.image}
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Package className="w-8 h-8 text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3">
                          <button
                            className="text-blue-600 hover:underline font-semibold text-lg"
                            onClick={() => window.open(`/products/${product.id}`, '_blank')}
                          >
                            {product.name}
                          </button>
                          <Badge variant="outline">{product.category}</Badge>
                        </div>
                        <p className="text-gray-600 text-sm mt-1">
                          {product.specifications?.length || 0} farklı ebat talebi
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`/products/${product.id}`, '_blank')}
                      >
                        <ExternalLink className="w-4 h-4 mr-1" />
                        Ürüne Git
                      </Button>
                    </div>

                    {/* Specifications Table for this Product */}
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse border border-gray-300">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Ebat No</th>
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Boyutlar (cm)</th>
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Yüzey İşlemi</th>
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Ambalaj</th>
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Teslimat</th>
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Miktar (m²)</th>
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Hedef Fiyat</th>
                          </tr>
                        </thead>
                        <tbody>
                          {product.specifications && product.specifications.map((spec: any, specIndex: number) => (
                            <tr key={specIndex} className="hover:bg-gray-50">
                              <td className="border border-gray-300 px-3 py-2 text-sm font-medium">
                                #{specIndex + 1}
                              </td>
                              <td className="border border-gray-300 px-3 py-2 text-sm">
                                <span className="font-medium">
                                  {spec.thickness} × {spec.width} × {spec.length}
                                </span>
                              </td>
                              <td className="border border-gray-300 px-3 py-2 text-sm">
                                <Badge variant="secondary" className="text-xs">
                                  {spec.surface}
                                </Badge>
                              </td>
                              <td className="border border-gray-300 px-3 py-2 text-sm">{spec.packaging}</td>
                              <td className="border border-gray-300 px-3 py-2 text-sm">{spec.delivery}</td>
                              <td className="border border-gray-300 px-3 py-2 text-sm font-medium">
                                {spec.area} m²
                              </td>
                              <td className="border border-gray-300 px-3 py-2 text-sm">
                                <span className="font-medium text-green-600">
                                  ${spec.targetPrice} {spec.currency}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )) : (
                  // Fallback for old data structure
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border border-gray-300 px-4 py-3 text-left font-medium">Ürün Resmi</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-medium">Ürün Adı</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-medium">Boyutlar</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-medium">Miktar</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-medium">İşlemler</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td className="border border-gray-300 px-4 py-3">
                            <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                              <Package className="w-10 h-10 text-gray-400" />
                            </div>
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <button
                              className="text-blue-600 hover:underline font-medium"
                              onClick={() => window.open(`/products/1`, '_blank')}
                            >
                              {request.productName}
                            </button>
                          </td>
                          <td className="border border-gray-300 px-4 py-3">{request.dimension}</td>
                          <td className="border border-gray-300 px-4 py-3">{request.quantity} {request.unit}</td>
                          <td className="border border-gray-300 px-4 py-3">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(`/products/1`, '_blank')}
                            >
                              <ExternalLink className="w-4 h-4 mr-1" />
                              Ürüne Git
                            </Button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quotes Table - Improved for Multiple Products/Specifications */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  Gelen Teklifler ({request.quotesReceived})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {request.quotes.length > 0 ? (
                  <div className="space-y-6">
                    {request.quotes.map((quote: any, quoteIndex: number) => (
                      <div key={quoteIndex} className={`border rounded-lg p-4 ${quote.status === 'accepted' ? 'border-green-300 bg-green-50' : 'border-gray-200'}`}>
                        {/* Producer Header */}
                        <div className="flex items-center justify-between mb-4 pb-3 border-b">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <button
                                className="text-blue-600 hover:underline font-semibold text-lg"
                                onClick={() => window.open(`/producer/${quote.id}`, '_blank')}
                              >
                                {quote.producerName}
                              </button>
                              <Badge className={getQuoteStatusColor(quote.status)}>
                                {getQuoteStatusText(quote.status)}
                              </Badge>
                            </div>
                            <div className="text-sm text-gray-600">
                              <div className="flex items-center gap-4">
                                <div className="flex items-center gap-1">
                                  <Mail className="w-3 h-3" />
                                  <a href={`mailto:${quote.producerEmail}`} className="hover:underline">
                                    {quote.producerEmail}
                                  </a>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Phone className="w-3 h-3" />
                                  <a href={`tel:${quote.producerPhone}`} className="hover:underline">
                                    {quote.producerPhone}
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="text-right text-sm text-gray-600">
                              <div>Teklif: {new Date(quote.quotedDate).toLocaleDateString('tr-TR')}</div>
                              <div>Yanıt: {quote.responseTime}</div>
                            </div>
                            <div className="flex gap-1">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => alert('Teklif detayları açılacak')}
                              >
                                <Eye className="w-4 h-4 mr-1" />
                                Detay
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => window.open(`/producer/${quote.id}`, '_blank')}
                              >
                                <Building2 className="w-4 h-4 mr-1" />
                                Üretici
                              </Button>
                            </div>
                          </div>
                        </div>

                        {/* Quote Details Table */}
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse border border-gray-300">
                            <thead>
                              <tr className="bg-gray-50">
                                <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Ürün</th>
                                <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Ebat No</th>
                                <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Boyutlar</th>
                                <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Miktar</th>
                                <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Birim Fiyat</th>
                                <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Toplam</th>
                                <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Teslimat</th>
                              </tr>
                            </thead>
                            <tbody>
                              {/* For now, showing single product/spec - in real app this would be dynamic */}
                              {request.products ? request.products.map((product: any, productIndex: number) =>
                                product.specifications?.map((spec: any, specIndex: number) => (
                                  <tr key={`${productIndex}-${specIndex}`} className="hover:bg-gray-50">
                                    <td className="border border-gray-300 px-3 py-2 text-sm">
                                      <button
                                        className="text-blue-600 hover:underline font-medium"
                                        onClick={() => window.open(`/products/${product.id}`, '_blank')}
                                      >
                                        {product.name}
                                      </button>
                                    </td>
                                    <td className="border border-gray-300 px-3 py-2 text-sm font-medium">
                                      #{specIndex + 1}
                                    </td>
                                    <td className="border border-gray-300 px-3 py-2 text-sm">
                                      {spec.thickness}×{spec.width}×{spec.length}cm
                                    </td>
                                    <td className="border border-gray-300 px-3 py-2 text-sm">
                                      {spec.area} m²
                                    </td>
                                    <td className="border border-gray-300 px-3 py-2 text-sm font-semibold">
                                      ${quote.price} {quote.currency}
                                    </td>
                                    <td className="border border-gray-300 px-3 py-2 text-sm font-semibold text-green-600">
                                      ${(parseFloat(quote.price) * parseFloat(spec.area)).toFixed(2)} {quote.currency}
                                    </td>
                                    <td className="border border-gray-300 px-3 py-2 text-sm">
                                      {quote.deliveryTime}
                                    </td>
                                  </tr>
                                ))
                              ) : (
                                <tr className="hover:bg-gray-50">
                                  <td className="border border-gray-300 px-3 py-2 text-sm">
                                    <button
                                      className="text-blue-600 hover:underline font-medium"
                                      onClick={() => window.open(`/products/1`, '_blank')}
                                    >
                                      {request.productName}
                                    </button>
                                  </td>
                                  <td className="border border-gray-300 px-3 py-2 text-sm font-medium">#1</td>
                                  <td className="border border-gray-300 px-3 py-2 text-sm">{request.dimension}</td>
                                  <td className="border border-gray-300 px-3 py-2 text-sm">{request.quantity} {request.unit}</td>
                                  <td className="border border-gray-300 px-3 py-2 text-sm font-semibold">
                                    ${quote.price} {quote.currency}
                                  </td>
                                  <td className="border border-gray-300 px-3 py-2 text-sm font-semibold text-green-600">
                                    ${(parseFloat(quote.price) * parseFloat(request.quantity)).toFixed(2)} {quote.currency}
                                  </td>
                                  <td className="border border-gray-300 px-3 py-2 text-sm">{quote.deliveryTime}</td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        </div>

                        {/* Quote Notes */}
                        {quote.notes && (
                          <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                            <p className="text-sm font-medium text-blue-800 mb-1">Üretici Notları:</p>
                            <p className="text-sm text-blue-700">{quote.notes}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <AlertTriangle className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                    <p className="text-lg font-medium">Henüz teklif gelmedi</p>
                    <p className="text-sm">Bu talep için henüz üretici teklifi bulunmuyor.</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Price Summary */}
            {request.quotes.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-green-50">
                  <CardContent className="p-4 text-center">
                    <p className="text-sm text-gray-600">En İyi Fiyat</p>
                    <p className="text-2xl font-bold text-green-600">${request.bestPrice}</p>
                  </CardContent>
                </Card>
                <Card className="bg-blue-50">
                  <CardContent className="p-4 text-center">
                    <p className="text-sm text-gray-600">Ortalama Fiyat</p>
                    <p className="text-2xl font-bold text-blue-600">${request.avgPrice}</p>
                  </CardContent>
                </Card>
                <Card className="bg-purple-50">
                  <CardContent className="p-4 text-center">
                    <p className="text-sm text-gray-600">Toplam Teklif</p>
                    <p className="text-2xl font-bold text-purple-600">{request.quotesReceived}</p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Accepted Quote Details */}
            {request.status === 'completed' && request.selectedQuote && (
              <Card className="border-green-200 bg-green-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-800">
                    <CheckCircle className="w-5 h-5" />
                    Kabul Edilen Teklif
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-white p-4 rounded-lg">
                    <p className="font-semibold text-lg">{request.selectedQuote}</p>
                    <p className="text-green-600 font-medium">${request.bestPrice} {request.quotes[0]?.currency}</p>
                    <p className="text-sm text-gray-600 mt-2">
                      Bu teklif müşteri tarafından kabul edilmiş ve sipariş haline getirilmiştir.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-6 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              Son güncelleme: {new Date().toLocaleDateString('tr-TR')} {new Date().toLocaleTimeString('tr-TR')}
            </div>
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose}>
                Kapat
              </Button>
              <Button
                onClick={() => {
                  // Generate and download report
                  const reportData = {
                    requestId: request.id,
                    customer: request.customerName,
                    products: request.products || [{ name: request.productName }],
                    quotes: request.quotes,
                    timestamp: new Date().toISOString()
                  };
                  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `teklif-raporu-${request.id}-${new Date().toISOString().split('T')[0]}.json`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                }}
              >
                <FileText className="w-4 h-4 mr-2" />
                Rapor İndir
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  const subject = `Teklif Talebi: ${request.id} - ${request.customerName}`;
                  const body = `Merhaba,\n\n${request.id} numaralı teklif talebi hakkında bilgi almak istiyorum.\n\nMüşteri: ${request.customerName}\nÜrün: ${request.productName || request.products?.[0]?.name}\nTalep Tarihi: ${new Date(request.requestDate).toLocaleDateString('tr-TR')}\n\nTeşekkürler.`;
                  window.open(`mailto:${request.customerEmail}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
                }}
              >
                <Mail className="w-4 h-4 mr-2" />
                Müşteri ile İletişim
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
