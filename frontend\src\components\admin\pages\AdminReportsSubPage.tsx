'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  DollarSignIcon,
  TrendingUpIcon,
  UsersIcon,
  ActivityIcon,
  DownloadIcon,
  RefreshCcwIcon,
  BarChart3Icon
} from 'lucide-react';

interface AdminReportsSubPageProps {
  type: 'financial' | 'business' | 'users' | 'system';
  title: string;
  description: string;
}

const AdminReportsSubPage: React.FC<AdminReportsSubPageProps> = ({
  type,
  title,
  description
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [reportData, setReportData] = useState<any>(null);

  useEffect(() => {
    loadReportData();
  }, [type]);

  const loadReportData = async () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setReportData(getMockReportData(type));
      setIsLoading(false);
    }, 1000);
  };

  const getMockReportData = (reportType: string) => {
    switch (reportType) {
      case 'financial':
        return {
          totalRevenue: 125000,
          totalExpenses: 85000,
          netProfit: 40000,
          profitMargin: 32,
          monthlyData: [
            { month: 'Ocak', revenue: 20000, expenses: 15000 },
            { month: 'Şubat', revenue: 25000, expenses: 18000 },
            { month: 'Mart', revenue: 30000, expenses: 20000 },
            { month: 'Nisan', revenue: 28000, expenses: 19000 },
            { month: 'Mayıs', revenue: 22000, expenses: 13000 }
          ]
        };
      case 'business':
        return {
          totalOrders: 1250,
          completedOrders: 1100,
          cancelledOrders: 150,
          averageOrderValue: 850,
          topProducts: [
            { name: 'Travertine', orders: 450, revenue: 380000 },
            { name: 'Marble', orders: 320, revenue: 270000 },
            { name: 'Granite', orders: 280, revenue: 240000 }
          ]
        };
      case 'users':
        return {
          totalCustomers: 450,
          activeCustomers: 320,
          totalProducers: 85,
          activeProducers: 72,
          newRegistrations: 25,
          userActivity: [
            { type: 'Müşteri Kayıt', count: 15 },
            { type: 'Üretici Kayıt', count: 8 },
            { type: 'Teklif Talebi', count: 125 },
            { type: 'Sipariş', count: 85 }
          ]
        };
      case 'system':
        return {
          totalRequests: 15420,
          avgResponseTime: 245,
          uptime: 99.8,
          errorRate: 0.2,
          systemMetrics: [
            { metric: 'API Çağrıları', value: 15420, change: 12 },
            { metric: 'Veritabanı Sorguları', value: 45230, change: 8 },
            { metric: 'Aktif Oturumlar', value: 125, change: -5 },
            { metric: 'Disk Kullanımı', value: 68, change: 3 }
          ]
        };
      default:
        return {};
    }
  };

  const getTypeIcon = () => {
    switch (type) {
      case 'financial':
        return <DollarSignIcon className="h-6 w-6 text-green-600" />;
      case 'business':
        return <TrendingUpIcon className="h-6 w-6 text-blue-600" />;
      case 'users':
        return <UsersIcon className="h-6 w-6 text-purple-600" />;
      case 'system':
        return <ActivityIcon className="h-6 w-6 text-orange-600" />;
      default:
        return <BarChart3Icon className="h-6 w-6 text-gray-600" />;
    }
  };

  const handleExport = () => {
    // Export functionality
    alert(`${title} raporu dışa aktarılıyor...`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Rapor verileri yükleniyor...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getTypeIcon()}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
            <p className="text-gray-600 mt-1">{description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={loadReportData}
            className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            <RefreshCcwIcon className="h-4 w-4" />
            <span>Yenile</span>
          </button>
          <button
            onClick={handleExport}
            className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
          >
            <DownloadIcon className="h-4 w-4" />
            <span>Dışa Aktar</span>
          </button>
        </div>
      </div>

      {/* Type-specific Content */}
      {type === 'financial' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Toplam Gelir</h4>
              <p className="text-2xl font-bold text-green-600">₺{reportData.totalRevenue.toLocaleString()}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Toplam Gider</h4>
              <p className="text-2xl font-bold text-red-600">₺{reportData.totalExpenses.toLocaleString()}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Net Kar</h4>
              <p className="text-2xl font-bold text-blue-600">₺{reportData.netProfit.toLocaleString()}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Kar Marjı</h4>
              <p className="text-2xl font-bold text-purple-600">{reportData.profitMargin}%</p>
            </div>
          </div>
        </div>
      )}

      {type === 'business' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Toplam Sipariş</h4>
              <p className="text-2xl font-bold text-blue-600">{reportData.totalOrders}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Tamamlanan</h4>
              <p className="text-2xl font-bold text-green-600">{reportData.completedOrders}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">İptal Edilen</h4>
              <p className="text-2xl font-bold text-red-600">{reportData.cancelledOrders}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Ortalama Sipariş</h4>
              <p className="text-2xl font-bold text-purple-600">₺{reportData.averageOrderValue}</p>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">En Çok Satan Ürünler</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Ürün</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Sipariş Sayısı</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Toplam Gelir</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {reportData.topProducts?.map((product: any, index: number) => (
                    <tr key={index}>
                      <td className="px-6 py-4 text-sm font-medium text-gray-900">{product.name}</td>
                      <td className="px-6 py-4 text-sm text-gray-500">{product.orders}</td>
                      <td className="px-6 py-4 text-sm text-gray-500">₺{product.revenue.toLocaleString()}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {type === 'users' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Toplam Müşteri</h4>
              <p className="text-2xl font-bold text-blue-600">{reportData.totalCustomers}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Aktif Müşteri</h4>
              <p className="text-2xl font-bold text-green-600">{reportData.activeCustomers}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Toplam Üretici</h4>
              <p className="text-2xl font-bold text-purple-600">{reportData.totalProducers}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Aktif Üretici</h4>
              <p className="text-2xl font-bold text-orange-600">{reportData.activeProducers}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Yeni Kayıt</h4>
              <p className="text-2xl font-bold text-indigo-600">{reportData.newRegistrations}</p>
            </div>
          </div>
        </div>
      )}

      {type === 'system' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Toplam İstek</h4>
              <p className="text-2xl font-bold text-blue-600">{reportData.totalRequests}</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Ortalama Yanıt</h4>
              <p className="text-2xl font-bold text-green-600">{reportData.avgResponseTime}ms</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Çalışma Oranı</h4>
              <p className="text-2xl font-bold text-purple-600">{reportData.uptime}%</p>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Hata Oranı</h4>
              <p className="text-2xl font-bold text-red-600">{reportData.errorRate}%</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminReportsSubPage;
