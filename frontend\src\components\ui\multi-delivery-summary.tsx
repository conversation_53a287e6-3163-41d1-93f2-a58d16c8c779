'use client'

import * as React from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Package, 
  TrendingUp, 
  Calendar, 
  DollarSign,
  Truck,
  Factory,
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowRight
} from 'lucide-react'
import { MultiDeliveryOrder } from '@/types/multi-delivery'
import { getOverallProgress } from '@/data/mock-multi-delivery'

interface MultiDeliverySummaryProps {
  order: MultiDeliveryOrder
  onViewDetails: () => void
}

export function MultiDeliverySummary({
  order,
  onViewDetails
}: MultiDeliverySummaryProps) {
  const overallProgress = getOverallProgress(order)
  
  // Calculate delivery statistics
  const deliveryStats = React.useMemo(() => {
    const packages = order.deliveryPackages
    return {
      pending: packages.filter(p => p.deliveryStatus === 'pending').length,
      ready: packages.filter(p => p.deliveryStatus === 'ready').length,
      shipped: packages.filter(p => p.deliveryStatus === 'shipped').length,
      delivered: packages.filter(p => p.deliveryStatus === 'delivered').length
    }
  }, [order.deliveryPackages])

  // Calculate payment statistics
  const paymentStats = React.useMemo(() => {
    const allPayments = order.deliveryPackages.flatMap(pkg => pkg.payments)
    const totalAmount = order.totalAmount
    const paidAmount = allPayments
      .filter(p => p.status === 'paid')
      .reduce((sum, p) => sum + p.amount, 0)
    const overdueAmount = allPayments
      .filter(p => p.status === 'overdue')
      .reduce((sum, p) => sum + p.amount, 0)
    
    return {
      totalAmount,
      paidAmount,
      overdueAmount,
      paymentPercentage: Math.round((paidAmount / totalAmount) * 100),
      overduePayments: allPayments.filter(p => p.status === 'overdue').length
    }
  }, [order])

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  const getStatusColor = (status: MultiDeliveryOrder['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in_production':
        return 'bg-blue-100 text-blue-800'
      case 'partially_delivered':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: MultiDeliveryOrder['status']) => {
    switch (status) {
      case 'in_production':
        return 'Üretimde'
      case 'partially_delivered':
        return 'Kısmi Teslim'
      case 'completed':
        return 'Tamamlandı'
      default:
        return 'Bekliyor'
    }
  }

  return (
    <Card className="overflow-hidden border-purple-200 bg-purple-50">
      <CardHeader className="bg-purple-100 border-b border-purple-200">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl flex items-center gap-2">
            <Package className="w-6 h-6 text-purple-600" />
            Çoklu Teslimat Siparişi #{order.id}
          </CardTitle>
          <Badge className={getStatusColor(order.status)}>
            {getStatusText(order.status)}
          </Badge>
        </div>
        <p className="text-purple-700">
          {order.totalQuantity} m² - {order.deliveryPackages.length} paket halinde teslimat
        </p>
      </CardHeader>

      <CardContent className="p-6 space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Factory className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-gray-700">Üretim</span>
            </div>
            <p className="text-lg font-bold text-blue-900">%{overallProgress.overallPercentage}</p>
            <p className="text-xs text-gray-600">{overallProgress.completedPackages}/{overallProgress.totalPackages} paket</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Truck className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-medium text-gray-700">Teslimat</span>
            </div>
            <p className="text-lg font-bold text-purple-900">{deliveryStats.delivered}</p>
            <p className="text-xs text-gray-600">teslim edildi</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <DollarSign className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-gray-700">Ödeme</span>
            </div>
            <p className="text-lg font-bold text-green-900">%{paymentStats.paymentPercentage}</p>
            <p className="text-xs text-gray-600">${paymentStats.paidAmount.toLocaleString()}</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Calendar className="w-4 h-4 text-orange-600" />
              <span className="text-sm font-medium text-gray-700">Bitiş</span>
            </div>
            <p className="text-sm font-bold text-orange-900">{formatDate(order.estimatedCompletionDate)}</p>
            <p className="text-xs text-gray-600">tahmini</p>
          </div>
        </div>

        {/* Progress Overview */}
        <div className="space-y-3">
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Genel İlerleme</span>
              <span className="text-sm text-gray-600">
                {overallProgress.completedPackages}/{overallProgress.totalPackages} paket tamamlandı
              </span>
            </div>
            <Progress value={overallProgress.overallPercentage} className="h-2" />
          </div>

          <div className="grid grid-cols-3 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>{overallProgress.completedPackages} Tamamlandı</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-blue-600" />
              <span>{overallProgress.inProgressPackages} Üretimde</span>
            </div>
            <div className="flex items-center gap-2">
              <Package className="w-4 h-4 text-gray-600" />
              <span>{overallProgress.pendingPackages} Bekliyor</span>
            </div>
          </div>
        </div>

        {/* Alerts */}
        {paymentStats.overdueAmount > 0 && (
          <div className="bg-red-50 p-3 rounded-lg border border-red-200">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <div>
                <p className="font-medium text-red-800">Gecikmiş Ödeme Uyarısı</p>
                <p className="text-sm text-red-700">
                  ${paymentStats.overdueAmount.toLocaleString()} tutarında {paymentStats.overduePayments} ödeme gecikmiş
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex gap-3 pt-3 border-t border-purple-200">
          <Button
            onClick={onViewDetails}
            className="flex-1 bg-purple-600 hover:bg-purple-700"
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            Detaylı Görünüm
          </Button>
          
          <Button
            variant="outline"
            onClick={() => window.location.href = '/producer/orders/multi-delivery/delivery'}
            className="flex-1"
          >
            <Truck className="w-4 h-4 mr-2" />
            Teslimat Takvimi
          </Button>
          
          <Button
            variant="outline"
            onClick={() => window.location.href = '/producer/orders/multi-delivery/payments'}
            className="flex-1"
          >
            <DollarSign className="w-4 h-4 mr-2" />
            Ödeme Takibi
          </Button>
        </div>

        {/* Order Notes */}
        {order.notes && (
          <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
            <p className="text-sm text-purple-800">
              <span className="font-medium">Not:</span> {order.notes}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
