'use client'

import React from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Building2,
  MapPin,
  User,
  Calendar,
  FileText,
  CheckCircle,
  XCircle,
  Eye,
  Download,
  AlertTriangle,
  Clock,
  Filter,
  Search
} from 'lucide-react'

// Mock quarry applications data - gerçek uygulamada API'den gelecek
const mockQuarryApplications = [
  {
    id: '1',
    producerName: 'Afyon Mermer A.Ş.',
    producerId: 'producer1',
    quarryName: 'Afyon Beyaz Mermer Ocağı',
    location: 'Afyon Merkez',
    address: 'Afyon Merkez, Ocak Mevkii No:15',
    googleMapsLink: 'https://maps.google.com/?q=38.7569,30.5387',
    ownershipType: 'owner',
    submittedAt: '2025-01-10',
    status: 'pending',
    documents: [
      { name: 'Ocak İşletme Ruhsatı.pdf', type: 'license', size: '2.4 MB' },
      { name: 'Tapu Senedi.pdf', type: 'ownership', size: '1.8 MB' },
      { name: '<PERSON><PERSON><PERSON> İzin Belgesi.pdf', type: 'environmental', size: '3.2 MB' }
    ],
    notes: 'Yeni ocak başvurusu. Tüm belgeler eksiksiz.'
  },
  {
    id: '2',
    producerName: 'Denizli Traverten Ltd.',
    producerId: 'producer2',
    quarryName: 'Denizli Ana Ocak',
    location: 'Denizli Pamukkale',
    address: 'Pamukkale Mevkii, Traverten Ocağı',
    googleMapsLink: 'https://maps.google.com/?q=37.9144,29.1256',
    ownershipType: 'partner',
    submittedAt: '2025-01-08',
    status: 'approved',
    documents: [
      { name: 'Ortaklık Sözleşmesi.pdf', type: 'partnership', size: '1.5 MB' },
      { name: 'Ruhsat Belgesi.pdf', type: 'license', size: '2.1 MB' }
    ],
    notes: 'Ortaklık belgesi onaylandı.',
    approvedAt: '2025-01-09',
    approvedBy: 'Admin User'
  },
  {
    id: '3',
    producerName: 'Muğla Granit A.Ş.',
    producerId: 'producer3',
    quarryName: 'Muğla Granit Ocağı',
    location: 'Muğla Bodrum',
    address: 'Bodrum Granit Ocağı, Sanayi Bölgesi',
    googleMapsLink: 'https://maps.google.com/?q=37.0344,27.4305',
    ownershipType: 'renter',
    submittedAt: '2025-01-05',
    status: 'rejected',
    documents: [
      { name: 'Kira Sözleşmesi.pdf', type: 'rental', size: '1.2 MB' }
    ],
    notes: 'Kira sözleşmesi eksik bilgiler içeriyor.',
    rejectedAt: '2025-01-07',
    rejectedBy: 'Admin User',
    rejectionReason: 'Kira sözleşmesinde eksik bilgiler mevcut. Lütfen güncel sözleşme yükleyiniz.'
  }
]

export default function AdminQuarriesPage() {
  const [applications, setApplications] = React.useState(mockQuarryApplications)
  const [selectedStatus, setSelectedStatus] = React.useState('all')
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedApplication, setSelectedApplication] = React.useState<any>(null)
  const [showDetailsModal, setShowDetailsModal] = React.useState(false)

  const filteredApplications = applications.filter(app => {
    const matchesStatus = selectedStatus === 'all' || app.status === selectedStatus
    const matchesSearch = app.producerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.quarryName.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesStatus && matchesSearch
  })

  const handleApprove = (applicationId: string) => {
    setApplications(prev => prev.map(app => 
      app.id === applicationId 
        ? { 
            ...app, 
            status: 'approved', 
            approvedAt: new Date().toISOString().split('T')[0],
            approvedBy: 'Admin User'
          }
        : app
    ))
    console.log('Ocak başvurusu onaylandı:', applicationId)
  }

  const handleReject = (applicationId: string, reason: string) => {
    setApplications(prev => prev.map(app => 
      app.id === applicationId 
        ? { 
            ...app, 
            status: 'rejected', 
            rejectedAt: new Date().toISOString().split('T')[0],
            rejectedBy: 'Admin User',
            rejectionReason: reason
          }
        : app
    ))
    console.log('Ocak başvurusu reddedildi:', applicationId, reason)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-4 h-4" />
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'rejected':
        return <XCircle className="w-4 h-4" />
      default:
        return <AlertTriangle className="w-4 h-4" />
    }
  }

  const getOwnershipTypeText = (type: string) => {
    switch (type) {
      case 'owner':
        return 'Sahibi'
      case 'partner':
        return 'Ortak'
      case 'renter':
        return 'Kiracı'
      default:
        return type
    }
  }

  const statusCounts = {
    all: applications.length,
    pending: applications.filter(app => app.status === 'pending').length,
    approved: applications.filter(app => app.status === 'approved').length,
    rejected: applications.filter(app => app.status === 'rejected').length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Ocak Onayları</h1>
          <p className="text-gray-600 mt-1">
            Üretici ocak başvurularını inceleyin ve onaylayın
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant="secondary" className="bg-orange-100 text-orange-800 text-lg px-4 py-2">
            <Building2 className="w-4 h-4 mr-2" />
            {statusCounts.pending} Bekleyen Başvuru
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Başvuru</p>
              <p className="text-xl font-semibold">{statusCounts.all}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Bekleyen</p>
              <p className="text-xl font-semibold">{statusCounts.pending}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Onaylanan</p>
              <p className="text-xl font-semibold">{statusCounts.approved}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <XCircle className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Reddedilen</p>
              <p className="text-xl font-semibold">{statusCounts.rejected}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Filtreler</h3>
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Üretici veya ocak adı ile arayın..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="all">Tüm Durumlar ({statusCounts.all})</option>
              <option value="pending">Bekleyen ({statusCounts.pending})</option>
              <option value="approved">Onaylanan ({statusCounts.approved})</option>
              <option value="rejected">Reddedilen ({statusCounts.rejected})</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Applications List */}
      <div className="grid grid-cols-1 gap-6">
        {filteredApplications.map((application) => (
          <Card key={application.id} className="overflow-hidden">
            {/* Application Header */}
            <div className="p-4 border-b bg-gray-50">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-semibold text-lg text-gray-900">{application.quarryName}</h3>
                  <p className="text-sm text-gray-600 flex items-center gap-1 mt-1">
                    <User className="w-3 h-3" />
                    {application.producerName}
                  </p>
                  <p className="text-sm text-gray-600 flex items-center gap-1">
                    <MapPin className="w-3 h-3" />
                    {application.location}
                  </p>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <Badge className={getStatusColor(application.status)}>
                    {getStatusIcon(application.status)}
                    <span className="ml-1">
                      {application.status === 'approved' ? 'Onaylandı' :
                       application.status === 'pending' ? 'Bekliyor' :
                       application.status === 'rejected' ? 'Reddedildi' : application.status}
                    </span>
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {new Date(application.submittedAt).toLocaleDateString('tr-TR')}
                  </span>
                </div>
              </div>
            </div>

            {/* Application Details */}
            <div className="p-4">
              <div className="space-y-4">
                {/* Basic Info */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Sahiplik Durumu</label>
                    <p className="text-gray-900">{getOwnershipTypeText(application.ownershipType)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Belge Sayısı</label>
                    <p className="text-gray-900">{application.documents.length} dosya</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Başvuru Tarihi</label>
                    <p className="text-gray-900">{new Date(application.submittedAt).toLocaleDateString('tr-TR')}</p>
                  </div>
                </div>

                {/* Documents */}
                <div>
                  <label className="text-sm font-medium text-gray-600 mb-2 block">Yüklenen Belgeler</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {application.documents.map((doc, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 border border-gray-200 rounded-lg">
                        <FileText className="w-4 h-4 text-blue-600" />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">{doc.name}</p>
                          <p className="text-xs text-gray-500">{doc.size}</p>
                        </div>
                        <Button variant="outline" size="sm">
                          <Download className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Address and Maps */}
                <div>
                  <label className="text-sm font-medium text-gray-600">Adres</label>
                  <p className="text-gray-900 mb-2">{application.address}</p>
                  {application.googleMapsLink && (
                    <a
                      href={application.googleMapsLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline text-sm"
                    >
                      Google Maps'te Görüntüle
                    </a>
                  )}
                </div>

                {/* Notes */}
                {application.notes && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Notlar</label>
                    <p className="text-gray-900">{application.notes}</p>
                  </div>
                )}

                {/* Approval/Rejection Info */}
                {application.status === 'approved' && (
                  <div className="bg-green-50 p-3 rounded-lg">
                    <p className="text-sm text-green-800">
                      <CheckCircle className="w-4 h-4 inline mr-1" />
                      {application.approvedAt && new Date(application.approvedAt).toLocaleDateString('tr-TR')} tarihinde {application.approvedBy} tarafından onaylandı.
                    </p>
                  </div>
                )}

                {application.status === 'rejected' && (
                  <div className="bg-red-50 p-3 rounded-lg">
                    <p className="text-sm text-red-800 mb-2">
                      <XCircle className="w-4 h-4 inline mr-1" />
                      {application.rejectedAt && new Date(application.rejectedAt).toLocaleDateString('tr-TR')} tarihinde {application.rejectedBy} tarafından reddedildi.
                    </p>
                    {application.rejectionReason && (
                      <p className="text-sm text-red-700">
                        <strong>Ret Sebebi:</strong> {application.rejectionReason}
                      </p>
                    )}
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2 pt-2 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedApplication(application)
                      setShowDetailsModal(true)
                    }}
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    Detaylar
                  </Button>

                  {application.status === 'pending' && (
                    <>
                      <Button
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                        onClick={() => handleApprove(application.id)}
                      >
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Onayla
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:bg-red-50"
                        onClick={() => {
                          const reason = prompt('Ret sebebini giriniz:')
                          if (reason) {
                            handleReject(application.id, reason)
                          }
                        }}
                      >
                        <XCircle className="w-4 h-4 mr-1" />
                        Reddet
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </Card>
        ))}

        {filteredApplications.length === 0 && (
          <div className="text-center py-12">
            <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Başvuru bulunamadı</h3>
            <p className="text-gray-600">
              Arama kriterlerinize uygun ocak başvurusu bulunamadı.
            </p>
          </div>
        )}
      </div>

      {/* Details Modal */}
      {showDetailsModal && selectedApplication && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{selectedApplication.quarryName}</h2>
                  <p className="text-gray-600">{selectedApplication.producerName}</p>
                </div>
                <div className="flex items-center gap-3">
                  <Badge className={getStatusColor(selectedApplication.status)}>
                    {getStatusIcon(selectedApplication.status)}
                    <span className="ml-1">
                      {selectedApplication.status === 'approved' ? 'Onaylandı' :
                       selectedApplication.status === 'pending' ? 'Bekliyor' :
                       selectedApplication.status === 'rejected' ? 'Reddedildi' : selectedApplication.status}
                    </span>
                  </Badge>
                  <button
                    onClick={() => setShowDetailsModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XCircle className="w-6 h-6" />
                  </button>
                </div>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Temel Bilgiler</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Ocak Adı</label>
                    <p className="text-gray-900">{selectedApplication.quarryName}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Üretici</label>
                    <p className="text-gray-900">{selectedApplication.producerName}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Konum</label>
                    <p className="text-gray-900">{selectedApplication.location}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Sahiplik Durumu</label>
                    <p className="text-gray-900">{getOwnershipTypeText(selectedApplication.ownershipType)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Başvuru Tarihi</label>
                    <p className="text-gray-900">{new Date(selectedApplication.submittedAt).toLocaleDateString('tr-TR')}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Durum</label>
                    <Badge className={getStatusColor(selectedApplication.status)}>
                      {getStatusIcon(selectedApplication.status)}
                      <span className="ml-1">
                        {selectedApplication.status === 'approved' ? 'Onaylandı' :
                         selectedApplication.status === 'pending' ? 'Bekliyor' :
                         selectedApplication.status === 'rejected' ? 'Reddedildi' : selectedApplication.status}
                      </span>
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Address and Location */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Adres ve Konum</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Detaylı Adres</label>
                    <p className="text-gray-900">{selectedApplication.address}</p>
                  </div>

                  {selectedApplication.googleMapsLink && (
                    <div>
                      <label className="block text-sm font-medium text-gray-600 mb-2">Harita Konumu</label>
                      <div className="w-full h-64 rounded-lg overflow-hidden border border-gray-200 mb-3">
                        <iframe
                          src={`https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=${selectedApplication.location}`}
                          width="100%"
                          height="100%"
                          style={{ border: 0 }}
                          allowFullScreen
                          loading="lazy"
                          referrerPolicy="no-referrer-when-downgrade"
                          title={`${selectedApplication.quarryName} Konumu`}
                        />
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(selectedApplication.googleMapsLink, '_blank')}
                        >
                          <MapPin className="w-4 h-4 mr-2" />
                          Google Maps'te Aç
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigator.clipboard.writeText(selectedApplication.googleMapsLink)}
                        >
                          📍 Linki Kopyala
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Documents */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Yüklenen Belgeler</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {selectedApplication.documents.map((doc, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
                      <FileText className="w-8 h-8 text-blue-600" />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 truncate">{doc.name}</p>
                        <p className="text-sm text-gray-500">{doc.size}</p>
                        <p className="text-xs text-gray-400 capitalize">{doc.type}</p>
                      </div>
                      <div className="flex gap-1">
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Notes */}
              {selectedApplication.notes && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Notlar</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-gray-900">{selectedApplication.notes}</p>
                  </div>
                </div>
              )}

              {/* Approval/Rejection History */}
              {(selectedApplication.status === 'approved' || selectedApplication.status === 'rejected') && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">İşlem Geçmişi</h3>
                  <div className={`p-4 rounded-lg ${
                    selectedApplication.status === 'approved' ? 'bg-green-50' : 'bg-red-50'
                  }`}>
                    {selectedApplication.status === 'approved' && (
                      <div className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-green-900">Başvuru Onaylandı</p>
                          <p className="text-sm text-green-700">
                            {selectedApplication.approvedAt && new Date(selectedApplication.approvedAt).toLocaleDateString('tr-TR')} tarihinde {selectedApplication.approvedBy} tarafından onaylandı.
                          </p>
                        </div>
                      </div>
                    )}

                    {selectedApplication.status === 'rejected' && (
                      <div className="flex items-start gap-3">
                        <XCircle className="w-5 h-5 text-red-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-red-900">Başvuru Reddedildi</p>
                          <p className="text-sm text-red-700 mb-2">
                            {selectedApplication.rejectedAt && new Date(selectedApplication.rejectedAt).toLocaleDateString('tr-TR')} tarihinde {selectedApplication.rejectedBy} tarafından reddedildi.
                          </p>
                          {selectedApplication.rejectionReason && (
                            <div className="bg-red-100 p-3 rounded border border-red-200">
                              <p className="text-sm font-medium text-red-900">Ret Sebebi:</p>
                              <p className="text-sm text-red-800">{selectedApplication.rejectionReason}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Modal Footer */}
            <div className="p-6 border-t border-gray-200 bg-gray-50">
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => setShowDetailsModal(false)}
                >
                  Kapat
                </Button>

                {selectedApplication.status === 'pending' && (
                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      className="text-red-600 hover:bg-red-50"
                      onClick={() => {
                        const reason = prompt('Ret sebebini giriniz:')
                        if (reason) {
                          handleReject(selectedApplication.id, reason)
                          setShowDetailsModal(false)
                        }
                      }}
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Reddet
                    </Button>
                    <Button
                      className="bg-green-600 hover:bg-green-700"
                      onClick={() => {
                        handleApprove(selectedApplication.id)
                        setShowDetailsModal(false)
                      }}
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Onayla
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
