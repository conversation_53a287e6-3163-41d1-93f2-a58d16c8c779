#!/bin/bash

# Natural Stone Marketplace - Monitoring and Logging Setup
# Sets up comprehensive monitoring, logging, and alerting

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PROJECT_NAME="natural-stone-marketplace"
LOG_DIR="/var/log/$PROJECT_NAME"
MONITORING_DIR="/var/www/$PROJECT_NAME/monitoring"

echo -e "${BLUE}📊 Setting up monitoring and logging for $PROJECT_NAME${NC}"

# Create directories
sudo mkdir -p $LOG_DIR
sudo mkdir -p $MONITORING_DIR
sudo mkdir -p /etc/logrotate.d

# Set permissions
sudo chown -R deploy:deploy $LOG_DIR
sudo chmod 755 $LOG_DIR

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 1. Setup log rotation
print_status "Setting up log rotation..."
sudo tee /etc/logrotate.d/$PROJECT_NAME > /dev/null << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 deploy deploy
    postrotate
        systemctl reload $PROJECT_NAME-backend
        systemctl reload nginx
    endscript
}
EOF

# 2. Setup system monitoring script
print_status "Creating system monitoring script..."
cat > $MONITORING_DIR/system-monitor.sh << 'EOF'
#!/bin/bash

# System monitoring script
LOG_FILE="/var/log/natural-stone-marketplace/monitoring.log"
ALERT_EMAIL="<EMAIL>"
SLACK_WEBHOOK_URL=""  # Add your Slack webhook URL

# Function to log and alert
log_alert() {
    local level=$1
    local message=$2
    echo "$(date): [$level] $message" >> $LOG_FILE
    
    if [ "$level" = "CRITICAL" ] || [ "$level" = "ERROR" ]; then
        # Send email alert
        echo "$message" | mail -s "[$level] Natural Stone Marketplace Alert" $ALERT_EMAIL
        
        # Send Slack notification
        if [ ! -z "$SLACK_WEBHOOK_URL" ]; then
            curl -X POST -H 'Content-type: application/json' \
                --data "{\"text\":\"🚨 [$level] $message\"}" \
                $SLACK_WEBHOOK_URL
        fi
    fi
}

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    log_alert "CRITICAL" "Disk usage is at ${DISK_USAGE}%"
elif [ $DISK_USAGE -gt 80 ]; then
    log_alert "WARNING" "Disk usage is at ${DISK_USAGE}%"
fi

# Check memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEMORY_USAGE -gt 90 ]; then
    log_alert "CRITICAL" "Memory usage is at ${MEMORY_USAGE}%"
elif [ $MEMORY_USAGE -gt 80 ]; then
    log_alert "WARNING" "Memory usage is at ${MEMORY_USAGE}%"
fi

# Check CPU load
CPU_LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
CPU_CORES=$(nproc)
CPU_THRESHOLD=$(echo "$CPU_CORES * 2" | bc)

if (( $(echo "$CPU_LOAD > $CPU_THRESHOLD" | bc -l) )); then
    log_alert "WARNING" "High CPU load: $CPU_LOAD (cores: $CPU_CORES)"
fi

# Check service status
for service in "natural-stone-marketplace-backend" "natural-stone-marketplace-frontend" "nginx" "mysql"; do
    if ! systemctl is-active --quiet $service; then
        log_alert "CRITICAL" "Service $service is not running"
    fi
done

# Check database connectivity
if ! mysql -u production_user -p natural_stone_marketplace_prod -e "SELECT 1;" > /dev/null 2>&1; then
    log_alert "CRITICAL" "Database connection failed"
fi

# Check API health
if ! curl -f http://localhost:3000/health > /dev/null 2>&1; then
    log_alert "CRITICAL" "Backend API health check failed"
fi

# Check frontend
if ! curl -f http://localhost:3001 > /dev/null 2>&1; then
    log_alert "CRITICAL" "Frontend health check failed"
fi

# Check SSL certificate expiry
CERT_EXPIRY=$(openssl x509 -enddate -noout -in /etc/letsencrypt/live/yourdomain.com/cert.pem | cut -d= -f2)
CERT_EXPIRY_EPOCH=$(date -d "$CERT_EXPIRY" +%s)
CURRENT_EPOCH=$(date +%s)
DAYS_UNTIL_EXPIRY=$(( ($CERT_EXPIRY_EPOCH - $CURRENT_EPOCH) / 86400 ))

if [ $DAYS_UNTIL_EXPIRY -lt 7 ]; then
    log_alert "CRITICAL" "SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
elif [ $DAYS_UNTIL_EXPIRY -lt 30 ]; then
    log_alert "WARNING" "SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
fi

# Log system stats
echo "$(date): System stats - CPU: ${CPU_LOAD}, Memory: ${MEMORY_USAGE}%, Disk: ${DISK_USAGE}%" >> $LOG_FILE
EOF

chmod +x $MONITORING_DIR/system-monitor.sh

# 3. Setup application monitoring script
print_status "Creating application monitoring script..."
cat > $MONITORING_DIR/app-monitor.sh << 'EOF'
#!/bin/bash

# Application monitoring script
LOG_FILE="/var/log/natural-stone-marketplace/app-monitoring.log"
API_URL="http://localhost:3000"

# Function to check API endpoint
check_endpoint() {
    local endpoint=$1
    local expected_status=$2
    local response=$(curl -s -o /dev/null -w "%{http_code}" $API_URL$endpoint)
    
    if [ "$response" = "$expected_status" ]; then
        echo "$(date): ✅ $endpoint - Status: $response" >> $LOG_FILE
        return 0
    else
        echo "$(date): ❌ $endpoint - Expected: $expected_status, Got: $response" >> $LOG_FILE
        return 1
    fi
}

# Check critical endpoints
check_endpoint "/health" "200"
check_endpoint "/api/products" "200"
check_endpoint "/api/auth/me" "401"  # Should return 401 without auth

# Check database performance
DB_RESPONSE_TIME=$(mysql -u production_user -p natural_stone_marketplace_prod -e "SELECT BENCHMARK(1000, MD5('test'));" 2>&1 | grep "Query OK" | awk '{print $3}')
echo "$(date): Database response time: $DB_RESPONSE_TIME" >> $LOG_FILE

# Check file upload directory
UPLOAD_DIR="/var/www/natural-stone-marketplace/backend/uploads"
UPLOAD_SIZE=$(du -sh $UPLOAD_DIR | awk '{print $1}')
echo "$(date): Upload directory size: $UPLOAD_SIZE" >> $LOG_FILE

# Check error logs for recent errors
ERROR_COUNT=$(tail -n 100 /var/log/natural-stone-marketplace/error.log | grep "$(date +%Y-%m-%d)" | wc -l)
if [ $ERROR_COUNT -gt 10 ]; then
    echo "$(date): WARNING: High error count in last 100 lines: $ERROR_COUNT" >> $LOG_FILE
fi
EOF

chmod +x $MONITORING_DIR/app-monitor.sh

# 4. Setup performance monitoring
print_status "Creating performance monitoring script..."
cat > $MONITORING_DIR/performance-monitor.sh << 'EOF'
#!/bin/bash

# Performance monitoring script
LOG_FILE="/var/log/natural-stone-marketplace/performance.log"
API_URL="http://localhost:3000"

# Function to measure response time
measure_response_time() {
    local endpoint=$1
    local response_time=$(curl -o /dev/null -s -w "%{time_total}" $API_URL$endpoint)
    echo "$(date): $endpoint response time: ${response_time}s" >> $LOG_FILE
    
    # Alert if response time is too high
    if (( $(echo "$response_time > 2.0" | bc -l) )); then
        echo "$(date): WARNING: Slow response time for $endpoint: ${response_time}s" >> $LOG_FILE
    fi
}

# Measure key endpoints
measure_response_time "/api/products"
measure_response_time "/api/quotes"
measure_response_time "/api/orders"

# Database query performance
mysql -u production_user -p natural_stone_marketplace_prod -e "
SELECT 
    ROUND(AVG(query_time), 4) as avg_query_time,
    COUNT(*) as query_count
FROM mysql.slow_log 
WHERE start_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR);" >> $LOG_FILE 2>/dev/null || true

# Node.js memory usage
if pgrep -f "node.*natural-stone-marketplace" > /dev/null; then
    NODE_PID=$(pgrep -f "node.*natural-stone-marketplace")
    NODE_MEMORY=$(ps -p $NODE_PID -o rss= | awk '{print $1/1024}')
    echo "$(date): Node.js memory usage: ${NODE_MEMORY}MB" >> $LOG_FILE
fi
EOF

chmod +x $MONITORING_DIR/performance-monitor.sh

# 5. Setup cron jobs for monitoring
print_status "Setting up monitoring cron jobs..."
(crontab -l 2>/dev/null; cat << EOF
# System monitoring every 5 minutes
*/5 * * * * $MONITORING_DIR/system-monitor.sh

# Application monitoring every 2 minutes
*/2 * * * * $MONITORING_DIR/app-monitor.sh

# Performance monitoring every 10 minutes
*/10 * * * * $MONITORING_DIR/performance-monitor.sh

# Daily log cleanup
0 1 * * * find $LOG_DIR -name "*.log" -size +100M -exec truncate -s 50M {} \;
EOF
) | crontab -

# 6. Setup Prometheus metrics (optional)
print_status "Setting up Prometheus metrics endpoint..."
cat > $MONITORING_DIR/metrics.js << 'EOF'
// Prometheus metrics for Natural Stone Marketplace
const express = require('express');
const promClient = require('prom-client');

const app = express();
const register = new promClient.Registry();

// Default metrics
promClient.collectDefaultMetrics({ register });

// Custom metrics
const httpRequestDuration = new promClient.Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code'],
    buckets: [0.1, 0.5, 1, 2, 5]
});

const activeUsers = new promClient.Gauge({
    name: 'active_users_total',
    help: 'Number of active users'
});

const databaseConnections = new promClient.Gauge({
    name: 'database_connections_active',
    help: 'Number of active database connections'
});

register.registerMetric(httpRequestDuration);
register.registerMetric(activeUsers);
register.registerMetric(databaseConnections);

app.get('/metrics', async (req, res) => {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
});

app.listen(9090, () => {
    console.log('Metrics server listening on port 9090');
});
EOF

# 7. Create monitoring dashboard
print_status "Creating monitoring dashboard..."
cat > $MONITORING_DIR/dashboard.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Natural Stone Marketplace - Monitoring Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .metric { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .status-ok { border-left: 5px solid #4CAF50; }
        .status-warning { border-left: 5px solid #FF9800; }
        .status-error { border-left: 5px solid #F44336; }
        .refresh-btn { background: #2196F3; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🚀 Natural Stone Marketplace - System Status</h1>
    <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
    
    <div id="metrics">
        <div class="metric status-ok">
            <h3>✅ System Status</h3>
            <p>All systems operational</p>
            <small>Last updated: <span id="timestamp"></span></small>
        </div>
    </div>

    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        
        // Auto-refresh every 30 seconds
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
EOF

# 8. Setup log aggregation
print_status "Setting up log aggregation..."
cat > $MONITORING_DIR/log-aggregator.sh << 'EOF'
#!/bin/bash

# Log aggregation script
AGGREGATE_LOG="/var/log/natural-stone-marketplace/aggregate.log"
DATE=$(date +%Y-%m-%d)

# Aggregate logs from different sources
{
    echo "=== System Logs ==="
    tail -n 50 /var/log/syslog | grep natural-stone-marketplace
    
    echo "=== Application Logs ==="
    tail -n 50 /var/log/natural-stone-marketplace/app.log
    
    echo "=== Error Logs ==="
    tail -n 50 /var/log/natural-stone-marketplace/error.log
    
    echo "=== Nginx Logs ==="
    tail -n 50 /var/log/nginx/natural-stone-marketplace.access.log
    
    echo "=== Database Logs ==="
    tail -n 50 /var/log/mysql/error.log
    
} > $AGGREGATE_LOG

# Compress old aggregate logs
find /var/log/natural-stone-marketplace -name "aggregate-*.log" -mtime +7 -exec gzip {} \;
EOF

chmod +x $MONITORING_DIR/log-aggregator.sh

print_status "✅ Monitoring and logging setup completed!"

echo -e "${GREEN}"
echo "=================================================="
echo "📊 MONITORING SETUP COMPLETED!"
echo "=================================================="
echo "Log directory: $LOG_DIR"
echo "Monitoring scripts: $MONITORING_DIR"
echo "Dashboard: $MONITORING_DIR/dashboard.html"
echo "Metrics endpoint: http://localhost:9090/metrics"
echo "=================================================="
echo -e "${NC}"
