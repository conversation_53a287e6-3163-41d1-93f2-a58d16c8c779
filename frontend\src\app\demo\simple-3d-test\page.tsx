'use client';

import React from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import * as THREE from 'three';

function TestCube() {
  return (
    <mesh>
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial color="orange" />
    </mesh>
  );
}

export default function Simple3DTest() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          3D Test Sayfası
        </h1>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Three.js Test
          </h2>
          
          <div className="w-full h-96 bg-gray-100 rounded-lg overflow-hidden">
            <Canvas camera={{ position: [3, 3, 3], fov: 50 }}>
              <ambientLight intensity={0.5} />
              <directionalLight position={[10, 10, 5]} intensity={1} />
              
              <TestCube />
              
              <OrbitControls enablePan enableZoom enableRotate />
              <Environment preset="sunset" />
            </Canvas>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            <p>✅ Three.js versiyonu: {THREE.REVISION}</p>
            <p>✅ Canvas yüklendi</p>
            <p>✅ OrbitControls aktif</p>
            <p>✅ Environment preset çalışıyor</p>
          </div>
        </div>
      </div>
    </div>
  );
}
