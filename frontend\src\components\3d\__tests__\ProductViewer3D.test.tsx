/**
 * ProductViewer3D Component Tests
 * Test suite for the 3D product viewer component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ProductViewer3D } from '../ProductViewer3D';
import { 
  Asset3D, 
  AssetType, 
  AssetFormat, 
  AssetQuality, 
  ViewerConfiguration,
  MaterialDefinition 
} from '../../../types/3d';

// Mock Three.js and React Three Fiber
jest.mock('@react-three/fiber', () => ({
  Canvas: ({ children, ...props }: any) => (
    <div data-testid="canvas" {...props}>
      {children}
    </div>
  ),
  useFrame: jest.fn(),
  useThree: () => ({
    gl: {
      info: {
        memory: { geometries: 1, textures: 1 },
        render: { calls: 1, triangles: 1000 }
      },
      capabilities: {
        isWebGL2: true,
        maxTextureSize: 4096
      }
    },
    scene: { children: [] }
  })
}));

jest.mock('@react-three/drei', () => ({
  OrbitControls: ({ children, ...props }: any) => (
    <div data-testid="orbit-controls" {...props}>
      {children}
    </div>
  ),
  Environment: ({ children, ...props }: any) => (
    <div data-testid="environment" {...props}>
      {children}
    </div>
  ),
  ContactShadows: ({ children, ...props }: any) => (
    <div data-testid="contact-shadows" {...props}>
      {children}
    </div>
  ),
  Html: ({ children, ...props }: any) => (
    <div data-testid="html" {...props}>
      {children}
    </div>
  ),
  useProgress: () => ({ progress: 50 }),
  Loader: () => <div data-testid="loader">Loading...</div>
}));

jest.mock('../ModelLoader', () => ({
  ModelLoader: ({ onLoad, onError, ...props }: any) => {
    React.useEffect(() => {
      // Simulate successful load after a short delay
      const timer = setTimeout(() => {
        onLoad?.();
      }, 100);
      return () => clearTimeout(timer);
    }, [onLoad]);

    return <div data-testid="model-loader" {...props} />;
  }
}));

// Mock data
const mockAssets: Asset3D[] = [
  {
    id: 'asset-1',
    productId: 'product-1',
    name: 'Test Marble Model',
    description: 'Test 3D model',
    type: AssetType.MODEL_3D,
    format: AssetFormat.GLB,
    quality: AssetQuality.HIGH,
    fileName: 'test_marble.glb',
    filePath: '/models/test_marble.glb',
    fileSize: 1024000,
    mimeType: 'model/gltf-binary',
    vertices: 10000,
    faces: 3333,
    tags: ['test', 'marble'],
    downloadCount: 0,
    viewCount: 0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const mockMaterials: MaterialDefinition[] = [
  {
    id: 'material-1',
    name: 'White Marble',
    description: 'Polished white marble material',
    baseColor: '#f8f8f8',
    metallic: 0.0,
    roughness: 0.1,
    tilingU: 1.0,
    tilingV: 1.0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const mockConfiguration: ViewerConfiguration = {
  id: 'config-1',
  productId: 'product-1',
  cameraPosition: { x: 5, y: 5, z: 5 },
  cameraTarget: { x: 0, y: 0, z: 0 },
  cameraFov: 75,
  ambientLightColor: '#ffffff',
  ambientLightIntensity: 0.4,
  directionalLightColor: '#ffffff',
  directionalLightIntensity: 1.0,
  directionalLightPosition: { x: 10, y: 10, z: 5 },
  backgroundType: 'color',
  backgroundColor: '#f0f0f0',
  enableOrbitControls: true,
  enableZoom: true,
  enablePan: true,
  enableRotate: true,
  autoRotate: false,
  autoRotateSpeed: 2.0,
  enableShadows: true,
  shadowMapSize: 1024,
  enableAntialiasing: true,
  pixelRatio: 1.0,
  enableAnnotations: true,
  annotations: [
    {
      id: 'annotation-1',
      position: { x: 1, y: 1, z: 1 },
      title: 'Test Annotation',
      description: 'Test annotation description',
      type: 'info',
      visible: true
    }
  ],
  createdAt: new Date(),
  updatedAt: new Date()
};

describe('ProductViewer3D', () => {
  const defaultProps = {
    productId: 'product-1',
    assets: mockAssets,
    configuration: mockConfiguration,
    materials: mockMaterials,
    width: 800,
    height: 600
  };

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  test('renders without crashing', () => {
    render(<ProductViewer3D {...defaultProps} />);
    expect(screen.getByTestId('canvas')).toBeInTheDocument();
  });

  test('displays loading state initially', () => {
    render(<ProductViewer3D {...defaultProps} />);
    expect(screen.getByText('Loading 3D model...')).toBeInTheDocument();
  });

  test('calls onLoad callback when model loads', async () => {
    const onLoad = jest.fn();
    render(<ProductViewer3D {...defaultProps} onLoad={onLoad} />);

    await waitFor(() => {
      expect(onLoad).toHaveBeenCalled();
    });
  });

  test('displays error state when no model asset is provided', () => {
    const assetsWithoutModel = mockAssets.filter(asset => asset.type !== AssetType.MODEL_3D);
    
    render(
      <ProductViewer3D 
        {...defaultProps} 
        assets={assetsWithoutModel}
      />
    );

    expect(screen.getByText('3D Viewer Error')).toBeInTheDocument();
    expect(screen.getByText('No 3D model found for this product')).toBeInTheDocument();
  });

  test('renders viewer controls when enabled', async () => {
    render(<ProductViewer3D {...defaultProps} enableControls={true} />);

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading 3D model...')).not.toBeInTheDocument();
    });

    // Controls should be rendered (they would be in the DOM but might not be visible in test)
    expect(screen.getByTestId('canvas')).toBeInTheDocument();
  });

  test('handles quality change', () => {
    const { rerender } = render(
      <ProductViewer3D {...defaultProps} quality={AssetQuality.HIGH} />
    );

    // Change quality
    rerender(
      <ProductViewer3D {...defaultProps} quality={AssetQuality.LOW} />
    );

    expect(screen.getByTestId('canvas')).toBeInTheDocument();
  });

  test('handles camera change callback', async () => {
    const onCameraChange = jest.fn();
    
    render(
      <ProductViewer3D 
        {...defaultProps} 
        onCameraChange={onCameraChange}
      />
    );

    // Camera changes would be triggered by OrbitControls in real usage
    // In tests, we can verify the component renders correctly
    expect(screen.getByTestId('orbit-controls')).toBeInTheDocument();
  });

  test('handles annotation click', async () => {
    const onAnnotationClick = jest.fn();
    
    render(
      <ProductViewer3D 
        {...defaultProps} 
        onAnnotationClick={onAnnotationClick}
        enableAnnotations={true}
      />
    );

    // Annotations would be rendered as part of the 3D scene
    expect(screen.getByTestId('canvas')).toBeInTheDocument();
  });

  test('applies custom dimensions', () => {
    const customWidth = 1200;
    const customHeight = 800;
    
    render(
      <ProductViewer3D 
        {...defaultProps} 
        width={customWidth}
        height={customHeight}
      />
    );

    const container = screen.getByTestId('canvas').parentElement;
    expect(container).toHaveStyle(`width: ${customWidth}px`);
    expect(container).toHaveStyle(`height: ${customHeight}px`);
  });

  test('handles progress updates', () => {
    const onProgress = jest.fn();
    
    render(
      <ProductViewer3D 
        {...defaultProps} 
        onProgress={onProgress}
      />
    );

    // Progress would be called during model loading
    expect(screen.getByTestId('canvas')).toBeInTheDocument();
  });

  test('renders with performance monitor when enabled', () => {
    render(
      <ProductViewer3D 
        {...defaultProps} 
        enablePerformanceMonitor={true}
      />
    );

    expect(screen.getByTestId('canvas')).toBeInTheDocument();
  });

  test('handles error callback', () => {
    const onError = jest.fn();
    const assetsWithoutModel = [];
    
    render(
      <ProductViewer3D 
        {...defaultProps} 
        assets={assetsWithoutModel}
        onError={onError}
      />
    );

    expect(screen.getByText('3D Viewer Error')).toBeInTheDocument();
  });

  test('applies configuration settings correctly', () => {
    const customConfig = {
      ...mockConfiguration,
      backgroundColor: '#ff0000',
      enableShadows: false,
      cameraFov: 60
    };

    render(
      <ProductViewer3D 
        {...defaultProps} 
        configuration={customConfig}
      />
    );

    const canvas = screen.getByTestId('canvas');
    expect(canvas).toHaveStyle('background: #ff0000');
  });

  test('handles material changes', () => {
    const { rerender } = render(
      <ProductViewer3D {...defaultProps} />
    );

    // Change materials
    const newMaterials = [
      ...mockMaterials,
      {
        id: 'material-2',
        name: 'Black Marble',
        description: 'Black marble material',
        baseColor: '#2a2a2a',
        metallic: 0.0,
        roughness: 0.2,
        tilingU: 1.0,
        tilingV: 1.0,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    rerender(
      <ProductViewer3D 
        {...defaultProps} 
        materials={newMaterials}
      />
    );

    expect(screen.getByTestId('canvas')).toBeInTheDocument();
  });

  test('handles disabled controls', () => {
    render(
      <ProductViewer3D 
        {...defaultProps} 
        enableControls={false}
      />
    );

    expect(screen.getByTestId('canvas')).toBeInTheDocument();
  });

  test('handles disabled annotations', () => {
    render(
      <ProductViewer3D 
        {...defaultProps} 
        enableAnnotations={false}
      />
    );

    expect(screen.getByTestId('canvas')).toBeInTheDocument();
  });

  test('applies custom className', () => {
    const customClassName = 'custom-viewer-class';
    
    render(
      <ProductViewer3D 
        {...defaultProps} 
        className={customClassName}
      />
    );

    const container = screen.getByTestId('canvas').parentElement;
    expect(container).toHaveClass(customClassName);
  });

  test('handles empty assets array', () => {
    render(
      <ProductViewer3D 
        {...defaultProps} 
        assets={[]}
      />
    );

    expect(screen.getByText('3D Viewer Error')).toBeInTheDocument();
    expect(screen.getByText('No 3D model found for this product')).toBeInTheDocument();
  });

  test('handles missing configuration', () => {
    render(
      <ProductViewer3D 
        {...defaultProps} 
        configuration={undefined}
      />
    );

    // Should still render with default settings
    expect(screen.getByTestId('canvas')).toBeInTheDocument();
  });
});
