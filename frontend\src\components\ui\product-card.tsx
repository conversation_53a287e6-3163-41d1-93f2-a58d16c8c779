import * as React from "react"
import { cn } from "@/lib/utils"
import { <PERSON>, CardContent, CardFooter } from "./card"
import { Button } from "./button"
import { useAuth } from "@/contexts/auth-context"
import { QuoteRequestModal } from "./quote-request-modal"

export interface ProductCardProps extends React.HTMLAttributes<HTMLDivElement> {
  product: {
    id: string
    name: string
    category: string
    type?: string // ebatlı veya blok
    price: {
      min: number
      max: number
      currency: string
      unit: string
    }
    image: string
    rating?: number
    reviewCount?: number
    location?: string
    producer?: string
    features?: string[]
    hasQuarry?: boolean // blok ürünleri için ocak sahibi olup olmadığı
  }
  onViewDetails?: (productId: string) => void
  onRequestQuote?: (productId: string) => void
  onToggleFavorite?: (productId: string) => void
  onView3D?: (productId: string) => void
  isFavorite?: boolean
  show3DViewer?: boolean
}

/**
 * ProductCard component following RFC-004 UI/UX Design System
 * Specialized card for natural stone products with 3D viewer integration
 */
const ProductCard = React.forwardRef<HTMLDivElement, ProductCardProps>(
  ({ 
    className, 
    product, 
    onViewDetails,
    onRequestQuote,
    onToggleFavorite,
    onView3D,
    isFavorite = false,
    show3DViewer = true,
    ...props 
  }, ref) => {
    const { isAuthenticated, showLoginModal } = useAuth()
    const [isQuoteModalOpen, setIsQuoteModalOpen] = React.useState(false)

    const handleViewDetails = () => {
      // Navigate to product detail page
      window.location.href = `/products/${product.id}`
    }

    const handleRequestQuote = (e: React.MouseEvent) => {
      e.stopPropagation()
      if (!isAuthenticated) {
        showLoginModal()
        return
      }
      setIsQuoteModalOpen(true)
    }

    const handleToggleFavorite = (e: React.MouseEvent) => {
      e.stopPropagation()
      if (!isAuthenticated) {
        showLoginModal()
        return
      }
      onToggleFavorite?.(product.id)
    }

    const handleView3D = (e: React.MouseEvent) => {
      e.stopPropagation()
      onView3D?.(product.id)
    }

    return (
      <>
        <Card
          ref={ref}
          variant="product"
          hoverable
          className={cn("cursor-pointer group flex flex-col h-full", className)}
          onClick={handleViewDetails}
          {...props}
        >
          {/* Product Image Container */}
          <div className="relative overflow-hidden">
            <img
              src={product.image}
              alt={product.name}
              className={cn(
                "w-full h-48 object-cover",
                "transition-transform duration-300 ease-in-out",
                "group-hover:scale-105"
              )}
              loading="lazy"
            />
            
            {/* Category Badge */}
            <div className="absolute top-2 left-2">
              <span className={cn(
                "px-2 py-1 text-xs font-medium rounded-full",
                "bg-[var(--primary-stone)] text-white",
                "shadow-sm"
              )}>
                {product.category}
              </span>
            </div>

            {/* Type Badge */}
            {product.type && (
              <div className="absolute top-2 right-2">
                <span className={cn(
                  "px-2 py-1 text-xs font-medium rounded-full",
                  product.type === 'blok'
                    ? "bg-orange-500 text-white"
                    : "bg-blue-500 text-white",
                  "shadow-sm"
                )}>
                  {product.type === 'blok' ? '🧱 BLOK' : '📐 EBATLI'}
                </span>
              </div>
            )}


          </div>

          {/* Product Content */}
          <CardContent className="p-4 flex-grow">
            <div className="space-y-2">
              {/* Product Name */}
              <h3 className="font-semibold text-lg text-gray-900 line-clamp-1">
                {product.name}
              </h3>

              {/* Producer & Location */}
              {(product.producer || product.location) && (
                <div className="text-sm text-gray-600">
                  {product.producer && <span>{product.producer}</span>}
                  {product.producer && product.location && <span> • </span>}
                  {product.location && <span>{product.location}</span>}
                </div>
              )}

              {/* Rating */}
              {product.rating && (
                <div className="flex items-center space-x-1">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        className={cn(
                          "h-4 w-4",
                          i < Math.floor(product.rating!)
                            ? "text-yellow-400 fill-current"
                            : "text-gray-300"
                        )}
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    {product.rating} ({product.reviewCount})
                  </span>
                </div>
              )}

              {/* Features */}
              {product.features && product.features.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {product.features.slice(0, 3).map((feature, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                    >
                      {feature}
                    </span>
                  ))}
                  {product.features.length > 3 && (
                    <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                      +{product.features.length - 3}
                    </span>
                  )}
                </div>
              )}
            </div>
          </CardContent>

          {/* Action Buttons */}
          <CardFooter className="p-4 pt-2">
            <div className="space-y-2">
              {/* First row: Teklif İste and Favoriler */}
              <div className="flex space-x-2">
                <Button
                  onClick={handleRequestQuote}
                  className="flex-1"
                  size="sm"
                >
                  Teklif İste
                </Button>
                <Button
                  onClick={handleToggleFavorite}
                  variant="outline"
                  size="sm"
                  className={cn(
                    "px-3 min-w-[44px]",
                    isFavorite && "text-red-500 border-red-200 bg-red-50"
                  )}
                  title={isFavorite ? "Favorilerden Çıkar" : "Favorilere Ekle"}
                >
                  <svg
                    className="h-4 w-4"
                    fill={isFavorite ? "currentColor" : "none"}
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                    />
                  </svg>
                </Button>
              </div>

              {/* Second row: 3D Görünüm (only for non-block products) */}
              {product.type !== 'blok' && (
                <Button
                  onClick={handleView3D}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  3D Görünüm
                </Button>
              )}
            </div>
          </CardFooter>
        </Card>

        {/* Quote Request Modal */}
        <QuoteRequestModal
          isOpen={isQuoteModalOpen}
          onClose={() => setIsQuoteModalOpen(false)}
          product={product}
        />
      </>
    )
  }
)

ProductCard.displayName = "ProductCard"

export default ProductCard
