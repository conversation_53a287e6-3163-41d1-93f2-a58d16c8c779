'use client';

import React, { useState, useEffect } from 'react';
import { ChatBubbleLeftRightIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface WhatsAppWidgetProps {
  className?: string;
  position?: 'bottom-right' | 'bottom-left';
  showOnPages?: string[];
  hideOnPages?: string[];
}

interface WhatsAppConfig {
  enabled: boolean;
  businessNumber: string;
  businessURL: string;
  autoReply: boolean;
  isWithinBusinessHours: boolean;
}

const WhatsAppWidget: React.FC<WhatsAppWidgetProps> = ({
  className = '',
  position = 'bottom-right',
  showOnPages = [],
  hideOnPages = []
}) => {
  const [config, setConfig] = useState<WhatsAppConfig | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadWhatsAppConfig();
  }, []);

  const loadWhatsAppConfig = async () => {
    try {
      // Next.js rewrites ile backend'e yönlendirilir
      const response = await fetch('/api/whatsapp/widget-config');
      if (response.ok) {
        const data = await response.json();
        setConfig(data.data);
      }
    } catch (error) {
      console.error('Failed to load WhatsApp config:', error);
    } finally {
      setLoading(false);
    }
  };

  const shouldShowWidget = () => {
    if (!config?.enabled) return false;

    const currentPath = window.location.pathname;

    // Hide on specific pages (3D showroom and dashboard)
    if (hideOnPages.length > 0) {
      return !hideOnPages.some(page => currentPath.includes(page));
    }

    // Show only on specific pages
    if (showOnPages.length > 0) {
      return showOnPages.some(page => currentPath.includes(page));
    }

    // Default: show on all pages except 3D showroom and dashboard
    const hiddenPaths = ['/3d-showroom', '/dashboard', '/customer', '/producer', '/admin'];
    return !hiddenPaths.some(path => currentPath.includes(path));
  };

  const handleWhatsAppClick = () => {
    if (config?.businessURL) {
      window.open(config.businessURL, '_blank');
    }
  };

  const generateSupportURL = async (message: string) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/api/whatsapp/support-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Website Ziyaretçisi',
          issue: message,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        window.open(data.data.supportURL, '_blank');
      }
    } catch (error) {
      console.error('Failed to generate support URL:', error);
      // Fallback to business URL
      handleWhatsAppClick();
    }
  };

  if (loading || !shouldShowWidget()) {
    return null;
  }

  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-50 ${className}`}>
      {/* Chat Bubble */}
      {isOpen && (
        <div className="mb-4 bg-white rounded-lg shadow-lg border border-gray-200 w-80 max-w-sm">
          {/* Header */}
          <div className="bg-green-500 text-white p-4 rounded-t-lg flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                <span className="text-green-500 font-bold text-lg">DT</span>
              </div>
              <div>
                <h3 className="font-semibold">Doğal Taş Pazaryeri</h3>
                <p className="text-sm opacity-90">
                  {config?.isWithinBusinessHours ? 'Çevrimiçi' : 'Çevrimdışı'}
                </p>
              </div>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className="p-4">
            <div className="bg-gray-100 rounded-lg p-3 mb-4">
              <p className="text-sm text-gray-700">
                {config?.isWithinBusinessHours 
                  ? '👋 Merhaba! Size nasıl yardımcı olabiliriz?'
                  : '🌙 Mesaj saatleri dışında ulaştınız. En kısa sürede size dönüş yapacağız.'
                }
              </p>
            </div>

            {/* Quick Actions */}
            <div className="space-y-2">
              <button
                onClick={() => generateSupportURL('Genel bilgi almak istiyorum')}
                className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <span className="text-sm">💬 Genel Bilgi</span>
              </button>
              
              <button
                onClick={() => generateSupportURL('Sipariş durumu hakkında bilgi almak istiyorum')}
                className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <span className="text-sm">📦 Sipariş Durumu</span>
              </button>
              
              <button
                onClick={() => generateSupportURL('Ödeme konusunda yardım istiyorum')}
                className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <span className="text-sm">💳 Ödeme Yardımı</span>
              </button>
              
              <button
                onClick={() => generateSupportURL('Teknik destek istiyorum')}
                className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <span className="text-sm">🔧 Teknik Destek</span>
              </button>
            </div>

            {/* Start Chat Button */}
            <button
              onClick={handleWhatsAppClick}
              className="w-full mt-4 bg-green-500 text-white py-3 px-4 rounded-lg hover:bg-green-600 transition-colors font-medium"
            >
              WhatsApp'ta Sohbet Başlat
            </button>
          </div>
        </div>
      )}

      {/* Floating Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg 
          flex items-center justify-center transition-all duration-300 transform hover:scale-110
          ${isOpen ? 'rotate-180' : ''}
        `}
        title="WhatsApp Desteği"
      >
        {isOpen ? (
          <XMarkIcon className="h-6 w-6" />
        ) : (
          <ChatBubbleLeftRightIcon className="h-6 w-6" />
        )}
      </button>

      {/* Online Indicator */}
      {config?.isWithinBusinessHours && !isOpen && (
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full animate-pulse"></div>
      )}
    </div>
  );
};

export default WhatsAppWidget;
