{"name": "natural-stone-marketplace-frontend", "version": "1.0.0", "description": "Frontend for Turkish Natural Stone Marketplace Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:3d": "jest --testPathPattern=3d", "test:a11y": "jest --testPathPattern=a11y", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "ANALYZE=true next build", "e2e": "playwright test", "e2e:ui": "playwright test --ui"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.0", "@prisma/client": "^6.11.1", "@radix-ui/react-checkbox": "^1.1.7", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.8.0", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.6.0", "bcrypt": "^6.0.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "critters": "^0.0.23", "framer-motion": "^10.16.0", "i18next": "^25.3.2", "jsonwebtoken": "^9.0.2", "leva": "^0.10.0", "lucide-react": "^0.294.0", "next": "^14.0.0", "next-i18next": "^15.4.2", "prisma": "^6.11.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.0", "socket.io-client": "^4.8.1", "swr": "^2.2.4", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0", "three": "^0.177.0", "typescript": "^5.3.0", "zod": "^3.22.0", "zustand": "^4.4.7"}, "devDependencies": {"@playwright/test": "^1.40.0", "@storybook/addon-a11y": "^7.6.0", "@storybook/addon-docs": "^7.6.0", "@storybook/addon-essentials": "^7.6.0", "@storybook/addon-interactions": "^7.6.0", "@storybook/addon-links": "^7.6.0", "@storybook/addon-onboarding": "^1.0.8", "@storybook/blocks": "^7.6.0", "@storybook/nextjs": "^7.6.0", "@storybook/react": "^7.6.0", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.0", "@types/node": "20.19.6", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/three": "^0.177.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "eslint-config-next": "^14.0.0", "jest": "^29.7.0", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.7.0", "playwright": "^1.40.0", "postcss": "^8.4.0", "prettier": "^3.1.0", "storybook": "^7.6.0", "webpack-bundle-analyzer": "^4.10.0"}, "keywords": ["marketplace", "natural-stone", "b2b", "nextjs", "react", "typescript"], "author": "Natural Stone Marketplace Team", "license": "MIT", "engines": {"node": ">=20.10.0", "npm": ">=10.0.0"}}