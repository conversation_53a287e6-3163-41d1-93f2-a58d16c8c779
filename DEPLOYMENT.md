# 🚀 AI Marketing System - Production Deployment Guide

Bu dokümantasyon, Self-Learning AI Pazarlama Sistemi'nin production ortamına deploy edilmesi için gerekli tüm adımları içerir.

## 📋 Ön Gereksinimler

### Sistem Gereksinimleri
- **CPU**: Minimum 4 core, Önerilen 8+ core
- **RAM**: Minimum 8GB, Önerilen 16GB+
- **Disk**: Minimum 100GB SSD, Önerilen 500GB+ SSD
- **Network**: Stabil internet bağlantısı, minimum 100Mbps

### Yazılım Gereksinimleri
- Docker 20.10+
- Docker Compose 2.0+
- Git
- curl
- jq (JSON processor)

### API Keys ve Credentials
Aşağıdaki API key'leri temin etmeniz gerekiyor:

1. **OpenAI API Key** - AI modelleri için
2. **Trade Map API Key** - Ticaret verileri için
3. **LinkedIn API Credentials** - B2B pazarlama için
4. **Google Ads API Credentials** - Reklam yönetimi için

## 🔧 Kurulum Adımları

### 1. Repository'yi Clone Edin

```bash
git clone https://github.com/your-org/marketplace-ai-marketing.git
cd marketplace-ai-marketing
```

### 2. Environment Konfigürasyonu

```bash
# Production environment dosyasını oluşturun
cp .env.production.example .env.production

# Environment dosyasını düzenleyin
nano .env.production
```

**Kritik değişkenler:**
- `DB_PASSWORD`: Güçlü bir veritabanı şifresi
- `JWT_SECRET`: 32+ karakter güvenli key
- `OPENAI_API_KEY`: OpenAI API key'iniz
- API credentials (LinkedIn, Google Ads, Trade Map)

### 3. SSL Sertifikalarını Hazırlayın

```bash
# SSL dizinini oluşturun
mkdir -p nginx/ssl

# Sertifikalarınızı kopyalayın
cp your-cert.pem nginx/ssl/cert.pem
cp your-private-key.key nginx/ssl/private.key
```

### 4. Deployment Script'ini Çalıştırın

```bash
# Script'i çalıştırılabilir yapın
chmod +x scripts/deploy.sh

# Production deployment'ı başlatın
./scripts/deploy.sh production
```

## 📊 Monitoring ve Health Checks

### Health Check Script

```bash
# Sistem sağlığını kontrol edin
chmod +x scripts/health-check.sh
./scripts/health-check.sh --verbose
```

### Monitoring Dashboard'ları

- **Grafana**: http://your-domain:3003
- **Prometheus**: http://your-domain:9090
- **AI Marketing Dashboard**: http://your-domain/admin/ai-marketing

### Log Monitoring

```bash
# Tüm servislerin loglarını izleyin
docker-compose -f docker-compose.production.yml logs -f

# Sadece AI Marketing backend logları
docker-compose -f docker-compose.production.yml logs -f ai-marketing-backend

# Sadece hata logları
docker-compose -f docker-compose.production.yml logs -f | grep ERROR
```

## 🔄 Güncellemeler ve Maintenance

### Güncelleme Süreci

```bash
# Yeni kodu çekin
git pull origin main

# Backup oluşturun
./scripts/backup.sh

# Güncellenmiş sistemi deploy edin
./scripts/deploy.sh production
```

### Backup ve Restore

```bash
# Manuel backup oluşturun
docker-compose -f docker-compose.production.yml exec postgres pg_dump -U postgres marketplace > backup_$(date +%Y%m%d).sql

# Backup'ı restore edin
docker-compose -f docker-compose.production.yml exec -T postgres psql -U postgres marketplace < backup_20240105.sql
```

### Database Maintenance

```bash
# Database istatistiklerini güncelleyin
docker-compose -f docker-compose.production.yml exec postgres psql -U postgres marketplace -c "ANALYZE;"

# Vacuum işlemi
docker-compose -f docker-compose.production.yml exec postgres psql -U postgres marketplace -c "VACUUM ANALYZE;"
```

## 🛡️ Güvenlik Konfigürasyonu

### Firewall Ayarları

```bash
# Sadece gerekli portları açın
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

### SSL/TLS Konfigürasyonu

Nginx konfigürasyonunda SSL ayarları:

```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/private.key;
    
    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
}
```

## 📈 Performance Optimization

### Database Optimization

```sql
-- PostgreSQL performans ayarları
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
SELECT pg_reload_conf();
```

### Redis Optimization

```bash
# Redis memory policy
docker-compose -f docker-compose.production.yml exec redis redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

### AI Marketing System Tuning

Environment değişkenleri ile performans ayarları:

```bash
# Öğrenme döngüsü sıklığı (15 dakika)
LEARNING_CYCLE_INTERVAL=900000

# Araştırma döngüsü sıklığı (6 saat)
RESEARCH_CYCLE_INTERVAL=21600000

# Optimizasyon döngüsü sıklığı (5 dakika)
OPTIMIZATION_CYCLE_INTERVAL=300000
```

## 🚨 Troubleshooting

### Yaygın Sorunlar ve Çözümleri

#### 1. AI Marketing Sistemi Başlamıyor

```bash
# Logları kontrol edin
docker-compose -f docker-compose.production.yml logs ai-marketing-backend

# Environment değişkenlerini kontrol edin
docker-compose -f docker-compose.production.yml exec ai-marketing-backend env | grep AI_

# Sistemi yeniden başlatın
docker-compose -f docker-compose.production.yml restart ai-marketing-backend
```

#### 2. Database Bağlantı Sorunu

```bash
# PostgreSQL durumunu kontrol edin
docker-compose -f docker-compose.production.yml ps postgres

# Database loglarını kontrol edin
docker-compose -f docker-compose.production.yml logs postgres

# Bağlantıyı test edin
docker-compose -f docker-compose.production.yml exec postgres psql -U postgres -c "SELECT version();"
```

#### 3. API Entegrasyonları Çalışmıyor

```bash
# API health check
curl http://localhost:3001/api/admin/ai-marketing/system/health

# API key'leri kontrol edin
docker-compose -f docker-compose.production.yml exec ai-marketing-backend env | grep API_KEY
```

#### 4. Yüksek Memory Kullanımı

```bash
# Memory kullanımını kontrol edin
docker stats

# Node.js memory limit'ini artırın
NODE_OPTIONS=--max-old-space-size=4096
```

### Emergency Procedures

#### Sistem Tamamen Çöktüğünde

```bash
# Tüm servisleri durdur
docker-compose -f docker-compose.production.yml down

# Son backup'ı restore et
./scripts/restore-backup.sh latest

# Sistemi yeniden başlat
./scripts/deploy.sh production
```

#### Database Corruption

```bash
# Database backup'ından restore
docker-compose -f docker-compose.production.yml down
docker volume rm marketplace_postgres_data
docker-compose -f docker-compose.production.yml up -d postgres
# Backup restore işlemi...
```

## 📞 Destek ve İletişim

### Log Dosyaları Konumları

- **Application Logs**: `/app/logs/`
- **Nginx Logs**: `/var/log/nginx/`
- **PostgreSQL Logs**: Container içinde `/var/log/postgresql/`

### Monitoring Alerts

Kritik durumlar için alert'ler:

- CPU kullanımı > %90
- Memory kullanımı > %90
- Disk kullanımı > %90
- AI Marketing sistemi down
- Database bağlantı hatası

### Performance Metrics

İzlenmesi gereken metrikler:

- **Learning Cycles**: Dakikada tamamlanan öğrenme döngüsü sayısı
- **API Response Times**: API yanıt süreleri
- **Database Query Performance**: Veritabanı sorgu performansı
- **Memory Usage**: Bellek kullanımı
- **Error Rates**: Hata oranları

## 🎯 Production Checklist

Deployment öncesi kontrol listesi:

- [ ] Tüm environment değişkenleri ayarlandı
- [ ] SSL sertifikaları yüklendi
- [ ] Database backup stratejisi hazırlandı
- [ ] Monitoring dashboard'ları konfigüre edildi
- [ ] Firewall kuralları ayarlandı
- [ ] DNS kayıtları güncellendi
- [ ] Load testing yapıldı
- [ ] Disaster recovery planı hazırlandı
- [ ] Team'e deployment bilgisi verildi

## 📚 Ek Kaynaklar

- [AI Marketing System Architecture](./docs/ARCHITECTURE.md)
- [API Documentation](./docs/API.md)
- [Database Schema](./backend/src/modules/ai-marketing/database/schema.sql)
- [Monitoring Guide](./docs/MONITORING.md)

---

**Not**: Bu deployment guide sürekli güncellenmektedir. En son versiyonu için repository'yi kontrol edin.
