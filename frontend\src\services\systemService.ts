// System Monitoring Service
import apiService from './api';

export interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    temperature: number;
    loadAverage?: number[];
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
    available?: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
    available?: number;
  };
  network: {
    inbound: number;
    outbound: number;
    connections?: number;
  };
}

export interface ServiceStatus {
  name: string;
  status: 'running' | 'stopped' | 'error';
  version: string;
  uptime: number;
  pid?: number;
  memory?: number;
  cpu?: number;
}

export interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  resolved: boolean;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  source?: string;
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  timestamp: string;
  services: {
    api: string;
    database: string;
    redis: string;
  };
  uptime: number;
}

export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'debug' | 'info' | 'warning' | 'error';
  source: string;
  message: string;
  details?: any;
  userId?: string;
  ip?: string;
  userAgent?: string;
}

export interface LogFilter {
  level?: string;
  source?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface LogStats {
  totalLogs: number;
  errorCount: number;
  warningCount: number;
  infoCount: number;
  debugCount: number;
  sources: { [key: string]: number };
  hourlyDistribution: { [hour: string]: number };
}

export interface MaintenanceTask {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime?: string;
  endTime?: string;
  result?: any;
  error?: string;
}

export interface MaintenanceStats {
  cacheSize: number;
  databaseSize: number;
  logSize: number;
  tempFileSize: number;
  lastBackup?: string;
  lastOptimization?: string;
  lastCleanup?: string;
}

class SystemService {
  /**
   * Get system metrics
   */
  async getSystemMetrics(): Promise<SystemMetrics> {
    try {
      const response = await apiService.get('/api/system/metrics');
      return response.data;
    } catch (error) {
      console.error('Error fetching system metrics:', error);
      throw new Error('Failed to fetch system metrics');
    }
  }

  /**
   * Get service status
   */
  async getServiceStatus(): Promise<ServiceStatus[]> {
    try {
      const response = await apiService.get('/api/system/services');
      return response.data;
    } catch (error) {
      console.error('Error fetching service status:', error);
      throw new Error('Failed to fetch service status');
    }
  }

  /**
   * Get system alerts
   */
  async getSystemAlerts(limit: number = 50): Promise<SystemAlert[]> {
    try {
      const response = await apiService.get(`/api/system/alerts?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching system alerts:', error);
      throw new Error('Failed to fetch system alerts');
    }
  }

  /**
   * Clear system alerts
   */
  async clearSystemAlerts(): Promise<void> {
    try {
      await apiService.delete('/api/system/alerts');
    } catch (error) {
      console.error('Error clearing system alerts:', error);
      throw new Error('Failed to clear system alerts');
    }
  }

  /**
   * Get system uptime
   */
  async getSystemUptime(): Promise<{ uptime: number; uptimeFormatted: string }> {
    try {
      const response = await apiService.get('/api/system/uptime');
      return response.data;
    } catch (error) {
      console.error('Error fetching system uptime:', error);
      throw new Error('Failed to fetch system uptime');
    }
  }

  /**
   * Get system health
   */
  async getSystemHealth(): Promise<SystemHealth> {
    try {
      const response = await apiService.get('/api/system/health');
      return response.data;
    } catch (error) {
      console.error('Error fetching system health:', error);
      throw new Error('Failed to fetch system health');
    }
  }

  /**
   * Clear cache
   */
  async clearCache(): Promise<MaintenanceTask> {
    try {
      const response = await apiService.post('/api/system/maintenance/cache');
      return response.data;
    } catch (error) {
      console.error('Error clearing cache:', error);
      throw new Error('Failed to clear cache');
    }
  }

  /**
   * Optimize database
   */
  async optimizeDatabase(): Promise<MaintenanceTask> {
    try {
      const response = await apiService.post('/api/system/maintenance/database');
      return response.data;
    } catch (error) {
      console.error('Error optimizing database:', error);
      throw new Error('Failed to optimize database');
    }
  }

  /**
   * Get system logs
   */
  async getLogs(filter: LogFilter = {}): Promise<{ logs: LogEntry[]; total: number }> {
    try {
      const params = new URLSearchParams();

      if (filter.level) params.append('level', filter.level);
      if (filter.source) params.append('source', filter.source);
      if (filter.search) params.append('search', filter.search);
      if (filter.startDate) params.append('startDate', filter.startDate);
      if (filter.endDate) params.append('endDate', filter.endDate);
      if (filter.limit) params.append('limit', filter.limit.toString());
      if (filter.offset) params.append('offset', filter.offset.toString());

      const response = await apiService.get(`/api/system/logs?${params.toString()}`);
      return {
        logs: response.data,
        total: response.total || response.data.length
      };
    } catch (error) {
      console.error('Error fetching logs:', error);
      throw new Error('Failed to fetch logs');
    }
  }

  /**
   * Get log statistics
   */
  async getLogStats(filter: Partial<LogFilter> = {}): Promise<LogStats> {
    try {
      const params = new URLSearchParams();

      if (filter.startDate) params.append('startDate', filter.startDate);
      if (filter.endDate) params.append('endDate', filter.endDate);

      const response = await apiService.get(`/api/system/logs/stats?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching log stats:', error);
      throw new Error('Failed to fetch log statistics');
    }
  }

  /**
   * Get available log sources
   */
  async getLogSources(): Promise<string[]> {
    try {
      const response = await apiService.get('/api/system/logs/sources');
      return response.data;
    } catch (error) {
      console.error('Error fetching log sources:', error);
      throw new Error('Failed to fetch log sources');
    }
  }

  /**
   * Export logs
   */
  async exportLogs(filter: LogFilter = {}, format: 'json' | 'csv' | 'txt' = 'json'): Promise<string> {
    try {
      const response = await apiService.post('/api/system/logs/export', {
        filter,
        format
      });
      return response.data.filepath;
    } catch (error) {
      console.error('Error exporting logs:', error);
      throw new Error('Failed to export logs');
    }
  }

  /**
   * Clear old logs
   */
  async clearLogs(olderThanDays: number = 30): Promise<number> {
    try {
      const response = await apiService.delete('/api/system/logs/clear', {
        data: { olderThanDays }
      });
      return response.data.clearedCount;
    } catch (error) {
      console.error('Error clearing logs:', error);
      throw new Error('Failed to clear logs');
    }
  }

  /**
   * Clean temporary files
   */
  async cleanTempFiles(): Promise<MaintenanceTask> {
    try {
      const response = await apiService.post('/api/system/maintenance/cleanup');
      return response.data;
    } catch (error) {
      console.error('Error cleaning temp files:', error);
      throw new Error('Failed to clean temp files');
    }
  }

  /**
   * Create database backup
   */
  async createBackup(): Promise<MaintenanceTask> {
    try {
      const response = await apiService.post('/api/system/maintenance/backup');
      return response.data;
    } catch (error) {
      console.error('Error creating backup:', error);
      throw new Error('Failed to create backup');
    }
  }

  /**
   * Get maintenance statistics
   */
  async getMaintenanceStats(): Promise<MaintenanceStats> {
    try {
      const response = await apiService.get('/api/system/maintenance/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching maintenance stats:', error);
      throw new Error('Failed to fetch maintenance statistics');
    }
  }

  /**
   * Get running maintenance tasks
   */
  async getMaintenanceTasks(): Promise<MaintenanceTask[]> {
    try {
      const response = await apiService.get('/api/system/maintenance/tasks');
      return response.data;
    } catch (error) {
      console.error('Error fetching maintenance tasks:', error);
      throw new Error('Failed to fetch maintenance tasks');
    }
  }

  /**
   * Get maintenance task by ID
   */
  async getMaintenanceTask(taskId: string): Promise<MaintenanceTask> {
    try {
      const response = await apiService.get(`/api/system/maintenance/tasks/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching maintenance task:', error);
      throw new Error('Failed to fetch maintenance task');
    }
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes: number, decimals: number = 2): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  /**
   * Format uptime to human readable format
   */
  formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  /**
   * Get status color based on value and thresholds
   */
  getStatusColor(value: number, warningThreshold: number, criticalThreshold: number): string {
    if (value >= criticalThreshold) {
      return 'text-red-600';
    } else if (value >= warningThreshold) {
      return 'text-yellow-600';
    } else {
      return 'text-green-600';
    }
  }

  /**
   * Get status variant for badges
   */
  getStatusVariant(status: string): 'default' | 'destructive' | 'secondary' {
    switch (status) {
      case 'healthy':
      case 'running':
      case 'connected':
        return 'default';
      case 'warning':
      case 'slow':
        return 'secondary';
      case 'critical':
      case 'error':
      case 'stopped':
      case 'disconnected':
        return 'destructive';
      default:
        return 'secondary';
    }
  }

  /**
   * Calculate percentage
   */
  calculatePercentage(used: number, total: number): number {
    return Math.round((used / total) * 100 * 10) / 10;
  }

  /**
   * Get alert severity color
   */
  getAlertSeverityColor(severity: string): string {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  }

  /**
   * Check if system needs attention
   */
  needsAttention(metrics: SystemMetrics): boolean {
    return (
      metrics.cpu.usage > 80 ||
      metrics.memory.percentage > 85 ||
      metrics.disk.percentage > 90
    );
  }

  /**
   * Get system status based on metrics
   */
  getSystemStatus(metrics: SystemMetrics): 'healthy' | 'warning' | 'critical' {
    if (
      metrics.cpu.usage > 95 ||
      metrics.memory.percentage > 95 ||
      metrics.disk.percentage > 95
    ) {
      return 'critical';
    } else if (
      metrics.cpu.usage > 80 ||
      metrics.memory.percentage > 85 ||
      metrics.disk.percentage > 90
    ) {
      return 'warning';
    } else {
      return 'healthy';
    }
  }
}

export const systemService = new SystemService();
