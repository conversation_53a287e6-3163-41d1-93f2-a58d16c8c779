'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Trash2, 
  Settings, 
  BarChart3, 
  LineChart, 
  PieChart,
  Table,
  Filter,
  Calendar,
  Users,
  DollarSign,
  ShoppingCart,
  Activity,
  Save,
  Play
} from 'lucide-react';

interface ReportField {
  id: string;
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  category: 'financial' | 'business' | 'users' | 'system';
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max';
}

interface ReportFilter {
  id: string;
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'between';
  value: string | number | [string | number, string | number];
}

interface ReportConfig {
  name: string;
  description: string;
  chartType: 'line' | 'bar' | 'pie' | 'doughnut' | 'table';
  fields: string[];
  filters: ReportFilter[];
  groupBy?: string;
  sortBy?: string;
  sortOrder: 'asc' | 'desc';
  dateRange: {
    start: string;
    end: string;
  };
}

interface ReportBuilderProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: ReportConfig) => void;
  onPreview: (config: ReportConfig) => void;
}

const availableFields: ReportField[] = [
  // Financial Fields
  { id: 'revenue', name: 'Gelir', type: 'number', category: 'financial', aggregation: 'sum' },
  { id: 'commission', name: 'Komisyon', type: 'number', category: 'financial', aggregation: 'sum' },
  { id: 'payment_amount', name: 'Ödeme Tutarı', type: 'number', category: 'financial', aggregation: 'sum' },
  { id: 'payment_method', name: 'Ödeme Yöntemi', type: 'string', category: 'financial' },
  
  // Business Fields
  { id: 'order_count', name: 'Sipariş Sayısı', type: 'number', category: 'business', aggregation: 'count' },
  { id: 'quote_count', name: 'Teklif Sayısı', type: 'number', category: 'business', aggregation: 'count' },
  { id: 'conversion_rate', name: 'Dönüşüm Oranı', type: 'number', category: 'business', aggregation: 'avg' },
  { id: 'product_category', name: 'Ürün Kategorisi', type: 'string', category: 'business' },
  { id: 'producer_name', name: 'Üretici Adı', type: 'string', category: 'business' },
  
  // User Fields
  { id: 'user_count', name: 'Kullanıcı Sayısı', type: 'number', category: 'users', aggregation: 'count' },
  { id: 'customer_count', name: 'Müşteri Sayısı', type: 'number', category: 'users', aggregation: 'count' },
  { id: 'producer_count', name: 'Üretici Sayısı', type: 'number', category: 'users', aggregation: 'count' },
  { id: 'user_country', name: 'Kullanıcı Ülkesi', type: 'string', category: 'users' },
  { id: 'registration_date', name: 'Kayıt Tarihi', type: 'date', category: 'users' },
  
  // System Fields
  { id: 'api_calls', name: 'API Çağrıları', type: 'number', category: 'system', aggregation: 'count' },
  { id: 'response_time', name: 'Yanıt Süresi', type: 'number', category: 'system', aggregation: 'avg' },
  { id: 'error_count', name: 'Hata Sayısı', type: 'number', category: 'system', aggregation: 'count' },
  { id: 'uptime', name: 'Uptime', type: 'number', category: 'system', aggregation: 'avg' }
];

export default function ReportBuilder({ isOpen, onClose, onSave, onPreview }: ReportBuilderProps) {
  const [config, setConfig] = useState<ReportConfig>({
    name: '',
    description: '',
    chartType: 'bar',
    fields: [],
    filters: [],
    sortOrder: 'desc',
    dateRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end: new Date().toISOString().split('T')[0]
    }
  });

  const [activeTab, setActiveTab] = useState('basic');

  const chartTypes = [
    { value: 'bar', label: 'Bar Chart', icon: BarChart3 },
    { value: 'line', label: 'Line Chart', icon: LineChart },
    { value: 'pie', label: 'Pie Chart', icon: PieChart },
    { value: 'doughnut', label: 'Doughnut Chart', icon: PieChart },
    { value: 'table', label: 'Table', icon: Table }
  ];

  const operators = [
    { value: 'equals', label: 'Eşittir' },
    { value: 'not_equals', label: 'Eşit Değil' },
    { value: 'greater_than', label: 'Büyüktür' },
    { value: 'less_than', label: 'Küçüktür' },
    { value: 'contains', label: 'İçerir' },
    { value: 'between', label: 'Arasında' }
  ];

  const addField = (fieldId: string) => {
    if (!config.fields.includes(fieldId)) {
      setConfig(prev => ({
        ...prev,
        fields: [...prev.fields, fieldId]
      }));
    }
  };

  const removeField = (fieldId: string) => {
    setConfig(prev => ({
      ...prev,
      fields: prev.fields.filter(f => f !== fieldId)
    }));
  };

  const addFilter = () => {
    const newFilter: ReportFilter = {
      id: Date.now().toString(),
      field: availableFields[0].id,
      operator: 'equals',
      value: ''
    };
    setConfig(prev => ({
      ...prev,
      filters: [...prev.filters, newFilter]
    }));
  };

  const updateFilter = (filterId: string, updates: Partial<ReportFilter>) => {
    setConfig(prev => ({
      ...prev,
      filters: prev.filters.map(f => 
        f.id === filterId ? { ...f, ...updates } : f
      )
    }));
  };

  const removeFilter = (filterId: string) => {
    setConfig(prev => ({
      ...prev,
      filters: prev.filters.filter(f => f.id !== filterId)
    }));
  };

  const handleSave = () => {
    if (config.name && config.fields.length > 0) {
      onSave(config);
      onClose();
    }
  };

  const handlePreview = () => {
    if (config.fields.length > 0) {
      onPreview(config);
    }
  };

  const getFieldsByCategory = (category: string) => {
    return availableFields.filter(field => field.category === category);
  };

  const getFieldName = (fieldId: string) => {
    return availableFields.find(f => f.id === fieldId)?.name || fieldId;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Özel Rapor Oluşturucu
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Temel Bilgiler</TabsTrigger>
            <TabsTrigger value="fields">Alanlar</TabsTrigger>
            <TabsTrigger value="filters">Filtreler</TabsTrigger>
            <TabsTrigger value="settings">Ayarlar</TabsTrigger>
          </TabsList>

          {/* Basic Info Tab */}
          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="reportName">Rapor Adı</Label>
                <Input
                  id="reportName"
                  placeholder="Rapor adını girin"
                  value={config.name}
                  onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="chartType">Grafik Türü</Label>
                <Select value={config.chartType} onValueChange={(value: any) => setConfig(prev => ({ ...prev, chartType: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {chartTypes.map((type) => {
                      const IconComponent = type.icon;
                      return (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="w-4 h-4" />
                            {type.label}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="reportDescription">Açıklama</Label>
              <Input
                id="reportDescription"
                placeholder="Rapor açıklaması"
                value={config.description}
                onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
          </TabsContent>

          {/* Fields Tab */}
          <TabsContent value="fields" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Available Fields */}
              <Card>
                <CardHeader>
                  <CardTitle>Kullanılabilir Alanlar</CardTitle>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="financial" className="space-y-4">
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="financial">
                        <DollarSign className="w-4 h-4" />
                      </TabsTrigger>
                      <TabsTrigger value="business">
                        <BarChart3 className="w-4 h-4" />
                      </TabsTrigger>
                      <TabsTrigger value="users">
                        <Users className="w-4 h-4" />
                      </TabsTrigger>
                      <TabsTrigger value="system">
                        <Activity className="w-4 h-4" />
                      </TabsTrigger>
                    </TabsList>
                    
                    {['financial', 'business', 'users', 'system'].map(category => (
                      <TabsContent key={category} value={category} className="space-y-2">
                        {getFieldsByCategory(category).map(field => (
                          <div
                            key={field.id}
                            className="flex items-center justify-between p-2 border rounded hover:bg-gray-50 cursor-pointer"
                            onClick={() => addField(field.id)}
                          >
                            <div>
                              <p className="font-medium text-sm">{field.name}</p>
                              <p className="text-xs text-gray-500">
                                {field.type} {field.aggregation && `• ${field.aggregation}`}
                              </p>
                            </div>
                            <Plus className="w-4 h-4 text-blue-600" />
                          </div>
                        ))}
                      </TabsContent>
                    ))}
                  </Tabs>
                </CardContent>
              </Card>

              {/* Selected Fields */}
              <Card>
                <CardHeader>
                  <CardTitle>Seçili Alanlar</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {config.fields.length === 0 ? (
                      <p className="text-gray-500 text-center py-8">
                        Henüz alan seçilmedi
                      </p>
                    ) : (
                      config.fields.map(fieldId => (
                        <div
                          key={fieldId}
                          className="flex items-center justify-between p-2 border rounded bg-blue-50"
                        >
                          <span className="font-medium text-sm">{getFieldName(fieldId)}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeField(fieldId)}
                          >
                            <Trash2 className="w-4 h-4 text-red-600" />
                          </Button>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Filters Tab */}
          <TabsContent value="filters" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Filtreler</h3>
              <Button onClick={addFilter}>
                <Plus className="w-4 h-4 mr-2" />
                Filtre Ekle
              </Button>
            </div>
            
            <div className="space-y-3">
              {config.filters.map(filter => (
                <Card key={filter.id}>
                  <CardContent className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-3 items-end">
                      <div className="space-y-2">
                        <Label>Alan</Label>
                        <Select 
                          value={filter.field} 
                          onValueChange={(value) => updateFilter(filter.id, { field: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {availableFields.map(field => (
                              <SelectItem key={field.id} value={field.id}>
                                {field.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Operatör</Label>
                        <Select 
                          value={filter.operator} 
                          onValueChange={(value: any) => updateFilter(filter.id, { operator: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {operators.map(op => (
                              <SelectItem key={op.value} value={op.value}>
                                {op.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Değer</Label>
                        <Input
                          placeholder="Değer girin"
                          value={filter.value as string}
                          onChange={(e) => updateFilter(filter.id, { value: e.target.value })}
                        />
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFilter(filter.id)}
                      >
                        <Trash2 className="w-4 h-4 text-red-600" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {config.filters.length === 0 && (
                <p className="text-gray-500 text-center py-8">
                  Henüz filtre eklenmedi
                </p>
              )}
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Tarih Aralığı</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Başlangıç Tarihi</Label>
                    <Input
                      type="date"
                      value={config.dateRange.start}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        dateRange: { ...prev.dateRange, start: e.target.value }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Bitiş Tarihi</Label>
                    <Input
                      type="date"
                      value={config.dateRange.end}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        dateRange: { ...prev.dateRange, end: e.target.value }
                      }))}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Sıralama</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Sıralama Alanı</Label>
                    <Select 
                      value={config.sortBy || ''} 
                      onValueChange={(value) => setConfig(prev => ({ ...prev, sortBy: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sıralama alanı seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        {config.fields.map(fieldId => (
                          <SelectItem key={fieldId} value={fieldId}>
                            {getFieldName(fieldId)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Sıralama Yönü</Label>
                    <Select 
                      value={config.sortOrder} 
                      onValueChange={(value: any) => setConfig(prev => ({ ...prev, sortOrder: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="asc">Artan</SelectItem>
                        <SelectItem value="desc">Azalan</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            İptal
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handlePreview} disabled={config.fields.length === 0}>
              <Play className="w-4 h-4 mr-2" />
              Önizle
            </Button>
            <Button onClick={handleSave} disabled={!config.name || config.fields.length === 0}>
              <Save className="w-4 h-4 mr-2" />
              Kaydet
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
