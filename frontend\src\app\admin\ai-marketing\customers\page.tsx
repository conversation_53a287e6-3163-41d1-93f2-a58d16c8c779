'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Users,
  Search,
  Plus,
  Eye,
  Edit,
  Mail,
  Phone,
  MapPin,
  Building,
  TrendingUp,
  Target,
  MessageSquare,
  Calendar,
  Globe,
  Send,
  Save,
  X,
  ExternalLink,
  Star,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';

interface ProspectCustomer {
  id: string;
  companyName: string;
  industry: string;
  location: {
    country: string;
    city: string;
  };
  contactInfo: {
    email?: string;
    phone?: string;
    website?: string;
  };
  estimatedSize: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  potentialValue: number;
  leadScore: number;
  dataSource: 'google_maps' | 'linkedin' | 'trade_directory' | 'web_scraping';
  status: 'new' | 'contacted' | 'responded' | 'qualified' | 'converted' | 'rejected';
  lastContact?: Date;
  createdAt: Date;
}

interface ContactAttempt {
  id: string;
  prospectId: string;
  date: Date;
  method: 'email' | 'linkedin' | 'phone' | 'website_form';
  message: string;
  response?: string;
  status: 'sent' | 'delivered' | 'opened' | 'responded' | 'bounced' | 'failed';
}

export default function CustomerAcquisitionPage() {
  const [prospects, setProspects] = useState<ProspectCustomer[]>([]);
  const [contactAttempts, setContactAttempts] = useState<ContactAttempt[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  // Modal states
  const [selectedProspect, setSelectedProspect] = useState<ProspectCustomer | null>(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  // Contact history modal states
  const [selectedContactAttempt, setSelectedContactAttempt] = useState<ContactAttempt | null>(null);
  const [showContactDetailModal, setShowContactDetailModal] = useState(false);
  const [showReplyModal, setShowReplyModal] = useState(false);

  // Search modal states
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [searchInProgress, setSearchInProgress] = useState(false);
  const [searchProgress, setSearchProgress] = useState(0);
  const [searchResults, setSearchResults] = useState<ProspectCustomer[]>([]);
  const [searchConfig, setSearchConfig] = useState({
    countries: ['TR'],
    sectors: ['marble_warehouses', 'stone_importers'],
    companySize: ['medium', 'large'],
    maxResults: 100,
    minLeadScore: 60,
    dataSources: ['google_maps', 'linkedin', 'trade_directories'],
    customSector: '',
    customSectors: {} as Record<string, string>
  });

  // Manual add modal states
  const [showManualAddModal, setShowManualAddModal] = useState(false);
  const [manualAddForm, setManualAddForm] = useState({
    companyName: '',
    industry: 'construction',
    country: 'Turkey',
    city: '',
    email: '',
    phone: '',
    website: '',
    linkedin: '',
    companySize: 'medium',
    annualRevenue: '',
    employeeCount: '',
    notes: ''
  });
  const [isValidating, setIsValidating] = useState(false);
  const [validationResults, setValidationResults] = useState({
    email: null as boolean | null,
    website: null as boolean | null,
    duplicate: null as boolean | null
  });

  // Email form states
  const [emailSubject, setEmailSubject] = useState('');
  const [emailContent, setEmailContent] = useState('');
  const [emailLanguage, setEmailLanguage] = useState('tr');
  const [emailTemplate, setEmailTemplate] = useState('introduction');

  // Edit form states
  const [editForm, setEditForm] = useState({
    companyName: '',
    industry: '',
    estimatedSize: 'medium' as const,
    leadScore: 50,
    status: 'new' as const,
    notes: ''
  });

  useEffect(() => {
    fetchCustomerData();
  }, []);

  // Handler functions
  const handleViewProspect = (prospect: ProspectCustomer) => {
    setSelectedProspect(prospect);
    setShowViewModal(true);
  };

  const handleEmailProspect = (prospect: ProspectCustomer) => {
    setSelectedProspect(prospect);
    generateEmailContent(prospect);
    setShowEmailModal(true);
  };

  const handleEditProspect = (prospect: ProspectCustomer) => {
    setSelectedProspect(prospect);
    setEditForm({
      companyName: prospect.companyName,
      industry: prospect.industry,
      estimatedSize: prospect.estimatedSize,
      leadScore: prospect.leadScore,
      status: prospect.status,
      notes: ''
    });
    setShowEditModal(true);
  };

  const generateEmailContent = (prospect: ProspectCustomer) => {
    const templates = {
      introduction: {
        subject: `Türkiye'den Kaliteli Doğal Taş Ürünleri - ${prospect.companyName}`,
        content: `Merhaba ${prospect.companyName} ekibi,

Türkiye'nin önde gelen doğal taş üreticilerinden oluşan platformumuz aracılığıyla sizinle iletişime geçiyoruz.

${prospect.industry} sektöründeki deneyiminizi göz önünde bulundurarak, size özel fırsatlar sunmak istiyoruz:

• Premium kalitede mermer, traverten ve granit ürünleri
• Rekabetçi fiyatlarla direkt üretici bağlantısı
• Uluslararası kalite sertifikaları
• Hızlı ve güvenli teslimat seçenekleri

${prospect.location.country} pazarındaki ihtiyaçlarınızı karşılamak için hazırız.

Detaylı bilgi ve özel teklifler için yanıtlamanızı bekliyoruz.

Saygılarımızla,
Türkiye Doğal Taş Marketplace`
      },
      followup: {
        subject: `Takip: Doğal Taş Ürünleri Teklifimiz - ${prospect.companyName}`,
        content: `Merhaba,

Geçtiğimiz hafta gönderdiğimiz doğal taş ürünleri teklifimiz hakkında görüşlerinizi merak ediyoruz.

${prospect.companyName} için özel hazırladığımız katalog ve fiyat listesini inceleme fırsatınız oldu mu?

Sorularınızı yanıtlamak ve ihtiyaçlarınıza uygun çözümler sunmak için buradayız.

İyi çalışmalar,
Türkiye Doğal Taş Marketplace`
      }
    };

    const template = templates[emailTemplate as keyof typeof templates] || templates.introduction;
    setEmailSubject(template.subject);
    setEmailContent(template.content);
  };

  const handleSendEmail = async () => {
    if (!selectedProspect) return;

    try {
      // Email gönderme API çağrısı burada yapılacak
      console.log('Sending email to:', selectedProspect.contactInfo.email);
      console.log('Subject:', emailSubject);
      console.log('Content:', emailContent);

      // Mock email gönderimi
      const newContactAttempt: ContactAttempt = {
        id: Date.now().toString(),
        prospectId: selectedProspect.id,
        date: new Date(),
        method: 'email',
        message: emailContent,
        status: 'sent'
      };

      setContactAttempts(prev => [...prev, newContactAttempt]);

      // Prospect durumunu güncelle
      setProspects(prev => prev.map(p =>
        p.id === selectedProspect.id
          ? { ...p, status: 'contacted', lastContact: new Date() }
          : p
      ));

      setShowEmailModal(false);
      alert('Email başarıyla gönderildi!');
    } catch (error) {
      console.error('Email sending error:', error);
      alert('Email gönderilirken hata oluştu.');
    }
  };

  const handleSaveEdit = async () => {
    if (!selectedProspect) return;

    try {
      // Prospect güncelleme API çağrısı burada yapılacak
      const updatedProspect = {
        ...selectedProspect,
        ...editForm
      };

      setProspects(prev => prev.map(p =>
        p.id === selectedProspect.id ? updatedProspect : p
      ));

      setShowEditModal(false);
      alert('Müşteri bilgileri başarıyla güncellendi!');
    } catch (error) {
      console.error('Update error:', error);
      alert('Güncelleme sırasında hata oluştu.');
    }
  };

  // Contact history handlers
  const handleViewContactDetail = (attempt: ContactAttempt) => {
    setSelectedContactAttempt(attempt);
    setShowContactDetailModal(true);
  };

  const handleReplyToContact = (attempt: ContactAttempt) => {
    if (attempt.status !== 'responded') return;

    const prospect = prospects.find(p => p.id === attempt.prospectId);
    if (!prospect) return;

    setSelectedProspect(prospect);
    setSelectedContactAttempt(attempt);

    // Email içeriğini önceki iletişime göre hazırla
    const replySubject = `Re: ${attempt.message.split('\n')[0] || 'Doğal Taş Ürünleri'}`;
    const replyContent = `Merhaba,

${attempt.response ? `"${attempt.response.substring(0, 100)}..." mesajınız için teşekkür ederiz.` : ''}

Sorularınızı yanıtlamak ve size daha detaylı bilgi vermek için buradayız.

${prospect.industry} sektöründeki deneyiminizi göz önünde bulundurarak, size özel çözümler sunabiliriz.

Detaylı görüşme için uygun olduğunuz bir zaman dilimi var mı?

Saygılarımızla,
Türkiye Doğal Taş Marketplace`;

    setEmailSubject(replySubject);
    setEmailContent(replyContent);
    setShowReplyModal(true);
  };

  const handleSendReply = async () => {
    if (!selectedProspect || !selectedContactAttempt) return;

    try {
      // Reply email gönderme API çağrısı burada yapılacak
      console.log('Sending reply to:', selectedProspect.contactInfo.email);
      console.log('Subject:', emailSubject);
      console.log('Content:', emailContent);

      // Mock reply gönderimi
      const newContactAttempt: ContactAttempt = {
        id: Date.now().toString(),
        prospectId: selectedProspect.id,
        date: new Date(),
        method: 'email',
        message: emailContent,
        status: 'sent'
      };

      setContactAttempts(prev => [...prev, newContactAttempt]);

      // Prospect durumunu güncelle
      setProspects(prev => prev.map(p =>
        p.id === selectedProspect.id
          ? { ...p, status: 'contacted', lastContact: new Date() }
          : p
      ));

      setShowReplyModal(false);
      alert('Yanıt başarıyla gönderildi!');
    } catch (error) {
      console.error('Reply sending error:', error);
      alert('Yanıt gönderilirken hata oluştu.');
    }
  };

  // Search handlers
  const handleStartSearch = () => {
    setShowSearchModal(true);
  };

  const handleExecuteSearch = async () => {
    setSearchInProgress(true);
    setSearchProgress(0);
    setSearchResults([]);

    try {
      // Mock arama süreci
      const totalSteps = 5;
      const stepDuration = 2000; // 2 saniye per step

      for (let step = 1; step <= totalSteps; step++) {
        await new Promise(resolve => setTimeout(resolve, stepDuration));
        setSearchProgress((step / totalSteps) * 100);

        // Her adımda mock sonuçlar ekle
        if (step === 3) {
          const mockResults: ProspectCustomer[] = [
            {
              id: `search-${Date.now()}-1`,
              companyName: 'Anadolu İnşaat Ltd.',
              industry: 'construction',
              estimatedSize: 'medium',
              location: { city: 'İstanbul', country: 'Turkey' },
              contactInfo: {
                email: '<EMAIL>',
                phone: '+90 ************',
                website: 'anadoluinsaat.com'
              },
              leadScore: 78,
              potentialValue: 25000,
              status: 'new',
              dataSource: 'google_maps',
              lastContact: null
            },
            {
              id: `search-${Date.now()}-2`,
              companyName: 'Modern Mimarlık Atölyesi',
              industry: 'architecture',
              estimatedSize: 'small',
              location: { city: 'Ankara', country: 'Turkey' },
              contactInfo: {
                email: '<EMAIL>',
                website: 'modernmimarlik.com'
              },
              leadScore: 65,
              potentialValue: 15000,
              status: 'new',
              dataSource: 'linkedin',
              lastContact: null
            }
          ];
          setSearchResults(mockResults);
        }
      }

      alert(`Arama tamamlandı! ${searchResults.length} yeni müşteri bulundu.`);

      // Sonuçları ana listeye ekle
      setProspects(prev => [...prev, ...searchResults]);

    } catch (error) {
      console.error('Search error:', error);
      alert('Arama sırasında hata oluştu.');
    } finally {
      setSearchInProgress(false);
    }
  };

  const handleStopSearch = () => {
    setSearchInProgress(false);
    setSearchProgress(0);
    alert('Arama durduruldu.');
  };

  // Manual add handlers
  const handleManualAdd = () => {
    setShowManualAddModal(true);
  };

  const handleValidateForm = async () => {
    if (!manualAddForm.email && !manualAddForm.website) return;

    setIsValidating(true);
    try {
      // Mock validation
      await new Promise(resolve => setTimeout(resolve, 1000));

      setValidationResults({
        email: manualAddForm.email ? Math.random() > 0.2 : null, // 80% valid
        website: manualAddForm.website ? Math.random() > 0.3 : null, // 70% valid
        duplicate: Math.random() > 0.9 // 10% duplicate
      });
    } catch (error) {
      console.error('Validation error:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const calculateLeadScore = (form: typeof manualAddForm): number => {
    let score = 50; // Base score

    // Company size scoring
    const sizeScores = { small: 10, medium: 20, large: 30, enterprise: 40 };
    score += sizeScores[form.companySize as keyof typeof sizeScores] || 0;

    // Contact info scoring
    if (form.email) score += 10;
    if (form.phone) score += 5;
    if (form.website) score += 10;
    if (form.linkedin) score += 5;

    // Revenue scoring
    if (form.annualRevenue) {
      const revenue = parseInt(form.annualRevenue);
      if (revenue > 1000000) score += 15;
      else if (revenue > 500000) score += 10;
      else if (revenue > 100000) score += 5;
    }

    // Employee count scoring
    if (form.employeeCount) {
      const employees = parseInt(form.employeeCount);
      if (employees > 100) score += 10;
      else if (employees > 50) score += 7;
      else if (employees > 10) score += 5;
    }

    return Math.min(score, 100);
  };

  const handleSaveManualAdd = async () => {
    if (!manualAddForm.companyName || !manualAddForm.city) {
      alert('Şirket adı ve şehir alanları zorunludur.');
      return;
    }

    try {
      const leadScore = calculateLeadScore(manualAddForm);

      const newProspect: ProspectCustomer = {
        id: `manual-${Date.now()}`,
        companyName: manualAddForm.companyName,
        industry: manualAddForm.industry,
        estimatedSize: manualAddForm.companySize as 'small' | 'medium' | 'large' | 'enterprise',
        location: {
          city: manualAddForm.city,
          country: manualAddForm.country
        },
        contactInfo: {
          email: manualAddForm.email || undefined,
          phone: manualAddForm.phone || undefined,
          website: manualAddForm.website || undefined,
          linkedin: manualAddForm.linkedin || undefined
        },
        leadScore,
        potentialValue: leadScore * 500, // Rough calculation
        status: 'new',
        dataSource: 'manual',
        lastContact: null
      };

      setProspects(prev => [newProspect, ...prev]);

      // Reset form
      setManualAddForm({
        companyName: '',
        industry: 'construction',
        country: 'Turkey',
        city: '',
        email: '',
        phone: '',
        website: '',
        linkedin: '',
        companySize: 'medium',
        annualRevenue: '',
        employeeCount: '',
        notes: ''
      });

      setValidationResults({
        email: null,
        website: null,
        duplicate: null
      });

      setShowManualAddModal(false);
      alert('Müşteri başarıyla eklendi!');

    } catch (error) {
      console.error('Manual add error:', error);
      alert('Müşteri eklenirken hata oluştu.');
    }
  };

  const fetchCustomerData = async () => {
    setLoading(true);
    try {
      // Data will be loaded from API
      const mockProspects: ProspectCustomer[] = [];

      const mockContactAttempts: ContactAttempt[] = [
        {
          id: '1',
          prospectId: '1',
          date: new Date('2025-07-03T10:30:00'),
          method: 'email',
          message: 'Merhaba, Türkiye\'nin en kaliteli doğal taş ürünleri hakkında bilgi vermek istiyoruz...',
          status: 'delivered'
        },
        {
          id: '2',
          prospectId: '2',
          date: new Date('2025-07-02T14:15:00'),
          method: 'linkedin',
          message: 'LinkedIn üzerinden bağlantı kurma talebi ve kısa tanıtım mesajı',
          response: 'İlginiz için teşekkürler, detaylı bilgi almak istiyoruz.',
          status: 'responded'
        }
      ];

      setProspects(mockProspects);
      setContactAttempts(mockContactAttempts);
    } catch (error) {
      console.error('Error fetching customer data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'new':
        return <Badge variant="outline">Yeni</Badge>;
      case 'contacted':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">İletişim Kuruldu</Badge>;
      case 'responded':
        return <Badge variant="default" className="bg-green-100 text-green-800">Yanıt Alındı</Badge>;
      case 'qualified':
        return <Badge variant="default" className="bg-purple-100 text-purple-800">Nitelikli</Badge>;
      case 'converted':
        return <Badge variant="default" className="bg-green-100 text-green-800">Dönüştürüldü</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Reddedildi</Badge>;
      default:
        return <Badge variant="outline">Bilinmiyor</Badge>;
    }
  };

  const getSizeBadge = (size: string) => {
    const sizeLabels = {
      startup: 'Startup',
      small: 'Küçük',
      medium: 'Orta',
      large: 'Büyük',
      enterprise: 'Kurumsal'
    };
    return <Badge variant="secondary">{sizeLabels[size as keyof typeof sizeLabels]}</Badge>;
  };

  const getDataSourceIcon = (source: string) => {
    switch (source) {
      case 'google_maps':
        return '🗺️';
      case 'linkedin':
        return '💼';
      case 'trade_directory':
        return '📋';
      case 'web_scraping':
        return '🕷️';
      default:
        return '📊';
    }
  };

  const getContactMethodIcon = (method: string) => {
    switch (method) {
      case 'email':
        return <Mail className="w-4 h-4" />;
      case 'linkedin':
        return <Users className="w-4 h-4" />;
      case 'phone':
        return <Phone className="w-4 h-4" />;
      case 'website_form':
        return <Globe className="w-4 h-4" />;
      default:
        return <MessageSquare className="w-4 h-4" />;
    }
  };

  const filteredProspects = prospects.filter(prospect =>
    prospect.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    prospect.industry.toLowerCase().includes(searchTerm.toLowerCase()) ||
    prospect.location.country.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalProspects = prospects.length;
  const contactedProspects = prospects.filter(p => p.status !== 'new').length;
  const respondedProspects = prospects.filter(p => p.status === 'responded' || p.status === 'qualified').length;
  const avgLeadScore = prospects.length > 0 
    ? prospects.reduce((sum, p) => sum + p.leadScore, 0) / prospects.length
    : 0;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <span className="text-lg">Müşteri verileri yükleniyor...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Müşteri Arama AI</h1>
          <p className="text-gray-600 mt-1">
            AI ile potansiyel müşteri bulma ve otomatik iletişim kurma
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handleStartSearch}
            disabled={searchInProgress}
          >
            <Target className="w-4 h-4 mr-2" />
            {searchInProgress ? 'Arama Devam Ediyor...' : 'Arama Başlat'}
          </Button>
          <Button onClick={handleManualAdd}>
            <Plus className="w-4 h-4 mr-2" />
            Manuel Ekle
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Toplam Potansiyel</p>
                <p className="text-2xl font-bold">{totalProspects}</p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">İletişim Kurulan</p>
                <p className="text-2xl font-bold">{contactedProspects}</p>
              </div>
              <MessageSquare className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Yanıt Alınan</p>
                <p className="text-2xl font-bold">{respondedProspects}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Ortalama Skor</p>
                <p className="text-2xl font-bold">{avgLeadScore.toFixed(0)}</p>
              </div>
              <Target className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Tabs */}
      <Tabs defaultValue="prospects" className="space-y-4">
        <TabsList>
          <TabsTrigger value="prospects">Potansiyel Müşteriler</TabsTrigger>
          <TabsTrigger value="contacts">İletişim Geçmişi</TabsTrigger>
          <TabsTrigger value="analytics">Analitik</TabsTrigger>
          <TabsTrigger value="settings">AI Ayarları</TabsTrigger>
        </TabsList>

        <TabsContent value="prospects" className="space-y-4">
          {/* Search */}
          <div className="flex items-center space-x-2">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Şirket ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Prospects Table */}
          <Card>
            <CardHeader>
              <CardTitle>Potansiyel Müşteri Listesi</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Şirket</TableHead>
                    <TableHead>Sektör</TableHead>
                    <TableHead>Konum</TableHead>
                    <TableHead>Büyüklük</TableHead>
                    <TableHead>Potansiyel Değer</TableHead>
                    <TableHead>Lead Skoru</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead>Kaynak</TableHead>
                    <TableHead>İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredProspects.map((prospect) => (
                    <TableRow key={prospect.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{prospect.companyName}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            {prospect.contactInfo.email && (
                              <Mail className="w-3 h-3 text-gray-400" />
                            )}
                            {prospect.contactInfo.phone && (
                              <Phone className="w-3 h-3 text-gray-400" />
                            )}
                            {prospect.contactInfo.website && (
                              <Globe className="w-3 h-3 text-gray-400" />
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{prospect.industry}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-3 h-3 text-gray-400" />
                          <span className="text-sm">{prospect.location.city}, {prospect.location.country}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getSizeBadge(prospect.estimatedSize)}
                      </TableCell>
                      <TableCell className="font-medium">
                        ${prospect.potentialValue.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{prospect.leadScore}</span>
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${prospect.leadScore}%` }}
                            ></div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(prospect.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <span className="text-lg">{getDataSourceIcon(prospect.dataSource)}</span>
                          <span className="text-xs text-gray-500 capitalize">
                            {prospect.dataSource.replace('_', ' ')}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewProspect(prospect)}
                            title="Detayları Görüntüle"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEmailProspect(prospect)}
                            title="Email Gönder"
                            disabled={!prospect.contactInfo.email}
                          >
                            <Mail className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditProspect(prospect)}
                            title="Düzenle"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contacts" className="space-y-4">
          {/* Contact Attempts Table */}
          <Card>
            <CardHeader>
              <CardTitle>İletişim Geçmişi</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[140px]">Tarih</TableHead>
                      <TableHead className="w-[200px]">Şirket</TableHead>
                      <TableHead className="w-[120px]">Yöntem</TableHead>
                      <TableHead className="w-[300px]">Mesaj</TableHead>
                      <TableHead className="w-[100px]">Durum</TableHead>
                      <TableHead className="w-[300px]">Yanıt</TableHead>
                      <TableHead className="w-[80px]">İşlemler</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {contactAttempts.map((attempt) => {
                      const prospect = prospects.find(p => p.id === attempt.prospectId);
                      return (
                        <TableRow key={attempt.id}>
                          <TableCell className="text-sm">
                            {attempt.date.toLocaleDateString('tr-TR')}
                            <br />
                            <span className="text-xs text-gray-500">
                              {attempt.date.toLocaleTimeString('tr-TR', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                          </TableCell>
                          <TableCell className="font-medium">
                            {prospect?.companyName}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {getContactMethodIcon(attempt.method)}
                              <span className="capitalize text-sm">
                                {attempt.method === 'email' ? 'Email' :
                                 attempt.method === 'linkedin' ? 'LinkedIn' :
                                 attempt.method === 'phone' ? 'Telefon' :
                                 attempt.method === 'website_form' ? 'Web Form' : attempt.method}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-2">
                              <div className="text-sm leading-relaxed">
                                {attempt.message.length > 100 ? (
                                  <>
                                    <span>{attempt.message.substring(0, 100)}...</span>
                                    <Button
                                      variant="link"
                                      size="sm"
                                      className="p-0 h-auto text-blue-600 ml-1"
                                      onClick={() => {
                                        // Modal açma fonksiyonu buraya eklenecek
                                        alert(`Tam Mesaj:\n\n${attempt.message}`);
                                      }}
                                    >
                                      Devamını Oku
                                    </Button>
                                  </>
                                ) : (
                                  attempt.message
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={attempt.status === 'responded' ? 'default' : 'secondary'}>
                              {attempt.status === 'responded' ? 'Yanıtlandı' :
                               attempt.status === 'delivered' ? 'Teslim Edildi' :
                               attempt.status === 'opened' ? 'Açıldı' :
                               attempt.status === 'sent' ? 'Gönderildi' : 'Bilinmiyor'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {attempt.response ? (
                              <div className="space-y-2">
                                <div className="text-sm text-green-700 leading-relaxed bg-green-50 p-2 rounded">
                                  {attempt.response.length > 100 ? (
                                    <>
                                      <span>{attempt.response.substring(0, 100)}...</span>
                                      <Button
                                        variant="link"
                                        size="sm"
                                        className="p-0 h-auto text-green-600 ml-1"
                                        onClick={() => {
                                          // Modal açma fonksiyonu buraya eklenecek
                                          alert(`Tam Yanıt:\n\n${attempt.response}`);
                                        }}
                                      >
                                        Devamını Oku
                                      </Button>
                                    </>
                                  ) : (
                                    attempt.response
                                  )}
                                </div>
                              </div>
                            ) : (
                              <span className="text-gray-400 text-sm">Yanıt bekleniyor</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                title="Detayları Görüntüle"
                                onClick={() => handleViewContactDetail(attempt)}
                              >
                                <Eye className="w-3 h-3" />
                              </Button>
                              {attempt.status === 'responded' && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  title="Yanıtla"
                                  onClick={() => handleReplyToContact(attempt)}
                                >
                                  <Mail className="w-3 h-3" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          {/* Overview KPIs */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Toplam Müşteri</p>
                    <p className="text-2xl font-bold text-blue-600">{prospects.length}</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
                <p className="text-xs text-blue-600 mt-1">+{prospects.filter(p => p.dataSource === 'manual').length} manuel eklenen</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">İletişim Oranı</p>
                    <p className="text-2xl font-bold text-green-600">
                      {prospects.length > 0 ? Math.round((contactAttempts.length / prospects.length) * 100) : 0}%
                    </p>
                  </div>
                  <MessageSquare className="w-8 h-8 text-green-600" />
                </div>
                <p className="text-xs text-green-600 mt-1">{contactAttempts.length} toplam iletişim</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Yanıt Oranı</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {contactAttempts.length > 0 ? Math.round((contactAttempts.filter(c => c.status === 'responded').length / contactAttempts.length) * 100) : 0}%
                    </p>
                  </div>
                  <Mail className="w-8 h-8 text-purple-600" />
                </div>
                <p className="text-xs text-purple-600 mt-1">{contactAttempts.filter(c => c.status === 'responded').length} yanıt alındı</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Ortalama Lead Skoru</p>
                    <p className="text-2xl font-bold text-orange-600">
                      {prospects.length > 0 ? Math.round(prospects.reduce((sum, p) => sum + p.leadScore, 0) / prospects.length) : 0}
                    </p>
                  </div>
                  <Star className="w-8 h-8 text-orange-600" />
                </div>
                <p className="text-xs text-orange-600 mt-1">100 üzerinden</p>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Kaynak Bazlı Analiz */}
            <Card>
              <CardHeader>
                <CardTitle>Kaynak Bazlı Dağılım</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['google_maps', 'linkedin', 'trade_directories', 'manual'].map(source => {
                    const count = prospects.filter(p => p.dataSource === source).length;
                    const percentage = prospects.length > 0 ? (count / prospects.length) * 100 : 0;
                    const sourceName = {
                      google_maps: 'Google Maps',
                      linkedin: 'LinkedIn',
                      trade_directories: 'Trade Directories',
                      manual: 'Manuel Eklenen'
                    }[source];

                    return (
                      <div key={source} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>{sourceName}</span>
                          <span className="font-medium">{count} ({percentage.toFixed(1)}%)</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Sektörel Dağılım */}
            <Card>
              <CardHeader>
                <CardTitle>Sektörel Dağılım</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['construction', 'architecture', 'interior_design', 'landscaping', 'stone_processing', 'monument_makers', 'real_estate', 'hospitality'].map(industry => {
                    const count = prospects.filter(p => p.industry === industry).length;
                    const percentage = prospects.length > 0 ? (count / prospects.length) * 100 : 0;
                    const industryName = {
                      construction: 'İnşaat Firmaları',
                      architecture: 'Mimarlık Büroları',
                      interior_design: 'İç Mimarlık',
                      landscaping: 'Peyzaj Mimarlığı',
                      stone_processing: 'Taş İşleme Atölyeleri',
                      monument_makers: 'Anıt/Mezar Taşı',
                      real_estate: 'Gayrimenkul Geliştirme',
                      hospitality: 'Otel & Restoran'
                    }[industry] || industry;

                    return (
                      <div key={industry} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>{industryName}</span>
                          <span className="font-medium">{count} ({percentage.toFixed(1)}%)</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Coğrafi Dağılım */}
            <Card>
              <CardHeader>
                <CardTitle>Coğrafi Dağılım</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Array.from(new Set(prospects.map(p => p.location.country))).map(country => {
                    const count = prospects.filter(p => p.location.country === country).length;
                    const percentage = prospects.length > 0 ? (count / prospects.length) * 100 : 0;

                    return (
                      <div key={country} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>{country}</span>
                          <span className="font-medium">{count} ({percentage.toFixed(1)}%)</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Lead Skoru Dağılımı */}
            <Card>
              <CardHeader>
                <CardTitle>Lead Skoru Dağılımı</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { range: '80-100', label: 'Yüksek (80-100)', color: 'bg-green-600' },
                    { range: '60-79', label: 'Orta (60-79)', color: 'bg-yellow-600' },
                    { range: '40-59', label: 'Düşük (40-59)', color: 'bg-orange-600' },
                    { range: '0-39', label: 'Çok Düşük (0-39)', color: 'bg-red-600' }
                  ].map(scoreRange => {
                    const [min, max] = scoreRange.range.split('-').map(Number);
                    const count = prospects.filter(p => p.leadScore >= min && p.leadScore <= max).length;
                    const percentage = prospects.length > 0 ? (count / prospects.length) * 100 : 0;

                    return (
                      <div key={scoreRange.range} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>{scoreRange.label}</span>
                          <span className="font-medium">{count} ({percentage.toFixed(1)}%)</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`${scoreRange.color} h-2 rounded-full transition-all duration-300`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* İletişim Performansı */}
          <Card>
            <CardHeader>
              <CardTitle>İletişim Performansı</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {['email', 'linkedin', 'phone'].map(method => {
                  const methodAttempts = contactAttempts.filter(c => c.method === method);
                  const responseCount = methodAttempts.filter(c => c.status === 'responded').length;
                  const responseRate = methodAttempts.length > 0 ? (responseCount / methodAttempts.length) * 100 : 0;
                  const methodName = {
                    email: 'Email',
                    linkedin: 'LinkedIn',
                    phone: 'Telefon'
                  }[method];

                  return (
                    <div key={method} className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-blue-600 mb-2">
                        {responseRate.toFixed(1)}%
                      </div>
                      <div className="text-sm font-medium mb-1">{methodName}</div>
                      <div className="text-xs text-gray-500">
                        {responseCount}/{methodAttempts.length} yanıt
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${responseRate}%` }}
                        ></div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* AI Settings Tab */}
        <TabsContent value="settings" className="space-y-4">
          {/* Müşteri Arama AI Ayarları */}
          <Card>
            <CardHeader>
              <CardTitle>Müşteri Arama AI Ayarları</CardTitle>
              <p className="text-sm text-gray-600">
                Otomatik müşteri arama sisteminin davranışını kontrol edin
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>Günlük Arama Limiti</Label>
                  <div className="mt-2">
                    <input
                      type="range"
                      min="50"
                      max="500"
                      step="50"
                      defaultValue="100"
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>50</span>
                      <span>100 (Mevcut)</span>
                      <span>500</span>
                    </div>
                  </div>
                </div>
                <div>
                  <Label>Minimum Lead Skoru</Label>
                  <div className="mt-2">
                    <input
                      type="range"
                      min="30"
                      max="90"
                      step="10"
                      defaultValue="60"
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>30</span>
                      <span>60 (Mevcut)</span>
                      <span>90</span>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <Label>Arama Derinliği</Label>
                <Select defaultValue="medium">
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="shallow">Yüzeysel (Hızlı)</SelectItem>
                    <SelectItem value="medium">Orta (Dengeli)</SelectItem>
                    <SelectItem value="deep">Derin (Kapsamlı)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Aktif Veri Kaynakları</Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                  {[
                    { id: 'google_maps', name: 'Google Maps', enabled: true },
                    { id: 'linkedin', name: 'LinkedIn', enabled: true },
                    { id: 'trade_directories', name: 'Trade Directories', enabled: false }
                  ].map(source => (
                    <label key={source.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        defaultChecked={source.enabled}
                        className="rounded"
                      />
                      <span className="text-sm">{source.name}</span>
                    </label>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* İletişim AI Ayarları */}
          <Card>
            <CardHeader>
              <CardTitle>İletişim AI Ayarları</CardTitle>
              <p className="text-sm text-gray-600">
                Otomatik iletişim sisteminin davranışını kontrol edin
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>Günlük Email Limiti</Label>
                  <div className="mt-2">
                    <input
                      type="range"
                      min="10"
                      max="100"
                      step="10"
                      defaultValue="30"
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>10</span>
                      <span>30 (Mevcut)</span>
                      <span>100</span>
                    </div>
                  </div>
                </div>
                <div>
                  <Label>İletişim Aralığı (Saat)</Label>
                  <div className="mt-2">
                    <input
                      type="range"
                      min="1"
                      max="24"
                      step="1"
                      defaultValue="4"
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>1</span>
                      <span>4 (Mevcut)</span>
                      <span>24</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>Mesaj Tonu</Label>
                  <Select defaultValue="professional">
                    <SelectTrigger className="mt-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="professional">Profesyonel</SelectItem>
                      <SelectItem value="friendly">Samimi</SelectItem>
                      <SelectItem value="sales">Satış Odaklı</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Kişiselleştirme Seviyesi</Label>
                  <Select defaultValue="medium">
                    <SelectTrigger className="mt-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Düşük</SelectItem>
                      <SelectItem value="medium">Orta</SelectItem>
                      <SelectItem value="high">Yüksek</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Otomatik İletişim</Label>
                <div className="flex items-center space-x-2 mt-2">
                  <input
                    type="checkbox"
                    defaultChecked={true}
                    className="rounded"
                  />
                  <span className="text-sm">Yeni müşteriler bulunduğunda otomatik email gönder</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Lead Skorlama Ayarları */}
          <Card>
            <CardHeader>
              <CardTitle>Lead Skorlama Ayarları</CardTitle>
              <p className="text-sm text-gray-600">
                AI'ın müşteri skorlamasında kullandığı kriterlerin ağırlıklarını ayarlayın
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {[
                { name: 'Şirket Büyüklüğü', current: 30, min: 20, max: 40 },
                { name: 'Web Sitesi Kalitesi', current: 20, min: 10, max: 30 },
                { name: 'Sosyal Medya Varlığı', current: 15, min: 5, max: 20 },
                { name: 'Sektörel Uygunluk', current: 25, min: 15, max: 35 },
                { name: 'Coğrafi Konum', current: 10, min: 5, max: 15 }
              ].map(criteria => (
                <div key={criteria.name}>
                  <div className="flex justify-between mb-2">
                    <Label>{criteria.name}</Label>
                    <span className="text-sm text-gray-600">%{criteria.current}</span>
                  </div>
                  <input
                    type="range"
                    min={criteria.min}
                    max={criteria.max}
                    step="5"
                    defaultValue={criteria.current}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>%{criteria.min}</span>
                    <span>%{criteria.max}</span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Sistem Performans Ayarları */}
          <Card>
            <CardHeader>
              <CardTitle>Sistem Performans Ayarları</CardTitle>
              <p className="text-sm text-gray-600">
                AI sisteminin kaynak kullanımını ve performansını kontrol edin
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>Eşzamanlı İşlem Limiti</Label>
                  <Select defaultValue="5">
                    <SelectTrigger className="mt-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="3">3 İşlem</SelectItem>
                      <SelectItem value="5">5 İşlem</SelectItem>
                      <SelectItem value="10">10 İşlem</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Hata Yeniden Deneme</Label>
                  <Select defaultValue="3">
                    <SelectTrigger className="mt-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 Kez</SelectItem>
                      <SelectItem value="3">3 Kez</SelectItem>
                      <SelectItem value="5">5 Kez</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Veri Saklama Süresi</Label>
                <Select defaultValue="90">
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 Gün</SelectItem>
                    <SelectItem value="90">90 Gün</SelectItem>
                    <SelectItem value="365">1 Yıl</SelectItem>
                    <SelectItem value="unlimited">Sınırsız</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button>
              <Save className="w-4 h-4 mr-2" />
              Ayarları Kaydet
            </Button>
          </div>
        </TabsContent>
      </Tabs>

      {/* View Prospect Modal */}
      <Dialog open={showViewModal} onOpenChange={setShowViewModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Building className="w-5 h-5" />
              <span>{selectedProspect?.companyName} - Detaylı Görünüm</span>
            </DialogTitle>
          </DialogHeader>

          {selectedProspect && (
            <div className="space-y-6">
              {/* Genel Bilgiler */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Genel Bilgiler</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Şirket Adı</Label>
                      <p className="font-medium">{selectedProspect.companyName}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Sektör</Label>
                      <p>{selectedProspect.industry}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Şirket Büyüklüğü</Label>
                      <p>{getSizeBadge(selectedProspect.estimatedSize)}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Konum</Label>
                      <p className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <span>{selectedProspect.location.city}, {selectedProspect.location.country}</span>
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">İletişim Bilgileri</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {selectedProspect.contactInfo.email && (
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Email</Label>
                        <p className="flex items-center space-x-2">
                          <Mail className="w-4 h-4 text-gray-400" />
                          <span>{selectedProspect.contactInfo.email}</span>
                        </p>
                      </div>
                    )}
                    {selectedProspect.contactInfo.phone && (
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Telefon</Label>
                        <p className="flex items-center space-x-2">
                          <Phone className="w-4 h-4 text-gray-400" />
                          <span>{selectedProspect.contactInfo.phone}</span>
                        </p>
                      </div>
                    )}
                    {selectedProspect.contactInfo.website && (
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Web Sitesi</Label>
                        <p className="flex items-center space-x-2">
                          <Globe className="w-4 h-4 text-gray-400" />
                          <a
                            href={`https://${selectedProspect.contactInfo.website}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline flex items-center space-x-1"
                          >
                            <span>{selectedProspect.contactInfo.website}</span>
                            <ExternalLink className="w-3 h-3" />
                          </a>
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* AI Analizi */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">AI Analizi</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="flex items-center justify-center space-x-2 mb-2">
                        <Star className="w-5 h-5 text-blue-600" />
                        <span className="font-medium">Lead Skoru</span>
                      </div>
                      <div className="text-2xl font-bold text-blue-600">{selectedProspect.leadScore}</div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${selectedProspect.leadScore}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="flex items-center justify-center space-x-2 mb-2">
                        <TrendingUp className="w-5 h-5 text-green-600" />
                        <span className="font-medium">Potansiyel Değer</span>
                      </div>
                      <div className="text-2xl font-bold text-green-600">
                        ${selectedProspect.potentialValue.toLocaleString()}
                      </div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="flex items-center justify-center space-x-2 mb-2">
                        <Target className="w-5 h-5 text-purple-600" />
                        <span className="font-medium">Durum</span>
                      </div>
                      <div className="mt-2">
                        {getStatusBadge(selectedProspect.status)}
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-600">Veri Kaynağı</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-lg">{getDataSourceIcon(selectedProspect.dataSource)}</span>
                      <span className="capitalize">{selectedProspect.dataSource.replace('_', ' ')}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* İletişim Geçmişi */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">İletişim Geçmişi</CardTitle>
                </CardHeader>
                <CardContent>
                  {contactAttempts.filter(attempt => attempt.prospectId === selectedProspect.id).length > 0 ? (
                    <div className="space-y-3">
                      {contactAttempts
                        .filter(attempt => attempt.prospectId === selectedProspect.id)
                        .map(attempt => (
                          <div key={attempt.id} className="border-l-4 border-blue-200 pl-4 py-2">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center space-x-2">
                                {getContactMethodIcon(attempt.method)}
                                <span className="font-medium capitalize">{attempt.method}</span>
                                <Badge variant="secondary">{attempt.status}</Badge>
                              </div>
                              <span className="text-sm text-gray-500">
                                {attempt.date.toLocaleString('tr-TR')}
                              </span>
                            </div>
                            <p className="text-sm text-gray-700 mb-2">{attempt.message}</p>
                            {attempt.response && (
                              <div className="bg-green-50 p-2 rounded text-sm text-green-700">
                                <strong>Yanıt:</strong> {attempt.response}
                              </div>
                            )}
                          </div>
                        ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">Henüz iletişim geçmişi bulunmuyor.</p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Email Modal */}
      <Dialog open={showEmailModal} onOpenChange={setShowEmailModal}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Mail className="w-5 h-5" />
              <span>Email Gönder - {selectedProspect?.companyName}</span>
            </DialogTitle>
            <DialogDescription>
              AI destekli email kompozisyonu ile kişiselleştirilmiş mesaj gönderin.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Email Template Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="template">Email Şablonu</Label>
                <Select value={emailTemplate} onValueChange={setEmailTemplate}>
                  <SelectTrigger>
                    <SelectValue placeholder="Şablon seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="introduction">Tanıtım Emaili</SelectItem>
                    <SelectItem value="followup">Takip Emaili</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="language">Dil</Label>
                <Select value={emailLanguage} onValueChange={setEmailLanguage}>
                  <SelectTrigger>
                    <SelectValue placeholder="Dil seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tr">Türkçe</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="de">Deutsch</SelectItem>
                    <SelectItem value="fr">Français</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Email Subject */}
            <div>
              <Label htmlFor="subject">Konu</Label>
              <Input
                id="subject"
                value={emailSubject}
                onChange={(e) => setEmailSubject(e.target.value)}
                placeholder="Email konusu"
              />
            </div>

            {/* Email Content */}
            <div>
              <Label htmlFor="content">Mesaj İçeriği</Label>
              <Textarea
                id="content"
                value={emailContent}
                onChange={(e) => setEmailContent(e.target.value)}
                placeholder="Email içeriği"
                rows={12}
                className="resize-none"
              />
            </div>

            {/* Recipient Info */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <Label className="text-sm font-medium text-gray-600">Alıcı Bilgileri</Label>
              <div className="mt-2 space-y-1">
                <p className="text-sm"><strong>Şirket:</strong> {selectedProspect?.companyName}</p>
                <p className="text-sm"><strong>Email:</strong> {selectedProspect?.contactInfo.email}</p>
                <p className="text-sm"><strong>Sektör:</strong> {selectedProspect?.industry}</p>
                <p className="text-sm"><strong>Konum:</strong> {selectedProspect?.location.city}, {selectedProspect?.location.country}</p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4">
              <Button
                variant="outline"
                onClick={() => selectedProspect && generateEmailContent(selectedProspect)}
              >
                <Target className="w-4 h-4 mr-2" />
                AI ile Yeniden Oluştur
              </Button>
              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={() => setShowEmailModal(false)}>
                  <X className="w-4 h-4 mr-2" />
                  İptal
                </Button>
                <Button onClick={handleSendEmail}>
                  <Send className="w-4 h-4 mr-2" />
                  Email Gönder
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Edit className="w-5 h-5" />
              <span>Müşteri Düzenle - {selectedProspect?.companyName}</span>
            </DialogTitle>
            <DialogDescription>
              Müşteri bilgilerini güncelleyin ve lead skorunu ayarlayın.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Company Name */}
            <div>
              <Label htmlFor="companyName">Şirket Adı</Label>
              <Input
                id="companyName"
                value={editForm.companyName}
                onChange={(e) => setEditForm(prev => ({ ...prev, companyName: e.target.value }))}
                placeholder="Şirket adı"
              />
            </div>

            {/* Industry */}
            <div>
              <Label htmlFor="industry">Sektör</Label>
              <Input
                id="industry"
                value={editForm.industry}
                onChange={(e) => setEditForm(prev => ({ ...prev, industry: e.target.value }))}
                placeholder="Sektör"
              />
            </div>

            {/* Company Size and Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="size">Şirket Büyüklüğü</Label>
                <Select
                  value={editForm.estimatedSize}
                  onValueChange={(value: any) => setEditForm(prev => ({ ...prev, estimatedSize: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Büyüklük seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="startup">Startup</SelectItem>
                    <SelectItem value="small">Küçük</SelectItem>
                    <SelectItem value="medium">Orta</SelectItem>
                    <SelectItem value="large">Büyük</SelectItem>
                    <SelectItem value="enterprise">Kurumsal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="status">Durum</Label>
                <Select
                  value={editForm.status}
                  onValueChange={(value: any) => setEditForm(prev => ({ ...prev, status: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Durum seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">Yeni</SelectItem>
                    <SelectItem value="contacted">İletişim Kuruldu</SelectItem>
                    <SelectItem value="responded">Yanıt Alındı</SelectItem>
                    <SelectItem value="qualified">Nitelikli</SelectItem>
                    <SelectItem value="converted">Dönüştürüldü</SelectItem>
                    <SelectItem value="rejected">Reddedildi</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Lead Score */}
            <div>
              <Label htmlFor="leadScore">Lead Skoru ({editForm.leadScore})</Label>
              <input
                type="range"
                id="leadScore"
                min="0"
                max="100"
                value={editForm.leadScore}
                onChange={(e) => setEditForm(prev => ({ ...prev, leadScore: parseInt(e.target.value) }))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>0</span>
                <span>50</span>
                <span>100</span>
              </div>
            </div>

            {/* Notes */}
            <div>
              <Label htmlFor="notes">Notlar</Label>
              <Textarea
                id="notes"
                value={editForm.notes}
                onChange={(e) => setEditForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Müşteri hakkında notlar..."
                rows={4}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setShowEditModal(false)}>
                <X className="w-4 h-4 mr-2" />
                İptal
              </Button>
              <Button onClick={handleSaveEdit}>
                <Save className="w-4 h-4 mr-2" />
                Kaydet
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Contact Detail Modal */}
      <Dialog open={showContactDetailModal} onOpenChange={setShowContactDetailModal}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <MessageSquare className="w-5 h-5" />
              <span>İletişim Detayları</span>
            </DialogTitle>
            <DialogDescription>
              İletişim girişiminin tam detayları ve geçmişi
            </DialogDescription>
          </DialogHeader>

          {selectedContactAttempt && (
            <div className="space-y-6">
              {/* İletişim Bilgileri */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">İletişim Bilgileri</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Tarih ve Saat</Label>
                      <p className="font-medium">{selectedContactAttempt.date.toLocaleString('tr-TR')}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">İletişim Yöntemi</Label>
                      <div className="flex items-center space-x-2">
                        {getContactMethodIcon(selectedContactAttempt.method)}
                        <span className="capitalize font-medium">
                          {selectedContactAttempt.method === 'email' ? 'Email' :
                           selectedContactAttempt.method === 'linkedin' ? 'LinkedIn' :
                           selectedContactAttempt.method === 'phone' ? 'Telefon' :
                           selectedContactAttempt.method === 'website_form' ? 'Web Form' : selectedContactAttempt.method}
                        </span>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Durum</Label>
                      <Badge variant={selectedContactAttempt.status === 'responded' ? 'default' : 'secondary'}>
                        {selectedContactAttempt.status === 'responded' ? 'Yanıtlandı' :
                         selectedContactAttempt.status === 'delivered' ? 'Teslim Edildi' :
                         selectedContactAttempt.status === 'opened' ? 'Açıldı' :
                         selectedContactAttempt.status === 'sent' ? 'Gönderildi' : 'Bilinmiyor'}
                      </Badge>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Müşteri</Label>
                      <p className="font-medium">
                        {prospects.find(p => p.id === selectedContactAttempt.prospectId)?.companyName}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Gönderilen Mesaj */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Gönderilen Mesaj</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <pre className="whitespace-pre-wrap text-sm text-blue-900 font-medium">
                      {selectedContactAttempt.message}
                    </pre>
                  </div>
                </CardContent>
              </Card>

              {/* Alınan Yanıt */}
              {selectedContactAttempt.response && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Alınan Yanıt</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <pre className="whitespace-pre-wrap text-sm text-green-900 font-medium">
                        {selectedContactAttempt.response}
                      </pre>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* AI Önerileri */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">AI Takip Önerileri</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {selectedContactAttempt.status === 'responded' ? (
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 text-green-600">
                          <CheckCircle className="w-4 h-4" />
                          <span className="font-medium">Pozitif yanıt alındı</span>
                        </div>
                        <ul className="list-disc list-inside text-sm text-gray-700 space-y-1 ml-6">
                          <li>24 saat içinde detaylı bilgi emaili gönder</li>
                          <li>Ürün katalogu ve fiyat listesi paylaş</li>
                          <li>Video call toplantısı öner</li>
                          <li>Referans projeler göster</li>
                        </ul>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 text-orange-600">
                          <Clock className="w-4 h-4" />
                          <span className="font-medium">Yanıt bekleniyor</span>
                        </div>
                        <ul className="list-disc list-inside text-sm text-gray-700 space-y-1 ml-6">
                          <li>3 gün sonra takip emaili gönder</li>
                          <li>Farklı iletişim kanalı dene (LinkedIn)</li>
                          <li>Mesaj tonunu değiştir</li>
                          <li>Özel teklif sun</li>
                        </ul>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="flex items-center justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={() => setShowContactDetailModal(false)}>
                  <X className="w-4 h-4 mr-2" />
                  Kapat
                </Button>
                {selectedContactAttempt.status === 'responded' && (
                  <Button onClick={() => {
                    setShowContactDetailModal(false);
                    handleReplyToContact(selectedContactAttempt);
                  }}>
                    <Mail className="w-4 h-4 mr-2" />
                    Yanıtla
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Reply Modal */}
      <Dialog open={showReplyModal} onOpenChange={setShowReplyModal}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Mail className="w-5 h-5" />
              <span>Yanıt Gönder - {selectedProspect?.companyName}</span>
            </DialogTitle>
            <DialogDescription>
              Müşteri yanıtına karşılık email gönder
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Önceki İletişim Özeti */}
            {selectedContactAttempt && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Önceki İletişim</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <strong>Gönderilen:</strong>
                    <p className="mt-1">{selectedContactAttempt.message.substring(0, 200)}...</p>
                  </div>
                  {selectedContactAttempt.response && (
                    <div className="bg-green-50 p-3 rounded text-sm">
                      <strong>Yanıt:</strong>
                      <p className="mt-1 text-green-800">{selectedContactAttempt.response}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Email Subject */}
            <div>
              <Label htmlFor="replySubject">Konu</Label>
              <Input
                id="replySubject"
                value={emailSubject}
                onChange={(e) => setEmailSubject(e.target.value)}
                placeholder="Email konusu"
              />
            </div>

            {/* Email Content */}
            <div>
              <Label htmlFor="replyContent">Yanıt Mesajı</Label>
              <Textarea
                id="replyContent"
                value={emailContent}
                onChange={(e) => setEmailContent(e.target.value)}
                placeholder="Yanıt mesajı içeriği"
                rows={10}
                className="resize-none"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setShowReplyModal(false)}>
                <X className="w-4 h-4 mr-2" />
                İptal
              </Button>
              <Button onClick={handleSendReply}>
                <Send className="w-4 h-4 mr-2" />
                Yanıt Gönder
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Search Modal */}
      <Dialog open={showSearchModal} onOpenChange={setShowSearchModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Target className="w-5 h-5" />
              <span>AI Müşteri Arama Sistemi</span>
            </DialogTitle>
            <DialogDescription>
              Otomatik müşteri arama parametrelerini ayarlayın ve aramayı başlatın
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {!searchInProgress ? (
              <>
                {/* Arama Konfigürasyonu */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Hedef Ülkeler */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Hedef Ülkeler</CardTitle>
                      <p className="text-sm text-gray-600">
                        Doğal taş ithalatı yapan ülkeleri seçin
                      </p>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {/* Ana Doğal Taş İthalatçısı Ülkeler */}
                        <div>
                          <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            Ana Doğal Taş İthalatçısı Ülkeler
                          </Label>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {[
                              { code: 'US', name: 'Amerika Birleşik Devletleri', flag: '🇺🇸' },
                              { code: 'DE', name: 'Almanya', flag: '🇩🇪' },
                              { code: 'IT', name: 'İtalya', flag: '🇮🇹' },
                              { code: 'FR', name: 'Fransa', flag: '🇫🇷' },
                              { code: 'GB', name: 'İngiltere', flag: '🇬🇧' },
                              { code: 'ES', name: 'İspanya', flag: '🇪🇸' },
                              { code: 'NL', name: 'Hollanda', flag: '🇳🇱' },
                              { code: 'BE', name: 'Belçika', flag: '🇧🇪' },
                              { code: 'CH', name: 'İsviçre', flag: '🇨🇭' },
                              { code: 'AT', name: 'Avusturya', flag: '🇦🇹' },
                              { code: 'SE', name: 'İsveç', flag: '🇸🇪' },
                              { code: 'NO', name: 'Norveç', flag: '🇳🇴' }
                            ].map(country => (
                              <label key={country.code} className="flex items-center space-x-2 p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={searchConfig.countries.includes(country.code)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        countries: [...prev.countries, country.code]
                                      }));
                                    } else {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        countries: prev.countries.filter(c => c !== country.code)
                                      }));
                                    }
                                  }}
                                  className="rounded"
                                />
                                <span className="text-lg">{country.flag}</span>
                                <span className="text-sm">{country.name}</span>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* Orta Doğu ve Körfez Ülkeleri */}
                        <div>
                          <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            Orta Doğu ve Körfez Ülkeleri
                          </Label>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {[
                              { code: 'AE', name: 'Birleşik Arap Emirlikleri', flag: '🇦🇪' },
                              { code: 'SA', name: 'Suudi Arabistan', flag: '🇸🇦' },
                              { code: 'QA', name: 'Katar', flag: '🇶🇦' },
                              { code: 'KW', name: 'Kuveyt', flag: '🇰🇼' },
                              { code: 'BH', name: 'Bahreyn', flag: '🇧🇭' },
                              { code: 'OM', name: 'Umman', flag: '🇴🇲' },
                              { code: 'JO', name: 'Ürdün', flag: '🇯🇴' },
                              { code: 'LB', name: 'Lübnan', flag: '🇱🇧' },
                              { code: 'IL', name: 'İsrail', flag: '🇮🇱' }
                            ].map(country => (
                              <label key={country.code} className="flex items-center space-x-2 p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={searchConfig.countries.includes(country.code)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        countries: [...prev.countries, country.code]
                                      }));
                                    } else {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        countries: prev.countries.filter(c => c !== country.code)
                                      }));
                                    }
                                  }}
                                  className="rounded"
                                />
                                <span className="text-lg">{country.flag}</span>
                                <span className="text-sm">{country.name}</span>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* Asya-Pasifik Ülkeleri */}
                        <div>
                          <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            Asya-Pasifik Ülkeleri
                          </Label>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {[
                              { code: 'JP', name: 'Japonya', flag: '🇯🇵' },
                              { code: 'KR', name: 'Güney Kore', flag: '🇰🇷' },
                              { code: 'CN', name: 'Çin', flag: '🇨🇳' },
                              { code: 'SG', name: 'Singapur', flag: '🇸🇬' },
                              { code: 'HK', name: 'Hong Kong', flag: '🇭🇰' },
                              { code: 'AU', name: 'Avustralya', flag: '🇦🇺' },
                              { code: 'NZ', name: 'Yeni Zelanda', flag: '🇳🇿' },
                              { code: 'MY', name: 'Malezya', flag: '🇲🇾' },
                              { code: 'TH', name: 'Tayland', flag: '🇹🇭' }
                            ].map(country => (
                              <label key={country.code} className="flex items-center space-x-2 p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={searchConfig.countries.includes(country.code)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        countries: [...prev.countries, country.code]
                                      }));
                                    } else {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        countries: prev.countries.filter(c => c !== country.code)
                                      }));
                                    }
                                  }}
                                  className="rounded"
                                />
                                <span className="text-lg">{country.flag}</span>
                                <span className="text-sm">{country.name}</span>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* Diğer Önemli Ülkeler */}
                        <div>
                          <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            Diğer Önemli Pazarlar
                          </Label>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {[
                              { code: 'CA', name: 'Kanada', flag: '🇨🇦' },
                              { code: 'BR', name: 'Brezilya', flag: '🇧🇷' },
                              { code: 'MX', name: 'Meksika', flag: '🇲🇽' },
                              { code: 'RU', name: 'Rusya', flag: '🇷🇺' },
                              { code: 'ZA', name: 'Güney Afrika', flag: '🇿🇦' },
                              { code: 'EG', name: 'Mısır', flag: '🇪🇬' },
                              { code: 'GR', name: 'Yunanistan', flag: '🇬🇷' },
                              { code: 'CY', name: 'Kıbrıs', flag: '🇨🇾' },
                              { code: 'TR', name: 'Türkiye', flag: '🇹🇷' }
                            ].map(country => (
                              <label key={country.code} className="flex items-center space-x-2 p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={searchConfig.countries.includes(country.code)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        countries: [...prev.countries, country.code]
                                      }));
                                    } else {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        countries: prev.countries.filter(c => c !== country.code)
                                      }));
                                    }
                                  }}
                                  className="rounded"
                                />
                                <span className="text-lg">{country.flag}</span>
                                <span className="text-sm">{country.name}</span>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* Hızlı Seçim Butonları */}
                        <div>
                          <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            Hızlı Seçim
                          </Label>
                          <div className="flex flex-wrap gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const europeanCountries = ['DE', 'IT', 'FR', 'GB', 'ES', 'NL', 'BE', 'CH', 'AT', 'SE', 'NO', 'GR'];
                                setSearchConfig(prev => ({
                                  ...prev,
                                  countries: [...new Set([...prev.countries, ...europeanCountries])]
                                }));
                              }}
                            >
                              Avrupa Ülkeleri
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const middleEastCountries = ['AE', 'SA', 'QA', 'KW', 'BH', 'OM', 'JO', 'LB'];
                                setSearchConfig(prev => ({
                                  ...prev,
                                  countries: [...new Set([...prev.countries, ...middleEastCountries])]
                                }));
                              }}
                            >
                              Körfez Ülkeleri
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const asianCountries = ['JP', 'KR', 'CN', 'SG', 'HK', 'AU', 'MY', 'TH'];
                                setSearchConfig(prev => ({
                                  ...prev,
                                  countries: [...new Set([...prev.countries, ...asianCountries])]
                                }));
                              }}
                            >
                              Asya-Pasifik
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const topImporters = ['US', 'DE', 'IT', 'FR', 'GB', 'AE', 'SA', 'JP', 'KR', 'CN'];
                                setSearchConfig(prev => ({
                                  ...prev,
                                  countries: [...new Set([...prev.countries, ...topImporters])]
                                }));
                              }}
                            >
                              En Büyük İthalatçılar
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSearchConfig(prev => ({
                                  ...prev,
                                  countries: []
                                }));
                              }}
                            >
                              Temizle
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Hedef Sektörler */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Hedef Sektörler</CardTitle>
                      <p className="text-sm text-gray-600">
                        Doğal taş ürünleri kullanan sektörleri seçin veya özel sektör ekleyin
                      </p>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {/* Doğal Taş Tedarik Zinciri */}
                        <div>
                          <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            Doğal Taş Tedarik Zinciri
                          </Label>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {[
                              { id: 'marble_warehouses', name: 'Mermer Depoları', desc: 'Toptan mermer satış depoları' },
                              { id: 'stone_importers', name: 'Doğal Taş İthalatçıları', desc: 'Uluslararası taş ithal eden firmalar' },
                              { id: 'stone_distributors', name: 'Doğal Taş Distribütörleri', desc: 'Bölgesel dağıtım firmaları' },
                              { id: 'marble_companies', name: 'Mermer Firmaları', desc: 'Mermer üretim ve satış firmaları' },
                              { id: 'stone_suppliers', name: 'Taş Tedarikçileri', desc: 'İnşaat firmalarına taş tedariki' },
                              { id: 'building_material_stores', name: 'Yapı Malzemesi Mağazaları', desc: 'Perakende yapı malzemesi satışı' }
                            ].map(sector => (
                              <label key={sector.id} className="flex items-start space-x-2 p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={searchConfig.sectors.includes(sector.id)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        sectors: [...prev.sectors, sector.id]
                                      }));
                                    } else {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        sectors: prev.sectors.filter(s => s !== sector.id)
                                      }));
                                    }
                                  }}
                                  className="rounded mt-1"
                                />
                                <div>
                                  <div className="text-sm font-medium">{sector.name}</div>
                                  <div className="text-xs text-gray-500">{sector.desc}</div>
                                </div>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* İnşaat ve Müteahhitlik */}
                        <div>
                          <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            İnşaat ve Müteahhitlik
                          </Label>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {[
                              { id: 'general_contractors', name: 'Genel Müteahhitler', desc: 'Ana yüklenici inşaat firmaları' },
                              { id: 'construction_companies', name: 'İnşaat Firmaları', desc: 'Konut ve ticari yapı inşaatı' },
                              { id: 'stone_contractors', name: 'Taş İşleri Müteahhitleri', desc: 'Doğal taş montaj uzmanları' },
                              { id: 'facade_contractors', name: 'Cephe Müteahhitleri', desc: 'Bina cephesi kaplama uzmanları' },
                              { id: 'flooring_contractors', name: 'Döşeme Müteahhitleri', desc: 'Zemin kaplama uzmanları' },
                              { id: 'masonry_contractors', name: 'Taş Duvar Müteahhitleri', desc: 'Taş duvar ve yapı uzmanları' }
                            ].map(sector => (
                              <label key={sector.id} className="flex items-start space-x-2 p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={searchConfig.sectors.includes(sector.id)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        sectors: [...prev.sectors, sector.id]
                                      }));
                                    } else {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        sectors: prev.sectors.filter(s => s !== sector.id)
                                      }));
                                    }
                                  }}
                                  className="rounded mt-1"
                                />
                                <div>
                                  <div className="text-sm font-medium">{sector.name}</div>
                                  <div className="text-xs text-gray-500">{sector.desc}</div>
                                </div>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* Tasarım ve Mimarlık */}
                        <div>
                          <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            Tasarım ve Mimarlık
                          </Label>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {[
                              { id: 'architecture_firms', name: 'Mimarlık Büroları', desc: 'Tasarım ve proje ofisleri' },
                              { id: 'interior_designers', name: 'İç Mimarlık Firmaları', desc: 'İç mekan tasarım uzmanları' },
                              { id: 'landscape_architects', name: 'Peyzaj Mimarları', desc: 'Bahçe ve dış mekan tasarımı' },
                              { id: 'kitchen_designers', name: 'Mutfak Tasarımcıları', desc: 'Mutfak tezgahı ve tasarımı' },
                              { id: 'bathroom_designers', name: 'Banyo Tasarımcıları', desc: 'Banyo ve spa tasarımı' },
                              { id: 'stone_artisans', name: 'Taş Sanatçıları', desc: 'Özel taş işçiliği ve sanat' }
                            ].map(sector => (
                              <label key={sector.id} className="flex items-start space-x-2 p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={searchConfig.sectors.includes(sector.id)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        sectors: [...prev.sectors, sector.id]
                                      }));
                                    } else {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        sectors: prev.sectors.filter(s => s !== sector.id)
                                      }));
                                    }
                                  }}
                                  className="rounded mt-1"
                                />
                                <div>
                                  <div className="text-sm font-medium">{sector.name}</div>
                                  <div className="text-xs text-gray-500">{sector.desc}</div>
                                </div>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* Özel Projeler ve Uygulamalar */}
                        <div>
                          <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            Özel Projeler ve Uygulamalar
                          </Label>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {[
                              { id: 'monument_makers', name: 'Anıt ve Mezar Taşı', desc: 'Mezarlık ve anıt yapımcıları' },
                              { id: 'luxury_hotels', name: 'Lüks Otel Projeleri', desc: '5 yıldızlı otel ve resort' },
                              { id: 'shopping_malls', name: 'AVM ve Alışveriş Merkezleri', desc: 'Büyük ticari projeler' },
                              { id: 'religious_buildings', name: 'Dini Yapılar', desc: 'Cami, kilise ve dini mekanlar' },
                              { id: 'cultural_centers', name: 'Kültür Merkezleri', desc: 'Müze, opera ve sanat merkezleri' },
                              { id: 'government_buildings', name: 'Devlet Binaları', desc: 'Resmi kurum ve devlet binaları' }
                            ].map(sector => (
                              <label key={sector.id} className="flex items-start space-x-2 p-2 border rounded hover:bg-gray-50 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={searchConfig.sectors.includes(sector.id)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        sectors: [...prev.sectors, sector.id]
                                      }));
                                    } else {
                                      setSearchConfig(prev => ({
                                        ...prev,
                                        sectors: prev.sectors.filter(s => s !== sector.id)
                                      }));
                                    }
                                  }}
                                  className="rounded mt-1"
                                />
                                <div>
                                  <div className="text-sm font-medium">{sector.name}</div>
                                  <div className="text-xs text-gray-500">{sector.desc}</div>
                                </div>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* Özel Sektör Ekleme */}
                        <div>
                          <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            Özel Sektör Ekle
                          </Label>
                          <div className="flex space-x-2">
                            <Input
                              placeholder="Örn: Spa & Wellness Merkezleri, Müze & Galeri, vb."
                              value={searchConfig.customSector || ''}
                              onChange={(e) => setSearchConfig(prev => ({
                                ...prev,
                                customSector: e.target.value
                              }))}
                              className="flex-1"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => {
                                if (searchConfig.customSector && searchConfig.customSector.trim()) {
                                  const customId = `custom_${Date.now()}`;
                                  setSearchConfig(prev => ({
                                    ...prev,
                                    sectors: [...prev.sectors, customId],
                                    customSectors: {
                                      ...prev.customSectors,
                                      [customId]: searchConfig.customSector.trim()
                                    },
                                    customSector: ''
                                  }));
                                }
                              }}
                              disabled={!searchConfig.customSector?.trim()}
                            >
                              <Plus className="w-4 h-4" />
                            </Button>
                          </div>

                          {/* Eklenen Özel Sektörler */}
                          {searchConfig.customSectors && Object.keys(searchConfig.customSectors).length > 0 && (
                            <div className="mt-3 space-y-2">
                              <Label className="text-xs text-gray-600">Eklenen Özel Sektörler:</Label>
                              <div className="flex flex-wrap gap-2">
                                {Object.entries(searchConfig.customSectors).map(([id, name]) => (
                                  <div key={id} className="flex items-center space-x-1 bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                                    <span>{name}</span>
                                    <button
                                      onClick={() => {
                                        setSearchConfig(prev => ({
                                          ...prev,
                                          sectors: prev.sectors.filter(s => s !== id),
                                          customSectors: Object.fromEntries(
                                            Object.entries(prev.customSectors || {}).filter(([key]) => key !== id)
                                          )
                                        }));
                                      }}
                                      className="text-blue-600 hover:text-blue-800"
                                    >
                                      <X className="w-3 h-3" />
                                    </button>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Hızlı Seçim Butonları */}
                        <div>
                          <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            Hızlı Seçim
                          </Label>
                          <div className="flex flex-wrap gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const supplySectors = ['marble_warehouses', 'stone_importers', 'stone_distributors', 'marble_companies'];
                                setSearchConfig(prev => ({
                                  ...prev,
                                  sectors: [...new Set([...prev.sectors, ...supplySectors])]
                                }));
                              }}
                            >
                              Tedarik Zinciri
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const contractorSectors = ['general_contractors', 'construction_companies', 'stone_contractors', 'facade_contractors'];
                                setSearchConfig(prev => ({
                                  ...prev,
                                  sectors: [...new Set([...prev.sectors, ...contractorSectors])]
                                }));
                              }}
                            >
                              Müteahhitler
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const designSectors = ['architecture_firms', 'interior_designers', 'landscape_architects', 'kitchen_designers'];
                                setSearchConfig(prev => ({
                                  ...prev,
                                  sectors: [...new Set([...prev.sectors, ...designSectors])]
                                }));
                              }}
                            >
                              Tasarım Firmaları
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const allSectors = [
                                  'marble_warehouses', 'stone_importers', 'stone_distributors', 'marble_companies', 'stone_suppliers', 'building_material_stores',
                                  'general_contractors', 'construction_companies', 'stone_contractors', 'facade_contractors', 'flooring_contractors', 'masonry_contractors',
                                  'architecture_firms', 'interior_designers', 'landscape_architects', 'kitchen_designers', 'bathroom_designers', 'stone_artisans',
                                  'monument_makers', 'luxury_hotels', 'shopping_malls', 'religious_buildings', 'cultural_centers', 'government_buildings'
                                ];
                                setSearchConfig(prev => ({
                                  ...prev,
                                  sectors: [...new Set([...prev.sectors, ...allSectors])]
                                }));
                              }}
                            >
                              Tümünü Seç
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSearchConfig(prev => ({
                                  ...prev,
                                  sectors: [],
                                  customSectors: {}
                                }));
                              }}
                            >
                              Temizle
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Arama Parametreleri */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Arama Parametreleri</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="maxResults">Maksimum Sonuç ({searchConfig.maxResults})</Label>
                        <input
                          type="range"
                          id="maxResults"
                          min="50"
                          max="500"
                          step="50"
                          value={searchConfig.maxResults}
                          onChange={(e) => setSearchConfig(prev => ({
                            ...prev,
                            maxResults: parseInt(e.target.value)
                          }))}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                          <span>50</span>
                          <span>500</span>
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="minLeadScore">Minimum Lead Skoru ({searchConfig.minLeadScore})</Label>
                        <input
                          type="range"
                          id="minLeadScore"
                          min="30"
                          max="90"
                          step="10"
                          value={searchConfig.minLeadScore}
                          onChange={(e) => setSearchConfig(prev => ({
                            ...prev,
                            minLeadScore: parseInt(e.target.value)
                          }))}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                          <span>30</span>
                          <span>90</span>
                        </div>
                      </div>
                      <div>
                        <Label>Şirket Büyüklüğü</Label>
                        <div className="space-y-1 mt-2">
                          {[
                            { id: 'small', name: 'Küçük' },
                            { id: 'medium', name: 'Orta' },
                            { id: 'large', name: 'Büyük' },
                            { id: 'enterprise', name: 'Kurumsal' }
                          ].map(size => (
                            <label key={size.id} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                checked={searchConfig.companySize.includes(size.id)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setSearchConfig(prev => ({
                                      ...prev,
                                      companySize: [...prev.companySize, size.id]
                                    }));
                                  } else {
                                    setSearchConfig(prev => ({
                                      ...prev,
                                      companySize: prev.companySize.filter(s => s !== size.id)
                                    }));
                                  }
                                }}
                                className="rounded"
                              />
                              <span className="text-xs">{size.name}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Veri Kaynakları */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Veri Kaynakları</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {[
                        { id: 'google_maps', name: 'Google Maps', desc: 'Yerel işletme arama' },
                        { id: 'linkedin', name: 'LinkedIn', desc: 'Profesyonel profil arama' },
                        { id: 'trade_directories', name: 'Trade Directories', desc: 'Sektörel dizin tarama' }
                      ].map(source => (
                        <label key={source.id} className="flex items-start space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={searchConfig.dataSources.includes(source.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSearchConfig(prev => ({
                                  ...prev,
                                  dataSources: [...prev.dataSources, source.id]
                                }));
                              } else {
                                setSearchConfig(prev => ({
                                  ...prev,
                                  dataSources: prev.dataSources.filter(s => s !== source.id)
                                }));
                              }
                            }}
                            className="rounded mt-1"
                          />
                          <div>
                            <div className="font-medium text-sm">{source.name}</div>
                            <div className="text-xs text-gray-500">{source.desc}</div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="flex items-center justify-end space-x-2 pt-4">
                  <Button variant="outline" onClick={() => setShowSearchModal(false)}>
                    <X className="w-4 h-4 mr-2" />
                    İptal
                  </Button>
                  <Button
                    onClick={handleExecuteSearch}
                    disabled={searchConfig.countries.length === 0 || searchConfig.sectors.length === 0}
                  >
                    <Target className="w-4 h-4 mr-2" />
                    Aramayı Başlat
                  </Button>
                </div>
              </>
            ) : (
              /* Arama İlerleme Ekranı */
              <div className="space-y-6">
                <div className="text-center">
                  <div className="text-lg font-medium mb-2">AI Müşteri Araması Devam Ediyor...</div>
                  <div className="text-sm text-gray-600">
                    {searchConfig.countries.join(', ')} ülkelerinde {searchConfig.sectors.length} sektörde arama yapılıyor
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>İlerleme</span>
                    <span>{Math.round(searchProgress)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-blue-600 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${searchProgress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Arama Aşamaları */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full ${searchProgress > 20 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className="text-sm">Google Maps taraması</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full ${searchProgress > 40 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className="text-sm">LinkedIn profil arama</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full ${searchProgress > 60 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className="text-sm">Trade directory tarama</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full ${searchProgress > 80 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className="text-sm">AI analizi ve skorlama</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full ${searchProgress === 100 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    <span className="text-sm">Sonuçların işlenmesi</span>
                  </div>
                </div>

                {/* Bulunan Sonuçlar */}
                {searchResults.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Bulunan Müşteriler ({searchResults.length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {searchResults.map(result => (
                          <div key={result.id} className="flex items-center justify-between p-2 border rounded">
                            <div>
                              <div className="font-medium text-sm">{result.companyName}</div>
                              <div className="text-xs text-gray-500">{result.location.city}, {result.location.country}</div>
                            </div>
                            <Badge variant="secondary">Skor: {result.leadScore}</Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Stop Button */}
                <div className="flex items-center justify-center pt-4">
                  <Button variant="outline" onClick={handleStopSearch}>
                    <X className="w-4 h-4 mr-2" />
                    Aramayı Durdur
                  </Button>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Manual Add Modal */}
      <Dialog open={showManualAddModal} onOpenChange={setShowManualAddModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Plus className="w-5 h-5" />
              <span>Manuel Müşteri Ekleme</span>
            </DialogTitle>
            <DialogDescription>
              Yeni müşteri bilgilerini manuel olarak ekleyin
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Temel Bilgiler */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Temel Bilgiler</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="companyName">Şirket Adı *</Label>
                    <Input
                      id="companyName"
                      value={manualAddForm.companyName}
                      onChange={(e) => setManualAddForm(prev => ({ ...prev, companyName: e.target.value }))}
                      placeholder="Şirket adını girin"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="industry">Sektör</Label>
                    <Select
                      value={manualAddForm.industry}
                      onValueChange={(value) => setManualAddForm(prev => ({ ...prev, industry: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sektör seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="construction">İnşaat Firmaları</SelectItem>
                        <SelectItem value="architecture">Mimarlık Büroları</SelectItem>
                        <SelectItem value="interior_design">İç Mimarlık</SelectItem>
                        <SelectItem value="landscaping">Peyzaj Mimarlığı</SelectItem>
                        <SelectItem value="stone_processing">Taş İşleme Atölyeleri</SelectItem>
                        <SelectItem value="monument_makers">Anıt/Mezar Taşı</SelectItem>
                        <SelectItem value="real_estate">Gayrimenkul Geliştirme</SelectItem>
                        <SelectItem value="hospitality">Otel & Restoran</SelectItem>
                        <SelectItem value="retail_stores">Mağaza Tasarımı</SelectItem>
                        <SelectItem value="public_projects">Kamu Projeleri</SelectItem>
                        <SelectItem value="luxury_homes">Lüks Konut</SelectItem>
                        <SelectItem value="industrial">Endüstriyel Yapılar</SelectItem>
                        <SelectItem value="other">Diğer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="country">Ülke</Label>
                    <Select
                      value={manualAddForm.country}
                      onValueChange={(value) => setManualAddForm(prev => ({ ...prev, country: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Ülke seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Turkey">Türkiye</SelectItem>
                        <SelectItem value="Germany">Almanya</SelectItem>
                        <SelectItem value="France">Fransa</SelectItem>
                        <SelectItem value="Italy">İtalya</SelectItem>
                        <SelectItem value="Spain">İspanya</SelectItem>
                        <SelectItem value="Greece">Yunanistan</SelectItem>
                        <SelectItem value="USA">ABD</SelectItem>
                        <SelectItem value="UK">İngiltere</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="city">Şehir *</Label>
                    <Input
                      id="city"
                      value={manualAddForm.city}
                      onChange={(e) => setManualAddForm(prev => ({ ...prev, city: e.target.value }))}
                      placeholder="Şehir adını girin"
                      required
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* İletişim Bilgileri */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">İletişim Bilgileri</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="email"
                        type="email"
                        value={manualAddForm.email}
                        onChange={(e) => setManualAddForm(prev => ({ ...prev, email: e.target.value }))}
                        placeholder="<EMAIL>"
                        onBlur={handleValidateForm}
                      />
                      {validationResults.email !== null && (
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center mt-2 ${
                          validationResults.email ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                        }`}>
                          {validationResults.email ? '✓' : '✗'}
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="phone">Telefon</Label>
                    <Input
                      id="phone"
                      value={manualAddForm.phone}
                      onChange={(e) => setManualAddForm(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="+90 ************"
                    />
                  </div>
                  <div>
                    <Label htmlFor="website">Web Sitesi</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="website"
                        value={manualAddForm.website}
                        onChange={(e) => setManualAddForm(prev => ({ ...prev, website: e.target.value }))}
                        placeholder="www.sirket.com"
                        onBlur={handleValidateForm}
                      />
                      {validationResults.website !== null && (
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center mt-2 ${
                          validationResults.website ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                        }`}>
                          {validationResults.website ? '✓' : '✗'}
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="linkedin">LinkedIn</Label>
                    <Input
                      id="linkedin"
                      value={manualAddForm.linkedin}
                      onChange={(e) => setManualAddForm(prev => ({ ...prev, linkedin: e.target.value }))}
                      placeholder="linkedin.com/company/sirket"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Şirket Detayları */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Şirket Detayları</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="companySize">Şirket Büyüklüğü</Label>
                    <Select
                      value={manualAddForm.companySize}
                      onValueChange={(value) => setManualAddForm(prev => ({ ...prev, companySize: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Büyüklük seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Küçük (1-10 çalışan)</SelectItem>
                        <SelectItem value="medium">Orta (11-50 çalışan)</SelectItem>
                        <SelectItem value="large">Büyük (51-200 çalışan)</SelectItem>
                        <SelectItem value="enterprise">Kurumsal (200+ çalışan)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="annualRevenue">Yıllık Ciro (USD)</Label>
                    <Input
                      id="annualRevenue"
                      type="number"
                      value={manualAddForm.annualRevenue}
                      onChange={(e) => setManualAddForm(prev => ({ ...prev, annualRevenue: e.target.value }))}
                      placeholder="1000000"
                    />
                  </div>
                  <div>
                    <Label htmlFor="employeeCount">Çalışan Sayısı</Label>
                    <Input
                      id="employeeCount"
                      type="number"
                      value={manualAddForm.employeeCount}
                      onChange={(e) => setManualAddForm(prev => ({ ...prev, employeeCount: e.target.value }))}
                      placeholder="25"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="notes">Notlar</Label>
                  <Textarea
                    id="notes"
                    value={manualAddForm.notes}
                    onChange={(e) => setManualAddForm(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Şirket hakkında ek bilgiler..."
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Lead Skoru Önizleme */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Lead Skoru Önizleme</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">
                      {calculateLeadScore(manualAddForm)}
                    </div>
                    <div className="text-sm text-gray-600">Lead Skoru</div>
                  </div>
                  <div className="flex-1">
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${calculateLeadScore(manualAddForm)}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Skor hesaplaması: Şirket büyüklüğü, iletişim bilgileri, gelir ve çalışan sayısına göre
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Duplicate Warning */}
            {validationResults.duplicate && (
              <Card className="border-orange-200 bg-orange-50">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2 text-orange-600">
                    <AlertTriangle className="w-4 h-4" />
                    <span className="font-medium">Uyarı: Benzer bir şirket zaten mevcut olabilir</span>
                  </div>
                  <p className="text-sm text-orange-700 mt-1">
                    Devam etmeden önce mevcut müşteri listesini kontrol etmenizi öneririz.
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4">
              <Button
                variant="outline"
                onClick={handleValidateForm}
                disabled={isValidating || (!manualAddForm.email && !manualAddForm.website)}
              >
                {isValidating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                    Doğrulanıyor...
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Bilgileri Doğrula
                  </>
                )}
              </Button>
              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={() => setShowManualAddModal(false)}>
                  <X className="w-4 h-4 mr-2" />
                  İptal
                </Button>
                <Button
                  onClick={handleSaveManualAdd}
                  disabled={!manualAddForm.companyName || !manualAddForm.city}
                >
                  <Save className="w-4 h-4 mr-2" />
                  Müşteri Ekle
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
