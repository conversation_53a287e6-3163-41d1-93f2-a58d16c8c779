#!/usr/bin/env node

/**
 * Database Restore Script
 * Restores database from backup files
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Load environment variables
require('dotenv').config({ path: '.env.production' });

console.log('🔄 Database Restore Utility\n');

// Configuration
const BACKUP_DIR = path.join(process.cwd(), 'backups');

// Parse database URL
function parseDatabaseUrl(url) {
  const dbUrl = new URL(url);
  return {
    host: dbUrl.hostname,
    port: dbUrl.port || 5432,
    username: dbUrl.username,
    password: dbUrl.password,
    database: dbUrl.pathname.slice(1)
  };
}

// List available backups
function listBackups() {
  if (!fs.existsSync(BACKUP_DIR)) {
    console.error('❌ Backup directory not found');
    process.exit(1);
  }

  const files = fs.readdirSync(BACKUP_DIR);
  const backupFiles = files
    .filter(file => file.startsWith('backup-') && file.endsWith('.sql.gz'))
    .map(file => {
      const filePath = path.join(BACKUP_DIR, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        path: filePath,
        size: stats.size,
        mtime: stats.mtime
      };
    })
    .sort((a, b) => b.mtime - a.mtime);

  if (backupFiles.length === 0) {
    console.error('❌ No backup files found');
    process.exit(1);
  }

  console.log('📋 Available backups:');
  backupFiles.forEach((file, index) => {
    const sizeInMB = (file.size / 1024 / 1024).toFixed(2);
    const date = file.mtime.toLocaleString();
    console.log(`${index + 1}. ${file.name} (${sizeInMB} MB) - ${date}`);
  });

  return backupFiles;
}

// Get user confirmation
function askQuestion(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

// Verify backup file
function verifyBackup(backupFile) {
  console.log('🔍 Verifying backup file...');
  
  try {
    // Check if file exists
    if (!fs.existsSync(backupFile)) {
      throw new Error('Backup file not found');
    }

    // Check file size
    const stats = fs.statSync(backupFile);
    if (stats.size === 0) {
      throw new Error('Backup file is empty');
    }

    // Test gzip integrity
    execSync(`gzip -t "${backupFile}"`, { stdio: 'pipe' });
    
    console.log(`✅ Backup file verified (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
    return true;
  } catch (error) {
    console.error('❌ Backup verification failed:', error.message);
    return false;
  }
}

// Create database backup before restore
async function createPreRestoreBackup() {
  console.log('💾 Creating pre-restore backup...');
  
  try {
    const dbConfig = parseDatabaseUrl(process.env.DATABASE_URL);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const preRestoreFile = path.join(BACKUP_DIR, `pre-restore-${timestamp}.sql`);
    
    const pgDumpCommand = [
      'pg_dump',
      `-h ${dbConfig.host}`,
      `-p ${dbConfig.port}`,
      `-U ${dbConfig.username}`,
      `-d ${dbConfig.database}`,
      '--clean',
      '--no-owner',
      '--no-privileges',
      `--file="${preRestoreFile}"`
    ].join(' ');

    execSync(pgDumpCommand, {
      stdio: 'inherit',
      env: { ...process.env, PGPASSWORD: dbConfig.password }
    });

    // Compress the backup
    execSync(`gzip "${preRestoreFile}"`, { stdio: 'inherit' });
    
    console.log(`✅ Pre-restore backup created: ${preRestoreFile}.gz`);
    return `${preRestoreFile}.gz`;
  } catch (error) {
    console.error('❌ Pre-restore backup failed:', error.message);
    throw error;
  }
}

// Restore database from backup
async function restoreDatabase(backupFile) {
  console.log('🔄 Restoring database...');
  
  try {
    const dbConfig = parseDatabaseUrl(process.env.DATABASE_URL);
    
    // Decompress backup file to temporary location
    const tempFile = backupFile.replace('.gz', '.temp');
    execSync(`gunzip -c "${backupFile}" > "${tempFile}"`, { stdio: 'inherit' });
    
    // Restore database
    const psqlCommand = [
      'psql',
      `-h ${dbConfig.host}`,
      `-p ${dbConfig.port}`,
      `-U ${dbConfig.username}`,
      `-d ${dbConfig.database}`,
      `-f "${tempFile}"`
    ].join(' ');

    execSync(psqlCommand, {
      stdio: 'inherit',
      env: { ...process.env, PGPASSWORD: dbConfig.password }
    });

    // Clean up temporary file
    fs.unlinkSync(tempFile);
    
    console.log('✅ Database restore completed');
  } catch (error) {
    console.error('❌ Database restore failed:', error.message);
    throw error;
  }
}

// Regenerate Prisma client after restore
async function regenerateClient() {
  console.log('🔧 Regenerating Prisma client...');
  
  try {
    execSync('npx prisma generate', { stdio: 'inherit' });
    console.log('✅ Prisma client regenerated');
  } catch (error) {
    console.error('❌ Client regeneration failed:', error.message);
    throw error;
  }
}

// Main restore process
async function main() {
  try {
    // List available backups
    const backups = listBackups();
    
    // Get backup selection from user
    const selection = await askQuestion('\nSelect backup number to restore: ');
    const selectedIndex = parseInt(selection) - 1;
    
    if (selectedIndex < 0 || selectedIndex >= backups.length) {
      console.error('❌ Invalid selection');
      process.exit(1);
    }
    
    const selectedBackup = backups[selectedIndex];
    console.log(`\nSelected: ${selectedBackup.name}`);
    
    // Verify backup file
    if (!verifyBackup(selectedBackup.path)) {
      process.exit(1);
    }
    
    // Final confirmation
    const confirm = await askQuestion('\n⚠️  This will REPLACE your current database. Continue? (yes/no): ');
    if (confirm.toLowerCase() !== 'yes') {
      console.log('❌ Restore cancelled');
      process.exit(0);
    }
    
    // Create pre-restore backup
    await createPreRestoreBackup();
    
    // Restore database
    await restoreDatabase(selectedBackup.path);
    
    // Regenerate Prisma client
    await regenerateClient();
    
    console.log('\n🎉 Database restore completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Restart your application');
    console.log('2. Verify data integrity');
    console.log('3. Check application functionality');
    
  } catch (error) {
    console.error('\n❌ Database restore failed:', error.message);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('Usage: node restore-database.js');
  console.log('');
  console.log('This script will:');
  console.log('1. List available backup files');
  console.log('2. Allow you to select a backup to restore');
  console.log('3. Create a pre-restore backup');
  console.log('4. Restore the selected backup');
  console.log('5. Regenerate Prisma client');
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
