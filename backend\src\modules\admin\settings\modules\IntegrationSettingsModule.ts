import { SettingsModule } from '../core/SettingsModule';
import { SettingsCategory } from '../types/settings.types';

export class IntegrationSettingsModule extends SettingsModule {
  protected category: SettingsCategory = 'INTEGRATIONS';

  protected getDefaultSettings() {
    return {
      paymentGatewayEnabled: {
        key: 'payment_gateway_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable payment gateway integration'
      },
      stripeEnabled: {
        key: 'stripe_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable Stripe payment gateway'
      },
      stripePublicKey: {
        key: 'stripe_public_key',
        value: '',
        type: 'string',
        category: this.category,
        description: 'Stripe public key'
      },
      stripeSecretKey: {
        key: 'stripe_secret_key',
        value: '',
        type: 'string',
        category: this.category,
        description: 'Stripe secret key'
      },
      paypalEnabled: {
        key: 'paypal_enabled',
        value: false,
        type: 'boolean',
        category: this.category,
        description: 'Enable PayPal payment gateway'
      },
      paypalClientId: {
        key: 'paypal_client_id',
        value: '',
        type: 'string',
        category: this.category,
        description: 'PayPal client ID'
      },
      paypalClientSecret: {
        key: 'paypal_client_secret',
        value: '',
        type: 'string',
        category: this.category,
        description: 'PayPal client secret'
      },
      emailServiceEnabled: {
        key: 'email_service_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable email service integration'
      },
      smtpHost: {
        key: 'smtp_host',
        value: '',
        type: 'string',
        category: this.category,
        description: 'SMTP server host'
      },
      smtpPort: {
        key: 'smtp_port',
        value: 587,
        type: 'number',
        category: this.category,
        description: 'SMTP server port'
      },
      smtpUsername: {
        key: 'smtp_username',
        value: '',
        type: 'string',
        category: this.category,
        description: 'SMTP username'
      },
      smtpPassword: {
        key: 'smtp_password',
        value: '',
        type: 'string',
        category: this.category,
        description: 'SMTP password'
      },
      smtpSecure: {
        key: 'smtp_secure',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Use secure SMTP connection'
      },
      cloudStorageEnabled: {
        key: 'cloud_storage_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable cloud storage integration'
      },
      awsS3Enabled: {
        key: 'aws_s3_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable AWS S3 storage'
      },
      awsAccessKeyId: {
        key: 'aws_access_key_id',
        value: '',
        type: 'string',
        category: this.category,
        description: 'AWS access key ID'
      },
      awsSecretAccessKey: {
        key: 'aws_secret_access_key',
        value: '',
        type: 'string',
        category: this.category,
        description: 'AWS secret access key'
      },
      awsS3Bucket: {
        key: 'aws_s3_bucket',
        value: '',
        type: 'string',
        category: this.category,
        description: 'AWS S3 bucket name'
      },
      awsRegion: {
        key: 'aws_region',
        value: 'us-east-1',
        type: 'string',
        category: this.category,
        description: 'AWS region'
      },
      googleMapsEnabled: {
        key: 'google_maps_enabled',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable Google Maps integration'
      },
      googleMapsApiKey: {
        key: 'google_maps_api_key',
        value: '',
        type: 'string',
        category: this.category,
        description: 'Google Maps API key'
      },
      smsServiceEnabled: {
        key: 'sms_service_enabled',
        value: false,
        type: 'boolean',
        category: this.category,
        description: 'Enable SMS service integration'
      },
      twilioEnabled: {
        key: 'twilio_enabled',
        value: false,
        type: 'boolean',
        category: this.category,
        description: 'Enable Twilio SMS service'
      },
      twilioAccountSid: {
        key: 'twilio_account_sid',
        value: '',
        type: 'string',
        category: this.category,
        description: 'Twilio account SID'
      },
      twilioAuthToken: {
        key: 'twilio_auth_token',
        value: '',
        type: 'string',
        category: this.category,
        description: 'Twilio auth token'
      },
      twilioPhoneNumber: {
        key: 'twilio_phone_number',
        value: '',
        type: 'string',
        category: this.category,
        description: 'Twilio phone number'
      }
    };
  }

  async validateSetting(key: string, value: any): Promise<boolean> {
    switch (key) {
      case 'payment_gateway_enabled':
      case 'stripe_enabled':
      case 'paypal_enabled':
      case 'email_service_enabled':
      case 'smtp_secure':
      case 'cloud_storage_enabled':
      case 'aws_s3_enabled':
      case 'google_maps_enabled':
      case 'sms_service_enabled':
      case 'twilio_enabled':
        return typeof value === 'boolean';
      case 'smtp_port':
        return typeof value === 'number' && value > 0 && value <= 65535;
      case 'aws_region':
        return typeof value === 'string' && /^[a-z0-9-]+$/.test(value);
      default:
        return typeof value === 'string';
    }
  }
}
