'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  X,
  Users,
  Mail,
  Calendar,
  TrendingUp,
  TrendingDown,
  Activity,
  MapPin,
  Building,
  Download,
  Eye,
  FileSpreadsheet,
  Search
} from 'lucide-react';

interface CountryEmailList {
  id: string;
  countryCode: string;
  countryName: string;
  flag: string;
  totalSubscribers: number;
  activeSubscribers: number;
  lastUpdated: Date;
  segments: number;
}

interface EmailSubscriber {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  company: string;
  industry: string;
  subscribeDate: Date;
  lastActivity: Date;
  status: 'active' | 'inactive' | 'bounced' | 'unsubscribed';
  segment: string;
  engagementScore: number;
}

interface CountryDetailModalProps {
  country: CountryEmailList;
  onClose: () => void;
}

export default function CountryDetailModal({ country, onClose }: CountryDetailModalProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'subscribers'>('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // Mock email subscribers data
  const mockSubscribers: EmailSubscriber[] = [
    {
      id: '1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Smith',
      company: 'Smith Construction LLC',
      industry: 'İnşaat',
      subscribeDate: new Date('2024-01-15'),
      lastActivity: new Date('2025-07-02'),
      status: 'active',
      segment: 'Premium Müşteri',
      engagementScore: 85
    },
    {
      id: '2',
      email: '<EMAIL>',
      firstName: 'Maria',
      lastName: 'Garcia',
      company: 'Garcia Architecture Studio',
      industry: 'Mimarlık',
      subscribeDate: new Date('2024-03-22'),
      lastActivity: new Date('2025-07-01'),
      status: 'active',
      segment: 'Yeni Müşteri',
      engagementScore: 72
    },
    {
      id: '3',
      email: '<EMAIL>',
      firstName: 'David',
      lastName: 'Wilson',
      company: 'Wilson Interiors',
      industry: 'İç Mimarlık',
      subscribeDate: new Date('2023-11-08'),
      lastActivity: new Date('2025-06-28'),
      status: 'active',
      segment: 'VIP Müşteri',
      engagementScore: 94
    },
    {
      id: '4',
      email: '<EMAIL>',
      firstName: 'Sarah',
      lastName: 'Johnson',
      company: 'Johnson Builders',
      industry: 'İnşaat',
      subscribeDate: new Date('2024-05-10'),
      lastActivity: new Date('2025-06-15'),
      status: 'inactive',
      segment: 'Standart Müşteri',
      engagementScore: 45
    },
    {
      id: '5',
      email: '<EMAIL>',
      firstName: 'Michael',
      lastName: 'Brown',
      company: 'Brown Design Co.',
      industry: 'Tasarım',
      subscribeDate: new Date('2024-02-18'),
      lastActivity: new Date('2025-05-20'),
      status: 'bounced',
      segment: 'Yeni Müşteri',
      engagementScore: 0
    }
  ];

  // Mock detailed data
  const detailedStats = {
    newSubscribersThisWeek: 23,
    unsubscribedThisWeek: 5,
    bounceRate: 2.3,
    engagementRate: 18.7,
    topIndustries: [
      { name: 'İnşaat', count: 156, percentage: 32.1 },
      { name: 'Mimarlık', count: 89, percentage: 18.3 },
      { name: 'İç Mimarlık', count: 67, percentage: 13.8 },
      { name: 'Peyzaj', count: 45, percentage: 9.3 },
      { name: 'Diğer', count: 128, percentage: 26.5 }
    ],
    recentCampaigns: [
      {
        id: '1',
        name: 'Traverten Koleksiyonu',
        sentDate: new Date('2025-07-01'),
        recipients: 485,
        openRate: 24.5,
        clickRate: 3.2
      },
      {
        id: '2',
        name: 'Mermer Trendleri',
        sentDate: new Date('2025-06-28'),
        recipients: 485,
        openRate: 22.1,
        clickRate: 2.8
      },
      {
        id: '3',
        name: 'Özel İndirim',
        sentDate: new Date('2025-06-25'),
        recipients: 478,
        openRate: 28.3,
        clickRate: 4.1
      }
    ]
  };

  const activeRate = (country.activeSubscribers / country.totalSubscribers) * 100;

  // Filter subscribers
  const filteredSubscribers = mockSubscribers.filter(subscriber => {
    const matchesSearch =
      subscriber.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      subscriber.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      subscriber.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      subscriber.company.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === 'all' || subscriber.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  // Export to Excel function
  const exportToExcel = () => {
    const csvContent = [
      // Header
      ['Email', 'Ad', 'Soyad', 'Şirket', 'Sektör', 'Abone Tarihi', 'Son Aktivite', 'Durum', 'Segment', 'Etkileşim Skoru'].join(','),
      // Data rows
      ...filteredSubscribers.map(subscriber => [
        subscriber.email,
        subscriber.firstName,
        subscriber.lastName,
        subscriber.company,
        subscriber.industry,
        subscriber.subscribeDate.toLocaleDateString('tr-TR'),
        subscriber.lastActivity.toLocaleDateString('tr-TR'),
        subscriber.status,
        subscriber.segment,
        subscriber.engagementScore
      ].join(','))
    ].join('\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${country.countryName}_email_listesi_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Aktif</span>;
      case 'inactive':
        return <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">Pasif</span>;
      case 'bounced':
        return <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">Geri Döndü</span>;
      case 'unsubscribed':
        return <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Abonelik İptal</span>;
      default:
        return <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Bilinmiyor</span>;
    }
  };

  const getEngagementColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="border-b">
          <div className="flex items-center justify-between p-6">
            <div className="flex items-center space-x-3">
              <span className="text-3xl">{country.flag}</span>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{country.countryName}</h2>
                <p className="text-gray-600">Email Liste Detayları</p>
              </div>
            </div>
            <Button variant="ghost" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Tabs */}
          <div className="flex space-x-1 px-6">
            <button
              onClick={() => setActiveTab('overview')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'overview'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Activity className="w-4 h-4 inline mr-2" />
              Genel Bakış
            </button>
            <button
              onClick={() => setActiveTab('subscribers')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'subscribers'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Users className="w-4 h-4 inline mr-2" />
              Abone Listesi ({filteredSubscribers.length})
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Toplam Abone</p>
                    <p className="text-2xl font-bold">{country.totalSubscribers.toLocaleString()}</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Aktif Abone</p>
                    <p className="text-2xl font-bold text-green-600">{country.activeSubscribers.toLocaleString()}</p>
                    <p className="text-xs text-gray-500">{activeRate.toFixed(1)}% aktif</p>
                  </div>
                  <Activity className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Bu Hafta Yeni</p>
                    <p className="text-2xl font-bold text-purple-600">{detailedStats.newSubscribersThisWeek}</p>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="w-3 h-3 text-green-500" />
                      <span className="text-xs text-green-600">+12%</span>
                    </div>
                  </div>
                  <TrendingUp className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Etkileşim Oranı</p>
                    <p className="text-2xl font-bold text-orange-600">{detailedStats.engagementRate}%</p>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="w-3 h-3 text-green-500" />
                      <span className="text-xs text-green-600">+2.3%</span>
                    </div>
                  </div>
                  <Mail className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Industries */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="w-5 h-5 mr-2" />
                  Sektör Dağılımı
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {detailedStats.topIndustries.map((industry, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">{industry.name}</span>
                          <span className="text-sm text-gray-500">{industry.count}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${industry.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Campaigns */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Son Kampanyalar
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {detailedStats.recentCampaigns.map((campaign) => (
                    <div key={campaign.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{campaign.name}</h4>
                        <span className="text-xs text-gray-500">
                          {campaign.sentDate.toLocaleDateString('tr-TR')}
                        </span>
                      </div>
                      <div className="grid grid-cols-3 gap-2 text-sm">
                        <div>
                          <span className="text-gray-600">Alıcı: </span>
                          <span className="font-medium">{campaign.recipients}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Açılma: </span>
                          <span className="font-medium text-green-600">{campaign.openRate}%</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Tıklama: </span>
                          <span className="font-medium text-blue-600">{campaign.clickRate}%</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Detaylı İstatistikler</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-600">{detailedStats.unsubscribedThisWeek}</p>
                  <p className="text-sm text-gray-600">Bu Hafta Ayrılan</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-yellow-600">{detailedStats.bounceRate}%</p>
                  <p className="text-sm text-gray-600">Bounce Oranı</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{country.segments}</p>
                  <p className="text-sm text-gray-600">Segment Sayısı</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-600">
                    {Math.floor((Date.now() - country.lastUpdated.getTime()) / (1000 * 60 * 60))}h
                  </p>
                  <p className="text-sm text-gray-600">Son Güncelleme</p>
                </div>
              </div>
            </CardContent>
          </Card>

            </div>
          )}

          {/* Subscribers Tab */}
          {activeTab === 'subscribers' && (
            <div className="space-y-4">
              {/* Search and Filter */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Email, ad, şirket ara..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="border rounded px-3 py-2"
                  >
                    <option value="all">Tüm Durumlar</option>
                    <option value="active">Aktif</option>
                    <option value="inactive">Pasif</option>
                    <option value="bounced">Geri Dönen</option>
                    <option value="unsubscribed">Abonelik İptal</option>
                  </select>
                </div>
                <Button onClick={exportToExcel} variant="outline">
                  <FileSpreadsheet className="w-4 h-4 mr-2" />
                  Excel İndir ({filteredSubscribers.length})
                </Button>
              </div>

              {/* Subscribers Table */}
              <Card>
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Email</TableHead>
                        <TableHead>Ad Soyad</TableHead>
                        <TableHead>Şirket</TableHead>
                        <TableHead>Sektör</TableHead>
                        <TableHead>Segment</TableHead>
                        <TableHead>Abone Tarihi</TableHead>
                        <TableHead>Son Aktivite</TableHead>
                        <TableHead>Etkileşim</TableHead>
                        <TableHead>Durum</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredSubscribers.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={9} className="text-center py-8">
                            <Users className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                            <p className="text-gray-500">
                              {searchTerm || filterStatus !== 'all'
                                ? 'Arama kriterlerinize uygun abone bulunamadı'
                                : 'Henüz abone bulunmuyor'
                              }
                            </p>
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredSubscribers.map((subscriber) => (
                          <TableRow key={subscriber.id}>
                            <TableCell>
                              <div>
                                <p className="font-medium">{subscriber.email}</p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>
                                <p className="font-medium">{subscriber.firstName} {subscriber.lastName}</p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <p className="text-sm">{subscriber.company}</p>
                            </TableCell>
                            <TableCell>
                              <p className="text-sm">{subscriber.industry}</p>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">{subscriber.segment}</Badge>
                            </TableCell>
                            <TableCell>
                              <p className="text-sm">{subscriber.subscribeDate.toLocaleDateString('tr-TR')}</p>
                            </TableCell>
                            <TableCell>
                              <p className="text-sm">{subscriber.lastActivity.toLocaleDateString('tr-TR')}</p>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <span className={`font-medium ${getEngagementColor(subscriber.engagementScore)}`}>
                                  {subscriber.engagementScore}
                                </span>
                                <div className="w-16 bg-gray-200 rounded-full h-2">
                                  <div
                                    className={`h-2 rounded-full ${
                                      subscriber.engagementScore >= 80 ? 'bg-green-500' :
                                      subscriber.engagementScore >= 60 ? 'bg-yellow-500' :
                                      subscriber.engagementScore >= 40 ? 'bg-orange-500' : 'bg-red-500'
                                    }`}
                                    style={{ width: `${subscriber.engagementScore}%` }}
                                  ></div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              {getStatusBadge(subscriber.status)}
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>

              {/* Subscribers Stats */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <p className="text-2xl font-bold text-green-600">
                      {mockSubscribers.filter(s => s.status === 'active').length}
                    </p>
                    <p className="text-sm text-gray-600">Aktif</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <p className="text-2xl font-bold text-yellow-600">
                      {mockSubscribers.filter(s => s.status === 'inactive').length}
                    </p>
                    <p className="text-sm text-gray-600">Pasif</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <p className="text-2xl font-bold text-red-600">
                      {mockSubscribers.filter(s => s.status === 'bounced').length}
                    </p>
                    <p className="text-sm text-gray-600">Geri Dönen</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <p className="text-2xl font-bold text-gray-600">
                      {mockSubscribers.filter(s => s.status === 'unsubscribed').length}
                    </p>
                    <p className="text-sm text-gray-600">İptal Eden</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <p className="text-2xl font-bold text-blue-600">
                      {(mockSubscribers.reduce((sum, s) => sum + s.engagementScore, 0) / mockSubscribers.length).toFixed(0)}
                    </p>
                    <p className="text-sm text-gray-600">Ort. Etkileşim</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Kapat
            </Button>
            <Button>
              <Mail className="w-4 h-4 mr-2" />
              Kampanya Gönder
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
