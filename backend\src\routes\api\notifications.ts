import { Router, Request, Response, NextFunction } from 'express';
import { NotificationController } from '../../controllers/NotificationController';
import { authMiddleware } from '../../middleware/authMiddleware';
import { asyncHandler } from '../../middleware/errorHandler';

const router = Router();

// Note: NotificationController will be initialized in the main app with NotificationService
let notificationController: NotificationController;

export function initializeNotificationRoutes(controller: NotificationController) {
  notificationController = controller;
}

// Apply authentication middleware to all routes
router.use(authMiddleware);

/**
 * @route POST /api/notifications/send
 * @desc Send notification to specific user
 * @access Private (Admin only)
 */
router.post('/send', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!notificationController) {
    return res.status(500).json({ success: false, error: 'Notification service not initialized' });
  }
  await notificationController.sendNotification(req, res, next);
}));

/**
 * @route POST /api/notifications/broadcast
 * @desc Broadcast notification to all users of a type
 * @access Private (Admin only)
 */
router.post('/broadcast', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!notificationController) {
    return res.status(500).json({ success: false, error: 'Notification service not initialized' });
  }
  await notificationController.broadcastNotification(req, res, next);
}));

/**
 * @route GET /api/notifications
 * @desc Get user's notifications
 * @access Private
 */
router.get('/', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!notificationController) {
    return res.status(500).json({ success: false, error: 'Notification service not initialized' });
  }
  await notificationController.getNotifications(req, res, next);
}));

/**
 * @route GET /api/notifications/unread-count
 * @desc Get unread notifications count
 * @access Private
 */
router.get('/unread-count', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!notificationController) {
    return res.status(500).json({ success: false, error: 'Notification service not initialized' });
  }
  await notificationController.getUnreadCount(req, res, next);
}));

/**
 * @route PUT /api/notifications/:id/read
 * @desc Mark notification as read
 * @access Private
 */
router.put('/:id/read', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!notificationController) {
    return res.status(500).json({ success: false, error: 'Notification service not initialized' });
  }
  await notificationController.markAsRead(req, res, next);
}));

/**
 * @route PUT /api/notifications/mark-all-read
 * @desc Mark all notifications as read
 * @access Private
 */
router.put('/mark-all-read', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!notificationController) {
    return res.status(500).json({ success: false, error: 'Notification service not initialized' });
  }
  await notificationController.markAllAsRead(req, res, next);
}));

/**
 * @route GET /api/notifications/preferences
 * @desc Get notification preferences
 * @access Private
 */
router.get('/preferences', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!notificationController) {
    return res.status(500).json({ success: false, error: 'Notification service not initialized' });
  }
  await notificationController.getPreferences(req, res, next);
}));

/**
 * @route PUT /api/notifications/preferences
 * @desc Update notification preferences
 * @access Private
 */
router.put('/preferences', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!notificationController) {
    return res.status(500).json({ success: false, error: 'Notification service not initialized' });
  }
  await notificationController.updatePreferences(req, res, next);
}));

/**
 * @route GET /api/notifications/stats
 * @desc Get notification statistics
 * @access Private (Admin only)
 */
router.get('/stats', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!notificationController) {
    return res.status(500).json({ success: false, error: 'Notification service not initialized' });
  }
  await notificationController.getStats(req, res, next);
}));

/**
 * @route POST /api/notifications/test
 * @desc Test notification system
 * @access Private (Admin only)
 */
router.post('/test', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!notificationController) {
    return res.status(500).json({ success: false, error: 'Notification service not initialized' });
  }
  await notificationController.testNotification(req, res, next);
}));

/**
 * @route GET /api/notifications/health
 * @desc Get notification system health
 * @access Private (Admin only)
 */
router.get('/health', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  if (!notificationController) {
    return res.status(500).json({ success: false, error: 'Notification service not initialized' });
  }
  await notificationController.getHealth(req, res, next);
}));

export default router;
