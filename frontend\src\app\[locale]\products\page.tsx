"use client"

import * as React from "react"
import { Navigation } from "@/components/ui/navigation"
import { Container } from "@/components/ui/container"
import { Button } from "@/components/ui/button"
import ProductCard from "@/components/ui/product-card"
import { VisuallyHidden } from "@/components/ui/visually-hidden"
import { Product3DViewerModal } from "@/components/ui/3d-viewer-modal"
import { QuoteRequestModal } from "@/components/ui/quote-request-modal"
import { FavoritesProvider, useFavorites } from "@/contexts/favorites-context"
import { useAuth } from "@/contexts/auth-context"
import { useSimpleTranslation } from "@/hooks/useSimpleTranslation"

// Mock data for products
const products = [
  {
    id: "1",
    name: "Carrara Beyaz Mermer",
    category: "Mermer",
    type: "ebatlı", // ebatlı veya blok
    price: { min: 45, max: 65, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.8,
    reviewCount: 127,
    location: "Afyon, Türkiye",
    producer: "ABC Mermer Ltd.",
    features: ["Cilalı", "60x60cm", "2cm Kalınlık"]
  },
  {
    id: "2",
    name: "Granit Siyah",
    category: "Granit",
    type: "ebatlı",
    price: { min: 55, max: 75, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.6,
    reviewCount: 89,
    location: "Kayseri, Türkiye",
    producer: "XYZ Granit A.Ş.",
    features: ["Cilalı", "40x40cm", "3cm Kalınlık"]
  },
  {
    id: "3",
    name: "Traverten Bej",
    category: "Traverten",
    type: "ebatlı",
    price: { min: 35, max: 50, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.7,
    reviewCount: 156,
    location: "Denizli, Türkiye",
    producer: "Traverten Pro Ltd.",
    features: ["Doğal", "30x60cm", "2cm Kalınlık"]
  },
  {
    id: "4",
    name: "Oniks Yeşil",
    category: "Oniks",
    type: "ebatlı",
    price: { min: 85, max: 120, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.9,
    reviewCount: 67,
    location: "Afyon, Türkiye",
    producer: "Oniks Dünyası A.Ş.",
    features: ["Şeffaf", "60x120cm", "2cm Kalınlık"]
  },
  {
    id: "5",
    name: "Mermer Gri",
    category: "Mermer",
    type: "ebatlı",
    price: { min: 40, max: 60, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.5,
    reviewCount: 98,
    location: "Bilecik, Türkiye",
    producer: "Gri Mermer Ltd.",
    features: ["Mat", "50x50cm", "2cm Kalınlık"]
  },
  {
    id: "6",
    name: "Granit Kırmızı",
    category: "Granit",
    type: "ebatlı",
    price: { min: 60, max: 80, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.4,
    reviewCount: 73,
    location: "Nevşehir, Türkiye",
    producer: "Kırmızı Granit A.Ş.",
    features: ["Cilalı", "60x60cm", "3cm Kalınlık"]
  },
  {
    id: "7",
    name: "Traverten Klasik",
    category: "Traverten",
    type: "ebatlı",
    price: { min: 30, max: 45, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.6,
    reviewCount: 112,
    location: "Denizli, Türkiye",
    producer: "Klasik Traverten Ltd.",
    features: ["Doğal", "40x60cm", "2cm Kalınlık"]
  },
  {
    id: "8",
    name: "Mermer Emperador",
    category: "Mermer",
    type: "ebatlı",
    price: { min: 70, max: 95, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.8,
    reviewCount: 89,
    location: "Isparta, Türkiye",
    producer: "Emperador Mermer A.Ş.",
    features: ["Cilalı", "60x120cm", "2cm Kalınlık"]
  },
  {
    id: "9",
    name: "Granit Beyaz",
    category: "Granit",
    type: "ebatlı",
    price: { min: 50, max: 70, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.5,
    reviewCount: 95,
    location: "Kayseri, Türkiye",
    producer: "Beyaz Granit Ltd.",
    features: ["Cilalı", "50x50cm", "3cm Kalınlık"]
  },
  // Blok Ürünleri
  {
    id: "11",
    name: "Afyon Beyaz Mermer Blok",
    category: "Mermer",
    type: "blok",
    price: { min: 800, max: 1200, currency: "$", unit: "ton" },
    image: "/api/placeholder/300/200",
    rating: 4.7,
    reviewCount: 34,
    location: "Afyon, Türkiye",
    producer: "Afyon Mermer Ocakları A.Ş.",
    features: ["Ham Blok", "2x1.5x1m", "Yüksek Kalite"],
    hasQuarry: true
  },
  {
    id: "12",
    name: "Denizli Traverten Blok",
    category: "Traverten",
    type: "blok",
    price: { min: 600, max: 900, currency: "$", unit: "ton" },
    image: "/api/placeholder/300/200",
    rating: 4.6,
    reviewCount: 28,
    location: "Denizli, Türkiye",
    producer: "Denizli Traverten Ocakları Ltd.",
    features: ["Ham Blok", "2.5x1.5x1m", "Doğal Renk"],
    hasQuarry: true
  },
  {
    id: "13",
    name: "Muğla Granit Blok",
    category: "Granit",
    type: "blok",
    price: { min: 1000, max: 1500, currency: "$", unit: "ton" },
    image: "/api/placeholder/300/200",
    rating: 4.8,
    reviewCount: 19,
    location: "Muğla, Türkiye",
    producer: "Muğla Granit Ocakları A.Ş.",
    features: ["Ham Blok", "2x1.5x1.2m", "Sert Yapı"],
    hasQuarry: true
  },
  {
    id: "14",
    name: "Oniks Bal Rengi",
    category: "Oniks",
    type: "ebatlı",
    price: { min: 90, max: 130, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.9,
    reviewCount: 45,
    location: "Afyon, Türkiye",
    producer: "Bal Oniks A.Ş.",
    features: ["Şeffaf", "60x60cm", "2cm Kalınlık"]
  },
  {
    id: "15",
    name: "Traverten Noce",
    category: "Traverten",
    type: "ebatlı",
    price: { min: 40, max: 55, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.7,
    reviewCount: 78,
    location: "Denizli, Türkiye",
    producer: "Noce Traverten Ltd.",
    features: ["Doğal", "30x60cm", "2cm Kalınlık"]
  },
  {
    id: "16",
    name: "Mermer Rosso",
    category: "Mermer",
    type: "ebatlı",
    price: { min: 65, max: 85, currency: "$", unit: "m²" },
    image: "/api/placeholder/300/200",
    rating: 4.6,
    reviewCount: 67,
    location: "Bilecik, Türkiye",
    producer: "Rosso Mermer A.Ş.",
    features: ["Cilalı", "60x60cm", "2cm Kalınlık"]
  },
  {
    id: "17",
    name: "Eskişehir Andezit Blok",
    category: "Andezit",
    type: "blok",
    price: { min: 400, max: 600, currency: "$", unit: "ton" },
    image: "/api/placeholder/300/200",
    rating: 4.5,
    reviewCount: 22,
    location: "Eskişehir, Türkiye",
    producer: "Eskişehir Andezit Ocakları Ltd.",
    features: ["Ham Blok", "2x1.5x1m", "Volkanik Yapı"],
    hasQuarry: true
  },
  {
    id: "18",
    name: "Afyon Sugar Mermer Blok",
    category: "Mermer",
    type: "blok",
    price: { min: 900, max: 1300, currency: "$", unit: "ton" },
    image: "/api/placeholder/300/200",
    rating: 4.8,
    reviewCount: 31,
    location: "Afyon, Türkiye",
    producer: "Sugar Mermer Ocakları A.Ş.",
    features: ["Ham Blok", "2.2x1.5x1m", "Premium Kalite"],
    hasQuarry: true
  }
]

function ProductsContent() {
  const { addToFavorites, removeFromFavorites, isFavorite } = useFavorites()
  const { isAuthenticated, user, showLoginModal, showCustomerRegisterModal, logout } = useAuth()
  const [selectedCategory, setSelectedCategory] = React.useState<string>("")
  const [selectedType, setSelectedType] = React.useState<string>("")
  const [selectedProduct, setSelectedProduct] = React.useState<typeof products[0] | null>(null)
  const [is3DViewerOpen, setIs3DViewerOpen] = React.useState(false)
  const [isQuoteModalOpen, setIsQuoteModalOpen] = React.useState(false)
  const [searchQuery, setSearchQuery] = React.useState<string>("")
  const { t } = useSimpleTranslation()

  const handleToggleFavorite = (productId: string) => {
    const product = products.find(p => p.id === productId)
    if (!product) return

    if (isFavorite(productId)) {
      removeFromFavorites(productId)
      console.log(`Removed ${product.name} from favorites`)
    } else {
      addToFavorites(product)
      console.log(`Added ${product.name} to favorites`)
    }
  }

  const handleView3D = (productId: string) => {
    const product = products.find(p => p.id === productId)
    if (product) {
      setSelectedProduct(product)
      setIs3DViewerOpen(true)
    }
  }

  const handleRequestQuote = (productId: string) => {
    // Auth kontrolü ProductCard'da yapılıyor, buraya geldiğinde zaten giriş yapmış
    const product = products.find(p => p.id === productId)
    if (product) {
      setSelectedProduct(product)
      setIsQuoteModalOpen(true)
    }
  }

  const filteredProducts = React.useMemo(() => {
    return products.filter(product => {
      // Category filter
      if (selectedCategory && product.category.toLowerCase() !== selectedCategory.toLowerCase()) {
        return false
      }

      // Type filter (ebatlı/blok)
      if (selectedType && product.type !== selectedType) {
        return false
      }

      // Search filter
      if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false
      }

      return true
    })
  }, [selectedCategory, selectedType, searchQuery])

  // Create navigation links with translations
  const navigationLinks = [
    { name: t('nav.home'), href: "/" },
    { name: t('nav.products'), href: "/products", active: true },
    { name: t('nav.3d_showroom'), href: "/3d-showroom" },
    { name: t('nav.news'), href: "/news" },
    { name: t('nav.about'), href: "/about" },
    { name: t('nav.contact'), href: "/contact" }
  ]

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50">
        <Navigation
          brand={{
            name: "Türkiye Doğal Taş Pazarı",
            href: "/"
          }}
          links={navigationLinks}
          actions={
            <div className="flex items-center gap-2">
              {!isAuthenticated ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={showLoginModal}
                  >
                    {t('auth.login')}
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={showCustomerRegisterModal}
                  >
                    {t('auth.register')}
                  </Button>
                </>
              ) : (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">
                    {t('auth.welcome')}, {user?.name}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={logout}
                  >
                    {t('auth.logout')}
                  </Button>
                </div>
              )}
            </div>
          }
        />

        <main id="main-content">
          <Container className="py-8">
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-stone-900 mb-4">
                {t('products.title')}
              </h1>
              <p className="text-lg text-stone-600 max-w-2xl">
                {t('products.subtitle')}
              </p>
            </div>

            {/* Filters Section */}
            <div className="mb-8 p-6 bg-white rounded-lg shadow-sm border border-stone-200">
              <h2 className="text-lg font-semibold text-stone-900 mb-4">{t('products.filters')}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Search */}
                <div>
                  <label className="block text-sm font-medium text-stone-700 mb-2">
                    {t('products.search')}
                  </label>
                  <input
                    type="text"
                    placeholder={t('products.search_placeholder')}
                    className="w-full px-3 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-stone-700 mb-2">
                    {t('products.category')}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                  >
                    <option value="">{t('products.all_categories')}</option>
                    <option value="mermer">{t('products.marble')}</option>
                    <option value="granit">{t('products.granite')}</option>
                    <option value="traverten">{t('products.travertine')}</option>
                    <option value="oniks">{t('products.onyx')}</option>
                    <option value="andezit">{t('products.andesite')}</option>
                  </select>
                </div>

                {/* Type */}
                <div>
                  <label className="block text-sm font-medium text-stone-700 mb-2">
                    {t('products.product_type')}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                    value={selectedType}
                    onChange={(e) => setSelectedType(e.target.value)}
                  >
                    <option value="">{t('products.all_types')}</option>
                    <option value="ebatlı">{t('products.sized_products')}</option>
                    <option value="blok">{t('products.block_products')}</option>
                  </select>
                </div>
              </div>

              {/* Filter Summary */}
              <div className="mt-4 flex items-center justify-between">
                <div className="text-sm text-stone-600">
                  {filteredProducts.length} {t('products.products_found')}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedCategory("")
                    setSelectedType("")
                    setSearchQuery("")
                  }}
                >
                  {t('products.clear_filters')}
                </Button>
              </div>
            </div>

            {/* Products Grid */}
            {filteredProducts.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12">
                {filteredProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    isFavorite={isFavorite(product.id)}
                    onRequestQuote={() => handleRequestQuote(product.id)}
                    onToggleFavorite={() => handleToggleFavorite(product.id)}
                    onView3D={() => handleView3D(product.id)}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-16 bg-white rounded-lg shadow-sm border border-stone-200 mb-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold text-stone-700 mb-2">
                  {t('products.no_products')}
                </h3>
                <p className="text-stone-600 mb-6 max-w-md mx-auto">
                  {t('products.no_products_description')}
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedCategory("")
                    setSelectedType("")
                    setSearchQuery("")
                  }}
                >
                  {t('products.clear_filters')}
                </Button>
              </div>
            )}

            {/* 3D Viewer Modal */}
            <Product3DViewerModal
              isOpen={is3DViewerOpen}
              onClose={() => setIs3DViewerOpen(false)}
              product={selectedProduct}
            />

            {/* Quote Request Modal */}
            <QuoteRequestModal
              isOpen={isQuoteModalOpen}
              onClose={() => setIsQuoteModalOpen(false)}
              product={selectedProduct}
            />
          </Container>
        </main>
      </div>
    </>
  )
}

export default function ProductsPage() {
  return (
    <FavoritesProvider>
      <ProductsContent />
    </FavoritesProvider>
  )
}
