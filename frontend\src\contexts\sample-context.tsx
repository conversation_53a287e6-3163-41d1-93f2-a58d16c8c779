'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

export interface SampleRequest {
  id: string;
  quoteRequestId: string;
  quoteId: string;
  customerId: string;
  producerId: string;
  requestedProducts: {
    productId: string;
    productName: string;
    sampleSize: string;
    specifications: string;
  }[];
  sampleSpecifications?: string;
  deliveryAddress: {
    name: string;
    address: string;
    city: string;
    country: string;
    phone: string;
  };
  status: 'pending' | 'approved' | 'rejected' | 'preparing' | 'shipped' | 'delivered' | 'evaluated';
  adminNotes?: string;
  rejectionReason?: string;
  producerResponse?: any;
  preparationTimeDays?: number;
  shippingInfo?: any;
  customerEvaluation?: {
    rating: number;
    feedback: string;
    orderNotes?: string;
  };
  willOrder?: boolean;
  createdAt: Date;
  updatedAt: Date;
  approvedAt?: Date;
  shippedAt?: Date;
  deliveredAt?: Date;
  evaluatedAt?: Date;
  tracking?: SampleRequestTracking[];
}

export interface SampleRequestTracking {
  id: string;
  sampleRequestId: string;
  status: string;
  notes?: string;
  createdBy?: string;
  createdByType?: 'admin' | 'customer' | 'producer';
  createdAt: Date;
}

export interface CreateSampleRequestData {
  quoteRequestId: string;
  quoteId: string;
  customerId: string;
  producerId: string;
  requestedProducts: {
    productId: string;
    productName: string;
    sampleSize: string;
    specifications: string;
  }[];
  deliveryAddress: {
    name: string;
    address: string;
    city: string;
    country: string;
    phone: string;
  };
  specialRequirements?: string;
}

export interface SampleEvaluationData {
  rating: number;
  feedback: string;
  willOrder: boolean;
  orderNotes?: string;
}

interface SampleContextType {
  sampleRequests: SampleRequest[];
  isLoading: boolean;
  error: string | null;
  
  // Customer functions
  createSampleRequest: (data: CreateSampleRequestData) => Promise<SampleRequest>;
  getSampleRequestsByCustomer: (customerId: string) => Promise<SampleRequest[]>;
  getSampleRequestDetail: (sampleRequestId: string) => Promise<SampleRequest>;
  evaluateSample: (sampleRequestId: string, evaluation: SampleEvaluationData) => Promise<SampleRequest>;
  
  // Admin functions
  getAllSampleRequests: (filters?: { status?: string; page?: number; limit?: number }) => Promise<any>;
  approveSampleRequest: (sampleRequestId: string, approved: boolean, notes?: string, rejectionReason?: string) => Promise<SampleRequest>;
  
  // Producer functions
  getSampleRequestsByProducer: (producerId: string, status?: string) => Promise<SampleRequest[]>;
  approveSampleRequestByProducer: (sampleRequestId: string, approved: boolean, notes?: string, rejectionReason?: string, preparationDays?: number) => Promise<SampleRequest>;
  updateSampleStatus: (sampleRequestId: string, status: string, data?: any) => Promise<SampleRequest>;
}

const SampleContext = createContext<SampleContextType | undefined>(undefined);

export const useSample = () => {
  const context = useContext(SampleContext);
  if (!context) {
    throw new Error('useSample must be used within a SampleProvider');
  }
  return context;
};

interface SampleProviderProps {
  children: React.ReactNode;
}

export const SampleProvider: React.FC<SampleProviderProps> = ({ children }) => {
  const [sampleRequests, setSampleRequests] = useState<SampleRequest[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8001';

  // Customer functions
  const createSampleRequest = async (data: CreateSampleRequestData): Promise<SampleRequest> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/samples/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to create sample request');
      }

      const result = await response.json();
      const newSampleRequest = result.data;
      
      setSampleRequests(prev => [...prev, newSampleRequest]);
      return newSampleRequest;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const getSampleRequestsByCustomer = async (customerId: string): Promise<SampleRequest[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/samples/customer/${customerId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch sample requests');
      }

      const result = await response.json();
      const samples = result.data;
      
      setSampleRequests(samples);
      return samples;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const getSampleRequestDetail = async (sampleRequestId: string): Promise<SampleRequest> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/samples/${sampleRequestId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch sample request detail');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const evaluateSample = async (sampleRequestId: string, evaluation: SampleEvaluationData): Promise<SampleRequest> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/samples/${sampleRequestId}/evaluate`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(evaluation),
      });

      if (!response.ok) {
        throw new Error('Failed to evaluate sample');
      }

      const result = await response.json();
      const updatedSample = result.data;
      
      setSampleRequests(prev => 
        prev.map(sample => 
          sample.id === sampleRequestId ? updatedSample : sample
        )
      );
      
      return updatedSample;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Admin functions
  const getAllSampleRequests = async (filters?: { status?: string; page?: number; limit?: number }) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      if (filters?.status) params.append('status', filters.status);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`${API_BASE_URL}/api/samples/admin/all?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch admin sample requests');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const approveSampleRequest = async (
    sampleRequestId: string, 
    approved: boolean, 
    notes?: string, 
    rejectionReason?: string
  ): Promise<SampleRequest> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/samples/admin/${sampleRequestId}/approve`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ approved, notes, rejectionReason }),
      });

      if (!response.ok) {
        throw new Error('Failed to approve/reject sample request');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Producer functions
  const getSampleRequestsByProducer = async (producerId: string, status?: string): Promise<SampleRequest[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const params = status ? `?status=${status}` : '';
      const response = await fetch(`${API_BASE_URL}/api/samples/producer/${producerId}${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch producer sample requests');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const approveSampleRequestByProducer = async (
    sampleRequestId: string,
    approved: boolean,
    notes?: string,
    rejectionReason?: string,
    preparationDays?: number
  ): Promise<SampleRequest> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/api/samples/producer/${sampleRequestId}/approve`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ approved, notes, rejectionReason, preparationDays }),
      });

      if (!response.ok) {
        throw new Error('Failed to approve/reject sample request');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateSampleStatus = async (sampleRequestId: string, status: string, data?: any): Promise<SampleRequest> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/api/samples/producer/${sampleRequestId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status, ...data }),
      });

      if (!response.ok) {
        throw new Error('Failed to update sample status');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const value: SampleContextType = {
    sampleRequests,
    isLoading,
    error,
    createSampleRequest,
    getSampleRequestsByCustomer,
    getSampleRequestDetail,
    evaluateSample,
    getAllSampleRequests,
    approveSampleRequest,
    getSampleRequestsByProducer,
    approveSampleRequestByProducer,
    updateSampleStatus,
  };

  return (
    <SampleContext.Provider value={value}>
      {children}
    </SampleContext.Provider>
  );
};
