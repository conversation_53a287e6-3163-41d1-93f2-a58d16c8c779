'use client'

import * as React from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Package, 
  Plus, 
  Trash2, 
  Calendar, 
  DollarSign,
  AlertCircle,
  CheckCircle,
  Calculator,
  Send,
  Clock
} from 'lucide-react'

interface MultiDeliveryQuoteModalProps {
  isOpen: boolean
  onClose: () => void
  quoteRequest: any
  onSendQuote: (quoteData: MultiDeliveryQuoteData) => void
}

interface DeliveryPackageQuote {
  packageNumber: number
  quantity: number
  amount: number
  productionDuration: number // days
  deliveryDate: string
  deliveryMethod: 'factory_pickup' | 'delivery' | 'partial_delivery'
  paymentTerms: {
    advancePercentage: number
    deliveryPercentage: number
    completionPercentage: number
  }
  notes: string
}

interface MultiDeliveryQuoteData {
  quoteType: 'single' | 'multi'
  totalAmount: number
  currency: string
  deliveryPackages: DeliveryPackageQuote[]
  generalTerms: string
  validityDays: number
}

export function MultiDeliveryQuoteModal({
  isOpen,
  onClose,
  quoteRequest,
  onSendQuote
}: MultiDeliveryQuoteModalProps) {
  const [quoteType, setQuoteType] = React.useState<'single' | 'multi'>('single')
  const [packages, setPackages] = React.useState<DeliveryPackageQuote[]>([])
  const [singleQuoteAmount, setSingleQuoteAmount] = React.useState(0)
  const [currency, setCurrency] = React.useState('USD')
  const [generalTerms, setGeneralTerms] = React.useState('')
  const [validityDays, setValidityDays] = React.useState(30)
  const [isLoading, setIsLoading] = React.useState(false)

  React.useEffect(() => {
    if (isOpen && quoteRequest) {
      // Reset form
      setQuoteType('single')
      setPackages([])
      setSingleQuoteAmount(0)
      setGeneralTerms('')
      setValidityDays(30)
    }
  }, [isOpen, quoteRequest])

  const generateMultiDeliveryPackages = () => {
    if (!quoteRequest) return

    const packageCount = 5 // Default to 5 packages
    const quantityPerPackage = Math.ceil(quoteRequest.quantity / packageCount)
    const estimatedTotalAmount = quoteRequest.quantity * 50 // $50 per m² estimate
    const amountPerPackage = Math.ceil(estimatedTotalAmount / packageCount)
    
    const newPackages: DeliveryPackageQuote[] = []
    let remainingQuantity = quoteRequest.quantity
    let remainingAmount = estimatedTotalAmount

    for (let i = 1; i <= packageCount; i++) {
      const isLastPackage = i === packageCount
      const packageQuantity = isLastPackage ? remainingQuantity : Math.min(quantityPerPackage, remainingQuantity)
      const packageAmount = isLastPackage ? remainingAmount : Math.min(amountPerPackage, remainingAmount)

      // Calculate delivery dates with 2-week intervals
      const deliveryDate = new Date()
      deliveryDate.setDate(deliveryDate.getDate() + (i * 14))

      newPackages.push({
        packageNumber: i,
        quantity: packageQuantity,
        amount: packageAmount,
        productionDuration: 10, // 10 days production
        deliveryDate: deliveryDate.toISOString().split('T')[0],
        deliveryMethod: 'delivery',
        paymentTerms: {
          advancePercentage: 50,
          deliveryPercentage: 50,
          completionPercentage: 0
        },
        notes: `Paket ${i} - 2 haftalık aralıklarla teslimat`
      })

      remainingQuantity -= packageQuantity
      remainingAmount -= packageAmount
    }

    setPackages(newPackages)
  }

  const addPackage = () => {
    const newPackage: DeliveryPackageQuote = {
      packageNumber: packages.length + 1,
      quantity: 0,
      amount: 0,
      productionDuration: 10,
      deliveryDate: '',
      deliveryMethod: 'delivery',
      paymentTerms: {
        advancePercentage: 50,
        deliveryPercentage: 50,
        completionPercentage: 0
      },
      notes: ''
    }
    setPackages([...packages, newPackage])
  }

  const removePackage = (index: number) => {
    const newPackages = packages.filter((_, i) => i !== index)
    // Renumber packages
    newPackages.forEach((pkg, i) => {
      pkg.packageNumber = i + 1
    })
    setPackages(newPackages)
  }

  const updatePackage = (index: number, field: keyof DeliveryPackageQuote, value: any) => {
    const newPackages = [...packages]
    if (field === 'paymentTerms') {
      newPackages[index].paymentTerms = { ...newPackages[index].paymentTerms, ...value }
    } else {
      (newPackages[index] as any)[field] = value
    }
    setPackages(newPackages)
  }

  const validateQuote = () => {
    if (quoteType === 'single') {
      return {
        isValid: singleQuoteAmount > 0,
        totalAmount: singleQuoteAmount
      }
    } else {
      const totalQuantity = packages.reduce((sum, pkg) => sum + pkg.quantity, 0)
      const totalAmount = packages.reduce((sum, pkg) => sum + pkg.amount, 0)
      
      return {
        isValid: totalQuantity === quoteRequest?.quantity && totalAmount > 0 && packages.length > 0,
        totalAmount,
        quantityDiff: totalQuantity - (quoteRequest?.quantity || 0)
      }
    }
  }

  const handleSendQuote = async () => {
    const validation = validateQuote()
    if (!validation.isValid) {
      alert('Lütfen teklif bilgilerini kontrol edin!')
      return
    }

    setIsLoading(true)
    try {
      const quoteData: MultiDeliveryQuoteData = {
        quoteType,
        totalAmount: validation.totalAmount,
        currency,
        deliveryPackages: quoteType === 'multi' ? packages : [],
        generalTerms,
        validityDays
      }

      await onSendQuote(quoteData)
      onClose()
    } catch (error) {
      console.error('Error sending quote:', error)
      alert('Teklif gönderilirken hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const validation = validateQuote()

  if (!quoteRequest) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[1000px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Çoklu Teslimat Teklifi - Talep #{quoteRequest.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Quote Request Summary */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-blue-800">Teklif Talebi Özeti</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-blue-700">Müşteri:</span>
                <p className="text-blue-900">{quoteRequest.customerName}</p>
              </div>
              <div>
                <span className="font-medium text-blue-700">Ürün:</span>
                <p className="text-blue-900">{quoteRequest.productName}</p>
              </div>
              <div>
                <span className="font-medium text-blue-700">Miktar:</span>
                <p className="text-blue-900">{quoteRequest.quantity} m²</p>
              </div>
              <div>
                <span className="font-medium text-blue-700">Talep Tarihi:</span>
                <p className="text-blue-900">{new Date(quoteRequest.requestDate).toLocaleDateString('tr-TR')}</p>
              </div>
            </CardContent>
          </Card>

          {/* Quote Type Selection */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Teklif Türü</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div 
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    quoteType === 'single' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setQuoteType('single')}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <input
                      type="radio"
                      checked={quoteType === 'single'}
                      onChange={() => setQuoteType('single')}
                      className="text-blue-600"
                    />
                    <span className="font-medium">Tek Teslimat</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Geleneksel tek seferde teslimat sistemi. Tüm ürün aynı anda teslim edilir.
                  </p>
                </div>

                <div 
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    quoteType === 'multi' ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setQuoteType('multi')}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <input
                      type="radio"
                      checked={quoteType === 'multi'}
                      onChange={() => setQuoteType('multi')}
                      className="text-purple-600"
                    />
                    <span className="font-medium">Çoklu Teslimat</span>
                    <Badge className="bg-purple-100 text-purple-800">Önerilen</Badge>
                  </div>
                  <p className="text-sm text-gray-600">
                    Büyük siparişler için paket paket teslimat. Esnek ödeme ve teslimat tarihleri.
                  </p>
                </div>
              </div>

              {quoteType === 'multi' && (
                <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-purple-800">Çoklu Teslimat Avantajları</h4>
                      <ul className="text-sm text-purple-700 mt-1 space-y-1">
                        <li>• Müşteri için daha esnek ödeme planı</li>
                        <li>• Erken teslimatlarla projeye hızlı başlangıç</li>
                        <li>• Risk dağıtımı ve daha iyi nakit akışı</li>
                      </ul>
                    </div>
                    <Button
                      size="sm"
                      onClick={generateMultiDeliveryPackages}
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      <Calculator className="w-4 h-4 mr-1" />
                      Otomatik Oluştur
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Single Quote */}
          {quoteType === 'single' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  Tek Teslimat Teklifi
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="singleAmount">Toplam Tutar</Label>
                  <Input
                    id="singleAmount"
                    type="number"
                    value={singleQuoteAmount}
                    onChange={(e) => setSingleQuoteAmount(parseInt(e.target.value) || 0)}
                    placeholder="Toplam teklif tutarı"
                  />
                </div>
                <div>
                  <Label htmlFor="currency">Para Birimi</Label>
                  <Select value={currency} onValueChange={setCurrency}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD ($)</SelectItem>
                      <SelectItem value="EUR">EUR (€)</SelectItem>
                      <SelectItem value="TRY">TRY (₺)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="validity">Geçerlilik (Gün)</Label>
                  <Input
                    id="validity"
                    type="number"
                    value={validityDays}
                    onChange={(e) => setValidityDays(parseInt(e.target.value) || 30)}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Multi Delivery Packages */}
          {quoteType === 'multi' && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Teslimat Paketleri ({packages.length})</h3>
                <Button onClick={addPackage} size="sm">
                  <Plus className="w-4 h-4 mr-1" />
                  Paket Ekle
                </Button>
              </div>

              {/* Validation Summary */}
              {packages.length > 0 && (
                <Card className={validation.isValid ? 'bg-green-50 border-green-200 mb-4' : 'bg-red-50 border-red-200 mb-4'}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      {validation.isValid ? (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      ) : (
                        <AlertCircle className="w-5 h-5 text-red-600" />
                      )}
                      <div className="flex-1">
                        <p className={`font-medium ${validation.isValid ? 'text-green-800' : 'text-red-800'}`}>
                          {validation.isValid ? 'Paket toplamları doğru' : 'Paket toplamları hatalı'}
                        </p>
                        <div className="text-sm mt-1">
                          <span className="font-medium">Toplam Miktar:</span> {packages.reduce((sum, pkg) => sum + pkg.quantity, 0)} m²
                          {validation.quantityDiff !== 0 && (
                            <span className="text-red-600 ml-2">
                              (Fark: {validation.quantityDiff > 0 ? '+' : ''}{validation.quantityDiff} m²)
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {packages.map((pkg, index) => (
                  <Card key={index} className="border-gray-200">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">Paket #{pkg.packageNumber}</CardTitle>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removePackage(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      {/* Basic Info */}
                      <div className="space-y-3">
                        <div>
                          <Label>Miktar (m²)</Label>
                          <Input
                            type="number"
                            value={pkg.quantity}
                            onChange={(e) => updatePackage(index, 'quantity', parseInt(e.target.value) || 0)}
                          />
                        </div>
                        <div>
                          <Label>Tutar ($)</Label>
                          <Input
                            type="number"
                            value={pkg.amount}
                            onChange={(e) => updatePackage(index, 'amount', parseInt(e.target.value) || 0)}
                          />
                        </div>
                      </div>

                      {/* Timing */}
                      <div className="space-y-3">
                        <div>
                          <Label>Üretim Süresi (Gün)</Label>
                          <Input
                            type="number"
                            value={pkg.productionDuration}
                            onChange={(e) => updatePackage(index, 'productionDuration', parseInt(e.target.value) || 0)}
                          />
                        </div>
                        <div>
                          <Label>Teslimat Tarihi</Label>
                          <Input
                            type="date"
                            value={pkg.deliveryDate}
                            onChange={(e) => updatePackage(index, 'deliveryDate', e.target.value)}
                          />
                        </div>
                      </div>

                      {/* Delivery Method */}
                      <div className="space-y-3">
                        <div>
                          <Label>Teslimat Yöntemi</Label>
                          <Select
                            value={pkg.deliveryMethod}
                            onValueChange={(value) => updatePackage(index, 'deliveryMethod', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="factory_pickup">Fabrika Teslim</SelectItem>
                              <SelectItem value="delivery">Teslimat</SelectItem>
                              <SelectItem value="partial_delivery">Kısmi Teslimat</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <Label>Avans %</Label>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={pkg.paymentTerms.advancePercentage}
                              onChange={(e) => updatePackage(index, 'paymentTerms', {
                                advancePercentage: parseInt(e.target.value) || 0
                              })}
                            />
                          </div>
                          <div>
                            <Label>Teslimat %</Label>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={pkg.paymentTerms.deliveryPercentage}
                              onChange={(e) => updatePackage(index, 'paymentTerms', {
                                deliveryPercentage: parseInt(e.target.value) || 0
                              })}
                            />
                          </div>
                        </div>
                      </div>

                      {/* Notes */}
                      <div>
                        <Label>Paket Notları</Label>
                        <Textarea
                          value={pkg.notes}
                          onChange={(e) => updatePackage(index, 'notes', e.target.value)}
                          rows={4}
                          placeholder="Bu paket için özel notlar..."
                        />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* General Terms */}
          <Card>
            <CardHeader>
              <CardTitle>Genel Şartlar ve Notlar</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={generalTerms}
                onChange={(e) => setGeneralTerms(e.target.value)}
                rows={4}
                placeholder="Teklif ile ilgili genel şartlar, özel durumlar, garanti bilgileri vb..."
              />
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              İptal
            </Button>
            <Button
              onClick={handleSendQuote}
              disabled={!validation.isValid || isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Gönderiliyor...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Teklif Gönder
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
