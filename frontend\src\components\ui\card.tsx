import * as React from "react"
import { cn } from "@/lib/utils"

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'product'
  hoverable?: boolean
}

/**
 * Card component following RFC-004 UI/UX Design System
 * Natural Stone Marketplace themed card with hover effects
 */
const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', hoverable = false, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        [
          // Base styles
          "bg-[var(--bg-primary)] text-[var(--text-primary)]",
          "border border-[var(--border-primary)]",
          "rounded-[var(--radius-lg)] overflow-hidden",
          "transition-all duration-300 ease-in-out",

          // Variant styles
          variant === 'default' && "shadow-[var(--shadow)]",
          variant === 'elevated' && "shadow-[var(--shadow-lg)]",
          variant === 'outlined' && "border-2 shadow-none",
          variant === 'product' && [
            "shadow-[var(--shadow)]",
            "border-[var(--gray-200)]"
          ],

          // Hoverable styles
          hoverable && [
            "cursor-pointer",
            "hover:transform hover:-translate-y-1",
            "hover:shadow-[var(--shadow-lg)]",
            variant === 'product' && "hover:shadow-[var(--shadow-xl)]"
          ]
        ].filter(Boolean).flat(),
        className
      )}
      {...props}
    />
  )
)
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex flex-col space-y-1.5 p-[var(--space-6)]",
      className
    )}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      [
        "text-[var(--text-lg)] font-[var(--font-semibold)]",
        "text-[var(--text-primary)] leading-none tracking-tight",
        "mb-[var(--space-2)]"
      ],
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn(
      "text-[var(--text-sm)] text-[var(--text-secondary)] leading-relaxed",
      className
    )}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("p-[var(--space-6)] pt-0", className)}
    {...props}
  />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex items-center p-[var(--space-6)] pt-0 gap-[var(--space-2)]",
      className
    )}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }
