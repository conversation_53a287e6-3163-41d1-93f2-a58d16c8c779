// AWS SDK imports disabled for now - using local storage only
// import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
// import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import multer from 'multer';
// import multerS3 from 'multer-s3';
import sharp from 'sharp';
import path from 'path';
import fs from 'fs';
import { promisify } from 'util';

const unlinkAsync = promisify(fs.unlink);

export interface FileUploadConfig {
  useS3: boolean;
  // s3Config?: {
  //   accessKeyId: string;
  //   secretAccessKey: string;
  //   region: string;
  //   bucket: string;
  // };
  localConfig?: {
    uploadPath: string;
  };
  maxFileSize: number;
  allowedTypes: string[];
}

export interface UploadedFile {
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  key?: string; // S3 key
  path?: string; // Local path
}

export interface ImageProcessingOptions {
  resize?: {
    width?: number;
    height?: number;
    fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  };
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
  generateThumbnail?: boolean;
  thumbnailSize?: { width: number; height: number };
}

export class FileUploadService {
  // private s3Client?: S3Client;
  private config: FileUploadConfig;

  constructor(config: FileUploadConfig) {
    this.config = config;

    // Temporarily disable S3 support
    // if (config.useS3 && config.s3Config) {
    //   this.s3Client = new S3Client({
    //     region: config.s3Config.region,
    //     credentials: {
    //       accessKeyId: config.s3Config.accessKeyId,
    //       secretAccessKey: config.s3Config.secretAccessKey,
    //     },
    //   });
    // }
  }

  /**
   * Get multer configuration for file uploads
   */
  getMulterConfig(options?: { 
    fieldName?: string; 
    maxCount?: number; 
    fileFilter?: (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => void;
  }) {
    const { fieldName = 'file', maxCount = 1, fileFilter } = options || {};

    const defaultFileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
      const allowedTypes = this.config.allowedTypes;
      const ext = path.extname(file.originalname).toLowerCase().substring(1);
      const mimetype = file.mimetype;

      if (allowedTypes.includes(ext) || allowedTypes.includes(mimetype)) {
        cb(null, true);
      } else {
        cb(new Error(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`));
      }
    };

    // Temporarily disable S3 support - use local storage only
    // if (this.config.useS3 && this.s3Client && this.config.s3Config) {
    //   // S3 storage configuration
    //   const storage = multerS3({
    //     s3: this.s3Client,
    //     bucket: this.config.s3Config.bucket,
    //     key: (req, file, cb) => {
    //       const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    //       const ext = path.extname(file.originalname);
    //       const key = `uploads/${Date.now()}/${file.fieldname}-${uniqueSuffix}${ext}`;
    //       cb(null, key);
    //     },
    //     contentType: multerS3.AUTO_CONTENT_TYPE,
    //     metadata: (req, file, cb) => {
    //       cb(null, {
    //         originalName: file.originalname,
    //         uploadedAt: new Date().toISOString(),
    //       });
    //     },
    //   });

    //   return multer({
    //     storage,
    //     limits: { fileSize: this.config.maxFileSize },
    //     fileFilter: fileFilter || defaultFileFilter,
    //   });
    // } else {
      // Local storage configuration
      const uploadPath = this.config.localConfig?.uploadPath || 'uploads';
      
      // Ensure upload directory exists
      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true });
      }

      const storage = multer.diskStorage({
        destination: (req, file, cb) => {
          cb(null, uploadPath);
        },
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
          const ext = path.extname(file.originalname);
          cb(null, file.fieldname + '-' + uniqueSuffix + ext);
        },
      });

      return multer({
        storage,
        limits: { fileSize: this.config.maxFileSize },
        fileFilter: fileFilter || defaultFileFilter,
      });
    }

  /**
   * Process uploaded image with optimization
   */
  public async processImage(file: Express.Multer.File, options?: ImageProcessingOptions): Promise<UploadedFile[]> {
    try {
      const results: UploadedFile[] = [];
      const uploadDir = this.config.localConfig?.uploadPath || 'uploads';

      // Ensure upload directory exists
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      // Read the uploaded file
      const imageBuffer = file.buffer || fs.readFileSync(file.path);
      let sharpInstance = sharp(imageBuffer);

      // Apply resize if specified
      if (options?.resize) {
        sharpInstance = sharpInstance.resize(options.resize.width, options.resize.height, {
          fit: options.resize.fit || 'cover'
        });
      }

      // Apply quality and format
      if (options?.format) {
        sharpInstance = sharpInstance.toFormat(options.format, { quality: options.quality || 80 });
      }

      // Process main image
      const mainFilename = this.generateFilename(file.originalname);
      const mainPath = path.join(uploadDir, mainFilename);

      await sharpInstance.toFile(mainPath);

      results.push({
        filename: mainFilename,
        originalName: file.originalname,
        mimetype: `image/${options?.format || 'jpeg'}`,
        size: fs.statSync(mainPath).size,
        url: `/uploads/${mainFilename}`,
        path: mainPath
      });

      // Generate thumbnail if requested
      if (options?.generateThumbnail) {
        const thumbFilename = `thumb-${mainFilename}`;
        const thumbPath = path.join(uploadDir, thumbFilename);

        await sharp(imageBuffer)
          .resize(options.thumbnailSize?.width || 200, options.thumbnailSize?.height || 200, { fit: 'cover' })
          .toFormat(options?.format || 'jpeg', { quality: 70 })
          .toFile(thumbPath);

        results.push({
          filename: thumbFilename,
          originalName: `thumb-${file.originalname}`,
          mimetype: `image/${options?.format || 'jpeg'}`,
          size: fs.statSync(thumbPath).size,
          url: `/uploads/${thumbFilename}`,
          path: thumbPath
        });
      }

      // Clean up temporary file
      if (file.path && fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }

      return results;
    } catch (error) {
      console.error('Image processing error:', error);
      throw new Error(`Image processing failed: ${error}`);
    }
  }

  /**
   * Generate unique filename
   */
  private generateFilename(originalname: string, suffix: string = ''): string {
    const ext = path.extname(originalname);
    const name = path.basename(originalname, ext);
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);

    return `${name}${suffix}_${timestamp}_${random}${ext}`;
  }

  /**
   * Delete file from storage
   */
  public async deleteFile(fileKey: string): Promise<void> {
    try {
      const uploadDir = this.config.localConfig?.uploadPath || 'uploads';
      const filePath = path.join(uploadDir, fileKey);
      if (fs.existsSync(filePath)) {
        await unlinkAsync(filePath);
      }
    } catch (error) {
      console.error('File deletion error:', error);
      throw new Error(`File deletion failed: ${error}`);
    }
  }

  /**
   * Get signed URL for file access
   */
  public async getSignedUrl(fileKey: string, expiresIn: number = 3600): Promise<string> {
    try {
      // For local storage, return direct URL
      return `/uploads/${fileKey}`;
    } catch (error) {
      console.error('Signed URL generation error:', error);
      throw new Error(`Signed URL generation failed: ${error}`);
    }
  }
}
