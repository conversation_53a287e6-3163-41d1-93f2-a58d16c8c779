# Payment System Database Migration Script
# Bu script ödeme sistemi için gerekli migration'ları çalıştırır

param(
    [switch]$DryRun = $false,
    [switch]$Force = $false,
    [string]$Environment = "development"
)

# Colors for output
$Red = "`e[31m"
$Green = "`e[32m"
$Yellow = "`e[33m"
$Blue = "`e[34m"
$Reset = "`e[0m"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = $Reset)
    Write-Host "$Color$Message$Reset"
}

function Write-Success { param([string]$Message) Write-ColorOutput $Message $Green }
function Write-Error { param([string]$Message) Write-ColorOutput $Message $Red }
function Write-Warning { param([string]$Message) Write-ColorOutput $Message $Yellow }
function Write-Info { param([string]$Message) Write-ColorOutput $Message $Blue }

Write-Info "🗄️ Payment System Database Migration Script"
Write-Info "Environment: $Environment"

if ($DryRun) {
    Write-Warning "⚠️ DRY RUN MODE - No actual changes will be made"
}

# Check if we're in the correct directory
if (!(Test-Path "prisma/schema.prisma")) {
    Write-Error "❌ Error: prisma/schema.prisma not found. Please run this script from the backend directory."
    exit 1
}

# Check database connection
Write-Info "🔍 Checking database connection..."
try {
    $dbStatus = npx prisma db execute --stdin --preview-feature <<< "SELECT 1;"
    Write-Success "✅ Database connection successful"
} catch {
    Write-Error "❌ Database connection failed. Please check your DATABASE_URL in .env file."
    Write-Error "Error: $_"
    exit 1
}

# Backup database (production only)
if ($Environment -eq "production" -and !$DryRun) {
    Write-Info "📦 Creating database backup..."
    $backupFile = "backup_payment_migration_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"
    try {
        # PostgreSQL backup command
        $env:PGPASSWORD = $env:DATABASE_PASSWORD
        pg_dump -h $env:DATABASE_HOST -U $env:DATABASE_USER -d $env:DATABASE_NAME > "backups/$backupFile"
        Write-Success "✅ Database backup created: $backupFile"
    } catch {
        Write-Error "❌ Failed to create database backup: $_"
        if (!$Force) {
            Write-Error "Use -Force to continue without backup"
            exit 1
        }
    }
}

# Generate Prisma client
Write-Info "🔧 Generating Prisma client..."
try {
    npx prisma generate
    Write-Success "✅ Prisma client generated"
} catch {
    Write-Error "❌ Failed to generate Prisma client: $_"
    exit 1
}

# Run Prisma migrations
Write-Info "🚀 Running Prisma migrations..."
try {
    if ($DryRun) {
        npx prisma migrate diff --from-empty --to-schema-datamodel prisma/schema.prisma
    } else {
        npx prisma migrate dev --name "payment_system_enhancement"
    }
    Write-Success "✅ Prisma migrations completed"
} catch {
    Write-Error "❌ Prisma migration failed: $_"
    exit 1
}

# Run custom payment system migrations
$migrationFiles = @(
    "prisma/migrations/20250113_payment_system_enhancement/migration.sql",
    "prisma/migrations/20250113_multi_delivery_payment_enhancement/migration.sql"
)

foreach ($migrationFile in $migrationFiles) {
    if (Test-Path $migrationFile) {
        Write-Info "📄 Running custom migration: $migrationFile"
        try {
            if ($DryRun) {
                Write-Info "Would execute: $migrationFile"
                Get-Content $migrationFile | Select-Object -First 10
                Write-Info "... (showing first 10 lines only)"
            } else {
                $migrationContent = Get-Content $migrationFile -Raw
                npx prisma db execute --stdin --preview-feature <<< $migrationContent
                Write-Success "✅ Custom migration completed: $migrationFile"
            }
        } catch {
            Write-Error "❌ Custom migration failed: $migrationFile"
            Write-Error "Error: $_"
            exit 1
        }
    } else {
        Write-Warning "⚠️ Migration file not found: $migrationFile"
    }
}

# Verify migration results
Write-Info "🔍 Verifying migration results..."
try {
    $verificationQueries = @(
        "SELECT COUNT(*) as payment_count FROM payments;",
        "SELECT COUNT(*) as commission_count FROM commission_tracking;",
        "SELECT COUNT(*) as payment_schedule_count FROM payment_schedules;",
        "SELECT COUNT(*) as payment_tracking_count FROM payment_tracking;",
        "SELECT COUNT(*) as escrow_log_count FROM escrow_transaction_log;"
    )
    
    foreach ($query in $verificationQueries) {
        if (!$DryRun) {
            $result = npx prisma db execute --stdin --preview-feature <<< $query
            Write-Success "✅ $query -> $result"
        }
    }
} catch {
    Write-Warning "⚠️ Verification queries failed (this might be normal for new installations): $_"
}

# Check for payment system enums
Write-Info "🔍 Checking payment system enums..."
try {
    $enumCheck = npx prisma db execute --stdin --preview-feature <<< "SELECT unnest(enum_range(NULL::\"PaymentType\"));"
    Write-Success "✅ PaymentType enum values: $enumCheck"
} catch {
    Write-Warning "⚠️ PaymentType enum check failed: $_"
}

# Summary
Write-Success "🎉 Payment System Migration Completed Successfully!"
Write-Info "📋 Migration Summary:"
Write-Info "   ✅ PaymentType enum added (FULL_PAYMENT, ADVANCE_PAYMENT, DELIVERY_PAYMENT, FINAL_PAYMENT, COMMISSION_PAYMENT)"
Write-Info "   ✅ CommissionTracking table created (PRD: m² başına $1, ton başına $10)"
Write-Info "   ✅ PaymentSchedule table created for multi-delivery orders"
Write-Info "   ✅ PaymentTracking table created for status change tracking"
Write-Info "   ✅ EscrowTransactionLog table created for escrow audit trail"
Write-Info "   ✅ Payment table enhanced with multi-delivery support"
Write-Info "   ✅ Automatic triggers created for commission calculation"
Write-Info "   ✅ Performance indexes added"

Write-Info "🔧 Next Steps:"
Write-Info "   1. Update your application code to use new PaymentType enum"
Write-Info "   2. Test commission calculation with sample orders"
Write-Info "   3. Verify multi-delivery payment workflows"
Write-Info "   4. Check escrow transaction logging"

if ($Environment -eq "production") {
    Write-Warning "⚠️ PRODUCTION DEPLOYMENT NOTES:"
    Write-Warning "   - Database backup created: $backupFile"
    Write-Warning "   - Monitor application logs for any issues"
    Write-Warning "   - Test payment flows thoroughly"
    Write-Warning "   - Verify commission calculations"
}

Write-Success "✨ Migration script completed successfully!"
