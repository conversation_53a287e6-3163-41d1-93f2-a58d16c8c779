/**
 * Express Type Extensions
 * Extends Express Request interface for authentication
 */

import { Request } from 'express';

declare global {
  namespace Express {
    interface User {
      id: string;
      email: string;
      userType: 'producer' | 'customer' | 'admin';
      status: string;
    }

    interface Request {
      user?: User;
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    userType: 'producer' | 'customer' | 'admin';
    status: string;
  };
}

export {};
