'use client'

import React from 'react'
import { Card } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Factory,
  Building2,
  MapPin,
  Eye,
  TrendingUp,
  DollarSign,
  Calendar,
  Search,
  Filter,
  Phone,
  Mail,
  ShoppingCart,
  Target,
  Star,
  Clock,
  Package,
  Users,
  CheckCircle
} from 'lucide-react'

export default function AdminProducersPage() {
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedCategory, setSelectedCategory] = React.useState('all')

  // Producer data will be loaded from API
  const mockProducers: any[] = []

  const categories = ['all', 'Mermer', 'Traverten', 'Granit', 'Oniks', 'Andezit', 'Bazalt']

  const filteredProducers = mockProducers.filter(producer => {
    const matchesSearch = producer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         producer.contact.person.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || producer.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const totalProducers = mockProducers.length
  const activeProducers = mockProducers.filter(p => p.status === 'active').length
  const totalRevenue = mockProducers.reduce((sum, producer) => sum + producer.stats.totalRevenue, 0)
  const avgRating = mockProducers.reduce((sum, producer) => sum + producer.stats.rating, 0) / mockProducers.length

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'suspended':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStockColor = (stock: string) => {
    switch (stock) {
      case 'Stokta':
        return 'bg-green-100 text-green-800'
      case 'Az Stok':
        return 'bg-yellow-100 text-yellow-800'
      case 'Stok Yok':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Üretici Yönetimi</h1>
          <p className="text-gray-600 mt-1">
            Üretici profillerini yönetin ve performanslarını analiz edin
          </p>
        </div>
        <Button>
          <Factory className="w-4 h-4 mr-2" />
          Yeni Üretici Ekle
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Factory className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Üretici</p>
              <p className="text-xl font-bold text-gray-900">{totalProducers}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Aktif Üretici</p>
              <p className="text-xl font-bold text-gray-900">{activeProducers}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-amber-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Gelir</p>
              <p className="text-xl font-bold text-gray-900">₺{(totalRevenue / 1000000).toFixed(1)}M</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Star className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Ort. Değerlendirme</p>
              <p className="text-xl font-bold text-gray-900">{avgRating.toFixed(1)}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Üretici adı veya kişi adı ile ara..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
          />
        </div>
        
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
        >
          {categories.map(category => (
            <option key={category} value={category}>
              {category === 'all' ? 'Tüm Kategoriler' : category}
            </option>
          ))}
        </select>
      </div>

      {/* Producers Grid */}
      <div className="grid grid-cols-1 gap-6">
        {filteredProducers.map((producer) => (
          <Card key={producer.id} className="overflow-hidden">
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {/* Producer Info */}
                <div className="lg:col-span-1">
                  <div className="flex items-start gap-3">
                    <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                      <Factory className="w-6 h-6 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <button
                        onClick={() => window.location.href = `/admin/producers/${producer.id}`}
                        className="text-lg font-semibold text-blue-600 hover:underline text-left"
                      >
                        {producer.name}
                      </button>
                      <p className="text-sm text-gray-600 flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        {producer.location}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline">{producer.category}</Badge>
                        <Badge className={getStatusColor(producer.status)}>
                          {producer.status === 'active' ? 'Aktif' :
                           producer.status === 'pending' ? 'Beklemede' : 'Askıda'}
                        </Badge>
                      </div>

                      <div className="mt-2 space-y-1">
                        <p className="text-xs text-gray-600">{producer.contact.person}</p>
                        <div className="flex items-center gap-2">
                          <Phone className="w-3 h-3 text-gray-400" />
                          <span className="text-xs text-gray-600">{producer.contact.phone}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 text-yellow-500 fill-current" />
                          <span className="text-xs text-gray-600">{producer.stats.rating}</span>
                          {producer.verified && (
                            <Badge variant="outline" className="ml-1 text-xs">Doğrulanmış</Badge>
                          )}
                        </div>

                        {/* Quarry Info */}
                        <div className="mt-2 pt-2 border-t border-gray-200">
                          <div className="flex items-center gap-1 mb-1">
                            <Building2 className="w-3 h-3 text-gray-400" />
                            <span className="text-xs font-medium text-gray-600">
                              Ocak Durumu: {producer.hasQuarry ? '✅ Var' : '❌ Yok'}
                            </span>
                          </div>
                          {producer.hasQuarry && producer.quarries && producer.quarries.length > 0 && (
                            <div className="space-y-1">
                              {producer.quarries.map((quarry: any, index: number) => (
                                <div key={index} className="text-xs text-gray-600">
                                  <span className="font-medium">{quarry.name}</span>
                                  <span className="text-gray-500"> • {quarry.location}</span>
                                  <Badge
                                    variant="outline"
                                    className={`ml-1 text-xs ${
                                      quarry.status === 'approved' ? 'text-green-600 border-green-300' :
                                      quarry.status === 'pending' ? 'text-yellow-600 border-yellow-300' :
                                      'text-red-600 border-red-300'
                                    }`}
                                  >
                                    {quarry.status === 'approved' ? 'Onaylı' :
                                     quarry.status === 'pending' ? 'Bekliyor' : 'Reddedildi'}
                                  </Badge>
                                </div>
                              ))}
                            </div>
                          )}
                          {!producer.hasQuarry && (
                            <p className="text-xs text-red-600">Blok satışı yapamaz</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Stats */}
                <div className="lg:col-span-2">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Ürün Sayısı</p>
                      <p className="text-lg font-bold text-gray-900">{producer.stats.totalProducts}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Toplam Sipariş</p>
                      <p className="text-lg font-bold text-gray-900">{producer.stats.totalOrders}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Toplam Gelir</p>
                      <p className="text-lg font-bold text-gray-900">₺{(producer.stats.totalRevenue / 1000).toFixed(0)}K</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Dönüşüm</p>
                      <p className="text-lg font-bold text-gray-900">{producer.stats.conversionRate.toFixed(1)}%</p>
                    </div>
                  </div>

                  {/* Recent Products */}
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Son Ürünler</h4>
                    <div className="space-y-1">
                      {producer.recentProducts.slice(0, 2).map((product, index) => (
                        <div key={index} className="text-xs text-gray-600 flex justify-between items-center">
                          <span>{product.name}</span>
                          <div className="flex items-center gap-2">
                            <span>${product.price}</span>
                            <Badge className={getStockColor(product.stock)} variant="outline">
                              {product.stock}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Quarries */}
                  <div className="mt-3">
                    <h4 className="text-sm font-medium text-gray-900 mb-1">Ocaklar</h4>
                    <div className="space-y-1">
                      {producer.quarries.map((quarry, index) => (
                        <div key={index} className="text-xs text-gray-600">
                          <span className="font-medium">{quarry.name}</span> - {quarry.capacity}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="lg:col-span-1">
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => window.location.href = `/admin/producers/${producer.id}`}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Profili Görüntüle
                    </Button>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Phone className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Mail className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <TrendingUp className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="text-xs text-gray-500 text-center">
                      Son aktivite: {producer.stats.lastActivity}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {filteredProducers.length === 0 && (
        <div className="text-center py-12">
          <Factory className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Üretici bulunamadı</h3>
          <p className="text-gray-600">Arama kriterlerinize uygun üretici bulunmuyor.</p>
        </div>
      )}
    </div>
  )
}
