'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Factory,
  Building2,
  MapPin,
  Eye,
  TrendingUp,
  DollarSign,
  Calendar,
  Search,
  Filter,
  Phone,
  Mail,
  ShoppingCart,
  Target,
  Star,
  Clock,
  Package,
  Users,
  CheckCircle,
  MoreHorizontal
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import Link from 'next/link'

interface ApprovedProducer {
  id: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  countryCode: string;
  companyType: string;
  productionCapacity: number;
  status: 'ACTIVE' | 'INACTIVE';
  approvedAt: string;
  productCategories: string[];
  totalProducts: number;
  totalOrders: number;
  rating: number;
}

export default function AdminProducersPage() {
  const [searchTerm, setSearchTerm] = React.useState('')
  const [selectedCategory, setSelectedCategory] = React.useState('all')
  const [producers, setProducers] = React.useState<ApprovedProducer[]>([])
  const [loading, setLoading] = React.useState(true)

  const categories = ['all', 'Mermer', 'Traverten', 'Granit', 'Oniks', 'Andezit', 'Bazalt']

  // Load approved producers
  React.useEffect(() => {
    const fetchApprovedProducers = async () => {
      try {
        const response = await fetch('/api/admin/producers/approved')
        if (response.ok) {
          const data = await response.json()
          setProducers(data.producers || [])
        }
      } catch (error) {
        console.error('Error fetching approved producers:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchApprovedProducers()
  }, [])

  const filteredProducers = producers.filter(producer => {
    const matchesSearch = producer.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         producer.contactPerson.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' ||
                           producer.productCategories.some(cat => cat === selectedCategory)

    return matchesSearch && matchesCategory
  })

  const totalProducers = producers.length
  const activeProducers = producers.filter(p => p.status === 'ACTIVE').length
  const totalProducts = producers.reduce((sum, producer) => sum + producer.totalProducts, 0)
  const avgRating = producers.length > 0 ? producers.reduce((sum, producer) => sum + producer.rating, 0) / producers.length : 0

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'INACTIVE':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Üretici Yönetimi</h1>
          <p className="text-gray-600 mt-1">
            Üretici profillerini yönetin ve performanslarını analiz edin
          </p>
        </div>
        <Button>
          <Factory className="w-4 h-4 mr-2" />
          Yeni Üretici Ekle
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Factory className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Üretici</p>
              <p className="text-xl font-bold text-gray-900">{totalProducers}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Aktif Üretici</p>
              <p className="text-xl font-bold text-gray-900">{activeProducers}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 rounded-lg">
              <Package className="w-5 h-5 text-amber-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Ürün</p>
              <p className="text-xl font-bold text-gray-900">{totalProducts}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Star className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Ort. Değerlendirme</p>
              <p className="text-xl font-bold text-gray-900">{avgRating.toFixed(1)}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Üretici adı veya kişi adı ile ara..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
          />
        </div>
        
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
        >
          {categories.map(category => (
            <option key={category} value={category}>
              {category === 'all' ? 'Tüm Kategoriler' : category}
            </option>
          ))}
        </select>
      </div>

      {/* Producers Grid */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Üreticiler yükleniyor...</p>
        </div>
      ) : filteredProducers.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Factory className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Henüz onaylanan üretici yok</h3>
            <p className="text-gray-600">Onaylanan üreticiler burada görünecek.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-6">
          {filteredProducers.map((producer) => (
            <Card key={producer.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                  {/* Producer Info */}
                  <div className="lg:col-span-1">
                    <div className="flex items-start gap-3">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Factory className="w-6 h-6 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <Link
                          href={`/admin/producers/${producer.id}`}
                          className="text-lg font-semibold text-blue-600 hover:underline"
                        >
                          {producer.companyName}
                        </Link>
                        <p className="text-sm text-gray-600 flex items-center gap-1 mt-1">
                          <Users className="w-3 h-3" />
                          {producer.contactPerson}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge className={getStatusColor(producer.status)}>
                            {producer.status === 'ACTIVE' ? 'Aktif' : 'Pasif'}
                          </Badge>
                          <Badge variant="outline">{producer.companyType}</Badge>
                        </div>

                        <div className="mt-3 space-y-2">
                          <div className="flex items-center gap-2">
                            <Phone className="w-3 h-3 text-gray-400" />
                            <span className="text-xs text-gray-600">{producer.phone}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Mail className="w-3 h-3 text-gray-400" />
                            <span className="text-xs text-gray-600">{producer.email}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Star className="w-3 h-3 text-yellow-500 fill-current" />
                            <span className="text-xs text-gray-600">{producer.rating.toFixed(1)}</span>
                            <Badge variant="outline" className="ml-1 text-xs">Doğrulanmış</Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-3 h-3 text-gray-400" />
                            <span className="text-xs text-gray-600">
                              Onaylandı: {formatDate(producer.approvedAt)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="lg:col-span-2">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Ürün Sayısı</p>
                        <p className="text-lg font-bold text-gray-900">{producer.totalProducts}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Toplam Sipariş</p>
                        <p className="text-lg font-bold text-gray-900">{producer.totalOrders}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Kapasite</p>
                        <p className="text-lg font-bold text-gray-900">{producer.productionCapacity} m²/ay</p>
                      </div>
                    </div>

                    {/* Product Categories */}
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Ürün Kategorileri</h4>
                      <div className="flex flex-wrap gap-1">
                        {producer.productCategories.map((category, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {category}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="lg:col-span-1">
                    <div className="space-y-2">
                      <Link href={`/admin/producers/${producer.id}`}>
                        <Button variant="outline" size="sm" className="w-full">
                          <Eye className="w-4 h-4 mr-2" />
                          Profili Görüntüle
                        </Button>
                      </Link>

                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" className="flex-1" title="Ara">
                          <Phone className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="flex-1" title="Email Gönder">
                          <Mail className="w-4 h-4" />
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="flex-1">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem>Düzenle</DropdownMenuItem>
                            <DropdownMenuItem>Deaktif Et</DropdownMenuItem>
                            <DropdownMenuItem>Raporla</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
