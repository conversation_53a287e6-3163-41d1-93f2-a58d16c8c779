'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  ArrowLeft, 
  Building2, 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

export default function QuoteDetailsPage() {
  const params = useParams();
  const quoteId = params.quoteId as string;
  const producerQuoteId = params.producerQuoteId as string;

  // Mock data
  const mockQuoteDetails = {
    'QUO-001': {
      'Q1': {
        customerRequest: {
          customerName: 'Mehmet Özkan',
          customerCompany: 'Özkan İnşaat Ltd.',
          productName: 'Oniks Yeşil',
          category: 'Oniks',
          dimension: '60x120x2cm',
          quantity: 100,
          surfaceFinish: 'Cilalı',
          requestDate: '2025-06-30',
          deliveryLocation: 'İstanbul, Türkiye',
          specialRequirements: 'A+ kalite, homojen renk dağılımı',
          budgetRange: '$80-130 per m²',
          notes: 'Lüks villa projesi için kullanılacak'
        },
        
        producerCompany: {
          name: 'Afyon Doğal Taş A.Ş.',
          establishedYear: 2005,
          location: 'Afyon, Türkiye',
          address: 'Organize Sanayi Bölgesi 1. Cadde No:15 Afyon',
          contactPerson: 'Ali Demir',
          contactTitle: 'Satış Müdürü',
          contactPhone: '+90 532 111 22 33',
          contactEmail: '<EMAIL>',
          certifications: ['ISO 9001', 'CE Belgesi', 'TSE Belgesi'],
          productionCapacity: '10,000 m²/ay',
          employeeCount: 150
        },

        producerQuote: {
          quoteNumber: 'AFY-2025-001',
          price: 120.00,
          currency: 'USD',
          totalPrice: 12000.00,
          deliveryTime: '15 gün',
          deliveryTerms: 'EXW Afyon Fabrika',
          paymentTerms: '%30 peşin, %70 sevkiyat öncesi',
          validUntil: '2025-07-25',
          submittedDate: '2025-07-01',
          status: 'Bekliyor',
          
          technicalSpecs: {
            material: 'Doğal Oniks',
            origin: 'Afyon Bölgesi',
            color: 'Yeşil (Verde)',
            quality: 'A+ Sınıf',
            thickness: '20mm'
          },

          notes: 'Yüksek kalite A+ sınıf oniks. 15 yıl garanti.',
          attachments: ['Teknik Çizim.pdf', 'Kalite Sertifikası.pdf']
        }
      }
    }
  };

  const quoteData = mockQuoteDetails[quoteId as keyof typeof mockQuoteDetails]?.[producerQuoteId as keyof any];

  if (!quoteData) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Teklif Bulunamadı</h2>
          <p className="text-gray-600 mb-6">Aradığınız teklif detayları bulunamadı.</p>
          <Link href="/admin/customers" className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
            Müşterilere Dön
          </Link>
        </div>
      </div>
    );
  }

  const { customerRequest, producerCompany, producerQuote } = quoteData;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Bekliyor': return 'bg-yellow-100 text-yellow-800';
      case 'Kabul Edildi': return 'bg-green-100 text-green-800';
      case 'Reddedildi': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <Link href="/admin/customers/1" className="flex items-center text-gray-600 hover:text-gray-900">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Müşteri Detayına Dön
          </Link>
          <Badge className={getStatusColor(producerQuote.status)}>
            {producerQuote.status}
          </Badge>
        </div>
        
        <div className="mt-4">
          <h1 className="text-2xl font-bold text-gray-900">
            Teklif Detayları - {producerQuote.quoteNumber}
          </h1>
          <p className="text-gray-600 mt-1">
            {customerRequest.productName} • {customerRequest.dimension} • {customerRequest.quantity} m²
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        {/* Sol Kolon - Müşteri Talebi */}
        <div className="lg:col-span-1">
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <User className="w-5 h-5 mr-2 text-blue-600" />
              Müşteri Talebi
            </h2>
            
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Müşteri</p>
                <p className="font-medium">{customerRequest.customerName}</p>
                <p className="text-sm text-gray-500">{customerRequest.customerCompany}</p>
              </div>

              <div>
                <p className="text-sm text-gray-600">Ürün</p>
                <p className="font-medium">{customerRequest.productName}</p>
                <p className="text-sm text-gray-500">{customerRequest.category}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Ebat</p>
                  <p className="font-medium">{customerRequest.dimension}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Miktar</p>
                  <p className="font-medium">{customerRequest.quantity} m²</p>
                </div>
              </div>

              <div>
                <p className="text-sm text-gray-600">Yüzey İşlemi</p>
                <p className="font-medium">{customerRequest.surfaceFinish}</p>
              </div>

              <div>
                <p className="text-sm text-gray-600">Teslimat Yeri</p>
                <p className="font-medium">{customerRequest.deliveryLocation}</p>
              </div>

              <div>
                <p className="text-sm text-gray-600">Bütçe Aralığı</p>
                <p className="font-medium text-green-600">{customerRequest.budgetRange}</p>
              </div>

              <div>
                <p className="text-sm text-gray-600">Talep Tarihi</p>
                <p className="font-medium">
                  {new Date(customerRequest.requestDate).toLocaleDateString('tr-TR')}
                </p>
              </div>

              {customerRequest.specialRequirements && (
                <div>
                  <p className="text-sm text-gray-600">Özel Gereksinimler</p>
                  <p className="text-sm text-gray-900 bg-gray-100 p-2 rounded">
                    {customerRequest.specialRequirements}
                  </p>
                </div>
              )}

              {customerRequest.notes && (
                <div>
                  <p className="text-sm text-gray-600">Notlar</p>
                  <p className="text-sm text-gray-900 bg-blue-50 p-2 rounded">
                    {customerRequest.notes}
                  </p>
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Sağ Kolon - Üretici ve Teklif */}
        <div className="lg:col-span-2 space-y-6">
          
          {/* Üretici Firma Bilgileri */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Building2 className="w-5 h-5 mr-2 text-green-600" />
              Üretici Firma Bilgileri
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600">Firma Adı</p>
                  <p className="font-semibold text-lg">{producerCompany.name}</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Kuruluş Yılı</p>
                    <p className="font-medium">{producerCompany.establishedYear}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Çalışan Sayısı</p>
                    <p className="font-medium">{producerCompany.employeeCount}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-600">Adres</p>
                  <p className="font-medium">{producerCompany.address}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">Üretim Kapasitesi</p>
                  <p className="font-medium text-blue-600">{producerCompany.productionCapacity}</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600">Yetkili Kişi</p>
                  <p className="font-medium">{producerCompany.contactPerson}</p>
                  <p className="text-sm text-gray-500">{producerCompany.contactTitle}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center">
                    <Phone className="w-4 h-4 mr-2 text-gray-400" />
                    <a href={`tel:${producerCompany.contactPhone}`} className="text-blue-600 hover:underline">
                      {producerCompany.contactPhone}
                    </a>
                  </div>
                  <div className="flex items-center">
                    <Mail className="w-4 h-4 mr-2 text-gray-400" />
                    <a href={`mailto:${producerCompany.contactEmail}`} className="text-blue-600 hover:underline">
                      {producerCompany.contactEmail}
                    </a>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-2 text-gray-400" />
                    <span className="text-gray-700">{producerCompany.location}</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-600">Sertifikalar</p>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {producerCompany.certifications.map((cert, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {cert}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={() => window.open(`tel:${producerCompany.contactPhone}`)}>
                    <Phone className="w-4 h-4 mr-1" />
                    Ara
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => window.open(`mailto:${producerCompany.contactEmail}`)}>
                    <Mail className="w-4 h-4 mr-1" />
                    Email
                  </Button>
                </div>
              </div>
            </div>
          </Card>

          {/* Üretici Teklifi */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FileText className="w-5 h-5 mr-2 text-purple-600" />
              Üretici Teklifi
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Teklif Fiyatı</p>
                  <p className="text-2xl font-bold text-green-600">
                    ${producerQuote.price} {producerQuote.currency}
                  </p>
                  <p className="text-sm text-gray-500">per m²</p>
                  <p className="text-lg font-semibold text-gray-900 mt-2">
                    Toplam: ${producerQuote.totalPrice.toLocaleString()} {producerQuote.currency}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Teslimat Süresi</p>
                    <p className="font-medium">{producerQuote.deliveryTime}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Geçerlilik</p>
                    <p className="font-medium">
                      {new Date(producerQuote.validUntil).toLocaleDateString('tr-TR')}
                    </p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-600">Teslimat Koşulları</p>
                  <p className="font-medium">{producerQuote.deliveryTerms}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">Ödeme Koşulları</p>
                  <p className="font-medium">{producerQuote.paymentTerms}</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600 font-semibold mb-2">Teknik Özellikler</p>
                  <div className="bg-blue-50 p-3 rounded space-y-2">
                    <div className="text-sm">
                      <span className="text-gray-600">Malzeme:</span>
                      <span className="font-medium ml-1">{producerQuote.technicalSpecs.material}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">Menşei:</span>
                      <span className="font-medium ml-1">{producerQuote.technicalSpecs.origin}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">Renk:</span>
                      <span className="font-medium ml-1">{producerQuote.technicalSpecs.color}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">Kalite:</span>
                      <span className="font-medium ml-1">{producerQuote.technicalSpecs.quality}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">Kalınlık:</span>
                      <span className="font-medium ml-1">{producerQuote.technicalSpecs.thickness}</span>
                    </div>
                  </div>
                </div>

                {producerQuote.notes && (
                  <div>
                    <p className="text-sm text-gray-600">Üretici Notları</p>
                    <div className="bg-blue-50 p-3 rounded mt-1">
                      <p className="text-sm text-gray-700">{producerQuote.notes}</p>
                    </div>
                  </div>
                )}

                {producerQuote.attachments && producerQuote.attachments.length > 0 && (
                  <div>
                    <p className="text-sm text-gray-600 mb-2">Ekler</p>
                    <div className="space-y-1">
                      {producerQuote.attachments.map((attachment, index) => (
                        <div key={index} className="flex items-center p-2 bg-gray-50 rounded hover:bg-gray-100 cursor-pointer">
                          <FileText className="w-4 h-4 mr-2 text-gray-500" />
                          <span className="text-sm text-gray-700">{attachment}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* Admin Aksiyon Butonları */}
          <Card className="p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Admin İşlemleri</h3>
              <p className="text-sm text-gray-600">
                Teklif kabul/red işlemleri müşteri tarafından yapılır. Admin olarak süreçleri takip edebilir ve gerekli işlemleri gerçekleştirebilirsiniz.
              </p>
            </div>

            <div className="flex flex-wrap gap-4 justify-center">
              <Button variant="outline" className="bg-blue-50 border-blue-200">
                <Mail className="w-4 h-4 mr-2" />
                Üretici ile İletişim
              </Button>
              <Button variant="outline" className="bg-green-50 border-green-200">
                <Phone className="w-4 h-4 mr-2" />
                Müşteri ile İletişim
              </Button>
              <Button variant="outline" className="bg-purple-50 border-purple-200">
                <FileText className="w-4 h-4 mr-2" />
                PDF Olarak İndir
              </Button>
              <Button variant="outline" className="bg-orange-50 border-orange-200">
                <AlertCircle className="w-4 h-4 mr-2" />
                Süreç Notları Ekle
              </Button>
            </div>

            {/* Teklif Durumu Bilgisi */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">Teklif Durumu</p>
                  <p className="text-sm text-gray-600">
                    {producerQuote.status === 'Bekliyor' && 'Müşteri değerlendirmesi bekleniyor'}
                    {producerQuote.status === 'Kabul Edildi' && 'Müşteri tarafından kabul edildi'}
                    {producerQuote.status === 'Reddedildi' && 'Müşteri tarafından reddedildi'}
                  </p>
                </div>
                <Badge className={getStatusColor(producerQuote.status)}>
                  {producerQuote.status}
                </Badge>
              </div>

              {producerQuote.status === 'Bekliyor' && (
                <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="text-sm text-yellow-800">
                    💡 <strong>Bilgi:</strong> Müşteri bu teklifi değerlendiriyor.
                    Gerekirse müşteri ile iletişime geçerek süreci hızlandırabilirsiniz.
                  </p>
                </div>
              )}

              {producerQuote.status === 'Kabul Edildi' && (
                <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded">
                  <p className="text-sm text-green-800">
                    ✅ <strong>Tebrikler:</strong> Teklif kabul edildi!
                    Sipariş sürecini başlatabilir ve üretici ile koordinasyonu sağlayabilirsiniz.
                  </p>
                </div>
              )}

              {producerQuote.status === 'Reddedildi' && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                  <p className="text-sm text-red-800">
                    ❌ <strong>Bilgi:</strong> Teklif reddedildi.
                    Müşteri ile iletişime geçerek red nedenlerini öğrenebilir ve alternatif çözümler sunabilirsiniz.
                  </p>
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
