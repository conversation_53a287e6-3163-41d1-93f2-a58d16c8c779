'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Search,
  Filter,
  Download,
  TrendingUp,
  DollarSign,
  PieChart,
  BarChart3,
  Calendar,
  Package,
  Building,
  Users,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'

// Mock data - gerçek API'den gelecek
const mockCommissionStats = {
  totalCommission: 45000,
  thisMonth: 12500,
  lastMonth: 10800,
  growth: 15.7,
  m2Commission: 35000, // $1/m² komisyon
  tonCommission: 10000, // $10/ton komisyon
  totalTransactions: 450,
  avgCommissionPerTransaction: 100
}

const mockCommissionByCategory = [
  { category: 'Mermer', amount: 18000, percentage: 40, transactions: 180, color: 'bg-blue-500' },
  { category: 'Traverten', amount: 13500, percentage: 30, transactions: 135, color: 'bg-green-500' },
  { category: 'Granit', amount: 9000, percentage: 20, transactions: 90, color: 'bg-purple-500' },
  { category: 'Oniks', amount: 2700, percentage: 6, transactions: 27, color: 'bg-yellow-500' },
  { category: 'Diğer', amount: 1800, percentage: 4, transactions: 18, color: 'bg-gray-500' }
]

const mockCommissionByProducer = [
  {
    id: 'prod_001',
    name: 'Afyon Mermer A.Ş.',
    totalCommission: 8500,
    transactions: 85,
    avgCommission: 100,
    lastTransaction: '2025-01-02T14:30:00Z',
    growth: 12.5
  },
  {
    id: 'prod_002',
    name: 'Denizli Traverten Ltd.',
    totalCommission: 6200,
    transactions: 62,
    avgCommission: 100,
    lastTransaction: '2025-01-02T12:15:00Z',
    growth: -5.2
  },
  {
    id: 'prod_003',
    name: 'Çanakkale Granit San.',
    totalCommission: 5800,
    transactions: 58,
    avgCommission: 100,
    lastTransaction: '2025-01-02T10:45:00Z',
    growth: 8.7
  },
  {
    id: 'prod_004',
    name: 'Marmara Oniks A.Ş.',
    totalCommission: 3200,
    transactions: 32,
    avgCommission: 100,
    lastTransaction: '2025-01-01T16:20:00Z',
    growth: 22.1
  }
]

const mockMonthlyTrend = [
  { month: 'Tem', commission: 8500, transactions: 85 },
  { month: 'Ağu', commission: 9200, transactions: 92 },
  { month: 'Eyl', commission: 10100, transactions: 101 },
  { month: 'Eki', commission: 10800, transactions: 108 },
  { month: 'Kas', commission: 11500, transactions: 115 },
  { month: 'Ara', commission: 12500, transactions: 125 }
]

export default function CommissionPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [timeFilter, setTimeFilter] = useState('this_month')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [commissionData, setCommissionData] = useState({
    stats: mockCommissionStats,
    categories: mockCommissionByCategory,
    producers: mockCommissionByProducer,
    monthlyTrend: mockMonthlyTrend
  })
  const [isLoading, setIsLoading] = useState(false)

  const filteredProducers = commissionData.producers.filter(producer => {
    const matchesSearch = producer.name.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesSearch
  })

  const filteredCategories = categoryFilter === 'all'
    ? commissionData.categories
    : commissionData.categories.filter(cat => cat.category === categoryFilter)

  const handleRefreshData = async () => {
    setIsLoading(true)
    try {
      // API çağrısı simülasyonu
      await new Promise(resolve => setTimeout(resolve, 1500))
      // Commission data refreshed successfully
    } catch (error) {
      // Error handling - could be logged to monitoring service
    } finally {
      setIsLoading(false)
    }
  }

  const handleExportReport = () => {
    // Rapor export işlemi
    const reportData = {
      period: timeFilter,
      totalCommission: commissionData.stats.totalCommission,
      categories: filteredCategories,
      producers: filteredProducers,
      monthlyTrend: commissionData.monthlyTrend
    }

    // Commission report prepared for export
    // Export işlemi burada yapılacak
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getGrowthBadge = (growth: number) => {
    if (growth > 0) {
      return (
        <Badge variant="secondary" className="bg-green-100 text-green-800">
          <ArrowUpRight className="w-3 h-3 mr-1" />
          +{growth.toFixed(1)}%
        </Badge>
      )
    } else {
      return (
        <Badge variant="secondary" className="bg-red-100 text-red-800">
          <ArrowDownRight className="w-3 h-3 mr-1" />
          {growth.toFixed(1)}%
        </Badge>
      )
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Komisyon Analizi</h1>
          <p className="text-gray-600 mt-1">
            Platform komisyonlarını takip edin ve analiz edin
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" onClick={handleExportReport}>
            <Download className="w-4 h-4 mr-2" />
            Rapor İndir
          </Button>
          <Button variant="outline" onClick={handleRefreshData} disabled={isLoading}>
            <BarChart3 className={`w-4 h-4 mr-2 ${isLoading ? 'animate-pulse' : ''}`} />
            {isLoading ? 'Yenileniyor...' : 'Detaylı Analiz'}
          </Button>
        </div>
      </div>

      {/* Main Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Komisyon</p>
              <p className="text-2xl font-bold text-gray-900">
                ${commissionData.stats.totalCommission.toLocaleString()}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Tüm zamanlar
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrendingUp className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Bu Ay</p>
              <p className="text-2xl font-bold text-gray-900">
                ${commissionData.stats.thisMonth.toLocaleString()}
              </p>
              <div className="flex items-center mt-1">
                {getGrowthBadge(commissionData.stats.growth)}
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Package className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam İşlem</p>
              <p className="text-2xl font-bold text-gray-900">
                {commissionData.stats.totalTransactions}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Bu ay: {commissionData.monthlyTrend[commissionData.monthlyTrend.length - 1]?.transactions || 0}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <BarChart3 className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Ortalama Komisyon</p>
              <p className="text-2xl font-bold text-gray-900">
                ${mockCommissionStats.avgCommissionPerTransaction}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Commission Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Komisyon Türü Dağılımı
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-4 h-4 bg-blue-500 rounded"></div>
                <div>
                  <p className="font-medium text-gray-900">m² Komisyonu</p>
                  <p className="text-sm text-gray-600">$1 per m²</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-gray-900">
                  ${commissionData.stats.m2Commission.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">
                  {((commissionData.stats.m2Commission / commissionData.stats.totalCommission) * 100).toFixed(1)}%
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-4 h-4 bg-green-500 rounded"></div>
                <div>
                  <p className="font-medium text-gray-900">Ton Komisyonu</p>
                  <p className="text-sm text-gray-600">$10 per ton</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-gray-900">
                  ${commissionData.stats.tonCommission.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">
                  {((commissionData.stats.tonCommission / commissionData.stats.totalCommission) * 100).toFixed(1)}%
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Aylık Trend
          </h3>
          <div className="space-y-3">
            {commissionData.monthlyTrend.map((month, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 text-sm text-gray-600">{month.month}</div>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${(month.commission / Math.max(...commissionData.monthlyTrend.map(m => m.commission))) * 100}%` }}
                    ></div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    ${month.commission.toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-500">{month.transactions} işlem</p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Üretici firma adı ile ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="this_month">Bu Ay</option>
              <option value="last_month">Geçen Ay</option>
              <option value="this_quarter">Bu Çeyrek</option>
              <option value="this_year">Bu Yıl</option>
              <option value="all_time">Tüm Zamanlar</option>
            </select>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tüm Kategoriler</option>
              <option value="Mermer">Mermer</option>
              <option value="Traverten">Traverten</option>
              <option value="Granit">Granit</option>
              <option value="Oniks">Oniks</option>
              <option value="Diğer">Diğer</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Category Breakdown */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <PieChart className="w-5 h-5" />
          Kategori Bazlı Komisyon Dağılımı
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {filteredCategories.map((category, index) => (
            <div key={index} className="text-center">
              <div className={`w-full h-32 ${category.color} rounded-lg mb-3 flex items-center justify-center`}>
                <div className="text-white">
                  <div className="text-2xl font-bold">{category.percentage}%</div>
                  <div className="text-sm">${category.amount.toLocaleString()}</div>
                </div>
              </div>
              <h4 className="font-medium text-gray-900">{category.category}</h4>
              <p className="text-sm text-gray-600">{category.transactions} işlem</p>
            </div>
          ))}
        </div>
      </Card>

      {/* Producer Performance */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Building className="w-5 h-5" />
            Üretici Bazlı Komisyon Performansı
          </h3>
          <div className="flex gap-3">
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filtrele
            </Button>
            <Button variant="outline" size="sm" onClick={handleExportReport}>
              <Download className="w-4 h-4 mr-2" />
              Excel İndir
            </Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Üretici
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Toplam Komisyon
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  İşlem Sayısı
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ortalama Komisyon
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Son İşlem
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Büyüme
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProducers.map((producer) => (
                <tr key={producer.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <Building className="h-5 w-5 text-gray-500" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {producer.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          ID: {producer.id}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      ${producer.totalCommission.toLocaleString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{producer.transactions}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      ${producer.avgCommission}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {formatDate(producer.lastTransaction)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getGrowthBadge(producer.growth)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  )
}
