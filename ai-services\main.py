"""
AI Services Main Application
FastAPI application for AI-powered features of Natural Stone Marketplace
"""

import os
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import routers
from routers import chatbot, news_aggregation, marketing_ai, price_optimization

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🤖 AI Services starting up...")
    
    # Initialize AI models and services here
    # await initialize_models()
    
    yield
    
    # Shutdown
    print("🤖 AI Services shutting down...")
    # Cleanup resources here

# Create FastAPI app
app = FastAPI(
    title="Natural Stone Marketplace AI Services",
    description="AI-powered services for Turkish Natural Stone Marketplace Platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Frontend
        "http://localhost:8000",  # Backend
        os.getenv("FRONTEND_URL", "http://localhost:3000"),
        os.getenv("BACKEND_URL", "http://localhost:8000"),
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "OK",
        "service": "AI Services",
        "version": "1.0.0",
        "timestamp": "2025-06-27T00:00:00Z"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Natural Stone Marketplace AI Services",
        "version": "1.0.0",
        "status": "Running",
        "endpoints": [
            "GET /health",
            "GET /docs",
            "POST /chatbot/chat",
            "GET /news/latest",
            "POST /marketing/generate-content",
            "POST /pricing/optimize"
        ]
    }

# Include routers
app.include_router(chatbot.router, prefix="/chatbot", tags=["Chatbot"])
app.include_router(news_aggregation.router, prefix="/news", tags=["News"])
app.include_router(marketing_ai.router, prefix="/marketing", tags=["Marketing"])
app.include_router(price_optimization.router, prefix="/pricing", tags=["Pricing"])

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": {
                "message": "An unexpected error occurred",
                "type": type(exc).__name__,
                "details": str(exc) if os.getenv("DEBUG") == "true" else None
            }
        }
    )

if __name__ == "__main__":
    # Run the application
    port = int(os.getenv("PORT", 8001))
    host = os.getenv("HOST", "0.0.0.0")
    debug = os.getenv("DEBUG", "false").lower() == "true"
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )
