'use client';

import React, { Suspense, useState, useRef, useEffect } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { 
  OrbitControls, 
  Environment, 
  ContactShadows, 
  Text, 
  Box,
  Plane,
  useTexture,
  Html,
  PerspectiveCamera
} from '@react-three/drei';
import * as THREE from 'three';

interface Product {
  id: string;
  name: string;
  texture?: string;
  color?: string;
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
  position: [number, number, number];
  rotation: [number, number, number];
}

interface Room {
  type: 'bathroom' | 'kitchen' | 'living' | 'bedroom' | 'outdoor';
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
}

interface ThreeShowroomProps {
  selectedProducts: Product[];
  selectedRoom: Room;
  onProductSelect?: (product: Product) => void;
  onProductRemove?: (productId: string) => void;
  onProductMove?: (productId: string, position: [number, number, number]) => void;
}

// Stone Material Component
const StoneMaterial: React.FC<{ 
  texture?: string; 
  color?: string; 
  roughness?: number;
  metalness?: number;
}> = ({ texture, color = '#8B7355', roughness = 0.8, metalness = 0.1 }) => {
  const textureMap = texture ? useTexture(texture) : null;
  
  useEffect(() => {
    if (textureMap) {
      textureMap.wrapS = textureMap.wrapT = THREE.RepeatWrapping;
      textureMap.repeat.set(2, 2);
    }
  }, [textureMap]);

  return (
    <meshStandardMaterial
      map={textureMap}
      color={color}
      roughness={roughness}
      metalness={metalness}
      normalScale={[0.5, 0.5]}
    />
  );
};

// Interactive Stone Tile Component
const StoneTile: React.FC<{
  product: Product;
  isSelected: boolean;
  onSelect: () => void;
  onRemove: () => void;
  onMove: (position: [number, number, number]) => void;
}> = ({ product, isSelected, onSelect, onRemove, onMove }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);
  const [dragging, setDragging] = useState(false);

  useFrame(() => {
    if (meshRef.current && isSelected) {
      meshRef.current.rotation.y += 0.01;
    }
  });

  const handlePointerDown = (event: any) => {
    event.stopPropagation();
    setDragging(true);
    onSelect();
  };

  const handlePointerUp = () => {
    setDragging(false);
  };

  const handlePointerMove = (event: any) => {
    if (dragging) {
      const newPosition: [number, number, number] = [
        event.point.x,
        product.position[1],
        event.point.z
      ];
      onMove(newPosition);
    }
  };

  return (
    <group position={product.position} rotation={product.rotation}>
      <mesh
        ref={meshRef}
        onPointerDown={handlePointerDown}
        onPointerUp={handlePointerUp}
        onPointerMove={handlePointerMove}
        onPointerEnter={() => setHovered(true)}
        onPointerLeave={() => setHovered(false)}
        scale={hovered ? 1.05 : 1}
      >
        <boxGeometry args={[
          product.dimensions.width,
          product.dimensions.height,
          product.dimensions.depth
        ]} />
        <StoneMaterial 
          texture={product.texture}
          color={product.color}
        />
      </mesh>

      {/* Selection Indicator */}
      {isSelected && (
        <mesh position={[0, product.dimensions.height / 2 + 0.1, 0]}>
          <ringGeometry args={[0.3, 0.4, 16]} />
          <meshBasicMaterial color="#00ff00" transparent opacity={0.7} />
        </mesh>
      )}

      {/* Product Label */}
      {(hovered || isSelected) && (
        <Html position={[0, product.dimensions.height / 2 + 0.5, 0]}>
          <div className="bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs whitespace-nowrap">
            {product.name}
            {isSelected && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onRemove();
                }}
                className="ml-2 text-red-400 hover:text-red-300"
              >
                ✕
              </button>
            )}
          </div>
        </Html>
      )}
    </group>
  );
};

// Room Environment Component
const RoomEnvironment: React.FC<{ room: Room }> = ({ room }) => {
  const { width, height, depth } = room.dimensions;

  return (
    <group>
      {/* Floor */}
      <Plane 
        args={[width, depth]} 
        rotation={[-Math.PI / 2, 0, 0]} 
        position={[0, 0, 0]}
      >
        <meshStandardMaterial color="#f5f5f5" />
      </Plane>

      {/* Walls */}
      <Plane 
        args={[width, height]} 
        position={[0, height / 2, -depth / 2]}
      >
        <meshStandardMaterial color="#ffffff" />
      </Plane>

      <Plane 
        args={[depth, height]} 
        rotation={[0, Math.PI / 2, 0]}
        position={[-width / 2, height / 2, 0]}
      >
        <meshStandardMaterial color="#ffffff" />
      </Plane>

      {/* Ceiling */}
      <Plane 
        args={[width, depth]} 
        rotation={[Math.PI / 2, 0, 0]} 
        position={[0, height, 0]}
      >
        <meshStandardMaterial color="#f8f8f8" />
      </Plane>

      {/* Contact Shadows */}
      <ContactShadows
        position={[0, 0.01, 0]}
        opacity={0.4}
        scale={width}
        blur={2}
        far={depth}
      />
    </group>
  );
};

// Camera Controller
const CameraController: React.FC<{ room: Room }> = ({ room }) => {
  const { camera } = useThree();
  
  useEffect(() => {
    const maxDimension = Math.max(room.dimensions.width, room.dimensions.depth);
    camera.position.set(
      maxDimension * 0.8,
      room.dimensions.height * 0.8,
      maxDimension * 0.8
    );
    camera.lookAt(0, 0, 0);
  }, [camera, room]);

  return null;
};

// Loading Component
const LoadingFallback: React.FC = () => (
  <Html center>
    <div className="flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span className="ml-2 text-gray-600">3D Sahne Yükleniyor...</span>
    </div>
  </Html>
);

// Main 3D Showroom Component
const ThreeShowroom: React.FC<ThreeShowroomProps> = ({
  selectedProducts,
  selectedRoom,
  onProductSelect,
  onProductRemove,
  onProductMove
}) => {
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);

  const handleProductSelect = (product: Product) => {
    setSelectedProductId(product.id);
    onProductSelect?.(product);
  };

  const handleProductRemove = (productId: string) => {
    setSelectedProductId(null);
    onProductRemove?.(productId);
  };

  const handleProductMove = (productId: string, position: [number, number, number]) => {
    onProductMove?.(productId, position);
  };

  return (
    <div className="w-full h-full bg-gradient-to-b from-blue-50 to-blue-100">
      <Canvas
        shadows
        camera={{ 
          position: [5, 5, 5], 
          fov: 60,
          near: 0.1,
          far: 1000
        }}
        gl={{ 
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        <Suspense fallback={<LoadingFallback />}>
          {/* Camera Controller */}
          <CameraController room={selectedRoom} />

          {/* Lighting */}
          <ambientLight intensity={0.4} />
          <directionalLight
            position={[10, 10, 5]}
            intensity={1}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
          />
          <pointLight position={[-10, 10, -10]} intensity={0.5} />

          {/* Environment */}
          <Environment preset="apartment" />

          {/* Room */}
          <RoomEnvironment room={selectedRoom} />

          {/* Products */}
          {selectedProducts.map((product) => (
            <StoneTile
              key={product.id}
              product={product}
              isSelected={selectedProductId === product.id}
              onSelect={() => handleProductSelect(product)}
              onRemove={() => handleProductRemove(product.id)}
              onMove={(position) => handleProductMove(product.id, position)}
            />
          ))}

          {/* Controls */}
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={20}
            maxPolarAngle={Math.PI / 2}
          />
        </Suspense>
      </Canvas>

      {/* Instructions */}
      <div className="absolute bottom-4 left-4 bg-black bg-opacity-75 text-white p-3 rounded-lg text-sm">
        <div className="space-y-1">
          <div>🖱️ <strong>Fare:</strong> Döndür, Yakınlaştır</div>
          <div>👆 <strong>Tıkla:</strong> Ürün Seç</div>
          <div>🖱️ <strong>Sürükle:</strong> Ürün Taşı</div>
          <div>❌ <strong>X:</strong> Ürün Sil</div>
        </div>
      </div>
    </div>
  );
};

export default ThreeShowroom;
