import { EmailConfig } from '../services/EmailService';

/**
 * Create email configuration from environment variables
 */
export function createEmailConfig(): EmailConfig {
  const provider = (process.env.EMAIL_PROVIDER || 'smtp') as 'sendgrid' | 'smtp';

  if (provider === 'sendgrid') {
    return {
      provider: 'sendgrid',
      sendgrid: {
        apiKey: process.env.SENDGRID_API_KEY || '',
        fromEmail: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
        fromName: process.env.SENDGRID_FROM_NAME || 'Doğal Taş Pazaryeri',
      },
    };
  } else {
    return {
      provider: 'smtp',
      smtp: {
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER || '',
          pass: process.env.SMTP_PASS || '',
        },
        fromEmail: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        fromName: process.env.SMTP_FROM_NAME || 'Doğal Taş Pazaryeri',
      },
    };
  }
}

/**
 * Email template configurations
 */
export const emailTemplateConfig = {
  // Base template settings
  baseTemplate: {
    logoUrl: process.env.EMAIL_LOGO_URL || 'https://dogaltaspazaryeri.com/logo.png',
    websiteUrl: process.env.WEBSITE_URL || 'https://dogaltaspazaryeri.com',
    supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
    companyName: 'Doğal Taş Pazaryeri',
    companyAddress: 'İstanbul, Türkiye',
  },

  // Color scheme
  colors: {
    primary: '#2563eb',
    secondary: '#64748b',
    success: '#059669',
    warning: '#d97706',
    danger: '#dc2626',
    background: '#f8fafc',
    text: '#1e293b',
    textLight: '#64748b',
  },

  // Typography
  typography: {
    fontFamily: 'Arial, sans-serif',
    fontSize: {
      small: '14px',
      base: '16px',
      large: '18px',
      xl: '24px',
      xxl: '32px',
    },
    lineHeight: '1.6',
  },

  // Layout
  layout: {
    maxWidth: '600px',
    padding: '20px',
    borderRadius: '8px',
  },
};

/**
 * Email subject templates
 */
export const emailSubjects = {
  tr: {
    paymentInstructions: 'Ödeme Talimatları - Sipariş {{orderNumber}}',
    paymentConfirmation: 'Ödeme Onaylandı - Sipariş {{orderNumber}}',
    productionStart: 'Üretime Başlandı - Sipariş {{orderNumber}}',
    goodsReady: 'Ürün Hazır - Onayınız Bekleniyor - Sipariş {{orderNumber}}',
    paymentRelease: 'Ödeme Yapıldı - Sipariş {{orderNumber}}',
    disputeNotification: 'Anlaşmazlık Bildirimi - Sipariş {{orderNumber}}',
    welcome: 'Doğal Taş Pazaryeri\'ne Hoş Geldiniz',
    passwordReset: 'Şifre Sıfırlama Talebi',
    orderConfirmation: 'Sipariş Onaylandı - {{orderNumber}}',
    quoteReceived: 'Yeni Teklif Aldınız - {{quoteNumber}}',
    sampleRequest: 'Numune Talebi - {{requestNumber}}',
  },
  en: {
    paymentInstructions: 'Payment Instructions - Order {{orderNumber}}',
    paymentConfirmation: 'Payment Confirmed - Order {{orderNumber}}',
    productionStart: 'Production Started - Order {{orderNumber}}',
    goodsReady: 'Goods Ready - Approval Required - Order {{orderNumber}}',
    paymentRelease: 'Payment Released - Order {{orderNumber}}',
    disputeNotification: 'Dispute Notification - Order {{orderNumber}}',
    welcome: 'Welcome to Natural Stone Marketplace',
    passwordReset: 'Password Reset Request',
    orderConfirmation: 'Order Confirmed - {{orderNumber}}',
    quoteReceived: 'New Quote Received - {{quoteNumber}}',
    sampleRequest: 'Sample Request - {{requestNumber}}',
  },
};

/**
 * Email validation helpers
 */
export const emailValidation = {
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isValidEmailList: (emails: string[]): boolean => {
    return emails.every(email => emailValidation.isValidEmail(email));
  },

  sanitizeEmail: (email: string): string => {
    return email.toLowerCase().trim();
  },

  sanitizeEmailList: (emails: string[]): string[] => {
    return emails.map(email => emailValidation.sanitizeEmail(email));
  },
};

/**
 * Email rate limiting configuration
 */
export const emailRateLimit = {
  // Maximum emails per minute per user
  perUser: {
    minute: 5,
    hour: 50,
    day: 200,
  },

  // Maximum emails per minute globally
  global: {
    minute: 100,
    hour: 1000,
    day: 10000,
  },

  // Priority email types (bypass rate limiting)
  priority: [
    'password-reset',
    'payment-instructions',
    'payment-confirmation',
    'dispute-notification',
  ],
};

/**
 * Email retry configuration
 */
export const emailRetryConfig = {
  maxRetries: 3,
  retryDelay: 5000, // 5 seconds
  backoffMultiplier: 2,
  maxDelay: 60000, // 1 minute
};

/**
 * Email analytics configuration
 */
export const emailAnalyticsConfig = {
  trackOpens: true,
  trackClicks: true,
  trackUnsubscribes: true,
  webhookUrl: process.env.EMAIL_WEBHOOK_URL,
  analyticsEnabled: process.env.EMAIL_ANALYTICS_ENABLED === 'true',
};

/**
 * Get localized email subject
 */
export function getEmailSubject(
  templateName: keyof typeof emailSubjects.tr,
  language: 'tr' | 'en' = 'tr',
  variables: Record<string, string> = {}
): string {
  const subjects = emailSubjects[language] || emailSubjects.tr;
  let subject = subjects[templateName] || subjects[templateName];

  // Replace variables in subject
  Object.entries(variables).forEach(([key, value]) => {
    subject = subject.replace(new RegExp(`{{${key}}}`, 'g'), value);
  });

  return subject;
}

/**
 * Email template helper functions
 */
export const emailHelpers = {
  formatCurrency: (amount: number, currency: string = 'TRY'): string => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  },

  formatDate: (date: Date, locale: string = 'tr-TR'): string => {
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  },

  formatDateShort: (date: Date, locale: string = 'tr-TR'): string => {
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    }).format(date);
  },

  truncateText: (text: string, maxLength: number = 100): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  },

  generateTrackingUrl: (emailId: string, action: 'open' | 'click', url?: string): string => {
    const baseUrl = process.env.EMAIL_TRACKING_URL || 'https://dogaltaspazaryeri.com/email-tracking';
    const params = new URLSearchParams({
      id: emailId,
      action,
      ...(url && { url }),
    });
    return `${baseUrl}?${params.toString()}`;
  },
};
