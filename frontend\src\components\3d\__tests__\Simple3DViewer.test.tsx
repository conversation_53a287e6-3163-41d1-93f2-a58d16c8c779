import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Simple3DViewer from '../Simple3DViewer';

// Mock react-three-fiber
jest.mock('@react-three/fiber', () => ({
  Canvas: ({ children, ...props }: any) => (
    <div data-testid="canvas" {...props}>
      {children}
    </div>
  ),
  useFrame: jest.fn(),
}));

// Mock @react-three/drei
jest.mock('@react-three/drei/core/OrbitControls', () => ({
  OrbitControls: () => <div data-testid="orbit-controls" />,
}));

// Mock three.js
jest.mock('three', () => ({
  __esModule: true,
  ...jest.requireActual('three'),
}));

describe('Simple3DViewer', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<Simple3DViewer />);

    // Check if the main container is rendered
    const container = screen.getByTestId('canvas');
    expect(container).toBeInTheDocument();
  });

  it('renders the Canvas component', () => {
    render(<Simple3DViewer />);

    // Check if Canvas is rendered
    const canvas = screen.getByTestId('canvas');
    expect(canvas).toBeInTheDocument();
  });

  it('renders OrbitControls', () => {
    render(<Simple3DViewer />);

    // Check if OrbitControls is rendered
    const orbitControls = screen.getByTestId('orbit-controls');
    expect(orbitControls).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const customClass = 'custom-viewer-class';
    const { container } = render(<Simple3DViewer className={customClass} />);

    // Check if custom class is applied to the main div
    const mainDiv = container.firstChild as HTMLElement;
    expect(mainDiv).toHaveClass(customClass);
  });

  it('renders the UI overlay with correct content', () => {
    render(<Simple3DViewer />);
    
    // Check if the title is rendered
    expect(screen.getByText('3D Doğal Taş Görüntüleyici')).toBeInTheDocument();
    
    // Check if stone type descriptions are rendered
    expect(screen.getByText(/Mermer Küp/)).toBeInTheDocument();
    expect(screen.getByText(/Mermer Küre/)).toBeInTheDocument();
    expect(screen.getByText(/Granit Blok/)).toBeInTheDocument();
    
    // Check if instructions are rendered
    expect(screen.getByText(/Fare ile döndürün, zoom yapın/)).toBeInTheDocument();
  });

  it('has correct default styling', () => {
    const { container } = render(<Simple3DViewer />);

    const mainDiv = container.firstChild as HTMLElement;
    expect(mainDiv).toHaveClass('w-full', 'h-96', 'bg-gray-100', 'rounded-lg', 'overflow-hidden');
  });

  it('combines default and custom classes correctly', () => {
    const customClass = 'border-2 border-red-500';
    const { container } = render(<Simple3DViewer className={customClass} />);

    const mainDiv = container.firstChild as HTMLElement;
    expect(mainDiv).toHaveClass('w-full', 'h-96', 'bg-gray-100', 'rounded-lg', 'overflow-hidden');
    expect(mainDiv).toHaveClass('border-2', 'border-red-500');
  });

  it('renders with proper accessibility attributes', () => {
    const { container } = render(<Simple3DViewer />);

    // The main container should be accessible
    const mainDiv = container.firstChild as HTMLElement;
    expect(mainDiv).toBeInTheDocument();
  });

  it('handles empty className prop', () => {
    const { container } = render(<Simple3DViewer className="" />);

    const mainDiv = container.firstChild as HTMLElement;
    expect(mainDiv).toHaveClass('w-full', 'h-96', 'bg-gray-100', 'rounded-lg', 'overflow-hidden');
  });

  it('renders without className prop', () => {
    const { container } = render(<Simple3DViewer />);

    const mainDiv = container.firstChild as HTMLElement;
    expect(mainDiv).toHaveClass('w-full', 'h-96', 'bg-gray-100', 'rounded-lg', 'overflow-hidden');
  });
});
