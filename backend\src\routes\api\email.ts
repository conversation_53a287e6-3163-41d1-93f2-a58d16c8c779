import { Router, Request, Response } from 'express';
import { EmailController } from '../../controllers/EmailController';
import { authMiddleware } from '../../middleware/authMiddleware';
import { asyncHandler } from '../../middleware/errorHandler';

const router = Router();
const emailController = new EmailController();

// Apply authentication middleware to all routes
router.use(authMiddleware);

/**
 * @route POST /api/email/send
 * @desc Send single email
 * @access Private (Admin only)
 */
router.post('/send', emailController.sendEmail);

/**
 * @route POST /api/email/send-bulk
 * @desc Send bulk emails
 * @access Private (Admin only)
 */
router.post('/send-bulk', emailController.sendBulkEmail);

/**
 * @route POST /api/email/test
 * @desc Test email configuration
 * @access Private (Admin only)
 */
router.post('/test', emailController.testEmail);

/**
 * @route GET /api/email/templates
 * @desc Get available email templates
 * @access Private (Admin only)
 */
router.get('/templates', emailController.getTemplates);

/**
 * @route GET /api/email/stats
 * @desc Get email statistics
 * @access Private (Admin only)
 */
router.get('/stats', emailController.getEmailStats);

/**
 * @route POST /api/email/escrow-notification
 * @desc Send escrow-related notification emails
 * @access Private (System/Internal use)
 */
router.post('/escrow-notification', emailController.sendEscrowNotification);

/**
 * @route GET /api/email/health
 * @desc Check email service health
 * @access Private (Admin only)
 */
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    provider: process.env.EMAIL_PROVIDER || 'smtp',
    configuration: {
      sendgrid: {
        configured: !!(process.env.SENDGRID_API_KEY && process.env.SENDGRID_FROM_EMAIL),
        fromEmail: process.env.SENDGRID_FROM_EMAIL || 'not configured',
      },
      smtp: {
        configured: !!(process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS),
        host: process.env.SMTP_HOST || 'not configured',
        port: process.env.SMTP_PORT || 'not configured',
        fromEmail: process.env.SMTP_FROM_EMAIL || 'not configured',
      },
    },
    templates: {
      available: [
        'payment-instructions',
        'payment-confirmation',
        'production-start',
        'goods-ready',
        'payment-release',
        'dispute-notification',
        'welcome',
        'password-reset',
      ],
      location: 'src/templates/email/',
    },
  };

  res.json({
    success: true,
    data: health
  });
}));

/**
 * @route GET /api/email/config
 * @desc Get email configuration (safe info only)
 * @access Private (Admin only)
 */
router.get('/config', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const config = {
    provider: process.env.EMAIL_PROVIDER || 'smtp',
    fromEmail: process.env.EMAIL_PROVIDER === 'sendgrid' 
      ? process.env.SENDGRID_FROM_EMAIL 
      : process.env.SMTP_FROM_EMAIL,
    fromName: process.env.EMAIL_PROVIDER === 'sendgrid'
      ? process.env.SENDGRID_FROM_NAME
      : process.env.SMTP_FROM_NAME,
    rateLimits: {
      perUser: {
        minute: 5,
        hour: 50,
        day: 200,
      },
      global: {
        minute: 100,
        hour: 1000,
        day: 10000,
      },
    },
    features: {
      templates: true,
      bulkEmail: true,
      analytics: process.env.EMAIL_ANALYTICS_ENABLED === 'true',
      tracking: true,
    },
  };

  res.json({
    success: true,
    data: config
  });
}));

/**
 * @route POST /api/email/preview
 * @desc Preview email template with data
 * @access Private (Admin only)
 */
router.post('/preview', asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;
  
  if (!user || user.userType !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  const { templateName, templateData } = req.body;

  if (!templateName) {
    return res.status(400).json({
      success: false,
      error: 'Template name is required'
    });
  }

  try {
    // This would render the template and return HTML
    // For now, return a mock preview
    const preview = {
      templateName,
      subject: templateData?.subject || 'Email Preview',
      html: `<h1>Preview for ${templateName}</h1><p>This is a preview of the email template with provided data.</p>`,
      variables: templateData || {},
    };

    res.json({
      success: true,
      data: preview
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to generate preview'
    });
  }
}));

export default router;
