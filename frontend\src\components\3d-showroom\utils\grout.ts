import * as THREE from 'three';

export interface GroutSettings {
  width: number; // in mm
  color: string;
  opacity: number;
  visible: boolean;
  style: 'flat' | 'recessed' | 'raised';
}

export const DEFAULT_GROUT_SETTINGS: GroutSettings = {
  width: 2,
  color: '#e2e8f0',
  opacity: 1,
  visible: true,
  style: 'recessed'
};

export const GROUT_PRESETS = {
  minimal: {
    width: 1,
    color: '#f8f9fa',
    opacity: 0.8,
    visible: true,
    style: 'flat' as const
  },
  standard: {
    width: 2,
    color: '#e2e8f0',
    opacity: 1,
    visible: true,
    style: 'recessed' as const
  },
  bold: {
    width: 4,
    color: '#94a3b8',
    opacity: 1,
    visible: true,
    style: 'recessed' as const
  },
  contrast: {
    width: 3,
    color: '#1f2937',
    opacity: 1,
    visible: true,
    style: 'raised' as const
  },
  invisible: {
    width: 0,
    color: '#ffffff',
    opacity: 0,
    visible: false,
    style: 'flat' as const
  }
};

export class GroutManager {
  static createGroutTexture(
    tileSize: { width: number; height: number },
    groutSettings: GroutSettings,
    baseColor: string = '#f8f9fa',
    textureSize: number = 512
  ): THREE.CanvasTexture {
    const canvas = document.createElement('canvas');
    canvas.width = textureSize;
    canvas.height = textureSize;
    const ctx = canvas.getContext('2d')!;

    // Calculate tile and grout dimensions
    const groutWidthPx = (groutSettings.width / 10) * (textureSize / 100); // Convert mm to pixels
    const tileWidthPx = (tileSize.width / 100) * (textureSize / 100); // Convert cm to pixels
    const tileHeightPx = (tileSize.height / 100) * (textureSize / 100);

    // Fill background with grout color
    if (groutSettings.visible && groutSettings.width > 0) {
      ctx.fillStyle = groutSettings.color;
      ctx.globalAlpha = groutSettings.opacity;
      ctx.fillRect(0, 0, textureSize, textureSize);
    }

    // Draw tiles
    ctx.fillStyle = baseColor;
    ctx.globalAlpha = 1;

    const tilesX = Math.ceil(textureSize / (tileWidthPx + groutWidthPx));
    const tilesY = Math.ceil(textureSize / (tileHeightPx + groutWidthPx));

    for (let x = 0; x < tilesX; x++) {
      for (let y = 0; y < tilesY; y++) {
        const tileX = x * (tileWidthPx + groutWidthPx) + groutWidthPx / 2;
        const tileY = y * (tileHeightPx + groutWidthPx) + groutWidthPx / 2;

        // Apply grout style effects
        this.drawTileWithGroutStyle(
          ctx,
          tileX,
          tileY,
          tileWidthPx - groutWidthPx,
          tileHeightPx - groutWidthPx,
          groutSettings,
          baseColor
        );
      }
    }

    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
    return texture;
  }

  private static drawTileWithGroutStyle(
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    groutSettings: GroutSettings,
    baseColor: string
  ): void {
    ctx.save();

    switch (groutSettings.style) {
      case 'flat':
        // Simple flat tile
        ctx.fillStyle = baseColor;
        ctx.fillRect(x, y, width, height);
        break;

      case 'recessed':
        // Tile appears recessed into grout
        ctx.fillStyle = baseColor;
        ctx.fillRect(x, y, width, height);
        
        // Add subtle shadow effect
        const gradient = ctx.createLinearGradient(x, y, x + width, y + height);
        gradient.addColorStop(0, 'rgba(0,0,0,0.1)');
        gradient.addColorStop(1, 'rgba(0,0,0,0)');
        ctx.fillStyle = gradient;
        ctx.fillRect(x, y, width, height);
        break;

      case 'raised':
        // Tile appears raised above grout
        ctx.fillStyle = baseColor;
        ctx.fillRect(x, y, width, height);
        
        // Add highlight effect
        const highlightGradient = ctx.createLinearGradient(x, y, x + width, y + height);
        highlightGradient.addColorStop(0, 'rgba(255,255,255,0.2)');
        highlightGradient.addColorStop(1, 'rgba(255,255,255,0)');
        ctx.fillStyle = highlightGradient;
        ctx.fillRect(x, y, width, height);
        break;
    }

    ctx.restore();
  }

  static createGroutNormalMap(
    tileSize: { width: number; height: number },
    groutSettings: GroutSettings,
    textureSize: number = 512
  ): THREE.CanvasTexture {
    const canvas = document.createElement('canvas');
    canvas.width = textureSize;
    canvas.height = textureSize;
    const ctx = canvas.getContext('2d')!;

    // Create normal map for grout depth effect
    const groutWidthPx = (groutSettings.width / 10) * (textureSize / 100);
    const tileWidthPx = (tileSize.width / 100) * (textureSize / 100);
    const tileHeightPx = (tileSize.height / 100) * (textureSize / 100);

    // Fill with neutral normal (pointing up)
    ctx.fillStyle = '#8080ff';
    ctx.fillRect(0, 0, textureSize, textureSize);

    // Draw grout lines with different normal values
    if (groutSettings.visible && groutSettings.width > 0) {
      const groutNormalColor = groutSettings.style === 'recessed' ? '#4040ff' : '#c0c0ff';
      ctx.fillStyle = groutNormalColor;

      const tilesX = Math.ceil(textureSize / (tileWidthPx + groutWidthPx));
      const tilesY = Math.ceil(textureSize / (tileHeightPx + groutWidthPx));

      // Draw horizontal grout lines
      for (let y = 0; y < tilesY; y++) {
        const groutY = y * (tileHeightPx + groutWidthPx);
        ctx.fillRect(0, groutY, textureSize, groutWidthPx);
      }

      // Draw vertical grout lines
      for (let x = 0; x < tilesX; x++) {
        const groutX = x * (tileWidthPx + groutWidthPx);
        ctx.fillRect(groutX, 0, groutWidthPx, textureSize);
      }
    }

    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
    return texture;
  }

  static applyGroutToMaterial(
    material: THREE.MeshStandardMaterial,
    tileSize: { width: number; height: number },
    groutSettings: GroutSettings,
    baseColor: string
  ): THREE.MeshStandardMaterial {
    // Create main texture with grout
    const mainTexture = this.createGroutTexture(tileSize, groutSettings, baseColor);
    material.map = mainTexture;

    // Create normal map for depth effect
    if (groutSettings.style !== 'flat') {
      const normalTexture = this.createGroutNormalMap(tileSize, groutSettings);
      material.normalMap = normalTexture;
      material.normalScale = new THREE.Vector2(0.5, 0.5);
    }

    material.needsUpdate = true;
    return material;
  }

  static getGroutColor(baseColor: string, contrast: number = 0.2): string {
    const color = new THREE.Color(baseColor);
    
    // Calculate luminance
    const luminance = 0.299 * color.r + 0.587 * color.g + 0.114 * color.b;
    
    // If base color is light, make grout darker; if dark, make grout lighter
    if (luminance > 0.5) {
      color.multiplyScalar(1 - contrast);
    } else {
      color.addScalar(contrast);
    }
    
    return `#${color.getHexString()}`;
  }

  static validateGroutSettings(settings: Partial<GroutSettings>): GroutSettings {
    return {
      width: Math.max(0, Math.min(10, settings.width || DEFAULT_GROUT_SETTINGS.width)),
      color: settings.color || DEFAULT_GROUT_SETTINGS.color,
      opacity: Math.max(0, Math.min(1, settings.opacity || DEFAULT_GROUT_SETTINGS.opacity)),
      visible: settings.visible !== undefined ? settings.visible : DEFAULT_GROUT_SETTINGS.visible,
      style: settings.style || DEFAULT_GROUT_SETTINGS.style
    };
  }
}

export default GroutManager;
