// Trade Map API Integration
// ITC Trade Map API entegrasyonu - Uluslararası ticaret verileri

import axios, { AxiosInstance } from 'axios';
import { EventEmitter } from 'events';

export interface TradeMapConfig {
  apiKey: string;
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
}

export interface ImportData {
  productCode: string;
  productName: string;
  importerCountry: string;
  importerCountryName: string;
  timeframe: string;
  totalValue: number; // USD
  totalQuantity: number; // Tons
  averageUnitPrice: number; // USD per ton
  growthRate: number; // %
  marketShare: number; // %
  topSuppliers: Supplier[];
  monthlyData: MonthlyData[];
  lastUpdated: Date;
}

export interface Supplier {
  country: string;
  countryName: string;
  value: number;
  quantity: number;
  marketShare: number;
  unitPrice: number;
  growthRate: number;
}

export interface MonthlyData {
  month: string;
  value: number;
  quantity: number;
  unitPrice: number;
}

export interface ExportOpportunity {
  targetCountry: string;
  targetCountryName: string;
  productCode: string;
  opportunityScore: number;
  marketSize: number;
  growthRate: number;
  currentMarketShare: number;
  potentialMarketShare: number;
  averageImportPrice: number;
  mainCompetitors: Supplier[];
  entryBarriers: string[];
  recommendedStrategy: string;
  estimatedRevenue: number;
  riskLevel: 'low' | 'medium' | 'high';
  timeToMarket: string;
}

export class TradeMapAPI extends EventEmitter {
  private client: AxiosInstance;
  private config: TradeMapConfig;
  private cache: Map<string, any> = new Map();
  private rateLimitDelay: number = 1000; // 1 second between requests

  constructor(config: TradeMapConfig) {
    super();
    this.config = config;
    this.client = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Natural-Stone-Marketplace/1.0'
      }
    });

    this.setupInterceptors();
  }

  /**
   * İthalat verilerini getir
   */
  public async getImportData(
    productCode: string,
    importerCountry: string,
    timeframe: string = 'latest'
  ): Promise<ImportData> {
    const cacheKey = `import_${productCode}_${importerCountry}_${timeframe}`;
    
    // Cache kontrolü
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < 24 * 60 * 60 * 1000) { // 24 saat
        return cached.data;
      }
    }

    try {
      await this.rateLimitWait();
      
      const response = await this.client.get('/imports', {
        params: {
          product: productCode, // HS Code: 2515 (Marble), 2516 (Granite)
          reporter: importerCountry,
          period: timeframe,
          format: 'json'
        }
      });

      const importData: ImportData = {
        productCode,
        productName: response.data.productName || 'Natural Stone',
        importerCountry,
        importerCountryName: response.data.reporterName || importerCountry,
        timeframe,
        totalValue: response.data.totalValue || 0,
        totalQuantity: response.data.totalQuantity || 0,
        averageUnitPrice: response.data.averagePrice || 0,
        growthRate: response.data.growthRate || 0,
        marketShare: response.data.marketShare || 0,
        topSuppliers: this.parseSuppliers(response.data.suppliers || []),
        monthlyData: this.parseMonthlyData(response.data.monthlyBreakdown || []),
        lastUpdated: new Date()
      };

      // Cache'e kaydet
      this.cache.set(cacheKey, {
        data: importData,
        timestamp: Date.now()
      });

      this.emit('dataFetched', { type: 'import', country: importerCountry, product: productCode });
      return importData;

    } catch (error) {
      this.emit('error', { type: 'import', error, params: { productCode, importerCountry, timeframe } });
      throw new Error(`Trade Map import data fetch failed: ${error}`);
    }
  }

  /**
   * İhracat fırsatlarını analiz et
   */
  public async analyzeExportOpportunities(
    productCode: string,
    exporterCountry: string = 'TR'
  ): Promise<ExportOpportunity[]> {
    try {
      await this.rateLimitWait();

      const response = await this.client.get('/export-opportunities', {
        params: {
          product: productCode,
          exporter: exporterCountry,
          format: 'json'
        }
      });

      const opportunities: ExportOpportunity[] = [];

      for (const country of response.data.countries || []) {
        const opportunity: ExportOpportunity = {
          targetCountry: country.code,
          targetCountryName: country.name,
          productCode,
          opportunityScore: this.calculateOpportunityScore(country),
          marketSize: country.totalValue || 0,
          growthRate: country.growthRate || 0,
          currentMarketShare: this.getCurrentMarketShare(country, exporterCountry),
          potentialMarketShare: this.estimatePotentialShare(country),
          averageImportPrice: country.averagePrice || 0,
          mainCompetitors: this.parseSuppliers(country.suppliers || []).slice(0, 5),
          entryBarriers: this.analyzeEntryBarriers(country),
          recommendedStrategy: this.generateEntryStrategy(country),
          estimatedRevenue: this.estimateRevenuePotential(country),
          riskLevel: this.assessRiskLevel(country),
          timeToMarket: this.estimateTimeToMarket(country)
        };

        if (opportunity.opportunityScore > 60) {
          opportunities.push(opportunity);
        }
      }

      // Fırsat skoruna göre sırala
      opportunities.sort((a, b) => b.opportunityScore - a.opportunityScore);

      this.emit('opportunitiesAnalyzed', { 
        product: productCode, 
        exporter: exporterCountry, 
        count: opportunities.length 
      });

      return opportunities;

    } catch (error) {
      this.emit('error', { type: 'opportunities', error, params: { productCode, exporterCountry } });
      throw new Error(`Trade Map opportunities analysis failed: ${error}`);
    }
  }

  /**
   * Pazar trend analizi
   */
  public async getMarketTrends(
    productCode: string,
    timeframe: string = '5years'
  ): Promise<any> {
    try {
      await this.rateLimitWait();

      const response = await this.client.get('/market-trends', {
        params: {
          product: productCode,
          period: timeframe,
          format: 'json'
        }
      });

      const trends = {
        productCode,
        timeframe,
        globalTrends: {
          totalTradeValue: response.data.globalValue || 0,
          averageGrowthRate: this.calculateAverageGrowth(response.data.yearlyData || []),
          volatilityIndex: this.calculateVolatility(response.data.yearlyData || []),
          seasonalPatterns: this.identifySeasonalPatterns(response.data.monthlyData || [])
        },
        topMarkets: (response.data.topImporters || []).map((market: any) => ({
          country: market.country,
          countryName: market.countryName,
          currentValue: market.currentValue || 0,
          growthRate: market.growthRate || 0,
          marketStability: this.assessMarketStability(market),
          futureProjection: this.projectFutureGrowth(market)
        })),
        emergingMarkets: this.identifyEmergingMarkets(response.data),
        decliningMarkets: this.identifyDecliningMarkets(response.data),
        lastUpdated: new Date()
      };

      this.emit('trendsAnalyzed', { product: productCode, timeframe });
      return trends;

    } catch (error) {
      this.emit('error', { type: 'trends', error, params: { productCode, timeframe } });
      throw new Error(`Trade Map trends analysis failed: ${error}`);
    }
  }

  /**
   * Rate limiting
   */
  private async rateLimitWait(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, this.rateLimitDelay));
  }

  /**
   * Axios interceptors kurulumu
   */
  private setupInterceptors(): void {
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 429) {
          // Rate limit hit, wait and retry
          await new Promise(resolve => setTimeout(resolve, 5000));
          return this.client.request(error.config);
        }
        return Promise.reject(error);
      }
    );
  }

  // Yardımcı metodlar
  private parseSuppliers(suppliers: any[]): Supplier[] {
    return suppliers.map(supplier => ({
      country: supplier.country || '',
      countryName: supplier.countryName || '',
      value: supplier.value || 0,
      quantity: supplier.quantity || 0,
      marketShare: supplier.share || 0,
      unitPrice: supplier.unitPrice || 0,
      growthRate: supplier.growthRate || 0
    }));
  }

  private parseMonthlyData(monthlyData: any[]): MonthlyData[] {
    return monthlyData.map(month => ({
      month: month.month || '',
      value: month.value || 0,
      quantity: month.quantity || 0,
      unitPrice: month.unitPrice || 0
    }));
  }

  private calculateOpportunityScore(country: any): number {
    let score = 0;
    
    // Market size (40%)
    const marketSize = country.totalValue || 0;
    score += Math.min(40, (marketSize / 1000000) * 4); // $1M = 4 points
    
    // Growth rate (30%)
    const growthRate = country.growthRate || 0;
    score += Math.min(30, Math.max(0, growthRate * 3)); // 10% growth = 30 points
    
    // Competition level (20%)
    const competitorCount = (country.suppliers || []).length;
    score += Math.max(0, 20 - competitorCount); // Fewer competitors = higher score
    
    // Price level (10%)
    const avgPrice = country.averagePrice || 0;
    if (avgPrice > 500) score += 10; // Premium market
    else if (avgPrice > 200) score += 5; // Mid-range market
    
    return Math.min(100, score);
  }

  private getCurrentMarketShare(country: any, exporterCountry: string): number {
    const suppliers = country.suppliers || [];
    const exporter = suppliers.find((s: any) => s.country === exporterCountry);
    return exporter?.share || 0;
  }

  private estimatePotentialShare(country: any): number {
    const currentShare = this.getCurrentMarketShare(country, 'TR');
    const competitorCount = (country.suppliers || []).length;
    
    // Basit tahmin: mevcut payın 2-5 katı (rekabet seviyesine göre)
    const multiplier = Math.max(2, 6 - (competitorCount / 2));
    return Math.min(25, currentShare * multiplier); // Max %25 pazar payı
  }

  private analyzeEntryBarriers(country: any): string[] {
    const barriers = [];
    
    if ((country.suppliers || []).length > 10) {
      barriers.push('High competition');
    }
    
    if (country.averagePrice < 200) {
      barriers.push('Price-sensitive market');
    }
    
    // Daha fazla analiz eklenebilir
    return barriers;
  }

  private generateEntryStrategy(country: any): string {
    const competitorCount = (country.suppliers || []).length;
    const avgPrice = country.averagePrice || 0;
    
    if (competitorCount < 5 && avgPrice > 500) {
      return 'Premium positioning with quality focus';
    } else if (competitorCount > 10) {
      return 'Differentiation through unique value proposition';
    } else {
      return 'Competitive pricing with quality assurance';
    }
  }

  private estimateRevenuePotential(country: any): number {
    const marketSize = country.totalValue || 0;
    const potentialShare = this.estimatePotentialShare(country);
    return (marketSize * potentialShare) / 100;
  }

  private assessRiskLevel(country: any): 'low' | 'medium' | 'high' {
    const competitorCount = (country.suppliers || []).length;
    const growthRate = country.growthRate || 0;
    
    if (competitorCount > 15 || growthRate < -5) return 'high';
    if (competitorCount > 8 || growthRate < 0) return 'medium';
    return 'low';
  }

  private estimateTimeToMarket(country: any): string {
    const competitorCount = (country.suppliers || []).length;
    
    if (competitorCount < 5) return '3-6 months';
    if (competitorCount < 10) return '6-12 months';
    return '12-18 months';
  }

  private calculateAverageGrowth(yearlyData: any[]): number {
    if (yearlyData.length < 2) return 0;
    
    const growthRates = [];
    for (let i = 1; i < yearlyData.length; i++) {
      const current = yearlyData[i].value || 0;
      const previous = yearlyData[i - 1].value || 0;
      if (previous > 0) {
        growthRates.push(((current - previous) / previous) * 100);
      }
    }
    
    return growthRates.length > 0 
      ? growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length 
      : 0;
  }

  private calculateVolatility(yearlyData: any[]): number {
    const values = yearlyData.map(d => d.value || 0);
    if (values.length < 2) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance) / mean; // Coefficient of variation
  }

  private identifySeasonalPatterns(monthlyData: any[]): any {
    // Basit mevsimsel analiz
    const monthlyAvg = new Array(12).fill(0);
    const monthlyCount = new Array(12).fill(0);
    
    monthlyData.forEach(data => {
      const month = new Date(data.month).getMonth();
      monthlyAvg[month] += data.value || 0;
      monthlyCount[month]++;
    });
    
    for (let i = 0; i < 12; i++) {
      if (monthlyCount[i] > 0) {
        monthlyAvg[i] /= monthlyCount[i];
      }
    }
    
    return {
      monthlyAverages: monthlyAvg,
      peakMonths: monthlyAvg.map((val, idx) => ({ month: idx, value: val }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 3)
        .map(item => item.month)
    };
  }

  private assessMarketStability(market: any): 'stable' | 'volatile' | 'declining' {
    const growthRate = market.growthRate || 0;
    const volatility = market.volatility || 0;
    
    if (growthRate < -10) return 'declining';
    if (volatility > 0.3) return 'volatile';
    return 'stable';
  }

  private projectFutureGrowth(market: any): number {
    const currentGrowth = market.growthRate || 0;
    const stability = this.assessMarketStability(market);
    
    switch (stability) {
      case 'declining': return Math.max(-15, currentGrowth - 5);
      case 'volatile': return currentGrowth * 0.7; // Reduce by 30%
      default: return Math.min(20, currentGrowth * 1.1); // Increase by 10%
    }
  }

  private identifyEmergingMarkets(data: any): any[] {
    return (data.topImporters || [])
      .filter((market: any) => (market.growthRate || 0) > 15)
      .slice(0, 5);
  }

  private identifyDecliningMarkets(data: any): any[] {
    return (data.topImporters || [])
      .filter((market: any) => (market.growthRate || 0) < -5)
      .slice(0, 5);
  }

  /**
   * Cache temizleme
   */
  public clearCache(): void {
    this.cache.clear();
    this.emit('cacheCleared');
  }

  /**
   * API durumu kontrolü
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.client.get('/health');
      return true;
    } catch {
      return false;
    }
  }
}
