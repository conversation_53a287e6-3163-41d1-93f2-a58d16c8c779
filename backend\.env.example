# Environment
NODE_ENV=development

# Server Configuration
PORT=8000
FRONTEND_URL=http://localhost:3000
ADMIN_URL=http://localhost:3001

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/natural_stone_marketplace?schema=public"

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Natural Stone Marketplace"

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# AWS S3 Configuration (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=natural-stone-marketplace

# Payment Gateway Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=sandbox

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# AI Services Configuration
OPENAI_API_KEY=your-openai-api-key
AI_SERVICES_URL=http://localhost:8001

# AI Chatbot Configuration
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=500
CHATBOT_SESSION_TTL=86400
CHATBOT_MAX_CONVERSATION_LENGTH=50
CHATBOT_CONFIDENCE_THRESHOLD=0.3
CHATBOT_SENTIMENT_THRESHOLD=-0.5

# Monitoring and Logging
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this-in-production

# External APIs
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
CURRENCY_API_KEY=your-currency-api-key

# Elasticsearch Configuration (Optional)
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
