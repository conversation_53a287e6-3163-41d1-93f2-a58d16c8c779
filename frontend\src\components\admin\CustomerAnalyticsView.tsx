'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Package, 
  ShoppingCart,
  CreditCard,
  Truck,
  Calendar,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

interface CustomerAnalyticsViewProps {
  customerId: string;
}

// Mock customer data - gerçek uygulamada API'den gelecek
const mockCustomers = {
  'ABC İnşaat Ltd.': { id: '1', name: 'ABC İnşaat Ltd.', email: '<EMAIL>', phone: '0532 123 45 67' },
  'XYZ Yapı A.Ş.': { id: '2', name: 'XYZ Yapı A.Ş.', email: '<EMAIL>', phone: '0533 987 65 43' },
  'DEF Mimarlık': { id: '3', name: '<PERSON><PERSON>mar<PERSON>ı<PERSON>', email: '<EMAIL>', phone: '0534 555 66 77' }
};

// Mock data - gerçek uygulamada customer analytics API'sinden gelecek
const mockCustomerAnalytics = {
  // Satış Analizi
  sales: {
    totalRevenue: 7500.00,
    totalProfit: 2250.00,
    profitMargin: 30.0,
    totalSales: 5,
    deliveredSales: 3,
    salesData: [
      {
        id: 1,
        productName: 'Beyaz Mermer',
        category: 'Mermer',
        dimensions: '2.0 x 60 x 120 cm',
        quantity: 15.5,
        salePrice: 2500.00,
        saleDate: '2024-01-15',
        paymentStatus: 'Ödendi',
        paymentMethod: 'Banka Havalesi',
        deliveryStatus: 'Teslim Edildi',
        orderNumber: 'SIP-2024-001',
        companyName: 'ABC İnşaat Ltd.'
      },
      {
        id: 2,
        productName: 'Gri Granit',
        category: 'Granit',
        dimensions: '3.0 x 80 x 100 cm',
        quantity: 22.0,
        salePrice: 3200.00,
        saleDate: '2024-01-20',
        paymentStatus: 'Kısmi Ödendi',
        paymentMethod: 'Çek',
        deliveryStatus: 'Hazırlanıyor',
        orderNumber: 'SIP-2024-002',
        companyName: 'XYZ Yapı A.Ş.'
      },
      {
        id: 3,
        productName: 'Traverten Doğal',
        category: 'Traverten',
        dimensions: '2.5 x 40 x 80 cm',
        quantity: 8.5,
        salePrice: 1800.00,
        saleDate: '2024-01-25',
        paymentStatus: 'Bekliyor',
        paymentMethod: 'Kredi Kartı',
        deliveryStatus: 'Bekliyor',
        orderNumber: 'SIP-2024-003',
        companyName: 'DEF Mimarlık'
      }
    ]
  },

  // Alım Analizi (Purchases)
  purchases: {
    totalPurchases: 4200.00,
    totalQuantity: 45.0,
    avgPrice: 93.33,
    purchaseCount: 3,
    purchasesData: [
      {
        id: 1,
        productName: 'Ham Mermer Blok',
        supplier: 'Afyon Taş Ocağı',
        quantity: 20.0,
        unitPrice: 80.00,
        totalPrice: 1600.00,
        purchaseDate: '2024-01-10',
        status: 'Teslim Alındı'
      },
      {
        id: 2,
        productName: 'Granit Ham Malzeme',
        supplier: 'Ege Granit A.Ş.',
        quantity: 15.0,
        unitPrice: 120.00,
        totalPrice: 1800.00,
        purchaseDate: '2024-01-15',
        status: 'Bekliyor'
      },
      {
        id: 3,
        productName: 'Traverten Blok',
        supplier: 'Denizli Doğal Taş',
        quantity: 10.0,
        unitPrice: 80.00,
        totalPrice: 800.00,
        purchaseDate: '2024-01-20',
        status: 'Kargoda'
      }
    ]
  },

  // Giderler
  expenses: {
    totalExpenses: 4500.00,
    monthlyExpenses: 1500.00,
    expenseCount: 3,
    recurringExpenses: 1,
    expensesData: [
      {
        id: 1,
        title: 'Ocak Ayı Nakliye Ücreti',
        category: 'Nakliye Ücreti',
        amount: 1200.00,
        date: '2024-01-15',
        supplier: 'ABC Lojistik',
        paymentMethod: 'Banka Havalesi',
        isRecurring: false
      },
      {
        id: 2,
        title: 'Gümrük Vergisi - Mermer İthalatı',
        category: 'Gümrük Vergisi',
        amount: 2500.00,
        date: '2024-01-20',
        supplier: 'Gümrük Müşavirliği',
        paymentMethod: 'Çek',
        isRecurring: false
      },
      {
        id: 3,
        title: 'Aylık Depo Kirası',
        category: 'Kira',
        amount: 800.00,
        date: '2024-01-01',
        supplier: 'XYZ Gayrimenkul',
        paymentMethod: 'Banka Havalesi',
        isRecurring: true
      }
    ]
  },

  // Kar-Zarar
  profitLoss: {
    totalRevenue: 7500.00,
    totalExpenses: 4500.00,
    netProfit: 3000.00,
    profitMargin: 40.0,
    monthlyTrend: [
      { month: 'Ocak', revenue: 7500, expenses: 4500, profit: 3000 }
    ]
  }
};

export default function CustomerAnalyticsView({ customerId }: CustomerAnalyticsViewProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [selectedSaleCustomer, setSelectedSaleCustomer] = useState<any>(null);

  const handleCustomerClick = (sale: any) => {
    // Satış için girilen müşteri bilgilerini göster
    setSelectedSaleCustomer({
      companyName: sale.companyName,
      orderNumber: sale.orderNumber,
      productName: sale.productName,
      salePrice: sale.salePrice,
      saleDate: sale.saleDate,
      // Mock customer contact details - gerçek uygulamada sale objesinde olacak
      contactPerson: 'Ahmet Yılmaz',
      phone: '0532 123 45 67',
      email: '<EMAIL>',
      address: 'Atatürk Cad. No:123 Şişli/İstanbul',
      taxNumber: '1234567890',
      taxOffice: 'Şişli Vergi Dairesi',
      notes: 'VIP müşteri, özel indirim uygulandı'
    });
    setShowCustomerModal(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Ödendi':
      case 'Teslim Edildi':
      case 'Teslim Alındı':
        return 'bg-green-100 text-green-800';
      case 'Kısmi Ödendi':
      case 'Hazırlanıyor':
      case 'Kargoda':
        return 'bg-yellow-100 text-yellow-800';
      case 'Bekliyor':
        return 'bg-gray-100 text-gray-800';
      case 'Gecikmiş':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'Nakliye Ücreti': 'bg-blue-100 text-blue-800',
      'Gümrük Vergisi': 'bg-red-100 text-red-800',
      'Kira': 'bg-green-100 text-green-800',
      'Elektrik': 'bg-yellow-100 text-yellow-800',
      'Su': 'bg-cyan-100 text-cyan-800',
      'İnternet': 'bg-purple-100 text-purple-800',
      'Telefon': 'bg-pink-100 text-pink-800',
      'Personel Maaşı': 'bg-indigo-100 text-indigo-800',
      'Diğer': 'bg-gray-100 text-gray-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Analytics Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Genel Bakış', icon: TrendingUp },
            { id: 'sales', label: 'Satışlar', icon: ShoppingCart },
            { id: 'purchases', label: 'Alımlar', icon: Package },
            { id: 'expenses', label: 'Giderler', icon: CreditCard },
            { id: 'profit-loss', label: 'Kar-Zarar', icon: DollarSign }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <DollarSign className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Toplam Gelir</p>
                  <p className="text-xl font-bold text-gray-900">
                    ${mockCustomerAnalytics.sales.totalRevenue.toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <ShoppingCart className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Toplam Satış</p>
                  <p className="text-xl font-bold text-gray-900">
                    {mockCustomerAnalytics.sales.totalSales}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-100 rounded-lg">
                  <CreditCard className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Toplam Gider</p>
                  <p className="text-xl font-bold text-gray-900">
                    ${mockCustomerAnalytics.expenses.totalExpenses.toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <TrendingUp className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Net Kar</p>
                  <p className="text-xl font-bold text-gray-900">
                    ${mockCustomerAnalytics.profitLoss.netProfit.toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Quick Summary */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <h4 className="text-lg font-semibold mb-4">Satış Özeti</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Teslim Edilen</span>
                  <span className="font-medium">
                    {mockCustomerAnalytics.sales.deliveredSales} / {mockCustomerAnalytics.sales.totalSales}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Kar Marjı</span>
                  <span className="font-medium text-green-600">
                    %{mockCustomerAnalytics.sales.profitMargin}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Toplam Kar</span>
                  <span className="font-medium">
                    ${mockCustomerAnalytics.sales.totalProfit.toLocaleString()}
                  </span>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="text-lg font-semibold mb-4">Gider Özeti</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Bu Ay</span>
                  <span className="font-medium">
                    ${mockCustomerAnalytics.expenses.monthlyExpenses.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Toplam Kayıt</span>
                  <span className="font-medium">{mockCustomerAnalytics.expenses.expenseCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Düzenli Gider</span>
                  <span className="font-medium">{mockCustomerAnalytics.expenses.recurringExpenses}</span>
                </div>
              </div>
            </Card>
          </div>
        </div>
      )}

      {activeTab === 'sales' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              Satış Kayıtları ({mockCustomerAnalytics.sales.salesData.length})
            </h3>
            <div className="text-sm text-gray-600">
              Toplam: ${mockCustomerAnalytics.sales.totalRevenue.toLocaleString()}
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Ürün</th>
                  <th className="text-left py-3 px-4">Müşteri</th>
                  <th className="text-left py-3 px-4">Ebat & Miktar</th>
                  <th className="text-left py-3 px-4">Fiyat</th>
                  <th className="text-left py-3 px-4">Ödeme</th>
                  <th className="text-left py-3 px-4">Teslimat</th>
                  <th className="text-left py-3 px-4">Tarih</th>
                </tr>
              </thead>
              <tbody>
                {mockCustomerAnalytics.sales.salesData.map((sale) => (
                  <tr key={sale.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div>
                        <div className="font-medium">{sale.productName}</div>
                        <div className="text-sm text-gray-500">{sale.category}</div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <div
                          className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer hover:underline"
                          onClick={() => handleCustomerClick(sale)}
                          title="Satış müşteri bilgilerini görüntüle"
                        >
                          {sale.companyName}
                        </div>
                        <div className="text-sm text-gray-500">{sale.orderNumber}</div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <div className="text-sm">{sale.dimensions}</div>
                        <div className="text-sm text-gray-500">{sale.quantity} m²</div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <div className="font-medium">${sale.salePrice.toLocaleString()}</div>
                        <div className="text-sm text-gray-500">{sale.paymentMethod}</div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Badge className={getStatusColor(sale.paymentStatus)}>
                        {sale.paymentStatus}
                      </Badge>
                    </td>
                    <td className="py-3 px-4">
                      <Badge className={getStatusColor(sale.deliveryStatus)}>
                        {sale.deliveryStatus}
                      </Badge>
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-500">
                      {new Date(sale.saleDate).toLocaleDateString('tr-TR')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === 'purchases' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              Alım Kayıtları ({mockCustomerAnalytics.purchases.purchasesData.length})
            </h3>
            <div className="text-sm text-gray-600">
              Toplam: ${mockCustomerAnalytics.purchases.totalPurchases.toLocaleString()}
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Ürün</th>
                  <th className="text-left py-3 px-4">Tedarikçi</th>
                  <th className="text-left py-3 px-4">Miktar</th>
                  <th className="text-left py-3 px-4">Birim Fiyat</th>
                  <th className="text-left py-3 px-4">Toplam</th>
                  <th className="text-left py-3 px-4">Durum</th>
                  <th className="text-left py-3 px-4">Tarih</th>
                </tr>
              </thead>
              <tbody>
                {mockCustomerAnalytics.purchases.purchasesData.map((purchase) => (
                  <tr key={purchase.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4 font-medium">{purchase.productName}</td>
                    <td className="py-3 px-4">{purchase.supplier}</td>
                    <td className="py-3 px-4">{purchase.quantity} m²</td>
                    <td className="py-3 px-4">${purchase.unitPrice}</td>
                    <td className="py-3 px-4 font-medium">${purchase.totalPrice.toLocaleString()}</td>
                    <td className="py-3 px-4">
                      <Badge className={getStatusColor(purchase.status)}>
                        {purchase.status}
                      </Badge>
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-500">
                      {new Date(purchase.purchaseDate).toLocaleDateString('tr-TR')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === 'expenses' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              Gider Kayıtları ({mockCustomerAnalytics.expenses.expensesData.length})
            </h3>
            <div className="text-sm text-gray-600">
              Toplam: ${mockCustomerAnalytics.expenses.totalExpenses.toLocaleString()}
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Gider</th>
                  <th className="text-left py-3 px-4">Kategori</th>
                  <th className="text-left py-3 px-4">Tutar</th>
                  <th className="text-left py-3 px-4">Tedarikçi</th>
                  <th className="text-left py-3 px-4">Ödeme Yöntemi</th>
                  <th className="text-left py-3 px-4">Tarih</th>
                </tr>
              </thead>
              <tbody>
                {mockCustomerAnalytics.expenses.expensesData.map((expense) => (
                  <tr key={expense.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div>
                        <div className="font-medium">{expense.title}</div>
                        {expense.isRecurring && (
                          <div className="text-xs text-blue-600 mt-1">🔄 Düzenli</div>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Badge className={getCategoryColor(expense.category)}>
                        {expense.category}
                      </Badge>
                    </td>
                    <td className="py-3 px-4 font-medium">${expense.amount.toLocaleString()}</td>
                    <td className="py-3 px-4">{expense.supplier}</td>
                    <td className="py-3 px-4">{expense.paymentMethod}</td>
                    <td className="py-3 px-4 text-sm text-gray-500">
                      {new Date(expense.date).toLocaleDateString('tr-TR')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === 'profit-loss' && (
        <div className="space-y-6">
          {/* Kar-Zarar Özeti */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Toplam Gelir</p>
                  <p className="text-2xl font-bold text-green-600">
                    ${mockCustomerAnalytics.profitLoss.totalRevenue.toLocaleString()}
                  </p>
                </div>
                <TrendingUp className="w-8 h-8 text-green-600" />
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Toplam Gider</p>
                  <p className="text-2xl font-bold text-red-600">
                    ${mockCustomerAnalytics.profitLoss.totalExpenses.toLocaleString()}
                  </p>
                </div>
                <TrendingDown className="w-8 h-8 text-red-600" />
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Net Kar</p>
                  <p className="text-2xl font-bold text-blue-600">
                    ${mockCustomerAnalytics.profitLoss.netProfit.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-500">
                    Kar Marjı: %{mockCustomerAnalytics.profitLoss.profitMargin}
                  </p>
                </div>
                <DollarSign className="w-8 h-8 text-blue-600" />
              </div>
            </Card>
          </div>

          {/* Performans Metrikleri */}
          <Card className="p-6">
            <h4 className="text-lg font-semibold mb-4">Performans Metrikleri</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">
                  ${(mockCustomerAnalytics.sales.totalRevenue / mockCustomerAnalytics.sales.totalSales).toFixed(0)}
                </p>
                <p className="text-sm text-gray-600">Ortalama Satış Değeri</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  %{mockCustomerAnalytics.sales.profitMargin}
                </p>
                <p className="text-sm text-gray-600">Kar Marjı</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round((mockCustomerAnalytics.sales.deliveredSales / mockCustomerAnalytics.sales.totalSales) * 100)}%
                </p>
                <p className="text-sm text-gray-600">Teslimat Oranı</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-600">
                  ${(mockCustomerAnalytics.expenses.totalExpenses / mockCustomerAnalytics.expenses.expenseCount).toFixed(0)}
                </p>
                <p className="text-sm text-gray-600">Ortalama Gider</p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Customer Details Modal */}
      {showCustomerModal && selectedSaleCustomer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-bold text-gray-900">
                    Satış Müşteri Bilgileri
                  </h2>
                  <p className="text-gray-600 mt-1">
                    {selectedSaleCustomer.orderNumber} - {selectedSaleCustomer.productName}
                  </p>
                </div>
                <button
                  onClick={() => setShowCustomerModal(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                  ×
                </button>
              </div>

              {/* Customer Information */}
              <div className="space-y-6">

                {/* Company Info */}
                <div className="bg-blue-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-3">Firma Bilgileri</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Firma Adı</p>
                      <p className="font-medium">{selectedSaleCustomer.companyName}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Vergi Numarası</p>
                      <p className="font-medium">{selectedSaleCustomer.taxNumber}</p>
                    </div>
                    <div className="md:col-span-2">
                      <p className="text-sm text-gray-600">Vergi Dairesi</p>
                      <p className="font-medium">{selectedSaleCustomer.taxOffice}</p>
                    </div>
                  </div>
                </div>

                {/* Contact Info */}
                <div className="bg-green-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-3">İletişim Bilgileri</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Yetkili Kişi</p>
                      <p className="font-medium">{selectedSaleCustomer.contactPerson}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Telefon</p>
                      <p className="font-medium">
                        <a href={`tel:${selectedSaleCustomer.phone}`} className="text-blue-600 hover:underline">
                          {selectedSaleCustomer.phone}
                        </a>
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Email</p>
                      <p className="font-medium">
                        <a href={`mailto:${selectedSaleCustomer.email}`} className="text-blue-600 hover:underline">
                          {selectedSaleCustomer.email}
                        </a>
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Adres</p>
                      <p className="font-medium">{selectedSaleCustomer.address}</p>
                    </div>
                  </div>
                </div>

                {/* Sale Info */}
                <div className="bg-yellow-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-3">Satış Bilgileri</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Sipariş No</p>
                      <p className="font-medium">{selectedSaleCustomer.orderNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Satış Tutarı</p>
                      <p className="font-medium text-green-600">
                        ${selectedSaleCustomer.salePrice.toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Satış Tarihi</p>
                      <p className="font-medium">
                        {new Date(selectedSaleCustomer.saleDate).toLocaleDateString('tr-TR')}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Notes */}
                {selectedSaleCustomer.notes && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-900 mb-3">Notlar</h3>
                    <p className="text-gray-700">{selectedSaleCustomer.notes}</p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <button
                    onClick={() => window.open(`tel:${selectedSaleCustomer.phone}`)}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    📞 Ara
                  </button>
                  <button
                    onClick={() => window.open(`mailto:${selectedSaleCustomer.email}`)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    ✉️ Email Gönder
                  </button>
                  <button
                    onClick={() => setShowCustomerModal(false)}
                    className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    Kapat
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
