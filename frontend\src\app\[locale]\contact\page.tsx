"use client"

import { Navigation } from "@/components/ui/navigation";
import { Button } from "@/components/ui/button";
import { useSimpleTranslation } from "@/hooks/useSimpleTranslation";

export default function ContactPage() {
  const { t } = useSimpleTranslation();

  // Create navigation links with translations
  const navigationLinks = [
    { name: t('nav.home'), href: "/" },
    { name: t('nav.products'), href: "/products" },
    { name: t('nav.3d_showroom'), href: "/3d-showroom" },
    { name: t('nav.news'), href: "/news" },
    { name: t('nav.about'), href: "/about" },
    { name: t('nav.contact'), href: "/contact", active: true }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <Navigation
        brand={{
          name: "Türk<PERSON>ye Doğal Taş Pazarı",
          href: "/"
        }}
        links={navigationLinks}
      />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-stone-50 to-stone-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              {t('contact.hero.title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              {t('contact.hero.subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-white p-8 rounded-lg shadow-lg">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {t('contact.form.title')}
              </h2>
              <form className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.name')}
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-stone-500 focus:border-transparent"
                    placeholder={t('contact.form.name_placeholder')}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.email')}
                  </label>
                  <input
                    type="email"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-stone-500 focus:border-transparent"
                    placeholder={t('contact.form.email_placeholder')}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.subject')}
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-stone-500 focus:border-transparent"
                    placeholder={t('contact.form.subject_placeholder')}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.message')}
                  </label>
                  <textarea
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-stone-500 focus:border-transparent"
                    placeholder={t('contact.form.message_placeholder')}
                  />
                </div>
                <Button type="submit" size="lg" className="w-full bg-stone-600 hover:bg-stone-700">
                  {t('contact.form.submit')}
                </Button>
              </form>
            </div>

            {/* Contact Info */}
            <div className="space-y-8">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  {t('contact.info.title')}
                </h2>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="text-2xl">📍</div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {t('contact.info.address.title')}
                      </h3>
                      <p className="text-gray-600">
                        {t('contact.info.address.value')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="text-2xl">📞</div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {t('contact.info.phone.title')}
                      </h3>
                      <p className="text-gray-600">
                        {t('contact.info.phone.value')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="text-2xl">✉️</div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {t('contact.info.email.title')}
                      </h3>
                      <p className="text-gray-600">
                        {t('contact.info.email.value')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="text-2xl">🕒</div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {t('contact.info.hours.title')}
                      </h3>
                      <p className="text-gray-600">
                        {t('contact.info.hours.value')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Social Media */}
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  {t('contact.social.title')}
                </h3>
                <div className="flex space-x-4">
                  <Button variant="outline" size="sm">
                    LinkedIn
                  </Button>
                  <Button variant="outline" size="sm">
                    Twitter
                  </Button>
                  <Button variant="outline" size="sm">
                    Instagram
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
