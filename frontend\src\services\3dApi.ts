/**
 * 3D API Service
 * Handles API calls for 3D viewer functionality
 */

import { 
  ViewerConfiguration, 
  Asset3D, 
  MaterialDefinition, 
  ViewerSession,
  ViewerCapabilities,
  ViewerAnalytics,
  ViewerConfigResponse,
  ViewerSessionResponse,
  AssetUploadResponse
} from '../types/3d';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

class ThreeDApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/api/3d`;
  }

  /**
   * Make HTTP request with error handling
   */
  private async makeRequest<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('3D API request failed:', error);
      throw error;
    }
  }

  /**
   * Get viewer configuration for product
   */
  async getViewerConfiguration(productId: string): Promise<ViewerConfigResponse> {
    return this.makeRequest<ViewerConfigResponse>(`/viewer/config/${productId}`);
  }

  /**
   * Update viewer configuration
   */
  async updateViewerConfiguration(
    productId: string, 
    config: Partial<ViewerConfiguration>
  ): Promise<ViewerConfiguration> {
    return this.makeRequest<ViewerConfiguration>(`/viewer/config/${productId}`, {
      method: 'PUT',
      body: JSON.stringify(config),
    });
  }

  /**
   * Start viewer session
   */
  async startViewerSession(
    productId: string,
    deviceInfo?: {
      userAgent?: string;
      deviceType?: 'mobile' | 'tablet' | 'desktop';
      screenResolution?: string;
    }
  ): Promise<ViewerSessionResponse> {
    return this.makeRequest<ViewerSessionResponse>('/viewer/session/start', {
      method: 'POST',
      body: JSON.stringify({ productId, deviceInfo }),
    });
  }

  /**
   * Update viewer session
   */
  async updateViewerSession(
    sessionId: string,
    updates: {
      viewDuration?: number;
      interactionCount?: number;
      zoomCount?: number;
      rotationCount?: number;
      annotationViews?: number;
      loadTime?: number;
      frameRate?: number;
      memoryUsage?: number;
    }
  ): Promise<void> {
    await this.makeRequest(`/viewer/session/${sessionId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  /**
   * End viewer session
   */
  async endViewerSession(sessionId: string): Promise<void> {
    await this.makeRequest(`/viewer/session/${sessionId}/end`, {
      method: 'POST',
    });
  }

  /**
   * Get assets for product
   */
  async getProductAssets(productId: string): Promise<Asset3D[]> {
    return this.makeRequest<Asset3D[]>(`/assets/product/${productId}`);
  }

  /**
   * Get asset by ID
   */
  async getAsset(assetId: string): Promise<Asset3D> {
    return this.makeRequest<Asset3D>(`/assets/${assetId}`);
  }

  /**
   * Upload asset
   */
  async uploadAsset(
    file: File,
    productId: string,
    metadata: {
      name: string;
      description?: string;
      tags?: string[];
    }
  ): Promise<AssetUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('productId', productId);
    formData.append('name', metadata.name);
    if (metadata.description) {
      formData.append('description', metadata.description);
    }
    if (metadata.tags) {
      formData.append('tags', JSON.stringify(metadata.tags));
    }

    const response = await fetch(`${this.baseUrl}/assets/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Upload failed');
    }

    return response.json();
  }

  /**
   * Delete asset
   */
  async deleteAsset(assetId: string): Promise<void> {
    await this.makeRequest(`/assets/${assetId}`, {
      method: 'DELETE',
    });
  }

  /**
   * Get materials for product
   */
  async getProductMaterials(productId: string): Promise<MaterialDefinition[]> {
    return this.makeRequest<MaterialDefinition[]>(`/materials/product/${productId}`);
  }

  /**
   * Create material
   */
  async createMaterial(material: Omit<MaterialDefinition, 'id' | 'createdAt' | 'updatedAt'>): Promise<MaterialDefinition> {
    return this.makeRequest<MaterialDefinition>('/materials', {
      method: 'POST',
      body: JSON.stringify(material),
    });
  }

  /**
   * Update material
   */
  async updateMaterial(
    materialId: string, 
    updates: Partial<MaterialDefinition>
  ): Promise<MaterialDefinition> {
    return this.makeRequest<MaterialDefinition>(`/materials/${materialId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  /**
   * Delete material
   */
  async deleteMaterial(materialId: string): Promise<void> {
    await this.makeRequest(`/materials/${materialId}`, {
      method: 'DELETE',
    });
  }

  /**
   * Get viewer analytics
   */
  async getViewerAnalytics(
    productId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<ViewerAnalytics> {
    const params = new URLSearchParams();
    if (productId) params.append('productId', productId);
    if (startDate) params.append('startDate', startDate.toISOString());
    if (endDate) params.append('endDate', endDate.toISOString());

    const query = params.toString();
    return this.makeRequest<ViewerAnalytics>(`/analytics${query ? `?${query}` : ''}`);
  }

  /**
   * Detect viewer capabilities
   */
  detectViewerCapabilities(): ViewerCapabilities {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    const gl2 = canvas.getContext('webgl2');
    
    let maxTextureSize = 2048;
    let maxVertexAttributes = 8;
    
    if (gl) {
      maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
      maxVertexAttributes = gl.getParameter(gl.MAX_VERTEX_ATTRIBS);
    }

    // Clean up
    canvas.remove();

    return {
      webgl: !!gl,
      webgl2: !!gl2,
      webgpu: 'gpu' in navigator,
      maxTextureSize,
      maxVertexAttributes,
      supportedFormats: ['GLB', 'GLTF', 'JPG', 'PNG', 'WEBP'] as any[],
      deviceMemory: (navigator as any).deviceMemory,
      hardwareConcurrency: navigator.hardwareConcurrency
    };
  }

  /**
   * Get asset URL for download/streaming
   */
  getAssetUrl(assetId: string, quality?: string): string {
    const params = quality ? `?quality=${quality}` : '';
    return `${this.baseUrl}/assets/${assetId}/download${params}`;
  }

  /**
   * Get texture URL
   */
  getTextureUrl(textureId: string, quality?: string): string {
    const params = quality ? `?quality=${quality}` : '';
    return `${this.baseUrl}/textures/${textureId}/download${params}`;
  }

  /**
   * Preload asset
   */
  async preloadAsset(assetId: string, quality?: string): Promise<void> {
    const url = this.getAssetUrl(assetId, quality);
    
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.responseType = 'blob';
      
      xhr.onload = () => {
        if (xhr.status === 200) {
          resolve();
        } else {
          reject(new Error(`Failed to preload asset: ${xhr.status}`));
        }
      };
      
      xhr.onerror = () => reject(new Error('Network error during preload'));
      xhr.send();
    });
  }

  /**
   * Get device type based on user agent
   */
  getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (/mobile|android|iphone/.test(userAgent)) {
      return 'mobile';
    } else if (/tablet|ipad/.test(userAgent)) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  /**
   * Get screen resolution
   */
  getScreenResolution(): string {
    return `${screen.width}x${screen.height}`;
  }

  /**
   * Check if WebGL is supported
   */
  isWebGLSupported(): boolean {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      canvas.remove();
      return !!gl;
    } catch (e) {
      return false;
    }
  }

  /**
   * Check if WebGL2 is supported
   */
  isWebGL2Supported(): boolean {
    try {
      const canvas = document.createElement('canvas');
      const gl2 = canvas.getContext('webgl2');
      canvas.remove();
      return !!gl2;
    } catch (e) {
      return false;
    }
  }

  /**
   * Get optimal quality based on device capabilities
   */
  getOptimalQuality(): string {
    const capabilities = this.detectViewerCapabilities();
    const deviceType = this.getDeviceType();
    
    if (deviceType === 'mobile') {
      return 'LOW';
    } else if (deviceType === 'tablet') {
      return 'MEDIUM';
    } else if (capabilities.webgl2 && capabilities.maxTextureSize >= 4096) {
      return 'HIGH';
    } else {
      return 'MEDIUM';
    }
  }

  /**
   * Estimate memory usage for asset
   */
  estimateMemoryUsage(asset: Asset3D): number {
    // Rough estimation in MB
    let memoryMB = 0;
    
    if (asset.vertices) {
      // Vertices, normals, UVs = roughly 32 bytes per vertex
      memoryMB += (asset.vertices * 32) / (1024 * 1024);
    }
    
    if (asset.width && asset.height) {
      // Texture memory (assuming RGBA)
      memoryMB += (asset.width * asset.height * 4) / (1024 * 1024);
    }
    
    return Math.ceil(memoryMB);
  }

  /**
   * Check if asset can be loaded on current device
   */
  canLoadAsset(asset: Asset3D): boolean {
    const capabilities = this.detectViewerCapabilities();
    const estimatedMemory = this.estimateMemoryUsage(asset);
    
    // Check texture size limits
    if (asset.width && asset.width > capabilities.maxTextureSize) {
      return false;
    }
    
    // Check memory constraints (rough estimate)
    if (capabilities.deviceMemory && estimatedMemory > capabilities.deviceMemory * 100) {
      return false;
    }
    
    return true;
  }
}

// Export singleton instance
export const threeDApi = new ThreeDApiService();
export default threeDApi;
