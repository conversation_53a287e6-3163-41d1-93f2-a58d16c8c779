#!/bin/bash

# AI Marketing System Production Deployment Script
# This script handles the complete deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DEPLOYMENT_ENV=${1:-production}
BACKUP_BEFORE_DEPLOY=${BACKUP_BEFORE_DEPLOY:-true}
RUN_TESTS=${RUN_TESTS:-true}
HEALTH_CHECK_TIMEOUT=${HEALTH_CHECK_TIMEOUT:-300}

echo -e "${BLUE}🚀 Starting AI Marketing System Deployment${NC}"
echo -e "${BLUE}Environment: ${DEPLOYMENT_ENV}${NC}"
echo -e "${BLUE}Timestamp: $(date)${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to wait for service health
wait_for_service() {
    local service_name=$1
    local health_url=$2
    local timeout=$3
    local counter=0
    
    print_info "Waiting for $service_name to be healthy..."
    
    while [ $counter -lt $timeout ]; do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            print_status "$service_name is healthy"
            return 0
        fi
        
        sleep 5
        counter=$((counter + 5))
        echo -n "."
    done
    
    print_error "$service_name failed to become healthy within $timeout seconds"
    return 1
}

# Pre-deployment checks
print_info "Running pre-deployment checks..."

# Check required commands
required_commands=("docker" "docker-compose" "curl" "jq")
for cmd in "${required_commands[@]}"; do
    if ! command_exists "$cmd"; then
        print_error "Required command '$cmd' is not installed"
        exit 1
    fi
done

print_status "All required commands are available"

# Check environment file
if [ ! -f ".env.${DEPLOYMENT_ENV}" ]; then
    print_error "Environment file .env.${DEPLOYMENT_ENV} not found"
    print_info "Please copy .env.production.example to .env.${DEPLOYMENT_ENV} and configure it"
    exit 1
fi

print_status "Environment file found"

# Load environment variables
set -a
source ".env.${DEPLOYMENT_ENV}"
set +a

# Validate critical environment variables
critical_vars=("DB_PASSWORD" "JWT_SECRET" "OPENAI_API_KEY")
for var in "${critical_vars[@]}"; do
    if [ -z "${!var}" ]; then
        print_error "Critical environment variable $var is not set"
        exit 1
    fi
done

print_status "Critical environment variables are set"

# Run tests if enabled
if [ "$RUN_TESTS" = "true" ]; then
    print_info "Running tests..."
    
    cd backend
    npm test
    cd ..
    
    print_status "All tests passed"
fi

# Create backup if enabled
if [ "$BACKUP_BEFORE_DEPLOY" = "true" ]; then
    print_info "Creating backup before deployment..."
    
    # Create backup directory
    backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup database if running
    if docker-compose -f docker-compose.production.yml ps postgres | grep -q "Up"; then
        print_info "Creating database backup..."
        docker-compose -f docker-compose.production.yml exec -T postgres pg_dump -U "$DB_USER" "$DB_NAME" > "$backup_dir/database.sql"
        print_status "Database backup created"
    fi
    
    # Backup application data
    if [ -d "data" ]; then
        print_info "Creating application data backup..."
        tar -czf "$backup_dir/app_data.tar.gz" data/
        print_status "Application data backup created"
    fi
    
    print_status "Backup completed: $backup_dir"
fi

# Build and deploy
print_info "Building and deploying services..."

# Pull latest images
print_info "Pulling latest base images..."
docker-compose -f docker-compose.production.yml pull

# Build custom images
print_info "Building application images..."
docker-compose -f docker-compose.production.yml build --no-cache

# Stop existing services gracefully
print_info "Stopping existing services..."
docker-compose -f docker-compose.production.yml down --timeout 30

# Start database and cache services first
print_info "Starting database and cache services..."
docker-compose -f docker-compose.production.yml up -d postgres redis

# Wait for database to be ready
wait_for_service "PostgreSQL" "http://localhost:5432" 60

# Run database migrations
print_info "Running database migrations..."
docker-compose -f docker-compose.production.yml exec -T postgres psql -U "$DB_USER" -d "$DB_NAME" -f /docker-entrypoint-initdb.d/01-ai-marketing-schema.sql || true

# Start application services
print_info "Starting application services..."
docker-compose -f docker-compose.production.yml up -d ai-marketing-backend frontend

# Wait for backend to be ready
wait_for_service "AI Marketing Backend" "http://localhost:3001/health" $HEALTH_CHECK_TIMEOUT

# Wait for frontend to be ready
wait_for_service "Frontend" "http://localhost:3000/api/health" $HEALTH_CHECK_TIMEOUT

# Start reverse proxy
print_info "Starting reverse proxy..."
docker-compose -f docker-compose.production.yml up -d nginx

# Start monitoring services
print_info "Starting monitoring services..."
docker-compose -f docker-compose.production.yml up -d prometheus grafana loki promtail

# Final health checks
print_info "Running final health checks..."

# Check all services are running
services=("postgres" "redis" "ai-marketing-backend" "frontend" "nginx")
for service in "${services[@]}"; do
    if ! docker-compose -f docker-compose.production.yml ps "$service" | grep -q "Up"; then
        print_error "Service $service is not running"
        exit 1
    fi
done

print_status "All services are running"

# Check API endpoints
api_endpoints=(
    "http://localhost:3001/health:Backend Health"
    "http://localhost:3000/api/health:Frontend Health"
    "http://localhost:3001/api/admin/ai-marketing/system/health:AI Marketing Health"
)

for endpoint_info in "${api_endpoints[@]}"; do
    IFS=':' read -r url description <<< "$endpoint_info"
    if curl -f -s "$url" > /dev/null; then
        print_status "$description check passed"
    else
        print_warning "$description check failed - this may be normal during startup"
    fi
done

# Display deployment summary
print_info "Deployment Summary:"
echo "===================="
echo "Environment: $DEPLOYMENT_ENV"
echo "Timestamp: $(date)"
echo "Services Status:"

docker-compose -f docker-compose.production.yml ps

echo ""
echo "Service URLs:"
echo "- Frontend: http://localhost:3000"
echo "- Backend API: http://localhost:3001"
echo "- AI Marketing Dashboard: http://localhost:3000/admin/ai-marketing"
echo "- Grafana Monitoring: http://localhost:3003"
echo "- Prometheus Metrics: http://localhost:9090"

print_status "🎉 Deployment completed successfully!"

# Post-deployment tasks
print_info "Running post-deployment tasks..."

# Warm up the AI Marketing system
print_info "Warming up AI Marketing system..."
curl -s "http://localhost:3001/api/admin/ai-marketing/stats" > /dev/null || print_warning "AI Marketing warmup failed"

# Send deployment notification (if configured)
if [ -n "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"🚀 AI Marketing System deployed successfully to $DEPLOYMENT_ENV environment\"}" \
        "$SLACK_WEBHOOK_URL" || print_warning "Failed to send Slack notification"
fi

# Log deployment
echo "$(date): Deployment to $DEPLOYMENT_ENV completed successfully" >> deployments.log

print_status "Post-deployment tasks completed"

# Display next steps
echo ""
print_info "Next Steps:"
echo "1. Monitor the application logs: docker-compose -f docker-compose.production.yml logs -f"
echo "2. Check Grafana dashboards: http://localhost:3003"
echo "3. Verify AI Marketing functionality: http://localhost:3000/admin/ai-marketing"
echo "4. Set up SSL certificates if not already configured"
echo "5. Configure domain DNS if deploying to production domain"

print_status "Deployment script completed!"

exit 0
