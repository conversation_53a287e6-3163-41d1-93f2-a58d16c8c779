/**
 * Database Conversation Manager
 * Manages conversation state and context with database persistence
 */

import { v4 as uuidv4 } from 'uuid';
import { PrismaClient } from '@prisma/client';
import { 
  ConversationContext, 
  ChatMessage, 
  ChatbotIntent, 
  ExtractedEntity,
  UserProfile 
} from '../types';

export class DatabaseConversationManager {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Create new conversation session
   */
  async createSession(userId?: string, language: string = 'en'): Promise<string> {
    const sessionId = uuidv4();
    
    try {
      await this.prisma.chatbotConversation.create({
        data: {
          sessionId,
          userId,
          language,
          status: 'ACTIVE',
          currentIntent: 'GREETING',
          messageCount: 0,
          escalationLevel: 0,
          resolvedIssues: [],
          unresolvedIssues: []
        }
      });

      return sessionId;
    } catch (error) {
      console.error('Error creating conversation session:', error);
      throw new Error('Failed to create conversation session');
    }
  }

  /**
   * Get conversation context
   */
  async getContext(sessionId: string): Promise<ConversationContext | null> {
    try {
      const conversation = await this.prisma.chatbotConversation.findUnique({
        where: { sessionId },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
            take: 50 // Limit to last 50 messages
          },
          user: true
        }
      });

      if (!conversation) return null;

      // Convert database records to ConversationContext
      const conversationHistory: ChatMessage[] = conversation.messages.map(msg => ({
        id: msg.id,
        role: msg.role.toLowerCase() as 'user' | 'assistant' | 'system',
        content: msg.content,
        timestamp: msg.createdAt,
        language: msg.language || conversation.language,
        intent: msg.intent as ChatbotIntent,
        confidence: msg.confidence ? parseFloat(msg.confidence.toString()) : undefined,
        entities: (msg.entities as unknown as ExtractedEntity[]) || []
      }));

      const userProfile: UserProfile | undefined = conversation.user ? {
        type: conversation.userType as 'producer' | 'customer' || 'customer',
        preferences: {
          language: conversation.language,
          communicationStyle: 'formal',
          topics: []
        },
        previousInteractions: []
      } : undefined;

      const context: ConversationContext = {
        sessionId: conversation.sessionId,
        userId: conversation.userId || undefined,
        language: conversation.language,
        currentIntent: conversation.currentIntent as ChatbotIntent || ChatbotIntent.UNKNOWN,
        entities: [], // Would need to aggregate from messages
        conversationHistory,
        userProfile,
        escalationLevel: conversation.escalationLevel,
        escalationReason: conversation.escalationReason || undefined,
        humanAgentId: conversation.humanAgentId || undefined,
        resolvedIssues: conversation.resolvedIssues,
        unresolvedIssues: conversation.unresolvedIssues,
        confidence: conversation.averageConfidence ? parseFloat(conversation.averageConfidence.toString()) : 1.0,
        sentiment: conversation.sentimentScore ? parseFloat(conversation.sentimentScore.toString()) : 0.0
      };

      return context;
    } catch (error) {
      console.error('Error getting conversation context:', error);
      return null;
    }
  }

  /**
   * Save conversation context
   */
  async saveContext(sessionId: string, context: ConversationContext): Promise<void> {
    try {
      await this.prisma.chatbotConversation.update({
        where: { sessionId },
        data: {
          currentIntent: context.currentIntent as any,
          escalationLevel: context.escalationLevel,
          escalationReason: context.escalationReason,
          humanAgentId: context.humanAgentId,
          resolvedIssues: context.resolvedIssues,
          unresolvedIssues: context.unresolvedIssues,
          averageConfidence: context.confidence,
          sentimentScore: context.sentiment,
          lastActivityAt: new Date(),
          messageCount: context.conversationHistory.length
        }
      });
    } catch (error) {
      console.error('Error saving conversation context:', error);
      throw error;
    }
  }

  /**
   * Add message to conversation
   */
  async addMessage(
    sessionId: string, 
    role: 'user' | 'assistant' | 'system',
    content: string,
    intent?: ChatbotIntent,
    confidence?: number,
    entities?: ExtractedEntity[],
    userId?: string
  ): Promise<void> {
    try {
      // Add message to database
      await this.prisma.chatbotMessage.create({
        data: {
          conversationId: sessionId,
          role: role.toUpperCase() as 'USER' | 'ASSISTANT' | 'SYSTEM',
          content,
          intent: intent as any || undefined,
          confidence: confidence || undefined,
          entities: entities as any || undefined,
          userId: userId || undefined
        }
      });

      // Update conversation metadata
      await this.prisma.chatbotConversation.update({
        where: { sessionId },
        data: {
          lastActivityAt: new Date(),
          messageCount: {
            increment: 1
          },
          currentIntent: intent as any || undefined
        }
      });
    } catch (error) {
      console.error('Error adding message:', error);
      throw error;
    }
  }

  /**
   * Update conversation context
   */
  async updateContext(
    sessionId: string,
    updates: Partial<ConversationContext>
  ): Promise<void> {
    try {
      const updateData: any = {};

      if (updates.currentIntent !== undefined) {
        updateData.currentIntent = updates.currentIntent;
      }
      if (updates.escalationLevel !== undefined) {
        updateData.escalationLevel = updates.escalationLevel;
      }
      if (updates.escalationReason !== undefined) {
        updateData.escalationReason = updates.escalationReason;
      }
      if (updates.humanAgentId !== undefined) {
        updateData.humanAgentId = updates.humanAgentId;
      }
      if (updates.resolvedIssues !== undefined) {
        updateData.resolvedIssues = updates.resolvedIssues;
      }
      if (updates.unresolvedIssues !== undefined) {
        updateData.unresolvedIssues = updates.unresolvedIssues;
      }
      if (updates.confidence !== undefined) {
        updateData.averageConfidence = updates.confidence;
      }
      if (updates.sentiment !== undefined) {
        updateData.sentimentScore = updates.sentiment;
      }
      if (updates.language !== undefined) {
        updateData.language = updates.language;
      }

      updateData.lastActivityAt = new Date();

      await this.prisma.chatbotConversation.update({
        where: { sessionId },
        data: updateData
      });
    } catch (error) {
      console.error('Error updating conversation context:', error);
      throw error;
    }
  }

  /**
   * Get conversation history
   */
  async getHistory(sessionId: string, limit: number = 20): Promise<ChatMessage[]> {
    try {
      const messages = await this.prisma.chatbotMessage.findMany({
        where: {
          conversation: { sessionId }
        },
        orderBy: { createdAt: 'desc' },
        take: limit
      });

      return messages.reverse().map(msg => ({
        id: msg.id,
        role: msg.role.toLowerCase() as 'user' | 'assistant' | 'system',
        content: msg.content,
        timestamp: msg.createdAt,
        language: msg.language || undefined,
        intent: msg.intent as ChatbotIntent || undefined,
        confidence: msg.confidence ? parseFloat(msg.confidence.toString()) : undefined,
        entities: (msg.entities as unknown as ExtractedEntity[]) || []
      }));
    } catch (error) {
      console.error('Error getting conversation history:', error);
      return [];
    }
  }

  /**
   * Delete conversation
   */
  async deleteConversation(sessionId: string): Promise<void> {
    try {
      await this.prisma.chatbotConversation.update({
        where: { sessionId },
        data: {
          status: 'CLOSED',
          endedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error deleting conversation:', error);
      throw error;
    }
  }

  /**
   * Get active conversations for a user
   */
  async getUserConversations(userId: string): Promise<string[]> {
    try {
      const conversations = await this.prisma.chatbotConversation.findMany({
        where: {
          userId,
          status: 'ACTIVE'
        },
        select: {
          sessionId: true
        }
      });

      return conversations.map(conv => conv.sessionId);
    } catch (error) {
      console.error('Error getting user conversations:', error);
      return [];
    }
  }

  /**
   * Get conversation statistics
   */
  async getConversationStats(sessionId: string): Promise<{
    messageCount: number;
    duration: number;
    intents: Record<string, number>;
    averageConfidence: number;
    escalationLevel: number;
  }> {
    try {
      const conversation = await this.prisma.chatbotConversation.findUnique({
        where: { sessionId },
        include: {
          messages: {
            select: {
              intent: true,
              confidence: true,
              createdAt: true
            }
          }
        }
      });

      if (!conversation) {
        throw new Error('Conversation not found');
      }

      const messageCount = conversation.messageCount;
      const duration = conversation.endedAt 
        ? conversation.endedAt.getTime() - conversation.startedAt.getTime()
        : Date.now() - conversation.startedAt.getTime();

      const intents: Record<string, number> = {};
      let totalConfidence = 0;
      let confidenceCount = 0;

      conversation.messages.forEach(msg => {
        if (msg.intent) {
          intents[msg.intent] = (intents[msg.intent] || 0) + 1;
        }
        if (msg.confidence) {
          totalConfidence += parseFloat(msg.confidence.toString());
          confidenceCount++;
        }
      });

      const averageConfidence = confidenceCount > 0 ? totalConfidence / confidenceCount : 0;

      return {
        messageCount,
        duration,
        intents,
        averageConfidence,
        escalationLevel: conversation.escalationLevel
      };
    } catch (error) {
      console.error('Error getting conversation stats:', error);
      throw error;
    }
  }

  /**
   * Mark issue as resolved
   */
  async markIssueResolved(sessionId: string, issue: string): Promise<void> {
    try {
      const conversation = await this.prisma.chatbotConversation.findUnique({
        where: { sessionId }
      });

      if (!conversation) return;

      const resolvedIssues = [...conversation.resolvedIssues];
      const unresolvedIssues = conversation.unresolvedIssues.filter(i => i !== issue);

      if (!resolvedIssues.includes(issue)) {
        resolvedIssues.push(issue);
      }

      await this.prisma.chatbotConversation.update({
        where: { sessionId },
        data: {
          resolvedIssues,
          unresolvedIssues
        }
      });
    } catch (error) {
      console.error('Error marking issue as resolved:', error);
    }
  }

  /**
   * Mark issue as unresolved
   */
  async markIssueUnresolved(sessionId: string, issue: string): Promise<void> {
    try {
      const conversation = await this.prisma.chatbotConversation.findUnique({
        where: { sessionId }
      });

      if (!conversation) return;

      const unresolvedIssues = [...conversation.unresolvedIssues];

      if (!unresolvedIssues.includes(issue)) {
        unresolvedIssues.push(issue);
      }

      await this.prisma.chatbotConversation.update({
        where: { sessionId },
        data: {
          unresolvedIssues
        }
      });
    } catch (error) {
      console.error('Error marking issue as unresolved:', error);
    }
  }

  /**
   * Set user profile
   */
  async setUserProfile(sessionId: string, profile: UserProfile): Promise<void> {
    try {
      await this.prisma.chatbotConversation.update({
        where: { sessionId },
        data: {
          userType: profile.type
        }
      });
    } catch (error) {
      console.error('Error setting user profile:', error);
    }
  }

  /**
   * Update sentiment score
   */
  async updateSentiment(sessionId: string, sentiment: number): Promise<void> {
    await this.updateContext(sessionId, { sentiment });
  }

  /**
   * Update confidence score
   */
  async updateConfidence(sessionId: string, confidence: number): Promise<void> {
    await this.updateContext(sessionId, { confidence });
  }

  /**
   * Get all active conversations (for admin)
   */
  async getAllActiveConversations(): Promise<Array<{
    sessionId: string;
    userId?: string;
    language: string;
    messageCount: number;
    lastActivity: Date;
    escalationLevel: number;
  }>> {
    try {
      const conversations = await this.prisma.chatbotConversation.findMany({
        where: {
          status: 'ACTIVE'
        },
        orderBy: {
          lastActivityAt: 'desc'
        }
      });

      return conversations.map(conv => ({
        sessionId: conv.sessionId,
        userId: conv.userId || undefined,
        language: conv.language,
        messageCount: conv.messageCount,
        lastActivity: conv.lastActivityAt,
        escalationLevel: conv.escalationLevel
      }));
    } catch (error) {
      console.error('Error getting all conversations:', error);
      return [];
    }
  }

  /**
   * Cleanup expired conversations
   */
  async cleanupExpiredConversations(): Promise<number> {
    try {
      const expirationTime = new Date();
      expirationTime.setHours(expirationTime.getHours() - 24); // 24 hours ago

      const result = await this.prisma.chatbotConversation.updateMany({
        where: {
          status: 'ACTIVE',
          lastActivityAt: {
            lt: expirationTime
          }
        },
        data: {
          status: 'EXPIRED',
          endedAt: new Date()
        }
      });

      return result.count;
    } catch (error) {
      console.error('Error cleaning up conversations:', error);
      return 0;
    }
  }

  /**
   * Submit feedback
   */
  async submitFeedback(
    sessionId: string,
    messageId: string,
    rating: number,
    feedback?: string,
    userId?: string
  ): Promise<void> {
    try {
      await this.prisma.chatbotFeedback.create({
        data: {
          conversationId: sessionId,
          messageId,
          userId,
          rating,
          feedback
        }
      });
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  }

  /**
   * Get conversation feedback
   */
  async getConversationFeedback(sessionId: string): Promise<any[]> {
    try {
      const feedback = await this.prisma.chatbotFeedback.findMany({
        where: {
          conversationId: sessionId
        },
        include: {
          message: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return feedback;
    } catch (error) {
      console.error('Error getting conversation feedback:', error);
      return [];
    }
  }

  /**
   * Cleanup - close database connection
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
