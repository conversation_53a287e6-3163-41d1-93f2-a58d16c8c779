import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { authMiddleware, AuthenticatedRequest } from '../../middleware/authMiddleware';
import { asyncHandler } from '../../middleware/errorHandler';
import { z } from 'zod';
import bcrypt from 'bcrypt';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const updateProfileSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  profile: z.object({
    phone: z.string().optional(),
    address: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    postalCode: z.string().optional(),
    country: z.string().optional(),
    website: z.string().url().optional(),
    companyName: z.string().optional(),
    taxNumber: z.string().optional(),
    businessType: z.string().optional()
  }).optional()
});

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string().min(1, 'Password confirmation is required')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

/**
 * @swagger
 * /api/users/profile:
 *   get:
 *     summary: Get current user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 */
router.get('/profile', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;
  
  const userProfile = await prisma.user.findUnique({
    where: { id: user.id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      userType: true,
      status: true,
      emailVerified: true,
      createdAt: true,
      updatedAt: true,
      profile: true,
      companyName: true,
      _count: {
        select: {
          products: true,
          customerOrders: true,
          producerQuotes: true,
          notifications: {
            where: { status: 'UNREAD' }
          }
        }
      }
    }
  });
  
  if (!userProfile) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'User not found',
        statusCode: 404
      }
    });
  }
  
  res.json({
    success: true,
    data: userProfile
  });
}));

/**
 * @swagger
 * /api/users/profile:
 *   put:
 *     summary: Update user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               profile:
 *                 type: object
 *     responses:
 *       200:
 *         description: Profile updated successfully
 */
router.put('/profile', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;
  const updateData = updateProfileSchema.parse(req.body);
  
  const updatedUser = await prisma.user.update({
    where: { id: user.id },
    data: {
      firstName: updateData.firstName,
      lastName: updateData.lastName,
      profile: updateData.profile ? {
        upsert: {
          create: {
            ...updateData.profile,
            countryCode: (updateData.profile as any).countryCode || 'TR',
            companyName: updateData.profile.companyName || ''
          } as any,
          update: updateData.profile
        }
      } : undefined
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      userType: true,
      status: true,
      profile: true,
      companyName: true
    }
  });
  
  res.json({
    success: true,
    data: updatedUser,
    message: 'Profile updated successfully'
  });
}));

/**
 * @swagger
 * /api/users/change-password:
 *   put:
 *     summary: Change user password
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *               confirmPassword:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password changed successfully
 */
router.put('/change-password', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;
  const { currentPassword, newPassword } = changePasswordSchema.parse(req.body);
  
  // Get current user with password
  const currentUser = await prisma.user.findUnique({
    where: { id: user.id },
    select: { id: true, password: true }
  });
  
  if (!currentUser) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'User not found',
        statusCode: 404
      }
    });
  }
  
  // Verify current password
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, currentUser.password || '');
  
  if (!isCurrentPasswordValid) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Current password is incorrect',
        statusCode: 400
      }
    });
  }
  
  // Hash new password
  const hashedNewPassword = await bcrypt.hash(newPassword, 12);
  
  // Update password
  await prisma.user.update({
    where: { id: user.id },
    data: { password: hashedNewPassword }
  });
  
  res.json({
    success: true,
    message: 'Password changed successfully'
  });
}));

/**
 * @swagger
 * /api/users/dashboard-stats:
 *   get:
 *     summary: Get user dashboard statistics
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard stats retrieved successfully
 */
router.get('/dashboard-stats', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;
  
  let stats: any = {};
  
  if (user.userType === 'customer') {
    // Customer dashboard stats
    const [
      totalOrders,
      pendingQuotes,
      activeOrders,
      completedOrders,
      totalSpent,
      recentActivity
    ] = await Promise.all([
      prisma.order.count({
        where: { customerId: user.id }
      }),
      prisma.quote.count({
        where: { customerId: user.id, status: 'PENDING' }
      }),
      prisma.order.count({
        where: { 
          customerId: user.id,
          status: { in: ['PENDING', 'CONFIRMED', 'PRODUCTION', 'SHIPPED'] }
        }
      }),
      prisma.order.count({
        where: { customerId: user.id, status: 'DELIVERED' }
      }),
      prisma.order.aggregate({
        where: { customerId: user.id, status: 'DELIVERED' },
        _sum: { totalAmount: true }
      }),
      prisma.order.findMany({
        where: { customerId: user.id },
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          quote: {
            include: {
              product: {
                select: { name: true }
              }
            }
          }
        }
      })
    ]);
    
    stats = {
      totalOrders,
      pendingQuotes,
      activeOrders,
      completedOrders,
      totalSpent: totalSpent._sum.totalAmount || 0,
      recentActivity
    };
    
  } else if (user.userType === 'producer') {
    // Producer dashboard stats
    const [
      totalProducts,
      pendingQuotes,
      activeOrders,
      totalRevenue,
      monthlyRevenue,
      recentOrders
    ] = await Promise.all([
      prisma.product.count({
        where: { producerId: user.id, isActive: true }
      }),
      prisma.quote.count({
        where: { producerId: user.id, status: 'PENDING' }
      }),
      prisma.order.count({
        where: { 
          quote: { producerId: user.id },
          status: { in: ['PENDING', 'CONFIRMED', 'PRODUCTION', 'SHIPPED'] }
        }
      }),
      prisma.order.aggregate({
        where: { 
          quote: { producerId: user.id },
          status: 'DELIVERED'
        },
        _sum: { totalAmount: true }
      }),
      prisma.order.aggregate({
        where: { 
          quote: { producerId: user.id },
          status: 'DELIVERED',
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        },
        _sum: { totalAmount: true }
      }),
      prisma.order.findMany({
        where: { quote: { producerId: user.id } },
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          customer: {
            select: { firstName: true, lastName: true }
          },
          quote: {
            include: {
              product: {
                select: { name: true }
              }
            }
          }
        }
      })
    ]);
    
    stats = {
      totalProducts,
      pendingQuotes,
      activeOrders,
      totalRevenue: totalRevenue._sum.totalAmount || 0,
      monthlyRevenue: monthlyRevenue._sum.totalAmount || 0,
      recentOrders
    };
  }
  
  res.json({
    success: true,
    data: stats
  });
}));

/**
 * @swagger
 * /api/users/notifications:
 *   get:
 *     summary: Get user notifications
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [READ, UNREAD]
 *     responses:
 *       200:
 *         description: Notifications retrieved successfully
 */
router.get('/notifications', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const status = req.query.status as string;
  
  const skip = (page - 1) * limit;
  const where: any = { userId: user.id };
  
  if (status) {
    where.status = status;
  }
  
  const [notifications, total, unreadCount] = await Promise.all([
    prisma.notification.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' }
    }),
    prisma.notification.count({ where }),
    prisma.notification.count({
      where: { userId: user.id, status: 'UNREAD' }
    })
  ]);
  
  const totalPages = Math.ceil(total / limit);
  
  res.json({
    success: true,
    data: {
      notifications,
      unreadCount,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @swagger
 * /api/users/notifications/{id}/read:
 *   put:
 *     summary: Mark notification as read
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification marked as read
 */
router.put('/notifications/:id/read', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const user = req.user!;
  
  const notification = await prisma.notification.findFirst({
    where: { id, userId: user.id }
  });
  
  if (!notification) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Notification not found',
        statusCode: 404
      }
    });
  }
  
  await prisma.notification.update({
    where: { id },
    data: { 
      status: 'READ',
      readAt: new Date()
    }
  });
  
  res.json({
    success: true,
    message: 'Notification marked as read'
  });
}));

/**
 * @swagger
 * /api/users/notifications/mark-all-read:
 *   put:
 *     summary: Mark all notifications as read
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: All notifications marked as read
 */
router.put('/notifications/mark-all-read', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;
  
  await prisma.notification.updateMany({
    where: { 
      userId: user.id,
      status: 'UNREAD'
    },
    data: { 
      status: 'READ',
      readAt: new Date()
    }
  });
  
  res.json({
    success: true,
    message: 'All notifications marked as read'
  });
}));

export default router;
