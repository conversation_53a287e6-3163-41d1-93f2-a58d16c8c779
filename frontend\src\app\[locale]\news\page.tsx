"use client"

import { Navigation } from "@/components/ui/navigation";
import { Button } from "@/components/ui/button";
import { useSimpleTranslation } from "@/hooks/useSimpleTranslation";

const newsArticles = [
  {
    id: 1,
    title: "Türkiye Doğal Taş İhracatında Rekor Kırdı",
    excerpt: "2024 yılının ilk yarısında doğal taş ihracatı %15 artış gösterdi...",
    date: "2024-07-10",
    category: "Sektör Haberleri",
    image: "/api/placeholder/400/250"
  },
  {
    id: 2,
    title: "Yeni 3D Teknolojisi ile Doğal Taş Seçimi",
    excerpt: "Platformumuzda sunduğumuz 3D görüntüleme teknolojisi ile müşteriler...",
    date: "2024-07-08",
    category: "Teknoloji",
    image: "/api/placeholder/400/250"
  },
  {
    id: 3,
    title: "Sürdürülebilir Madencilik Uygulamaları",
    excerpt: "Çevre dostu üretim yöntemleri ile doğal taş sektöründe sürdürülebilirlik...",
    date: "2024-07-05",
    category: "Sürdürülebilirlik",
    image: "/api/placeholder/400/250"
  }
];

export default function NewsPage() {
  const { t } = useSimpleTranslation();

  // Create navigation links with translations
  const navigationLinks = [
    { name: t('nav.home'), href: "/" },
    { name: t('nav.products'), href: "/products" },
    { name: t('nav.3d_showroom'), href: "/3d-showroom" },
    { name: t('nav.news'), href: "/news", active: true },
    { name: t('nav.about'), href: "/about" },
    { name: t('nav.contact'), href: "/contact" }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <Navigation
        brand={{
          name: "Türkiye Doğal Taş Pazarı",
          href: "/"
        }}
        links={navigationLinks}
      />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-stone-50 to-stone-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              {t('news.hero.title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              {t('news.hero.subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* News Articles */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-3 gap-8">
            {newsArticles.map((article) => (
              <article key={article.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <img
                  src={article.image}
                  alt={article.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm text-stone-600 font-medium">
                      {article.category}
                    </span>
                    <span className="text-sm text-gray-500">
                      {new Date(article.date).toLocaleDateString('tr-TR')}
                    </span>
                  </div>
                  <h2 className="text-xl font-bold text-gray-900 mb-3">
                    {article.title}
                  </h2>
                  <p className="text-gray-600 mb-4">
                    {article.excerpt}
                  </p>
                  <Button variant="outline" size="sm">
                    {t('news.read_more')}
                  </Button>
                </div>
              </article>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button size="lg" variant="outline">
              {t('news.load_more')}
            </Button>
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-20 bg-stone-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            {t('news.newsletter.title')}
          </h2>
          <p className="text-xl text-stone-100 mb-8 max-w-2xl mx-auto">
            {t('news.newsletter.subtitle')}
          </p>
          <div className="max-w-md mx-auto flex gap-4">
            <input
              type="email"
              placeholder={t('news.newsletter.placeholder')}
              className="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white"
            />
            <Button size="lg" className="bg-white text-stone-600 hover:bg-stone-50">
              {t('news.newsletter.subscribe')}
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
