/**
 * OpenAI Service
 * Handles OpenAI API integration for natural language processing
 */

import OpenAI from 'openai';
import { ChatbotIntent, ExtractedEntity, ConversationContext } from '../types';

export class OpenAIService {
  private openai: OpenAI;
  private model: string = 'gpt-4';

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * Detect language of the input text
   */
  async detectLanguage(text: string): Promise<string> {
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a language detection system. Respond only with the ISO 639-1 language code (e.g., "en", "tr", "ar", "de", "fr", "es", "it", "ru", "zh", "ja"). If uncertain, respond with "en".'
          },
          {
            role: 'user',
            content: text
          }
        ],
        max_tokens: 10,
        temperature: 0
      });

      const detectedLanguage = response.choices[0]?.message?.content?.trim().toLowerCase() || 'en';
      
      // Validate language code
      const supportedLanguages = ['en', 'tr', 'ar', 'de', 'fr', 'es', 'it', 'ru', 'zh', 'ja'];
      return supportedLanguages.includes(detectedLanguage) ? detectedLanguage : 'en';
    } catch (error) {
      console.error('Language detection error:', error);
      return 'en'; // Default to English
    }
  }

  /**
   * Classify intent of user message
   */
  async classifyIntent(message: string, language: string = 'en'): Promise<{ intent: ChatbotIntent; confidence: number }> {
    try {
      const systemPrompt = this.getIntentClassificationPrompt(language);
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: message
          }
        ],
        max_tokens: 50,
        temperature: 0
      });

      const result = response.choices[0]?.message?.content?.trim();
      return this.parseIntentResult(result || '');
    } catch (error) {
      console.error('Intent classification error:', error);
      return { intent: ChatbotIntent.UNKNOWN, confidence: 0.1 };
    }
  }

  /**
   * Extract entities from user message
   */
  async extractEntities(message: string, intent: ChatbotIntent): Promise<ExtractedEntity[]> {
    try {
      const systemPrompt = this.getEntityExtractionPrompt(intent);
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: message
          }
        ],
        max_tokens: 200,
        temperature: 0
      });

      const result = response.choices[0]?.message?.content?.trim();
      return this.parseEntityResult(result || '', message);
    } catch (error) {
      console.error('Entity extraction error:', error);
      return [];
    }
  }

  /**
   * Analyze sentiment of user message
   */
  async analyzeSentiment(message: string): Promise<number> {
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'Analyze the sentiment of the following message. Respond with a number between -1 (very negative) and 1 (very positive), with 0 being neutral. Respond only with the number.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        max_tokens: 10,
        temperature: 0
      });

      const sentimentStr = response.choices[0]?.message?.content?.trim();
      const sentiment = parseFloat(sentimentStr || '0');
      
      // Ensure sentiment is within valid range
      return Math.max(-1, Math.min(1, sentiment));
    } catch (error) {
      console.error('Sentiment analysis error:', error);
      return 0; // Neutral sentiment as fallback
    }
  }

  /**
   * Generate response based on context
   */
  async generateResponse(context: ConversationContext, knowledgeBase: any): Promise<string> {
    try {
      const systemPrompt = this.getResponseGenerationPrompt(context, knowledgeBase);
      
      const messages = [
        {
          role: 'system' as const,
          content: systemPrompt
        },
        ...context.conversationHistory.slice(-5).map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content
        }))
      ];

      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages,
        max_tokens: 500,
        temperature: 0.7
      });

      return response.choices[0]?.message?.content?.trim() || 'I apologize, but I cannot generate a response at the moment. Please try again.';
    } catch (error) {
      console.error('Response generation error:', error);
      return 'I apologize, but I cannot generate a response at the moment. Please try again.';
    }
  }

  /**
   * Get intent classification prompt based on language
   */
  private getIntentClassificationPrompt(language: string): string {
    const basePrompt = `You are an intent classifier for a natural stone marketplace platform. 
    
Available intents:
- product_inquiry: Questions about stone types, properties, or general product information
- product_specifications: Technical details, dimensions, quality standards
- product_pricing: Price inquiries, cost estimates
- product_availability: Stock availability, delivery times
- order_status: Checking order progress
- order_tracking: Shipment tracking information
- order_modification: Changes to existing orders
- order_cancellation: Canceling orders
- bid_process: How bidding works, bid submission
- bid_status: Status of submitted bids
- bid_requirements: Requirements for bidding
- account_setup: Registration, profile creation
- profile_update: Updating account information
- verification_status: Account verification status
- payment_methods: Available payment options
- payment_status: Payment confirmation, issues
- refund_request: Requesting refunds
- technical_issue: Platform bugs, technical problems
- platform_usage: How to use the platform
- greeting: Hello, welcome messages
- goodbye: Farewell messages
- human_handoff: Request to speak with human agent
- complaint: Complaints, dissatisfaction
- unknown: Cannot determine intent

Respond with: intent_name confidence_score (e.g., "product_inquiry 0.85")`;

    if (language === 'tr') {
      return basePrompt + '\n\nThe user message will be in Turkish. Classify accordingly.';
    }
    
    return basePrompt + '\n\nThe user message will be in English or another language. Classify accordingly.';
  }

  /**
   * Get entity extraction prompt based on intent
   */
  private getEntityExtractionPrompt(intent: ChatbotIntent): string {
    const entityTypes = {
      [ChatbotIntent.PRODUCT_INQUIRY]: ['stone_type', 'application', 'location'],
      [ChatbotIntent.PRODUCT_SPECIFICATIONS]: ['stone_type', 'dimension', 'finish_type', 'quality_standard'],
      [ChatbotIntent.PRODUCT_PRICING]: ['stone_type', 'quantity', 'unit', 'delivery_location'],
      [ChatbotIntent.ORDER_STATUS]: ['order_id', 'order_number'],
      [ChatbotIntent.BID_STATUS]: ['bid_id', 'product_type'],
      // Add more mappings as needed
    };

    const relevantEntities = (entityTypes as any)[intent] || [];
    
    return `Extract entities from the user message. Look for these entity types: ${relevantEntities.join(', ')}.
    
Respond in JSON format:
[
  {
    "type": "entity_type",
    "value": "extracted_value",
    "confidence": 0.95,
    "start": 0,
    "end": 10
  }
]

If no entities found, respond with: []`;
  }

  /**
   * Get response generation prompt
   */
  private getResponseGenerationPrompt(context: ConversationContext, knowledgeBase: any): string {
    const languageInstructions = {
      'tr': 'Respond in Turkish. Be professional and helpful.',
      'en': 'Respond in English. Be professional and helpful.',
      'ar': 'Respond in Arabic. Be professional and helpful.',
      'de': 'Respond in German. Be professional and helpful.',
      'fr': 'Respond in French. Be professional and helpful.',
      'es': 'Respond in Spanish. Be professional and helpful.',
      'it': 'Respond in Italian. Be professional and helpful.',
      'ru': 'Respond in Russian. Be professional and helpful.',
      'zh': 'Respond in Chinese. Be professional and helpful.',
      'ja': 'Respond in Japanese. Be professional and helpful.'
    };

    return `You are a helpful AI assistant for a Turkish natural stone marketplace platform.

Context:
- User language: ${context.language}
- Current intent: ${context.currentIntent}
- User type: ${context.userProfile?.type || 'unknown'}
- Conversation history available

Instructions:
- ${(languageInstructions as any)[context.language] || languageInstructions['en']}
- Provide accurate information about natural stone products, processes, and platform features
- If you don't know something, admit it and suggest contacting human support
- Be concise but informative
- Maintain a professional yet friendly tone
- Focus on helping the user achieve their goal

Platform Information:
- We are a B2B marketplace for Turkish natural stone
- We offer marble, travertine, granite, onyx, limestone, basalt, and andesite
- We have an anonymous bidding system
- We support international shipping
- Payment terms include FOB, CIF, and DDP
- We have escrow payment protection

Current user intent: ${context.currentIntent}
Respond appropriately to help the user.`;
  }

  /**
   * Parse intent classification result
   */
  private parseIntentResult(result: string): { intent: ChatbotIntent; confidence: number } {
    try {
      const parts = result.split(' ');
      const intentStr = parts[0];
      const confidenceStr = parts[1];
      
      const intent = Object.values(ChatbotIntent).includes(intentStr as ChatbotIntent) 
        ? intentStr as ChatbotIntent 
        : ChatbotIntent.UNKNOWN;
      
      const confidence = parseFloat(confidenceStr) || 0.1;
      
      return { intent, confidence };
    } catch (error) {
      return { intent: ChatbotIntent.UNKNOWN, confidence: 0.1 };
    }
  }

  /**
   * Parse entity extraction result
   */
  private parseEntityResult(result: string, originalMessage: string): ExtractedEntity[] {
    try {
      const entities = JSON.parse(result);
      return Array.isArray(entities) ? entities : [];
    } catch (error) {
      console.error('Entity parsing error:', error);
      return [];
    }
  }
}
