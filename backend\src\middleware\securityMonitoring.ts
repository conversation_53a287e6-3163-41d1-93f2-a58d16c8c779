/**
 * Security Monitoring and Intrusion Detection System
 * Monitors suspicious activities, tracks security events, and provides audit logging
 */

import { Request, Response, NextFunction } from 'express';
import { logInfo, logWarn, logError } from '../utils/logger';
import { sessionManager } from '../config/session';

export interface SecurityEvent {
  type: 'login_attempt' | 'failed_login' | 'suspicious_activity' | 'rate_limit_exceeded' | 
        'csrf_violation' | 'file_upload_blocked' | 'sql_injection_attempt' | 'xss_attempt' |
        'unauthorized_access' | 'session_hijack' | 'brute_force' | 'account_lockout';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ip: string;
  userAgent: string;
  endpoint: string;
  method: string;
  timestamp: number;
  details: any;
  blocked: boolean;
}

export interface ThreatLevel {
  ip: string;
  score: number;
  events: SecurityEvent[];
  firstSeen: number;
  lastSeen: number;
  blocked: boolean;
}

/**
 * In-memory threat tracking (in production, use Redis or database)
 */
const threatTracker = new Map<string, ThreatLevel>();
const suspiciousIPs = new Set<string>();
const blockedIPs = new Set<string>();

/**
 * Security thresholds
 */
const SECURITY_THRESHOLDS = {
  FAILED_LOGIN_ATTEMPTS: 5,
  SUSPICIOUS_ACTIVITY_SCORE: 50,
  CRITICAL_ACTIVITY_SCORE: 100,
  TIME_WINDOW: 15 * 60 * 1000, // 15 minutes
  BLOCK_DURATION: 60 * 60 * 1000, // 1 hour
};

/**
 * Calculate threat score based on event type
 */
function calculateThreatScore(eventType: SecurityEvent['type']): number {
  const scores = {
    'login_attempt': 1,
    'failed_login': 10,
    'suspicious_activity': 15,
    'rate_limit_exceeded': 5,
    'csrf_violation': 25,
    'file_upload_blocked': 20,
    'sql_injection_attempt': 50,
    'xss_attempt': 40,
    'unauthorized_access': 30,
    'session_hijack': 75,
    'brute_force': 60,
    'account_lockout': 35
  };
  return scores[eventType] || 10;
}

/**
 * Log security event
 */
export function logSecurityEvent(event: SecurityEvent): void {
  const logData = {
    type: event.type,
    severity: event.severity,
    userId: event.userId,
    ip: event.ip,
    userAgent: event.userAgent,
    endpoint: event.endpoint,
    method: event.method,
    blocked: event.blocked,
    details: event.details
  };

  switch (event.severity) {
    case 'critical':
      logError(`CRITICAL SECURITY EVENT: ${event.type}`, new Error('Security violation'), logData);
      break;
    case 'high':
      logError(`HIGH SECURITY EVENT: ${event.type}`, new Error('Security violation'), logData);
      break;
    case 'medium':
      logWarn(`MEDIUM SECURITY EVENT: ${event.type}`, logData);
      break;
    case 'low':
      logInfo(`LOW SECURITY EVENT: ${event.type}`, logData);
      break;
  }

  // Update threat tracking
  updateThreatLevel(event);
}

/**
 * Update threat level for IP
 */
function updateThreatLevel(event: SecurityEvent): void {
  const ip = event.ip;
  const now = Date.now();
  
  let threat = threatTracker.get(ip);
  if (!threat) {
    threat = {
      ip,
      score: 0,
      events: [],
      firstSeen: now,
      lastSeen: now,
      blocked: false
    };
  }

  // Add event
  threat.events.push(event);
  threat.lastSeen = now;
  threat.score += calculateThreatScore(event.type);

  // Clean old events (outside time window)
  const cutoff = now - SECURITY_THRESHOLDS.TIME_WINDOW;
  threat.events = threat.events.filter(e => e.timestamp > cutoff);
  
  // Recalculate score based on recent events
  threat.score = threat.events.reduce((sum, e) => sum + calculateThreatScore(e.type), 0);

  // Check if IP should be blocked
  if (threat.score >= SECURITY_THRESHOLDS.CRITICAL_ACTIVITY_SCORE && !threat.blocked) {
    threat.blocked = true;
    blockedIPs.add(ip);
    
    logError('IP BLOCKED due to critical threat level', new Error('Security violation'), {
      ip,
      score: threat.score,
      events: threat.events.length
    });

    // Schedule unblock
    setTimeout(() => {
      blockedIPs.delete(ip);
      threat!.blocked = false;
      logInfo('IP UNBLOCKED after timeout', { ip });
    }, SECURITY_THRESHOLDS.BLOCK_DURATION);
  }

  // Mark as suspicious
  if (threat.score >= SECURITY_THRESHOLDS.SUSPICIOUS_ACTIVITY_SCORE) {
    suspiciousIPs.add(ip);
  }

  threatTracker.set(ip, threat);
}

/**
 * Check if IP is blocked
 */
export function isIPBlocked(ip: string): boolean {
  return blockedIPs.has(ip);
}

/**
 * Check if IP is suspicious
 */
export function isIPSuspicious(ip: string): boolean {
  return suspiciousIPs.has(ip);
}

/**
 * Get threat level for IP
 */
export function getThreatLevel(ip: string): ThreatLevel | null {
  return threatTracker.get(ip) || null;
}

/**
 * Security monitoring middleware
 */
export function securityMonitoring(req: Request, res: Response, next: NextFunction): any {
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  const userAgent = req.get('User-Agent') || 'unknown';
  const endpoint = req.path;
  const method = req.method;

  // Check if IP is blocked
  if (isIPBlocked(ip)) {
    logSecurityEvent({
      type: 'unauthorized_access',
      severity: 'high',
      ip,
      userAgent,
      endpoint,
      method,
      timestamp: Date.now(),
      details: { reason: 'IP blocked due to previous violations' },
      blocked: true
    });

    return res.status(403).json({
      success: false,
      error: 'Access denied',
      code: 'IP_BLOCKED'
    });
  }

  // Log suspicious activity
  if (isIPSuspicious(ip)) {
    logSecurityEvent({
      type: 'suspicious_activity',
      severity: 'medium',
      ip,
      userAgent,
      endpoint,
      method,
      timestamp: Date.now(),
      details: { reason: 'IP marked as suspicious' },
      blocked: false
    });
  }

  // Monitor for suspicious patterns
  const suspiciousPatterns = [
    /\.\.\//g, // Directory traversal
    /<script/gi, // XSS attempts
    /union\s+select/gi, // SQL injection
    /exec\s*\(/gi, // Code execution
    /eval\s*\(/gi, // Code evaluation
  ];

  const fullUrl = req.originalUrl || req.url;
  const body = JSON.stringify(req.body || {});
  const query = JSON.stringify(req.query || {});

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(fullUrl) || pattern.test(body) || pattern.test(query)) {
      logSecurityEvent({
        type: 'suspicious_activity',
        severity: 'high',
        ip,
        userAgent,
        endpoint,
        method,
        timestamp: Date.now(),
        details: { 
          pattern: pattern.source,
          url: fullUrl,
          body: body.substring(0, 200),
          query: query.substring(0, 200)
        },
        blocked: false
      });
      break;
    }
  }

  // Add security headers to response
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  next();
}

/**
 * Failed login monitoring
 */
export function monitorFailedLogin(ip: string, userAgent: string, email?: string): void {
  logSecurityEvent({
    type: 'failed_login',
    severity: 'medium',
    ip,
    userAgent,
    endpoint: '/api/auth/login',
    method: 'POST',
    timestamp: Date.now(),
    details: { email },
    blocked: false
  });
}

/**
 * Successful login monitoring
 */
export function monitorSuccessfulLogin(ip: string, userAgent: string, userId: string): void {
  logSecurityEvent({
    type: 'login_attempt',
    severity: 'low',
    userId,
    ip,
    userAgent,
    endpoint: '/api/auth/login',
    method: 'POST',
    timestamp: Date.now(),
    details: { success: true },
    blocked: false
  });
}

/**
 * Rate limit exceeded monitoring
 */
export function monitorRateLimitExceeded(ip: string, userAgent: string, endpoint: string): void {
  logSecurityEvent({
    type: 'rate_limit_exceeded',
    severity: 'medium',
    ip,
    userAgent,
    endpoint,
    method: 'ANY',
    timestamp: Date.now(),
    details: { reason: 'Rate limit exceeded' },
    blocked: true
  });
}

/**
 * CSRF violation monitoring
 */
export function monitorCSRFViolation(ip: string, userAgent: string, endpoint: string): void {
  logSecurityEvent({
    type: 'csrf_violation',
    severity: 'high',
    ip,
    userAgent,
    endpoint,
    method: 'POST',
    timestamp: Date.now(),
    details: { reason: 'CSRF token missing or invalid' },
    blocked: true
  });
}

/**
 * File upload blocked monitoring
 */
export function monitorFileUploadBlocked(ip: string, userAgent: string, filename: string, reason: string): void {
  logSecurityEvent({
    type: 'file_upload_blocked',
    severity: 'medium',
    ip,
    userAgent,
    endpoint: '/api/upload',
    method: 'POST',
    timestamp: Date.now(),
    details: { filename, reason },
    blocked: true
  });
}

/**
 * Get security statistics
 */
export function getSecurityStats(): any {
  const now = Date.now();
  const cutoff = now - SECURITY_THRESHOLDS.TIME_WINDOW;
  
  let totalEvents = 0;
  let criticalEvents = 0;
  let blockedIPs = 0;
  let suspiciousIPs = 0;

  for (const [ip, threat] of threatTracker.entries()) {
    const recentEvents = threat.events.filter(e => e.timestamp > cutoff);
    totalEvents += recentEvents.length;
    
    const criticalEventsForIP = recentEvents.filter(e => e.severity === 'critical').length;
    criticalEvents += criticalEventsForIP;
    
    if (threat.blocked) blockedIPs++;
    if (threat.score >= SECURITY_THRESHOLDS.SUSPICIOUS_ACTIVITY_SCORE) suspiciousIPs++;
  }

  return {
    totalEvents,
    criticalEvents,
    blockedIPs,
    suspiciousIPs,
    timeWindow: SECURITY_THRESHOLDS.TIME_WINDOW,
    thresholds: SECURITY_THRESHOLDS
  };
}

/**
 * Clean up old threat data (should be run periodically)
 */
export function cleanupThreatData(): void {
  const now = Date.now();
  const cutoff = now - (24 * 60 * 60 * 1000); // 24 hours

  for (const [ip, threat] of threatTracker.entries()) {
    if (threat.lastSeen < cutoff) {
      threatTracker.delete(ip);
      suspiciousIPs.delete(ip);
      // Don't remove from blockedIPs as they have their own timeout
    }
  }

  logInfo('Threat data cleanup completed', {
    remainingThreats: threatTracker.size,
    suspiciousIPs: suspiciousIPs.size,
    blockedIPs: blockedIPs.size
  });
}
