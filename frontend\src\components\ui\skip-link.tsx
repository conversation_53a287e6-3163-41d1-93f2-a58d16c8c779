import * as React from "react"
import { cn } from "@/lib/utils"

export interface SkipLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  targetId: string
  children: React.ReactNode
}

/**
 * SkipLink component following RFC-004 UI/UX Design System
 * Accessibility component for keyboard navigation
 */
const SkipLink = React.forwardRef<HTMLAnchorElement, SkipLinkProps>(
  ({ className, targetId, children, ...props }, ref) => {
    return (
      <a
        ref={ref}
        href={`#${targetId}`}
        className={cn(
          // Position off-screen by default
          "absolute -top-10 left-6 z-[9999]",
          
          // Styling
          "bg-[var(--primary-stone)] text-white",
          "px-4 py-2 rounded-[var(--radius-md)]",
          "font-medium text-[var(--text-sm)]",
          "shadow-lg border-2 border-[var(--primary-dark)]",
          
          // Focus styles - bring into view
          "focus:top-6",
          "focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2",
          
          // Transitions
          "transition-all duration-200 ease-in-out",
          
          className
        )}
        {...props}
      >
        {children}
      </a>
    )
  }
)

SkipLink.displayName = "SkipLink"

export { SkipLink }
