'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import {
  Package,
  Search,
  Filter,
  Eye,
  Calendar,
  DollarSign,
  Building,
  Truck
} from 'lucide-react';
import toast from 'react-hot-toast';

interface OrderSummary {
  id: string;
  orderNumber: string;
  productName: string;
  totalQuantity: number;
  unit: string;
  totalAmount: number;
  currency: string;
  status: string;
  createdAt: string;
  producer: {
    name: string;
    company: string;
  };
  deliveryProgress: {
    completed: number;
    total: number;
    percentage: number;
  };
  paymentProgress: {
    paid: number;
    total: number;
    percentage: number;
  };
  nextDeliveryDate?: string;
}

export default function CustomerOrdersPage() {
  const router = useRouter();
  const [orders, setOrders] = useState<OrderSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockOrders: OrderSummary[] = [
        {
          id: 'ord_001',
          orderNumber: 'ORD-001234',
          productName: 'Beyaz Mermer - Afyon',
          totalQuantity: 500,
          unit: 'm²',
          totalAmount: 75000,
          currency: 'TRY',
          status: 'in_production',
          createdAt: '2024-01-15T10:00:00Z',
          producer: {
            name: 'Mehmet Kaya',
            company: 'Afyon Mermer A.Ş.'
          },
          deliveryProgress: {
            completed: 1,
            total: 3,
            percentage: 33
          },
          paymentProgress: {
            paid: 52500,
            total: 75000,
            percentage: 70
          },
          nextDeliveryDate: '2024-02-15'
        }
      ];
      setOrders(mockOrders);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Siparişler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleNavigate = (route: string) => {
    router.push(route);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Bekliyor', color: 'bg-yellow-100 text-yellow-800' },
      confirmed: { label: 'Onaylandı', color: 'bg-blue-100 text-blue-800' },
      in_production: { label: 'Üretimde', color: 'bg-purple-100 text-purple-800' },
      partially_delivered: { label: 'Kısmi Teslim', color: 'bg-orange-100 text-orange-800' },
      completed: { label: 'Tamamlandı', color: 'bg-green-100 text-green-800' },
      cancelled: { label: 'İptal Edildi', color: 'bg-red-100 text-red-800' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.producer.company.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Siparişler yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Siparişlerim</h1>
          <p className="text-gray-600 mt-1">Tüm siparişlerinizi görüntüleyin ve takip edin</p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Sipariş numarası, ürün adı veya üretici ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
        >
          <option value="all">Tüm Durumlar</option>
          <option value="pending">Bekliyor</option>
          <option value="confirmed">Onaylandı</option>
          <option value="in_production">Üretimde</option>
          <option value="partially_delivered">Kısmi Teslim</option>
          <option value="completed">Tamamlandı</option>
          <option value="cancelled">İptal Edildi</option>
        </select>
      </div>

      {/* Orders List */}
      <div className="grid gap-6">
        {filteredOrders.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Sipariş bulunamadı</h3>
              <p className="text-gray-600">
                {searchTerm || statusFilter !== 'all'
                  ? 'Arama kriterlerinize uygun sipariş bulunamadı.'
                  : 'Henüz hiç siparişiniz bulunmuyor.'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredOrders.map((order) => (
            <Card key={order.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{order.orderNumber}</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">{order.productName}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-gray-900 mb-1">
                      {formatCurrency(order.totalAmount, order.currency)}
                    </div>
                    {getStatusBadge(order.status)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <p className="text-sm font-medium text-gray-900 mb-1">Üretici</p>
                    <p className="text-sm text-gray-600 flex items-center gap-1">
                      <Building className="w-4 h-4" />
                      {order.producer.company}
                    </p>
                    <p className="text-sm text-gray-600">{order.producer.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 mb-1">Miktar</p>
                    <p className="text-sm text-gray-600">
                      {order.totalQuantity} {order.unit}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 mb-1">Sipariş Tarihi</p>
                    <p className="text-sm text-gray-600 flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {formatDate(order.createdAt)}
                    </p>
                  </div>
                </div>

                {/* Progress Bars */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">Teslimat İlerlemesi</span>
                      <span className="text-sm text-gray-600">
                        {order.deliveryProgress.completed}/{order.deliveryProgress.total}
                      </span>
                    </div>
                    <Progress value={order.deliveryProgress.percentage} className="h-2" />
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">Ödeme İlerlemesi</span>
                      <span className="text-sm text-gray-600">
                        {formatCurrency(order.paymentProgress.paid, order.currency)} / {formatCurrency(order.paymentProgress.total, order.currency)}
                      </span>
                    </div>
                    <Progress value={order.paymentProgress.percentage} className="h-2" />
                  </div>
                </div>

                {/* Next Delivery */}
                {order.nextDeliveryDate && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                    <p className="text-sm font-medium text-blue-900 flex items-center gap-1">
                      <Truck className="w-4 h-4" />
                      Sonraki Teslimat: {formatDate(order.nextDeliveryDate)}
                    </p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleNavigate(`/customer/orders/${order.id}`)}
                    className="flex-1"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Detayları Görüntüle
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
