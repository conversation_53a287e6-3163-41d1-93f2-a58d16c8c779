'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  CreditCardIcon,
  BanknotesIcon,
  GlobeAltIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import { useSample } from '@/contexts/sample-context';

interface CustomerSamplePaymentPageProps {
  sampleRequestId: string;
  onNavigate?: (route: string) => void;
}

const CustomerSamplePaymentPage: React.FC<CustomerSamplePaymentPageProps> = ({ 
  sampleRequestId, 
  onNavigate 
}) => {
  const { getSampleRequestDetail, isLoading } = useSample();
  const [sampleRequest, setSampleRequest] = useState<any>(null);
  const [payment, setPayment] = useState<any>(null);
  const [selectedMethod, setSelectedMethod] = useState<'credit_card' | 'bank_transfer' | 'paypal'>('credit_card');
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentComplete, setPaymentComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Credit Card Form
  const [cardData, setCardData] = useState({
    number: '',
    expiry: '',
    cvv: '',
    name: ''
  });

  // Bank Transfer Form
  const [bankData, setBankData] = useState({
    accountName: '',
    iban: '',
    reference: ''
  });

  useEffect(() => {
    loadSampleRequest();
    loadPaymentInfo();
  }, [sampleRequestId]);

  const loadSampleRequest = async () => {
    try {
      const response = await fetch(`http://localhost:8001/api/samples/${sampleRequestId}`);
      if (response.ok) {
        const result = await response.json();
        setSampleRequest(result.data);
      }
    } catch (error) {
      console.error('Error loading sample request:', error);
    }
  };

  const loadPaymentInfo = async () => {
    try {
      const response = await fetch(`http://localhost:8001/api/samples/payment/${sampleRequestId}`);
      if (response.ok) {
        const result = await response.json();
        setPayment(result.data);
        if (result.data.status === 'completed') {
          setPaymentComplete(true);
        }
      }
    } catch (error) {
      console.error('Error loading payment info:', error);
    }
  };

  const processPayment = async () => {
    if (!payment) return;

    setIsProcessing(true);
    setError(null);

    try {
      let transactionData = {};

      if (selectedMethod === 'credit_card') {
        transactionData = {
          cardNumber: cardData.number.replace(/\s/g, ''),
          expiryDate: cardData.expiry,
          cvv: cardData.cvv,
          cardholderName: cardData.name
        };
      } else if (selectedMethod === 'bank_transfer') {
        transactionData = {
          accountName: bankData.accountName,
          iban: bankData.iban,
          reference: bankData.reference
        };
      } else if (selectedMethod === 'paypal') {
        transactionData = {
          paypalEmail: '<EMAIL>' // Should be actual customer email
        };
      }

      const response = await fetch(`http://localhost:8001/api/samples/payment/${payment.id}/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentMethod: selectedMethod,
          transactionData
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setPaymentComplete(true);
        setPayment(result.data.payment);
        
        // Send notification
        await sendPaymentNotification();
        
        setTimeout(() => {
          onNavigate?.('/customer/requests/samples');
        }, 3000);
      } else {
        const errorResult = await response.json();
        setError(errorResult.error || 'Ödeme işlemi başarısız oldu.');
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      setError('Ödeme işlemi sırasında hata oluştu.');
    } finally {
      setIsProcessing(false);
    }
  };

  const sendPaymentNotification = async () => {
    try {
      await fetch('http://localhost:8001/api/notifications/payment-completed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sampleRequestId,
          customerId: sampleRequest?.customerId,
          producerId: sampleRequest?.producerId,
          amount: payment?.amount
        }),
      });
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiry = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Ödeme bilgileri yükleniyor...</span>
      </div>
    );
  }

  if (paymentComplete) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="max-w-md mx-auto text-center py-12"
      >
        <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Ödeme Başarılı!</h2>
        <p className="text-gray-600 mb-4">
          Kargo ücreti başarıyla ödendi. Üretici numune hazırlığına başlayacak.
        </p>
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <p className="text-sm text-green-800">
            <strong>Ödenen Tutar:</strong> {payment?.amount} TL<br />
            <strong>İşlem ID:</strong> {payment?.transactionId}
          </p>
        </div>
        <button
          onClick={() => onNavigate?.('/customer/requests/samples')}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Numune Taleplerime Dön
        </button>
      </motion.div>
    );
  }

  if (!sampleRequest || !payment) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Ödeme Bilgisi Bulunamadı</h2>
        <p className="text-gray-600">Bu numune talebi için ödeme bilgisi bulunamadı.</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => onNavigate?.('/customer/requests/samples')}
          className="p-2 text-gray-600 hover:text-gray-900 rounded-lg hover:bg-gray-100"
        >
          <ArrowLeftIcon className="h-5 w-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Numune Kargo Ücreti Ödemesi</h1>
          <p className="text-gray-600">Numune Talebi #{sampleRequestId.slice(-6)}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Payment Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Ödeme Özeti</h3>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Kargo Ücreti:</span>
                <span className="font-medium">{payment.amount} TL</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Para Birimi:</span>
                <span className="font-medium">{payment.currency}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Kargo Firması:</span>
                <span className="font-medium">
                  {sampleRequest.producerResponse?.carrier === 'aras' ? 'Aras Kargo' :
                   sampleRequest.producerResponse?.carrier === 'mng' ? 'MNG Kargo' :
                   sampleRequest.producerResponse?.carrier === 'ups' ? 'UPS' :
                   sampleRequest.producerResponse?.carrier === 'dhl' ? 'DHL' : 'Belirtilmemiş'}
                </span>
              </div>
              <hr className="my-3" />
              <div className="flex justify-between text-lg font-semibold">
                <span>Toplam:</span>
                <span className="text-blue-600">{payment.amount} TL</span>
              </div>
            </div>

            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <ClockIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Ödeme Bekleniyor</p>
                  <p className="text-sm text-yellow-700">
                    Ödeme yapıldıktan sonra üretici numune hazırlığına başlayacaktır.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Ödeme Yöntemi Seçin</h3>

            {/* Payment Method Tabs */}
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6">
              <button
                onClick={() => setSelectedMethod('credit_card')}
                className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  selectedMethod === 'credit_card'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <CreditCardIcon className="h-4 w-4" />
                <span>Kredi Kartı</span>
              </button>
              <button
                onClick={() => setSelectedMethod('bank_transfer')}
                className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  selectedMethod === 'bank_transfer'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <BanknotesIcon className="h-4 w-4" />
                <span>Banka Havalesi</span>
              </button>
              <button
                onClick={() => setSelectedMethod('paypal')}
                className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  selectedMethod === 'paypal'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <GlobeAltIcon className="h-4 w-4" />
                <span>PayPal</span>
              </button>
            </div>

            {/* Payment Forms */}
            {selectedMethod === 'credit_card' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Kart Numarası
                  </label>
                  <input
                    type="text"
                    value={cardData.number}
                    onChange={(e) => setCardData(prev => ({ ...prev, number: formatCardNumber(e.target.value) }))}
                    placeholder="1234 5678 9012 3456"
                    maxLength={19}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Son Kullanma Tarihi
                    </label>
                    <input
                      type="text"
                      value={cardData.expiry}
                      onChange={(e) => setCardData(prev => ({ ...prev, expiry: formatExpiry(e.target.value) }))}
                      placeholder="MM/YY"
                      maxLength={5}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      CVV
                    </label>
                    <input
                      type="text"
                      value={cardData.cvv}
                      onChange={(e) => setCardData(prev => ({ ...prev, cvv: e.target.value.replace(/\D/g, '') }))}
                      placeholder="123"
                      maxLength={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Kart Sahibinin Adı
                  </label>
                  <input
                    type="text"
                    value={cardData.name}
                    onChange={(e) => setCardData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Ad Soyad"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            )}

            {selectedMethod === 'bank_transfer' && (
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">Banka Bilgileri</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <p><strong>Banka:</strong> Türkiye İş Bankası</p>
                    <p><strong>Hesap Adı:</strong> Doğal Taş Pazaryeri Ltd. Şti.</p>
                    <p><strong>IBAN:</strong> TR12 0006 4000 0011 2345 6789 01</p>
                    <p><strong>Açıklama:</strong> Numune #{sampleRequestId.slice(-6)}</p>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Gönderen Hesap Adı
                  </label>
                  <input
                    type="text"
                    value={bankData.accountName}
                    onChange={(e) => setBankData(prev => ({ ...prev, accountName: e.target.value }))}
                    placeholder="Hesap sahibinin adı"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Gönderen IBAN
                  </label>
                  <input
                    type="text"
                    value={bankData.iban}
                    onChange={(e) => setBankData(prev => ({ ...prev, iban: e.target.value }))}
                    placeholder="TR12 3456 7890 1234 5678 9012 34"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Referans/Açıklama
                  </label>
                  <input
                    type="text"
                    value={bankData.reference}
                    onChange={(e) => setBankData(prev => ({ ...prev, reference: e.target.value }))}
                    placeholder={`Numune ${sampleRequestId.slice(-6)}`}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            )}

            {selectedMethod === 'paypal' && (
              <div className="text-center py-8">
                <GlobeAltIcon className="h-16 w-16 text-blue-500 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">PayPal ile Ödeme</h4>
                <p className="text-gray-600 mb-4">
                  PayPal hesabınızla güvenli ödeme yapabilirsiniz.
                </p>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <p className="text-sm text-gray-600">
                    "Ödeme Yap" butonuna tıkladığınızda PayPal sayfasına yönlendirileceksiniz.
                  </p>
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5" />
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            )}

            {/* Payment Button */}
            <div className="mt-6">
              <button
                onClick={processPayment}
                disabled={isProcessing || (selectedMethod === 'credit_card' && (!cardData.number || !cardData.expiry || !cardData.cvv || !cardData.name))}
                className="w-full py-3 px-4 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>İşleniyor...</span>
                  </>
                ) : (
                  <span>{payment.amount} TL Ödeme Yap</span>
                )}
              </button>
            </div>

            {/* Security Info */}
            <div className="mt-4 text-center">
              <p className="text-xs text-gray-500">
                🔒 Ödeme bilgileriniz SSL ile şifrelenir ve güvenli bir şekilde işlenir.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerSamplePaymentPage;
