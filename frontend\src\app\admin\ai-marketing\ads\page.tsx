'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  TrendingUp,
  DollarSign,
  Search,
  Plus,
  Eye,
  Edit,
  Play,
  Pause,
  Bar<PERSON>hart<PERSON>,
  Target,
  MousePointer,
  Users,
  Calendar,
  X,
  Save,
  AlertTriangle
} from 'lucide-react';

interface AdCampaign {
  id: string;
  name: string;
  platform: 'google_ads' | 'facebook_ads' | 'linkedin_ads' | 'twitter_ads';
  campaignType: 'search' | 'display' | 'video' | 'shopping' | 'social';
  status: 'active' | 'paused' | 'ended' | 'draft';
  budget: {
    total: number;
    daily: number;
    spent: number;
    currency: string;
  };
  performance: {
    impressions: number;
    clicks: number;
    conversions: number;
    ctr: number;
    cpc: number;
    cpa: number;
    roas: number;
  };
  targetAudience: {
    locations: string[];
    demographics: string;
    interests: string[];
  };
  startDate: Date;
  endDate?: Date;
  aiManaged: boolean;
  lastOptimized?: Date;
}

interface AdCreative {
  id: string;
  campaignId: string;
  type: 'text' | 'image' | 'video' | 'carousel';
  headline: string;
  description: string;
  imageUrl?: string;
  performance: {
    impressions: number;
    clicks: number;
    ctr: number;
  };
  aiGenerated: boolean;
}

export default function AdsManagementPage() {
  const [campaigns, setCampaigns] = useState<AdCampaign[]>([]);
  const [creatives, setCreatives] = useState<AdCreative[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  // Modal states
  const [selectedCampaign, setSelectedCampaign] = useState<AdCampaign | null>(null);
  const [selectedCreative, setSelectedCreative] = useState<AdCreative | null>(null);
  const [showCampaignModal, setShowCampaignModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showStatsModal, setShowStatsModal] = useState(false);
  const [showCreativeModal, setShowCreativeModal] = useState(false);
  const [showEditCreativeModal, setShowEditCreativeModal] = useState(false);

  // Form states
  const [editForm, setEditForm] = useState<Partial<AdCampaign>>({});
  const [editCreativeForm, setEditCreativeForm] = useState<Partial<AdCreative>>({});

  // New campaign and optimization states
  const [showNewCampaignModal, setShowNewCampaignModal] = useState(false);
  const [showOptimizationModal, setShowOptimizationModal] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [newCampaignForm, setNewCampaignForm] = useState({
    name: '',
    platform: '',
    campaignType: '',
    dailyBudget: '',
    totalBudget: '',
    targetLocations: '',
    targetDemographics: '',
    targetInterests: ''
  });

  useEffect(() => {
    fetchAdsData();
  }, []);

  const fetchAdsData = async () => {
    setLoading(true);
    try {
      // Data will be loaded from API
      const mockCampaigns: AdCampaign[] = [];
      const mockCreatives: AdCreative[] = [];

      setCampaigns([]);
      setCreatives([]);
    } catch (error) {
      console.error('Error fetching ads data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Aktif</Badge>;
      case 'paused':
        return <Badge variant="secondary">Duraklatıldı</Badge>;
      case 'ended':
        return <Badge variant="outline">Sona Erdi</Badge>;
      case 'draft':
        return <Badge variant="outline">Taslak</Badge>;
      default:
        return <Badge variant="outline">Bilinmiyor</Badge>;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'google_ads':
        return '🔍';
      case 'facebook_ads':
        return '📘';
      case 'linkedin_ads':
        return '💼';
      case 'twitter_ads':
        return '🐦';
      default:
        return '📱';
    }
  };

  const getPlatformName = (platform: string) => {
    switch (platform) {
      case 'google_ads':
        return 'Google Ads';
      case 'facebook_ads':
        return 'Facebook Ads';
      case 'linkedin_ads':
        return 'LinkedIn Ads';
      case 'twitter_ads':
        return 'Twitter Ads';
      default:
        return platform;
    }
  };

  const filteredCampaigns = campaigns.filter(campaign =>
    campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    getPlatformName(campaign.platform).toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalSpent = campaigns.reduce((sum, campaign) => sum + campaign.budget.spent, 0);
  const totalImpressions = campaigns.reduce((sum, campaign) => sum + campaign.performance.impressions, 0);
  const totalClicks = campaigns.reduce((sum, campaign) => sum + campaign.performance.clicks, 0);
  const totalConversions = campaigns.reduce((sum, campaign) => sum + campaign.performance.conversions, 0);
  const avgROAS = campaigns.length > 0
    ? campaigns.reduce((sum, campaign) => sum + campaign.performance.roas, 0) / campaigns.length
    : 0;

  // Handler functions
  const handleViewCampaign = (campaign: AdCampaign) => {
    setSelectedCampaign(campaign);
    setShowCampaignModal(true);
  };

  const handleEditCampaign = (campaign: AdCampaign) => {
    setSelectedCampaign(campaign);
    setEditForm(campaign);
    setShowEditModal(true);
  };

  const handleToggleCampaignStatus = async (campaign: AdCampaign) => {
    try {
      const newStatus = campaign.status === 'active' ? 'paused' : 'active';

      // API call to update campaign status
      const response = await fetch(`/api/admin/ai-marketing/ads/campaigns/${campaign.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        // Update local state
        setCampaigns(prev => prev.map(c =>
          c.id === campaign.id ? { ...c, status: newStatus } : c
        ));
      }
    } catch (error) {
      console.error('Error updating campaign status:', error);
    }
  };

  const handleShowStats = (campaign: AdCampaign) => {
    setSelectedCampaign(campaign);
    setShowStatsModal(true);
  };

  const handleViewCreative = (creative: AdCreative) => {
    setSelectedCreative(creative);
    setShowCreativeModal(true);
  };

  const handleEditCreative = (creative: AdCreative) => {
    setSelectedCreative(creative);
    setEditCreativeForm(creative);
    setShowEditCreativeModal(true);
  };

  const handleSaveEdit = async () => {
    if (!selectedCampaign) return;

    try {
      const response = await fetch(`/api/admin/ai-marketing/ads/campaigns/${selectedCampaign.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (response.ok) {
        // Update local state
        setCampaigns(prev => prev.map(c =>
          c.id === selectedCampaign.id ? { ...c, ...editForm } : c
        ));
        setShowEditModal(false);
        setEditForm({});
      }
    } catch (error) {
      console.error('Error updating campaign:', error);
    }
  };

  const handleSaveCreativeEdit = async () => {
    if (!selectedCreative) return;

    try {
      const response = await fetch(`/api/admin/ai-marketing/ads/creatives/${selectedCreative.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editCreativeForm),
      });

      if (response.ok) {
        // Update local state
        setCreatives(prev => prev.map(c =>
          c.id === selectedCreative.id ? { ...c, ...editCreativeForm } : c
        ));
        setShowEditCreativeModal(false);
        setEditCreativeForm({});
      }
    } catch (error) {
      console.error('Error updating creative:', error);
    }
  };

  const handleRunOptimization = async () => {
    setIsOptimizing(true);
    setShowOptimizationModal(true);

    try {
      // API call to run AI optimization
      const response = await fetch('/api/admin/ai-marketing/ads/optimize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          campaignIds: campaigns.filter(c => c.status === 'active').map(c => c.id)
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Optimization completed:', result);
        // Refresh campaigns data
        await fetchAdsData();
      }
    } catch (error) {
      console.error('Error running optimization:', error);
    } finally {
      setIsOptimizing(false);
      setTimeout(() => setShowOptimizationModal(false), 2000);
    }
  };

  const handleCreateNewCampaign = async () => {
    try {
      const response = await fetch('/api/admin/ai-marketing/ads/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newCampaignForm,
          dailyBudget: Number(newCampaignForm.dailyBudget),
          totalBudget: Number(newCampaignForm.totalBudget),
          status: 'draft',
          aiManaged: true
        }),
      });

      if (response.ok) {
        const newCampaign = await response.json();
        setCampaigns(prev => [...prev, newCampaign]);
        setShowNewCampaignModal(false);
        setNewCampaignForm({
          name: '',
          platform: '',
          campaignType: '',
          dailyBudget: '',
          totalBudget: '',
          targetLocations: '',
          targetDemographics: '',
          targetInterests: ''
        });
      }
    } catch (error) {
      console.error('Error creating campaign:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <span className="text-lg">Reklam verileri yükleniyor...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reklam Yönetimi AI</h1>
          <p className="text-gray-600 mt-1">
            Google ve sosyal medya reklamlarınızı AI ile optimize edin
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handleRunOptimization}
            disabled={isOptimizing}
          >
            <Target className="w-4 h-4 mr-2" />
            {isOptimizing ? 'Optimizasyon Çalışıyor...' : 'Optimizasyon Çalıştır'}
          </Button>
          <Button onClick={() => setShowNewCampaignModal(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Yeni Kampanya
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Toplam Harcama</p>
                <p className="text-2xl font-bold">${totalSpent.toLocaleString()}</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Gösterim</p>
                <p className="text-2xl font-bold">{totalImpressions.toLocaleString()}</p>
              </div>
              <Eye className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Tıklama</p>
                <p className="text-2xl font-bold">{totalClicks.toLocaleString()}</p>
              </div>
              <MousePointer className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Dönüşüm</p>
                <p className="text-2xl font-bold">{totalConversions}</p>
              </div>
              <Target className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Ortalama ROAS</p>
                <p className="text-2xl font-bold">{avgROAS.toFixed(1)}x</p>
              </div>
              <TrendingUp className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Tabs */}
      <Tabs defaultValue="campaigns" className="space-y-4">
        <TabsList>
          <TabsTrigger value="campaigns">Kampanyalar</TabsTrigger>
          <TabsTrigger value="creatives">Kreatifler</TabsTrigger>
          <TabsTrigger value="analytics">Analitik</TabsTrigger>
          <TabsTrigger value="settings">AI Ayarları</TabsTrigger>
        </TabsList>

        <TabsContent value="campaigns" className="space-y-4">
          {/* Search */}
          <div className="flex items-center space-x-2">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Kampanya ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Campaigns Table */}
          <Card>
            <CardHeader>
              <CardTitle>Reklam Kampanyaları</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Kampanya</TableHead>
                    <TableHead>Platform</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead>Bütçe</TableHead>
                    <TableHead>Harcama</TableHead>
                    <TableHead>Performans</TableHead>
                    <TableHead>ROAS</TableHead>
                    <TableHead>AI Yönetimi</TableHead>
                    <TableHead>İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCampaigns.map((campaign) => (
                    <TableRow key={campaign.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{campaign.name}</p>
                          <p className="text-sm text-gray-500 capitalize">{campaign.campaignType}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{getPlatformIcon(campaign.platform)}</span>
                          <span>{getPlatformName(campaign.platform)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(campaign.status)}
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">${campaign.budget.total.toLocaleString()}</p>
                          <p className="text-sm text-gray-500">${campaign.budget.daily}/gün</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">${campaign.budget.spent.toLocaleString()}</p>
                          <div className="w-16 bg-gray-200 rounded-full h-2 mt-1">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${(campaign.budget.spent / campaign.budget.total) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-xs space-y-1">
                          <div>👁️ {campaign.performance.impressions.toLocaleString()}</div>
                          <div>👆 {campaign.performance.clicks.toLocaleString()}</div>
                          <div>🎯 {campaign.performance.conversions}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={`font-medium ${campaign.performance.roas >= 2 ? 'text-green-600' : 'text-red-600'}`}>
                          {campaign.performance.roas.toFixed(1)}x
                        </span>
                      </TableCell>
                      <TableCell>
                        {campaign.aiManaged ? (
                          <div className="flex items-center space-x-1">
                            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                            <span className="text-xs text-green-600">AI Aktif</span>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-1">
                            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                            <span className="text-xs text-gray-500">Manuel</span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewCampaign(campaign)}
                            title="Kampanya Detayları"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditCampaign(campaign)}
                            title="Kampanya Düzenle"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleCampaignStatus(campaign)}
                            title={campaign.status === 'active' ? 'Duraklat' : 'Başlat'}
                          >
                            {campaign.status === 'active' ? (
                              <Pause className="w-4 h-4" />
                            ) : (
                              <Play className="w-4 h-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleShowStats(campaign)}
                            title="Detaylı İstatistikler"
                          >
                            <BarChart3 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {/* Analytics Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Performance Trends */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Performans Trendleri</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Grafik gösterimi için Chart.js entegrasyonu gerekli</p>
                    <p className="text-sm text-gray-400">Son 30 günlük performans trendi</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Top Performing Campaigns */}
            <Card>
              <CardHeader>
                <CardTitle>En İyi Kampanyalar</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {campaigns
                    .sort((a, b) => b.performance.roas - a.performance.roas)
                    .slice(0, 3)
                    .map((campaign, index) => (
                      <div key={campaign.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center space-x-2">
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                            index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : 'bg-orange-500'
                          }`}>
                            {index + 1}
                          </div>
                          <div>
                            <p className="text-sm font-medium truncate max-w-32">{campaign.name}</p>
                            <p className="text-xs text-gray-500">{getPlatformName(campaign.platform)}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-bold text-green-600">{campaign.performance.roas.toFixed(1)}x</p>
                          <p className="text-xs text-gray-500">ROAS</p>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Platform Comparison */}
          <Card>
            <CardHeader>
              <CardTitle>Platform Karşılaştırması</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Platform</TableHead>
                      <TableHead>Kampanya Sayısı</TableHead>
                      <TableHead>Toplam Harcama</TableHead>
                      <TableHead>Gösterim</TableHead>
                      <TableHead>Tıklama</TableHead>
                      <TableHead>Dönüşüm</TableHead>
                      <TableHead>Ortalama ROAS</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(
                      campaigns.reduce((acc, campaign) => {
                        if (!acc[campaign.platform]) {
                          acc[campaign.platform] = {
                            count: 0,
                            spent: 0,
                            impressions: 0,
                            clicks: 0,
                            conversions: 0,
                            roas: 0
                          };
                        }
                        acc[campaign.platform].count++;
                        acc[campaign.platform].spent += campaign.budget.spent;
                        acc[campaign.platform].impressions += campaign.performance.impressions;
                        acc[campaign.platform].clicks += campaign.performance.clicks;
                        acc[campaign.platform].conversions += campaign.performance.conversions;
                        acc[campaign.platform].roas += campaign.performance.roas;
                        return acc;
                      }, {} as Record<string, any>)
                    ).map(([platform, stats]) => (
                      <TableRow key={platform}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{getPlatformIcon(platform)}</span>
                            <span>{getPlatformName(platform)}</span>
                          </div>
                        </TableCell>
                        <TableCell>{stats.count}</TableCell>
                        <TableCell>${stats.spent.toLocaleString()}</TableCell>
                        <TableCell>{stats.impressions.toLocaleString()}</TableCell>
                        <TableCell>{stats.clicks.toLocaleString()}</TableCell>
                        <TableCell>{stats.conversions}</TableCell>
                        <TableCell>
                          <span className={`font-medium ${(stats.roas / stats.count) >= 2 ? 'text-green-600' : 'text-red-600'}`}>
                            {(stats.roas / stats.count).toFixed(1)}x
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>

          {/* Creative Performance Analysis */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>En İyi Kreatifler</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {creatives
                    .sort((a, b) => b.performance.ctr - a.performance.ctr)
                    .slice(0, 5)
                    .map((creative, index) => (
                      <div key={creative.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <p className="font-medium text-sm truncate">{creative.headline}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className="text-xs">{creative.type}</Badge>
                            {creative.aiGenerated && (
                              <Badge variant="default" className="text-xs bg-blue-100 text-blue-800">AI</Badge>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-blue-600">{creative.performance.ctr.toFixed(2)}%</p>
                          <p className="text-xs text-gray-500">CTR</p>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Kreatif Tipi Analizi</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(
                    creatives.reduce((acc, creative) => {
                      if (!acc[creative.type]) {
                        acc[creative.type] = { count: 0, totalCTR: 0, totalImpressions: 0 };
                      }
                      acc[creative.type].count++;
                      acc[creative.type].totalCTR += creative.performance.ctr;
                      acc[creative.type].totalImpressions += creative.performance.impressions;
                      return acc;
                    }, {} as Record<string, any>)
                  ).map(([type, stats]) => (
                    <div key={type} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="capitalize font-medium">{type}</span>
                        <span className="text-sm text-gray-600">{stats.count} kreatif</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span>Ortalama CTR: {(stats.totalCTR / stats.count).toFixed(2)}%</span>
                        <span>Toplam Gösterim: {stats.totalImpressions.toLocaleString()}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${(stats.totalImpressions / creatives.reduce((sum, c) => sum + c.performance.impressions, 0)) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="creatives" className="space-y-4">
          {/* Creatives Table */}
          <Card>
            <CardHeader>
              <CardTitle>Reklam Kreatifler</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Başlık</TableHead>
                    <TableHead>Açıklama</TableHead>
                    <TableHead>Tip</TableHead>
                    <TableHead>Kampanya</TableHead>
                    <TableHead>Performans</TableHead>
                    <TableHead>AI Üretimi</TableHead>
                    <TableHead>İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {creatives.map((creative) => {
                    const campaign = campaigns.find(c => c.id === creative.campaignId);
                    return (
                      <TableRow key={creative.id}>
                        <TableCell className="font-medium">
                          {creative.headline}
                        </TableCell>
                        <TableCell className="max-w-xs">
                          <div className="truncate">
                            {creative.description}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="capitalize">
                            {creative.type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {campaign?.name}
                        </TableCell>
                        <TableCell>
                          <div className="text-xs space-y-1">
                            <div>👁️ {creative.performance.impressions.toLocaleString()}</div>
                            <div>👆 {creative.performance.clicks.toLocaleString()}</div>
                            <div>📊 {creative.performance.ctr.toFixed(2)}% CTR</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {creative.aiGenerated ? (
                            <Badge variant="default" className="bg-blue-100 text-blue-800">
                              AI Üretimi
                            </Badge>
                          ) : (
                            <Badge variant="secondary">Manuel</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewCreative(creative)}
                              title="Kreatif Detayları"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditCreative(creative)}
                              title="Kreatif Düzenle"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          {/* AI Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Optimization Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Optimizasyon Ayarları</CardTitle>
                <CardDescription>
                  AI optimizasyon parametrelerini yapılandırın
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="autoOptimization">Otomatik Optimizasyon</Label>
                  <Select defaultValue="enabled">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="enabled">Etkin</SelectItem>
                      <SelectItem value="disabled">Devre Dışı</SelectItem>
                      <SelectItem value="manual">Manuel Onay</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="optimizationFrequency">Optimizasyon Sıklığı</Label>
                  <Select defaultValue="daily">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">Saatlik</SelectItem>
                      <SelectItem value="daily">Günlük</SelectItem>
                      <SelectItem value="weekly">Haftalık</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="bidStrategy">Teklif Stratejisi</Label>
                  <Select defaultValue="maximize_conversions">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="maximize_conversions">Dönüşümleri Maksimize Et</SelectItem>
                      <SelectItem value="target_cpa">Hedef CPA</SelectItem>
                      <SelectItem value="target_roas">Hedef ROAS</SelectItem>
                      <SelectItem value="maximize_clicks">Tıklamaları Maksimize Et</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="targetROAS">Hedef ROAS</Label>
                  <Input
                    id="targetROAS"
                    type="number"
                    step="0.1"
                    defaultValue="3.0"
                    placeholder="3.0"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Platform Integrations */}
            <Card>
              <CardHeader>
                <CardTitle>Platform Entegrasyonları</CardTitle>
                <CardDescription>
                  Reklam platformu API ayarları
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="googleAdsAPI">Google Ads API</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="googleAdsAPI"
                      type="password"
                      defaultValue="••••••••••••••••"
                      placeholder="API Anahtarı"
                    />
                    <Button variant="outline" size="sm">Test</Button>
                  </div>
                </div>

                <div>
                  <Label htmlFor="facebookAPI">Facebook Ads API</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="facebookAPI"
                      type="password"
                      defaultValue="••••••••••••••••"
                      placeholder="Access Token"
                    />
                    <Button variant="outline" size="sm">Test</Button>
                  </div>
                </div>

                <div>
                  <Label htmlFor="linkedinAPI">LinkedIn Ads API</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="linkedinAPI"
                      type="password"
                      defaultValue="••••••••••••••••"
                      placeholder="API Anahtarı"
                    />
                    <Button variant="outline" size="sm">Test</Button>
                  </div>
                </div>

                <div className="pt-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Bağlantı Durumu:</span>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span className="text-green-600">Tüm platformlar bağlı</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Creative AI Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Kreatif AI Ayarları</CardTitle>
                <CardDescription>
                  Otomatik kreatif üretimi ve test ayarları
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="autoCreativeGeneration">Otomatik Kreatif Üretimi</Label>
                  <Select defaultValue="enabled">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="enabled">Etkin</SelectItem>
                      <SelectItem value="disabled">Devre Dışı</SelectItem>
                      <SelectItem value="approval_required">Onay Gerekli</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="creativesPerCampaign">Kampanya Başına Kreatif Sayısı</Label>
                  <Input
                    id="creativesPerCampaign"
                    type="number"
                    min="1"
                    max="10"
                    defaultValue="3"
                  />
                </div>

                <div>
                  <Label htmlFor="abTestDuration">A/B Test Süresi (gün)</Label>
                  <Input
                    id="abTestDuration"
                    type="number"
                    min="1"
                    max="30"
                    defaultValue="7"
                  />
                </div>

                <div>
                  <Label htmlFor="contentTone">İçerik Tonu</Label>
                  <Select defaultValue="professional">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="professional">Profesyonel</SelectItem>
                      <SelectItem value="friendly">Samimi</SelectItem>
                      <SelectItem value="urgent">Acil</SelectItem>
                      <SelectItem value="luxury">Lüks</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Budget Management */}
            <Card>
              <CardHeader>
                <CardTitle>Bütçe Yönetimi</CardTitle>
                <CardDescription>
                  Otomatik bütçe dağıtımı ve limit ayarları
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="autoBudgetReallocation">Otomatik Bütçe Dağıtımı</Label>
                  <Select defaultValue="enabled">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="enabled">Etkin</SelectItem>
                      <SelectItem value="disabled">Devre Dışı</SelectItem>
                      <SelectItem value="conservative">Konservatif</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="maxDailySpend">Maksimum Günlük Harcama ($)</Label>
                  <Input
                    id="maxDailySpend"
                    type="number"
                    min="0"
                    defaultValue="1000"
                  />
                </div>

                <div>
                  <Label htmlFor="emergencyStop">Acil Durdurma Limiti ($)</Label>
                  <Input
                    id="emergencyStop"
                    type="number"
                    min="0"
                    defaultValue="5000"
                  />
                </div>

                <div>
                  <Label htmlFor="performanceThreshold">Performans Eşiği (ROAS)</Label>
                  <Input
                    id="performanceThreshold"
                    type="number"
                    step="0.1"
                    min="0"
                    defaultValue="1.5"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Notification Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Bildirim Ayarları</CardTitle>
              <CardDescription>
                Performans uyarıları ve rapor ayarları
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Performans Uyarıları</h4>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="lowPerformanceAlert">Düşük Performans Uyarısı</Label>
                    <input type="checkbox" id="lowPerformanceAlert" defaultChecked className="rounded" />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="budgetAlert">Bütçe Uyarısı</Label>
                    <input type="checkbox" id="budgetAlert" defaultChecked className="rounded" />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="highCPAAlert">Yüksek CPA Uyarısı</Label>
                    <input type="checkbox" id="highCPAAlert" defaultChecked className="rounded" />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="campaignEndAlert">Kampanya Bitiş Uyarısı</Label>
                    <input type="checkbox" id="campaignEndAlert" defaultChecked className="rounded" />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Rapor Ayarları</h4>

                  <div>
                    <Label htmlFor="reportFrequency">Rapor Sıklığı</Label>
                    <Select defaultValue="weekly">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Günlük</SelectItem>
                        <SelectItem value="weekly">Haftalık</SelectItem>
                        <SelectItem value="monthly">Aylık</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="reportEmail">Rapor Email</Label>
                    <Input
                      id="reportEmail"
                      type="email"
                      defaultValue="<EMAIL>"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="includeRecommendations">AI Önerileri Dahil Et</Label>
                    <input type="checkbox" id="includeRecommendations" defaultChecked className="rounded" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Save Settings */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline">
              Varsayılana Sıfırla
            </Button>
            <Button>
              <Save className="w-4 h-4 mr-2" />
              Ayarları Kaydet
            </Button>
          </div>
        </TabsContent>
      </Tabs>

      {/* Campaign Detail Modal */}
      <Dialog open={showCampaignModal} onOpenChange={setShowCampaignModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Kampanya Detayları</DialogTitle>
            <DialogDescription>
              {selectedCampaign?.name} kampanyasının detaylı bilgileri
            </DialogDescription>
          </DialogHeader>
          {selectedCampaign && (
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Kampanya Adı</Label>
                  <p className="text-sm text-gray-600">{selectedCampaign.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Platform</Label>
                  <p className="text-sm text-gray-600">{getPlatformName(selectedCampaign.platform)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Kampanya Tipi</Label>
                  <p className="text-sm text-gray-600 capitalize">{selectedCampaign.campaignType}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Durum</Label>
                  <div className="mt-1">{getStatusBadge(selectedCampaign.status)}</div>
                </div>
              </div>

              {/* Budget Info */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Bütçe Bilgileri</Label>
                <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-xs text-gray-500">Toplam Bütçe</p>
                    <p className="font-medium">${selectedCampaign.budget.total.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Günlük Bütçe</p>
                    <p className="font-medium">${selectedCampaign.budget.daily.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Harcanan</p>
                    <p className="font-medium">${selectedCampaign.budget.spent.toLocaleString()}</p>
                  </div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Performans Metrikleri</Label>
                <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-xs text-gray-500">Gösterim</p>
                    <p className="font-medium">{selectedCampaign.performance.impressions.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Tıklama</p>
                    <p className="font-medium">{selectedCampaign.performance.clicks.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Dönüşüm</p>
                    <p className="font-medium">{selectedCampaign.performance.conversions}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">ROAS</p>
                    <p className="font-medium">{selectedCampaign.performance.roas.toFixed(1)}x</p>
                  </div>
                </div>
              </div>

              {/* Target Audience */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Hedef Kitle</Label>
                <div className="p-4 bg-gray-50 rounded-lg space-y-2">
                  <div>
                    <p className="text-xs text-gray-500">Lokasyonlar</p>
                    <p className="text-sm">{selectedCampaign.targetAudience.locations.join(', ')}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Demografik</p>
                    <p className="text-sm">{selectedCampaign.targetAudience.demographics}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">İlgi Alanları</p>
                    <p className="text-sm">{selectedCampaign.targetAudience.interests.join(', ')}</p>
                  </div>
                </div>
              </div>

              {/* Dates */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Başlangıç Tarihi</Label>
                  <p className="text-sm text-gray-600">{selectedCampaign.startDate.toLocaleDateString('tr-TR')}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Bitiş Tarihi</Label>
                  <p className="text-sm text-gray-600">
                    {selectedCampaign.endDate ? selectedCampaign.endDate.toLocaleDateString('tr-TR') : 'Belirsiz'}
                  </p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Campaign Edit Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Kampanya Düzenle</DialogTitle>
            <DialogDescription>
              Kampanya ayarlarını güncelleyin
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="campaignName">Kampanya Adı</Label>
              <Input
                id="campaignName"
                value={editForm.name || ''}
                onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="dailyBudget">Günlük Bütçe ($)</Label>
                <Input
                  id="dailyBudget"
                  type="number"
                  value={editForm.budget?.daily || ''}
                  onChange={(e) => setEditForm(prev => ({
                    ...prev,
                    budget: { ...prev.budget!, daily: Number(e.target.value) }
                  }))}
                />
              </div>
              <div>
                <Label htmlFor="totalBudget">Toplam Bütçe ($)</Label>
                <Input
                  id="totalBudget"
                  type="number"
                  value={editForm.budget?.total || ''}
                  onChange={(e) => setEditForm(prev => ({
                    ...prev,
                    budget: { ...prev.budget!, total: Number(e.target.value) }
                  }))}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="campaignStatus">Kampanya Durumu</Label>
              <Select
                value={editForm.status || ''}
                onValueChange={(value) => setEditForm(prev => ({ ...prev, status: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Durum seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Aktif</SelectItem>
                  <SelectItem value="paused">Duraklatıldı</SelectItem>
                  <SelectItem value="ended">Sona Erdi</SelectItem>
                  <SelectItem value="draft">Taslak</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowEditModal(false)}>
                İptal
              </Button>
              <Button onClick={handleSaveEdit}>
                <Save className="w-4 h-4 mr-2" />
                Kaydet
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Campaign Statistics Modal */}
      <Dialog open={showStatsModal} onOpenChange={setShowStatsModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Detaylı İstatistikler</DialogTitle>
            <DialogDescription>
              {selectedCampaign?.name} kampanyasının performans analizi
            </DialogDescription>
          </DialogHeader>
          {selectedCampaign && (
            <div className="space-y-6">
              {/* Performance Overview */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-blue-600">
                        {((selectedCampaign.performance.clicks / selectedCampaign.performance.impressions) * 100).toFixed(2)}%
                      </p>
                      <p className="text-sm text-gray-600">CTR (Tıklama Oranı)</p>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">
                        ${selectedCampaign.performance.cpc.toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-600">CPC (Tıklama Maliyeti)</p>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-purple-600">
                        ${selectedCampaign.performance.cpa.toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-600">CPA (Dönüşüm Maliyeti)</p>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-orange-600">
                        {((selectedCampaign.performance.conversions / selectedCampaign.performance.clicks) * 100).toFixed(2)}%
                      </p>
                      <p className="text-sm text-gray-600">Dönüşüm Oranı</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Budget Analysis */}
              <Card>
                <CardHeader>
                  <CardTitle>Bütçe Analizi</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Harcanan Bütçe</span>
                      <span className="font-medium">${selectedCampaign.budget.spent.toLocaleString()}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-blue-600 h-3 rounded-full"
                        style={{ width: `${(selectedCampaign.budget.spent / selectedCampaign.budget.total) * 100}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>Kalan: ${(selectedCampaign.budget.total - selectedCampaign.budget.spent).toLocaleString()}</span>
                      <span>Toplam: ${selectedCampaign.budget.total.toLocaleString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* AI Management Status */}
              {selectedCampaign.aiManaged && (
                <Card>
                  <CardHeader>
                    <CardTitle>AI Yönetimi</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                      <span className="text-green-600 font-medium">AI Optimizasyonu Aktif</span>
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Son Optimizasyon: {selectedCampaign.lastOptimized?.toLocaleDateString('tr-TR')}</p>
                      <p>Otomatik teklif optimizasyonu ve hedef kitle ayarlaması yapılıyor.</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Creative Detail Modal */}
      <Dialog open={showCreativeModal} onOpenChange={setShowCreativeModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Kreatif Detayları</DialogTitle>
            <DialogDescription>
              Reklam kreatifinin detaylı bilgileri
            </DialogDescription>
          </DialogHeader>
          {selectedCreative && (
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Başlık</Label>
                <p className="text-sm text-gray-600">{selectedCreative.headline}</p>
              </div>

              <div>
                <Label className="text-sm font-medium">Açıklama</Label>
                <p className="text-sm text-gray-600">{selectedCreative.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Tip</Label>
                  <Badge variant="outline" className="capitalize">{selectedCreative.type}</Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium">AI Üretimi</Label>
                  {selectedCreative.aiGenerated ? (
                    <Badge variant="default" className="bg-blue-100 text-blue-800">AI Üretimi</Badge>
                  ) : (
                    <Badge variant="secondary">Manuel</Badge>
                  )}
                </div>
              </div>

              {selectedCreative.imageUrl && (
                <div>
                  <Label className="text-sm font-medium">Görsel</Label>
                  <div className="mt-2 border rounded-lg p-2">
                    <img
                      src={selectedCreative.imageUrl}
                      alt={selectedCreative.headline}
                      className="w-full h-48 object-cover rounded"
                    />
                  </div>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium mb-2 block">Performans</Label>
                <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-xs text-gray-500">Gösterim</p>
                    <p className="font-medium">{selectedCreative.performance.impressions.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Tıklama</p>
                    <p className="font-medium">{selectedCreative.performance.clicks.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">CTR</p>
                    <p className="font-medium">{selectedCreative.performance.ctr.toFixed(2)}%</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Creative Edit Modal */}
      <Dialog open={showEditCreativeModal} onOpenChange={setShowEditCreativeModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Kreatif Düzenle</DialogTitle>
            <DialogDescription>
              Reklam kreatifini güncelleyin
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="creativeHeadline">Başlık</Label>
              <Input
                id="creativeHeadline"
                value={editCreativeForm.headline || ''}
                onChange={(e) => setEditCreativeForm(prev => ({ ...prev, headline: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="creativeDescription">Açıklama</Label>
              <Textarea
                id="creativeDescription"
                value={editCreativeForm.description || ''}
                onChange={(e) => setEditCreativeForm(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="creativeType">Kreatif Tipi</Label>
              <Select
                value={editCreativeForm.type || ''}
                onValueChange={(value) => setEditCreativeForm(prev => ({ ...prev, type: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tip seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text">Metin</SelectItem>
                  <SelectItem value="image">Görsel</SelectItem>
                  <SelectItem value="video">Video</SelectItem>
                  <SelectItem value="carousel">Carousel</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {editCreativeForm.type === 'image' && (
              <div>
                <Label htmlFor="creativeImageUrl">Görsel URL</Label>
                <Input
                  id="creativeImageUrl"
                  value={editCreativeForm.imageUrl || ''}
                  onChange={(e) => setEditCreativeForm(prev => ({ ...prev, imageUrl: e.target.value }))}
                  placeholder="https://example.com/image.jpg"
                />
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowEditCreativeModal(false)}>
                İptal
              </Button>
              <Button onClick={handleSaveCreativeEdit}>
                <Save className="w-4 h-4 mr-2" />
                Kaydet
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* New Campaign Modal */}
      <Dialog open={showNewCampaignModal} onOpenChange={setShowNewCampaignModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Yeni Kampanya Oluştur</DialogTitle>
            <DialogDescription>
              AI destekli yeni reklam kampanyası oluşturun
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="newCampaignName">Kampanya Adı</Label>
              <Input
                id="newCampaignName"
                value={newCampaignForm.name}
                onChange={(e) => setNewCampaignForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Örn: Traverten Ürünleri - Avrupa"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="newCampaignPlatform">Platform</Label>
                <Select
                  value={newCampaignForm.platform}
                  onValueChange={(value) => setNewCampaignForm(prev => ({ ...prev, platform: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Platform seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="google_ads">Google Ads</SelectItem>
                    <SelectItem value="facebook_ads">Facebook Ads</SelectItem>
                    <SelectItem value="linkedin_ads">LinkedIn Ads</SelectItem>
                    <SelectItem value="twitter_ads">Twitter Ads</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="newCampaignType">Kampanya Tipi</Label>
                <Select
                  value={newCampaignForm.campaignType}
                  onValueChange={(value) => setNewCampaignForm(prev => ({ ...prev, campaignType: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tip seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="search">Arama</SelectItem>
                    <SelectItem value="display">Display</SelectItem>
                    <SelectItem value="video">Video</SelectItem>
                    <SelectItem value="shopping">Shopping</SelectItem>
                    <SelectItem value="social">Sosyal Medya</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="newCampaignDailyBudget">Günlük Bütçe ($)</Label>
                <Input
                  id="newCampaignDailyBudget"
                  type="number"
                  value={newCampaignForm.dailyBudget}
                  onChange={(e) => setNewCampaignForm(prev => ({ ...prev, dailyBudget: e.target.value }))}
                  placeholder="100"
                />
              </div>

              <div>
                <Label htmlFor="newCampaignTotalBudget">Toplam Bütçe ($)</Label>
                <Input
                  id="newCampaignTotalBudget"
                  type="number"
                  value={newCampaignForm.totalBudget}
                  onChange={(e) => setNewCampaignForm(prev => ({ ...prev, totalBudget: e.target.value }))}
                  placeholder="3000"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="newCampaignLocations">Hedef Lokasyonlar</Label>
              <Input
                id="newCampaignLocations"
                value={newCampaignForm.targetLocations}
                onChange={(e) => setNewCampaignForm(prev => ({ ...prev, targetLocations: e.target.value }))}
                placeholder="US, DE, IT (virgülle ayırın)"
              />
            </div>

            <div>
              <Label htmlFor="newCampaignDemographics">Hedef Demografik</Label>
              <Input
                id="newCampaignDemographics"
                value={newCampaignForm.targetDemographics}
                onChange={(e) => setNewCampaignForm(prev => ({ ...prev, targetDemographics: e.target.value }))}
                placeholder="25-65, Tüm cinsiyetler"
              />
            </div>

            <div>
              <Label htmlFor="newCampaignInterests">İlgi Alanları</Label>
              <Input
                id="newCampaignInterests"
                value={newCampaignForm.targetInterests}
                onChange={(e) => setNewCampaignForm(prev => ({ ...prev, targetInterests: e.target.value }))}
                placeholder="İnşaat, Mimarlık, Ev Dekorasyonu (virgülle ayırın)"
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowNewCampaignModal(false)}>
                İptal
              </Button>
              <Button onClick={handleCreateNewCampaign}>
                <Plus className="w-4 h-4 mr-2" />
                Kampanya Oluştur
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Optimization Modal */}
      <Dialog open={showOptimizationModal} onOpenChange={setShowOptimizationModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>AI Optimizasyonu</DialogTitle>
            <DialogDescription>
              Kampanyalarınız AI tarafından optimize ediliyor
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {isOptimizing ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-lg font-medium">Optimizasyon çalışıyor...</p>
                <p className="text-sm text-gray-600 mt-2">
                  AI teklif stratejilerini, hedef kitleyi ve bütçe dağıtımını analiz ediyor
                </p>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Target className="w-6 h-6 text-green-600" />
                </div>
                <p className="text-lg font-medium text-green-600">Optimizasyon Tamamlandı!</p>
                <p className="text-sm text-gray-600 mt-2">
                  Kampanyalarınız başarıyla optimize edildi
                </p>
              </div>
            )}

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Aktif Kampanyalar:</span>
                <span className="font-medium">{campaigns.filter(c => c.status === 'active').length}</span>
              </div>
              <div className="flex justify-between">
                <span>Optimize Edilen:</span>
                <span className="font-medium">{isOptimizing ? '...' : campaigns.filter(c => c.status === 'active').length}</span>
              </div>
              <div className="flex justify-between">
                <span>Tahmini İyileştirme:</span>
                <span className="font-medium text-green-600">{isOptimizing ? '...' : '+15-25% ROAS'}</span>
              </div>
            </div>

            {!isOptimizing && (
              <div className="flex justify-end">
                <Button onClick={() => setShowOptimizationModal(false)}>
                  Tamam
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
