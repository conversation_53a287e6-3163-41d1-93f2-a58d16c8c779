'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import CustomerAnalyticsView from '@/components/admin/CustomerAnalyticsView'
import { 
  ArrowLeft,
  Building2,
  MapPin,
  Package,
  Users,
  TrendingUp,
  DollarSign,
  Clock,
  Star,
  Phone,
  Mail,
  Globe,
  ShoppingCart,
  Target,
  Calendar,
  Eye,
  MessageSquare,
  FileText
} from 'lucide-react'

interface CustomerDetailPageProps {
  params: {
    id: string
  }
}

export default function AdminCustomerDetailPage({ params }: CustomerDetailPageProps) {
  const router = useRouter()
  const [selectedTab, setSelectedTab] = React.useState('overview')
  const [selectedQuoteId, setSelectedQuoteId] = React.useState<string | null>(null)
  const [showQuoteDetailsModal, setShowQuoteDetailsModal] = React.useState(false)

  const handleShowQuoteDetails = (quoteId: string) => {
    setSelectedQuoteId(quoteId)
    setShowQuoteDetailsModal(true)
  }

  const getSelectedQuote = () => {
    return mockQuotes.find(quote => quote.id === selectedQuoteId)
  }

  const getQuoteStatusColor = (status: string) => {
    switch (status) {
      case 'Bekliyor':
        return 'bg-yellow-100 text-yellow-800'
      case 'Kabul Edildi':
        return 'bg-green-100 text-green-800'
      case 'Reddedildi':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }



  const handleViewQuoteDetails = (quoteId: string, producerQuoteId: string) => {
    // Navigate to detailed quote view page
    window.open(`/admin/quotes/${quoteId}/details/${producerQuoteId}`, '_blank')
  }

  // Mock data - gerçek uygulamada API'den gelecek
  const mockCustomer = {
    id: params.id,
    name: 'İnşaat A.Ş.',
    sector: 'İnşaat',
    location: {
      city: 'İstanbul',
      district: 'Şişli',
      address: 'Şişli Plaza, Kat: 15, No: 1501'
    },
    contact: {
      phone: '+90 212 555 01 23',
      email: '<EMAIL>',
      website: 'www.insaat.com',
      person: 'Ahmet Yılmaz',
      title: 'Satın Alma Müdürü'
    },
    companyInfo: {
      establishedYear: 2015,
      employeeCount: 250,
      taxNumber: '1234567890',
      tradeRegister: 'İstanbul Ticaret Sicil No: 54321'
    },
    stats: {
      totalOrders: 25,
      totalValue: 2850000,
      avgOrderValue: 114000,
      quotesRequested: 45,
      conversionRate: 55.6,
      lastActivity: '2 gün önce',
      memberSince: '2023-03-15',
      favoriteProducts: 8,
      activeQuotes: 3
    }
  }

  const mockOrders = [
    {
      id: 'ORD-001',
      productName: 'Afyon Beyaz Mermer',
      producerName: 'Afyon Doğal Taş A.Ş.',
      dimension: '60x120x2cm',
      quantity: 150,
      unitPrice: 115,
      totalPrice: 17250,
      currency: 'USD',
      orderDate: '2025-06-28',
      deliveryDate: '2025-07-15',
      status: 'Teslim Edildi'
    },
    {
      id: 'ORD-002',
      productName: 'Traverten Bej',
      producerName: 'Denizli Taş Ltd.',
      dimension: '40x80x2cm',
      quantity: 200,
      unitPrice: 85,
      totalPrice: 17000,
      currency: 'USD',
      orderDate: '2025-06-25',
      deliveryDate: '2025-07-10',
      status: 'Kargoda'
    },
    {
      id: 'ORD-003',
      productName: 'Granit Siyah',
      producerName: 'Premium Stone Co.',
      dimension: '80x160x3cm',
      quantity: 120,
      unitPrice: 95,
      totalPrice: 11400,
      currency: 'USD',
      orderDate: '2025-06-20',
      deliveryDate: '2025-07-05',
      status: 'Teslim Edildi'
    }
  ]

  const mockQuotes = [
    {
      id: 'QUO-001',
      productName: 'Oniks Yeşil',
      dimension: '60x120x2cm',
      quantity: 100,
      requestDate: '2025-06-30',
      quotesReceived: 3,
      bestPrice: 120,
      avgPrice: 125,
      status: 'Değerlendiriliyor',
      receivedQuotes: [
        {
          id: 'Q1',
          producerName: 'Afyon Doğal Taş A.Ş.',
          producerLocation: 'Afyon',
          price: 120.00,
          currency: 'USD',
          deliveryTime: '15 gün',
          validUntil: '2025-07-25',
          notes: 'Yüksek kalite, A+ sınıf oniks',
          submittedDate: '2025-07-01',
          status: 'Bekliyor'
        },
        {
          id: 'Q2',
          producerName: 'Ege Mermer Ltd.',
          producerLocation: 'İzmir',
          price: 125.00,
          currency: 'USD',
          deliveryTime: '20 gün',
          validUntil: '2025-07-30',
          notes: 'Özel kesim, kaliteli işçilik',
          submittedDate: '2025-07-02',
          status: 'Bekliyor'
        },
        {
          id: 'Q3',
          producerName: 'Marmara Taş San.',
          producerLocation: 'Bursa',
          price: 130.00,
          currency: 'USD',
          deliveryTime: '10 gün',
          validUntil: '2025-07-20',
          notes: 'Hızlı teslimat, premium kalite',
          submittedDate: '2025-07-03',
          status: 'Bekliyor'
        }
      ]
    },
    {
      id: 'QUO-002',
      productName: 'Mermer Beyaz',
      dimension: '100x200x3cm',
      quantity: 80,
      requestDate: '2025-06-28',
      quotesReceived: 2,
      bestPrice: 140,
      avgPrice: 145,
      status: 'Teklif Bekleniyor',
      receivedQuotes: [
        {
          id: 'Q4',
          producerName: 'Denizli Mermer A.Ş.',
          producerLocation: 'Denizli',
          price: 140.00,
          currency: 'USD',
          deliveryTime: '25 gün',
          validUntil: '2025-07-28',
          notes: 'Birinci sınıf beyaz mermer',
          submittedDate: '2025-06-29',
          status: 'Bekliyor'
        },
        {
          id: 'Q5',
          producerName: 'Anadolu Mermer Ltd.',
          producerLocation: 'Ankara',
          price: 150.00,
          currency: 'USD',
          deliveryTime: '30 gün',
          validUntil: '2025-08-01',
          notes: 'Doğal beyaz mermer, damar desenli',
          submittedDate: '2025-06-30',
          status: 'Bekliyor'
        }
      ]
    }
  ]

  const mockProducers = [
    {
      name: 'Afyon Doğal Taş A.Ş.',
      orderCount: 8,
      totalValue: 920000,
      avgPrice: 115,
      lastOrder: '2025-06-28'
    },
    {
      name: 'Premium Stone Co.',
      orderCount: 6,
      totalValue: 680000,
      avgPrice: 113,
      lastOrder: '2025-06-20'
    },
    {
      name: 'Denizli Taş Ltd.',
      orderCount: 5,
      totalValue: 425000,
      avgPrice: 85,
      lastOrder: '2025-06-25'
    }
  ]

  if (!mockCustomer) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Müşteri bulunamadı</h3>
          <p className="text-gray-600 mb-4">Aradığınız müşteri mevcut değil.</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Geri Dön
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Geri Dön
          </Button>
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
              <Building2 className="w-8 h-8 text-gray-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{mockCustomer.name}</h1>
              <p className="text-gray-600 mt-1">
                {mockCustomer.sector} • {mockCustomer.location.city} • Üye: {new Date(mockCustomer.stats.memberSince).toLocaleDateString('tr-TR')}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="text-right">
            <p className="text-sm text-gray-600">Toplam Sipariş Değeri</p>
            <p className="text-2xl font-bold text-green-600">₺{(mockCustomer.stats.totalValue / 1000).toFixed(0)}K</p>
          </div>
          <Button variant="outline">
            <MessageSquare className="w-4 h-4 mr-2" />
            Mesaj Gönder
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <ShoppingCart className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Sipariş</p>
              <p className="text-xl font-bold text-gray-900">{mockCustomer.stats.totalOrders}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <DollarSign className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Ort. Sipariş</p>
              <p className="text-xl font-bold text-gray-900">₺{(mockCustomer.stats.avgOrderValue / 1000).toFixed(0)}K</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 rounded-lg">
              <Target className="w-5 h-5 text-amber-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Dönüşüm</p>
              <p className="text-xl font-bold text-gray-900">{mockCustomer.stats.conversionRate}%</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <FileText className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Aktif Teklif</p>
              <p className="text-xl font-bold text-gray-900">{mockCustomer.stats.activeQuotes}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <Clock className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Son Aktivite</p>
              <p className="text-xl font-bold text-gray-900">{mockCustomer.stats.lastActivity}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Genel Bakış', icon: Eye },
            { id: 'orders', label: 'Siparişler', icon: ShoppingCart },
            { id: 'quotes', label: 'Teklifler', icon: FileText },
            { id: 'producers', label: 'Üreticiler', icon: Building2 },
            { id: 'analytics', label: 'Analitik', icon: TrendingUp }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === tab.id
                    ? 'border-amber-500 text-amber-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {selectedTab === 'overview' && (
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            {/* Company Info */}
            <div className="xl:col-span-2 space-y-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Firma Bilgileri</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Sektör</label>
                    <p className="text-gray-900">{mockCustomer.sector}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Kuruluş Yılı</label>
                    <p className="text-gray-900">{mockCustomer.companyInfo.establishedYear}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Çalışan Sayısı</label>
                    <p className="text-gray-900">{mockCustomer.companyInfo.employeeCount} kişi</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Şehir</label>
                    <p className="text-gray-900">{mockCustomer.location.city}</p>
                  </div>
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-gray-600">Adres</label>
                    <p className="text-gray-900">{mockCustomer.location.address}</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Son Siparişler</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">Ürün</th>
                        <th className="text-left py-2">Ebat</th>
                        <th className="text-left py-2">Miktar</th>
                        <th className="text-left py-2">Fiyat</th>
                        <th className="text-left py-2">Tarih</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockOrders.slice(0, 3).map((order) => (
                        <tr key={order.id} className="border-b">
                          <td className="py-2 font-medium">{order.productName}</td>
                          <td className="py-2">{order.dimension}</td>
                          <td className="py-2">{order.quantity} m²</td>
                          <td className="py-2">${order.unitPrice}</td>
                          <td className="py-2">{new Date(order.orderDate).toLocaleDateString('tr-TR')}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </Card>
            </div>

            {/* Side Info */}
            <div className="space-y-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">İletişim</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600">İletişim Kişisi</p>
                    <p className="font-medium">{mockCustomer.contact.person}</p>
                    <p className="text-sm text-gray-600">{mockCustomer.contact.title}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-gray-500" />
                    <span className="text-sm">{mockCustomer.contact.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-gray-500" />
                    <span className="text-sm">{mockCustomer.contact.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Globe className="w-4 h-4 text-gray-500" />
                    <span className="text-sm">{mockCustomer.contact.website}</span>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Performans Özeti</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Teklif Talep Sayısı</span>
                    <span className="font-medium">{mockCustomer.stats.quotesRequested}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Favori Ürün</span>
                    <span className="font-medium">{mockCustomer.stats.favoriteProducts}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Üyelik Süresi</span>
                    <span className="font-medium">
                      {Math.floor((new Date().getTime() - new Date(mockCustomer.stats.memberSince).getTime()) / (1000 * 60 * 60 * 24 * 30))} ay
                    </span>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}

        {selectedTab === 'orders' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Sipariş Geçmişi ({mockOrders.length})</h3>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Sipariş No</th>
                    <th className="text-left py-3 px-4">Ürün</th>
                    <th className="text-left py-3 px-4">Üretici</th>
                    <th className="text-left py-3 px-4">Ebat</th>
                    <th className="text-left py-3 px-4">Miktar</th>
                    <th className="text-left py-3 px-4">Birim Fiyat</th>
                    <th className="text-left py-3 px-4">Toplam</th>
                    <th className="text-left py-3 px-4">Tarih</th>
                    <th className="text-left py-3 px-4">Durum</th>
                  </tr>
                </thead>
                <tbody>
                  {mockOrders.map((order) => (
                    <tr key={order.id} className="border-b">
                      <td className="py-3 px-4 font-medium">{order.id}</td>
                      <td className="py-3 px-4">{order.productName}</td>
                      <td className="py-3 px-4">
                        <button className="text-blue-600 hover:underline">
                          {order.producerName}
                        </button>
                      </td>
                      <td className="py-3 px-4">{order.dimension}</td>
                      <td className="py-3 px-4">{order.quantity} m²</td>
                      <td className="py-3 px-4">${order.unitPrice}</td>
                      <td className="py-3 px-4 font-medium">${order.totalPrice.toLocaleString()}</td>
                      <td className="py-3 px-4">{new Date(order.orderDate).toLocaleDateString('tr-TR')}</td>
                      <td className="py-3 px-4">
                        <Badge variant={order.status === 'Teslim Edildi' ? 'default' : 'secondary'}>
                          {order.status}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {selectedTab === 'quotes' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Aktif Teklifler ({mockQuotes.length})</h3>
            
            <div className="grid grid-cols-1 gap-4">
              {mockQuotes.map((quote) => (
                <Card key={quote.id} className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold">{quote.productName}</h4>
                    <Badge variant="outline">{quote.status}</Badge>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Ebat</p>
                      <p className="font-medium">{quote.dimension}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Miktar</p>
                      <p className="font-medium">{quote.quantity} m²</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">En İyi Fiyat</p>
                      <p className="font-medium text-green-600">${quote.bestPrice}</p>
                    </div>
                    <div
                      className="cursor-pointer hover:bg-blue-50 p-2 rounded transition-colors"
                      onClick={() => handleShowQuoteDetails(quote.id)}
                    >
                      <p className="text-sm text-gray-600">Gelen Teklif</p>
                      <p className="font-medium text-blue-600 hover:text-blue-800">
                        {quote.quotesReceived} adet →
                      </p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {selectedTab === 'producers' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Çalıştığı Üreticiler ({mockProducers.length})</h3>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Üretici</th>
                    <th className="text-left py-3 px-4">Sipariş Sayısı</th>
                    <th className="text-left py-3 px-4">Toplam Değer</th>
                    <th className="text-left py-3 px-4">Ort. Fiyat</th>
                    <th className="text-left py-3 px-4">Son Sipariş</th>
                  </tr>
                </thead>
                <tbody>
                  {mockProducers.map((producer, index) => (
                    <tr key={index} className="border-b">
                      <td className="py-3 px-4">
                        <button className="text-blue-600 hover:underline font-medium">
                          {producer.name}
                        </button>
                      </td>
                      <td className="py-3 px-4">{producer.orderCount}</td>
                      <td className="py-3 px-4">₺{producer.totalValue.toLocaleString()}</td>
                      <td className="py-3 px-4">${producer.avgPrice}</td>
                      <td className="py-3 px-4">{new Date(producer.lastOrder).toLocaleDateString('tr-TR')}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {selectedTab === 'analytics' && (
          <CustomerAnalyticsView customerId={params.id} />
        )}
      </div>

      {/* Quote Details Modal */}
      {showQuoteDetailsModal && selectedQuoteId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-bold text-gray-900">
                    Teklif Detayları - {getSelectedQuote()?.id}
                  </h2>
                  <p className="text-gray-600 mt-1">
                    <a
                      href="/products"
                      target="_blank"
                      className="text-blue-600 hover:text-blue-800 hover:underline font-medium"
                    >
                      {getSelectedQuote()?.productName}
                    </a>
                    {' '} - {getSelectedQuote()?.dimension}
                  </p>
                </div>
                <button
                  onClick={() => setShowQuoteDetailsModal(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                  ×
                </button>
              </div>

              {/* Quote Request Info */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-gray-900 mb-3">Teklif Talebi Bilgileri</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Ürün</p>
                    <p className="font-medium">{getSelectedQuote()?.productName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Ebat</p>
                    <p className="font-medium">{getSelectedQuote()?.dimension}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Miktar</p>
                    <p className="font-medium">{getSelectedQuote()?.quantity} m²</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Talep Tarihi</p>
                    <p className="font-medium">
                      {getSelectedQuote()?.requestDate && new Date(getSelectedQuote()!.requestDate).toLocaleDateString('tr-TR')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Received Quotes */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-4">
                  Gelen Teklifler ({getSelectedQuote()?.receivedQuotes?.length || 0})
                </h3>

                <div className="space-y-4">
                  {getSelectedQuote()?.receivedQuotes?.map((quote, index) => (
                    <div key={quote.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-semibold text-gray-900">{quote.producerName}</h4>
                          <p className="text-sm text-gray-600">{quote.producerLocation}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-green-600">
                            ${quote.price} {quote.currency}
                          </p>
                          <Badge className={getQuoteStatusColor(quote.status)}>
                            {quote.status}
                          </Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                        <div>
                          <p className="text-sm text-gray-600">Teslimat Süresi</p>
                          <p className="font-medium">{quote.deliveryTime}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Geçerlilik</p>
                          <p className="font-medium">
                            {new Date(quote.validUntil).toLocaleDateString('tr-TR')}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Gönderim Tarihi</p>
                          <p className="font-medium">
                            {new Date(quote.submittedDate).toLocaleDateString('tr-TR')}
                          </p>
                        </div>
                      </div>

                      {quote.notes && (
                        <div>
                          <p className="text-sm text-gray-600">Notlar</p>
                          <p className="text-sm text-gray-900 bg-gray-100 p-2 rounded mt-1">
                            {quote.notes}
                          </p>
                        </div>
                      )}

                      {/* Admin Action Buttons */}
                      <div className="flex justify-end space-x-2 mt-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewQuoteDetails(getSelectedQuote()!.id, quote.id)}
                          className="bg-blue-50 border-blue-200"
                        >
                          Detayları Görüntüle
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`mailto:${getSelectedQuote()?.receivedQuotes?.find(q => q.id === quote.id)?.producerName || ''}@example.com`)}
                          className="bg-green-50 border-green-200"
                        >
                          Üretici ile İletişim
                        </Button>
                      </div>

                      {/* Teklif Durumu Bilgisi */}
                      <div className="mt-3 p-3 bg-gray-50 rounded">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Durum:</span>
                          <Badge className={getQuoteStatusColor(quote.status)}>
                            {quote.status}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {quote.status === 'Bekliyor' && 'Müşteri değerlendirmesi bekleniyor'}
                          {quote.status === 'Kabul Edildi' && 'Müşteri tarafından kabul edildi'}
                          {quote.status === 'Reddedildi' && 'Müşteri tarafından reddedildi'}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Modal Footer */}
              <div className="flex justify-end mt-6 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => setShowQuoteDetailsModal(false)}
                >
                  Kapat
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
