// Settings Types - RFC-012
export type SettingDataType = 'string' | 'number' | 'boolean' | 'json' | 'array';

export type SettingsCategory =
  | 'PLATFORM'
  | 'SECURITY'
  | 'BUSINESS'
  | 'NOTIFICATIONS'
  | 'SYSTEM'
  | 'INTEGRATIONS';

export type SettingValue = string | number | boolean | object | any[];

export interface AdminSetting {
  id: string;
  category: SettingsCategory;
  key: string;
  value: SettingValue;
  dataType: SettingDataType;
  description?: string;
  isSensitive: boolean;
  requiresRestart: boolean;
  createdAt: Date;
  updatedAt: Date;
  updatedBy?: string;
}

export interface SettingUpdate {
  category: SettingsCategory;
  key: string;
  value: SettingValue;
  changeReason?: string;
}

export interface SettingsQueryOptions {
  category?: SettingsCategory;
  includeDescription?: boolean;
  includeSensitive?: boolean;
}

export interface SettingsAuditLog {
  id: string;
  settingId: string;
  category: SettingsCategory;
  key: string;
  oldValue: SettingValue;
  newValue: SettingValue;
  changedBy: string;
  changeReason?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

export interface SettingValidationRule {
  type: SettingDataType;
  required: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: string;
  enum?: any[];
  custom?: (value: any) => boolean | string;
}

export interface SettingsSchema {
  [category: string]: {
    [key: string]: SettingValidationRule & {
      description: string;
      sensitive: boolean;
      requiresRestart: boolean;
      defaultValue: SettingValue;
    };
  };
}

// Platform Settings
export interface PlatformSettings {
  siteName: string;
  siteDescription: string;
  logoUrl: string;
  faviconUrl: string;
  maintenanceMode: boolean;
  maintenanceMessage: string;
  defaultLanguage: string;
  supportedLanguages: string[];
  timezone: string;
  dateFormat: string;
  currencyFormat: string;
}

// Security Settings
export interface SecuritySettings {
  passwordMinLength: number;
  passwordRequireUppercase: boolean;
  passwordRequireLowercase: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireSymbols: boolean;
  sessionTimeout: number;
  maxConcurrentSessions: number;
  require2FA: boolean;
  allowedIPs: string[];
  blockedIPs: string[];
  loginAttemptLimit: number;
  lockoutDuration: number;
}

// Business Rules Settings
export interface BusinessSettings {
  commissionRateM2: number;
  commissionRateTon: number;
  upfrontPaymentPercentage: number;
  quoteValidityDays: number;
  autoQuoteExpiry: boolean;
  minimumOrderValueM2: number;
  minimumOrderValueTon: number;
  maxQuoteRequestsPerDay: number;
  producerApprovalRequired: boolean;
  productApprovalRequired: boolean;
  autoApproveReturningCustomers: boolean;
}

// Notification Settings
export interface NotificationSettings {
  emailEnabled: boolean;
  smsEnabled: boolean;
  pushEnabled: boolean;
  smtpHost: string;
  smtpPort: number;
  smtpUsername: string;
  smtpPassword: string;
  smtpSecure: boolean;
  smsProvider: string;
  smsApiKey: string;
  pushProvider: string;
  pushApiKey: string;
  notifyOnNewQuote: boolean;
  notifyOnQuoteAccepted: boolean;
  notifyOnPaymentReceived: boolean;
  notifyOnOrderShipped: boolean;
}

// System Settings
export interface SystemSettings {
  cacheTimeout: number;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  maxFileUploadSize: number;
  allowedFileTypes: string[];
  backupEnabled: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  backupRetentionDays: number;
  apiRateLimit: number;
  apiRateLimitWindow: number;
  enableMetrics: boolean;
  enableHealthCheck: boolean;
}

// Integration Settings
export interface IntegrationSettings {
  stripeEnabled: boolean;
  stripePublicKey: string;
  stripeSecretKey: string;
  paypalEnabled: boolean;
  paypalClientId: string;
  paypalClientSecret: string;
  googleMapsApiKey: string;
  openaiApiKey: string;
  chatbotEnabled: boolean;
  analyticsEnabled: boolean;
  analyticsTrackingId: string;
  webhookUrl: string;
  webhookSecret: string;
}

// API Request/Response Types
export interface GetSettingsRequest {
  category?: SettingsCategory;
  includeDescription?: boolean;
}

export interface GetSettingsResponse {
  success: boolean;
  data: AdminSetting[];
  message?: string;
}

export interface UpdateSettingsRequest {
  settings: SettingUpdate[];
}

export interface UpdateSettingsResponse {
  success: boolean;
  message: string;
  requiresRestart?: boolean;
}

export interface ResetSettingsRequest {
  category: SettingsCategory;
}

export interface ResetSettingsResponse {
  success: boolean;
  message: string;
}

export interface ExportSettingsResponse {
  success: boolean;
  data: any;
  filename: string;
}

export interface ImportSettingsRequest {
  settings: any;
  overwrite?: boolean;
}

export interface ImportSettingsResponse {
  success: boolean;
  message: string;
  imported: number;
  skipped: number;
  errors: string[];
}

export interface SettingsSchemaResponse {
  success: boolean;
  data: SettingsSchema;
}

// Error Types
export class SettingsValidationError extends Error {
  constructor(
    public field: string,
    public value: any,
    public rule: string,
    message: string
  ) {
    super(message);
    this.name = 'SettingsValidationError';
  }
}

export class SettingsNotFoundError extends Error {
  constructor(category: string, key: string) {
    super(`Setting not found: ${category}.${key}`);
    this.name = 'SettingsNotFoundError';
  }
}

export class SettingsPermissionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'SettingsPermissionError';
  }
}
