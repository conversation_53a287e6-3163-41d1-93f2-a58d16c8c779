'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  X,
  Mail,
  Users,
  Eye,
  MousePointer,
  TrendingUp,
  Calendar,
  Globe,
  Target,
  BarChart3
} from 'lucide-react';

interface EmailCampaign {
  id: string;
  name: string;
  subject: string;
  targetCountries: string[];
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'paused';
  scheduledFor?: Date;
  sentAt?: Date;
  recipients: number;
  openRate: number;
  clickRate: number;
  conversionRate: number;
  createdAt: Date;
}

interface CampaignDetailModalProps {
  campaign: EmailCampaign;
  onClose: () => void;
}

export default function CampaignDetailModal({ campaign, onClose }: CampaignDetailModalProps) {
  // Mock detailed campaign data
  const campaignDetails = {
    content: `
      <h1>Yeni ${campaign.name} Koleksiyonumuz!</h1>
      <p><PERSON>v<PERSON><PERSON>ş<PERSON><PERSON>,</p>
      <p><PERSON>ürkiye'nin en kaliteli doğal taş ürünleri ile projelerinizi hayata geçirmenin zamanı geldi!</p>
      <p>Yeni koleksiyonumuzda:</p>
      <ul>
        <li>Premium kalite traverten ürünleri</li>
        <li>Çeşitli boyut ve yüzey seçenekleri</li>
        <li>Hızlı teslimat garantisi</li>
        <li>Rekabetçi fiyatlar</li>
      </ul>
      <p>Detaylı bilgi ve fiyat teklifi için hemen iletişime geçin!</p>
    `,
    detailedMetrics: {
      delivered: Math.floor(campaign.recipients * 0.95),
      bounced: Math.floor(campaign.recipients * 0.05),
      opened: Math.floor(campaign.recipients * (campaign.openRate / 100)),
      clicked: Math.floor(campaign.recipients * (campaign.clickRate / 100)),
      converted: Math.floor(campaign.recipients * (campaign.conversionRate / 100)),
      unsubscribed: Math.floor(campaign.recipients * 0.002),
      complaints: Math.floor(campaign.recipients * 0.001)
    },
    countryBreakdown: [
      { country: 'Amerika', code: 'US', flag: '🇺🇸', recipients: Math.floor(campaign.recipients * 0.4), openRate: 26.2 },
      { country: 'Almanya', code: 'DE', flag: '🇩🇪', recipients: Math.floor(campaign.recipients * 0.35), openRate: 23.8 },
      { country: 'İtalya', code: 'IT', flag: '🇮🇹', recipients: Math.floor(campaign.recipients * 0.25), openRate: 21.5 }
    ],
    hourlyStats: [
      { hour: '09:00', opens: 45, clicks: 8 },
      { hour: '10:00', opens: 67, clicks: 12 },
      { hour: '11:00', opens: 89, clicks: 18 },
      { hour: '12:00', opens: 56, clicks: 11 },
      { hour: '13:00', opens: 34, clicks: 6 },
      { hour: '14:00', opens: 78, clicks: 15 },
      { hour: '15:00', opens: 92, clicks: 19 },
      { hour: '16:00', opens: 67, clicks: 13 }
    ]
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary">Taslak</Badge>;
      case 'scheduled':
        return <Badge variant="outline">Zamanlandı</Badge>;
      case 'sending':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Gönderiliyor</Badge>;
      case 'sent':
        return <Badge variant="default" className="bg-green-100 text-green-800">Gönderildi</Badge>;
      case 'paused':
        return <Badge variant="destructive">Duraklatıldı</Badge>;
      default:
        return <Badge variant="outline">Bilinmiyor</Badge>;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{campaign.name}</h2>
            <div className="flex items-center space-x-4 mt-2">
              <p className="text-gray-600">{campaign.subject}</p>
              {getStatusBadge(campaign.status)}
            </div>
          </div>
          <Button variant="ghost" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>

        <div className="p-6 space-y-6">
          {/* Campaign Info */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Toplam Alıcı</p>
                    <p className="text-2xl font-bold">{campaign.recipients.toLocaleString()}</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Açılma Oranı</p>
                    <p className="text-2xl font-bold text-green-600">{campaign.openRate}%</p>
                  </div>
                  <Eye className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Tıklama Oranı</p>
                    <p className="text-2xl font-bold text-purple-600">{campaign.clickRate}%</p>
                  </div>
                  <MousePointer className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Dönüşüm Oranı</p>
                    <p className="text-2xl font-bold text-orange-600">{campaign.conversionRate}%</p>
                  </div>
                  <Target className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Detailed Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Detaylı Metrikler
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Teslim Edilen</span>
                    <span className="font-semibold text-green-600">{campaignDetails.detailedMetrics.delivered.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Geri Dönen</span>
                    <span className="font-semibold text-red-600">{campaignDetails.detailedMetrics.bounced.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Açan</span>
                    <span className="font-semibold text-blue-600">{campaignDetails.detailedMetrics.opened.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Tıklayan</span>
                    <span className="font-semibold text-purple-600">{campaignDetails.detailedMetrics.clicked.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Dönüştüren</span>
                    <span className="font-semibold text-orange-600">{campaignDetails.detailedMetrics.converted.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Abonelikten Çıkan</span>
                    <span className="font-semibold text-gray-600">{campaignDetails.detailedMetrics.unsubscribed.toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Country Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="w-5 h-5 mr-2" />
                  Ülke Bazlı Performans
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {campaignDetails.countryBreakdown.map((country, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{country.flag}</span>
                          <span className="font-medium">{country.country}</span>
                        </div>
                        <span className="text-sm text-gray-500">{country.recipients.toLocaleString()} alıcı</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Açılma Oranı</span>
                        <span className="font-semibold text-green-600">{country.openRate}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Campaign Content Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="w-5 h-5 mr-2" />
                İçerik Önizlemesi
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="bg-white p-6 rounded border max-h-64 overflow-y-auto">
                  <div dangerouslySetInnerHTML={{ __html: campaignDetails.content }} />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Hourly Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Saatlik Performans
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-32 flex items-end justify-between space-x-1">
                {campaignDetails.hourlyStats.map((stat, index) => {
                  const maxOpens = Math.max(...campaignDetails.hourlyStats.map(s => s.opens));
                  const height = (stat.opens / maxOpens) * 100;
                  
                  return (
                    <div key={index} className="flex flex-col items-center">
                      <div 
                        className="bg-blue-500 rounded-t w-8 min-h-[4px] transition-all hover:bg-blue-600"
                        style={{ height: `${height}%` }}
                        title={`${stat.hour} - ${stat.opens} açılma, ${stat.clicks} tıklama`}
                      ></div>
                      <span className="text-xs text-gray-500 mt-1">{stat.hour}</span>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Campaign Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Kampanya Zaman Çizelgesi</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="font-medium">Kampanya Oluşturuldu</p>
                    <p className="text-sm text-gray-500">{campaign.createdAt.toLocaleString('tr-TR')}</p>
                  </div>
                </div>
                {campaign.scheduledFor && (
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Gönderim Zamanlandı</p>
                      <p className="text-sm text-gray-500">{campaign.scheduledFor.toLocaleString('tr-TR')}</p>
                    </div>
                  </div>
                )}
                {campaign.sentAt && (
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Kampanya Gönderildi</p>
                      <p className="text-sm text-gray-500">{campaign.sentAt.toLocaleString('tr-TR')}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Kapat
            </Button>
            <Button variant="outline">
              <TrendingUp className="w-4 h-4 mr-2" />
              Rapor İndir
            </Button>
            <Button>
              <Mail className="w-4 h-4 mr-2" />
              Benzer Kampanya Oluştur
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
