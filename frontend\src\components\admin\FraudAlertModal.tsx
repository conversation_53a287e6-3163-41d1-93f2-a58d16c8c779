'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Card } from '@/components/ui/card'
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  User,
  DollarSign,
  MapPin,
  Monitor,
  Clock,
  Flag,
  FileText,
  Activity,
  Eye
} from 'lucide-react'

interface FraudAlertModalProps {
  alert: any
  isOpen: boolean
  onClose: () => void
  onInvestigate: (alertId: string, resolution: string, notes: string) => void
}

export function FraudAlertModal({
  alert,
  isOpen,
  onClose,
  onInvestigate
}: FraudAlertModalProps) {
  const [resolution, setResolution] = useState('')
  const [notes, setNotes] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)

  const handleSubmit = async () => {
    if (!resolution) {
      alert('Lütfen bir karar seçin')
      return
    }

    setIsProcessing(true)
    try {
      await onInvestigate(alert.id, resolution, notes)
      onClose()
    } catch (error) {
      console.error('Error investigating alert:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge variant="destructive">Kritik</Badge>
      case 'high':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Yüksek</Badge>
      case 'medium':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Orta</Badge>
      case 'low':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Düşük</Badge>
      default:
        return <Badge variant="outline">{severity}</Badge>
    }
  }

  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return 'text-red-600 bg-red-100'
    if (score >= 60) return 'text-yellow-600 bg-yellow-100'
    if (score >= 40) return 'text-blue-600 bg-blue-100'
    return 'text-green-600 bg-green-100'
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'suspicious_amount':
        return <DollarSign className="w-5 h-5" />
      case 'unusual_pattern':
        return <Activity className="w-5 h-5" />
      case 'high_risk_user':
        return <User className="w-5 h-5" />
      case 'duplicate_transaction':
        return <FileText className="w-5 h-5" />
      default:
        return <AlertTriangle className="w-5 h-5" />
    }
  }

  if (!alert) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Fraud Alert İnceleme - {alert.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Alert Overview */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getSeverityBadge(alert.severity)}
              <span className="text-sm text-gray-500">
                Oluşturulma: {formatDate(alert.createdAt)}
              </span>
            </div>
            <div className={`px-4 py-2 rounded-lg ${getRiskScoreColor(alert.riskScore)}`}>
              <div className="text-center">
                <div className="text-2xl font-bold">{alert.riskScore}</div>
                <div className="text-xs">Risk Skoru</div>
              </div>
            </div>
          </div>

          {/* Alert Description */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              {getTypeIcon(alert.type)}
              Uyarı Detayları
            </h3>
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Uyarı Türü</label>
                <p className="text-gray-900 capitalize">{alert.type.replace('_', ' ')}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Açıklama</label>
                <p className="text-gray-900">{alert.description}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Risk Faktörleri</label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {alert.flags.map((flag: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      <Flag className="w-3 h-3 mr-1" />
                      {flag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </Card>

          {/* Customer and Payment Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <User className="w-5 h-5" />
                Müşteri Bilgileri
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Firma Adı</label>
                  <p className="text-gray-900">{alert.customerName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Kullanıcı ID</label>
                  <p className="text-gray-900 font-mono text-sm">{alert.userId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Hesap Yaşı</label>
                  <p className="text-gray-900">{alert.details.accountAge} gün</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Önceki İşlemler</label>
                  <p className="text-gray-900">{alert.details.previousTransactions} işlem</p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <DollarSign className="w-5 h-5" />
                Ödeme Bilgileri
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Ödeme ID</label>
                  <p className="text-gray-900 font-mono text-sm">{alert.paymentId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Tutar</label>
                  <p className="text-gray-900 text-lg font-semibold">
                    ${alert.amount.toLocaleString()} {alert.currency}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">İşlem Tarihi</label>
                  <p className="text-gray-900">{formatDate(alert.createdAt)}</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Technical Details */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              Teknik Detaylar
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">IP Adresi</label>
                  <p className="text-gray-900 font-mono">{alert.details.ipAddress}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Konum</label>
                  <p className="text-gray-900 flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    {alert.details.location}
                  </p>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Cihaz Parmak İzi</label>
                  <p className="text-gray-900 font-mono text-sm">{alert.details.deviceFingerprint}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Risk Skoru</label>
                  <div className="flex items-center gap-2">
                    <div className={`px-3 py-1 rounded ${getRiskScoreColor(alert.riskScore)}`}>
                      <span className="font-bold">{alert.riskScore}/100</span>
                    </div>
                    <span className="text-sm text-gray-500">
                      {alert.riskScore >= 80 ? 'Çok Yüksek Risk' :
                       alert.riskScore >= 60 ? 'Yüksek Risk' :
                       alert.riskScore >= 40 ? 'Orta Risk' : 'Düşük Risk'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Investigation History */}
          {alert.investigated && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Clock className="w-5 h-5" />
                İnceleme Geçmişi
              </h3>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  <strong>İnceleme Tarihi:</strong> {formatDate(alert.investigatedAt)}
                </p>
                <p className="text-sm text-gray-600">
                  <strong>İnceleme Yapan:</strong> {alert.investigatedBy}
                </p>
                <p className="text-sm text-gray-600">
                  <strong>Sonuç:</strong> 
                  <Badge className="ml-2" variant={alert.resolution === 'false_positive' ? 'secondary' : 'destructive'}>
                    {alert.resolution === 'false_positive' ? 'Yanlış Alarm' : 
                     alert.resolution === 'confirmed_fraud' ? 'Dolandırıcılık Onaylandı' : 
                     'İnceleme Altında'}
                  </Badge>
                </p>
              </div>
            </Card>
          )}

          {/* Investigation Actions */}
          {!alert.investigated && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                İnceleme Sonucu
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Karar
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="resolution"
                        value="false_positive"
                        checked={resolution === 'false_positive'}
                        onChange={(e) => setResolution(e.target.value)}
                        className="mr-2"
                      />
                      <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                      Yanlış Alarm - Güvenli İşlem
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="resolution"
                        value="confirmed_fraud"
                        checked={resolution === 'confirmed_fraud'}
                        onChange={(e) => setResolution(e.target.value)}
                        className="mr-2"
                      />
                      <XCircle className="w-4 h-4 mr-2 text-red-600" />
                      Dolandırıcılık Onaylandı
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="resolution"
                        value="under_review"
                        checked={resolution === 'under_review'}
                        onChange={(e) => setResolution(e.target.value)}
                        className="mr-2"
                      />
                      <Eye className="w-4 h-4 mr-2 text-blue-600" />
                      Daha Fazla İnceleme Gerekli
                    </label>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    İnceleme Notları
                  </label>
                  <Textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="İnceleme sürecinde elde ettiğiniz bulgular ve kararınızın gerekçesini yazın..."
                    rows={4}
                    className="w-full"
                  />
                </div>

                <div className="flex gap-3">
                  <Button
                    onClick={handleSubmit}
                    disabled={isProcessing || !resolution}
                    className="flex-1"
                  >
                    {isProcessing ? 'İşleniyor...' : 'İncelemeyi Tamamla'}
                  </Button>
                  <Button
                    onClick={onClose}
                    variant="outline"
                    disabled={isProcessing}
                  >
                    İptal
                  </Button>
                </div>
              </div>
            </Card>
          )}

          {/* Quick Actions */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Hızlı İşlemler
            </h3>
            <div className="flex gap-3">
              <Button variant="outline" size="sm">
                <User className="w-4 h-4 mr-2" />
                Müşteri Profilini Görüntüle
              </Button>
              <Button variant="outline" size="sm">
                <FileText className="w-4 h-4 mr-2" />
                Ödeme Detaylarını Görüntüle
              </Button>
              <Button variant="outline" size="sm">
                <Activity className="w-4 h-4 mr-2" />
                İşlem Geçmişini İncele
              </Button>
            </div>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
