'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  ShoppingCartIcon,
  ClipboardDocumentListIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  TruckIcon
} from '@heroicons/react/24/outline';

interface Activity {
  id: number;
  type: 'order' | 'quote' | 'delivery' | 'payment';
  title: string;
  description: string;
  time: string;
  status: 'completed' | 'pending' | 'cancelled' | 'in-progress';
}

interface RecentActivityProps {
  activities: Activity[];
  onNavigate?: (route: string) => void;
}

const RecentActivity: React.FC<RecentActivityProps> = ({ activities, onNavigate }) => {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order':
        return ShoppingCartIcon;
      case 'quote':
        return ClipboardDocumentListIcon;
      case 'delivery':
        return TruckIcon;
      case 'payment':
        return CheckCircleIcon;
      default:
        return ClockIcon;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return CheckCircleIcon;
      case 'cancelled':
        return XCircleIcon;
      case 'pending':
      case 'in-progress':
        return ClockIcon;
      default:
        return ClockIcon;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'in-progress':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'order':
        return 'text-blue-600 bg-blue-100';
      case 'quote':
        return 'text-purple-600 bg-purple-100';
      case 'delivery':
        return 'text-green-600 bg-green-100';
      case 'payment':
        return 'text-orange-600 bg-orange-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Tamamlandı';
      case 'cancelled':
        return 'İptal Edildi';
      case 'pending':
        return 'Bekliyor';
      case 'in-progress':
        return 'Devam Ediyor';
      default:
        return 'Bilinmiyor';
    }
  };

  // Test verileri kaldırıldı - gerçek veriler API'den gelecek
  const displayActivities = activities;

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Son Aktiviteler
        </h3>
        <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
          Tümünü Gör
        </button>
      </div>

      {/* Activities List */}
      <div className="space-y-4">
        {displayActivities.slice(0, 5).map((activity, index) => {
          const ActivityIcon = getActivityIcon(activity.type);
          const StatusIcon = getStatusIcon(activity.status);
          
          return (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-150 cursor-pointer group"
            >
              {/* Activity Icon */}
              <div className={`p-2 rounded-lg ${getActivityColor(activity.type)} group-hover:scale-110 transition-transform duration-200`}>
                <ActivityIcon className="h-4 w-4" />
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900 group-hover:text-gray-700">
                      {activity.title}
                    </h4>
                    <p className="text-sm text-gray-600 mt-1 group-hover:text-gray-500">
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-500 mt-2">
                      {activity.time}
                    </p>
                  </div>

                  {/* Status Badge */}
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(activity.status)}`}>
                    <StatusIcon className="h-3 w-3" />
                    <span>{getStatusLabel(activity.status)}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Empty State */}
      {displayActivities.length === 0 && (
        <div className="text-center py-8">
          <ClockIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            Henüz aktivite yok
          </h4>
          <p className="text-sm text-gray-600">
            İlk işleminizi yaptığınızda burada görünecek.
          </p>
        </div>
      )}

      {/* Footer */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full text-center py-2 px-4 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200"
        >
          Tüm Aktiviteleri Görüntüle
        </motion.button>
      </div>
    </div>
  );
};

export default RecentActivity;
