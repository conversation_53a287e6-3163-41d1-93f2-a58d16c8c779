'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useProducts } from '@/contexts/products-context'
import {
  ArrowLeft,
  MapPin,
  Building2,
  Calendar,
  CheckCircle,
  AlertCircle,
  Search,
  Plus,
  Package,
  Upload,
  Ruler,
  DollarSign,
  Palette,
  Box,
  Truck,
  Trash2
} from 'lucide-react'
import { Quarry, QuarryProduct, ProductFormData } from '@/types/quarry'

// Form data interface (mevcut formdan alındı)
interface ProductFormDataDetailed {
  // Temel Bilgiler
  name: string
  category: string
  description: string
  origin: string
  status: 'active' | 'draft' | 'inactive'
  isJoiningExisting?: boolean

  // Ürün Medya Yükleme Alanı
  media: {
    coverImage?: { file?: File; url?: string }
    slabImages: Array<{ file?: File; url?: string }>
    mockupImages: Array<{ file?: File; url?: string }>
    renderImages?: Array<{ file?: File; url?: string }>
    video?: { file?: File; url?: string }
  }

  // Teknik Özellikler
  technicalSpecs: {
    density?: string
    waterAbsorption?: string
    compressiveStrength?: string
    flexuralStrength?: string
    frostResistance?: string
    abrasionResistance?: string
    customSpecs: Array<{
      name: string
      value: string
    }>
  }

  // Ebatlı Ürün Fiyat Listesi
  dimensionPrices: Array<{
    width: number
    height: number
    thickness: number
    price: number
    currency: string
    unit: string
  }>

  // Yüzey İşlemi Fiyat Listesi
  dimensionSurfaceFinishPrices: Array<{
    finish: string
    additionalPrice: number
    currency: string
  }>

  // Ambalaj Fiyat Listesi
  dimensionPackagingPrices: Array<{
    packaging: string
    price: number
    currency: string
  }>

  // Plaka Ürün Fiyat Listesi
  slabPrices: Array<{
    size: string
    price: number
    currency: string
    unit: string
  }>

  // Plaka Yüzey İşlemi Fiyat Listesi
  slabSurfaceFinishPrices: Array<{
    finish: string
    additionalPrice: number
    currency: string
  }>

  // Plaka Ambalaj Fiyat Listesi
  slabPackagingPrices: Array<{
    packaging: string
    price: number
    currency: string
  }>

  // Taş Analiz Raporları
  analysisReports: Array<{
    file?: File
    url?: string
    name: string
    type: string
  }>
}

// Mock data - gerçek uygulamada API'den gelecek
const mockQuarries: Quarry[] = [
  {
    id: '1',
    name: 'Afyon Beyaz Mermer Ocağı',
    location: {
      city: 'Afyon',
      district: 'Merkez',
      coordinates: { lat: 38.7507, lng: 30.5567 },
      address: 'Afyon Merkez, Mermer Mahallesi',
      googleMapsLink: 'https://maps.google.com/...'
    },
    owner: 'Afyon Mermer A.Ş.',
    establishedYear: 1985,
    capacity: '10000 m³/yıl',
    certifications: ['ISO 9001', 'CE'],
    isActive: true,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '2',
    name: 'Denizli Traverten Ocağı',
    location: {
      city: 'Denizli',
      district: 'Pamukkale',
      coordinates: { lat: 37.9144, lng: 29.1256 },
      address: 'Pamukkale, Traverten Bölgesi',
      googleMapsLink: 'https://maps.google.com/...'
    },
    owner: 'Pamukkale Doğaltaş Ltd.',
    establishedYear: 1992,
    capacity: '15000 m³/yıl',
    certifications: ['ISO 9001', 'ISO 14001'],
    isActive: true,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '3',
    name: 'Muğla Granit Ocağı',
    location: {
      city: 'Muğla',
      district: 'Milas',
      coordinates: { lat: 37.3167, lng: 27.7833 },
      address: 'Milas, Granit Bölgesi',
      googleMapsLink: 'https://maps.google.com/...'
    },
    owner: 'Ege Granit A.Ş.',
    establishedYear: 1998,
    capacity: '8000 m³/yıl',
    certifications: ['ISO 9001', 'CE', 'ISO 14001'],
    isActive: true,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  }
]

// Her ocaktan çıkan taşlar
const mockQuarryStones = {
  '1': [ // Afyon Beyaz Mermer Ocağı
    {
      id: 'stone-1-1',
      name: 'Afyon Beyaz Mermer - Premium',
      category: 'Mermer',
      image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRkZGRkZGIiBzdHJva2U9IiNFNUU3RUIiLz4KPHRleHQgeD0iMTUwIiB5PSIxMDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM2QjcyODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZHk9Ii4zZW0iPkFmeW9uIEJleWF6PC90ZXh0Pjwvc3ZnPg==',
      description: 'Yüksek kaliteli beyaz mermer, premium seleksiyon',
      hasExistingProduct: true,
      producerCount: 3
    },
    {
      id: 'stone-1-2',
      name: 'Afyon Beyaz Mermer - Standart',
      category: 'Mermer',
      image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjlGQUZCIiBzdHJva2U9IiNFNUU3RUIiLz4KPHRleHQgeD0iMTUwIiB5PSIxMDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM2QjcyODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgZHk9Ii4zZW0iPkFmeW9uIFN0YW5kYXJ0PC90ZXh0Pjwvc3ZnPg==',
      description: 'Standart kalite beyaz mermer',
      hasExistingProduct: false,
      producerCount: 0
    }
  ],
  '2': [ // Denizli Traverten Ocağı
    {
      id: 'stone-2-1',
      name: 'Denizli Traverten Klasik',
      category: 'Traverten',
      image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRkJGNUYwIiBzdHJva2U9IiNFNUU3RUIiLz4KPHRleHQgeD0iMTUwIiB5PSIxMDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM5MjQwMEQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgZHk9Ii4zZW0iPkRlbml6bGkgVHJhdmVydGVuPC90ZXh0Pjwvc3ZnPg==',
      description: 'Klasik traverten, doğal doku',
      hasExistingProduct: true,
      producerCount: 2
    },
    {
      id: 'stone-2-2',
      name: 'Denizli Traverten Noce',
      category: 'Traverten',
      image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRDJCNDhDIiBzdHJva2U9IiNFNUU3RUIiLz4KPHRleHQgeD0iMTUwIiB5PSIxMDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM5MjQwMEQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZHk9Ii4zZW0iPk5vY2UgVHJhdmVydGVuPC90ZXh0Pjwvc3ZnPg==',
      description: 'Koyu renkli traverten, premium kalite',
      hasExistingProduct: false,
      producerCount: 0
    },
    {
      id: 'stone-2-3',
      name: 'Denizli Traverten Silver',
      category: 'Traverten',
      image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2IiBzdHJva2U9IiNFNUU3RUIiLz4KPHRleHQgeD0iMTUwIiB5PSIxMDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM2QjcyODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgZHk9Ii4zZW0iPlNpbHZlciBUcmF2ZXJ0ZW48L3RleHQ+PC9zdmc+',
      description: 'Gümüşi gri traverten',
      hasExistingProduct: true,
      producerCount: 1
    }
  ],
  '3': [ // Muğla Granit Ocağı
    {
      id: 'stone-3-1',
      name: 'Muğla Siyah Granit',
      category: 'Granit',
      image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjMTExODI3IiBzdHJva2U9IiNFNUU3RUIiLz4KPHRleHQgeD0iMTUwIiB5PSIxMDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiNGRkZGRkYiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZHk9Ii4zZW0iPk11xJ9sYSBTaXlhaCBHcmFuaXQ8L3RleHQ+PC9zdmc+',
      description: 'Premium siyah granit, yüksek parlaklık',
      hasExistingProduct: false,
      producerCount: 0
    },
    {
      id: 'stone-3-2',
      name: 'Muğla Gri Granit',
      category: 'Granit',
      image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNkI3MjgwIiBzdHJva2U9IiNFNUU3RUIiLz4KPHRleHQgeD0iMTUwIiB5PSIxMDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiNGRkZGRkYiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZHk9Ii4zZW0iPk11xJ9sYSBHcmkgR3Jhbml0PC90ZXh0Pjwvc3ZnPg==',
      description: 'Doğal gri granit, dayanıklı',
      hasExistingProduct: true,
      producerCount: 1
    }
  ]
}

const mockExistingProducts: QuarryProduct[] = [
  {
    id: '1',
    quarryId: '1',
    name: 'Afyon Beyaz Mermer',
    category: 'Mermer',
    description: 'Yüksek kaliteli beyaz mermer',
    technicalSpecs: {
      density: 2650,
      hardness: 3.5,
      waterAbsorption: 0.2
    },
    media: {
      coverImage: '',
      images: []
    },
    producers: ['producer1', 'producer2'],
    isActive: true,
    approvalStatus: 'approved',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    createdBy: 'producer1'
  }
]

export default function AddProductPage() {
  const router = useRouter()
  const { addProduct } = useProducts()
  const [currentStep, setCurrentStep] = React.useState(1)
  const [selectedQuarry, setSelectedQuarry] = React.useState<Quarry | null>(null)
  const [selectedStone, setSelectedStone] = React.useState<any>(null)
  const [existingProduct, setExistingProduct] = React.useState<QuarryProduct | null>(null)
  const [quarrySearch, setQuarrySearch] = React.useState('')
  const [showNewQuarryForm, setShowNewQuarryForm] = React.useState(false)
  const [showQuarryDetails, setShowQuarryDetails] = React.useState(false)
  const [newQuarryData, setNewQuarryData] = React.useState({
    name: '',
    address: '',
    googleMapsLink: '',
    owner: '',
    stones: [{ name: '', category: '', description: '' }]
  })

  // Form data state
  const [formData, setFormData] = React.useState<ProductFormDataDetailed>({
    name: '',
    category: '',
    description: '',
    origin: '',
    status: 'draft',
    media: {
      slabImages: [],
      mockupImages: [],
      renderImages: []
    },
    technicalSpecs: {
      customSpecs: []
    },
    dimensionPrices: [],
    dimensionSurfaceFinishPrices: [],
    dimensionPackagingPrices: [],
    slabPrices: [],
    slabSurfaceFinishPrices: [],
    slabPackagingPrices: [],
    analysisReports: []
  })

  const steps = [
    { id: 1, title: 'Ocak Seçimi', description: 'Ürününüzün çıkarıldığı ocağı seçin' },
    { id: 2, title: 'Taş Seçimi', description: 'Bu ocaktan çıkan taşları görün ve seçin' },
    { id: 3, title: 'Ürün Kontrolü', description: 'Bu taş daha önce sisteme eklenmiş mi?' },
    { id: 4, title: 'Temel Bilgiler', description: 'Ürün adı, kategori ve açıklama' },
    { id: 5, title: 'Medya Yükleme', description: 'Resim, mokap, render ve video' },
    { id: 6, title: 'Teknik Özellikler', description: 'Ürünün teknik detayları' },
    { id: 7, title: 'Ebatlı Fiyat Listesi', description: 'Boyutlara göre fiyatlandırma' },
    { id: 8, title: 'Yüzey İşlemi Fiyatları', description: 'İşlem türlerine göre ek fiyatlar' },
    { id: 9, title: 'Plaka Fiyat Listesi', description: 'Plaka ürünleri için fiyatlandırma' },
    { id: 10, title: 'Plaka Yüzey İşlemi', description: 'Plaka işlem fiyatları' },
    { id: 11, title: 'Analiz Raporları', description: 'Taş analiz raporları ve belgeler' },
    { id: 12, title: 'Onay', description: 'Bilgileri kontrol edin ve gönderin' }
  ]

  const filteredQuarries = mockQuarries.filter(quarry =>
    quarry.name.toLowerCase().includes(quarrySearch.toLowerCase()) ||
    quarry.location.city.toLowerCase().includes(quarrySearch.toLowerCase())
  )

  const handleQuarrySelect = (quarry: Quarry) => {
    setSelectedQuarry(quarry)
    setCurrentStep(2) // Taş seçimi adımına git
  }

  const handleStoneSelect = (stone: any) => {
    setSelectedStone(stone)

    // Bu taş için daha önce ürün eklenmiş mi kontrol et
    if (stone.hasExistingProduct) {
      // Mock existing product oluştur
      const existingProduct = {
        id: stone.id,
        quarryId: selectedQuarry?.id || '',
        name: stone.name,
        category: stone.category,
        description: stone.description,
        technicalSpecs: {},
        media: { coverImage: stone.image, images: [] },
        producers: Array.from({length: stone.producerCount}, (_, i) => `producer${i+1}`),
        isActive: true,
        approvalStatus: 'approved' as const,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'producer1'
      }
      setExistingProduct(existingProduct)
    } else {
      setExistingProduct(null)
    }

    setCurrentStep(3) // Ürün kontrolü adımına git
  }

  const handleContinueWithExisting = () => {
    // Mevcut ürüne katıl - sadece fiyat listesi adımına git
    // Mevcut ürün bilgilerini form data'ya yükle
    if (existingProduct && selectedStone) {
      setFormData(prev => ({
        ...prev,
        name: selectedStone.name,
        category: selectedStone.category,
        description: selectedStone.description,
        origin: selectedQuarry?.location.city || '',
        isJoiningExisting: true // Mevcut ürüne katıldığını işaretle
      }))
    }
    setCurrentStep(7) // Ebatlı fiyat listesi adımına git
  }

  const handleCreateNewProduct = () => {
    // Yeni ürün oluştur - tam detay adımına git
    setExistingProduct(null)
    // Temel bilgileri önceden doldur
    if (selectedStone && selectedQuarry) {
      setFormData(prev => ({
        ...prev,
        name: selectedStone.name,
        category: selectedStone.category,
        description: selectedStone.description,
        origin: selectedQuarry.location.city
      }))
    }
    setCurrentStep(4) // Temel bilgiler adımına git
  }

  const handleSubmitProduct = async (productData: ProductFormDataDetailed, sendToAdmin: boolean) => {
    try {
      // Validation
      if (!productData.name.trim()) {
        alert('Ürün adı gereklidir')
        return
      }

      if (!productData.category) {
        alert('Kategori seçimi gereklidir')
        return
      }

      if (!productData.media.coverImage?.url && !productData.media.coverImage?.file) {
        alert('Kapak resmi zorunludur')
        return
      }

      // Durum kontrolü - Aktif ise admin onayına gönder
      const finalProductData = {
        ...productData,
        approvalStatus: sendToAdmin ? 'pending' : undefined,
        submittedAt: sendToAdmin ? new Date() : undefined,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      // Products context'e ekle
      const newProduct = {
        id: finalProductData.id,
        name: finalProductData.name,
        category: finalProductData.category,
        image: finalProductData.media.coverImage?.url || finalProductData.media.coverImage?.file ? URL.createObjectURL(finalProductData.media.coverImage.file) : undefined,
        status: finalProductData.status as 'active' | 'draft' | 'inactive',
        stock: 0,
        unit: 'm²',
        basePrice: 0,
        currency: 'USD',
        createdAt: new Date().toISOString().split('T')[0],
        approvalStatus: sendToAdmin ? 'pending' as const : undefined,
        submittedAt: sendToAdmin ? new Date() : undefined,
        producer: 'Test Üretici', // Gerçek uygulamada auth context'ten gelecek
        description: finalProductData.description,
        media: finalProductData.media
      }

      // Context'e ekle
      addProduct(newProduct)

      console.log('Ürün kaydediliyor:', finalProductData)

      if (sendToAdmin) {
        alert('✅ Ürün başarıyla admin onayına gönderildi!\n\nÜrününüz incelendikten sonra müşterilere görünür hale gelecektir.')
      } else {
        alert('✅ Ürün başarıyla kaydedildi!\n\nÜrününüz profilinizde görüntülenebilir ve istediğiniz zaman düzenleyebilirsiniz.')
      }

      // Başarılı kayıt sonrası yönlendirme
      router.push('/producer/products')

    } catch (error) {
      console.error('Ürün kaydetme hatası:', error)
      alert('❌ Ürün kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.')
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Ocak Seçimi</h2>
              <p className="text-gray-600">
                Sistemde kayıtlı ocaklar arasından ürününüzün çıkarıldığı ocağı seçin.
              </p>
              <p className="text-sm text-amber-600 mt-2">
                Her ocaktan çıkan taşları görebilir ve doğru seçimi yapabilirsiniz.
              </p>
            </div>

            {/* Arama */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Ocak adı veya şehir ile arayın..."
                value={quarrySearch}
                onChange={(e) => setQuarrySearch(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              />
            </div>

            {/* Ocak Listesi */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredQuarries.map((quarry) => (
                <Card 
                  key={quarry.id} 
                  className="p-4 cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-amber-300"
                  onClick={() => handleQuarrySelect(quarry)}
                >
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <h3 className="font-semibold text-lg text-gray-900">{quarry.name}</h3>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Aktif
                      </Badge>
                    </div>
                    
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4" />
                        {quarry.location.city}, {quarry.location.district}
                      </div>
                      <div className="flex items-center gap-2">
                        <Building2 className="w-4 h-4" />
                        {quarry.owner}
                      </div>
                      {quarry.establishedYear && (
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          Kuruluş: {quarry.establishedYear}
                        </div>
                      )}
                    </div>

                    {quarry.capacity && (
                      <div className="text-sm">
                        <span className="font-medium">Kapasite:</span> {quarry.capacity}
                      </div>
                    )}

                    {/* Taş Sayısı */}
                    <div className="text-sm">
                      <span className="font-medium">Çıkan Taş Çeşidi:</span> {
                        mockQuarryStones[quarry.id as keyof typeof mockQuarryStones]?.length || 0
                      } adet
                    </div>

                    {quarry.certifications.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {quarry.certifications.map((cert, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {cert}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Taş Önizlemesi */}
                    <div className="mt-3">
                      <p className="text-xs text-gray-500 mb-2">Bu ocaktan çıkan taşlar:</p>
                      <div className="flex gap-1 overflow-x-auto">
                        {mockQuarryStones[quarry.id as keyof typeof mockQuarryStones]?.slice(0, 3).map((stone, index) => (
                          <div key={index} className="flex-shrink-0">
                            <img
                              src={stone.image}
                              alt={stone.name}
                              className="w-12 h-8 object-cover rounded border"
                              title={stone.name}
                            />
                          </div>
                        ))}
                        {mockQuarryStones[quarry.id as keyof typeof mockQuarryStones]?.length > 3 && (
                          <div className="flex-shrink-0 w-12 h-8 bg-gray-100 rounded border flex items-center justify-center">
                            <span className="text-xs text-gray-500">+{mockQuarryStones[quarry.id as keyof typeof mockQuarryStones].length - 3}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Yeni Ocak Ekle Butonu */}
            <div className="text-center py-4">
              <Button
                variant="outline"
                onClick={() => setShowNewQuarryForm(true)}
                className="border-amber-400 text-amber-700 hover:bg-amber-50"
              >
                <Plus className="w-4 h-4 mr-2" />
                Yeni Ocak Ekle
              </Button>
            </div>

            {filteredQuarries.length === 0 && quarrySearch && (
              <div className="text-center py-8">
                <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Ocak bulunamadı</h3>
                <p className="text-gray-600 mb-4">
                  Arama kriterlerinize uygun ocak bulunamadı. Farklı arama terimleri deneyin.
                </p>
                <Button
                  variant="outline"
                  onClick={() => setQuarrySearch('')}
                  className="border-amber-400 text-amber-700 hover:bg-amber-50"
                >
                  Aramayı Temizle
                </Button>
              </div>
            )}

            {/* Yeni Ocak Ekleme Formu */}
            {showNewQuarryForm && (
              <Card className="p-6 border-2 border-amber-200 bg-amber-50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-amber-900">Yeni Ocak Ekle</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowNewQuarryForm(false)}
                  >
                    ✕
                  </Button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ocak Adı *
                    </label>
                    <input
                      type="text"
                      value={newQuarryData.name}
                      onChange={(e) => setNewQuarryData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Örn: Afyon Beyaz Mermer Ocağı"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Açık Adres *
                    </label>
                    <textarea
                      value={newQuarryData.address}
                      onChange={(e) => setNewQuarryData(prev => ({ ...prev, address: e.target.value }))}
                      placeholder="Ocağın detaylı adresi"
                      rows={2}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Google Maps Linki
                    </label>
                    <input
                      type="url"
                      value={newQuarryData.googleMapsLink}
                      onChange={(e) => setNewQuarryData(prev => ({ ...prev, googleMapsLink: e.target.value }))}
                      placeholder="https://maps.google.com/..."
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ocak Sahibi Firma *
                    </label>
                    <input
                      type="text"
                      value={newQuarryData.owner}
                      onChange={(e) => setNewQuarryData(prev => ({ ...prev, owner: e.target.value }))}
                      placeholder="Firma adı"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Çıkan Taş Çeşitleri
                    </label>
                    {newQuarryData.stones.map((stone, index) => (
                      <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-2 p-2 bg-white rounded border">
                        <input
                          type="text"
                          value={stone.name}
                          onChange={(e) => {
                            const newStones = [...newQuarryData.stones]
                            newStones[index].name = e.target.value
                            setNewQuarryData(prev => ({ ...prev, stones: newStones }))
                          }}
                          placeholder="Taş adı"
                          className="p-2 border border-gray-300 rounded text-sm"
                        />
                        <select
                          value={stone.category}
                          onChange={(e) => {
                            const newStones = [...newQuarryData.stones]
                            newStones[index].category = e.target.value
                            setNewQuarryData(prev => ({ ...prev, stones: newStones }))
                          }}
                          className="p-2 border border-gray-300 rounded text-sm"
                        >
                          <option value="">Kategori</option>
                          <option value="Mermer">Mermer</option>
                          <option value="Traverten">Traverten</option>
                          <option value="Granit">Granit</option>
                          <option value="Oniks">Oniks</option>
                          <option value="Limestone">Limestone</option>
                        </select>
                        <div className="flex gap-1">
                          <input
                            type="text"
                            value={stone.description}
                            onChange={(e) => {
                              const newStones = [...newQuarryData.stones]
                              newStones[index].description = e.target.value
                              setNewQuarryData(prev => ({ ...prev, stones: newStones }))
                            }}
                            placeholder="Açıklama"
                            className="flex-1 p-2 border border-gray-300 rounded text-sm"
                          />
                          {newQuarryData.stones.length > 1 && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const newStones = newQuarryData.stones.filter((_, i) => i !== index)
                                setNewQuarryData(prev => ({ ...prev, stones: newStones }))
                              }}
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setNewQuarryData(prev => ({
                          ...prev,
                          stones: [...prev.stones, { name: '', category: '', description: '' }]
                        }))
                      }}
                      className="text-amber-700 border-amber-300"
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      Taş Ekle
                    </Button>
                  </div>

                  <div className="flex gap-3 pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setShowNewQuarryForm(false)}
                      className="flex-1"
                    >
                      İptal
                    </Button>
                    <Button
                      onClick={() => {
                        // Yeni ocak ekleme işlemi - 4. adıma geç
                        setShowNewQuarryForm(false)
                        setSelectedQuarry({
                          id: Date.now().toString(),
                          name: newQuarryData.name,
                          location: {
                            city: newQuarryData.city,
                            district: newQuarryData.district,
                            address: newQuarryData.address,
                            coordinates: { lat: 0, lng: 0 }
                          },
                          owner: newQuarryData.owner,
                          establishedYear: newQuarryData.establishedYear,
                          capacity: newQuarryData.capacity,
                          certifications: [],
                          contactInfo: {
                            phone: newQuarryData.phone,
                            email: newQuarryData.email
                          }
                        })
                        setSelectedStone(null)
                        setExistingProduct(null)
                        setCurrentStep(4) // Temel bilgiler adımına git
                      }}
                      disabled={!newQuarryData.name || !newQuarryData.address || !newQuarryData.owner}
                      className="flex-1 bg-amber-600 hover:bg-amber-700"
                    >
                      Ocak Ekle
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Taş Seçimi</h2>
              <p className="text-gray-600">
                Seçtiğiniz ocak: <strong>{selectedQuarry?.name}</strong>
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Bu ocaktan çıkan taşları görün ve üretmek istediğiniz taşı seçin
              </p>
            </div>

            {/* Taş Listesi */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {selectedQuarry && mockQuarryStones[selectedQuarry.id as keyof typeof mockQuarryStones]?.map((stone) => (
                <Card
                  key={stone.id}
                  className="overflow-hidden cursor-pointer hover:shadow-lg transition-all border-2 hover:border-amber-300"
                  onClick={() => handleStoneSelect(stone)}
                >
                  {/* Taş Resmi */}
                  <div className="aspect-video bg-gray-200 relative">
                    <img
                      src={stone.image}
                      alt={stone.name}
                      className="w-full h-full object-cover"
                    />
                    {stone.hasExistingProduct && (
                      <div className="absolute top-2 right-2">
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Sistemde Var
                        </Badge>
                      </div>
                    )}
                  </div>

                  {/* Taş Bilgileri */}
                  <div className="p-4">
                    <div className="space-y-2">
                      <h3 className="font-semibold text-lg text-gray-900">{stone.name}</h3>
                      <p className="text-sm text-gray-600">{stone.description}</p>

                      <div className="flex items-center justify-between">
                        <Badge variant="outline">{stone.category}</Badge>
                        {stone.hasExistingProduct && (
                          <span className="text-xs text-green-600 font-medium">
                            {stone.producerCount} Üretici
                          </span>
                        )}
                      </div>

                      {stone.hasExistingProduct ? (
                        <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded">
                          <p className="text-xs text-green-800">
                            Bu taş sistemde mevcut. Mevcut ürüne katılabilir veya farklı varyasyon ekleyebilirsiniz.
                          </p>
                        </div>
                      ) : (
                        <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
                          <p className="text-xs text-blue-800">
                            Bu taş için ilk ürünü siz ekleyeceksiniz.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Bu ocak için yeni ürün ekle */}
            <Card className="p-4 border-2 border-dashed border-blue-300 bg-blue-50">
              <div className="text-center">
                <h4 className="font-medium text-blue-800 mb-2">Bu Ocak İçin Yeni Ürün Ekle</h4>
                <p className="text-sm text-blue-700 mb-4">
                  Yukarıdaki taşlar dışında bu ocaktan çıkan farklı bir ürün eklemek istiyorsanız bu butonu kullanın.
                </p>
                <Button
                  variant="outline"
                  className="border-blue-400 text-blue-700 hover:bg-blue-100"
                  onClick={() => {
                    setSelectedStone(null)
                    setExistingProduct(null)
                    setCurrentStep(4) // Direkt temel bilgiler adımına git
                  }}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Yeni Ürün Ekle
                </Button>
              </div>
            </Card>

            {/* Geri Butonu */}
            <div className="flex justify-center">
              <Button
                variant="outline"
                onClick={() => setCurrentStep(1)}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Ocak Seçimine Dön
              </Button>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Ürün Kontrolü</h2>
              <p className="text-gray-600">
                Seçtiğiniz taş: <strong>{selectedStone?.name}</strong>
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Ocak: {selectedQuarry?.name}
              </p>
            </div>

            {existingProduct ? (
              <Card className="p-6 border-2 border-amber-200 bg-amber-50">
                <div className="flex items-start gap-4">
                  <CheckCircle className="w-8 h-8 text-amber-600 flex-shrink-0 mt-1" />
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-amber-900 mb-2">
                      Bu ocaktan zaten ürün eklenmiş!
                    </h3>
                    <p className="text-amber-800 mb-4">
                      <strong>{existingProduct.name}</strong> ürünü bu ocak için zaten sistemde mevcut. 
                      Bu ürünü siz de üretiyorsanız, mevcut ürüne katılabilir ve sadece fiyat listesi 
                      ekleyebilirsiniz.
                    </p>
                    
                    <div className="bg-white p-4 rounded-lg border border-amber-200 mb-4">
                      <h4 className="font-medium text-gray-900 mb-2">Mevcut Ürün Detayları:</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        <p><strong>Kategori:</strong> {existingProduct.category}</p>
                        <p><strong>Açıklama:</strong> {existingProduct.description}</p>
                        <p><strong>Mevcut Üretici Sayısı:</strong> {existingProduct.producers.length}</p>
                      </div>
                    </div>

                    <div className="flex gap-3">
                      <Button
                        onClick={handleContinueWithExisting}
                        className="bg-amber-600 hover:bg-amber-700"
                      >
                        Mevcut Ürüne Katıl
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleCreateNewProduct}
                        className="border-gray-300"
                      >
                        Farklı Ürün Ekle
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            ) : (
              <Card className="p-6 border-2 border-green-200 bg-green-50">
                <div className="flex items-start gap-4">
                  <Plus className="w-8 h-8 text-green-600 flex-shrink-0 mt-1" />
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-green-900 mb-2">
                      Bu ocaktan henüz ürün eklenmemiş
                    </h3>
                    <p className="text-green-800 mb-4">
                      Bu ocak için ilk ürünü siz ekleyeceksiniz. Ürün detaylarını tam olarak 
                      girmeniz gerekecek.
                    </p>
                    
                    <Button
                      onClick={handleCreateNewProduct}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      Yeni Ürün Ekle
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Temel Bilgiler</h2>
              <p className="text-gray-600">
                Ürününüzün temel bilgilerini girin
              </p>
            </div>

            <Card className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Package className="w-5 h-5 text-amber-600" />
                <h3 className="text-lg font-semibold">Ürün Bilgileri</h3>
              </div>

              <div className="space-y-4">
                {/* Ürün Adı */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ürün Adı *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Örn: Afyon Beyaz Mermer Premium"
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    required
                  />
                </div>

                {/* Kategori */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Kategori *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    required
                  >
                    <option value="">Kategori Seçin</option>
                    <option value="Mermer">Mermer</option>
                    <option value="Traverten">Traverten</option>
                    <option value="Granit">Granit</option>
                    <option value="Oniks">Oniks</option>
                    <option value="Limestone">Limestone</option>
                  </select>
                </div>

                {/* Menşei */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Menşei
                  </label>
                  <input
                    type="text"
                    value={formData.origin}
                    onChange={(e) => setFormData(prev => ({ ...prev, origin: e.target.value }))}
                    placeholder="Örn: Afyon, Türkiye"
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  />
                </div>

                {/* Açıklama */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ürün Açıklaması *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Ürününüzün detaylı açıklamasını yazın..."
                    rows={4}
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    required
                  />
                </div>

                {/* Durum */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ürün Durumu
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  >
                    <option value="draft">Taslak</option>
                    <option value="active">Aktif (Admin Onayına Gönder)</option>
                    <option value="inactive">Pasif</option>
                  </select>
                  <p className="text-sm text-gray-500 mt-1">
                    Aktif seçerseniz ürün admin onayına gönderilir ve onaylandıktan sonra müşteriler tarafından görülebilir olur.
                  </p>
                </div>
              </div>

              {/* Navigation */}
              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(3)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Geri
                </Button>
                <Button
                  onClick={() => setCurrentStep(5)}
                  disabled={!formData.name || !formData.category || !formData.description}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  Devam Et
                </Button>
              </div>
            </Card>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Medya Yükleme</h2>
              <p className="text-gray-600">
                Ürününüzün resim, video ve dokümantasyonunu yükleyin
              </p>
            </div>

            <div className="space-y-6">
              {/* Kapak Resmi */}
              <Card className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Upload className="w-5 h-5 text-amber-600" />
                  <h3 className="text-lg font-semibold">Kapak Resmi *</h3>
                </div>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {formData.media.coverImage?.url ? (
                    <div className="space-y-4">
                      <img
                        src={formData.media.coverImage.url}
                        alt="Kapak resmi"
                        className="max-w-xs mx-auto rounded-lg"
                      />
                      <Button
                        variant="outline"
                        onClick={() => setFormData(prev => ({
                          ...prev,
                          media: { ...prev.media, coverImage: undefined }
                        }))}
                      >
                        Değiştir
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                      <div>
                        <p className="text-lg font-medium text-gray-900">Kapak Resmi Yükleyin</p>
                        <p className="text-sm text-gray-500">PNG, JPG formatında, maksimum 5MB</p>
                      </div>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0]
                          if (file) {
                            const url = URL.createObjectURL(file)
                            setFormData(prev => ({
                              ...prev,
                              media: { ...prev.media, coverImage: { file, url } }
                            }))
                          }
                        }}
                        className="hidden"
                        id="cover-image"
                      />
                      <label
                        htmlFor="cover-image"
                        className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                      >
                        Dosya Seç
                      </label>
                    </div>
                  )}
                </div>
              </Card>

              {/* Plaka Resimleri */}
              <Card className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Upload className="w-5 h-5 text-amber-600" />
                  <h3 className="text-lg font-semibold">Plaka Resimleri (3 adet)</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[0, 1, 2].map((index) => (
                    <div key={index} className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                      {formData.media.slabImages[index]?.url ? (
                        <div className="space-y-2">
                          <img
                            src={formData.media.slabImages[index].url}
                            alt={`Plaka resmi ${index + 1}`}
                            className="w-full h-32 object-cover rounded"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const newSlabImages = [...formData.media.slabImages]
                              newSlabImages.splice(index, 1)
                              setFormData(prev => ({
                                ...prev,
                                media: { ...prev.media, slabImages: newSlabImages }
                              }))
                            }}
                          >
                            Kaldır
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                          <p className="text-sm text-gray-600">Plaka {index + 1}</p>
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0]
                              if (file) {
                                const url = URL.createObjectURL(file)
                                const newSlabImages = [...formData.media.slabImages]
                                newSlabImages[index] = { file, url }
                                setFormData(prev => ({
                                  ...prev,
                                  media: { ...prev.media, slabImages: newSlabImages }
                                }))
                              }
                            }}
                            className="hidden"
                            id={`slab-image-${index}`}
                          />
                          <label
                            htmlFor={`slab-image-${index}`}
                            className="inline-flex items-center px-3 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                          >
                            Seç
                          </label>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </Card>

              {/* Mokap Resimleri */}
              <Card className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Upload className="w-5 h-5 text-amber-600" />
                  <h3 className="text-lg font-semibold">Mokap Resimleri (3 adet)</h3>
                  <span className="text-sm text-gray-500">- Ürünün yerleştirilmiş hali</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[0, 1, 2].map((index) => (
                    <div key={index} className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                      {formData.media.mockupImages[index]?.url ? (
                        <div className="space-y-2">
                          <img
                            src={formData.media.mockupImages[index].url}
                            alt={`Mokap resmi ${index + 1}`}
                            className="w-full h-32 object-cover rounded"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const newMockupImages = [...formData.media.mockupImages]
                              newMockupImages.splice(index, 1)
                              setFormData(prev => ({
                                ...prev,
                                media: { ...prev.media, mockupImages: newMockupImages }
                              }))
                            }}
                          >
                            Kaldır
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                          <p className="text-sm text-gray-600">Mokap {index + 1}</p>
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0]
                              if (file) {
                                const url = URL.createObjectURL(file)
                                const newMockupImages = [...formData.media.mockupImages]
                                newMockupImages[index] = { file, url }
                                setFormData(prev => ({
                                  ...prev,
                                  media: { ...prev.media, mockupImages: newMockupImages }
                                }))
                              }
                            }}
                            className="hidden"
                            id={`mockup-image-${index}`}
                          />
                          <label
                            htmlFor={`mockup-image-${index}`}
                            className="inline-flex items-center px-3 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                          >
                            Seç
                          </label>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </Card>

              {/* Render Resimleri */}
              <Card className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Upload className="w-5 h-5 text-amber-600" />
                  <h3 className="text-lg font-semibold">Render Resimleri (3 adet)</h3>
                  <span className="text-sm text-gray-500">- 3D render görüntüleri</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[0, 1, 2].map((index) => (
                    <div key={index} className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                      {formData.media.renderImages?.[index]?.url ? (
                        <div className="space-y-2">
                          <img
                            src={formData.media.renderImages[index].url}
                            alt={`Render resmi ${index + 1}`}
                            className="w-full h-32 object-cover rounded"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const newRenderImages = [...(formData.media.renderImages || [])]
                              newRenderImages.splice(index, 1)
                              setFormData(prev => ({
                                ...prev,
                                media: { ...prev.media, renderImages: newRenderImages }
                              }))
                            }}
                          >
                            Kaldır
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                          <p className="text-sm text-gray-600">Render {index + 1}</p>
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0]
                              if (file) {
                                const url = URL.createObjectURL(file)
                                const newRenderImages = [...(formData.media.renderImages || [])]
                                newRenderImages[index] = { file, url }
                                setFormData(prev => ({
                                  ...prev,
                                  media: { ...prev.media, renderImages: newRenderImages }
                                }))
                              }
                            }}
                            className="hidden"
                            id={`render-image-${index}`}
                          />
                          <label
                            htmlFor={`render-image-${index}`}
                            className="inline-flex items-center px-3 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                          >
                            Seç
                          </label>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </Card>

              {/* Video */}
              <Card className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Upload className="w-5 h-5 text-amber-600" />
                  <h3 className="text-lg font-semibold">Ürün Videosu (10 saniye, sessiz)</h3>
                </div>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {formData.media.video?.url ? (
                    <div className="space-y-4">
                      <video
                        src={formData.media.video.url}
                        controls
                        className="max-w-xs mx-auto rounded-lg"
                        muted
                      />
                      <Button
                        variant="outline"
                        onClick={() => setFormData(prev => ({
                          ...prev,
                          media: { ...prev.media, video: undefined }
                        }))}
                      >
                        Değiştir
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                      <div>
                        <p className="text-lg font-medium text-gray-900">Video Yükleyin</p>
                        <p className="text-sm text-gray-500">MP4 formatında, maksimum 10 saniye, 10MB</p>
                      </div>
                      <input
                        type="file"
                        accept="video/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0]
                          if (file) {
                            const url = URL.createObjectURL(file)
                            setFormData(prev => ({
                              ...prev,
                              media: { ...prev.media, video: { file, url } }
                            }))
                          }
                        }}
                        className="hidden"
                        id="product-video"
                      />
                      <label
                        htmlFor="product-video"
                        className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                      >
                        Video Seç
                      </label>
                    </div>
                  )}
                </div>
              </Card>

              {/* Navigation */}
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(4)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Geri
                </Button>
                <Button
                  onClick={() => setCurrentStep(6)}
                  disabled={!formData.media.coverImage}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  Devam Et
                </Button>
              </div>
            </div>
          </div>
        )

      case 6:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Teknik Özellikler</h2>
              <p className="text-gray-600">Ürününüzün teknik detaylarını girin</p>
            </div>

            <Card className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Ruler className="w-5 h-5 text-amber-600" />
                <h3 className="text-lg font-semibold">Standart Özellikler</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Yoğunluk (kg/m³)</label>
                  <input
                    type="text"
                    value={formData.technicalSpecs.density || ''}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      technicalSpecs: { ...prev.technicalSpecs, density: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Su Emme Oranı (%)</label>
                  <input
                    type="text"
                    value={formData.technicalSpecs.waterAbsorption || ''}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      technicalSpecs: { ...prev.technicalSpecs, waterAbsorption: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Basınç Dayanımı (MPa)</label>
                  <input
                    type="text"
                    value={formData.technicalSpecs.compressiveStrength || ''}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      technicalSpecs: { ...prev.technicalSpecs, compressiveStrength: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Eğilme Dayanımı (MPa)</label>
                  <input
                    type="text"
                    value={formData.technicalSpecs.flexuralStrength || ''}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      technicalSpecs: { ...prev.technicalSpecs, flexuralStrength: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                  />
                </div>
              </div>

              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(5)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Geri
                </Button>
                <Button
                  onClick={() => setCurrentStep(7)}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  Devam Et
                </Button>
              </div>
            </Card>
          </div>
        )

      case 7:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Ebatlı Fiyat Listesi</h2>
              <p className="text-gray-600">Boyutlara göre fiyatlandırma tablosu</p>
            </div>

            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-5 h-5 text-amber-600" />
                  <h3 className="text-lg font-semibold">Ebatlı Ürün Fiyat Listesi</h3>
                </div>
                <Button
                  onClick={() => {
                    const newPrice = {
                      id: Date.now().toString(),
                      thickness: 2,
                      width: 60,
                      length: 60,
                      surfaceFinish: 'Ham',
                      packaging: 'Paletüstü',
                      delivery: 'Fabrika Teslim',
                      price: 0,
                      currency: 'USD'
                    }
                    setFormData(prev => ({
                      ...prev,
                      dimensionPrices: [...prev.dimensionPrices, newPrice]
                    }))
                  }}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Ebat Ekle
                </Button>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full min-w-[1200px] border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-12">Sıra</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-20">Kalınlık (cm)</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-20">En (cm)</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-20">Boy (cm)</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-24">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-24">Ambalaj</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-24">Teslimat</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-24">Fiyat (m²)</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-20">Para Birimi</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-16">İşlem</th>
                    </tr>
                  </thead>
                  <tbody>
                    {formData.dimensionPrices.map((price, index) => (
                      <tr key={price.id} className="hover:bg-gray-50">
                        <td className="border border-gray-300 px-2 py-2 text-xs text-center">{index + 1}</td>
                        <td className="border border-gray-300 px-2 py-2">
                          <input
                            type="number"
                            value={price.thickness}
                            onChange={(e) => {
                              const newPrices = [...formData.dimensionPrices]
                              newPrices[index].thickness = Number(e.target.value)
                              setFormData(prev => ({ ...prev, dimensionPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          />
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <input
                            type="number"
                            value={price.width}
                            onChange={(e) => {
                              const newPrices = [...formData.dimensionPrices]
                              newPrices[index].width = Number(e.target.value)
                              setFormData(prev => ({ ...prev, dimensionPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          />
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <input
                            type="number"
                            value={price.length}
                            onChange={(e) => {
                              const newPrices = [...formData.dimensionPrices]
                              newPrices[index].length = Number(e.target.value)
                              setFormData(prev => ({ ...prev, dimensionPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          />
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <select
                            value={price.surfaceFinish}
                            onChange={(e) => {
                              const newPrices = [...formData.dimensionPrices]
                              newPrices[index].surfaceFinish = e.target.value
                              setFormData(prev => ({ ...prev, dimensionPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          >
                            <option value="Ham">Ham</option>
                            <option value="Honlu">Honlu</option>
                            <option value="Cilalı">Cilalı</option>
                            <option value="Fırçalı">Fırçalı</option>
                            <option value="Kumlama">Kumlama</option>
                            <option value="Yakma">Yakma</option>
                            <option value="Eskitme">Eskitme</option>
                          </select>
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <select
                            value={price.packaging}
                            onChange={(e) => {
                              const newPrices = [...formData.dimensionPrices]
                              newPrices[index].packaging = e.target.value
                              setFormData(prev => ({ ...prev, dimensionPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          >
                            <option value="Paletüstü">Paletüstü</option>
                            <option value="Kasalı">Kasalı</option>
                            <option value="Bandıllı">Bandıllı</option>
                          </select>
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <select
                            value={price.delivery}
                            onChange={(e) => {
                              const newPrices = [...formData.dimensionPrices]
                              newPrices[index].delivery = e.target.value
                              setFormData(prev => ({ ...prev, dimensionPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          >
                            <option value="Fabrika Teslim">Fabrika Teslim</option>
                            <option value="Liman">Liman</option>
                            <option value="FOB">FOB</option>
                          </select>
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <input
                            type="number"
                            step="0.01"
                            value={price.price}
                            onChange={(e) => {
                              const newPrices = [...formData.dimensionPrices]
                              newPrices[index].price = Number(e.target.value)
                              setFormData(prev => ({ ...prev, dimensionPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          />
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <select
                            value={price.currency}
                            onChange={(e) => {
                              const newPrices = [...formData.dimensionPrices]
                              newPrices[index].currency = e.target.value
                              setFormData(prev => ({ ...prev, dimensionPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          >
                            <option value="USD">USD</option>
                            <option value="EUR">EUR</option>
                            <option value="TRY">TRY</option>
                          </select>
                        </td>
                        <td className="border border-gray-300 px-2 py-2 text-center">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const newPrices = formData.dimensionPrices.filter((_, i) => i !== index)
                              setFormData(prev => ({ ...prev, dimensionPrices: newPrices }))
                            }}
                            className="text-red-600 hover:text-red-700 p-1"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {formData.dimensionPrices.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>Henüz ebat eklenmemiş. "Ebat Ekle" butonunu kullanarak ebat ekleyin.</p>
                </div>
              )}

              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(6)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Geri
                </Button>
                <Button
                  onClick={() => setCurrentStep(8)}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  Devam Et
                </Button>
              </div>
            </Card>
          </div>
        )

      case 8:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Yüzey İşlemi Fiyatları</h2>
              <p className="text-gray-600">İşlem türlerine göre ek fiyatlar</p>
            </div>

            <Card className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Palette className="w-5 h-5 text-amber-600" />
                <h3 className="text-lg font-semibold">Ebatlı Ürün Yüzey İşlemi Fiyat Listesi</h3>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Sıra</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Yaparım/Yapamam</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Fiyat</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Para Birimi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {['Honlu', 'Cilalı', 'Fırçalı', 'Kumlama', 'Yakma', 'Eskitme', 'Dolgu'].map((finish, index) => {
                      const existingPrice = formData.dimensionSurfaceFinishPrices.find(p => p.surfaceFinish === finish)
                      const canDo = existingPrice?.canDo ?? true

                      return (
                        <tr key={finish} className="hover:bg-gray-50">
                          <td className="border border-gray-300 px-3 py-2 text-sm">{index + 1}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm font-medium">{finish}</td>
                          <td className="border border-gray-300 px-3 py-2">
                            <select
                              value={canDo ? 'yaparim' : 'yapamam'}
                              onChange={(e) => {
                                const newCanDo = e.target.value === 'yaparim'
                                const newPrices = [...formData.dimensionSurfaceFinishPrices]
                                const existingIndex = newPrices.findIndex(p => p.surfaceFinish === finish)

                                if (existingIndex >= 0) {
                                  newPrices[existingIndex] = { ...newPrices[existingIndex], canDo: newCanDo }
                                } else {
                                  newPrices.push({
                                    id: Date.now().toString(),
                                    surfaceFinish: finish,
                                    canDo: newCanDo,
                                    price: 0,
                                    currency: 'USD'
                                  })
                                }
                                setFormData(prev => ({ ...prev, dimensionSurfaceFinishPrices: newPrices }))
                              }}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                            >
                              <option value="yaparim">Yaparım</option>
                              <option value="yapamam">Yapamam</option>
                            </select>
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <input
                              type="number"
                              step="0.01"
                              value={existingPrice?.price || 0}
                              disabled={!canDo}
                              onChange={(e) => {
                                const newPrices = [...formData.dimensionSurfaceFinishPrices]
                                const existingIndex = newPrices.findIndex(p => p.surfaceFinish === finish)

                                if (existingIndex >= 0) {
                                  newPrices[existingIndex] = { ...newPrices[existingIndex], price: Number(e.target.value) }
                                } else {
                                  newPrices.push({
                                    id: Date.now().toString(),
                                    surfaceFinish: finish,
                                    canDo: true,
                                    price: Number(e.target.value),
                                    currency: 'USD'
                                  })
                                }
                                setFormData(prev => ({ ...prev, dimensionSurfaceFinishPrices: newPrices }))
                              }}
                              className={`w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500 ${!canDo ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <select
                              value={existingPrice?.currency || 'USD'}
                              disabled={!canDo}
                              onChange={(e) => {
                                const newPrices = [...formData.dimensionSurfaceFinishPrices]
                                const existingIndex = newPrices.findIndex(p => p.surfaceFinish === finish)

                                if (existingIndex >= 0) {
                                  newPrices[existingIndex] = { ...newPrices[existingIndex], currency: e.target.value }
                                } else {
                                  newPrices.push({
                                    id: Date.now().toString(),
                                    surfaceFinish: finish,
                                    canDo: true,
                                    price: 0,
                                    currency: e.target.value
                                  })
                                }
                                setFormData(prev => ({ ...prev, dimensionSurfaceFinishPrices: newPrices }))
                              }}
                              className={`w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500 ${!canDo ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                            >
                              <option value="USD">USD</option>
                              <option value="EUR">EUR</option>
                              <option value="TRY">TRY</option>
                            </select>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>

              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(7)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Geri
                </Button>
                <Button
                  onClick={() => setCurrentStep(9)}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  Devam Et
                </Button>
              </div>
            </Card>
          </div>
        )

      case 9:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Plaka Fiyat Listesi</h2>
              <p className="text-gray-600">Plaka ürünleri için fiyatlandırma</p>
            </div>

            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Box className="w-5 h-5 text-amber-600" />
                  <h3 className="text-lg font-semibold">Plaka Ürün Fiyat Listesi</h3>
                </div>
                <Button
                  onClick={() => {
                    const newPrice = {
                      id: Date.now().toString(),
                      thickness: 2,
                      surfaceFinish: 'Ham',
                      packaging: 'Paletüstü',
                      delivery: 'Fabrika Teslim',
                      price: 0,
                      currency: 'USD'
                    }
                    setFormData(prev => ({
                      ...prev,
                      slabPrices: [...prev.slabPrices, newPrice]
                    }))
                  }}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Plaka Ekle
                </Button>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full min-w-[1000px] border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-12">Sıra</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-24">Kalınlık (cm)</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-28">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-24">Ambalaj</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-28">Teslimat</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-24">Fiyat (m²)</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-20">Para Birimi</th>
                      <th className="border border-gray-300 px-2 py-2 text-left text-xs font-medium text-gray-700 w-16">İşlem</th>
                    </tr>
                  </thead>
                  <tbody>
                    {formData.slabPrices.map((price, index) => (
                      <tr key={price.id} className="hover:bg-gray-50">
                        <td className="border border-gray-300 px-2 py-2 text-xs text-center">{index + 1}</td>
                        <td className="border border-gray-300 px-2 py-2">
                          <input
                            type="number"
                            value={price.thickness}
                            onChange={(e) => {
                              const newPrices = [...formData.slabPrices]
                              newPrices[index].thickness = Number(e.target.value)
                              setFormData(prev => ({ ...prev, slabPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          />
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <select
                            value={price.surfaceFinish}
                            onChange={(e) => {
                              const newPrices = [...formData.slabPrices]
                              newPrices[index].surfaceFinish = e.target.value
                              setFormData(prev => ({ ...prev, slabPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          >
                            <option value="Ham">Ham</option>
                            <option value="Honlu">Honlu</option>
                            <option value="Cilalı">Cilalı</option>
                            <option value="Fırçalı">Fırçalı</option>
                            <option value="Kumlama">Kumlama</option>
                            <option value="Yakma">Yakma</option>
                            <option value="Eskitme">Eskitme</option>
                          </select>
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <select
                            value={price.packaging}
                            onChange={(e) => {
                              const newPrices = [...formData.slabPrices]
                              newPrices[index].packaging = e.target.value
                              setFormData(prev => ({ ...prev, slabPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          >
                            <option value="Paletüstü">Paletüstü</option>
                            <option value="Kasalı">Kasalı</option>
                            <option value="Bandıllı">Bandıllı</option>
                          </select>
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <select
                            value={price.delivery}
                            onChange={(e) => {
                              const newPrices = [...formData.slabPrices]
                              newPrices[index].delivery = e.target.value
                              setFormData(prev => ({ ...prev, slabPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          >
                            <option value="Fabrika Teslim">Fabrika Teslim</option>
                            <option value="Liman">Liman</option>
                            <option value="FOB">FOB</option>
                          </select>
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <input
                            type="number"
                            step="0.01"
                            value={price.price}
                            onChange={(e) => {
                              const newPrices = [...formData.slabPrices]
                              newPrices[index].price = Number(e.target.value)
                              setFormData(prev => ({ ...prev, slabPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          />
                        </td>
                        <td className="border border-gray-300 px-2 py-2">
                          <select
                            value={price.currency}
                            onChange={(e) => {
                              const newPrices = [...formData.slabPrices]
                              newPrices[index].currency = e.target.value
                              setFormData(prev => ({ ...prev, slabPrices: newPrices }))
                            }}
                            className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                          >
                            <option value="USD">USD</option>
                            <option value="EUR">EUR</option>
                            <option value="TRY">TRY</option>
                          </select>
                        </td>
                        <td className="border border-gray-300 px-2 py-2 text-center">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const newPrices = formData.slabPrices.filter((_, i) => i !== index)
                              setFormData(prev => ({ ...prev, slabPrices: newPrices }))
                            }}
                            className="text-red-600 hover:text-red-700 p-1"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {formData.slabPrices.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>Henüz plaka eklenmemiş. "Plaka Ekle" butonunu kullanarak plaka ekleyin.</p>
                </div>
              )}

              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(8)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Geri
                </Button>
                <Button
                  onClick={() => setCurrentStep(10)}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  Devam Et
                </Button>
              </div>
            </Card>
          </div>
        )

      case 10:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Plaka Yüzey İşlemi Fiyatları</h2>
              <p className="text-gray-600">Plaka işlem fiyatları</p>
            </div>

            <Card className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Palette className="w-5 h-5 text-amber-600" />
                <h3 className="text-lg font-semibold">Plaka Yüzey İşlemi Fiyat Listesi</h3>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Sıra</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Yüzey İşlemi</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Yaparım/Yapamam</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Fiyat</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Para Birimi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {['Honlu', 'Cilalı', 'Fırçalı', 'Kumlama', 'Yakma', 'Eskitme', 'Dolgu'].map((finish, index) => {
                      const existingPrice = formData.slabSurfaceFinishPrices.find(p => p.surfaceFinish === finish)
                      const canDo = existingPrice?.canDo ?? true

                      return (
                        <tr key={finish} className="hover:bg-gray-50">
                          <td className="border border-gray-300 px-3 py-2 text-sm">{index + 1}</td>
                          <td className="border border-gray-300 px-3 py-2 text-sm font-medium">{finish}</td>
                          <td className="border border-gray-300 px-3 py-2">
                            <select
                              value={canDo ? 'yaparim' : 'yapamam'}
                              onChange={(e) => {
                                const newCanDo = e.target.value === 'yaparim'
                                const newPrices = [...formData.slabSurfaceFinishPrices]
                                const existingIndex = newPrices.findIndex(p => p.surfaceFinish === finish)

                                if (existingIndex >= 0) {
                                  newPrices[existingIndex] = { ...newPrices[existingIndex], canDo: newCanDo }
                                } else {
                                  newPrices.push({
                                    id: Date.now().toString(),
                                    surfaceFinish: finish,
                                    canDo: newCanDo,
                                    price: 0,
                                    currency: 'USD'
                                  })
                                }
                                setFormData(prev => ({ ...prev, slabSurfaceFinishPrices: newPrices }))
                              }}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500"
                            >
                              <option value="yaparim">Yaparım</option>
                              <option value="yapamam">Yapamam</option>
                            </select>
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <input
                              type="number"
                              step="0.01"
                              value={existingPrice?.price || 0}
                              disabled={!canDo}
                              onChange={(e) => {
                                const newPrices = [...formData.slabSurfaceFinishPrices]
                                const existingIndex = newPrices.findIndex(p => p.surfaceFinish === finish)

                                if (existingIndex >= 0) {
                                  newPrices[existingIndex] = { ...newPrices[existingIndex], price: Number(e.target.value) }
                                } else {
                                  newPrices.push({
                                    id: Date.now().toString(),
                                    surfaceFinish: finish,
                                    canDo: true,
                                    price: Number(e.target.value),
                                    currency: 'USD'
                                  })
                                }
                                setFormData(prev => ({ ...prev, slabSurfaceFinishPrices: newPrices }))
                              }}
                              className={`w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500 ${!canDo ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <select
                              value={existingPrice?.currency || 'USD'}
                              disabled={!canDo}
                              onChange={(e) => {
                                const newPrices = [...formData.slabSurfaceFinishPrices]
                                const existingIndex = newPrices.findIndex(p => p.surfaceFinish === finish)

                                if (existingIndex >= 0) {
                                  newPrices[existingIndex] = { ...newPrices[existingIndex], currency: e.target.value }
                                } else {
                                  newPrices.push({
                                    id: Date.now().toString(),
                                    surfaceFinish: finish,
                                    canDo: true,
                                    price: 0,
                                    currency: e.target.value
                                  })
                                }
                                setFormData(prev => ({ ...prev, slabSurfaceFinishPrices: newPrices }))
                              }}
                              className={`w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-amber-500 ${!canDo ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                            >
                              <option value="USD">USD</option>
                              <option value="EUR">EUR</option>
                              <option value="TRY">TRY</option>
                            </select>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>

              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(9)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Geri
                </Button>
                <Button
                  onClick={() => setCurrentStep(11)}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  Devam Et
                </Button>
              </div>
            </Card>
          </div>
        )

      case 11:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Analiz Raporları</h2>
              <p className="text-gray-600">Taş analiz raporları ve belgeler</p>
            </div>

            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Upload className="w-5 h-5 text-amber-600" />
                  <h3 className="text-lg font-semibold">Analiz Raporları</h3>
                </div>
                <Button
                  onClick={() => {
                    const newReport = {
                      id: Date.now().toString(),
                      reportName: '',
                      laboratoryName: '',
                      reportDate: '',
                      density: '',
                      hardness: '',
                      waterAbsorption: '',
                      freezeThawResistance: '',
                      compressiveStrength: '',
                      flexuralStrength: '',
                      file: null,
                      url: ''
                    }
                    setFormData(prev => ({
                      ...prev,
                      analysisReports: [...prev.analysisReports, newReport]
                    }))
                  }}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Rapor Ekle
                </Button>
              </div>

              <div className="space-y-4">
                {formData.analysisReports.map((report, index) => (
                  <Card key={report.id} className="p-4 border-2 border-gray-200">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-md font-semibold">Analiz Raporu {index + 1}</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newReports = formData.analysisReports.filter((_, i) => i !== index)
                          setFormData(prev => ({ ...prev, analysisReports: newReports }))
                        }}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Rapor Adı *</label>
                        <input
                          type="text"
                          value={report.reportName}
                          onChange={(e) => {
                            const newReports = [...formData.analysisReports]
                            newReports[index].reportName = e.target.value
                            setFormData(prev => ({ ...prev, analysisReports: newReports }))
                          }}
                          placeholder="Örn: Afyon Beyaz Mermer Analiz Raporu"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Laboratuvar Adı</label>
                        <input
                          type="text"
                          value={report.laboratoryName}
                          onChange={(e) => {
                            const newReports = [...formData.analysisReports]
                            newReports[index].laboratoryName = e.target.value
                            setFormData(prev => ({ ...prev, analysisReports: newReports }))
                          }}
                          placeholder="Örn: TÜBİTAK MAM"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Rapor Tarihi</label>
                        <input
                          type="date"
                          value={report.reportDate}
                          onChange={(e) => {
                            const newReports = [...formData.analysisReports]
                            newReports[index].reportDate = e.target.value
                            setFormData(prev => ({ ...prev, analysisReports: newReports }))
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                        />
                      </div>
                    </div>

                    {/* Teknik Özellikler */}
                    <div className="mb-4">
                      <h5 className="text-sm font-semibold text-gray-700 mb-3">Teknik Özellikler</h5>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Yoğunluk (kg/m³)</label>
                          <input
                            type="text"
                            value={report.density}
                            onChange={(e) => {
                              const newReports = [...formData.analysisReports]
                              newReports[index].density = e.target.value
                              setFormData(prev => ({ ...prev, analysisReports: newReports }))
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Sertlik</label>
                          <input
                            type="text"
                            value={report.hardness}
                            onChange={(e) => {
                              const newReports = [...formData.analysisReports]
                              newReports[index].hardness = e.target.value
                              setFormData(prev => ({ ...prev, analysisReports: newReports }))
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Su Emme Oranı (%)</label>
                          <input
                            type="text"
                            value={report.waterAbsorption}
                            onChange={(e) => {
                              const newReports = [...formData.analysisReports]
                              newReports[index].waterAbsorption = e.target.value
                              setFormData(prev => ({ ...prev, analysisReports: newReports }))
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Donma-Çözülme Direnci</label>
                          <input
                            type="text"
                            value={report.freezeThawResistance}
                            onChange={(e) => {
                              const newReports = [...formData.analysisReports]
                              newReports[index].freezeThawResistance = e.target.value
                              setFormData(prev => ({ ...prev, analysisReports: newReports }))
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Basınç Dayanımı (MPa)</label>
                          <input
                            type="text"
                            value={report.compressiveStrength}
                            onChange={(e) => {
                              const newReports = [...formData.analysisReports]
                              newReports[index].compressiveStrength = e.target.value
                              setFormData(prev => ({ ...prev, analysisReports: newReports }))
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Eğilme Dayanımı (MPa)</label>
                          <input
                            type="text"
                            value={report.flexuralStrength}
                            onChange={(e) => {
                              const newReports = [...formData.analysisReports]
                              newReports[index].flexuralStrength = e.target.value
                              setFormData(prev => ({ ...prev, analysisReports: newReports }))
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Dosya Yükleme */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Rapor Dosyası (PDF)</label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        {report.url ? (
                          <div className="space-y-2">
                            <div className="flex items-center justify-center gap-2">
                              <Upload className="w-5 h-5 text-green-600" />
                              <span className="text-sm text-green-600">Dosya yüklendi</span>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const newReports = [...formData.analysisReports]
                                newReports[index].file = null
                                newReports[index].url = ''
                                setFormData(prev => ({ ...prev, analysisReports: newReports }))
                              }}
                            >
                              Değiştir
                            </Button>
                          </div>
                        ) : (
                          <div className="space-y-2">
                            <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                            <p className="text-sm text-gray-600">PDF dosyası yükleyin</p>
                            <input
                              type="file"
                              accept=".pdf"
                              onChange={(e) => {
                                const file = e.target.files?.[0]
                                if (file) {
                                  const url = URL.createObjectURL(file)
                                  const newReports = [...formData.analysisReports]
                                  newReports[index].file = file
                                  newReports[index].url = url
                                  setFormData(prev => ({ ...prev, analysisReports: newReports }))
                                }
                              }}
                              className="hidden"
                              id={`report-file-${index}`}
                            />
                            <label
                              htmlFor={`report-file-${index}`}
                              className="inline-flex items-center px-3 py-1 border border-gray-300 rounded text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                            >
                              Dosya Seç
                            </label>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              {formData.analysisReports.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>Henüz analiz raporu eklenmemiş. "Rapor Ekle" butonunu kullanarak rapor ekleyin.</p>
                </div>
              )}

              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(10)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Geri
                </Button>
                <Button
                  onClick={() => setCurrentStep(12)}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  Devam Et
                </Button>
              </div>
            </Card>
          </div>
        )

      case 12:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Ürün Onayı</h2>
              <p className="text-gray-600">
                Bilgilerinizi kontrol edin ve ürünü sisteme ekleyin
              </p>
            </div>

            <Card className="p-6">
              <div className="space-y-6">
                {/* Ürün Özeti */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Ürün Özeti</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Ürün Adı</label>
                      <p className="text-sm text-gray-900">{formData.name || 'Belirtilmemiş'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Kategori</label>
                      <p className="text-sm text-gray-900">{formData.category || 'Belirtilmemiş'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Menşei</label>
                      <p className="text-sm text-gray-900">{formData.origin || 'Belirtilmemiş'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Ürün Durumu</label>
                      <Badge
                        variant={formData.status === 'active' ? 'default' : 'secondary'}
                        className={formData.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                      >
                        {formData.status === 'active' ? 'Aktif' : formData.status === 'draft' ? 'Taslak' : 'Pasif'}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Durum Açıklaması */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-semibold text-blue-800 mb-1">Ürün Durumu Hakkında</h4>
                      {formData.status === 'active' ? (
                        <p className="text-sm text-blue-700">
                          <strong>Aktif</strong> durumda olan ürünler admin onayına gönderilir.
                          Onaylandıktan sonra müşterilere görünür hale gelir.
                        </p>
                      ) : (
                        <p className="text-sm text-blue-700">
                          <strong>{formData.status === 'draft' ? 'Taslak' : 'Pasif'}</strong> durumda olan ürünler
                          sadece sizin profilinizde görünür. İstediğiniz zaman düzenleyebilir ve aktif hale getirebilirsiniz.
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Medya Özeti */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Medya Dosyaları</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="text-center">
                      <div className={`w-12 h-12 mx-auto rounded-lg flex items-center justify-center ${
                        formData.media.coverImage ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
                      }`}>
                        <Upload className="w-6 h-6" />
                      </div>
                      <p className="mt-2">Kapak Resmi</p>
                      <p className={formData.media.coverImage ? 'text-green-600' : 'text-gray-400'}>
                        {formData.media.coverImage ? '✓' : '✗'}
                      </p>
                    </div>
                    <div className="text-center">
                      <div className={`w-12 h-12 mx-auto rounded-lg flex items-center justify-center ${
                        formData.media.slabImages.length > 0 ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
                      }`}>
                        <Upload className="w-6 h-6" />
                      </div>
                      <p className="mt-2">Plaka Resimleri</p>
                      <p className={formData.media.slabImages.length > 0 ? 'text-green-600' : 'text-gray-400'}>
                        {formData.media.slabImages.length} adet
                      </p>
                    </div>
                    <div className="text-center">
                      <div className={`w-12 h-12 mx-auto rounded-lg flex items-center justify-center ${
                        formData.media.mockupImages.length > 0 ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
                      }`}>
                        <Upload className="w-6 h-6" />
                      </div>
                      <p className="mt-2">Mokap Resimleri</p>
                      <p className={formData.media.mockupImages.length > 0 ? 'text-green-600' : 'text-gray-400'}>
                        {formData.media.mockupImages.length} adet
                      </p>
                    </div>
                    <div className="text-center">
                      <div className={`w-12 h-12 mx-auto rounded-lg flex items-center justify-center ${
                        formData.media.video ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
                      }`}>
                        <Upload className="w-6 h-6" />
                      </div>
                      <p className="mt-2">Video</p>
                      <p className={formData.media.video ? 'text-green-600' : 'text-gray-400'}>
                        {formData.media.video ? '✓' : '✗'}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Fiyat Listeleri Özeti */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Fiyat Listeleri</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="border rounded-lg p-3">
                      <h4 className="font-medium mb-2">Ebatlı Ürün Fiyatları</h4>
                      <p className="text-gray-600">{formData.dimensionPrices.length} adet ebat</p>
                    </div>
                    <div className="border rounded-lg p-3">
                      <h4 className="font-medium mb-2">Plaka Fiyatları</h4>
                      <p className="text-gray-600">{formData.slabPrices.length} adet plaka</p>
                    </div>
                    <div className="border rounded-lg p-3">
                      <h4 className="font-medium mb-2">Yüzey İşlemi (Ebatlı)</h4>
                      <p className="text-gray-600">{formData.dimensionSurfaceFinishPrices.length} işlem</p>
                    </div>
                    <div className="border rounded-lg p-3">
                      <h4 className="font-medium mb-2">Yüzey İşlemi (Plaka)</h4>
                      <p className="text-gray-600">{formData.slabSurfaceFinishPrices.length} işlem</p>
                    </div>
                  </div>
                </div>

                {/* Analiz Raporları Özeti */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Analiz Raporları</h3>
                  <p className="text-sm text-gray-600">{formData.analysisReports.length} adet analiz raporu</p>
                </div>

                {/* İşlem Butonları */}
                <div className="flex justify-between pt-6 border-t">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentStep(11)}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    Geri
                  </Button>
                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Taslak olarak kaydet
                        const draftData = { ...formData, status: 'draft' }
                        handleSubmitProduct(draftData, false)
                      }}
                      className="border-gray-300 text-gray-700 hover:bg-gray-50"
                    >
                      Taslak Kaydet
                    </Button>
                    <Button
                      onClick={() => {
                        // Duruma göre kaydet
                        handleSubmitProduct(formData, formData.status === 'active')
                      }}
                      className="bg-amber-600 hover:bg-amber-700"
                    >
                      {formData.status === 'active' ? 'Admin Onayına Gönder' : 'Ürünü Kaydet'}
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )

      default:
        return (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Bilinmeyen Adım
            </h3>
            <p className="text-gray-600">
              Bu adım bulunamadı.
            </p>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Geri
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">Yeni Ürün Ekle</h1>
            </div>
            
            <div className="text-sm text-gray-500">
              Adım {currentStep} / {steps.length}
            </div>
          </div>
        </div>
      </div>

      {/* Progress Steps - 2 Satır Düzeni */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          {/* İlk Satır - Adım 1-6 */}
          <div className="flex items-center justify-between mb-4">
            {steps.slice(0, 6).map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  currentStep >= step.id
                    ? 'bg-amber-600 border-amber-600 text-white'
                    : 'border-gray-300 text-gray-400'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    <span className="text-sm font-medium">{step.id}</span>
                  )}
                </div>
                <div className="ml-2 hidden lg:block">
                  <p className={`text-xs font-medium ${
                    currentStep >= step.id ? 'text-amber-600' : 'text-gray-400'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < 5 && (
                  <div className={`hidden sm:block w-8 lg:w-12 h-0.5 ml-2 lg:ml-4 ${
                    currentStep > step.id ? 'bg-amber-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>

          {/* İkinci Satır - Adım 7-12 */}
          <div className="flex items-center justify-between">
            {steps.slice(6, 12).map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  currentStep >= step.id
                    ? 'bg-amber-600 border-amber-600 text-white'
                    : 'border-gray-300 text-gray-400'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    <span className="text-sm font-medium">{step.id}</span>
                  )}
                </div>
                <div className="ml-2 hidden lg:block">
                  <p className={`text-xs font-medium ${
                    currentStep >= step.id ? 'text-amber-600' : 'text-gray-400'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < 5 && (
                  <div className={`hidden sm:block w-8 lg:w-12 h-0.5 ml-2 lg:ml-4 ${
                    currentStep > step.id ? 'bg-amber-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="h-full max-w-4xl mx-auto">
            <div className="h-full overflow-y-auto">
              {renderStepContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
