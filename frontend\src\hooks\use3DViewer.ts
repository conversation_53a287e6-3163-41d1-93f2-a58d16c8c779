/**
 * 3D Viewer Hook
 * Custom React hook for 3D viewer functionality
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  ViewerConfiguration, 
  Asset3D, 
  MaterialDefinition, 
  ViewerSession,
  ViewerError,
  UseViewerReturn,
  AssetQuality
} from '../types/3d';
import { threeDApi } from '../services/3dApi';

interface Use3DViewerOptions {
  productId: string;
  userId?: string;
  autoStart?: boolean;
  quality?: AssetQuality;
  onLoad?: () => void;
  onError?: (error: ViewerError) => void;
  onSessionUpdate?: (session: ViewerSession) => void;
}

export const use3DViewer = (options: Use3DViewerOptions): UseViewerReturn => {
  const {
    productId,
    userId,
    autoStart = true,
    quality = AssetQuality.HIGH,
    onLoad,
    onError,
    onSessionUpdate
  } = options;

  const [configuration, setConfiguration] = useState<ViewerConfiguration | null>(null);
  const [assets, setAssets] = useState<Asset3D[]>([]);
  const [materials, setMaterials] = useState<MaterialDefinition[]>([]);
  const [session, setSession] = useState<ViewerSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<ViewerError | null>(null);

  const sessionUpdateInterval = useRef<NodeJS.Timeout | null>(null);
  const sessionStartTime = useRef<number>(0);
  const interactionCount = useRef<number>(0);
  const zoomCount = useRef<number>(0);
  const rotationCount = useRef<number>(0);
  const annotationViews = useRef<number>(0);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Load viewer configuration and assets
   */
  const loadAssets = useCallback(async () => {
    try {
      setIsLoading(true);
      clearError();

      // Load configuration and assets in parallel
      const [configResponse, assetsData, materialsData] = await Promise.all([
        threeDApi.getViewerConfiguration(productId),
        threeDApi.getProductAssets(productId),
        threeDApi.getProductMaterials(productId)
      ]);

      setConfiguration(configResponse.configuration);
      setAssets(assetsData);
      setMaterials(materialsData);

      onLoad?.();
    } catch (err) {
      const viewerError: ViewerError = {
        code: 'LOAD_ERROR',
        message: err instanceof Error ? err.message : 'Failed to load 3D viewer data',
        details: err,
        recoverable: true
      };
      setError(viewerError);
      onError?.(viewerError);
    } finally {
      setIsLoading(false);
    }
  }, [productId, onLoad, onError, clearError]);

  /**
   * Start viewer session
   */
  const startSession = useCallback(async () => {
    try {
      const deviceInfo = {
        userAgent: navigator.userAgent,
        deviceType: threeDApi.getDeviceType(),
        screenResolution: threeDApi.getScreenResolution()
      };

      const response = await threeDApi.startViewerSession(productId, deviceInfo);
      
      const newSession: ViewerSession = {
        id: response.sessionId,
        sessionId: response.sessionId,
        productId,
        userId,
        viewDuration: 0,
        interactionCount: 0,
        zoomCount: 0,
        rotationCount: 0,
        annotationViews: 0,
        userAgent: deviceInfo.userAgent,
        deviceType: deviceInfo.deviceType,
        screenResolution: deviceInfo.screenResolution,
        startedAt: new Date()
      };

      setSession(newSession);
      sessionStartTime.current = Date.now();

      // Start periodic session updates
      sessionUpdateInterval.current = setInterval(() => {
        updateSessionMetrics();
      }, 5000); // Update every 5 seconds

    } catch (err) {
      const viewerError: ViewerError = {
        code: 'SESSION_ERROR',
        message: err instanceof Error ? err.message : 'Failed to start viewer session',
        details: err,
        recoverable: true
      };
      setError(viewerError);
      onError?.(viewerError);
    }
  }, [productId, userId, onError]);

  /**
   * Update session metrics
   */
  const updateSessionMetrics = useCallback(async () => {
    if (!session) return;

    const currentTime = Date.now();
    const viewDuration = Math.floor((currentTime - sessionStartTime.current) / 1000);

    const updates = {
      viewDuration,
      interactionCount: interactionCount.current,
      zoomCount: zoomCount.current,
      rotationCount: rotationCount.current,
      annotationViews: annotationViews.current
    };

    try {
      await threeDApi.updateViewerSession(session.sessionId, updates);
      
      const updatedSession = { ...session, ...updates };
      setSession(updatedSession);
      onSessionUpdate?.(updatedSession);
    } catch (err) {
      console.warn('Failed to update session metrics:', err);
    }
  }, [session, onSessionUpdate]);

  /**
   * Update session with specific data
   */
  const updateSession = useCallback(async (updates: Partial<ViewerSession>) => {
    if (!session) return;

    try {
      await threeDApi.updateViewerSession(session.sessionId, updates);
      
      const updatedSession = { ...session, ...updates };
      setSession(updatedSession);
      onSessionUpdate?.(updatedSession);
    } catch (err) {
      console.warn('Failed to update session:', err);
    }
  }, [session, onSessionUpdate]);

  /**
   * End viewer session
   */
  const endSession = useCallback(async () => {
    if (!session) return;

    try {
      // Final metrics update
      await updateSessionMetrics();
      
      // End session
      await threeDApi.endViewerSession(session.sessionId);
      
      // Clear interval
      if (sessionUpdateInterval.current) {
        clearInterval(sessionUpdateInterval.current);
        sessionUpdateInterval.current = null;
      }

      setSession(null);
    } catch (err) {
      console.warn('Failed to end session:', err);
    }
  }, [session, updateSessionMetrics]);

  /**
   * Track user interactions
   */
  const trackInteraction = useCallback((type: 'zoom' | 'rotation' | 'annotation') => {
    interactionCount.current++;
    
    switch (type) {
      case 'zoom':
        zoomCount.current++;
        break;
      case 'rotation':
        rotationCount.current++;
        break;
      case 'annotation':
        annotationViews.current++;
        break;
    }
  }, []);

  /**
   * Get optimal asset quality based on device capabilities
   */
  const getOptimalQuality = useCallback((): AssetQuality => {
    const capabilities = threeDApi.detectViewerCapabilities();
    const deviceType = threeDApi.getDeviceType();
    
    if (deviceType === 'mobile') {
      return AssetQuality.LOW;
    } else if (deviceType === 'tablet') {
      return AssetQuality.MEDIUM;
    } else if (capabilities.webgl2 && capabilities.maxTextureSize >= 4096) {
      return AssetQuality.HIGH;
    } else {
      return AssetQuality.MEDIUM;
    }
  }, []);

  /**
   * Check if assets can be loaded on current device
   */
  const canLoadAssets = useCallback((): boolean => {
    if (!threeDApi.isWebGLSupported()) {
      setError({
        code: 'WEBGL_NOT_SUPPORTED',
        message: 'WebGL is not supported on this device',
        recoverable: false
      });
      return false;
    }

    const capabilities = threeDApi.detectViewerCapabilities();
    const totalMemory = assets.reduce((sum, asset) => 
      sum + threeDApi.estimateMemoryUsage(asset), 0
    );

    if (capabilities.deviceMemory && totalMemory > capabilities.deviceMemory * 100) {
      setError({
        code: 'INSUFFICIENT_MEMORY',
        message: 'Insufficient device memory to load 3D assets',
        recoverable: true
      });
      return false;
    }

    return true;
  }, [assets]);

  /**
   * Preload assets for better performance
   */
  const preloadAssets = useCallback(async (targetQuality?: AssetQuality) => {
    const qualityToUse = targetQuality || getOptimalQuality();
    
    try {
      const modelAssets = assets.filter(asset => asset.type === 'MODEL_3D');
      
      for (const asset of modelAssets) {
        if (threeDApi.canLoadAsset(asset)) {
          await threeDApi.preloadAsset(asset.id, qualityToUse);
        }
      }
    } catch (err) {
      console.warn('Failed to preload assets:', err);
    }
  }, [assets, getOptimalQuality]);

  /**
   * Auto-start session if enabled
   */
  useEffect(() => {
    if (autoStart && productId && !session) {
      loadAssets().then(() => {
        if (canLoadAssets()) {
          startSession();
        }
      });
    }
  }, [autoStart, productId, session, loadAssets, startSession, canLoadAssets]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (sessionUpdateInterval.current) {
        clearInterval(sessionUpdateInterval.current);
      }
      if (session) {
        endSession();
      }
    };
  }, [session, endSession]);

  /**
   * Track page visibility for session management
   */
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && session) {
        // Pause session updates when page is hidden
        if (sessionUpdateInterval.current) {
          clearInterval(sessionUpdateInterval.current);
          sessionUpdateInterval.current = null;
        }
      } else if (!document.hidden && session && !sessionUpdateInterval.current) {
        // Resume session updates when page becomes visible
        sessionUpdateInterval.current = setInterval(() => {
          updateSessionMetrics();
        }, 5000);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [session, updateSessionMetrics]);

  return {
    configuration,
    assets,
    materials,
    session,
    isLoading,
    error,
    startSession,
    endSession,
    updateSession,
    loadAssets,
    clearError,
    // Additional utility methods
    trackInteraction,
    getOptimalQuality,
    canLoadAssets,
    preloadAssets
  } as UseViewerReturn & {
    trackInteraction: (type: 'zoom' | 'rotation' | 'annotation') => void;
    getOptimalQuality: () => AssetQuality;
    canLoadAssets: () => boolean;
    preloadAssets: (quality?: AssetQuality) => Promise<void>;
  };
};

export default use3DViewer;
