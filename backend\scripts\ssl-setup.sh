#!/bin/bash

# SSL/TLS Setup Script for Natural Stone Marketplace
# This script sets up SSL certificates using Let's Encrypt

set -e

# Configuration
DOMAIN="yourdomain.com"
EMAIL="<EMAIL>"
WEBROOT="/var/www/html"
NGINX_CONFIG="/etc/nginx/sites-available/natural-stone-marketplace"
SSL_DIR="/etc/ssl/natural-stone-marketplace"

echo "🔐 Setting up SSL/TLS for Natural Stone Marketplace"
echo "Domain: $DOMAIN"
echo "Email: $EMAIL"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run this script as root"
    exit 1
fi

# Update system packages
echo "📦 Updating system packages..."
apt update && apt upgrade -y

# Install required packages
echo "📦 Installing required packages..."
apt install -y nginx certbot python3-certbot-nginx ufw

# Create SSL directory
echo "📁 Creating SSL directory..."
mkdir -p $SSL_DIR

# Configure UFW firewall
echo "🔥 Configuring firewall..."
ufw allow 'Nginx Full'
ufw allow ssh
ufw --force enable

# Create Nginx configuration
echo "⚙️ Creating Nginx configuration..."
cat > $NGINX_CONFIG << EOF
# Natural Stone Marketplace - Production Configuration

# Redirect HTTP to HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN www.$DOMAIN;
    
    # Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root $WEBROOT;
    }
    
    # Redirect all other traffic to HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

# HTTPS Configuration
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/$DOMAIN/chain.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self'; object-src 'none'; child-src 'self'; frame-ancestors 'self'; form-action 'self'; base-uri 'self';" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Frontend (React App)
    location / {
        root /var/www/natural-stone-marketplace/frontend/build;
        index index.html index.htm;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # WebSocket Support
    location /socket.io/ {
        proxy_pass http://localhost:8001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # File Uploads
    location /uploads/ {
        root /var/www/natural-stone-marketplace/backend;
        expires 1y;
        add_header Cache-Control "public";
    }

    # Health Check
    location /health {
        proxy_pass http://localhost:8001/health;
        access_log off;
    }

    # Block access to sensitive files
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(env|log|config)$ {
        deny all;
    }
}
EOF

# Enable Nginx site
echo "🔗 Enabling Nginx site..."
ln -sf $NGINX_CONFIG /etc/nginx/sites-enabled/
nginx -t

# Create webroot directory
echo "📁 Creating webroot directory..."
mkdir -p $WEBROOT

# Obtain SSL certificate
echo "🔐 Obtaining SSL certificate..."
certbot certonly \
    --webroot \
    --webroot-path=$WEBROOT \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    --domains $DOMAIN,www.$DOMAIN

# Test Nginx configuration
echo "🧪 Testing Nginx configuration..."
nginx -t

# Restart Nginx
echo "🔄 Restarting Nginx..."
systemctl restart nginx

# Set up automatic certificate renewal
echo "🔄 Setting up automatic certificate renewal..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

# Create SSL monitoring script
cat > /usr/local/bin/ssl-monitor.sh << 'EOF'
#!/bin/bash
# SSL Certificate Monitoring Script

DOMAIN="yourdomain.com"
DAYS_BEFORE_EXPIRY=30
EMAIL="<EMAIL>"

# Check certificate expiry
EXPIRY_DATE=$(openssl x509 -in /etc/letsencrypt/live/$DOMAIN/cert.pem -noout -enddate | cut -d= -f2)
EXPIRY_TIMESTAMP=$(date -d "$EXPIRY_DATE" +%s)
CURRENT_TIMESTAMP=$(date +%s)
DAYS_UNTIL_EXPIRY=$(( ($EXPIRY_TIMESTAMP - $CURRENT_TIMESTAMP) / 86400 ))

if [ $DAYS_UNTIL_EXPIRY -le $DAYS_BEFORE_EXPIRY ]; then
    echo "⚠️ SSL certificate for $DOMAIN expires in $DAYS_UNTIL_EXPIRY days!"
    # Send email notification (requires mail command)
    # echo "SSL certificate for $DOMAIN expires in $DAYS_UNTIL_EXPIRY days!" | mail -s "SSL Certificate Expiry Warning" $EMAIL
fi
EOF

chmod +x /usr/local/bin/ssl-monitor.sh

# Add SSL monitoring to cron
(crontab -l 2>/dev/null; echo "0 9 * * * /usr/local/bin/ssl-monitor.sh") | crontab -

echo "✅ SSL/TLS setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Update your DNS records to point to this server"
echo "2. Update the DOMAIN variable in this script with your actual domain"
echo "3. Update the EMAIL variable with your actual email"
echo "4. Run the script again with your actual domain"
echo "5. Test your SSL configuration at: https://www.ssllabs.com/ssltest/"
echo ""
echo "🔧 Configuration files:"
echo "- Nginx config: $NGINX_CONFIG"
echo "- SSL certificates: /etc/letsencrypt/live/$DOMAIN/"
echo "- SSL monitoring: /usr/local/bin/ssl-monitor.sh"
echo ""
echo "🔄 Automatic renewal is set up via cron job"
echo "📧 SSL monitoring will alert you 30 days before expiry"
