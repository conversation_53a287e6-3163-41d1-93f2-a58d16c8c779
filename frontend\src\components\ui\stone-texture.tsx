import * as React from "react"
import { cn } from "@/lib/utils"

export interface StoneTextureProps extends React.HTMLAttributes<HTMLDivElement> {
  type: 'marble' | 'granite' | 'travertine' | 'onyx'
  intensity?: 'light' | 'medium' | 'strong'
  animated?: boolean
}

/**
 * StoneTexture component following RFC-004 UI/UX Design System
 * Natural stone texture backgrounds for authentic visual experience
 */
const StoneTexture = React.forwardRef<HTMLDivElement, StoneTextureProps>(
  ({ className, type, intensity = 'medium', animated = false, children, ...props }, ref) => {
    
    const getTextureStyles = () => {
      const baseStyles = "relative overflow-hidden"
      
      switch (type) {
        case 'marble':
          return cn(
            baseStyles,
            "bg-gradient-to-br from-[#F5F5DC] via-white to-[#F0F0F0]",
            intensity === 'light' && "opacity-30",
            intensity === 'medium' && "opacity-60",
            intensity === 'strong' && "opacity-90"
          )
          
        case 'granite':
          return cn(
            baseStyles,
            "bg-[#2F4F4F]",
            intensity === 'light' && "opacity-40",
            intensity === 'medium' && "opacity-70",
            intensity === 'strong' && "opacity-95"
          )
          
        case 'travertine':
          return cn(
            baseStyles,
            "bg-[#DEB887]",
            intensity === 'light' && "opacity-25",
            intensity === 'medium' && "opacity-50",
            intensity === 'strong' && "opacity-80"
          )
          
        case 'onyx':
          return cn(
            baseStyles,
            "bg-gradient-to-br from-white via-gray-100 to-white",
            intensity === 'light' && "opacity-20",
            intensity === 'medium' && "opacity-40",
            intensity === 'strong' && "opacity-70"
          )
          
        default:
          return baseStyles
      }
    }
    
    const getPatternOverlay = () => {
      switch (type) {
        case 'marble':
          return (
            <div className={cn(
              "absolute inset-0 pointer-events-none",
              "bg-[radial-gradient(circle_at_20%_30%,rgba(200,200,200,0.3)_1px,transparent_1px),radial-gradient(circle_at_80%_70%,rgba(180,180,180,0.2)_1px,transparent_1px)]",
              "bg-[length:50px_50px,75px_75px]",
              animated && "animate-pulse"
            )} />
          )
          
        case 'granite':
          return (
            <div className={cn(
              "absolute inset-0 pointer-events-none",
              "bg-[radial-gradient(circle_at_25%_25%,#4A4A4A_1px,transparent_1px),radial-gradient(circle_at_75%_75%,#1A1A1A_1px,transparent_1px),radial-gradient(circle_at_50%_50%,#3A3A3A_0.5px,transparent_0.5px)]",
              "bg-[length:20px_20px,30px_30px,15px_15px]"
            )} />
          )
          
        case 'travertine':
          return (
            <div className={cn(
              "absolute inset-0 pointer-events-none",
              "bg-[linear-gradient(90deg,rgba(0,0,0,0.1)_1px,transparent_1px),linear-gradient(rgba(0,0,0,0.05)_1px,transparent_1px)]",
              "bg-[length:40px_40px,20px_20px]"
            )} />
          )
          
        case 'onyx':
          return (
            <div className={cn(
              "absolute inset-0 pointer-events-none",
              "bg-gradient-to-br from-transparent via-white/30 to-transparent",
              animated && "animate-shimmer"
            )} />
          )
          
        default:
          return null
      }
    }

    return (
      <div
        ref={ref}
        className={cn(getTextureStyles(), className)}
        {...props}
      >
        {getPatternOverlay()}
        {children && (
          <div className="relative z-10">
            {children}
          </div>
        )}
      </div>
    )
  }
)

StoneTexture.displayName = "StoneTexture"

// Shimmer animation for onyx effect
const shimmerKeyframes = `
  @keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
  }
  .animate-shimmer {
    animation: shimmer 3s ease-in-out infinite;
  }
`

// Inject keyframes into document head
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = shimmerKeyframes
  document.head.appendChild(style)
}

export { StoneTexture }
