"use client"

import * as React from "react"
import { Button } from "./button"

interface CustomerLocation {
  id: string
  name: string
  address: string
  googleMapsLink: string
}

interface CustomerRegistrationData {
  // Kişisel Bilgiler
  firstName: string
  lastName: string
  email: string
  phone: string
  password: string
  confirmPassword: string
  
  // Şirket Bilgileri
  companyName: string
  position: string
  companyDescription: string
  
  // Cari Bilgileri
  taxNumber: string
  taxOffice: string
  
  // Hizmet Bilgileri
  serviceCountry: string
  serviceArea: string
  customServiceArea: string
  
  // Lokasyonlar
  locations: CustomerLocation[]
}

interface CustomerRegistrationModalProps {
  isOpen: boolean
  onClose: () => void
  onRegister: (data: CustomerRegistrationData) => Promise<boolean>
  onSwitchToLogin: () => void
}

const SERVICE_AREAS = [
  "İnşaat Firması",
  "Mimar",
  "Depocu",
  "Uygulamacı",
  "Diğer"
]

const COUNTRIES = [
  "Türkiye",
  "Afganistan",
  "Almanya",
  "Amerika Birleşik Devletleri",
  "Andorra",
  "Angola",
  "Antigua ve Barbuda",
  "Arjantin",
  "Arnavutluk",
  "Avustralya",
  "Avusturya",
  "Azerbaycan",
  "Bahama",
  "Bahreyn",
  "Bangladeş",
  "Barbados",
  "Belarus",
  "Belçika",
  "Belize",
  "Benin",
  "Bhutan",
  "Birleşik Arap Emirlikleri",
  "Birleşik Krallık",
  "Bolivya",
  "Bosna Hersek",
  "Botsvana",
  "Brezilya",
  "Brunei",
  "Bulgaristan",
  "Burkina Faso",
  "Burundi",
  "Cezayir",
  "Cibuti",
  "Çad",
  "Çek Cumhuriyeti",
  "Çin",
  "Danimarka",
  "Demokratik Kongo Cumhuriyeti",
  "Doğu Timor",
  "Dominik",
  "Dominik Cumhuriyeti",
  "Ekvador",
  "Ekvator Ginesi",
  "El Salvador",
  "Endonezya",
  "Eritre",
  "Ermenistan",
  "Estonya",
  "Etiyopya",
  "Fas",
  "Fiji",
  "Filipinler",
  "Finlandiya",
  "Fransa",
  "Gabon",
  "Gambiya",
  "Gana",
  "Grenada",
  "Guatemala",
  "Gine",
  "Gine-Bissau",
  "Guyana",
  "Güney Afrika",
  "Güney Kore",
  "Güney Sudan",
  "Gürcistan",
  "Haiti",
  "Hindistan",
  "Hırvatistan",
  "Hollanda",
  "Honduras",
  "Irak",
  "İran",
  "İrlanda",
  "İspanya",
  "İsrail",
  "İsveç",
  "İsviçre",
  "İtalya",
  "İzlanda",
  "Jamaika",
  "Japonya",
  "Kamboçya",
  "Kamerun",
  "Kanada",
  "Karadağ",
  "Katar",
  "Kazakistan",
  "Kenya",
  "Kıbrıs",
  "Kırgızistan",
  "Kiribati",
  "Kolombiya",
  "Komor",
  "Kongo",
  "Kosta Rika",
  "Kuveyt",
  "Kuzey Kore",
  "Kuzey Makedonya",
  "Laos",
  "Lesotho",
  "Letonya",
  "Liban",
  "Liberya",
  "Libya",
  "Liechtenstein",
  "Litvanya",
  "Lüksemburg",
  "Macaristan",
  "Madagaskar",
  "Malavi",
  "Maldivler",
  "Malezya",
  "Mali",
  "Malta",
  "Marshall Adaları",
  "Mauritius",
  "Meksika",
  "Mısır",
  "Mikronezya",
  "Moğolistan",
  "Moldova",
  "Monako",
  "Mozambik",
  "Myanmar",
  "Namibya",
  "Nauru",
  "Nepal",
  "Nijer",
  "Nijerya",
  "Nikaragua",
  "Norveç",
  "Orta Afrika Cumhuriyeti",
  "Özbekistan",
  "Pakistan",
  "Palau",
  "Panama",
  "Papua Yeni Gine",
  "Paraguay",
  "Peru",
  "Polonya",
  "Portekiz",
  "Romanya",
  "Ruanda",
  "Rusya",
  "Saint Kitts ve Nevis",
  "Saint Lucia",
  "Saint Vincent ve Grenadinler",
  "Samoa",
  "San Marino",
  "São Tomé ve Príncipe",
  "Senegal",
  "Seyşeller",
  "Sierra Leone",
  "Singapur",
  "Sırbistan",
  "Slovakya",
  "Slovenya",
  "Solomon Adaları",
  "Somali",
  "Sri Lanka",
  "Sudan",
  "Surinam",
  "Suudi Arabistan",
  "Şili",
  "Suriye",
  "Svaziland",
  "Tacikistan",
  "Tanzanya",
  "Tayland",
  "Togo",
  "Tonga",
  "Trinidad ve Tobago",
  "Tunus",
  "Tuvalu",
  "Türkmenistan",
  "Uganda",
  "Ukrayna",
  "Umman",
  "Uruguay",
  "Ürdün",
  "Vanuatu",
  "Vatikan",
  "Venezuela",
  "Vietnam",
  "Yemen",
  "Yeni Zelanda",
  "Yunanistan",
  "Zambiya",
  "Zimbabve"
]

// Güvenli şifre örneği generator
const generatePasswordExample = () => {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const symbols = '@$!%*?&';

  const getRandomChar = (str: string) => str[Math.floor(Math.random() * str.length)];

  // Her kategoriden en az bir karakter al
  let password = '';
  password += getRandomChar(uppercase);
  password += getRandomChar(lowercase);
  password += getRandomChar(numbers);
  password += getRandomChar(symbols);

  // Kalan karakterleri rastgele ekle
  const allChars = uppercase + lowercase + numbers + symbols;
  for (let i = 4; i < 8; i++) {
    password += getRandomChar(allChars);
  }

  // Karakterleri karıştır
  return password.split('').sort(() => Math.random() - 0.5).join('');
};

export function CustomerRegistrationModal({
  isOpen,
  onClose,
  onRegister,
  onSwitchToLogin
}: CustomerRegistrationModalProps) {
  const [currentStep, setCurrentStep] = React.useState(1)
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState('')
  const [passwordExample, setPasswordExample] = React.useState(generatePasswordExample()) // Component mount'ta bir kez oluştur

  const [formData, setFormData] = React.useState<CustomerRegistrationData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    companyName: "",
    position: "",
    companyDescription: "",
    taxNumber: "",
    taxOffice: "",
    serviceCountry: "",
    serviceArea: "",
    customServiceArea: "",
    locations: [{
      id: "1",
      name: "",
      address: "",
      googleMapsLink: ""
    }]
  })

  const handleInputChange = (field: keyof CustomerRegistrationData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleLocationChange = (id: string, field: keyof CustomerLocation, value: string) => {
    setFormData(prev => ({
      ...prev,
      locations: prev.locations.map(loc => 
        loc.id === id ? { ...loc, [field]: value } : loc
      )
    }))
  }

  const addLocation = () => {
    const newLocation: CustomerLocation = {
      id: Date.now().toString(),
      name: "",
      address: "",
      googleMapsLink: ""
    }
    setFormData(prev => ({
      ...prev,
      locations: [...prev.locations, newLocation]
    }))
  }

  const removeLocation = (id: string) => {
    if (formData.locations.length > 1) {
      setFormData(prev => ({
        ...prev,
        locations: prev.locations.filter(loc => loc.id !== id)
      }))
    }
  }

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        // Şifre validasyonu
        if (!formData.password || formData.password.length < 8) {
          setError('Şifre en az 8 karakter olmalıdır.')
          return false
        }

        // Şifre karmaşıklık kontrolü
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
        if (!passwordRegex.test(formData.password)) {
          setError('Şifre en az bir büyük harf, bir küçük harf, bir rakam ve bir özel karakter (@$!%*?&) içermelidir.')
          return false
        }
        if (formData.password !== formData.confirmPassword) {
          setError('Şifreler eşleşmiyor.')
          return false
        }
        // Email validasyonu
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(formData.email)) {
          setError('Geçerli bir e-posta adresi giriniz.')
          return false
        }
        // Telefon validasyonu
        if (!formData.phone || formData.phone.length < 10) {
          setError('Geçerli bir telefon numarası giriniz.')
          return false
        }
        if (!formData.firstName) {
          setError('Ad alanı zorunludur.')
          return false
        }
        if (!formData.lastName) {
          setError('Soyad alanı zorunludur.')
          return false
        }
        return true
      case 2:
        if (!formData.companyName) {
          setError('Şirket ismi zorunludur.')
          return false
        }
        if (!formData.position) {
          setError('Şirketteki görev zorunludur.')
          return false
        }
        return true
      case 3:
        if (!formData.serviceCountry) {
          setError('Hizmet verdiği ülke seçimi zorunludur.')
          return false
        }
        if (!formData.serviceArea) {
          setError('Hizmet alanı seçimi zorunludur.')
          return false
        }
        if (formData.serviceArea === 'Diğer' && !formData.customServiceArea) {
          setError('Diğer seçeneği için açıklama giriniz.')
          return false
        }
        return true
      case 4:
        if (formData.locations.length === 0) {
          setError('En az bir lokasyon eklemelisiniz.')
          return false
        }
        for (let i = 0; i < formData.locations.length; i++) {
          const loc = formData.locations[i]
          if (!loc.name) {
            setError(`${i + 1}. lokasyonun adı zorunludur.`)
            return false
          }
          if (!loc.address) {
            setError(`${i + 1}. lokasyonun adresi zorunludur.`)
            return false
          }
        }
        return true
      default:
        return true
    }
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4))
      setError('')
    }
    // Error mesajı validateStep içinde set ediliyor
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
    setError('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    if (formData.password !== formData.confirmPassword) {
      setError('Şifreler eşleşmiyor')
      setIsLoading(false)
      return
    }

    if (formData.password.length < 8) {
      setError('Şifre en az 8 karakter olmalıdır')
      setIsLoading(false)
      return
    }

    // Şifre karmaşıklık kontrolü
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
    if (!passwordRegex.test(formData.password)) {
      setError('Şifre en az bir büyük harf, bir küçük harf, bir rakam ve bir özel karakter (@$!%*?&) içermelidir.')
      setIsLoading(false)
      return
    }

    try {
      const success = await onRegister(formData)
      if (!success) {
        setError('Üye olurken bir hata oluştu')
      }
    } catch (error) {
      setError('Üye olurken bir hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Müşteri Üyeliği</h2>
            <p className="text-gray-600">Adım {currentStep} / 4</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-4 bg-gray-50">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">İlerleme</span>
            <span className="text-sm text-gray-500">{Math.round((currentStep / 4) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-stone-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / 4) * 100}%` }}
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 280px)' }}>
          <form onSubmit={handleSubmit}>
            {/* Step 1: Kişisel Bilgiler */}
            {currentStep === 1 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Kişisel Bilgiler</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ad *
                    </label>
                    <input
                      type="text"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      placeholder="Adınız"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Soyad *
                    </label>
                    <input
                      type="text"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      placeholder="Soyadınız"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      E-posta *
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Telefon *
                    </label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      placeholder="+90 555 123 45 67"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Şifre * (En az 8 karakter, büyük/küçük harf, rakam ve özel karakter)
                    </label>
                    <input
                      type="password"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      required
                      minLength={8}
                      pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]"
                      title="En az bir büyük harf, bir küçük harf, bir rakam ve bir özel karakter (@$!%*?&) içermelidir"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      placeholder="••••••••"
                    />
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-xs text-gray-500">
                        Örnek: <span className="font-mono bg-gray-100 px-1 rounded">{passwordExample}</span>
                      </p>
                      <button
                        type="button"
                        onClick={() => setPasswordExample(generatePasswordExample())}
                        className="text-xs text-blue-600 hover:text-blue-700 ml-2"
                        title="Yeni örnek oluştur"
                      >
                        🔄
                      </button>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">
                      (büyük harf, küçük harf, rakam, özel karakter)
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Şifre Tekrar * (En az 8 karakter, büyük/küçük harf, rakam ve özel karakter)
                    </label>
                    <input
                      type="password"
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      required
                      minLength={8}
                      pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]"
                      title="En az bir büyük harf, bir küçük harf, bir rakam ve bir özel karakter (@$!%*?&) içermelidir"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      placeholder="••••••••"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Şirket Bilgileri */}
            {currentStep === 2 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Şirket Bilgileri</h3>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Şirket İsmi *
                  </label>
                  <input
                    type="text"
                    value={formData.companyName}
                    onChange={(e) => handleInputChange('companyName', e.target.value)}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                    placeholder="ABC İnşaat Ltd. Şti."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Şirketteki Göreviniz *
                  </label>
                  <input
                    type="text"
                    value={formData.position}
                    onChange={(e) => handleInputChange('position', e.target.value)}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                    placeholder="Genel Müdür, Proje Yöneticisi, Satın Alma Uzmanı vb."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Vergi Numarası
                    </label>
                    <input
                      type="text"
                      value={formData.taxNumber}
                      onChange={(e) => handleInputChange('taxNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      placeholder="1234567890"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Vergi Dairesi
                    </label>
                    <input
                      type="text"
                      value={formData.taxOffice}
                      onChange={(e) => handleInputChange('taxOffice', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      placeholder="Kadıköy Vergi Dairesi"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Şirket Tanıtımı
                  </label>
                  <textarea
                    value={formData.companyDescription}
                    onChange={(e) => handleInputChange('companyDescription', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                    placeholder="Şirketinizin faaliyet alanları, deneyimi, referansları hakkında bilgi verebilirsiniz..."
                  />
                </div>
              </div>
            )}

            {/* Step 3: Hizmet Bilgileri */}
            {currentStep === 3 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Hizmet Bilgileri</h3>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Hizmet Verdiğiniz Ülke *
                  </label>
                  <select
                    value={formData.serviceCountry}
                    onChange={(e) => handleInputChange('serviceCountry', e.target.value)}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                  >
                    <option value="">Ülke seçin</option>
                    {COUNTRIES.map(country => (
                      <option key={country} value={country}>{country}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Hizmet Verdiğiniz Alan *
                  </label>
                  <select
                    value={formData.serviceArea}
                    onChange={(e) => handleInputChange('serviceArea', e.target.value)}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                  >
                    <option value="">Alan seçin</option>
                    {SERVICE_AREAS.map(area => (
                      <option key={area} value={area}>{area}</option>
                    ))}
                  </select>
                </div>

                {formData.serviceArea === 'Diğer' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Hizmet Alanınızı Belirtin
                    </label>
                    <input
                      type="text"
                      value={formData.customServiceArea}
                      onChange={(e) => handleInputChange('customServiceArea', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                      placeholder="Hizmet alanınızı yazın"
                    />
                  </div>
                )}
              </div>
            )}

            {/* Step 4: Lokasyonlar */}
            {currentStep === 4 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Şirket Lokasyonları</h3>

                {formData.locations.map((location, index) => (
                  <div key={location.id} className="border border-gray-200 rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-gray-900">Lokasyon {index + 1}</h4>
                      {formData.locations.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeLocation(location.id)}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          Kaldır
                        </button>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Lokasyon Adı
                      </label>
                      <input
                        type="text"
                        value={location.name}
                        onChange={(e) => handleLocationChange(location.id, 'name', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                        placeholder="Merkez Ofis, Şantiye, Depo vb."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Adres
                      </label>
                      <textarea
                        value={location.address}
                        onChange={(e) => handleLocationChange(location.id, 'address', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                        placeholder="Tam adres bilgisi"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Google Maps Linki
                      </label>
                      <input
                        type="url"
                        value={location.googleMapsLink}
                        onChange={(e) => handleLocationChange(location.id, 'googleMapsLink', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
                        placeholder="https://maps.google.com/..."
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Google Maps'ten konum linkini kopyalayıp yapıştırabilirsiniz
                      </p>
                    </div>
                  </div>
                ))}

                <button
                  type="button"
                  onClick={addLocation}
                  className="w-full py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700"
                >
                  + Yeni Lokasyon Ekle
                </button>
              </div>
            )}

            {error && (
              <div className="text-red-600 text-sm mt-4">{error}</div>
            )}
          </form>
        </div>

        {/* Footer */}
        <div className="flex-shrink-0 flex items-center justify-between p-6 border-t border-gray-200 bg-white">
          <div className="flex gap-3">
            {currentStep > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
              >
                Geri
              </Button>
            )}
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              İptal
            </Button>
          </div>

          <div className="flex gap-3">
            {currentStep < 4 ? (
              <Button
                type="button"
                onClick={nextStep}
                className="bg-stone-600 hover:bg-stone-700"
              >
                İleri
              </Button>
            ) : (
              <Button
                type="submit"
                disabled={isLoading}
                onClick={handleSubmit}
                className="bg-stone-600 hover:bg-stone-700"
              >
                {isLoading ? 'Üye oluyor...' : 'Üyeliği Tamamla'}
              </Button>
            )}
          </div>
        </div>

        {/* Switch to Login */}
        <div className="flex-shrink-0 px-6 pb-4 text-center bg-white">
          <p className="text-sm text-gray-600">
            Zaten hesabınız var mı?{' '}
            <button 
              onClick={onSwitchToLogin}
              className="text-stone-600 hover:text-stone-700 font-medium"
            >
              Giriş Yap
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
