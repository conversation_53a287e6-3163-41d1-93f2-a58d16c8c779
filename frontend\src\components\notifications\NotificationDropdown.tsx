import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON>, Check<PERSON>he<PERSON>, X, Clock, AlertCircle } from 'lucide-react';
import { useNotifications, Notification } from '../../hooks/useNotifications';
import { formatDistanceToNow } from 'date-fns';
import { tr } from 'date-fns/locale';

interface NotificationDropdownProps {
  className?: string;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({ className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const {
    notifications,
    unreadCount,
    isConnected,
    isLoading,
    markAsRead,
    markAllAsRead,
    fetchNotifications
  } = useNotifications();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleNotificationClick = async (notification: Notification) => {
    if (notification.status === 'UNREAD') {
      await markAsRead(notification.id);
    }
    
    // Handle navigation based on notification type
    if (notification.relatedEntityType && notification.relatedEntityId) {
      handleNotificationNavigation(notification);
    }
  };

  const handleNotificationNavigation = (notification: Notification) => {
    const { relatedEntityType, relatedEntityId } = notification;
    
    switch (relatedEntityType) {
      case 'QUOTE_REQUEST':
        window.location.href = `/dashboard/quotes/requests/${relatedEntityId}`;
        break;
      case 'QUOTE':
        window.location.href = `/dashboard/quotes/${relatedEntityId}`;
        break;
      case 'ORDER':
        window.location.href = `/dashboard/orders/${relatedEntityId}`;
        break;
      case 'PRODUCT':
        window.location.href = `/dashboard/products/${relatedEntityId}`;
        break;
      default:
        // Default to dashboard
        window.location.href = '/dashboard';
    }
    setIsOpen(false);
  };

  const getNotificationIcon = (type: string, priority: string) => {
    const iconClass = `w-4 h-4 ${
      priority === 'URGENT' ? 'text-red-500' :
      priority === 'HIGH' ? 'text-orange-500' :
      priority === 'MEDIUM' ? 'text-blue-500' :
      'text-gray-500'
    }`;

    switch (type) {
      case 'QUOTE_REQUEST_RECEIVED':
      case 'QUOTE_RECEIVED':
        return <Clock className={iconClass} />;
      case 'ORDER_CREATED':
      case 'ORDER_STATUS_UPDATED':
        return <Check className={iconClass} />;
      case 'PRODUCT_APPROVED':
        return <CheckCheck className={`${iconClass} text-green-500`} />;
      case 'PRODUCT_REJECTED':
        return <X className={`${iconClass} text-red-500`} />;
      case 'SYSTEM_MAINTENANCE':
        return <AlertCircle className={iconClass} />;
      default:
        return <Bell className={iconClass} />;
    }
  };

  const getNotificationTypeText = (type: string) => {
    switch (type) {
      case 'QUOTE_REQUEST_RECEIVED':
        return 'Teklif Talebi';
      case 'QUOTE_RECEIVED':
        return 'Teklif Alındı';
      case 'ORDER_CREATED':
        return 'Yeni Sipariş';
      case 'ORDER_STATUS_UPDATED':
        return 'Sipariş Güncellendi';
      case 'PRODUCT_APPROVED':
        return 'Ürün Onaylandı';
      case 'PRODUCT_REJECTED':
        return 'Ürün Reddedildi';
      case 'SYSTEM_MAINTENANCE':
        return 'Sistem Bakımı';
      default:
        return 'Bildirim';
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Notification Bell Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg transition-colors"
        aria-label="Bildirimler"
      >
        <Bell className="w-6 h-6" />
        
        {/* Unread Count Badge */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
        
        {/* Connection Status Indicator */}
        <span className={`absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-white ${
          isConnected ? 'bg-green-500' : 'bg-red-500'
        }`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Bildirimler</h3>
            <div className="flex items-center space-x-2">
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  Tümünü Okundu İşaretle
                </button>
              )}
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Notifications List */}
          <div className="max-h-80 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-2">Bildirimler yükleniyor...</p>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <Bell className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>Henüz bildiriminiz yok</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {notifications.slice(0, 10).map((notification) => (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                      notification.status === 'UNREAD' ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type, notification.priority)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {getNotificationTypeText(notification.type)}
                          </p>
                          <span className="text-xs text-gray-500">
                            {formatDistanceToNow(new Date(notification.createdAt), { 
                              addSuffix: true, 
                              locale: tr 
                            })}
                          </span>
                        </div>
                        
                        <p className="text-sm text-gray-700 line-clamp-2">
                          {notification.message}
                        </p>
                        
                        {notification.status === 'UNREAD' && (
                          <div className="flex items-center mt-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                            <span className="text-xs text-blue-600 font-medium">Okunmadı</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
              <button
                onClick={() => {
                  window.location.href = '/dashboard/notifications';
                  setIsOpen(false);
                }}
                className="w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                Tüm Bildirimleri Görüntüle
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown;
