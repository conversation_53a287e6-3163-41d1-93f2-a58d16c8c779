export interface SampleRequestTabConfig {
  key: 'all' | 'pending' | 'approved' | 'payment_required' | 'shipped' | 'delivered' | 'evaluated';
  label: string;
  count: number;
}

export interface SampleRequestCardProps {
  request: any; // Will use SampleRequest from context
  onNavigate?: (route: string) => void;
  onViewDetail?: (requestId: string) => void;
  onPayment?: (requestId: string) => void;
  onEvaluate?: (requestId: string) => void;
  onOrder?: (requestId: string) => void;
}

export interface SampleRequestTabsProps {
  tabs: SampleRequestTabConfig[];
  activeTab: string;
  onTabChange: (tabKey: string) => void;
}

export interface SampleStatusUtilsConfig {
  status: string;
  icon: React.ComponentType<any>;
  label: string;
  color: string;
  bgColor: string;
}

export interface SampleRequestEmptyStateProps {
  activeTab: string;
  tabLabel?: string;
  onNavigate?: (route: string) => void;
}

export interface SampleRequestFilterProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  dateRange?: {
    start: Date | null;
    end: Date | null;
  };
  onDateRangeChange?: (range: { start: Date | null; end: Date | null }) => void;
  statusFilter?: string[];
  onStatusFilterChange?: (statuses: string[]) => void;
}

export interface SampleRequestsPageProps {
  onNavigate?: (route: string) => void;
}
