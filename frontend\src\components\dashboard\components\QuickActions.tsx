'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  ClipboardDocumentListIcon,
  HeartIcon,
  ChartBarIcon,
  CalculatorIcon,
  BellIcon
} from '@heroicons/react/24/outline';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  action: () => void;
}

interface QuickActionsProps {
  onNavigate?: (route: string) => void;
  onNewQuoteRequest?: () => void;
  onOpenNotifications?: () => void;
}

const QuickActions: React.FC<QuickActionsProps> = ({ onNavigate, onNewQuoteRequest, onOpenNotifications }) => {
  const quickActions: QuickAction[] = [
    {
      id: 'new-request',
      title: '<PERSON><PERSON>',
      description: 'Ürün için teklif talebi oluştur',
      icon: PlusIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100',
      action: () => {
        onNewQuoteRequest?.();
      }
    },
    {
      id: 'view-requests',
      title: 'Taleplerim',
      description: 'Aktif talepleri görü<PERSON>üle',
      icon: ClipboardDocumentListIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50 hover:bg-green-100',
      action: () => {
        onNavigate?.('/customer/requests/active');
      }
    },
    {
      id: 'favorites',
      title: 'Favorilerim',
      description: 'Favori ürünleri incele',
      icon: HeartIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-50 hover:bg-red-100',
      action: () => {
        onNavigate?.('/customer/favorites');
      }
    },
    {
      id: 'analytics',
      title: 'Analizler',
      description: 'Detaylı raporları görüntüle',
      icon: ChartBarIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 hover:bg-purple-100',
      action: () => {
        onNavigate?.('/customer/analytics');
      }
    },
    {
      id: 'accounting',
      title: 'Ön Muhasebe',
      description: 'Gelir-gider takibi yap',
      icon: CalculatorIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 hover:bg-orange-100',
      action: () => {
        onNavigate?.('/customer/accounting');
      }
    },
    {
      id: 'notifications',
      title: 'Bildirimler',
      description: 'Yeni bildirimleri kontrol et',
      icon: BellIcon,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50 hover:bg-indigo-100',
      action: () => {
        onOpenNotifications?.();
      }
    }
  ];

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Hızlı İşlemler
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Sık kullanılan işlemlere hızlı erişim
        </p>
      </div>

      {/* Actions Grid */}
      <div className="space-y-3">
        {quickActions.map((action, index) => (
          <motion.button
            key={action.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={action.action}
            className={`w-full p-4 rounded-lg border border-gray-100 ${action.bgColor} transition-all duration-200 text-left group`}
          >
            <div className="flex items-start space-x-3">
              <div className={`p-2 rounded-lg ${action.bgColor.replace('hover:', '')} group-hover:scale-110 transition-transform duration-200`}>
                <action.icon className={`h-5 w-5 ${action.color}`} />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 group-hover:text-gray-700">
                  {action.title}
                </h4>
                <p className="text-xs text-gray-600 mt-1 group-hover:text-gray-500">
                  {action.description}
                </p>
              </div>
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </motion.button>
        ))}
      </div>

      {/* Bottom CTA */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => onNewQuoteRequest?.()}
          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-sm"
        >
          <div className="flex items-center justify-center space-x-2">
            <PlusIcon className="h-5 w-5" />
            <span>Yeni Teklif İste</span>
          </div>
        </motion.button>
      </div>
    </div>
  );
};

export default QuickActions;
