'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  EyeIcon,
  CreditCardIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { SampleRequestCardProps } from '@/types/sample-request';
import { 
  getSampleStatusIcon, 
  getSampleStatusLabel, 
  getSampleStatusColor 
} from '@/utils/sample-status-utils';

const SampleRequestCard: React.FC<SampleRequestCardProps> = ({
  request,
  onNavigate,
  onViewDetail,
  onPayment,
  onEvaluate,
  onOrder
}) => {
  const handleViewDetail = () => {
    onViewDetail?.(request.id);
  };

  const handlePayment = () => {
    onPayment?.(request.id);
    onNavigate?.(`/customer/payments/sample/${request.id}`);
  };

  const handleEvaluate = () => {
    onEvaluate?.(request.id);
  };

  const handleOrder = () => {
    onOrder?.(request.id);
    onNavigate?.('/customer/dashboard');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200"
    >
      {/* Request Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            {getSampleStatusIcon(request.status)}
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getSampleStatusColor(request.status)}`}>
              {getSampleStatusLabel(request.status)}
            </span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Numune Talebi #{request.id.slice(-6)}
            </h3>
            <p className="text-sm text-gray-600">
              {new Date(request.createdAt).toLocaleDateString('tr-TR')}
            </p>
          </div>
        </div>
      </div>

      {/* Products */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Talep Edilen Ürünler</h4>
        <div className="space-y-2">
          {request.requestedProducts.map((product: any, idx: number) => (
            <div key={idx} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
              <div>
                <p className="font-medium text-gray-900">{product.productName}</p>
                <p className="text-sm text-gray-600">{product.specifications}</p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{product.sampleSize}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Delivery Address */}
      <div className="mb-4 bg-blue-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">Teslimat Adresi</h4>
        <p className="text-sm text-blue-800">
          {request.deliveryAddress.name} - {request.deliveryAddress.city}, {request.deliveryAddress.country}
        </p>
      </div>

      {/* Status Messages */}
      {request.status === 'rejected' && request.rejectionReason && (
        <div className="mb-4 bg-red-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-red-900 mb-2">Red Sebebi</h4>
          <p className="text-sm text-red-800">{request.rejectionReason}</p>
        </div>
      )}

      {request.status === 'shipped' && request.shippingInfo && (
        <div className="mb-4 bg-purple-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-purple-900 mb-2">Kargo Bilgileri</h4>
          <p className="text-sm text-purple-800">
            Kargo Firması: {request.shippingInfo.carrier || 'Belirtilmemiş'}
          </p>
          {request.shippingInfo.trackingNumber && (
            <p className="text-sm text-purple-800">
              Takip No: {request.shippingInfo.trackingNumber}
            </p>
          )}
        </div>
      )}

      {request.status === 'evaluated' && request.customerEvaluation && (
        <div className="mb-4 bg-yellow-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-yellow-900 mb-2">Değerlendirmeniz</h4>
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-sm text-yellow-800">Puan:</span>
            <div className="flex">
              {[1, 2, 3, 4, 5].map((star) => (
                <StarIcon
                  key={star}
                  className={`h-4 w-4 ${
                    star <= request.customerEvaluation!.rating
                      ? 'text-yellow-500 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>
          <p className="text-sm text-yellow-800">{request.customerEvaluation.feedback}</p>
          <p className="text-sm text-yellow-800 mt-1">
            <strong>Sipariş Kararı:</strong> {request.willOrder ? 'Sipariş verecek' : 'Sipariş vermeyecek'}
          </p>
        </div>
      )}

      {/* Payment Required Message */}
      {(request.status === 'approved_pending_payment' || request.status === 'payment_required') && (
        <div className="mb-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-orange-900 mb-2">Kargo Ücreti Ödeme Bekleniyor</h4>
          <p className="text-sm text-orange-800 mb-3">
            Üretici numune talebinizi onayladı. Numune hazırlığının başlaması için kargo ücretini ödemeniz gerekmektedir.
          </p>
          {request.producerResponse?.shippingCost && (
            <div className="text-sm text-orange-800">
              <strong>Kargo Ücreti:</strong> {request.producerResponse.shippingCost} TL
              {request.producerResponse.carrier && (
                <span> ({request.producerResponse.carrier === 'aras' ? 'Aras Kargo' :
                       request.producerResponse.carrier === 'mng' ? 'MNG Kargo' :
                       request.producerResponse.carrier === 'ups' ? 'UPS' :
                       request.producerResponse.carrier === 'dhl' ? 'DHL' :
                       request.producerResponse.carrier})</span>
              )}
            </div>
          )}
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleViewDetail}
            className="flex items-center space-x-2 text-blue-600 hover:text-blue-700"
          >
            <EyeIcon className="h-4 w-4" />
            <span className="text-sm font-medium">Detay</span>
          </button>
        </div>

        <div className="flex items-center space-x-3">
          {(request.status === 'approved_pending_payment' || request.status === 'payment_required') && (
            <button
              onClick={handlePayment}
              className="px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-lg hover:bg-orange-700 flex items-center space-x-2"
            >
              <CreditCardIcon className="h-4 w-4" />
              <span>Kargo Ücretini Öde</span>
            </button>
          )}

          {request.status === 'delivered' && !request.customerEvaluation && (
            <button 
              onClick={handleEvaluate}
              className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700"
            >
              Değerlendir
            </button>
          )}

          {request.status === 'evaluated' && request.willOrder && (
            <button
              onClick={handleOrder}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
            >
              Sipariş Ver
            </button>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default SampleRequestCard;
