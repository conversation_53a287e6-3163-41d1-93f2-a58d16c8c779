/**
 * Chatbot Service Tests
 * Unit tests for the main ChatbotService class
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { ChatbotService } from '../services/ChatbotService';
import { ChatbotIntent } from '../types';

// Mock dependencies
jest.mock('../services/OpenAIService');
jest.mock('../services/DatabaseConversationManager');
jest.mock('../services/KnowledgeBaseService');
jest.mock('../services/DatabaseEscalationManager');
jest.mock('../services/AnalyticsService');

describe('ChatbotService', () => {
  let chatbotService: ChatbotService;

  beforeEach(() => {
    chatbotService = new ChatbotService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('startConversation', () => {
    it('should create a new conversation session', async () => {
      // Mock the conversation manager
      const mockSessionId = 'test-session-123';
      const mockGreeting = 'Hello! How can I help you today?';
      
      jest.spyOn(chatbotService.conversationManager, 'createSession')
        .mockResolvedValue(mockSessionId);
      
      jest.spyOn(chatbotService.conversationManager, 'addMessage')
        .mockResolvedValue(undefined);

      const result = await chatbotService.startConversation('user123', 'en');

      expect(result.sessionId).toBe(mockSessionId);
      expect(result.greeting).toContain('Hello');
      expect(chatbotService.conversationManager.createSession).toHaveBeenCalledWith('user123', 'en');
    });

    it('should handle errors gracefully', async () => {
      jest.spyOn(chatbotService.conversationManager, 'createSession')
        .mockRejectedValue(new Error('Database error'));

      await expect(chatbotService.startConversation('user123', 'en'))
        .rejects.toThrow('Database error');
    });
  });

  describe('processMessage', () => {
    const mockSessionId = 'test-session-123';
    const mockContext = {
      sessionId: mockSessionId,
      userId: 'user123',
      language: 'en',
      currentIntent: ChatbotIntent.GREETING,
      entities: [],
      conversationHistory: [],
      escalationLevel: 0,
      resolvedIssues: [],
      unresolvedIssues: [],
      confidence: 1.0,
      sentiment: 0.0
    };

    beforeEach(() => {
      jest.spyOn(chatbotService.conversationManager, 'getContext')
        .mockResolvedValue(mockContext);
      
      jest.spyOn(chatbotService.conversationManager, 'addMessage')
        .mockResolvedValue(undefined);
      
      jest.spyOn(chatbotService.conversationManager, 'updateContext')
        .mockResolvedValue(undefined);
    });

    it('should process a user message successfully', async () => {
      const mockResponse = {
        success: true,
        sessionId: mockSessionId,
        response: 'I can help you with that!',
        confidence: 0.9,
        intent: ChatbotIntent.PRODUCT_INQUIRY,
        entities: [],
        suggestions: ['Tell me about marble', 'Show pricing']
      };

      // Mock OpenAI service responses
      jest.spyOn(chatbotService['openAIService'], 'classifyIntent')
        .mockResolvedValue({ intent: ChatbotIntent.PRODUCT_INQUIRY, confidence: 0.9 });
      
      jest.spyOn(chatbotService['openAIService'], 'extractEntities')
        .mockResolvedValue([]);
      
      jest.spyOn(chatbotService['openAIService'], 'analyzeSentiment')
        .mockResolvedValue(0.5);
      
      jest.spyOn(chatbotService['openAIService'], 'generateResponse')
        .mockResolvedValue('I can help you with that!');

      // Mock escalation check
      jest.spyOn(chatbotService.escalationManager, 'shouldEscalate')
        .mockReturnValue(false);

      const result = await chatbotService.processMessage(mockSessionId, 'Tell me about marble products');

      expect(result.success).toBe(true);
      expect(result.sessionId).toBe(mockSessionId);
      expect(result.response).toBe('I can help you with that!');
      expect(result.intent).toBe(ChatbotIntent.PRODUCT_INQUIRY);
    });

    it('should handle escalation when needed', async () => {
      const mockEscalationResult = {
        escalated: true,
        agentId: 'agent-123',
        estimatedWaitTime: 5,
        reason: 'Low confidence'
      };

      // Mock OpenAI service responses
      jest.spyOn(chatbotService['openAIService'], 'classifyIntent')
        .mockResolvedValue({ intent: ChatbotIntent.COMPLAINT, confidence: 0.2 });
      
      jest.spyOn(chatbotService['openAIService'], 'extractEntities')
        .mockResolvedValue([]);
      
      jest.spyOn(chatbotService['openAIService'], 'analyzeSentiment')
        .mockResolvedValue(-0.8);

      // Mock escalation
      jest.spyOn(chatbotService.escalationManager, 'shouldEscalate')
        .mockReturnValue(true);
      
      jest.spyOn(chatbotService.escalationManager, 'escalateToHuman')
        .mockResolvedValue(mockEscalationResult);

      const result = await chatbotService.processMessage(mockSessionId, 'I am very unhappy with your service!');

      expect(result.escalated).toBe(true);
      expect(result.agentId).toBe('agent-123');
      expect(result.estimatedWaitTime).toBe(5);
    });

    it('should handle conversation not found error', async () => {
      jest.spyOn(chatbotService.conversationManager, 'getContext')
        .mockResolvedValue(null);

      await expect(chatbotService.processMessage('invalid-session', 'Hello'))
        .rejects.toThrow('Conversation not found');
    });
  });

  describe('submitFeedback', () => {
    it('should submit feedback successfully', async () => {
      const feedback = {
        sessionId: 'test-session-123',
        messageId: 'msg-123',
        rating: 5,
        feedback: 'Very helpful!',
        timestamp: new Date()
      };

      jest.spyOn(chatbotService.conversationManager, 'submitFeedback')
        .mockResolvedValue(undefined);
      
      jest.spyOn(chatbotService.conversationManager, 'markIssueResolved')
        .mockResolvedValue(undefined);

      await expect(chatbotService.submitFeedback(feedback)).resolves.not.toThrow();
      
      expect(chatbotService.conversationManager.submitFeedback)
        .toHaveBeenCalledWith(feedback.sessionId, feedback.messageId, feedback.rating, feedback.feedback);
      
      expect(chatbotService.conversationManager.markIssueResolved)
        .toHaveBeenCalledWith(feedback.sessionId, 'User satisfied with response');
    });

    it('should mark issue as unresolved for low ratings', async () => {
      const feedback = {
        sessionId: 'test-session-123',
        messageId: 'msg-123',
        rating: 2,
        feedback: 'Not helpful',
        timestamp: new Date()
      };

      jest.spyOn(chatbotService.conversationManager, 'submitFeedback')
        .mockResolvedValue(undefined);
      
      jest.spyOn(chatbotService.conversationManager, 'markIssueUnresolved')
        .mockResolvedValue(undefined);

      await chatbotService.submitFeedback(feedback);
      
      expect(chatbotService.conversationManager.markIssueUnresolved)
        .toHaveBeenCalledWith(feedback.sessionId, 'Low rating: 2');
    });
  });

  describe('getAnalytics', () => {
    it('should return analytics data', async () => {
      const mockAnalytics = {
        responseAccuracy: 0.85,
        resolutionRate: 0.78,
        averageResponseTime: 1.2,
        userSatisfactionScore: 4.2,
        totalConversations: 150,
        averageConversationLength: 8.5,
        mostCommonIntents: [
          { intent: ChatbotIntent.PRODUCT_INQUIRY, count: 45, percentage: 30 }
        ],
        languageDistribution: [
          { language: 'en', count: 100, percentage: 67 },
          { language: 'tr', count: 50, percentage: 33 }
        ],
        escalationRate: 0.12,
        escalationReasons: [
          { reason: 'Low confidence', count: 8, percentage: 40 }
        ],
        humanResolutionRate: 0.95,
        newKnowledgeGaps: [],
        improvementOpportunities: []
      };

      jest.spyOn(chatbotService['analyticsService'], 'getAnalytics')
        .mockResolvedValue(mockAnalytics);

      const result = await chatbotService.getAnalytics();

      expect(result).toEqual(mockAnalytics);
      expect(result.totalConversations).toBe(150);
      expect(result.responseAccuracy).toBe(0.85);
    });

    it('should handle analytics errors', async () => {
      jest.spyOn(chatbotService['analyticsService'], 'getAnalytics')
        .mockRejectedValue(new Error('Analytics service error'));

      await expect(chatbotService.getAnalytics())
        .rejects.toThrow('Analytics service error');
    });
  });

  describe('private helper methods', () => {
    it('should generate appropriate greeting based on language', () => {
      const englishGreeting = chatbotService['getGreeting']('en');
      const turkishGreeting = chatbotService['getGreeting']('tr');
      
      expect(englishGreeting).toContain('Hello');
      expect(turkishGreeting).toContain('Merhaba');
    });

    it('should generate appropriate suggestions based on language', () => {
      const englishSuggestions = chatbotService['getGeneralSuggestions']('en');
      const turkishSuggestions = chatbotService['getGeneralSuggestions']('tr');
      
      expect(englishSuggestions).toBeInstanceOf(Array);
      expect(englishSuggestions.length).toBeGreaterThan(0);
      expect(turkishSuggestions).toBeInstanceOf(Array);
      expect(turkishSuggestions.length).toBeGreaterThan(0);
    });

    it('should handle error responses correctly', () => {
      const errorResponse = chatbotService['getErrorResponse']('session-123', 'test message');
      
      expect(errorResponse.success).toBe(false);
      expect(errorResponse.sessionId).toBe('session-123');
      expect(errorResponse.intent).toBe(ChatbotIntent.UNKNOWN);
      expect(errorResponse.confidence).toBe(0.0);
    });
  });
});
