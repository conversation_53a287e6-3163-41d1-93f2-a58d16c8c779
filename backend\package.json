{"name": "natural-stone-marketplace-backend", "version": "1.0.0", "description": "Backend API for Turkish Natural Stone Marketplace Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=__tests__ --testPathIgnorePatterns=integration,api,e2e", "test:integration": "jest --testPathPattern=integration", "test:api": "jest --testPathPattern=api", "test:ai-marketing": "jest --testPathPattern=ai-marketing", "test:ai-marketing:watch": "jest --testPathPattern=ai-marketing --watch", "test:ai-marketing:coverage": "jest --testPathPattern=ai-marketing --coverage", "test:3d": "jest --testPathPattern=3d-viewer.test.ts", "lint": "eslint src/**/*.ts", "db:seed": "ts-node prisma/seed.ts", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:reset": "prisma migrate reset --force && npm run db:seed", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:deploy": "prisma migrate deploy", "db:studio": "prisma studio"}, "keywords": ["marketplace", "natural-stone", "b2b", "typescript", "express", "prisma", "postgresql"], "author": "Natural Stone Marketplace Team", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@langchain/openai": "^0.0.14", "@next/bundle-analyzer": "^15.3.5", "@prisma/client": "^5.7.0", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@sendgrid/mail": "^8.1.5", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.9", "@types/express-session": "^1.18.2", "@types/multer-s3": "^3.0.3", "@types/pg": "^8.15.4", "@types/socket.io": "^3.0.1", "axios": "^1.6.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "connect-redis": "^9.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.0", "draco3d": "^1.5.6", "express": "^4.18.0", "express-rate-limit": "^7.1.0", "express-session": "^1.18.1", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "file-type": "^21.0.0", "gltf-pipeline": "^4.1.0", "google-ads-api": "^20.0.1", "handlebars": "^4.7.8", "helmet": "^7.1.0", "ioredis": "^5.3.0", "jsonwebtoken": "^9.0.0", "langchain": "^0.0.200", "magic-bytes.js": "^1.12.1", "mjml": "^4.15.3", "mjml-core": "^4.15.3", "morgan": "^1.10.0", "multer": "^1.4.4", "multer-s3": "^3.0.1", "node-cron": "^4.2.0", "nodemailer": "^6.10.1", "openai": "^4.20.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "prisma": "^5.7.0", "redis": "^5.6.0", "sharp": "^0.33.5", "socket.io": "^4.8.1", "stripe": "^14.0.0", "swagger-jsdoc": "^6.2.0", "swagger-ui-express": "^5.0.0", "three": "^0.177.0", "twilio": "^4.19.0", "uuid": "^9.0.0", "web-vitals": "^5.0.3", "winston": "^3.17.0", "zod": "^3.22.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.0", "@types/compression": "^1.7.0", "@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.0", "@types/morgan": "^1.9.0", "@types/multer": "^1.4.0", "@types/node": "^20.10.0", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.0", "@types/passport": "^1.0.0", "@types/passport-jwt": "^3.0.0", "@types/passport-local": "^1.0.0", "@types/supertest": "^6.0.0", "@types/swagger-jsdoc": "^6.0.0", "@types/swagger-ui-express": "^4.1.0", "@types/three": "^0.177.0", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "babel-plugin-import": "^1.13.8", "eslint": "^8.55.0", "husky": "^8.0.0", "jest": "^29.7.0", "lint-staged": "^15.2.0", "nodemon": "^3.0.0", "prettier": "^3.1.0", "supertest": "^6.3.4", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.0"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "engines": {"node": ">=20.10.0", "npm": ">=10.0.0"}}