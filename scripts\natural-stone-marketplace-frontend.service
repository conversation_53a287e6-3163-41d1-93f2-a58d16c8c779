[Unit]
Description=Natural Stone Marketplace Frontend
Documentation=https://github.com/your-org/natural-stone-marketplace
After=network.target natural-stone-marketplace-backend.service
Wants=natural-stone-marketplace-backend.service

[Service]
Type=simple
User=deploy
Group=deploy
WorkingDirectory=/var/www/natural-stone-marketplace/frontend
Environment=NODE_ENV=production
Environment=PORT=3001
Environment=NEXT_PUBLIC_API_URL=https://api.yourdomain.com
ExecStart=/usr/bin/npm start
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=nsm-frontend

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Health check
TimeoutStartSec=60
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
