'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDownIcon, ChevronRightIcon, ArrowRightOnRectangleIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/auth-context';

import { useSimpleTranslation } from '@/hooks/useSimpleTranslation';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  route: string;
  badge?: number;
  submenu?: Array<{
    label: string;
    route: string;
  }>;
}

interface SidebarProps {
  items: SidebarItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  userName?: string;
  userEmail?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ items, activeTab, onTabChange, userName, userEmail }) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const { logout } = useAuth();
  const { t } = useSimpleTranslation();

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleLogout = async () => {
    try {
      await logout();
      // Yönlendirme ana sayfaya
      window.location.href = '/';
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const isExpanded = (itemId: string) => expandedItems.includes(itemId);

  return (
    <div className="w-64 bg-white shadow-lg border-r border-gray-200 flex-shrink-0 h-screen flex flex-col">
      {/* Logo/Brand */}
      <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">MT</span>
          </div>
          <span className="text-lg font-semibold text-gray-900">
            Mermer Ticaret
          </span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 mt-6 px-3 overflow-y-auto">
        <div className="space-y-1">
          {items.map((item) => (
            <div key={item.id}>
              {/* Main Item */}
              <motion.button
                whileHover={{ x: 2 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  if (item.submenu) {
                    toggleExpanded(item.id);
                  } else {
                    onTabChange(item.route);
                  }
                }}
                className={`
                  w-full flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200
                  ${activeTab === item.id 
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' 
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                  }
                `}
              >
                <div className="flex items-center space-x-3">
                  <item.icon className={`h-5 w-5 ${activeTab === item.id ? 'text-blue-600' : 'text-gray-400'}`} />
                  <span>{item.label}</span>
                  {item.badge && (
                    <span className="bg-red-100 text-red-800 text-xs font-medium px-2 py-0.5 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </div>
                
                {item.submenu && (
                  <motion.div
                    animate={{ rotate: isExpanded(item.id) ? 90 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                  </motion.div>
                )}
              </motion.button>

              {/* Submenu */}
              <AnimatePresence>
                {item.submenu && isExpanded(item.id) && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="ml-8 mt-1 space-y-1"
                  >
                    {item.submenu.map((subItem, index) => (
                      <motion.button
                        key={subItem.route}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05 }}
                        whileHover={{ x: 2 }}
                        onClick={() => onTabChange(subItem.route)}
                        className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors duration-150"
                      >
                        {subItem.label}
                      </motion.button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}
        </div>
      </nav>

      {/* Bottom Section - User Info & Logout */}
      <div className="p-4 border-t border-gray-200 bg-white mt-auto">
        {/* User Info */}
        {(userName || userEmail) && (
          <div className="mb-3 px-3 py-2 bg-gray-50 rounded-lg">
            <div className="text-sm font-medium text-gray-900 truncate">
              {userName || 'Kullanıcı'}
            </div>
            {userEmail && (
              <div className="text-xs text-gray-500 truncate">
                {userEmail}
              </div>
            )}
          </div>
        )}



        {/* Logout Button */}
        <motion.button
          whileHover={{ x: 2 }}
          whileTap={{ scale: 0.98 }}
          onClick={handleLogout}
          className="w-full flex items-center space-x-3 px-3 py-2.5 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-lg transition-all duration-200"
        >
          <ArrowRightOnRectangleIcon className="h-5 w-5" />
          <span>{t('dashboard.menu.logout', 'Çıkış Yap')}</span>
        </motion.button>
      </div>

    </div>
  );
};

export default Sidebar;
