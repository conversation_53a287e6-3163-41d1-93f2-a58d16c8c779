<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #1e293b;
            background-color: #f8fafc;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .email-header {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .email-body {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2563eb;
        }
        
        .content p {
            margin-bottom: 15px;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .highlight-box {
            background-color: #f1f5f9;
            border-left: 4px solid #2563eb;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .amount-box {
            background: linear-gradient(135deg, #059669 0%, #**********%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        
        .amount {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .amount-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .bank-info {
            background-color: #fef3c7;
            border: 2px solid #d97706;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .bank-info h3 {
            color: #92400e;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .info-table td {
            padding: 10px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .info-table td:first-child {
            font-weight: bold;
            color: #475569;
            width: 40%;
        }
        
        .reference-code {
            background-color: #1e293b;
            color: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            font-family: monospace;
            font-size: 18px;
            font-weight: bold;
            letter-spacing: 2px;
            margin: 20px 0;
        }
        
        .warning {
            background-color: #fee2e2;
            border-left: 4px solid #dc2626;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 6px 6px 0;
        }
        
        .warning h4 {
            color: #dc2626;
            margin-bottom: 10px;
        }
        
        .steps {
            background-color: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .steps h3 {
            color: #2563eb;
            margin-bottom: 15px;
        }
        
        .steps ol {
            padding-left: 20px;
        }
        
        .steps li {
            margin-bottom: 10px;
            font-size: 15px;
        }
        
        .email-footer {
            background-color: #f8fafc;
            padding: 30px 20px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-text {
            font-size: 12px;
            color: #64748b;
            line-height: 1.5;
        }
        
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .email-body {
                padding: 20px 15px;
            }
            
            .amount {
                font-size: 24px;
            }
            
            .info-table td {
                padding: 8px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="company-name">Doğal Taş Pazaryeri</div>
            <div>Ödeme Talimatları</div>
        </div>
        
        <!-- Body -->
        <div class="email-body">
            <div class="greeting">Merhaba {{customerName}},</div>
            
            <div class="content">
                <p><strong>Sipariş {{orderNumber}}</strong> için ödeme yapmanız gerekmektedir. Aşağıdaki bilgileri kullanarak banka havalesi ile ödemenizi gerçekleştirebilirsiniz.</p>
                
                {{#if productName}}
                <div class="highlight-box">
                    <strong>Ürün:</strong> {{productName}}
                </div>
                {{/if}}
                
                <!-- Amount -->
                <div class="amount-box">
                    <div class="amount">{{amount}} {{currency}}</div>
                    <div class="amount-label">Ödenecek Tutar</div>
                </div>
                
                <!-- Bank Information -->
                <div class="bank-info">
                    <h3>🏦 Banka Bilgileri</h3>
                    <table class="info-table">
                        <tr>
                            <td>Banka Adı:</td>
                            <td>{{bankInfo.bankName}}</td>
                        </tr>
                        <tr>
                            <td>Hesap Sahibi:</td>
                            <td>{{bankInfo.accountHolder}}</td>
                        </tr>
                        <tr>
                            <td>IBAN:</td>
                            <td style="font-family: monospace; font-weight: bold;">{{bankInfo.iban}}</td>
                        </tr>
                        <tr>
                            <td>Tutar:</td>
                            <td><strong>{{amount}} {{currency}}</strong></td>
                        </tr>
                    </table>
                </div>
                
                <!-- Reference Code -->
                <div>
                    <p><strong>Önemli:</strong> Havale yaparken mutlaka aşağıdaki referans kodunu açıklama kısmına yazın:</p>
                    <div class="reference-code">{{referenceCode}}</div>
                </div>
                
                <!-- Payment Steps -->
                <div class="steps">
                    <h3>📋 Ödeme Adımları</h3>
                    <ol>
                        <li>Bankanızın internet bankacılığı veya mobil uygulamasına giriş yapın</li>
                        <li>Havale/EFT işlemini seçin</li>
                        <li>Yukarıdaki IBAN numarasını girin</li>
                        <li>Tutarı <strong>{{amount}} {{currency}}</strong> olarak girin</li>
                        <li>Açıklama kısmına <strong>{{referenceCode}}</strong> kodunu yazın</li>
                        <li>İşlemi onaylayın ve gönderin</li>
                    </ol>
                </div>
                
                <!-- Warning -->
                <div class="warning">
                    <h4>⚠️ Önemli Uyarılar</h4>
                    <ul>
                        <li>Referans kodunu mutlaka yazın, aksi takdirde ödemeniz eşleştirilemez</li>
                        <li>Ödeme işlemi 1-2 iş günü içinde onaylanacaktır</li>
                        <li>Ödemeniz onaylandıktan sonra üretici üretime başlayacaktır</li>
                        {{#if dueDate}}
                        <li>Son ödeme tarihi: <strong>{{dueDate}}</strong></li>
                        {{/if}}
                    </ul>
                </div>
                
                <p>Ödemeniz onaylandıktan sonra size bilgilendirme emaili gönderilecek ve üretici üretime başlayacaktır.</p>
                
                <p>Herhangi bir sorunuz olursa lütfen bizimle iletişime geçin.</p>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="email-footer">
            <div class="footer-text">
                <p><strong>Doğal Taş Pazaryeri Ltd. Şti.</strong></p>
                <p>Destek: <EMAIL> | Tel: +90 212 XXX XX XX</p>
                <p>Bu email otomatik olarak gönderilmiştir.</p>
            </div>
        </div>
    </div>
</body>
</html>
