/**
 * Dynamic Pricing Service
 * Handles real-time price calculations for advanced 3D visualization
 */

import { 
  PriceBreakdown, 
  AdvancedProductConfig, 
  SurfaceFinishName,
  DimensionPricing 
} from '../types/3d';

export interface PricingConfiguration {
  basePrice: {
    calculation: 'per_m2' | 'per_piece' | 'per_ton';
    currency: 'USD' | 'EUR' | 'TRY';
    vatIncluded: boolean;
    basePricePerM2: number;
  };
  
  modifiers: {
    surfaceFinish: Record<SurfaceFinishName, number>;
    customSize: {
      threshold: number; // m²
      multiplier: number;
    };
    thickness: Record<number, number>;
    quantity: Array<{
      min: number;
      max: number;
      discount: number;
    }>;
    urgency: {
      standard: number; // 30 days
      fast: number; // 15 days
      express: number; // 7 days
    };
  };
  
  additionalCosts: {
    cutting: number; // per m²
    edgePolishing: number; // per linear meter
    packaging: number; // per m²
    shipping: 'calculated' | 'included' | 'free_over_amount';
    freeShippingThreshold?: number;
  };
}

export class DynamicPricingService {
  private config: PricingConfiguration;
  private cache: Map<string, { price: PriceBreakdown; timestamp: number }> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  constructor(config?: Partial<PricingConfiguration>) {
    this.config = {
      basePrice: {
        calculation: 'per_m2',
        currency: 'USD',
        vatIncluded: false,
        basePricePerM2: 50,
        ...config?.basePrice
      },
      modifiers: {
        surfaceFinish: {
          'ham': 1.0,
          'honlu': 1.2,
          'cilali': 1.5,
          'fircinlanmis': 1.3,
          'yakma': 1.4,
          'eskitme': 1.6,
          'kumlama': 1.25,
          'dolgu': 1.1
        },
        customSize: {
          threshold: 100, // m²
          multiplier: 1.15
        },
        thickness: {
          1: 0.8,
          1.5: 0.9,
          2: 1.0,
          3: 1.2,
          4: 1.4,
          5: 1.6
        },
        quantity: [
          { min: 0, max: 50, discount: 0 },
          { min: 51, max: 100, discount: 0.05 },
          { min: 101, max: 500, discount: 0.1 },
          { min: 501, max: 1000, discount: 0.15 },
          { min: 1001, max: Infinity, discount: 0.2 }
        ],
        urgency: {
          standard: 1.0,
          fast: 1.2,
          express: 1.5
        },
        ...config?.modifiers
      },
      additionalCosts: {
        cutting: 5, // $5 per m²
        edgePolishing: 3, // $3 per linear meter
        packaging: 2, // $2 per m²
        shipping: 'calculated',
        freeShippingThreshold: 1000,
        ...config?.additionalCosts
      }
    };
  }

  /**
   * Calculate price for advanced product configuration
   */
  async calculatePrice(
    productConfig: AdvancedProductConfig,
    quantity: number = 1,
    urgency: 'standard' | 'fast' | 'express' = 'standard',
    options: {
      includeShipping?: boolean;
      includePackaging?: boolean;
      includeCutting?: boolean;
      includeEdgePolishing?: boolean;
    } = {}
  ): Promise<PriceBreakdown> {
    // Generate cache key
    const cacheKey = this.generateCacheKey(productConfig, quantity, urgency, options);
    
    // Check cache
    const cached = this.cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.price;
    }

    try {
      const priceBreakdown = await this.performPriceCalculation(
        productConfig, 
        quantity, 
        urgency, 
        options
      );
      
      // Cache result
      this.cache.set(cacheKey, {
        price: priceBreakdown,
        timestamp: Date.now()
      });
      
      return priceBreakdown;
    } catch (error) {
      console.error('Price calculation failed:', error);
      throw new Error('Fiyat hesaplama başarısız oldu');
    }
  }

  /**
   * Perform the actual price calculation
   */
  private async performPriceCalculation(
    productConfig: AdvancedProductConfig,
    quantity: number,
    urgency: 'standard' | 'fast' | 'express',
    options: any
  ): Promise<PriceBreakdown> {
    const { dimensions, surfaceFinish } = productConfig;
    
    // Calculate area
    const area = (dimensions.width * dimensions.height) / 10000; // m²
    const totalArea = area * quantity;
    
    // Base price calculation
    let basePrice = 0;
    if (this.config.basePrice.calculation === 'per_m2') {
      basePrice = totalArea * this.config.basePrice.basePricePerM2;
    }
    
    // Surface finish multiplier
    const surfaceFinishMultiplier = this.config.modifiers.surfaceFinish[surfaceFinish] || 1.0;
    const surfaceFinishCost = basePrice * (surfaceFinishMultiplier - 1);
    
    // Custom size premium
    const isCustomSize = this.isCustomSize(dimensions);
    const customSizeCost = isCustomSize && totalArea > this.config.modifiers.customSize.threshold
      ? basePrice * (this.config.modifiers.customSize.multiplier - 1)
      : 0;
    
    // Thickness multiplier
    const thicknessMultiplier = this.config.modifiers.thickness[dimensions.thickness] || 1.0;
    const thicknessCost = basePrice * (thicknessMultiplier - 1);
    
    // Quantity discount
    const quantityDiscount = this.calculateQuantityDiscount(totalArea);
    const quantityDiscountAmount = (basePrice + surfaceFinishCost + customSizeCost + thicknessCost) * quantityDiscount;
    
    // Urgency multiplier
    const urgencyMultiplier = this.config.modifiers.urgency[urgency];
    const urgencyCost = basePrice * (urgencyMultiplier - 1);
    
    // Additional costs
    let cuttingCost = 0;
    let edgePolishingCost = 0;
    let packagingCost = 0;
    let shippingCost = 0;
    
    if (options.includeCutting && isCustomSize) {
      cuttingCost = totalArea * this.config.additionalCosts.cutting;
    }
    
    if (options.includeEdgePolishing) {
      const perimeter = 2 * (dimensions.width + dimensions.height) / 100; // meters
      edgePolishingCost = perimeter * quantity * this.config.additionalCosts.edgePolishing;
    }
    
    if (options.includePackaging) {
      packagingCost = totalArea * this.config.additionalCosts.packaging;
    }
    
    if (options.includeShipping) {
      shippingCost = this.calculateShippingCost(totalArea, basePrice);
    }
    
    // Calculate total
    const subtotal = basePrice + surfaceFinishCost + customSizeCost + thicknessCost + urgencyCost;
    const discountedSubtotal = subtotal - quantityDiscountAmount;
    const additionalCostsTotal = cuttingCost + edgePolishingCost + packagingCost + shippingCost;
    const total = discountedSubtotal + additionalCostsTotal;
    
    // Build breakdown
    const breakdown: PriceBreakdown['breakdown'] = [
      { label: 'Temel Fiyat', amount: basePrice, type: 'base' }
    ];
    
    if (surfaceFinishCost > 0) {
      breakdown.push({ label: 'Yüzey İşlemi', amount: surfaceFinishCost, type: 'addition' });
    }
    
    if (customSizeCost > 0) {
      breakdown.push({ label: 'Özel Ebat', amount: customSizeCost, type: 'addition' });
    }
    
    if (thicknessCost > 0) {
      breakdown.push({ label: 'Kalınlık', amount: thicknessCost, type: 'addition' });
    }
    
    if (urgencyCost > 0) {
      breakdown.push({ label: 'Hızlı Teslimat', amount: urgencyCost, type: 'addition' });
    }
    
    if (quantityDiscountAmount > 0) {
      breakdown.push({ label: 'Miktar İndirimi', amount: -quantityDiscountAmount, type: 'discount' });
    }
    
    if (cuttingCost > 0) {
      breakdown.push({ label: 'Kesim Ücreti', amount: cuttingCost, type: 'addition' });
    }
    
    if (edgePolishingCost > 0) {
      breakdown.push({ label: 'Kenar Cilalama', amount: edgePolishingCost, type: 'addition' });
    }
    
    if (packagingCost > 0) {
      breakdown.push({ label: 'Ambalaj', amount: packagingCost, type: 'addition' });
    }
    
    if (shippingCost > 0) {
      breakdown.push({ label: 'Kargo', amount: shippingCost, type: 'addition' });
    }

    return {
      basePrice,
      surfaceFinishCost,
      customSizeCost,
      cuttingCost,
      quantityDiscount: quantityDiscountAmount,
      total,
      currency: this.config.basePrice.currency,
      vatIncluded: this.config.basePrice.vatIncluded,
      breakdown
    };
  }

  /**
   * Check if dimensions are custom size
   */
  private isCustomSize(dimensions: { width: number; height: number; thickness: number }): boolean {
    // Standard sizes (this should come from configuration)
    const standardSizes = [
      { width: 30, height: 60 },
      { width: 60, height: 60 },
      { width: 80, height: 80 },
      { width: 25, height: 40 },
      { width: 30, height: 30 },
      { width: 40, height: 80 }
    ];
    
    return !standardSizes.some(size => 
      size.width === dimensions.width && size.height === dimensions.height
    );
  }

  /**
   * Calculate quantity discount
   */
  private calculateQuantityDiscount(totalArea: number): number {
    const discountTier = this.config.modifiers.quantity.find(tier => 
      totalArea >= tier.min && totalArea <= tier.max
    );
    
    return discountTier?.discount || 0;
  }

  /**
   * Calculate shipping cost
   */
  private calculateShippingCost(totalArea: number, basePrice: number): number {
    const { shipping, freeShippingThreshold } = this.config.additionalCosts;
    
    if (shipping === 'included') {
      return 0;
    }
    
    if (shipping === 'free_over_amount' && freeShippingThreshold && basePrice >= freeShippingThreshold) {
      return 0;
    }
    
    // Simple shipping calculation based on area
    if (totalArea <= 10) return 50;
    if (totalArea <= 50) return 100;
    if (totalArea <= 100) return 150;
    return 200;
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(
    productConfig: AdvancedProductConfig,
    quantity: number,
    urgency: string,
    options: any
  ): string {
    return JSON.stringify({
      productId: productConfig.productId,
      dimensions: productConfig.dimensions,
      surfaceFinish: productConfig.surfaceFinish,
      quantity,
      urgency,
      options
    });
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Update pricing configuration
   */
  updateConfiguration(config: Partial<PricingConfiguration>): void {
    this.config = { ...this.config, ...config };
    this.clearCache();
  }

  /**
   * Get surface finish multiplier
   */
  getSurfaceFinishMultiplier(finish: SurfaceFinishName): number {
    return this.config.modifiers.surfaceFinish[finish] || 1.0;
  }

  /**
   * Get thickness multiplier
   */
  getThicknessMultiplier(thickness: number): number {
    return this.config.modifiers.thickness[thickness] || 1.0;
  }

  /**
   * Estimate delivery time based on urgency
   */
  getEstimatedDeliveryTime(urgency: 'standard' | 'fast' | 'express'): string {
    const deliveryTimes = {
      standard: '20-30 iş günü',
      fast: '10-15 iş günü',
      express: '5-7 iş günü'
    };
    
    return deliveryTimes[urgency];
  }
}

// Export singleton instance
export const dynamicPricingService = new DynamicPricingService();
