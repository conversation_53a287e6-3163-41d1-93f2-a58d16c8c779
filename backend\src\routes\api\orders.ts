import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { authMiddleware, AuthenticatedRequest } from '../../middleware/authMiddleware';
import { asyncHandler } from '../../middleware/errorHandler';
import { z } from 'zod';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const createOrderSchema = z.object({
  quoteId: z.string().min(1, 'Quote ID is required'),
  shippingAddress: z.object({
    street: z.string().min(1, 'Street is required'),
    city: z.string().min(1, 'City is required'),
    state: z.string().optional(),
    postalCode: z.string().min(1, 'Postal code is required'),
    country: z.string().min(1, 'Country is required'),
    phone: z.string().optional()
  }),
  notes: z.string().optional(),
  paymentMethod: z.enum(['BANK_TRANSFER', 'CREDIT_CARD', 'PAYPAL']),
  deliveryPreference: z.object({
    preferredDate: z.string().optional(),
    timeSlot: z.string().optional(),
    specialInstructions: z.string().optional()
  }).optional()
});

const updateOrderStatusSchema = z.object({
  status: z.enum(['PENDING', 'CONFIRMED', 'PRODUCTION', 'SHIPPED', 'DELIVERED', 'CANCELLED']),
  notes: z.string().optional(),
  trackingNumber: z.string().optional(),
  estimatedDelivery: z.string().optional()
});

const querySchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('20'),
  status: z.string().optional(),
  producerId: z.string().optional(),
  customerId: z.string().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'totalAmount']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional()
});

/**
 * @swagger
 * /api/orders:
 *   get:
 *     summary: Get orders with pagination and filtering
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 */
router.get('/', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;
  const query = querySchema.parse(req.query);
  const { page, limit, status, producerId, customerId, sortBy, sortOrder, dateFrom, dateTo } = query;
  
  const skip = (page - 1) * limit;
  
  // Build where clause based on user role
  const where: any = {};
  
  if (user.userType === 'customer') {
    where.customerId = user.id;
  } else if (user.userType === 'producer') {
    where.quote = {
      producerId: user.id
    };
  } else if (user.userType === 'admin') {
    // Admin can see all orders with optional filters
    if (producerId) {
      where.quote = { producerId };
    }
    if (customerId) {
      where.customerId = customerId;
    }
  }
  
  if (status) where.status = status;
  
  if (dateFrom || dateTo) {
    where.createdAt = {};
    if (dateFrom) where.createdAt.gte = new Date(dateFrom);
    if (dateTo) where.createdAt.lte = new Date(dateTo);
  }
  
  const [orders, total] = await Promise.all([
    prisma.order.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sortBy]: sortOrder },
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            profile: {
              select: {
                phone: true,
                city: true,
                country: true
              }
            }
          }
        },
        quote: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                category: true,
                images: {
                  where: { isPrimary: true },
                  take: 1
                }
              }
            },
            producer: {
              select: {
                id: true,
                companyName: true,
                profile: {
                  select: {
                    city: true,
                    country: true
                  }
                }
              }
            }
          }
        },
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paymentMethod: true,
            createdAt: true
          }
        },
        shipments: {
          select: {
            id: true,
            trackingNumber: true,
            status: true,
            estimatedDelivery: true,
            actualDelivery: true
          }
        }
      }
    }),
    prisma.order.count({ where })
  ]);
  
  const totalPages = Math.ceil(total / limit);
  
  res.json({
    success: true,
    data: {
      orders,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @swagger
 * /api/orders/{id}:
 *   get:
 *     summary: Get order by ID
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Order retrieved successfully
 *       404:
 *         description: Order not found
 */
router.get('/:id', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const user = req.user!;
  
  const where: any = { id };
  
  // Apply access control based on user role
  if (user.userType === 'customer') {
    where.customerId = user.id;
  } else if (user.userType === 'producer') {
    where.quote = {
      producerId: user.id
    };
  }
  // Admin can access any order
  
  const order = await prisma.order.findFirst({
    where,
    include: {
      customer: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          profile: true
        }
      },
      quote: {
        include: {
          product: {
            include: {
              images: true
            }
          },
          producer: {
            select: {
              id: true,
              companyName: true,
              profile: true
            }
          }
        }
      },
      payments: {
        orderBy: { createdAt: 'desc' }
      },
      shipments: {
        orderBy: { createdAt: 'desc' }
      },
      // deliverySchedules: {
      //   include: {
      //     payment: true
      //   },
      //   orderBy: { deliveryNumber: 'asc' }
      // },
      orderItems: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              category: true
            }
          }
        }
      }
    }
  });
  
  if (!order) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Order not found',
        statusCode: 404
      }
    });
  }
  
  res.json({
    success: true,
    data: order
  });
}));

/**
 * @swagger
 * /api/orders:
 *   post:
 *     summary: Create new order from quote
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               quoteId:
 *                 type: string
 *               shippingAddress:
 *                 type: object
 *               paymentMethod:
 *                 type: string
 *     responses:
 *       201:
 *         description: Order created successfully
 */
router.post('/', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;
  
  if (user.userType !== 'customer') {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Only customers can create orders',
        statusCode: 403
      }
    });
  }
  
  const orderData = createOrderSchema.parse(req.body);
  
  // Verify quote exists and belongs to user
  const quote = await prisma.quote.findFirst({
    where: {
      id: orderData.quoteId,
      customerId: user.id,
      status: 'ACCEPTED'
    },
    include: {
      product: true,
      producer: true
    }
  });
  
  if (!quote) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Quote not found or not accepted',
        statusCode: 404
      }
    });
  }
  
  // Check if order already exists for this quote
  const existingOrder = await prisma.order.findFirst({
    where: { quoteId: quote.id }
  });
  
  if (existingOrder) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Order already exists for this quote',
        statusCode: 400
      }
    });
  }
  
  // Calculate commission based on m² - temporarily use a default value
  const totalM2 = 100; // Default value, should be extracted from quote items
  const commissionPerM2 = parseFloat(process.env.PLATFORM_COMMISSION_PER_M2 || '1.00');
  const platformCommission = totalM2 * commissionPerM2;

  // Create order
  const order = await prisma.order.create({
    data: {
      orderNumber: `ORD-${Date.now()}`,
      customerId: user.id,
      producerId: quote.producerId,
      quoteId: quote.id,
      items: quote.items || [],
      subtotal: quote.totalPrice,
      totalAmount: quote.totalPrice,
      totalM2: totalM2,
      platformCommission: platformCommission,
      deliveryAddress: orderData.shippingAddress,
      shippingAddress: orderData.shippingAddress,
      notes: orderData.notes,
      paymentMethod: orderData.paymentMethod,
      status: 'PENDING'
    },
    include: {
      quote: {
        include: {
          product: true,
          producer: true
        }
      }
    }
  });
  
  // Create order items from quote items
  if (quote.items && Array.isArray(quote.items)) {
    const orderItems = quote.items.map((item: any) => ({
      orderId: order.id,
      productId: quote.productId,
      quantity: item.quantity || 1,
      unitPrice: item.unitPrice || quote.totalPrice,
      totalPrice: (item.quantity || 1) * (item.unitPrice || quote.totalPrice),
      specifications: item.specifications || {},
      dimensions: item.dimensions || {}
    }));
    
    await prisma.orderItem.createMany({
      data: orderItems
    });
  }
  
  // Send notification using notification service
  const notificationService = req.app.get('notificationService');
  if (notificationService) {
    await notificationService.createAndSendNotification({
      userId: quote.producerId,
      title: 'New Order Received',
      message: `You have received a new order for ${quote.product.name}`,
      notificationType: 'ORDER_CREATED',
      relatedEntityId: order.id,
      relatedEntityType: 'ORDER',
      priority: 'HIGH'
    });
  }
  
  res.status(201).json({
    success: true,
    data: order,
    message: 'Order created successfully'
  });
}));

/**
 * @swagger
 * /api/orders/{id}/status:
 *   put:
 *     summary: Update order status (Producer/Admin only)
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Order status updated successfully
 */
router.put('/:id/status', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const user = req.user!;
  
  if (!['PRODUCER', 'ADMIN'].includes(user.userType)) {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Only producers and admins can update order status',
        statusCode: 403
      }
    });
  }
  
  const updateData = updateOrderStatusSchema.parse(req.body);
  
  // Check if order exists and user has permission
  const where: any = { id };
  if (user.userType === 'producer') {
    where.quote = {
      producerId: user.id
    };
  }
  
  const order = await prisma.order.findFirst({
    where,
    include: {
      customer: true,
      quote: {
        include: {
          product: true
        }
      }
    }
  });
  
  if (!order) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Order not found',
        statusCode: 404
      }
    });
  }
  
  // Update order
  const updatedOrder = await prisma.order.update({
    where: { id },
    data: {
      status: updateData.status,
      notes: updateData.notes ? `${order.notes || ''}\n${updateData.notes}` : order.notes,
      trackingNumber: updateData.trackingNumber,
      estimatedDelivery: updateData.estimatedDelivery ? new Date(updateData.estimatedDelivery) : undefined
    }
  });
  
  // Send notification using notification service
  const notificationService = req.app.get('notificationService');
  if (notificationService) {
    await notificationService.notifyOrderStatusUpdate(
      order.id,
      updateData.status,
      order.customerId,
      order.quote?.producerId
    );
  }
  
  res.json({
    success: true,
    data: updatedOrder,
    message: 'Order status updated successfully'
  });
}));

/**
 * @swagger
 * /api/orders/{id}/cancel:
 *   put:
 *     summary: Cancel order
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Order cancelled successfully
 */
router.put('/:id/cancel', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const user = req.user!;
  const { reason } = req.body;
  
  // Check if order exists and user has permission to cancel
  const where: any = { id };
  if (user.userType === 'customer') {
    where.customerId = user.id;
  } else if (user.userType === 'producer') {
    where.quote = {
      producerId: user.id
    };
  }
  // Admin can cancel any order
  
  const order = await prisma.order.findFirst({
    where,
    include: {
      customer: true,
      quote: {
        include: {
          product: true,
          producer: true
        }
      }
    }
  });
  
  if (!order) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Order not found',
        statusCode: 404
      }
    });
  }
  
  // Check if order can be cancelled
  if (['DELIVERED', 'CANCELLED'].includes(order.status)) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Order cannot be cancelled',
        statusCode: 400
      }
    });
  }
  
  // Cancel order
  const cancelledOrder = await prisma.order.update({
    where: { id },
    data: {
      status: 'CANCELLED',
      notes: `${order.notes || ''}\nCancelled by ${user.userType}: ${reason || 'No reason provided'}`
    }
  });
  
  // Create notifications
  const notificationTargets = [];
  
  if (user.userType !== 'customer') {
    notificationTargets.push({
      userId: order.customerId,
      title: 'Order Cancelled',
      message: `Your order for ${order.quote?.product?.name || 'product'} has been cancelled`,
      notificationType: 'SYSTEM_ANNOUNCEMENT' as any,
      relatedEntityId: order.id,
      relatedEntityType: 'ORDER'
    });
  }
  
  if (user.userType !== 'producer') {
    notificationTargets.push({
      userId: order.quote?.producerId || '',
      title: 'Order Cancelled',
      message: `Order for ${order.quote?.product?.name || 'product'} has been cancelled`,
      notificationType: 'SYSTEM_ANNOUNCEMENT' as any,
      relatedEntityId: order.id,
      relatedEntityType: 'ORDER'
    });
  }
  
  if (notificationTargets.length > 0) {
    await prisma.notification.createMany({
      data: notificationTargets
    });
  }
  
  res.json({
    success: true,
    data: cancelledOrder,
    message: 'Order cancelled successfully'
  });
}));

/**
 * @swagger
 * /api/orders/{id}/cancel:
 *   put:
 *     summary: Cancel order
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Order cancelled successfully
 */
router.put('/:id/cancel', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const user = req.user!;
  const { reason } = req.body;

  // Check if order exists and user has permission to cancel
  const where: any = { id };
  if (user.userType === 'customer') {
    where.customerId = user.id;
  } else if (user.userType === 'producer') {
    where.quote = {
      producerId: user.id
    };
  }

  const order = await prisma.order.findFirst({
    where,
    include: {
      customer: true,
      quote: {
        include: {
          product: true,
          producer: true
        }
      }
    }
  });

  if (!order) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Order not found',
        statusCode: 404
      }
    });
  }

  // Check if order can be cancelled
  if (['DELIVERED', 'CANCELLED'].includes(order.status)) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Order cannot be cancelled',
        statusCode: 400
      }
    });
  }

  // Cancel order
  const cancelledOrder = await prisma.order.update({
    where: { id },
    data: {
      status: 'CANCELLED',
      notes: `${order.notes || ''}\nCancelled by ${user.userType}: ${reason || 'No reason provided'}`
    }
  });

  // Create notifications
  const notificationTargets = [];

  if (user.userType !== 'customer') {
    notificationTargets.push({
      userId: order.customerId,
      title: 'Order Cancelled',
      message: `Your order for ${order.quote?.product?.name || 'product'} has been cancelled`,
      notificationType: 'SYSTEM_ANNOUNCEMENT' as any,
      relatedEntityId: order.id,
      relatedEntityType: 'ORDER'
    });
  }

  if (user.userType !== 'producer') {
    notificationTargets.push({
      userId: order.quote?.producerId || '',
      title: 'Order Cancelled',
      message: `Order for ${order.quote?.product?.name || 'product'} has been cancelled`,
      notificationType: 'SYSTEM_ANNOUNCEMENT' as any,
      relatedEntityId: order.id,
      relatedEntityType: 'ORDER'
    });
  }

  if (notificationTargets.length > 0) {
    await prisma.notification.createMany({
      data: notificationTargets
    });
  }

  res.json({
    success: true,
    data: cancelledOrder,
    message: 'Order cancelled successfully'
  });
}));

export default router;
