'use client';

import React, { useState } from 'react';
import { Navigation } from "@/components/ui/navigation";
import { useSimpleTranslation } from "@/hooks/useSimpleTranslation";

export default function ThreeDShowroomPage() {
  const [viewMode, setViewMode] = useState<'room' | 'product'>('room');
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const { t } = useSimpleTranslation();

  // Create navigation links with translations
  const navigationLinks = [
    { name: t('nav.home'), href: "/" },
    { name: t('nav.products'), href: "/products" },
    { name: t('nav.3d_showroom'), href: "/3d-showroom", active: true },
    { name: t('nav.news'), href: "/news" },
    { name: t('nav.about'), href: "/about" },
    { name: t('nav.contact'), href: "/contact" }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Navigation
        brand={{
          name: "Türkiye Doğal Taş Pazarı",
          href: "/"
        }}
        links={navigationLinks}
      />

      {/* Header */}
      <div className="bg-white shadow border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-blue-600 rounded"></div>
              <h1 className="text-2xl font-bold text-gray-900">{t('3d_showroom.title')}</h1>
              <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-sm">{t('3d_showroom.beta')}</span>
            </div>

            <div className="flex space-x-2">
              <button
                className="px-3 py-1 border rounded text-sm hover:bg-gray-50"
                onClick={() => alert(t('3d_showroom.share_coming_soon'))}
              >
                {t('3d_showroom.share')}
              </button>
              <button
                className="px-3 py-1 border rounded text-sm hover:bg-gray-50"
                onClick={() => alert(t('3d_showroom.download_coming_soon'))}
              >
                {t('3d_showroom.download')}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">

          {/* Sol Panel */}
          <div className="lg:col-span-1 space-y-4">

            {/* Görünüm Modu */}
            <div className="bg-white rounded shadow p-4">
              <h3 className="text-lg font-semibold mb-3">{t('3d_showroom.view_mode')}</h3>
              <div className="grid grid-cols-2 gap-2">
                <button
                  className={`px-3 py-2 rounded ${viewMode === 'room' ? 'bg-blue-600 text-white' : 'border'}`}
                  onClick={() => setViewMode('room')}
                >
                  {t('3d_showroom.room_view')}
                </button>
                <button
                  className={`px-3 py-2 rounded ${viewMode === 'product' ? 'bg-blue-600 text-white' : 'border'}`}
                  onClick={() => setViewMode('product')}
                >
                  {t('3d_showroom.product_view')}
                </button>
              </div>
            </div>

            {/* Ürün Seçimi */}
            <div className="bg-white rounded shadow p-4">
              <h3 className="text-lg font-semibold mb-3">{t('3d_showroom.product_selection')}</h3>
              <div className="space-y-2">
                {['Carrara Mermer', 'Granit Siyah', 'Traverten Bej', 'Oniks Yeşil'].map((product) => (
                  <button
                    key={product}
                    className={`w-full text-left px-3 py-2 rounded ${selectedProduct === product ? 'bg-blue-100 border-blue-300' : 'border'}`}
                    onClick={() => setSelectedProduct(product)}
                  >
                    {product}
                  </button>
                ))}
              </div>
            </div>

            {/* Ayarlar */}
            <div className="bg-white rounded shadow p-4">
              <h3 className="text-lg font-semibold mb-3">{t('3d_showroom.settings')}</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium mb-1">{t('3d_showroom.lighting')}</label>
                  <select className="w-full border rounded px-2 py-1">
                    <option>{t('3d_showroom.natural_light')}</option>
                    <option>{t('3d_showroom.warm_light')}</option>
                    <option>{t('3d_showroom.cool_light')}</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">{t('3d_showroom.environment')}</label>
                  <select className="w-full border rounded px-2 py-1">
                    <option>{t('3d_showroom.bathroom')}</option>
                    <option>{t('3d_showroom.kitchen')}</option>
                    <option>{t('3d_showroom.living_room')}</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Ana 3D Görüntüleyici */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded shadow h-96 lg:h-[600px] flex items-center justify-center">
              <div className="text-center">
                <div className="text-6xl mb-4">🏗️</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {t('3d_showroom.coming_soon')}
                </h3>
                <p className="text-gray-600 max-w-md">
                  {t('3d_showroom.development_message')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
