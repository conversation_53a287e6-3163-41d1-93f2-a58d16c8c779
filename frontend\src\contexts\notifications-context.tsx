'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface Notification {
  id: string;
  type: 'quote_response' | 'order_update' | 'sample_update' | 'payment' | 'system';
  title: string;
  message: string;
  status: 'unread' | 'read' | 'archived';
  createdAt: Date;
  relatedId?: string; // ID of related quote, order, etc.
  actionUrl?: string; // URL to navigate when clicked
  priority: 'low' | 'medium' | 'high';
}

interface NotificationsContextType {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  archiveNotification: (notificationId: string) => void;
  deleteNotification: (notificationId: string) => void;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'status'>) => void;
  refreshNotifications: () => void;
}

const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationsContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationsProvider');
  }
  return context;
};

interface NotificationsProviderProps {
  children: ReactNode;
}

export const NotificationsProvider: React.FC<NotificationsProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Mock data - gerçek uygulamada API'den gelecek
  const mockNotifications: Notification[] = [
    {
      id: '1',
      type: 'quote_response',
      title: 'Yeni Teklif Yanıtı',
      message: 'Travertine ürünü için teklif yanıtı aldınız.',
      status: 'unread',
      createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 dakika önce
      relatedId: 'quote-123',
      actionUrl: '/customer/requests/active',
      priority: 'high'
    },
    {
      id: '2',
      type: 'order_update',
      title: 'Sipariş Durumu Güncellendi',
      message: 'Siparişiniz üretim aşamasına geçti.',
      status: 'unread',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 saat önce
      relatedId: 'order-456',
      actionUrl: '/customer/orders/ongoing',
      priority: 'medium'
    },
    {
      id: '3',
      type: 'sample_update',
      title: 'Numune Talebi Onaylandı',
      message: 'Marble ürünü için numune talebiniz onaylandı.',
      status: 'read',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 gün önce
      relatedId: 'sample-789',
      actionUrl: '/customer/requests/samples',
      priority: 'medium'
    },
    {
      id: '4',
      type: 'payment',
      title: 'Ödeme Hatırlatması',
      message: 'Kargo ücreti ödemeniz bekleniyor.',
      status: 'unread',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 saat önce
      relatedId: 'payment-101',
      actionUrl: '/customer/requests/samples/payment',
      priority: 'high'
    },
    {
      id: '5',
      type: 'system',
      title: 'Sistem Güncellemesi',
      message: 'Platform yeni özelliklerle güncellendi.',
      status: 'read',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3), // 3 gün önce
      actionUrl: '/customer/dashboard',
      priority: 'low'
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setNotifications(mockNotifications);
      setIsLoading(false);
    }, 1000);
  }, []);

  const unreadCount = notifications.filter(n => n.status === 'unread').length;

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, status: 'read' as const }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, status: 'read' as const }))
    );
  };

  const archiveNotification = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, status: 'archived' as const }
          : notification
      )
    );
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== notificationId));
  };

  const addNotification = (notificationData: Omit<Notification, 'id' | 'createdAt' | 'status'>) => {
    const newNotification: Notification = {
      ...notificationData,
      id: Date.now().toString(),
      createdAt: new Date(),
      status: 'unread'
    };
    
    setNotifications(prev => [newNotification, ...prev]);
  };

  const refreshNotifications = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setNotifications(mockNotifications);
      setIsLoading(false);
    }, 500);
  };

  const value: NotificationsContextType = {
    notifications: notifications.filter(n => n.status !== 'archived'),
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    archiveNotification,
    deleteNotification,
    addNotification,
    refreshNotifications
  };

  return (
    <NotificationsContext.Provider value={value}>
      {children}
    </NotificationsContext.Provider>
  );
};

export default NotificationsProvider;
