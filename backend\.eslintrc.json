{"extends": ["@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-function": "warn", "prefer-const": "error", "no-var": "error", "no-console": "off", "eqeqeq": "error", "curly": "error"}, "env": {"node": true, "es2022": true}}