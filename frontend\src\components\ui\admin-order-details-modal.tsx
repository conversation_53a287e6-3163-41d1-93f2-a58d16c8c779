"use client"

import * as React from "react"
import { But<PERSON> } from "./button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "./card"
import { Badge } from "./badge"
import {
  X,
  User,
  Package,
  Calendar,
  DollarSign,
  MapPin,
  FileText,
  Clock,
  Building2,
  Phone,
  Mail,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Target,
  Eye,
  ExternalLink,
  Edit,
  CreditCard,
  Truck,
  Download,
  MessageSquare,
  History,
  Upload,
  ArrowRight,
  Minus,
  Plus,
  Receipt
} from "lucide-react"
import { Order } from "@/types/orders"
import { OrderTimeline } from "./order-timeline"
import { OrderDocuments } from "./order-documents"

interface AdminOrderDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  order: Order | null
  onStatusUpdate?: (order: Order) => void
  onSendMessage?: (order: Order) => void
}

export function AdminOrderDetailsModal({
  isOpen,
  onClose,
  order,
  onStatusUpdate,
  onSendMessage
}: AdminOrderDetailsModalProps) {
  const [activeTab, setActiveTab] = React.useState('overview');
  if (!isOpen || !order) return null

  const getStatusColor = (status: string) => {
    const statusConfig = {
      'pending-payment': 'bg-yellow-100 text-yellow-800',
      'payment-confirmed': 'bg-blue-100 text-blue-800',
      'in-production': 'bg-orange-100 text-orange-800',
      'ready-for-shipment': 'bg-purple-100 text-purple-800',
      'shipped': 'bg-indigo-100 text-indigo-800',
      'delivered': 'bg-green-100 text-green-800',
      'completed': 'bg-gray-100 text-gray-800',
      'cancelled': 'bg-red-100 text-red-800',
      'refunded': 'bg-red-100 text-red-800'
    }
    return statusConfig[status as keyof typeof statusConfig] || 'bg-gray-100 text-gray-800'
  }

  const getStatusText = (status: string) => {
    const statusLabels = {
      'pending-payment': 'Ön Ödeme Bekliyor',
      'payment-confirmed': 'Ödeme Onaylandı',
      'in-production': 'Üretimde',
      'ready-for-shipment': 'Sevkiyat Hazır',
      'shipped': 'Kargoya Verildi',
      'delivered': 'Teslim Edildi',
      'completed': 'Tamamlandı',
      'cancelled': 'İptal Edildi',
      'refunded': 'İade Edildi'
    }
    return statusLabels[status as keyof typeof statusLabels] || status
  }

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      {/* Full Screen Modal */}
      <div className="bg-white rounded-lg shadow-xl w-full h-full max-w-7xl max-h-[95vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Sipariş Detayları</h2>
              <p className="text-gray-600">
                {order.orderNumber} - {order.customer.companyName}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Badge className={getStatusColor(order.status)}>
              {getStatusText(order.status)}
            </Badge>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 bg-white">
          <div className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Package className="w-4 h-4 inline mr-2" />
              Genel Bakış
            </button>
            <button
              onClick={() => setActiveTab('payments')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'payments'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <CreditCard className="w-4 h-4 inline mr-2" />
              Ödeme Takibi
            </button>
            <button
              onClick={() => setActiveTab('documents')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'documents'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <FileText className="w-4 h-4 inline mr-2" />
              Belgeler
            </button>
            <button
              onClick={() => setActiveTab('shipments')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'shipments'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Truck className="w-4 h-4 inline mr-2" />
              Sevkiyatlar
            </button>
            <button
              onClick={() => setActiveTab('timeline')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'timeline'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <History className="w-4 h-4 inline mr-2" />
              Zaman Çizelgesi
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
            {/* Order Overview */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Customer Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 h-5" />
                    Müşteri Bilgileri
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Şirket</label>
                      <p className="font-medium">{order.customer.companyName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">İletişim Kişisi</label>
                      <p>{order.customer.contactPerson}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">E-posta</label>
                      <a
                        href={`mailto:${order.customer.email}`}
                        className="text-blue-600 hover:underline"
                      >
                        {order.customer.email}
                      </a>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Telefon</label>
                      <a
                        href={`tel:${order.customer.phone}`}
                        className="text-blue-600 hover:underline"
                      >
                        {order.customer.phone}
                      </a>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full mt-3"
                      onClick={() => {
                        // Müşteri profil sayfasına git
                        window.open(`/admin/customers/${order.customer.id}`, '_blank')
                      }}
                    >
                      <Building2 className="w-4 h-4 mr-2" />
                      Müşteri Profilini Görüntüle
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Producer Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="w-5 h-5" />
                    Üretici Bilgileri
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Şirket</label>
                      <p className="font-medium">{order.producer.companyName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">İletişim Kişisi</label>
                      <p>{order.producer.contactPerson}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">E-posta</label>
                      <a
                        href={`mailto:${order.producer.email}`}
                        className="text-blue-600 hover:underline"
                      >
                        {order.producer.email}
                      </a>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Telefon</label>
                      <a
                        href={`tel:${order.producer.phone}`}
                        className="text-blue-600 hover:underline"
                      >
                        {order.producer.phone}
                      </a>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full mt-3"
                      onClick={() => {
                        // Üretici profil sayfasına git
                        window.open(`/admin/producers/${order.producer.id}`, '_blank')
                      }}
                    >
                      <Building2 className="w-4 h-4 mr-2" />
                      Üretici Profilini Görüntüle
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Order Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="w-5 h-5" />
                    Sipariş Özeti
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Sipariş No</label>
                      <p className="font-medium">{order.orderNumber}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Sipariş Tarihi</label>
                      <p>{new Date(order.createdAt).toLocaleDateString('tr-TR')}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Tahmini Teslim</label>
                      <p>{order.estimatedCompletionDate ? new Date(order.estimatedCompletionDate).toLocaleDateString('tr-TR') : '-'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Toplam Tutar</label>
                      <p className="text-lg font-bold text-green-600">
                        {formatCurrency(order.pricing.totalAmount, order.pricing.currency)}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full mt-3"
                      onClick={() => onStatusUpdate && onStatusUpdate(order)}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Durumu Güncelle
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Products */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  Sipariş Edilen Ürünler
                </CardTitle>
              </CardHeader>
              <CardContent>
                {order.products.map((product, productIndex) => (
                  <div key={productIndex} className="mb-6 last:mb-0">
                    {/* Product Header */}
                    <div className="flex items-center gap-4 mb-4 p-4 bg-blue-50 rounded-lg">
                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                        {product.productImage ? (
                          <img 
                            src={product.productImage} 
                            alt={product.productName}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Package className="w-8 h-8 text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3">
                          <button
                            className="text-blue-600 hover:underline font-semibold text-lg"
                            onClick={() => {
                              // Ürün detay sayfasına git
                              window.open(`/products/${product.productId}`, '_blank')
                            }}
                          >
                            {product.productName}
                          </button>
                          <Badge variant="outline">{product.category}</Badge>
                        </div>
                        <p className="text-gray-600 text-sm mt-1">
                          {product.specifications.length} farklı ebat
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Ürün detay sayfasına git
                          window.open(`/products/${product.productId}`, '_blank')
                        }}
                      >
                        <ExternalLink className="w-4 h-4 mr-1" />
                        Ürüne Git
                      </Button>
                    </div>

                    {/* Specifications Table */}
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse border border-gray-300">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Ebat</th>
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Yüzey</th>
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Ambalaj</th>
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Miktar</th>
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Birim Fiyat</th>
                            <th className="border border-gray-300 px-3 py-2 text-left font-medium text-sm">Toplam</th>
                          </tr>
                        </thead>
                        <tbody>
                          {product.specifications.map((spec, specIndex) => (
                            <tr key={specIndex} className="hover:bg-gray-50">
                              <td className="border border-gray-300 px-3 py-2 text-sm">
                                {spec.thickness}×{spec.width}×{spec.length}cm
                              </td>
                              <td className="border border-gray-300 px-3 py-2 text-sm">{spec.surface}</td>
                              <td className="border border-gray-300 px-3 py-2 text-sm">{spec.packaging}</td>
                              <td className="border border-gray-300 px-3 py-2 text-sm">{spec.quantity} {spec.unit}</td>
                              <td className="border border-gray-300 px-3 py-2 text-sm font-medium">
                                {formatCurrency(spec.unitPrice, spec.currency)}
                              </td>
                              <td className="border border-gray-300 px-3 py-2 text-sm font-bold text-green-600">
                                {formatCurrency(spec.totalPrice, spec.currency)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Pricing & Payment */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="w-5 h-5" />
                    Fiyat Detayları
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Ara Toplam:</span>
                      <span>{formatCurrency(order.pricing.subtotal, order.pricing.currency)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>KDV (%{order.pricing.taxRate * 100}):</span>
                      <span>{formatCurrency(order.pricing.taxAmount, order.pricing.currency)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Kargo:</span>
                      <span>{formatCurrency(order.pricing.shippingCost, order.pricing.currency)}</span>
                    </div>
                    {order.pricing.discountAmount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>İndirim:</span>
                        <span>-{formatCurrency(order.pricing.discountAmount, order.pricing.currency)}</span>
                      </div>
                    )}
                    <hr />
                    <div className="flex justify-between text-lg font-bold">
                      <span>Genel Toplam:</span>
                      <span className="text-green-600">{formatCurrency(order.pricing.totalAmount, order.pricing.currency)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="w-5 h-5" />
                    Ödeme Bilgileri
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Ön Ödeme</label>
                      <div className="flex justify-between items-center">
                        <span>{formatCurrency(order.payment.advancePayment.amount, order.pricing.currency)}</span>
                        <Badge className={order.payment.advancePayment.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                          {order.payment.advancePayment.status === 'completed' ? 'Ödendi' : 'Bekliyor'}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Kalan Ödeme</label>
                      <div className="flex justify-between items-center">
                        <span>{formatCurrency(order.payment.remainingPayment.amount, order.pricing.currency)}</span>
                        <Badge className={order.payment.remainingPayment.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                          {order.payment.remainingPayment.status === 'completed' ? 'Ödendi' : 'Bekliyor'}
                        </Badge>
                      </div>
                    </div>
                    <hr />
                    <div className="flex justify-between font-medium">
                      <span>Toplam Ödenen:</span>
                      <span className="text-green-600">{formatCurrency(order.payment.totalPaid, order.pricing.currency)}</span>
                    </div>
                    <div className="flex justify-between font-medium">
                      <span>Kalan Borç:</span>
                      <span className="text-red-600">{formatCurrency(order.payment.totalDue, order.pricing.currency)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Documents and Timeline */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <OrderDocuments
                documents={order.documents}
                onUpload={(file, type) => {
                  console.log('Upload document:', file.name, type)
                  alert('Belge yükleme özelliği yakında eklenecek')
                }}
                onDownload={(document) => {
                  console.log('Download document:', document.name)
                  // Gerçek uygulamada dosya indirme
                  window.open(document.url, '_blank')
                }}
                onView={(document) => {
                  console.log('View document:', document.name)
                  window.open(document.url, '_blank')
                }}
                onDelete={(documentId) => {
                  console.log('Delete document:', documentId)
                  alert('Belge silme özelliği yakında eklenecek')
                }}
              />
              <OrderTimeline events={order.timeline} />
            </div>
            </div>
          )}

          {activeTab === 'payments' && (
            <div className="space-y-6">
              {/* Payment Summary */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="w-5 h-5 text-green-600" />
                      Müşteri Ödemeleri
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Toplam Tutar:</span>
                        <span className="font-medium">{formatCurrency(order.pricing.totalAmount, order.pricing.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Ödenen:</span>
                        <span className="font-medium text-green-600">{formatCurrency(order.payment.totalPaid, order.pricing.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Kalan:</span>
                        <span className="font-medium text-red-600">{formatCurrency(order.payment.totalDue, order.pricing.currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${(order.payment.totalPaid / order.pricing.totalAmount) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="w-5 h-5 text-purple-600" />
                      Üretici Ödemeleri
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Net Tutar:</span>
                        <span className="font-medium">{formatCurrency(order.pricing.totalAmount * 0.95, order.pricing.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Gönderilen:</span>
                        <span className="font-medium text-green-600">{formatCurrency(order.payment.totalPaid * 0.95, order.pricing.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Bekleyen:</span>
                        <span className="font-medium text-orange-600">{formatCurrency(order.payment.totalDue * 0.95, order.pricing.currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-purple-600 h-2 rounded-full"
                          style={{ width: `${(order.payment.totalPaid / order.pricing.totalAmount) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Receipt className="w-5 h-5 text-blue-600" />
                      Hizmet Bedeli
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Toplam m²:</span>
                        <span className="font-medium">150 m²</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Birim Bedel:</span>
                        <span className="font-medium">$1.00/m²</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Toplam Bedel:</span>
                        <span className="font-medium text-blue-600">$150.00</span>
                      </div>
                      <div className="text-xs text-gray-500 text-center">
                        Otomatik hesaplanır
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Payment Flow Visualization */}
              <Card>
                <CardHeader>
                  <CardTitle>Ödeme Akışı</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    {/* Customer Payment */}
                    <div className="text-center">
                      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                        <User className="w-8 h-8 text-blue-600" />
                      </div>
                      <p className="text-sm font-medium">Müşteri</p>
                      <p className="text-lg font-bold text-blue-600">
                        {formatCurrency(order.payment.totalPaid, order.pricing.currency)}
                      </p>
                      <Badge className="bg-green-100 text-green-800">Alındı</Badge>
                    </div>

                    <ArrowRight className="w-6 h-6 text-gray-400" />

                    {/* Platform Processing */}
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                        <ArrowRight className="w-8 h-8 text-gray-600" />
                      </div>
                      <p className="text-sm font-medium">İşlem</p>
                      <p className="text-lg font-bold text-gray-600">
                        Hizmet Bedeli
                      </p>
                      <Badge className="bg-gray-100 text-gray-600">İşleniyor</Badge>
                    </div>

                    <ArrowRight className="w-6 h-6 text-gray-400" />

                    {/* Producer Payment */}
                    <div className="text-center">
                      <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                        <Building2 className="w-8 h-8 text-purple-600" />
                      </div>
                      <p className="text-sm font-medium">Üretici</p>
                      <p className="text-lg font-bold text-purple-600">
                        {formatCurrency(order.payment.totalPaid * 0.95, order.pricing.currency)}
                      </p>
                      <Badge className="bg-purple-100 text-purple-800">Gönderildi</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Payment History */}
              <Card>
                <CardHeader>
                  <CardTitle>Ödeme Geçmişi</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {order.payment.history?.map((payment: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          </div>
                          <div>
                            <p className="font-medium">Ödeme #{index + 1}</p>
                            <p className="text-sm text-gray-600">{new Date(payment.date).toLocaleDateString('tr-TR')}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatCurrency(payment.amount, order.pricing.currency)}</p>
                          <p className="text-sm text-gray-600">{payment.method}</p>
                        </div>
                      </div>
                    )) || (
                      <div className="text-center py-8 text-gray-500">
                        <CreditCard className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                        <p>Henüz ödeme yapılmamış</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'documents' && (
            <div className="space-y-6">
              {/* Customer Documents */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 h-5" />
                    Müşteri Belgeleri
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <OrderDocuments
                    documents={order.documents.filter((doc: any) => doc.category === 'customer')}
                    onUpload={(file, type) => {
                      console.log('Upload customer document:', file.name, type)
                      alert('Müşteri belgesi yükleme özelliği yakında eklenecek')
                    }}
                    onDownload={(document) => {
                      console.log('Download document:', document.name)
                      window.open(document.url, '_blank')
                    }}
                    onView={(document) => {
                      console.log('View document:', document.name)
                      window.open(document.url, '_blank')
                    }}
                    onDelete={(documentId) => {
                      console.log('Delete document:', documentId)
                      alert('Belge silme özelliği yakında eklenecek')
                    }}
                  />
                </CardContent>
              </Card>

              {/* Producer Documents */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="w-5 h-5" />
                    Üretici Belgeleri
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <OrderDocuments
                    documents={order.documents.filter((doc: any) => doc.category === 'producer')}
                    onUpload={(file, type) => {
                      console.log('Upload producer document:', file.name, type)
                      alert('Üretici belgesi yükleme özelliği yakında eklenecek')
                    }}
                    onDownload={(document) => {
                      console.log('Download document:', document.name)
                      window.open(document.url, '_blank')
                    }}
                    onView={(document) => {
                      console.log('View document:', document.name)
                      window.open(document.url, '_blank')
                    }}
                    onDelete={(documentId) => {
                      console.log('Delete document:', documentId)
                      alert('Belge silme özelliği yakında eklenecek')
                    }}
                  />
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'shipments' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Truck className="w-5 h-5" />
                    Sevkiyat Planı
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[1, 2, 3].map((shipment) => (
                      <div key={shipment} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium">Sevkiyat #{shipment}</h4>
                          <Badge className="bg-blue-100 text-blue-800">Planlandı</Badge>
                        </div>
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Miktar:</span>
                            <span className="ml-2 font-medium">50 m²</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Tarih:</span>
                            <span className="ml-2 font-medium">15 Şubat 2024</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Durum:</span>
                            <span className="ml-2 font-medium">Hazırlanıyor</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'timeline' && (
            <div className="space-y-6">
              <OrderTimeline events={order.timeline} />
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-6 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              Son güncelleme: {new Date(order.updatedAt).toLocaleDateString('tr-TR')} {new Date(order.updatedAt).toLocaleTimeString('tr-TR')}
            </div>
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose}>
                Kapat
              </Button>
              <Button
                variant="outline"
                onClick={() => onSendMessage && onSendMessage(order)}
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Mesaj Gönder
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  // Fatura belgesini bul
                  const invoice = order.documents.find(doc => doc.type === 'invoice')
                  if (invoice) {
                    window.open(invoice.url, '_blank')
                  } else {
                    alert('Fatura belgesi bulunamadı')
                  }
                }}
              >
                <Download className="w-4 h-4 mr-2" />
                Fatura İndir
              </Button>
              <Button onClick={() => onStatusUpdate && onStatusUpdate(order)}>
                <Edit className="w-4 h-4 mr-2" />
                Durumu Güncelle
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
