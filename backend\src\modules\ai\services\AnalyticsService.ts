/**
 * Analytics Service
 * Handles chatbot analytics, metrics, and learning insights
 */

import { PrismaClient } from '@prisma/client';
import { 
  ChatbotAnalytics, 
  IntentFrequency, 
  LanguageStats, 
  EscalationReason,
  KnowledgeGap,
  Improvement,
  LearningInsights,
  FailedResponse,
  FAQCandidate
} from '../types';

export class AnalyticsService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Get comprehensive chatbot analytics
   */
  async getAnalytics(startDate?: Date, endDate?: Date): Promise<ChatbotAnalytics> {
    try {
      const dateFilter = this.buildDateFilter(startDate, endDate);
      
      const [
        conversationMetrics,
        messageMetrics,
        feedbackMetrics,
        escalationMetrics,
        intentStats,
        languageStats
      ] = await Promise.all([
        this.getConversationMetrics(dateFilter),
        this.getMessageMetrics(dateFilter),
        this.getFeedbackMetrics(dateFilter),
        this.getEscalationMetrics(dateFilter),
        this.getIntentStatistics(dateFilter),
        this.getLanguageStatistics(dateFilter)
      ]);

      return {
        responseAccuracy: await this.calculateResponseAccuracy(dateFilter),
        resolutionRate: conversationMetrics.resolutionRate,
        averageResponseTime: messageMetrics.averageResponseTime,
        userSatisfactionScore: feedbackMetrics.averageRating,
        totalConversations: conversationMetrics.total,
        averageConversationLength: conversationMetrics.averageLength,
        mostCommonIntents: intentStats,
        languageDistribution: languageStats,
        escalationRate: escalationMetrics.rate,
        escalationReasons: escalationMetrics.reasons,
        humanResolutionRate: await this.calculateHumanResolutionRate(dateFilter),
        newKnowledgeGaps: await this.identifyKnowledgeGaps(dateFilter),
        improvementOpportunities: await this.identifyImprovementOpportunities(dateFilter)
      };
    } catch (error) {
      console.error('Error getting analytics:', error);
      throw error;
    }
  }

  /**
   * Get conversation metrics
   */
  private async getConversationMetrics(dateFilter: any) {
    const conversations = await this.prisma.chatbotConversation.findMany({
      where: {
        startedAt: dateFilter,
        status: { not: 'EXPIRED' }
      },
      select: {
        messageCount: true,
        resolvedIssues: true,
        unresolvedIssues: true,
        status: true
      }
    });

    const total = conversations.length;
    const resolved = conversations.filter(c => 
      c.resolvedIssues.length > 0 && c.unresolvedIssues.length === 0
    ).length;
    
    const totalMessages = conversations.reduce((sum, c) => sum + c.messageCount, 0);
    const averageLength = total > 0 ? totalMessages / total : 0;
    const resolutionRate = total > 0 ? resolved / total : 0;

    return {
      total,
      resolved,
      averageLength,
      resolutionRate
    };
  }

  /**
   * Get message metrics
   */
  private async getMessageMetrics(dateFilter: any) {
    const messages = await this.prisma.chatbotMessage.findMany({
      where: {
        createdAt: dateFilter,
        processingTimeMs: { not: null }
      },
      select: {
        processingTimeMs: true,
        role: true
      }
    });

    const botMessages = messages.filter(m => m.role === 'ASSISTANT');
    const totalResponseTime = botMessages.reduce((sum, m) => sum + (m.processingTimeMs || 0), 0);
    const averageResponseTime = botMessages.length > 0 ? totalResponseTime / botMessages.length : 0;

    return {
      total: messages.length,
      botMessages: botMessages.length,
      averageResponseTime
    };
  }

  /**
   * Get feedback metrics
   */
  private async getFeedbackMetrics(dateFilter: any) {
    const feedback = await this.prisma.chatbotFeedback.findMany({
      where: {
        createdAt: dateFilter
      },
      select: {
        rating: true
      }
    });

    const total = feedback.length;
    const totalRating = feedback.reduce((sum, f) => sum + f.rating, 0);
    const averageRating = total > 0 ? totalRating / total : 0;
    const positiveRatings = feedback.filter(f => f.rating >= 4).length;
    const negativeRatings = feedback.filter(f => f.rating <= 2).length;

    return {
      total,
      averageRating,
      positiveRatings,
      negativeRatings
    };
  }

  /**
   * Get escalation metrics
   */
  private async getEscalationMetrics(dateFilter: any) {
    const conversations = await this.prisma.chatbotConversation.findMany({
      where: {
        startedAt: dateFilter
      },
      select: {
        escalationLevel: true,
        escalationReason: true
      }
    });

    const total = conversations.length;
    const escalated = conversations.filter(c => c.escalationLevel > 0).length;
    const rate = total > 0 ? escalated / total : 0;

    // Count escalation reasons
    const reasonCounts: Record<string, number> = {};
    conversations.forEach(c => {
      if (c.escalationReason) {
        reasonCounts[c.escalationReason] = (reasonCounts[c.escalationReason] || 0) + 1;
      }
    });

    const reasons: EscalationReason[] = Object.entries(reasonCounts).map(([reason, count]) => ({
      reason,
      count,
      percentage: escalated > 0 ? (count / escalated) * 100 : 0
    }));

    return {
      rate,
      reasons
    };
  }

  /**
   * Get intent statistics
   */
  private async getIntentStatistics(dateFilter: any): Promise<IntentFrequency[]> {
    const messages = await this.prisma.chatbotMessage.findMany({
      where: {
        createdAt: dateFilter,
        intent: { not: null },
        role: 'USER'
      },
      select: {
        intent: true
      }
    });

    const intentCounts: Record<string, number> = {};
    messages.forEach(m => {
      if (m.intent) {
        intentCounts[m.intent] = (intentCounts[m.intent] || 0) + 1;
      }
    });

    const total = messages.length;
    return Object.entries(intentCounts)
      .map(([intent, count]) => ({
        intent: intent as any,
        count,
        percentage: total > 0 ? (count / total) * 100 : 0
      }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Get language statistics
   */
  private async getLanguageStatistics(dateFilter: any): Promise<LanguageStats[]> {
    const conversations = await this.prisma.chatbotConversation.findMany({
      where: {
        startedAt: dateFilter
      },
      select: {
        language: true
      }
    });

    const languageCounts: Record<string, number> = {};
    conversations.forEach(c => {
      languageCounts[c.language] = (languageCounts[c.language] || 0) + 1;
    });

    const total = conversations.length;
    return Object.entries(languageCounts)
      .map(([language, count]) => ({
        language,
        count,
        percentage: total > 0 ? (count / total) * 100 : 0
      }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Calculate response accuracy
   */
  private async calculateResponseAccuracy(dateFilter: any): Promise<number> {
    const feedback = await this.prisma.chatbotFeedback.findMany({
      where: {
        createdAt: dateFilter,
        rating: { gte: 4 } // Consider ratings 4-5 as accurate
      }
    });

    const totalFeedback = await this.prisma.chatbotFeedback.count({
      where: {
        createdAt: dateFilter
      }
    });

    return totalFeedback > 0 ? feedback.length / totalFeedback : 0;
  }

  /**
   * Calculate human resolution rate
   */
  private async calculateHumanResolutionRate(dateFilter: any): Promise<number> {
    const escalatedConversations = await this.prisma.chatbotConversation.findMany({
      where: {
        startedAt: dateFilter,
        escalationLevel: { gt: 0 }
      },
      select: {
        resolvedIssues: true,
        unresolvedIssues: true
      }
    });

    const resolved = escalatedConversations.filter(c => 
      c.resolvedIssues.length > 0 && c.unresolvedIssues.length === 0
    ).length;

    return escalatedConversations.length > 0 ? resolved / escalatedConversations.length : 0;
  }

  /**
   * Identify knowledge gaps
   */
  private async identifyKnowledgeGaps(dateFilter: any): Promise<KnowledgeGap[]> {
    // Find messages with low confidence or unknown intent
    const lowConfidenceMessages = await this.prisma.chatbotMessage.findMany({
      where: {
        createdAt: dateFilter,
        role: 'USER',
        OR: [
          { confidence: { lt: 0.5 } },
          { intent: 'UNKNOWN' }
        ]
      },
      select: {
        content: true,
        intent: true,
        confidence: true
      }
    });

    // Group by similar topics/keywords
    const topicGroups: Record<string, string[]> = {};
    lowConfidenceMessages.forEach(msg => {
      const keywords = this.extractKeywords(msg.content);
      const topic = keywords[0] || 'unknown';
      
      if (!topicGroups[topic]) {
        topicGroups[topic] = [];
      }
      topicGroups[topic].push(msg.content);
    });

    return Object.entries(topicGroups)
      .map(([topic, examples]) => ({
        topic,
        frequency: examples.length,
        examples: examples.slice(0, 3) // Show top 3 examples
      }))
      .filter(gap => gap.frequency >= 3) // Only show gaps with 3+ occurrences
      .sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * Identify improvement opportunities
   */
  private async identifyImprovementOpportunities(dateFilter: any): Promise<Improvement[]> {
    const opportunities: Improvement[] = [];

    // Check for high escalation rate
    const escalationMetrics = await this.getEscalationMetrics(dateFilter);
    if (escalationMetrics.rate > 0.15) { // 15% escalation rate threshold
      opportunities.push({
        area: 'Escalation Rate',
        description: `High escalation rate (${(escalationMetrics.rate * 100).toFixed(1)}%). Consider improving AI responses or escalation criteria.`,
        priority: 'high',
        estimatedImpact: 'Reduce escalations by 20-30%'
      });
    }

    // Check for low satisfaction scores
    const feedbackMetrics = await this.getFeedbackMetrics(dateFilter);
    if (feedbackMetrics.averageRating < 3.5) {
      opportunities.push({
        area: 'User Satisfaction',
        description: `Low average rating (${feedbackMetrics.averageRating.toFixed(1)}). Review and improve response quality.`,
        priority: 'high',
        estimatedImpact: 'Increase satisfaction by 15-25%'
      });
    }

    // Check for slow response times
    const messageMetrics = await this.getMessageMetrics(dateFilter);
    if (messageMetrics.averageResponseTime > 3000) { // 3 seconds
      opportunities.push({
        area: 'Response Time',
        description: `Slow average response time (${messageMetrics.averageResponseTime}ms). Optimize AI processing.`,
        priority: 'medium',
        estimatedImpact: 'Reduce response time by 30-50%'
      });
    }

    return opportunities;
  }

  /**
   * Get learning insights
   */
  async getLearningInsights(dateFilter?: any): Promise<LearningInsights> {
    const filter = dateFilter || this.buildDateFilter();
    
    return {
      knowledgeGaps: await this.identifyKnowledgeGaps(filter),
      failedResponses: await this.getFailedResponses(filter),
      faqCandidates: await this.identifyFAQCandidates(filter),
      improvementSuggestions: await this.identifyImprovementOpportunities(filter)
    };
  }

  /**
   * Get failed responses
   */
  private async getFailedResponses(dateFilter: any): Promise<FailedResponse[]> {
    const lowRatedMessages = await this.prisma.chatbotMessage.findMany({
      where: {
        createdAt: dateFilter,
        role: 'ASSISTANT',
        feedback: {
          some: {
            rating: { lte: 2 }
          }
        }
      },
      include: {
        feedback: true
      },
      take: 20
    });

    return lowRatedMessages.map(msg => ({
      message: msg.content,
      intent: msg.intent as any || 'UNKNOWN',
      confidence: msg.confidence ? parseFloat(msg.confidence.toString()) : 0,
      reason: msg.feedback[0]?.feedback || 'Low rating'
    }));
  }

  /**
   * Identify FAQ candidates
   */
  private async identifyFAQCandidates(dateFilter: any): Promise<FAQCandidate[]> {
    // Find frequently asked questions with similar patterns
    const userMessages = await this.prisma.chatbotMessage.findMany({
      where: {
        createdAt: dateFilter,
        role: 'USER'
      },
      select: {
        content: true,
        intent: true
      }
    });

    // Group similar questions
    const questionGroups: Record<string, { questions: string[]; intent: string }> = {};
    
    userMessages.forEach(msg => {
      const keywords = this.extractKeywords(msg.content);
      const key = keywords.slice(0, 2).join('_'); // Use first 2 keywords as key
      
      if (!questionGroups[key]) {
        questionGroups[key] = {
          questions: [],
          intent: msg.intent || 'UNKNOWN'
        };
      }
      questionGroups[key].questions.push(msg.content);
    });

    return Object.entries(questionGroups)
      .filter(([_, group]) => group.questions.length >= 5) // 5+ similar questions
      .map(([key, group]) => ({
        question: this.generateRepresentativeQuestion(group.questions),
        frequency: group.questions.length,
        suggestedAnswer: 'To be created based on successful responses',
        category: this.mapIntentToCategory(group.intent)
      }))
      .sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * Store daily analytics
   */
  async storeDailyAnalytics(date: Date): Promise<void> {
    try {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      const dateFilter = {
        gte: startOfDay,
        lte: endOfDay
      };

      const analytics = await this.getAnalytics(startOfDay, endOfDay);
      
      await this.prisma.chatbotAnalytics.upsert({
        where: { date: startOfDay },
        update: {
          totalConversations: analytics.totalConversations,
          averageResponseTime: analytics.averageResponseTime,
          averageRating: analytics.userSatisfactionScore,
          escalationRate: analytics.escalationRate,
          resolutionRate: analytics.resolutionRate,
          languageStats: JSON.stringify(analytics.languageDistribution),
          intentStats: JSON.stringify(analytics.mostCommonIntents),
          escalationReasons: JSON.stringify(analytics.escalationReasons)
        },
        create: {
          date: startOfDay,
          totalConversations: analytics.totalConversations,
          averageResponseTime: analytics.averageResponseTime,
          averageRating: analytics.userSatisfactionScore,
          escalationRate: analytics.escalationRate,
          resolutionRate: analytics.resolutionRate,
          languageStats: JSON.stringify(analytics.languageDistribution),
          intentStats: JSON.stringify(analytics.mostCommonIntents),
          escalationReasons: JSON.stringify(analytics.escalationReasons)
        }
      });
    } catch (error) {
      console.error('Error storing daily analytics:', error);
      throw error;
    }
  }

  /**
   * Helper methods
   */
  private buildDateFilter(startDate?: Date, endDate?: Date) {
    if (!startDate && !endDate) {
      // Default to last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return { gte: thirtyDaysAgo };
    }

    const filter: any = {};
    if (startDate) filter.gte = startDate;
    if (endDate) filter.lte = endDate;
    
    return filter;
  }

  private extractKeywords(text: string): string[] {
    // Simple keyword extraction - could be enhanced with NLP
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .slice(0, 5);
  }

  private generateRepresentativeQuestion(questions: string[]): string {
    // Return the shortest question as representative
    return questions.reduce((shortest, current) => 
      current.length < shortest.length ? current : shortest
    );
  }

  private mapIntentToCategory(intent: string): string {
    const categoryMap: Record<string, string> = {
      'PRODUCT_INQUIRY': 'product',
      'PRODUCT_SPECIFICATIONS': 'product',
      'PRODUCT_PRICING': 'pricing',
      'BID_PROCESS': 'bidding',
      'ORDER_STATUS': 'orders',
      'PAYMENT_METHODS': 'payment',
      'TECHNICAL_ISSUE': 'support'
    };
    
    return categoryMap[intent] || 'general';
  }

  /**
   * Cleanup - close database connection
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
