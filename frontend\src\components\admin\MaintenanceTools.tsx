// Gelişmiş Sistem Bakım Araçları
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  RefreshCw, 
  Database, 
  HardDrive, 
  Server, 
  Trash2,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  BarChart3
} from 'lucide-react';
import { systemService, MaintenanceTask, MaintenanceStats } from '@/services/systemService';
import { toast } from 'react-hot-toast';

interface MaintenanceToolsProps {
  className?: string;
}

export default function MaintenanceTools({ className = '' }: MaintenanceToolsProps) {
  const [stats, setStats] = useState<MaintenanceStats | null>(null);
  const [runningTasks, setRunningTasks] = useState<MaintenanceTask[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadData();
    
    // Refresh running tasks every 5 seconds
    const interval = setInterval(loadRunningTasks, 5000);
    
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const [statsData, tasksData] = await Promise.all([
        systemService.getMaintenanceStats(),
        systemService.getMaintenanceTasks()
      ]);
      
      setStats(statsData);
      setRunningTasks(tasksData);
    } catch (error) {
      console.error('Error loading maintenance data:', error);
      toast.error('Bakım verileri yüklenirken hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const loadRunningTasks = async () => {
    try {
      const tasksData = await systemService.getMaintenanceTasks();
      setRunningTasks(tasksData);
    } catch (error) {
      console.error('Error loading running tasks:', error);
    }
  };

  const handleClearCache = async () => {
    try {
      const task = await systemService.clearCache();
      setRunningTasks(prev => [...prev, task]);
      toast.success('Cache temizleme işlemi başlatıldı');
      await loadData();
    } catch (error) {
      toast.error('Cache temizlenirken hata oluştu');
    }
  };

  const handleOptimizeDatabase = async () => {
    try {
      const task = await systemService.optimizeDatabase();
      setRunningTasks(prev => [...prev, task]);
      toast.success('Veritabanı optimizasyonu başlatıldı');
      await loadData();
    } catch (error) {
      toast.error('Veritabanı optimizasyonu başarısız');
    }
  };

  const handleCleanTempFiles = async () => {
    try {
      const task = await systemService.cleanTempFiles();
      setRunningTasks(prev => [...prev, task]);
      toast.success('Geçici dosya temizliği başlatıldı');
      await loadData();
    } catch (error) {
      toast.error('Geçici dosyalar temizlenirken hata oluştu');
    }
  };

  const handleCreateBackup = async () => {
    try {
      const task = await systemService.createBackup();
      setRunningTasks(prev => [...prev, task]);
      toast.success('Veritabanı yedekleme işlemi başlatıldı');
      await loadData();
    } catch (error) {
      toast.error('Yedekleme işlemi başarısız');
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getTaskStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'running':
        return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />;
      default:
        return <Clock className="w-4 h-4 text-yellow-600" />;
    }
  };

  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'failed':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'running':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      default:
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
    }
  };

  if (isLoading && !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p>Bakım verileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Maintenance Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Cache Boyutu</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {formatBytes(stats.cacheSize)}
                  </p>
                </div>
                <RefreshCw className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Veritabanı Boyutu</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatBytes(stats.databaseSize)}
                  </p>
                </div>
                <Database className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Log Boyutu</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {formatBytes(stats.logSize)}
                  </p>
                </div>
                <BarChart3 className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Geçici Dosyalar</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {formatBytes(stats.tempFileSize)}
                  </p>
                </div>
                <Trash2 className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Maintenance Tools */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="w-5 h-5" />
              Bakım Araçları
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={handleClearCache}
              disabled={runningTasks.some(t => t.name.includes('Cache') && t.status === 'running')}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Cache Temizle
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={handleOptimizeDatabase}
              disabled={runningTasks.some(t => t.name.includes('Optimizasyon') && t.status === 'running')}
            >
              <Database className="w-4 h-4 mr-2" />
              Veritabanı Optimizasyonu
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={handleCleanTempFiles}
              disabled={runningTasks.some(t => t.name.includes('Temizlik') && t.status === 'running')}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Geçici Dosya Temizliği
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={handleCreateBackup}
              disabled={runningTasks.some(t => t.name.includes('Yedekleme') && t.status === 'running')}
            >
              <Download className="w-4 h-4 mr-2" />
              Veritabanı Yedekle
            </Button>
          </CardContent>
        </Card>

        {/* Running Tasks */}
        <Card>
          <CardHeader>
            <CardTitle>Çalışan İşlemler</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {runningTasks.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Çalışan bakım işlemi bulunmuyor</p>
                </div>
              ) : (
                runningTasks.map((task) => (
                  <div
                    key={task.id}
                    className={`p-3 rounded-lg border ${getTaskStatusColor(task.status)}`}
                  >
                    <div className="flex items-start gap-3">
                      {getTaskStatusIcon(task.status)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <p className="text-sm font-medium">{task.name}</p>
                          <Badge variant="outline" className="text-xs">
                            {task.status === 'running' ? 'Çalışıyor' :
                             task.status === 'completed' ? 'Tamamlandı' :
                             task.status === 'failed' ? 'Başarısız' : 'Bekliyor'}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-600">{task.description}</p>
                        {task.startTime && (
                          <p className="text-xs text-gray-500 mt-1">
                            Başlangıç: {new Date(task.startTime).toLocaleString('tr-TR')}
                          </p>
                        )}
                        {task.error && (
                          <Alert className="mt-2 border-red-200 bg-red-50">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription className="text-xs">
                              {task.error}
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Last Maintenance Activities */}
      {stats && (
        <Card>
          <CardHeader>
            <CardTitle>Son Bakım İşlemleri</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-600">Son Yedekleme</p>
                <p className="text-lg font-bold text-blue-600">
                  {stats.lastBackup ? 
                    new Date(stats.lastBackup).toLocaleDateString('tr-TR') : 
                    'Henüz yapılmadı'
                  }
                </p>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-600">Son Optimizasyon</p>
                <p className="text-lg font-bold text-green-600">
                  {stats.lastOptimization ? 
                    new Date(stats.lastOptimization).toLocaleDateString('tr-TR') : 
                    'Henüz yapılmadı'
                  }
                </p>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-600">Son Temizlik</p>
                <p className="text-lg font-bold text-purple-600">
                  {stats.lastCleanup ? 
                    new Date(stats.lastCleanup).toLocaleDateString('tr-TR') : 
                    'Henüz yapılmadı'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
