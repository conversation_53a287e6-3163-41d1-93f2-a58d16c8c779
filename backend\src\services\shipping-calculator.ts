// Shipping cost calculation service for sample requests

interface ShippingAddress {
  city: string;
  country: string;
  region?: string;
}

interface ShippingCalculationRequest {
  fromAddress: ShippingAddress;
  toAddress: ShippingAddress;
  productCount: number;
  carrier?: 'aras' | 'mng' | 'ups' | 'dhl';
  weight?: number; // in kg
}

interface ShippingRate {
  baseRate: number;
  currency: string;
  estimatedDays: number;
  carrier: string;
}

// Mock shipping rates data
const SHIPPING_RATES = {
  domestic: {
    sameCity: { base: 20, days: 1 },
    sameRegion: { base: 30, days: 2 },
    differentRegion: { base: 45, days: 3 }
  },
  international: {
    europe: { base: 80, days: 5 },
    asia: { base: 100, days: 7 },
    americas: { base: 120, days: 10 },
    other: { base: 150, days: 14 }
  }
};

const CARRIER_MULTIPLIERS = {
  aras: 1.0,
  mng: 1.1,
  ups: 1.5,
  dhl: 1.6
};

const PRODUCT_COUNT_MULTIPLIERS: { [key: number]: number } = {
  1: 1.0,
  2: 1.2,
  3: 1.3,
  4: 1.4,
  5: 1.5
};

// Turkish cities and their regions
const TURKISH_REGIONS = {
  'Marmara': ['İstanbul', 'Ankara', 'Bursa', 'Kocaeli', 'Sakarya', 'Tekirdağ', 'Edirne', 'Kırklareli'],
  'Ege': ['İzmir', 'Manisa', 'Aydın', 'Muğla', 'Denizli', 'Uşak', 'Afyon'],
  'Akdeniz': ['Antalya', 'Mersin', 'Adana', 'Hatay', 'Osmaniye', 'Kahramanmaraş'],
  'İç Anadolu': ['Ankara', 'Konya', 'Kayseri', 'Sivas', 'Yozgat', 'Çorum'],
  'Karadeniz': ['Samsun', 'Trabzon', 'Ordu', 'Giresun', 'Rize', 'Artvin'],
  'Doğu Anadolu': ['Erzurum', 'Malatya', 'Van', 'Elazığ', 'Ağrı', 'Kars'],
  'Güneydoğu Anadolu': ['Gaziantep', 'Şanlıurfa', 'Diyarbakır', 'Mardin', 'Batman']
};

export class ShippingCalculator {
  
  static calculateShippingCost(request: ShippingCalculationRequest): ShippingRate {
    const { fromAddress, toAddress, productCount, carrier = 'aras' } = request;
    
    let baseRate = 0;
    let estimatedDays = 0;
    
    // Check if domestic or international
    if (fromAddress.country === 'Türkiye' && toAddress.country === 'Türkiye') {
      // Domestic shipping
      const shippingType = this.getDomesticShippingType(fromAddress.city, toAddress.city);
      const rates = SHIPPING_RATES.domestic[shippingType];
      baseRate = rates.base;
      estimatedDays = rates.days;
    } else {
      // International shipping
      const region = this.getInternationalRegion(toAddress.country);
      const rates = SHIPPING_RATES.international[region];
      baseRate = rates.base;
      estimatedDays = rates.days;
    }
    
    // Apply carrier multiplier
    const carrierMultiplier = CARRIER_MULTIPLIERS[carrier] || 1.0;
    baseRate *= carrierMultiplier;
    
    // Apply product count multiplier
    const productMultiplier = PRODUCT_COUNT_MULTIPLIERS[Math.min(productCount, 5)] || 1.5;
    baseRate *= productMultiplier;
    
    // Round to nearest 5 TL
    baseRate = Math.ceil(baseRate / 5) * 5;
    
    return {
      baseRate,
      currency: 'TRY',
      estimatedDays,
      carrier
    };
  }
  
  private static getDomesticShippingType(fromCity: string, toCity: string): 'sameCity' | 'sameRegion' | 'differentRegion' {
    // Same city
    if (fromCity.toLowerCase() === toCity.toLowerCase()) {
      return 'sameCity';
    }
    
    // Find regions for both cities
    const fromRegion = this.findCityRegion(fromCity);
    const toRegion = this.findCityRegion(toCity);
    
    // Same region
    if (fromRegion && toRegion && fromRegion === toRegion) {
      return 'sameRegion';
    }
    
    // Different regions
    return 'differentRegion';
  }
  
  private static findCityRegion(city: string): string | null {
    for (const [region, cities] of Object.entries(TURKISH_REGIONS)) {
      if (cities.some(c => c.toLowerCase() === city.toLowerCase())) {
        return region;
      }
    }
    return null;
  }
  
  private static getInternationalRegion(country: string): 'europe' | 'asia' | 'americas' | 'other' {
    const europeanCountries = [
      'Germany', 'France', 'Italy', 'Spain', 'Netherlands', 'Belgium', 'Austria',
      'Switzerland', 'Greece', 'Bulgaria', 'Romania', 'Poland', 'Czech Republic'
    ];
    
    const asianCountries = [
      'China', 'Japan', 'South Korea', 'India', 'Thailand', 'Vietnam', 'Malaysia',
      'Singapore', 'Indonesia', 'Philippines', 'UAE', 'Saudi Arabia', 'Qatar'
    ];
    
    const americanCountries = [
      'United States', 'Canada', 'Mexico', 'Brazil', 'Argentina', 'Chile',
      'Colombia', 'Peru', 'Venezuela'
    ];
    
    if (europeanCountries.includes(country)) return 'europe';
    if (asianCountries.includes(country)) return 'asia';
    if (americanCountries.includes(country)) return 'americas';
    
    return 'other';
  }
  
  static getAvailableCarriers(fromCountry: string, toCountry: string): string[] {
    if (fromCountry === 'Türkiye' && toCountry === 'Türkiye') {
      return ['aras', 'mng'];
    } else {
      return ['ups', 'dhl'];
    }
  }
  
  static getCarrierName(carrier: string): string {
    const names: { [key: string]: string } = {
      aras: 'Aras Kargo',
      mng: 'MNG Kargo',
      ups: 'UPS',
      dhl: 'DHL'
    };
    return names[carrier] || carrier;
  }
}

// Sample payment interface
export interface SamplePayment {
  id: string;
  sampleRequestId: string;
  amount: number;
  currency: string;
  shippingCost: number;
  paymentMethod: 'credit_card' | 'bank_transfer' | 'paypal';
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  transactionId?: string;
  paymentDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Payment service
export class SamplePaymentService {
  
  static createPaymentRequest(sampleRequestId: string, shippingCost: number): SamplePayment {
    return {
      id: `payment-${Date.now()}`,
      sampleRequestId,
      amount: shippingCost,
      currency: 'TRY',
      shippingCost,
      paymentMethod: 'credit_card',
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
  
  static processPayment(paymentId: string, paymentMethod: string, transactionData: any): boolean {
    // Mock payment processing
    // In real implementation, integrate with payment gateway
    console.log(`Processing payment ${paymentId} with ${paymentMethod}`);
    console.log('Transaction data:', transactionData);
    
    // Simulate success/failure
    return Math.random() > 0.1; // 90% success rate
  }
  
  static getPaymentStatus(paymentId: string): 'pending' | 'completed' | 'failed' | 'cancelled' {
    // Mock status check
    return 'completed';
  }
}
