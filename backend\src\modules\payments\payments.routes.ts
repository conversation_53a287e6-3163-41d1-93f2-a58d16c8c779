import { Router } from 'express';
import { authMiddleware } from '@/middleware/authMiddleware';

const router = Router();

/**
 * @swagger
 * /api/payments:
 *   get:
 *     summary: Get user payments
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Payments retrieved successfully
 */
router.get('/', authMiddleware, (req, res) => {
  res.json({
    success: true,
    message: 'Payments endpoint - Coming soon'
  });
});

export default router;
