// RFC-301: Anonymous ID Management Service
import { createClient, RedisClientType } from 'redis';
import crypto from 'crypto';

export interface ResolvedIdentities {
  customerId: string;
  producerId: string;
}

export class AnonymousIdService {
  private client: RedisClientType;

  constructor() {
    this.client = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    this.client.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });
  }

  async connect(): Promise<void> {
    if (!this.client.isOpen) {
      await this.client.connect();
    }
  }

  async disconnect(): Promise<void> {
    if (this.client.isOpen) {
      await this.client.disconnect();
    }
  }

  // Generate and cache anonymous IDs
  async generateAnonymousBidId(bidId: string): Promise<string> {
    await this.connect();
    const anonymousId = `BID_${crypto.randomBytes(4).toString('hex').toUpperCase()}`;
    
    // Bidirectional mapping for anonymization
    await this.client.setEx(`anon_bid:${bidId}`, 24 * 60 * 60, anonymousId);
    await this.client.setEx(`bid_anon:${anonymousId}`, 24 * 60 * 60, bidId);
    
    return anonymousId;
  }

  async generateAnonymousProducerId(producerId: string, bidRequestId: string): Promise<string> {
    await this.connect();
    // Check if producer already has anonymous ID for this request
    const existingId = await this.client.get(`anon_producer:${bidRequestId}:${producerId}`);
    if (existingId) return existingId;

    const anonymousId = `PRODUCER_${crypto.randomBytes(3).toString('hex').toUpperCase()}`;
    
    // Cache for the duration of bid request + 7 days
    const ttl = 30 * 24 * 60 * 60; // 30 days
    await this.client.setEx(`anon_producer:${bidRequestId}:${producerId}`, ttl, anonymousId);
    await this.client.setEx(`producer_anon:${bidRequestId}:${anonymousId}`, ttl, producerId);
    
    return anonymousId;
  }

  async generateAnonymousCustomerId(customerId: string, bidRequestId: string): Promise<string> {
    await this.connect();
    // Check if customer already has anonymous ID for this request
    const existingId = await this.client.get(`anon_customer:${bidRequestId}:${customerId}`);
    if (existingId) return existingId;

    const anonymousId = `CUSTOMER_${crypto.randomBytes(3).toString('hex').toUpperCase()}`;
    
    // Cache for the duration of bid request + 7 days
    const ttl = 30 * 24 * 60 * 60; // 30 days
    await this.client.setEx(`anon_customer:${bidRequestId}:${customerId}`, ttl, anonymousId);
    await this.client.setEx(`customer_anon:${bidRequestId}:${anonymousId}`, ttl, customerId);
    
    return anonymousId;
  }

  // Resolve anonymous IDs (only after payment - PRD requirement)
  async resolveAnonymousIds(bidRequestId: string, selectedBidId: string): Promise<ResolvedIdentities> {
    await this.connect();
    // This should only be called after escrow payment is confirmed
    const realBidId = await this.client.get(`bid_anon:${selectedBidId}`);
    if (!realBidId) throw new Error('Invalid anonymous bid ID');

    // Get real IDs from database
    const bid = await this.getBidFromDatabase(realBidId);
    const bidRequest = await this.getBidRequestFromDatabase(bidRequestId);

    return {
      customerId: bidRequest.customerId,
      producerId: bid.producerId
    };
  }

  async resolveAnonymousProducerId(bidRequestId: string, anonymousProducerId: string): Promise<string | null> {
    await this.connect();
    return await this.client.get(`producer_anon:${bidRequestId}:${anonymousProducerId}`);
  }

  async resolveAnonymousCustomerId(bidRequestId: string, anonymousCustomerId: string): Promise<string | null> {
    await this.connect();
    return await this.client.get(`customer_anon:${bidRequestId}:${anonymousCustomerId}`);
  }

  async resolveAnonymousBidId(anonymousBidId: string): Promise<string | null> {
    await this.connect();
    return await this.client.get(`bid_anon:${anonymousBidId}`);
  }

  // Cleanup expired anonymous IDs
  async cleanupExpiredIds(): Promise<void> {
    await this.connect();
    // Redis TTL will handle automatic cleanup
    // This method can be used for manual cleanup if needed
    console.log('Cleanup completed - Redis TTL handles automatic expiration');
  }

  // Validate anonymous ID format
  validateAnonymousId(anonymousId: string, type: 'bid' | 'producer' | 'customer'): boolean {
    const patterns = {
      bid: /^BID_[A-F0-9]{8}$/,
      producer: /^PRODUCER_[A-F0-9]{6}$/,
      customer: /^CUSTOMER_[A-F0-9]{6}$/
    };
    
    return patterns[type].test(anonymousId);
  }

  // Check if anonymous ID exists
  async anonymousIdExists(anonymousId: string, type: 'bid' | 'producer' | 'customer'): Promise<boolean> {
    await this.connect();
    const key = type === 'bid' ? `bid_anon:${anonymousId}` : 
                type === 'producer' ? `producer_anon:*:${anonymousId}` :
                `customer_anon:*:${anonymousId}`;
    
    if (type === 'bid') {
      const result = await this.client.get(key);
      return result !== null;
    } else {
      // For producer/customer, we need to scan for pattern
      const keys = await this.client.keys(key);
      return keys.length > 0;
    }
  }

  private async getBidFromDatabase(bidId: string): Promise<any> {
    // This would integrate with your Prisma client
    // For now, return mock data
    return {
      id: bidId,
      producerId: 'producer-123',
      bidRequestId: 'request-123'
    };
  }

  private async getBidRequestFromDatabase(bidRequestId: string): Promise<any> {
    // This would integrate with your Prisma client
    // For now, return mock data
    return {
      id: bidRequestId,
      customerId: 'customer-123'
    };
  }
}
