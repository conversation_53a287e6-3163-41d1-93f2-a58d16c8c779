'use client';

import { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useSimpleTranslation } from '@/hooks/useSimpleTranslation';

export default function CustomerPage() {
  const router = useRouter();
  const params = useParams();
  const { t } = useSimpleTranslation();

  // Get current locale from params
  const locale = params.locale as string;

  useEffect(() => {
    // Customer ana sayfasına geldiğinde dashboard'a yönlendir
    // Dil desteği ile birlikte yönlendir
    if (locale && locale !== 'tr') {
      router.replace(`/${locale}/customer/dashboard`);
    } else {
      router.replace('/customer/dashboard');
    }
  }, [router, locale]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">{t('customer.redirecting_to_dashboard')}</p>
      </div>
    </div>
  );
}
