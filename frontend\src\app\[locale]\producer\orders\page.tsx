'use client'

import * as React from 'react'
import Link from 'next/link'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  ShoppingCart, 
  Clock, 
  CheckCircle, 
  XCircle,
  Truck,
  Factory,
  Package,
  TrendingUp,
  DollarSign
} from 'lucide-react'

// Mock data for orders
const mockOrders = [
  {
    id: 'ORD-001',
    customerName: 'ABC İnşaat Ltd.',
    customerEmail: '<EMAIL>',
    productName: 'Beyaz Mermer',
    quantity: 300,
    unit: 'm²',
    orderDate: '2025-06-25',
    deliveryDate: '2025-07-10',
    status: 'production',
    totalValue: 15000,
    currency: 'USD',
    paymentStatus: 'partial',
    paidAmount: 4500
  },
  {
    id: 'ORD-002',
    customerName: 'XYZ Mimarlık',
    customerEmail: '<EMAIL>',
    productName: 'Traverten Plaka',
    quantity: 200,
    unit: 'm²',
    orderDate: '2025-06-20',
    deliveryDate: '2025-07-05',
    status: 'completed',
    totalValue: 8000,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 8000
  },
  {
    id: 'ORD-003',
    customerName: 'DEF Yapı',
    customerEmail: '<EMAIL>',
    productName: 'Granit Blok',
    quantity: 50,
    unit: 'ton',
    orderDate: '2025-06-15',
    deliveryDate: '2025-07-15',
    status: 'pending',
    totalValue: 40000,
    currency: 'USD',
    paymentStatus: 'pending',
    paidAmount: 0
  },
  {
    id: 'ORD-004',
    customerName: 'Lüks Villa Projesi',
    customerEmail: '<EMAIL>',
    productName: 'Traverten Klasik',
    quantity: 150,
    unit: 'm²',
    orderDate: '2024-11-15',
    deliveryDate: '2024-12-20',
    status: 'completed',
    totalValue: 7500,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 7500
  },
  {
    id: 'ORD-005',
    customerName: 'Modern Mutfak Ltd.',
    customerEmail: '<EMAIL>',
    productName: 'Mermer Beyaz Carrara',
    quantity: 80,
    unit: 'm²',
    orderDate: '2024-11-20',
    deliveryDate: '2024-12-18',
    status: 'completed',
    totalValue: 9600,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 9600
  },
  {
    id: 'ORD-006',
    customerName: 'Otel Projesi A.Ş.',
    customerEmail: '<EMAIL>',
    productName: 'Granit Siyah',
    quantity: 200,
    unit: 'm²',
    orderDate: '2024-10-10',
    deliveryDate: '2024-12-15',
    status: 'completed',
    totalValue: 16000,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 16000
  },
  {
    id: 'ORD-007',
    customerName: 'Banyo Renovasyon',
    customerEmail: '<EMAIL>',
    productName: 'Oniks Honey',
    quantity: 45,
    unit: 'm²',
    orderDate: '2024-12-01',
    deliveryDate: '2025-01-10',
    status: 'shipped',
    totalValue: 6750,
    currency: 'USD',
    paymentStatus: 'paid',
    paidAmount: 6750
  }
]

export default function ProducerOrders() {
  const { producer } = useProducerAuth()

  const menuItems = [
    { 
      id: 'pending', 
      label: 'Bekleyen Siparişler', 
      href: '/producer/orders/pending',
      count: mockOrders.filter(o => o.status === 'pending').length,
      icon: Clock,
      color: 'text-yellow-600 bg-yellow-100'
    },
    { 
      id: 'production', 
      label: 'Üretimde', 
      href: '/producer/orders/production',
      count: mockOrders.filter(o => o.status === 'production').length,
      icon: Factory,
      color: 'text-blue-600 bg-blue-100'
    },
    { 
      id: 'shipped', 
      label: 'Sevk Edildi', 
      href: '/producer/orders/shipped',
      count: mockOrders.filter(o => o.status === 'shipped').length,
      icon: Truck,
      color: 'text-purple-600 bg-purple-100'
    },
    { 
      id: 'completed', 
      label: 'Tamamlandı', 
      href: '/producer/orders/completed',
      count: mockOrders.filter(o => o.status === 'completed').length,
      icon: CheckCircle,
      color: 'text-green-600 bg-green-100'
    }
  ]

  // Summary stats
  const stats = {
    total: mockOrders.length,
    pending: mockOrders.filter(o => o.status === 'pending').length,
    production: mockOrders.filter(o => o.status === 'production').length,
    shipped: mockOrders.filter(o => o.status === 'shipped').length,
    completed: mockOrders.filter(o => o.status === 'completed').length,
    totalValue: mockOrders.reduce((sum, order) => sum + order.totalValue, 0)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100'
      case 'production':
        return 'text-blue-600 bg-blue-100'
      case 'shipped':
        return 'text-purple-600 bg-purple-100'
      case 'completed':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'production':
        return 'Üretimde'
      case 'shipped':
        return 'Sevk Edildi'
      case 'completed':
        return 'Tamamlandı'
      default:
        return status
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'production':
        return <Factory className="w-4 h-4" />
      case 'shipped':
        return <Truck className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      default:
        return <Package className="w-4 h-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Siparişlerim</h1>
          <p className="text-gray-600">
            Siparişlerinizi yönetin ve performansınızı takip edin
          </p>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Sipariş</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Tüm zamanlar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktif Siparişler</CardTitle>
            <Factory className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.production}</div>
            <p className="text-xs text-muted-foreground">
              Üretimde
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">
              Ödeme bekliyor
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Değer</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">${stats.totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Tüm siparişler
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Menu Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {menuItems.map((item) => {
          const Icon = item.icon
          return (
            <Link key={item.id} href={item.href}>
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <div className={`p-2 rounded-lg ${item.color}`}>
                          <Icon className="w-5 h-5" />
                        </div>
                      </div>
                      <h3 className="font-semibold text-gray-900">{item.label}</h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {item.count} sipariş
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">{item.count}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          )
        })}
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Son Aktiviteler</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockOrders.slice(0, 3).map((order) => (
              <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${getStatusColor(order.status)}`}>
                    {getStatusIcon(order.status)}
                  </div>
                  <div>
                    <p className="font-medium text-sm">#{order.id} - {order.customerName}</p>
                    <p className="text-sm text-gray-600">{order.productName} - {order.quantity} {order.unit}</p>
                    <p className="text-xs text-gray-500">{order.orderDate} - ${order.totalValue.toLocaleString()}</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                    {getStatusText(order.status)}
                  </span>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <Link href="/producer/orders/pending">
              <Button variant="outline" size="sm">
                Tüm Siparişleri Görüntüle
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
