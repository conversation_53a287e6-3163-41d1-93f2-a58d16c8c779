/**
 * Chatbot Hook
 * Custom React hook for chatbot functionality
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  ChatMessage, 
  ChatbotResponse, 
  ChatbotError, 
  ChatbotErrorCode,
  UseChatbotReturn 
} from '../types/chatbot';
import { chatbotApi } from '../services/chatbotApi';

interface UseChatbotOptions {
  userId?: string;
  language?: string;
  autoStart?: boolean;
  onMessage?: (message: ChatMessage) => void;
  onError?: (error: ChatbotError) => void;
  onEscalation?: (agentId: string, estimatedWaitTime?: number) => void;
}

export const useChatbot = (options: UseChatbotOptions = {}): UseChatbotReturn => {
  const {
    userId,
    language = 'en',
    autoStart = false,
    onMessage,
    onError,
    onEscalation
  } = options;

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [error, setError] = useState<ChatbotError | null>(null);

  const isInitialized = useRef(false);
  const retryCount = useRef(0);
  const maxRetries = 3;

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
    retryCount.current = 0;
  }, []);

  /**
   * Handle errors with retry logic
   */
  const handleError = useCallback((err: unknown) => {
    const chatbotError = err instanceof ChatbotError 
      ? err 
      : new ChatbotError('An unexpected error occurred', ChatbotErrorCode.API_ERROR, err);

    setError(chatbotError);
    setIsLoading(false);
    setIsTyping(false);

    if (onError) {
      onError(chatbotError);
    }

    console.error('Chatbot error:', chatbotError);
  }, [onError]);

  /**
   * Start new conversation
   */
  const startConversation = useCallback(async () => {
    if (sessionId && retryCount.current === 0) {
      return; // Already have an active session
    }

    try {
      setIsLoading(true);
      clearError();

      const response = await chatbotApi.startConversation(userId, language);
      setSessionId(response.sessionId);

      const greetingMessage: ChatMessage = {
        id: 'greeting-' + Date.now(),
        role: 'assistant',
        content: response.greeting,
        timestamp: new Date(),
        language
      };

      setMessages([greetingMessage]);
      setSuggestions(chatbotApi.getDefaultSuggestions(language));

      if (onMessage) {
        onMessage(greetingMessage);
      }

      retryCount.current = 0;
    } catch (err) {
      if (retryCount.current < maxRetries) {
        retryCount.current++;
        setTimeout(() => startConversation(), 1000 * retryCount.current);
      } else {
        handleError(err);
      }
    } finally {
      setIsLoading(false);
    }
  }, [userId, language, sessionId, onMessage, clearError, handleError]);

  /**
   * Send message to chatbot
   */
  const sendMessage = useCallback(async (message: string) => {
    if (!message.trim() || !sessionId || isLoading) {
      return;
    }

    const userMessage: ChatMessage = {
      id: 'user-' + Date.now(),
      role: 'user',
      content: message.trim(),
      timestamp: new Date(),
      language
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setIsTyping(true);
    clearError();

    if (onMessage) {
      onMessage(userMessage);
    }

    try {
      const response = await chatbotApi.sendMessage(sessionId, message, userId);

      const assistantMessage: ChatMessage = {
        id: 'assistant-' + Date.now(),
        role: 'assistant',
        content: response.response,
        timestamp: new Date(),
        language,
        intent: response.intent,
        confidence: response.confidence
      };

      setMessages(prev => [...prev, assistantMessage]);
      setSuggestions(response.suggestions || []);

      if (onMessage) {
        onMessage(assistantMessage);
      }

      // Handle escalation
      if (response.escalated && response.agentId) {
        const escalationMessage: ChatMessage = {
          id: 'escalation-' + Date.now(),
          role: 'system',
          content: `You have been connected to a human agent. ${
            response.estimatedWaitTime 
              ? `Estimated wait time: ${response.estimatedWaitTime} minutes.` 
              : ''
          }`,
          timestamp: new Date(),
          language
        };

        setMessages(prev => [...prev, escalationMessage]);

        if (onEscalation) {
          onEscalation(response.agentId, response.estimatedWaitTime);
        }
      }

      retryCount.current = 0;
    } catch (err) {
      // Remove user message on error
      setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
      
      if (retryCount.current < maxRetries) {
        retryCount.current++;
        setTimeout(() => sendMessage(message), 1000 * retryCount.current);
      } else {
        handleError(err);
        
        // Add error message to chat
        const errorMessage: ChatMessage = {
          id: 'error-' + Date.now(),
          role: 'assistant',
          content: 'I apologize, but I encountered an error. Please try again.',
          timestamp: new Date(),
          language
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  }, [sessionId, userId, language, isLoading, onMessage, onEscalation, clearError, handleError]);

  /**
   * Submit feedback for a message
   */
  const submitFeedback = useCallback(async (
    messageId: string, 
    rating: number, 
    feedback?: string
  ) => {
    if (!sessionId) {
      return;
    }

    try {
      await chatbotApi.submitFeedback(sessionId, messageId, rating, feedback);
    } catch (err) {
      console.warn('Failed to submit feedback:', err);
      // Don't show error to user for feedback submission failures
    }
  }, [sessionId]);

  /**
   * End conversation
   */
  const endConversation = useCallback(async () => {
    if (!sessionId) {
      return;
    }

    try {
      await chatbotApi.deleteConversation(sessionId);
    } catch (err) {
      console.warn('Failed to delete conversation:', err);
    } finally {
      setSessionId(null);
      setMessages([]);
      setSuggestions([]);
      clearError();
    }
  }, [sessionId, clearError]);

  /**
   * Auto-start conversation if enabled
   */
  useEffect(() => {
    if (autoStart && !isInitialized.current && !sessionId) {
      isInitialized.current = true;
      startConversation();
    }
  }, [autoStart, sessionId, startConversation]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (sessionId) {
        // Don't await this in cleanup
        chatbotApi.deleteConversation(sessionId).catch(console.warn);
      }
    };
  }, [sessionId]);

  /**
   * Check service availability on mount
   */
  useEffect(() => {
    const checkServiceAvailability = async () => {
      try {
        const isAvailable = await chatbotApi.isServiceAvailable();
        if (!isAvailable) {
          setError(new ChatbotError(
            'Chatbot service is currently unavailable',
            ChatbotErrorCode.SERVICE_UNAVAILABLE
          ));
        }
      } catch (err) {
        // Service check failed, but don't show error unless user tries to interact
        console.warn('Service availability check failed:', err);
      }
    };

    checkServiceAvailability();
  }, []);

  return {
    messages,
    isLoading,
    isTyping,
    sessionId,
    suggestions,
    sendMessage,
    startConversation,
    endConversation,
    submitFeedback,
    error,
    clearError
  };
};

/**
 * Hook for chatbot analytics (Admin use)
 */
export const useChatbotAnalytics = (token?: string) => {
  const [analytics, setAnalytics] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<ChatbotError | null>(null);

  const refresh = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await chatbotApi.getAnalytics(token);
      setAnalytics(data);
    } catch (err) {
      const chatbotError = err instanceof ChatbotError 
        ? err 
        : new ChatbotError('Failed to fetch analytics', ChatbotErrorCode.API_ERROR, err);
      setError(chatbotError);
    } finally {
      setIsLoading(false);
    }
  }, [token]);

  useEffect(() => {
    if (token) {
      refresh();
    }
  }, [token, refresh]);

  return {
    analytics,
    isLoading,
    error,
    refresh
  };
};

/**
 * Hook for chatbot admin functionality
 */
export const useChatbotAdmin = (token?: string) => {
  const [dashboardData, setDashboardData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<ChatbotError | null>(null);

  const updateEscalationCriteria = useCallback(async (criteria: any) => {
    try {
      await chatbotApi.updateEscalationCriteria(criteria, token);
      // Refresh dashboard data after update
      await refresh();
    } catch (err) {
      const chatbotError = err instanceof ChatbotError 
        ? err 
        : new ChatbotError('Failed to update escalation criteria', ChatbotErrorCode.API_ERROR, err);
      setError(chatbotError);
      throw chatbotError;
    }
  }, [token]);

  const updateAgentAvailability = useCallback(async (agentId: string, available: boolean) => {
    try {
      await chatbotApi.updateAgentAvailability(agentId, available, token);
      // Refresh dashboard data after update
      await refresh();
    } catch (err) {
      const chatbotError = err instanceof ChatbotError 
        ? err 
        : new ChatbotError('Failed to update agent availability', ChatbotErrorCode.API_ERROR, err);
      setError(chatbotError);
      throw chatbotError;
    }
  }, [token]);

  const refresh = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const [analytics, conversations, agents, criteria] = await Promise.all([
        chatbotApi.getAnalytics(token),
        chatbotApi.getActiveConversations(token),
        chatbotApi.getAvailableAgents(token),
        chatbotApi.getEscalationCriteria(token)
      ]);

      setDashboardData({
        analytics,
        activeSessions: conversations.conversations,
        agents: agents.agents,
        escalationCriteria: criteria,
        recentFeedback: [] // Would be fetched from a separate endpoint
      });
    } catch (err) {
      const chatbotError = err instanceof ChatbotError 
        ? err 
        : new ChatbotError('Failed to fetch dashboard data', ChatbotErrorCode.API_ERROR, err);
      setError(chatbotError);
    } finally {
      setIsLoading(false);
    }
  }, [token]);

  useEffect(() => {
    if (token) {
      refresh();
    }
  }, [token, refresh]);

  return {
    dashboardData,
    isLoading,
    error,
    updateEscalationCriteria,
    updateAgentAvailability,
    refresh
  };
};
