'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function AddExpensePage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    title: '',
    category: '',
    customCategory: '',
    amount: '',
    currency: 'USD',
    date: new Date().toISOString().split('T')[0],
    description: '',
    invoiceNumber: '',
    supplier: '',
    paymentMethod: '',
    checkCount: '',
    checkDetails: [],
    isRecurring: false,
    recurringPeriod: '',
    nextDueDate: '',
    tags: ''
  });

  const expenseCategories = [
    'Gümrük Vergisi', 'Nakliye Ücreti', 'Sigorta', 'Depolama', 'Komisyon',
    'Banka Ücreti', 'Kargo', 'Yakıt', 'Elektrik', 'Su', 'İnternet',
    'Telefon', 'Kira', 'Personel Ma<PERSON>şı', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Diğer'
  ];

  const paymentMethods = [
    '<PERSON>ki<PERSON>', 'Banka Havalesi', '<PERSON>redi Ka<PERSON>ı', 'Çek'
  ];

  const recurringPeriods = [
    'Haftalık', 'Aylık', 'Üç Aylık', 'Altı Aylık', 'Yıllık'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => {
        const newData = {
          ...prev,
          [name]: value
        };

        // Çek sayısı değiştiğinde çek detaylarını güncelle
        if (name === 'checkCount' && value) {
          const count = parseInt(value);
          const newCheckDetails = [];
          for (let i = 0; i < count; i++) {
            newCheckDetails.push({
              checkNumber: '',
              amount: '',
              dueDate: '',
              reminder: false,
              reminderDate: ''
            });
          }
          newData.checkDetails = newCheckDetails;
        }

        // Ödeme yöntemi değiştiğinde çek detaylarını temizle
        if (name === 'paymentMethod' && value !== 'Çek') {
          newData.checkCount = '';
          newData.checkDetails = [];
        }

        return newData;
      });
    }
  };

  const handleCheckDetailChange = (index: number, field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      checkDetails: prev.checkDetails.map((check, i) =>
        i === index ? { ...check, [field]: value } : check
      )
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Çek validasyonu
    if (formData.paymentMethod === 'Çek') {
      const invalidChecks = formData.checkDetails.filter(check =>
        !check.amount || !check.dueDate || (check.reminder && check.reminderDate && new Date(check.reminderDate) > new Date(check.dueDate))
      );

      if (invalidChecks.length > 0) {
        alert('Lütfen tüm çek bilgilerini eksiksiz doldurun. Hatırlatma tarihi vade tarihinden sonra olamaz.');
        return;
      }
    }

    try {
      // API call will be here
      console.log('Gider kaydı:', formData);

      // Success message and redirect
      alert('Gider kaydı başarıyla eklendi!');
      router.push('/customer/analytics/expenses');
    } catch (error) {
      console.error('Gider kaydı eklenirken hata:', error);
      alert('Gider kaydı eklenirken bir hata oluştu.');
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Yeni Gider Kaydı</h1>
            <p className="text-gray-600 mt-1">Gider bilgilerini ekleyin</p>
          </div>
          <Link
            href="/customer/analytics/expenses"
            className="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            ← Geri Dön
          </Link>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        
        {/* Temel Bilgiler */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Temel Bilgiler</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Gider Başlığı *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="Örn: Ocak ayı nakliye ücreti"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kategori *
              </label>
              <select
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">Kategori Seçin</option>
                {expenseCategories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              
              {formData.category === 'Diğer' && (
                <div className="mt-2">
                  <input
                    type="text"
                    name="customCategory"
                    value={formData.customCategory}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="Kategori adını yazın"
                  />
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tarih *
              </label>
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              />
            </div>

          </div>
        </div>

        {/* Tutar Bilgileri */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Tutar Bilgileri</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tutar *
              </label>
              <input
                type="number"
                name="amount"
                value={formData.amount}
                onChange={handleInputChange}
                required
                step="0.01"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="100.00"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Para Birimi
              </label>
              <select
                name="currency"
                value={formData.currency}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="TRY">TRY</option>
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ödeme Yöntemi
              </label>
              <select
                name="paymentMethod"
                value={formData.paymentMethod}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">Seçin</option>
                {paymentMethods.map(method => (
                  <option key={method} value={method}>{method}</option>
                ))}
              </select>

              {formData.paymentMethod === 'Çek' && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Çek Sayısı *
                    </label>
                    <input
                      type="number"
                      name="checkCount"
                      value={formData.checkCount}
                      onChange={handleInputChange}
                      required
                      min="1"
                      max="10"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      placeholder="Kaç adet çek?"
                    />
                  </div>

                  {formData.checkDetails.length > 0 && (
                    <div className="space-y-4">
                      <h4 className="text-sm font-medium text-gray-700">Çek Detayları</h4>
                      {formData.checkDetails.map((check, index) => (
                        <div key={index} className="p-4 bg-white rounded-lg border border-gray-200">
                          <h5 className="text-sm font-medium text-gray-600 mb-3">
                            {index + 1}. Çek
                          </h5>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                            <div>
                              <label className="block text-xs font-medium text-gray-600 mb-1">
                                Çek Numarası
                              </label>
                              <input
                                type="text"
                                value={check.checkNumber}
                                onChange={(e) => handleCheckDetailChange(index, 'checkNumber', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-red-500 focus:border-red-500"
                                placeholder="ÇEK-001"
                              />
                            </div>

                            <div>
                              <label className="block text-xs font-medium text-gray-600 mb-1">
                                Tutar ({formData.currency}) *
                              </label>
                              <input
                                type="number"
                                value={check.amount}
                                onChange={(e) => handleCheckDetailChange(index, 'amount', e.target.value)}
                                required
                                step="0.01"
                                min="0"
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-red-500 focus:border-red-500"
                                placeholder="100.00"
                              />
                            </div>

                            <div>
                              <label className="block text-xs font-medium text-gray-600 mb-1">
                                Vade Tarihi *
                              </label>
                              <input
                                type="date"
                                value={check.dueDate}
                                onChange={(e) => handleCheckDetailChange(index, 'dueDate', e.target.value)}
                                required
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-red-500 focus:border-red-500"
                              />
                            </div>
                          </div>

                          <div className="mt-3">
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                checked={check.reminder}
                                onChange={(e) => handleCheckDetailChange(index, 'reminder', e.target.checked)}
                                className="h-3 w-3 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                              />
                              <label className="ml-2 text-xs text-gray-600">
                                Vade tarihinde hatırlatma gönder
                              </label>
                            </div>

                            {check.reminder && (
                              <div className="mt-2">
                                <label className="block text-xs font-medium text-gray-600 mb-1">
                                  Hatırlatma Tarihi
                                </label>
                                <input
                                  type="date"
                                  value={check.reminderDate}
                                  onChange={(e) => handleCheckDetailChange(index, 'reminderDate', e.target.value)}
                                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-red-500 focus:border-red-500"
                                />
                                <p className="text-xs text-gray-500 mt-1">
                                  Boş bırakılırsa vade tarihinde hatırlatma gönderilir
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

          </div>
        </div>

        {/* Fatura ve Tedarikçi Bilgileri */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Fatura ve Tedarikçi Bilgileri</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fatura Numarası
              </label>
              <input
                type="text"
                name="invoiceNumber"
                value={formData.invoiceNumber}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="FAT-2024-001"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tedarikçi/Firma
              </label>
              <input
                type="text"
                name="supplier"
                value={formData.supplier}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="Tedarikçi adı"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Etiketler
              </label>
              <input
                type="text"
                name="tags"
                value={formData.tags}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="Virgülle ayırın: acil, aylık, vergi"
              />
            </div>

          </div>
        </div>

        {/* Tekrarlanan Gider */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Tekrarlanan Gider</h2>
          
          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              name="isRecurring"
              checked={formData.isRecurring}
              onChange={handleInputChange}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
            />
            <label className="ml-2 text-sm text-gray-700">
              Bu gider düzenli olarak tekrarlanır
            </label>
          </div>

          {formData.isRecurring && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-red-50 rounded-lg">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tekrar Periyodu *
                </label>
                <select
                  name="recurringPeriod"
                  value={formData.recurringPeriod}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                >
                  <option value="">Periyot Seçin</option>
                  {recurringPeriods.map(period => (
                    <option key={period} value={period}>{period}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sonraki Ödeme Tarihi *
                </label>
                <input
                  type="date"
                  name="nextDueDate"
                  value={formData.nextDueDate}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                />
              </div>
            </div>
          )}
        </div>

        {/* Açıklama */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Açıklama</h2>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            placeholder="Gider ile ilgili ek bilgiler, notlar..."
          />
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Link
            href="/customer/analytics/expenses"
            className="px-6 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            İptal
          </Link>
          <button
            type="submit"
            className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
          >
            Gider Kaydını Ekle
          </button>
        </div>

      </form>
    </div>
  );
}
