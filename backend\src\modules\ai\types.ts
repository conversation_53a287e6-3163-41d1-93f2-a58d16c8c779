/**
 * AI Chatbot Types
 * Type definitions for the AI chatbot system
 */

export enum ChatbotIntent {
  // Product Related
  PRODUCT_INQUIRY = 'product_inquiry',
  PRODUCT_SPECIFICATIONS = 'product_specifications',
  PRODUCT_PRICING = 'product_pricing',
  PRODUCT_AVAILABILITY = 'product_availability',
  
  // Order Related
  ORDER_STATUS = 'order_status',
  ORDER_TRACKING = 'order_tracking',
  ORDER_MODIFICATION = 'order_modification',
  ORDER_CANCELLATION = 'order_cancellation',
  
  // Bidding Related
  BID_PROCESS = 'bid_process',
  BID_STATUS = 'bid_status',
  BID_REQUIREMENTS = 'bid_requirements',
  
  // Account Related
  ACCOUNT_SETUP = 'account_setup',
  PROFILE_UPDATE = 'profile_update',
  VERIFICATION_STATUS = 'verification_status',
  
  // Payment Related
  PAYMENT_METHODS = 'payment_methods',
  PAYMENT_STATUS = 'payment_status',
  REFUND_REQUEST = 'refund_request',
  
  // Technical Support
  TECHNICAL_ISSUE = 'technical_issue',
  PLATFORM_USAGE = 'platform_usage',
  
  // General
  GREETING = 'greeting',
  GOODBYE = 'goodbye',
  HUMAN_HANDOFF = 'human_handoff',
  COMPLAINT = 'complaint',
  UNKNOWN = 'unknown'
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  language?: string;
  intent?: ChatbotIntent;
  confidence?: number;
  entities?: ExtractedEntity[];
}

export interface ExtractedEntity {
  type: string;
  value: string;
  confidence: number;
  start: number;
  end: number;
}

export interface ConversationContext {
  sessionId: string;
  userId?: string;
  language: string;
  currentIntent: ChatbotIntent;
  entities: ExtractedEntity[];
  conversationHistory: ChatMessage[];
  userProfile?: UserProfile;
  escalationLevel: number;
  escalationReason?: string;
  humanAgentId?: string;
  satisfactionScore?: number;
  resolvedIssues: string[];
  unresolvedIssues: string[];
  confidence: number;
  sentiment: number;
}

export interface UserProfile {
  type: 'producer' | 'customer';
  preferences: UserPreferences;
  previousInteractions: InteractionHistory[];
}

export interface UserPreferences {
  language: string;
  communicationStyle: 'formal' | 'casual';
  topics: string[];
}

export interface InteractionHistory {
  date: Date;
  topic: string;
  satisfaction: number;
  resolved: boolean;
}

export interface ChatbotResponse {
  success: boolean;
  sessionId: string;
  response: string;
  confidence: number;
  intent: ChatbotIntent;
  entities: ExtractedEntity[];
  suggestions: string[];
  escalated?: boolean;
  agentId?: string;
  estimatedWaitTime?: number;
}

export interface EscalationCriteria {
  lowConfidenceThreshold: number;
  mediumConfidenceThreshold: number;
  alwaysEscalate: ChatbotIntent[];
  neverEscalate: ChatbotIntent[];
  negativeSentimentThreshold: number;
  multipleUnresolvedIssues: number;
  conversationLength: number;
  repeatedQuestions: number;
  vipCustomers: string[];
  highValueOrders: number;
}

export interface EscalationResult {
  escalated: boolean;
  agentId?: string;
  estimatedWaitTime?: number;
  reason?: string;
}

export interface KnowledgeBase {
  productKnowledge: ProductKnowledgeBase;
  processKnowledge: ProcessKnowledgeBase;
  faqDatabase: FAQ[];
}

export interface ProductKnowledgeBase {
  stoneTypes: Record<string, StoneTypeInfo>;
  technicalSpecs: TechnicalSpecifications;
  pricingGuidelines: PricingGuidelines;
}

export interface StoneTypeInfo {
  name: string;
  properties: {
    density: number;
    hardness: number;
    waterAbsorption: number;
    freezeThawResistance: string;
  };
  applications: string[];
  maintenanceRequirements: string[];
  commonQuestions: FAQ[];
}

export interface TechnicalSpecifications {
  dimensions: DimensionGuide;
  finishTypes: FinishTypeGuide;
  qualityStandards: QualityStandardGuide;
  installationGuides: InstallationGuide[];
}

export interface DimensionGuide {
  standardSizes: string[];
  customSizes: boolean;
  tolerances: string;
}

export interface FinishTypeGuide {
  types: string[];
  descriptions: Record<string, string>;
  applications: Record<string, string[]>;
}

export interface QualityStandardGuide {
  standards: string[];
  certifications: string[];
  testMethods: string[];
}

export interface InstallationGuide {
  title: string;
  steps: string[];
  tools: string[];
  tips: string[];
}

export interface PricingGuidelines {
  factors: PricingFactor[];
  marketRanges: PriceRange[];
  calculationMethods: CalculationMethod[];
}

export interface PricingFactor {
  name: string;
  impact: 'low' | 'medium' | 'high';
  description: string;
}

export interface PriceRange {
  stoneType: string;
  minPrice: number;
  maxPrice: number;
  currency: string;
  unit: string;
}

export interface CalculationMethod {
  name: string;
  formula: string;
  variables: string[];
}

export interface ProcessKnowledgeBase {
  registration: ProcessInfo;
  bidding: ProcessInfo;
  orderProcess: ProcessInfo;
  disputeResolution: ProcessInfo;
}

export interface ProcessInfo {
  steps: ProcessStep[];
  requirements: Requirement[];
  commonIssues: Issue[];
  timeline?: Timeline;
}

export interface ProcessStep {
  order: number;
  title: string;
  description: string;
  duration?: string;
  requirements?: string[];
}

export interface Requirement {
  name: string;
  type: 'document' | 'information' | 'action';
  mandatory: boolean;
  description: string;
}

export interface Issue {
  problem: string;
  solution: string;
  frequency: 'common' | 'rare';
}

export interface Timeline {
  totalDuration: string;
  milestones: Milestone[];
}

export interface Milestone {
  name: string;
  duration: string;
  dependencies: string[];
}

export interface FAQ {
  question: string;
  answer: string;
  category: string;
  language: string;
  keywords: string[];
}

export interface ChatbotAnalytics {
  responseAccuracy: number;
  resolutionRate: number;
  averageResponseTime: number;
  userSatisfactionScore: number;
  totalConversations: number;
  averageConversationLength: number;
  mostCommonIntents: IntentFrequency[];
  languageDistribution: LanguageStats[];
  escalationRate: number;
  escalationReasons: EscalationReason[];
  humanResolutionRate: number;
  newKnowledgeGaps: KnowledgeGap[];
  improvementOpportunities: Improvement[];
}

export interface IntentFrequency {
  intent: ChatbotIntent;
  count: number;
  percentage: number;
}

export interface LanguageStats {
  language: string;
  count: number;
  percentage: number;
}

export interface EscalationReason {
  reason: string;
  count: number;
  percentage: number;
}

export interface KnowledgeGap {
  topic: string;
  frequency: number;
  examples: string[];
}

export interface Improvement {
  area: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  estimatedImpact: string;
}

export interface UserFeedback {
  sessionId: string;
  messageId: string;
  rating: number;
  feedback?: string;
  timestamp: Date;
}

export interface LearningInsights {
  knowledgeGaps: KnowledgeGap[];
  failedResponses: FailedResponse[];
  faqCandidates: FAQCandidate[];
  improvementSuggestions: Improvement[];
}

export interface FailedResponse {
  message: string;
  intent: ChatbotIntent;
  confidence: number;
  reason: string;
}

export interface FAQCandidate {
  question: string;
  frequency: number;
  suggestedAnswer: string;
  category: string;
}

export interface SupportedLanguages {
  primary: Record<string, string>;
  secondary: Record<string, string>;
}

export interface Agent {
  id: string;
  name: string;
  languages: string[];
  expertise: string[];
  currentLoad: number;
  available: boolean;
}
