'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  ChartBarIcon,
  ClipboardDocumentListIcon,
  ShoppingCartIcon,
  CubeIcon,
  BuildingOfficeIcon,
  HomeIcon,
  BellIcon
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import KPICard from './components/KPICard';
import ChartWidget from './components/ChartWidget';
import QuickActions from './components/QuickActions';
import ProducerQuickActions from './components/ProducerQuickActions';
import RecentActivity from './components/RecentActivity';
import Sidebar from './components/Sidebar';
import ProducerQuoteRequestsPage from './pages/ProducerQuoteRequestsPage';

interface DashboardData {
  kpis: {
    totalSales: number;
    activeQuotes: number;
    monthlyRevenue: number;
    productsSold: number;
  };
  charts: {
    salesTrend: any;
    quoteConversion: any;
    productPerformance: any;
  };
  recentActivity: any[];
}

const ProducerDashboard: React.FC = () => {
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  const sidebarItems = [
    {
      id: 'overview',
      label: 'Genel Bakış',
      icon: HomeIcon,
      route: '/producer/dashboard'
    },
    {
      id: 'quote-requests',
      label: 'Teklif Talepleri',
      icon: ClipboardDocumentListIcon,
      route: '/producer/quote-requests',
      badge: 5 // Yeni teklif talepleri sayısı
    },
    {
      id: 'orders',
      label: 'Siparişlerim',
      icon: ShoppingCartIcon,
      route: '/producer/orders',
      submenu: [
        { label: 'Aktif Siparişler', route: '/producer/orders/active' },
        { label: 'Tamamlanan', route: '/producer/orders/completed' },
        { label: 'İptal Edilen', route: '/producer/orders/cancelled' }
      ]
    },
    {
      id: 'products',
      label: 'Ürünlerim',
      icon: CubeIcon,
      route: '/producer/products',
      submenu: [
        { label: 'Ürün Listesi', route: '/producer/products/list' },
        { label: 'Yeni Ürün Ekle', route: '/producer/products/add' },
        { label: 'Stok Yönetimi', route: '/producer/products/stock' }
      ]
    },
    {
      id: 'analytics',
      label: 'Analizler',
      icon: ChartBarIcon,
      route: '/producer/analytics',
      submenu: [
        { label: 'Satış Analizi', route: '/producer/analytics/sales' },
        { label: 'Teklif Analizi', route: '/producer/analytics/quotes' },
        { label: 'Müşteri Analizi', route: '/producer/analytics/customers' }
      ]
    },
    {
      id: 'company',
      label: 'Şirket Profili',
      icon: BuildingOfficeIcon,
      route: '/producer/company'
    }
  ];

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleNavigate = (route: string) => {
    if (route.startsWith('/producer/')) {
      // Extract the tab name from route
      const tabName = route.split('/')[2];
      setActiveTab(tabName);
    } else {
      router.push(route);
    }
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      // API çağrısı simülasyonu
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockData: DashboardData = {
        kpis: {
          totalSales: 450000,
          activeQuotes: 12,
          monthlyRevenue: 85000,
          productsSold: 156
        },
        charts: {
          salesTrend: {
            labels: ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran'],
            datasets: [{
              label: 'Satış Trendi',
              data: [65000, 72000, 68000, 85000, 92000, 88000],
              borderColor: 'rgb(59, 130, 246)',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              tension: 0.4
            }]
          },
          quoteConversion: {
            labels: ['Gönderilen', 'Görüntülenen', 'Kabul Edilen'],
            datasets: [{
              label: 'Teklif Dönüşümü',
              data: [45, 32, 18],
              backgroundColor: ['#3B82F6', '#10B981', '#F59E0B']
            }]
          },
          productPerformance: {
            labels: ['Mermer', 'Granit', 'Traverten', 'Oniks'],
            datasets: [{
              label: 'Ürün Performansı',
              data: [35, 28, 22, 15],
              backgroundColor: ['#8B5CF6', '#06B6D4', '#84CC16', '#F97316']
            }]
          }
        },
        recentActivity: [
          {
            id: 1,
            type: 'quote_request',
            title: 'Yeni teklif talebi alındı',
            description: 'Ahmet Yılmaz - Beyaz Mermer için teklif istedi',
            timestamp: '2 saat önce',
            status: 'new'
          },
          {
            id: 2,
            type: 'quote_accepted',
            title: 'Teklif kabul edildi',
            description: 'Mehmet Kaya - Siyah Granit teklifinizi kabul etti',
            timestamp: '4 saat önce',
            status: 'success'
          },
          {
            id: 3,
            type: 'order_completed',
            title: 'Sipariş tamamlandı',
            description: 'Fatma Demir - Traverten siparişi teslim edildi',
            timestamp: '1 gün önce',
            status: 'completed'
          }
        ]
      };

      setDashboardData(mockData);
    } catch (error) {
      console.error('Dashboard verisi yüklenirken hata:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar
        items={sidebarItems}
        activeItem={activeTab}
        onItemClick={handleNavigate}
        className="hidden lg:flex"
      />

      {/* Mobile Sidebar Overlay */}
      <div className="lg:hidden">
        <Sidebar
          items={sidebarItems}
          activeItem={activeTab}
          onItemClick={handleNavigate}
          isMobile={true}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 lg:ml-64">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Üretici Dashboard
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  Hoş geldiniz! Satış süreçlerinizi yönetmek için tüm araçlar burada.
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <button className="relative p-2 text-gray-400 hover:text-gray-600">
                  <BellIcon className="h-6 w-6" />
                  <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Dashboard Content */}
        <main className="px-4 sm:px-6 lg:px-8 py-8">
          {/* Render different pages based on activeTab */}
          {activeTab === 'quote-requests' && (
            <ProducerQuoteRequestsPage onNavigate={handleNavigate} />
          )}

          {/* Default Overview Dashboard */}
          {activeTab === 'overview' && (
            <>
              {/* KPI Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <KPICard
                    title="Toplam Satış"
                    value={dashboardData?.kpis.totalSales || 0}
                    currency="USD"
                    change={{ percentage: 15.2, trend: 'up', period: 'month' }}
                    icon="currency-dollar"
                    color="green"
                  />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <KPICard
                    title="Aktif Teklifler"
                    value={dashboardData?.kpis.activeQuotes || 0}
                    change={{ percentage: 8.1, trend: 'up', period: 'week' }}
                    icon="clipboard-document-list"
                    color="blue"
                  />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <KPICard
                    title="Aylık Gelir"
                    value={dashboardData?.kpis.monthlyRevenue || 0}
                    currency="USD"
                    change={{ percentage: 12.3, trend: 'up', period: 'month' }}
                    icon="chart-bar"
                    color="purple"
                  />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <KPICard
                    title="Satılan Ürünler"
                    value={dashboardData?.kpis.productsSold || 0}
                    change={{ percentage: 5.7, trend: 'up', period: 'month' }}
                    icon="cube"
                    color="orange"
                  />
                </motion.div>
              </div>

              {/* Charts and Quick Actions */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                {/* Sales Trend Chart */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="lg:col-span-2"
                >
                  <ChartWidget
                    title="Satış Trendi"
                    type="line"
                    data={dashboardData?.charts.salesTrend}
                    height={300}
                  />
                </motion.div>

                {/* Quick Actions */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  <ProducerQuickActions onNavigate={handleNavigate} />
                </motion.div>
              </div>

              {/* Quote Conversion and Product Performance */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                >
                  <ChartWidget
                    title="Teklif Dönüşümü"
                    type="doughnut"
                    data={dashboardData?.charts.quoteConversion}
                    height={250}
                  />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                >
                  <ChartWidget
                    title="Ürün Performansı"
                    type="bar"
                    data={dashboardData?.charts.productPerformance}
                    height={250}
                  />
                </motion.div>
              </div>

              {/* Recent Activity */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9 }}
              >
                <RecentActivity activities={dashboardData?.recentActivity || []} />
              </motion.div>
            </>
          )}
        </main>
      </div>
    </div>
  );
};

export default ProducerDashboard;
