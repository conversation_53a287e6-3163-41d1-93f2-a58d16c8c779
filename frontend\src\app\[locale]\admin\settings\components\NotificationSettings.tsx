'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Bell, Mail, MessageSquare, Smartphone, AlertTriangle } from 'lucide-react';
import { useSettings } from '../context/SettingsContext';

const NotificationSettings = () => {
  const { getSettingValue, updateSetting } = useSettings();

  // Notification settings values
  const emailEnabled = getSettingValue('notification', 'emailEnabled') || true;
  const smsEnabled = getSettingValue('notification', 'smsEnabled') || false;
  const pushEnabled = getSettingValue('notification', 'pushEnabled') || true;
  const smtpHost = getSettingValue('notification', 'smtpHost') || '';
  const smtpPort = getSettingValue('notification', 'smtpPort') || 587;

  return (
    <div className="space-y-6">
      {/* Notification Channels */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Bell className="w-5 h-5 text-blue-600" />
            <CardTitle>Bildirim Kanalları</CardTitle>
          </div>
          <CardDescription>
            Hangi bildirim kanallarının aktif olacağını belirleyin
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-blue-600" />
                <div>
                  <Label htmlFor="emailEnabled">Email Bildirimleri</Label>
                  <p className="text-sm text-gray-500">SMTP ile email gönderimi</p>
                </div>
              </div>
              <Switch
                id="emailEnabled"
                checked={emailEnabled}
                onCheckedChange={(checked) => updateSetting('notification', 'emailEnabled', checked)}
              />
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <MessageSquare className="w-5 h-5 text-green-600" />
                <div>
                  <Label htmlFor="smsEnabled">SMS Bildirimleri</Label>
                  <p className="text-sm text-gray-500">SMS provider ile mesaj</p>
                </div>
              </div>
              <Switch
                id="smsEnabled"
                checked={smsEnabled}
                onCheckedChange={(checked) => updateSetting('notification', 'smsEnabled', checked)}
              />
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Smartphone className="w-5 h-5 text-purple-600" />
                <div>
                  <Label htmlFor="pushEnabled">Push Bildirimleri</Label>
                  <p className="text-sm text-gray-500">Mobil ve web push</p>
                </div>
              </div>
              <Switch
                id="pushEnabled"
                checked={pushEnabled}
                onCheckedChange={(checked) => updateSetting('notification', 'pushEnabled', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Email Configuration */}
      {emailEnabled && (
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Mail className="w-5 h-5 text-blue-600" />
              <CardTitle>Email Konfigürasyonu</CardTitle>
            </div>
            <CardDescription>
              SMTP sunucu ayarlarını yapılandırın
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="smtpHost">SMTP Sunucu</Label>
                <Input
                  id="smtpHost"
                  value={smtpHost}
                  onChange={(e) => updateSetting('notification', 'smtpHost', e.target.value)}
                  placeholder="smtp.gmail.com"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="smtpPort">SMTP Port</Label>
                <Input
                  id="smtpPort"
                  type="number"
                  value={smtpPort}
                  onChange={(e) => updateSetting('notification', 'smtpPort', parseInt(e.target.value) || 587)}
                  placeholder="587"
                />
              </div>
            </div>

            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Email ayarları henüz tam olarak yapılandırılmamış. Geliştirme aşamasında.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* Notification Rules */}
      <Card>
        <CardHeader>
          <CardTitle>Bildirim Kuralları</CardTitle>
          <CardDescription>
            Hangi durumlarda bildirim gönderileceğini belirleyin
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Bildirim kuralları sistemi geliştirilme aşamasında. Yakında kullanıma sunulacak.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationSettings;
