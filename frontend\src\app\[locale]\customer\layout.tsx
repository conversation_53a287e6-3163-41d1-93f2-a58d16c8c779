'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { ReactNode } from 'react';
import {
  ChartBarIcon,
  HeartIcon,
  ClipboardDocumentListIcon,
  ShoppingCartIcon,
  CalculatorIcon,
  BuildingStorefrontIcon,
  HomeIcon,
  BeakerIcon,
  BellIcon
} from '@heroicons/react/24/outline';
import Sidebar from '@/components/dashboard/components/Sidebar';
import { ProductSelectionModal } from '@/components/ui/product-selection-modal';
import { QuoteRequestModal } from '@/components/ui/quote-request-modal';
import { useAuth } from '@/contexts/auth-context';
import { useSimpleTranslation } from '@/hooks/useSimpleTranslation';


interface CustomerLayoutProps {
  children: ReactNode;
}

function CustomerLayoutClient({ children }: CustomerLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user } = useAuth();
  const { t, isRTL } = useSimpleTranslation();
  const [isProductSelectionModalOpen, setIsProductSelectionModalOpen] = useState(false);
  const [isQuoteRequestModalOpen, setIsQuoteRequestModalOpen] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);

  // Mock unread notifications count
  const unreadNotificationsCount = 3;

  // Debug: User bilgisini kontrol et
  React.useEffect(() => {
    console.log('Customer Layout - Current user:', user);
  }, [user]);

  // Quote modal event listener
  React.useEffect(() => {
    const handleOpenQuoteModal = () => {
      setIsProductSelectionModalOpen(true);
    };

    window.addEventListener('openQuoteModal', handleOpenQuoteModal);
    return () => window.removeEventListener('openQuoteModal', handleOpenQuoteModal);
  }, []);

  const handleProductSelection = (products: any[]) => {
    setSelectedProducts(products);
    setIsProductSelectionModalOpen(false);
    setIsQuoteRequestModalOpen(true);
  };

  const handleQuoteSubmit = (quoteData: any) => {
    console.log('Quote submitted:', { products: selectedProducts, ...quoteData });
    setIsQuoteRequestModalOpen(false);
    setSelectedProducts([]);
  };

  const sidebarItems = [
    {
      id: 'dashboard',
      label: t('dashboard.menu.dashboard', 'Dashboard'),
      icon: HomeIcon,
      route: '/customer/dashboard',
    },
    {
      id: 'requests',
      label: t('dashboard.menu.my_requests', 'Taleplerim'),
      icon: ClipboardDocumentListIcon,
      route: '/customer/requests',
      submenu: [
        { label: t('dashboard.menu.active_requests', 'Aktif Talepler'), route: '/customer/requests/active' },
        { label: t('dashboard.menu.completed_requests', 'Tamamlanan'), route: '/customer/requests/completed' },
        { label: t('dashboard.menu.cancelled_requests', 'İptal Edilen'), route: '/customer/requests/cancelled' },
        { label: t('dashboard.menu.sample_requests', 'Numune Taleplerim'), route: '/customer/requests/samples' },
      ]
    },
    {
      id: 'orders',
      label: t('dashboard.menu.my_orders', 'Siparişlerim'),
      icon: ShoppingCartIcon,
      route: '/customer/orders',
      submenu: [
        { label: t('dashboard.menu.ongoing_orders', 'Devam Eden'), route: '/customer/orders/ongoing' },
        { label: t('dashboard.menu.completed_orders', 'Tamamlanan'), route: '/customer/orders/completed' },
        { label: t('dashboard.menu.cancelled_orders', 'İptal Edilen'), route: '/customer/orders/cancelled' },
      ]
    },
    {
      id: 'favorites',
      label: t('dashboard.menu.favorites', 'Favorilerim'),
      icon: HeartIcon,
      route: '/customer/favorites',
    },
    {
      id: 'accounting',
      label: t('dashboard.menu.accounting', 'Muhasebe'),
      icon: CalculatorIcon,
      route: '/customer/accounting',
      submenu: [
        { label: t('dashboard.menu.invoices', 'Faturalar'), route: '/customer/accounting/invoices' },
        { label: t('dashboard.menu.income_expense', 'Gelir-Gider'), route: '/customer/accounting/income-expense' },
        { label: t('dashboard.menu.reports', 'Raporlar'), route: '/customer/accounting/reports' },
      ]
    },
    {
      id: 'analytics',
      label: t('dashboard.menu.analytics', 'Analizler'),
      icon: ChartBarIcon,
      route: '/customer/analytics',
      submenu: [
        { label: t('dashboard.menu.sales_analysis', 'Satış Analizi'), route: '/customer/analytics/sales' },
        { label: t('dashboard.menu.purchase_analysis', 'Alım Analizi'), route: '/customer/analytics/purchases' },
        { label: t('dashboard.menu.expenses', 'Giderler'), route: '/customer/analytics/expenses' },
        { label: t('dashboard.menu.profit_loss', 'Kar-Zarar'), route: '/customer/analytics/profit-loss' },
      ]
    },
    {
      id: 'notifications',
      label: t('dashboard.menu.notifications', 'Bildirimler'),
      icon: BellIcon,
      route: '/customer/notifications',
      badge: unreadNotificationsCount,
      submenu: [
        { label: t('dashboard.menu.all_notifications', 'Tüm Bildirimler'), route: '/customer/notifications' },
        { label: t('dashboard.menu.notification_settings', 'Bildirim Ayarları'), route: '/customer/notifications/settings' },
      ]
    },
    {
      id: 'stock',
      label: t('dashboard.menu.stock', 'Stok Takibi'),
      icon: BuildingStorefrontIcon,
      route: '/customer/stock',
    },
  ];

  const handleTabChange = (route: string) => {
    router.push(route);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar
        items={sidebarItems}
        activeTab={pathname}
        onTabChange={handleTabChange}
        userName={user?.name || 'Müşteri'}
        userEmail={user?.email || ''}
      />

      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
          <div className="w-full px-6 py-8">
            {children}
          </div>
        </main>
      </div>

      {/* Product Selection Modal */}
      <ProductSelectionModal
        isOpen={isProductSelectionModalOpen}
        onClose={() => setIsProductSelectionModalOpen(false)}
        onProductsSelected={handleProductSelection}
      />

      {/* Quote Request Modal */}
      <QuoteRequestModal
        isOpen={isQuoteRequestModalOpen}
        onClose={() => setIsQuoteRequestModalOpen(false)}
        onSubmit={handleQuoteSubmit}
        selectedProducts={selectedProducts}
      />
    </div>
  );
}

export default function CustomerLayout({ children }: CustomerLayoutProps) {
  return <CustomerLayoutClient>{children}</CustomerLayoutClient>;
}