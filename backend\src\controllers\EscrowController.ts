import { Request, Response } from 'express';
// import { EscrowService } from '../services/EscrowService'; // Temporarily disabled
import { asyncHandler } from '../middleware/errorHandler';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();
// const escrowService = new EscrowService(); // Temporarily disabled

// Validation schemas
const createEscrowSchema = z.object({
  orderId: z.string().cuid(),
  customerId: z.string().cuid(),
  producerId: z.string().cuid(),
  totalAmount: z.number().positive(),
});

const confirmPaymentSchema = z.object({
  escrowId: z.string().cuid(),
  bankTransferReceipt: z.object({
    transferDate: z.string(),
    transferAmount: z.number(),
    senderName: z.string(),
    referenceNumber: z.string().optional(),
    bankName: z.string().optional(),
  }).optional(),
});

const notifyGoodsReadySchema = z.object({
  escrowId: z.string().cuid(),
  productionProof: z.object({
    images: z.array(z.string()).optional(),
    description: z.string().optional(),
    completionDate: z.string(),
  }).optional(),
});

const disputeSchema = z.object({
  escrowId: z.string().cuid(),
  reason: z.string().min(10),
  evidence: z.object({
    description: z.string(),
    attachments: z.array(z.string()).optional(),
  }).optional(),
});

export class EscrowController {
  /**
   * Create escrow account for order
   * @route POST /api/escrow/create
   * @access Private (Admin only)
   */
  createEscrow = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const { orderId, customerId, producerId, totalAmount } = createEscrowSchema.parse(req.body);

    // Verify order exists and is valid
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        customer: true,
        producer: true,
      },
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    if (order.customerId !== customerId || order.producerId !== producerId) {
      return res.status(400).json({
        success: false,
        error: 'Order customer/producer mismatch'
      });
    }

    const escrowAccount = await escrowService.createEscrowAccount(
      orderId,
      customerId,
      producerId,
      totalAmount
    );

    const bankTransferInfo = escrowService.getBankTransferInfo(escrowAccount);

    res.status(201).json({
      success: true,
      data: {
        escrowAccount,
        bankTransferInfo,
        workflow: await escrowService.getPaymentWorkflow(escrowAccount.id),
      },
      message: 'Escrow account created successfully'
    });
  });

  /**
   * Get bank transfer information
   * @route GET /api/escrow/:escrowId/bank-info
   * @access Private (Customer only)
   */
  getBankTransferInfo = asyncHandler(async (req: Request, res: Response) => {
    const { escrowId } = req.params;
    const user = req.user;

    const escrowAccount = await prisma.escrowAccount.findUnique({
      where: { id: escrowId },
    });

    if (!escrowAccount) {
      return res.status(404).json({
        success: false,
        error: 'Escrow account not found'
      });
    }

    // Check if user is customer or admin
    if (user?.userType !== 'admin' && escrowAccount.customerId !== user?.id) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Temporarily disabled due to type issues
    // const bankTransferInfo = escrowService.getBankTransferInfo({
    //   ...escrowAccount,
    //   totalAmount: Number(escrowAccount.totalAmount),
    //   platformCommission: Number(escrowAccount.platformCommission),
    //   producerAmount: Number(escrowAccount.producerAmount),
    //   customerPaidAt: escrowAccount.customerPaidAt || undefined,
    //   producerPaidAt: escrowAccount.producerPaidAt || undefined,
    //   producerNotifiedAt: escrowAccount.producerNotifiedAt || undefined,
    //   customerApprovedAt: escrowAccount.customerApprovedAt || undefined
    // });

    const bankTransferInfo = {
      bankName: 'Temporary Bank',
      accountNumber: '**********',
      iban: 'TR********************1234',
      accountHolder: 'Platform Escrow Account',
      referenceCode: escrowAccount.id,
      amount: Number(escrowAccount.totalAmount),
      currency: escrowAccount.currency
    };

    res.json({
      success: true,
      data: {
        bankTransferInfo,
        escrowStatus: escrowAccount.status,
        totalAmount: escrowAccount.totalAmount,
        referenceCode: escrowAccount.referenceCode,
      }
    });
  });

  /**
   * Confirm customer payment received
   * @route POST /api/escrow/confirm-payment
   * @access Private (Admin only)
   */
  confirmPayment = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const { escrowId, bankTransferReceipt } = confirmPaymentSchema.parse(req.body);

    await escrowService.confirmCustomerPayment(escrowId, bankTransferReceipt);

    const workflow = await escrowService.getPaymentWorkflow(escrowId);

    res.json({
      success: true,
      data: { workflow },
      message: 'Customer payment confirmed successfully'
    });
  });

  /**
   * Producer notifies goods are ready
   * @route POST /api/escrow/goods-ready
   * @access Private (Producer only)
   */
  notifyGoodsReady = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'producer') {
      return res.status(403).json({
        success: false,
        error: 'Producer access required'
      });
    }

    const { escrowId, productionProof } = notifyGoodsReadySchema.parse(req.body);

    await escrowService.notifyGoodsReady(escrowId, user.id, productionProof);

    const workflow = await escrowService.getPaymentWorkflow(escrowId);

    res.json({
      success: true,
      data: { workflow },
      message: 'Goods ready notification sent successfully'
    });
  });

  /**
   * Customer approves payment to producer
   * @route POST /api/escrow/:escrowId/approve
   * @access Private (Customer only)
   */
  approvePayment = asyncHandler(async (req: Request, res: Response) => {
    const { escrowId } = req.params;
    const user = req.user;
    
    if (!user || user.userType !== 'customer') {
      return res.status(403).json({
        success: false,
        error: 'Customer access required'
      });
    }

    await escrowService.customerApprovePayment(escrowId, user.id);

    const workflow = await escrowService.getPaymentWorkflow(escrowId);

    res.json({
      success: true,
      data: { workflow },
      message: 'Payment approved successfully. Producer will be paid.'
    });
  });

  /**
   * Get payment workflow status
   * @route GET /api/escrow/:escrowId/workflow
   * @access Private
   */
  getWorkflow = asyncHandler(async (req: Request, res: Response) => {
    const { escrowId } = req.params;
    const user = req.user;

    const escrowAccount = await prisma.escrowAccount.findUnique({
      where: { id: escrowId },
      include: {
        order: {
          include: {
            customer: { select: { id: true, email: true, companyName: true } },
            producer: { select: { id: true, email: true, companyName: true } },
          }
        }
      },
    });

    if (!escrowAccount) {
      return res.status(404).json({
        success: false,
        error: 'Escrow account not found'
      });
    }

    // Check access permissions
    const hasAccess = user?.userType === 'admin' || 
                     escrowAccount.customerId === user?.id || 
                     escrowAccount.producerId === user?.id;

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    const workflow = await escrowService.getPaymentWorkflow(escrowId);

    res.json({
      success: true,
      data: {
        escrowAccount,
        workflow,
        order: escrowAccount.order,
      }
    });
  });

  /**
   * Handle dispute
   * @route POST /api/escrow/:escrowId/dispute
   * @access Private (Customer or Producer)
   */
  createDispute = asyncHandler(async (req: Request, res: Response) => {
    const { escrowId } = req.params;
    const user = req.user;
    const { reason, evidence } = disputeSchema.parse(req.body);

    const escrowAccount = await prisma.escrowAccount.findUnique({
      where: { id: escrowId },
    });

    if (!escrowAccount) {
      return res.status(404).json({
        success: false,
        error: 'Escrow account not found'
      });
    }

    // Check if user is involved in this escrow
    if (escrowAccount.customerId !== user?.id && escrowAccount.producerId !== user?.id) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Create dispute record
    const dispute = await prisma.dispute.create({
      data: {
        orderId: escrowAccount.orderId,
        customerId: escrowAccount.customerId,
        producerId: escrowAccount.producerId,
        initiatedBy: user.id,
        reason,
        description: reason, // Use reason as description
        evidence,
        status: 'OPEN',
      },
    });

    res.status(201).json({
      success: true,
      data: { dispute },
      message: 'Dispute created successfully. Admin will review and resolve.'
    });
  });

  /**
   * Resolve dispute (Admin only)
   * @route POST /api/escrow/:escrowId/resolve-dispute
   * @access Private (Admin only)
   */
  resolveDispute = asyncHandler(async (req: Request, res: Response) => {
    const { escrowId } = req.params;
    const user = req.user;
    const { resolution, refundCustomer } = req.body;

    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    if (refundCustomer) {
      await escrowService.handleDispute(escrowId, resolution, user.id);
    } else {
      // Pay producer despite dispute
      await escrowService.payProducer(escrowId);
    }

    const workflow = await escrowService.getPaymentWorkflow(escrowId);

    res.json({
      success: true,
      data: { workflow },
      message: 'Dispute resolved successfully'
    });
  });

  /**
   * Get escrow statistics (Admin only)
   * @route GET /api/escrow/stats
   * @access Private (Admin only)
   */
  getStats = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;
    
    if (!user || user.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const stats = await prisma.escrowAccount.groupBy({
      by: ['status'],
      _count: { status: true },
      _sum: { totalAmount: true },
    });

    const totalEscrows = await prisma.escrowAccount.count();
    const totalVolume = await prisma.escrowAccount.aggregate({
      _sum: { totalAmount: true },
    });

    res.json({
      success: true,
      data: {
        totalEscrows,
        totalVolume: totalVolume._sum.totalAmount || 0,
        statusBreakdown: stats,
      }
    });
  });
}
