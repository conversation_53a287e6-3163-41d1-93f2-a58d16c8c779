"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { useTheme } from "./theme-provider"
import { Button } from "./button"

export interface ThemeToggleProps extends React.HTMLAttributes<HTMLButtonElement> {
  variant?: 'button' | 'switch' | 'dropdown'
  size?: 'sm' | 'md' | 'lg'
  showLabel?: boolean
}

/**
 * ThemeToggle component following RFC-004 UI/UX Design System
 * Toggle between light, dark, and system themes with smooth animations
 */
const ThemeToggle = React.forwardRef<HTMLButtonElement, ThemeToggleProps>(
  ({ 
    className, 
    variant = 'button',
    size = 'md',
    showLabel = false,
    ...props 
  }, ref) => {
    
    const { theme, setTheme, resolvedTheme } = useTheme()
    
    const getNextTheme = () => {
      switch (theme) {
        case "light":
          return "dark"
        case "dark":
          return "system"
        case "system":
          return "light"
        default:
          return "light"
      }
    }
    
    const handleToggle = () => {
      setTheme(getNextTheme())
    }
    
    const getIcon = () => {
      switch (theme) {
        case "light":
          return "☀️"
        case "dark":
          return "🌙"
        case "system":
          return "💻"
        default:
          return "☀️"
      }
    }
    
    const getLabel = () => {
      switch (theme) {
        case "light":
          return "Açık Tema"
        case "dark":
          return "Koyu Tema"
        case "system":
          return "Sistem Teması"
        default:
          return "Açık Tema"
      }
    }

    if (variant === 'switch') {
      return (
        <div className="flex items-center gap-3">
          {showLabel && (
            <span className="text-[var(--text-sm)] text-[var(--text-secondary)]">
              {getLabel()}
            </span>
          )}
          <button
            ref={ref}
            onClick={handleToggle}
            className={cn(
              "relative inline-flex h-6 w-11 items-center rounded-full",
              "transition-colors duration-300 ease-in-out",
              "focus:outline-none focus:ring-2 focus:ring-[var(--primary-stone)] focus:ring-offset-2",
              resolvedTheme === "dark" 
                ? "bg-[var(--primary-stone)]" 
                : "bg-[var(--gray-200)]",
              className
            )}
            {...props}
          >
            <span
              className={cn(
                "inline-block h-4 w-4 transform rounded-full",
                "bg-white shadow-lg transition-transform duration-300 ease-in-out",
                "flex items-center justify-center text-xs",
                resolvedTheme === "dark" ? "translate-x-6" : "translate-x-1"
              )}
            >
              {getIcon()}
            </span>
          </button>
        </div>
      )
    }

    if (variant === 'dropdown') {
      return (
        <div className="relative">
          <Button
            ref={ref}
            variant="ghost"
            size={size}
            onClick={handleToggle}
            className={cn(
              "flex items-center gap-2",
              className
            )}
            {...props}
          >
            <span className="text-lg">{getIcon()}</span>
            {showLabel && (
              <span className="hidden sm:inline">
                {getLabel()}
              </span>
            )}
          </Button>
        </div>
      )
    }

    // Default button variant
    return (
      <Button
        ref={ref}
        variant="ghost"
        size={size}
        onClick={handleToggle}
        className={cn(
          "flex items-center gap-2",
          "hover:bg-[var(--gray-100)] hover:text-[var(--primary-stone)]",
          className
        )}
        title={`Şu anda: ${getLabel()}. Değiştirmek için tıklayın.`}
        {...props}
      >
        <span className={cn(
          "text-lg transition-transform duration-300",
          "hover:scale-110"
        )}>
          {getIcon()}
        </span>
        {showLabel && (
          <span className="hidden sm:inline text-[var(--text-sm)]">
            {getLabel()}
          </span>
        )}
      </Button>
    )
  }
)

ThemeToggle.displayName = "ThemeToggle"

export { ThemeToggle }
