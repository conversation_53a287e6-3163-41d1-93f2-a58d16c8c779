"use client"

import { useAuth } from '@/contexts/auth-context'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'customer' | 'producer' | 'admin'
  fallback?: React.ReactNode
}

export function ProtectedRoute({ 
  children, 
  requiredRole,
  fallback = <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-stone-600 mx-auto mb-4"></div>
      <p className="text-gray-600">Yetkilendirme kontrol ediliyor...</p>
    </div>
  </div>
}: ProtectedRouteProps) {
  const { user, isAuthenticated } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // If not authenticated, redirect to home with login prompt
    if (!isAuthenticated) {
      router.push('/?auth_required=true')
      return
    }

    // If specific role required, check user role
    if (requiredRole && user?.role !== requiredRole) {
      // Redirect based on user's actual role
      switch (user?.role) {
        case 'customer':
          router.push('/customer/dashboard')
          break
        case 'producer':
          router.push('/producer/dashboard')
          break
        case 'admin':
          router.push('/admin/dashboard')
          break
        default:
          router.push('/?role_mismatch=true')
      }
      return
    }
  }, [isAuthenticated, user, requiredRole, router])

  // Show loading while checking authentication
  if (!isAuthenticated || (requiredRole && user?.role !== requiredRole)) {
    return fallback
  }

  return <>{children}</>
}

// Convenience components for specific roles
export function CustomerProtectedRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRole="customer">
      {children}
    </ProtectedRoute>
  )
}

export function ProducerProtectedRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRole="producer">
      {children}
    </ProtectedRoute>
  )
}

export function AdminProtectedRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRole="admin">
      {children}
    </ProtectedRoute>
  )
}
