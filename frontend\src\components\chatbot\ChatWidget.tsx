'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Send, X, MessageCircle, Minimize2, Maximize2, ThumbsUp, ThumbsDown } from 'lucide-react';
import { ChatMessage, ChatbotResponse } from '../../types/chatbot';
import { chatbot<PERSON>pi } from '../../services/chatbotApi';

interface ChatWidgetProps {
  userId?: string;
  language?: string;
  position?: 'bottom-right' | 'bottom-left';
  theme?: 'light' | 'dark';
}

export const ChatWidget: React.FC<ChatWidgetProps> = ({
  userId,
  language = 'en',
  position = 'bottom-right',
  theme = 'light'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4'
  };

  const themeClasses = {
    light: {
      widget: 'bg-white border-gray-200 text-gray-900',
      header: 'bg-blue-600 text-white',
      message: {
        user: 'bg-blue-600 text-white',
        assistant: 'bg-gray-100 text-gray-900'
      },
      input: 'bg-white border-gray-300 text-gray-900'
    },
    dark: {
      widget: 'bg-gray-800 border-gray-600 text-white',
      header: 'bg-blue-700 text-white',
      message: {
        user: 'bg-blue-700 text-white',
        assistant: 'bg-gray-700 text-white'
      },
      input: 'bg-gray-700 border-gray-600 text-white'
    }
  };

  const currentTheme = themeClasses[theme];

  useEffect(() => {
    if (isOpen && !sessionId) {
      startConversation();
    }
  }, [isOpen]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, isMinimized]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const startConversation = async () => {
    try {
      setIsLoading(true);
      const response = await chatbotApi.startConversation(userId, language);
      setSessionId(response.sessionId);
      
      const greetingMessage: ChatMessage = {
        id: 'greeting',
        role: 'assistant',
        content: response.greeting,
        timestamp: new Date(),
        language
      };
      
      setMessages([greetingMessage]);
      setSuggestions(['Tell me about marble types', 'How does bidding work?', 'What are your delivery terms?']);
    } catch (error) {
      console.error('Failed to start conversation:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const sendMessage = async (message: string) => {
    if (!message.trim() || !sessionId || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: message,
      timestamp: new Date(),
      language
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setIsTyping(true);

    try {
      const response = await chatbotApi.sendMessage(sessionId, message, userId);
      
      const assistantMessage: ChatMessage = {
        id: Date.now().toString() + '_assistant',
        role: 'assistant',
        content: response.response,
        timestamp: new Date(),
        language,
        intent: response.intent,
        confidence: response.confidence
      };

      setMessages(prev => [...prev, assistantMessage]);
      setSuggestions(response.suggestions || []);

      // Handle escalation
      if (response.escalated) {
        const escalationMessage: ChatMessage = {
          id: Date.now().toString() + '_escalation',
          role: 'system',
          content: `You have been connected to a human agent. ${response.estimatedWaitTime ? `Estimated wait time: ${response.estimatedWaitTime} minutes.` : ''}`,
          timestamp: new Date(),
          language
        };
        setMessages(prev => [...prev, escalationMessage]);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      const errorMessage: ChatMessage = {
        id: Date.now().toString() + '_error',
        role: 'assistant',
        content: 'I apologize, but I encountered an error. Please try again.',
        timestamp: new Date(),
        language
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    sendMessage(suggestion);
  };

  const handleFeedback = async (messageId: string, rating: number) => {
    if (!sessionId) return;
    
    try {
      await chatbotApi.submitFeedback(sessionId, messageId, rating);
    } catch (error) {
      console.error('Failed to submit feedback:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(inputMessage);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (!isOpen) {
    return (
      <div className={`fixed ${positionClasses[position]} z-50`}>
        <button
          onClick={() => setIsOpen(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg transition-all duration-200 hover:scale-110"
          aria-label="Open chat"
        >
          <MessageCircle size={24} />
        </button>
      </div>
    );
  }

  return (
    <div className={`fixed ${positionClasses[position]} z-50 w-80 h-96 ${currentTheme.widget} border rounded-lg shadow-xl flex flex-col`}>
      {/* Header */}
      <div className={`${currentTheme.header} p-4 rounded-t-lg flex items-center justify-between`}>
        <div className="flex items-center space-x-2">
          <MessageCircle size={20} />
          <span className="font-medium">AI Assistant</span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="hover:bg-blue-700 p-1 rounded"
            aria-label={isMinimized ? 'Maximize' : 'Minimize'}
          >
            {isMinimized ? <Maximize2 size={16} /> : <Minimize2 size={16} />}
          </button>
          <button
            onClick={() => setIsOpen(false)}
            className="hover:bg-blue-700 p-1 rounded"
            aria-label="Close chat"
          >
            <X size={16} />
          </button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-3 py-2 rounded-lg ${
                    message.role === 'user'
                      ? currentTheme.message.user
                      : message.role === 'system'
                      ? 'bg-yellow-100 text-yellow-800 border border-yellow-300'
                      : currentTheme.message.assistant
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <div className="flex items-center justify-between mt-1">
                    <span className="text-xs opacity-70">
                      {formatTime(message.timestamp)}
                    </span>
                    {message.role === 'assistant' && message.confidence && (
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => handleFeedback(message.id, 5)}
                          className="hover:bg-green-200 p-1 rounded"
                          aria-label="Good response"
                        >
                          <ThumbsUp size={12} />
                        </button>
                        <button
                          onClick={() => handleFeedback(message.id, 1)}
                          className="hover:bg-red-200 p-1 rounded"
                          aria-label="Poor response"
                        >
                          <ThumbsDown size={12} />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className={`${currentTheme.message.assistant} px-3 py-2 rounded-lg`}>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Suggestions */}
          {suggestions.length > 0 && (
            <div className="px-4 pb-2">
              <div className="flex flex-wrap gap-2">
                {suggestions.slice(0, 3).map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 px-2 py-1 rounded-full transition-colors"
                    disabled={isLoading}
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Input */}
          <div className="p-4 border-t">
            <div className="flex space-x-2">
              <input
                ref={inputRef}
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className={`flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${currentTheme.input}`}
                disabled={isLoading}
              />
              <button
                onClick={() => sendMessage(inputMessage)}
                disabled={isLoading || !inputMessage.trim()}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white p-2 rounded-lg transition-colors"
                aria-label="Send message"
              >
                <Send size={16} />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
