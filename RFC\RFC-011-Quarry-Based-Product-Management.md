# RFC-011: Ocak Tabanlı Ürün Yönetim Sistemi

**Tarih**: 2025-06-30  
**Durum**: Taslak  
**Yazar**: Development Team  
**Gözden Geçiren**: Product Team  

## Özet

Bu RFC, aynı ürünün birden fazla üretici tarafından sisteme girilmesini engellemek ve ocak tabanlı ürün yönetimi sistemi oluşturmak için tasarlanmıştır. Sistem, her ürünü ocağına göre kategorize edecek ve müşteri talepleri geldiğinde o ürünü üreten tüm üreticilere bildirim gönderecektir.

## Problem Tanımı

### Mevcut Durum
- Aynı ürün (örn: Afyon Beyaz Mermer) birden fazla üretici tarafından ayrı ayrı sisteme giriliyor
- Bu durum sistemde karmaşıklığa neden oluyor
- Müşteri bir ürün için talep oluşturduğunda sadece o ürünü ekleyen üreticiye ulaşıyor
- Diğer üreticiler potansiyel müşterileri kaçırıyor

### Hedeflenen Durum
- Her ürün ocağına göre tek bir kez sisteme girilecek
- Aynı ocaktan ürün üreten üreticiler o ürünün üretici listesine eklenecek
- Müşteri talebi geldiğinde o ürünü üreten tüm üreticilere bildirim gidecek
- Admin panelinde her ürün için üretici listesi görünecek

## Teknik Tasarım

### 1. Veri Modeli

#### 1.1 Quarry (Ocak) Tablosu
```typescript
interface Quarry {
  id: string
  name: string // "Afyon Beyaz Mermer Ocağı"
  location: {
    city: string
    district: string
    coordinates: {
      lat: number
      lng: number
    }
    address: string
    googleMapsLink?: string
  }
  owner: string // Ocak sahibi firma
  establishedYear?: number
  capacity?: string
  certifications: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}
```

#### 1.2 Product (Ürün) Tablosu - Güncellenmiş
```typescript
interface Product {
  id: string
  quarryId: string // Hangi ocaktan geldiği
  name: string // "Afyon Beyaz Mermer"
  category: string
  description: string
  technicalSpecs: {
    density?: number
    hardness?: number
    waterAbsorption?: number
    freezeThawResistance?: string
    // Diğer teknik özellikler
  }
  media: {
    coverImage: string
    images: string[]
    videos?: string[]
    documents?: string[]
  }
  producers: string[] // Bu ürünü üreten üretici ID'leri
  isActive: boolean
  approvalStatus: 'pending' | 'approved' | 'rejected'
  createdAt: Date
  updatedAt: Date
  createdBy: string // İlk ekleyen üretici
}
```

#### 1.3 ProducerProduct (Üretici-Ürün İlişkisi) Tablosu
```typescript
interface ProducerProduct {
  id: string
  producerId: string
  productId: string
  quarryId: string
  priceList: {
    dimensions: Array<{
      width: number
      height: number
      thickness: number
      price: number
      currency: string
      unit: string
    }>
    slabs: Array<{
      size: string
      price: number
      currency: string
      unit: string
    }>
  }
  surfaceFinishes: string[]
  packaging: string[]
  delivery: string[]
  stock: number
  minOrder: number
  productionCapacity: string
  isActive: boolean
  joinedAt: Date
}
```

### 2. Kullanıcı Arayüzü Değişiklikleri

#### 2.1 Ürün Ekleme Süreci
1. **Ocak Seçimi**: Üretici önce ocağını seçer/ekler
2. **Ürün Kontrolü**: Sistem o ocaktan daha önce ürün eklenip eklenmediğini kontrol eder
3. **Yeni Ürün**: Eğer yoksa tam ürün detaylarını girer
4. **Mevcut Ürün**: Eğer varsa sadece fiyat listesi ve üretim detaylarını girer

#### 2.2 Tam Ekran Ürün Formu
- Modal yerine yeni sayfa (/producer/products/add)
- Daha geniş alan ve daha iyi kullanıcı deneyimi
- Adım adım wizard yapısı korunacak

### 3. Admin Panel Özellikleri

#### 3.1 Ürün Yönetimi
- Her ürün için üretici listesi
- Ocak bilgileri
- Toplam üretim kapasitesi
- Fiyat karşılaştırması

#### 3.2 Ocak Yönetimi
- Tüm ocakların listesi
- Her ocaktan çıkan ürünler
- Ocak sahipleri ve üreticiler

## Implementation Plan

### Faz 1: Veri Modeli ve Backend
1. Yeni veri modellerini oluştur
2. API endpoint'lerini güncelle
3. Mevcut verileri migrate et

### Faz 2: Üretici Arayüzü
1. Ocak seçimi/ekleme formu
2. Ürün ekleme sürecini güncelle
3. Tam ekran ürün formu

### Faz 3: Admin Panel
1. Ocak yönetimi sayfası
2. Ürün-üretici ilişki yönetimi
3. Raporlama özellikleri

### Faz 4: Bildirim Sistemi
1. Müşteri talebi geldiğinde tüm üreticilere bildirim
2. Email/SMS entegrasyonu
3. Dashboard bildirimleri

## Güvenlik ve Gizlilik

- Üretici listesi sadece admin panelinde görünür
- Müşteriler hangi üreticilerin o ürünü ürettiğini göremez
- Fiyat bilgileri gizli kalır
- Sadece teklif süreci başladığında üreticiler birbirlerini görür

## Test Senaryoları

1. **Yeni Ocak ve Ürün**: Üretici yeni ocak ekler ve ilk ürünü girer
2. **Mevcut Ocak, Yeni Ürün**: Mevcut ocaktan yeni ürün eklenir
3. **Mevcut Ürün**: Başka üretici aynı ocaktan ürün ürettiğini belirtir
4. **Müşteri Talebi**: Talep geldiğinde tüm üreticilere bildirim gider
5. **Admin Görünümü**: Admin tüm ürün-üretici ilişkilerini görür

## Sonuç

Bu sistem sayesinde:
- Ürün tekrarları önlenecek
- Müşteriler daha fazla üreticiye ulaşabilecek
- Rekabet artacak
- Sistem daha organize olacak
- Admin kontrolü artacak

## Sonraki Adımlar

1. Stakeholder onayı
2. Detaylı teknik tasarım
3. Development başlangıcı
4. Test ve deployment planı
