'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { PencilIcon, TrashIcon, EyeIcon } from '@heroicons/react/24/outline';

// Mock data - gerçek uygulamada API'den gelecek
const mockExpenses = [
  {
    id: 1,
    title: 'Ocak Ayı Nakliye Ücreti',
    category: 'Nakliye Ücreti',
    amount: 1200.00,
    currency: 'USD',
    date: '2024-01-15',
    supplier: 'ABC Lojistik',
    paymentMethod: 'Banka Havalesi',
    invoiceNumber: 'FAT-2024-001',
    isRecurring: false
  },
  {
    id: 2,
    title: 'Gümrük Vergisi - Mermer İthalatı',
    category: 'Gümrük Vergisi',
    amount: 2500.00,
    currency: 'USD',
    date: '2024-01-20',
    supplier: 'Gümrük Müşavirliği',
    paymentMethod: 'Çek',
    invoiceNumber: 'GUM-2024-005',
    isRecurring: false
  },
  {
    id: 3,
    title: '<PERSON><PERSON><PERSON><PERSON>',
    category: 'Kira',
    amount: 800.00,
    currency: 'USD',
    date: '2024-01-01',
    supplier: 'XYZ Gayrimenkul',
    paymentMethod: 'Banka Havalesi',
    invoiceNumber: 'KRA-2024-001',
    isRecurring: true
  }
];

export default function ExpensesPage() {
  const [expenses, setExpenses] = useState(mockExpenses);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [sortBy, setSortBy] = useState('date');

  // Filtreleme ve arama
  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.supplier.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterCategory === 'all' || expense.category === filterCategory;

    return matchesSearch && matchesFilter;
  });

  // Sıralama
  const sortedExpenses = [...filteredExpenses].sort((a, b) => {
    switch (sortBy) {
      case 'date':
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      case 'amount':
        return b.amount - a.amount;
      case 'category':
        return a.category.localeCompare(b.category);
      default:
        return 0;
    }
  });

  const handleDelete = (id: number) => {
    if (confirm('Bu gider kaydını silmek istediğinizden emin misiniz?')) {
      setExpenses(expenses.filter(expense => expense.id !== id));
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'Nakliye Ücreti': 'bg-blue-100 text-blue-800',
      'Gümrük Vergisi': 'bg-red-100 text-red-800',
      'Kira': 'bg-green-100 text-green-800',
      'Elektrik': 'bg-yellow-100 text-yellow-800',
      'Su': 'bg-cyan-100 text-cyan-800',
      'İnternet': 'bg-purple-100 text-purple-800',
      'Telefon': 'bg-pink-100 text-pink-800',
      'Personel Maaşı': 'bg-indigo-100 text-indigo-800',
      'Diğer': 'bg-gray-100 text-gray-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
  const monthlyExpenses = expenses.filter(expense => {
    const expenseDate = new Date(expense.date);
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    return expenseDate.getMonth() === currentMonth && expenseDate.getFullYear() === currentYear;
  }).reduce((sum, expense) => sum + expense.amount, 0);

  const recurringExpenses = expenses.filter(expense => expense.isRecurring).length;

  if (expenses.length === 0) {
    return (
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Giderler</h1>
            <p className="text-gray-600 mt-1">Gümrük, vergi, nakliye ve diğer giderlerinizi takip edin</p>
          </div>
          <Link
            href="/customer/analytics/expenses/add"
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
          >
            <span>+</span>
            <span>Gider Ekle</span>
          </Link>
        </div>

        {/* Empty State */}
        <div className="bg-white rounded-lg shadow">
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-600 text-4xl">💸</span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Henüz gider kaydı yok
            </h3>
            <p className="text-gray-600 mb-6">
              İlk giderinizi ekleyerek takibe başlayın.
            </p>
            <div className="flex justify-center space-x-4">
              <Link
                href="/customer/analytics/expenses/add"
                className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
              >
                İlk Gideri Ekle
              </Link>
              <Link
                href="/customer/analytics"
                className="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Analytics'e Dön
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Giderler</h1>
          <p className="text-gray-600 mt-1">Gümrük, vergi, nakliye ve diğer giderlerinizi takip edin</p>
        </div>
        <Link
          href="/customer/analytics/expenses/add"
          className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
        >
          <span>+</span>
          <span>Gider Ekle</span>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <span className="text-red-600 text-xl">💰</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Toplam Gider</p>
              <p className="text-2xl font-bold text-gray-900">${totalExpenses.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <span className="text-orange-600 text-xl">📅</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Bu Ay</p>
              <p className="text-2xl font-bold text-gray-900">${monthlyExpenses.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <span className="text-blue-600 text-xl">📊</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Toplam Kayıt</p>
              <p className="text-2xl font-bold text-gray-900">{expenses.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <span className="text-purple-600 text-xl">🔄</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Düzenli Gider</p>
              <p className="text-2xl font-bold text-gray-900">{recurringExpenses}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">

          {/* Search */}
          <div className="flex-1">
            <input
              type="text"
              placeholder="Gider başlığı, tedarikçi veya fatura numarası ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            />
          </div>

          {/* Filters */}
          <div className="flex space-x-4">
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            >
              <option value="all">Tüm Kategoriler</option>
              <option value="Nakliye Ücreti">Nakliye Ücreti</option>
              <option value="Gümrük Vergisi">Gümrük Vergisi</option>
              <option value="Kira">Kira</option>
              <option value="Elektrik">Elektrik</option>
              <option value="Su">Su</option>
              <option value="İnternet">İnternet</option>
              <option value="Telefon">Telefon</option>
              <option value="Personel Maaşı">Personel Maaşı</option>
              <option value="Diğer">Diğer</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            >
              <option value="date">Tarihe Göre</option>
              <option value="amount">Tutara Göre</option>
              <option value="category">Kategoriye Göre</option>
            </select>
          </div>
        </div>
      </div>

      {/* Expenses Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Gider
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Kategori
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tutar
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tedarikçi
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ödeme Yöntemi
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tarih
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedExpenses.map((expense) => (
                <tr key={expense.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{expense.title}</div>
                      <div className="text-sm text-gray-500">{expense.invoiceNumber}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(expense.category)}`}>
                      {expense.category}
                    </span>
                    {expense.isRecurring && (
                      <div className="text-xs text-blue-600 mt-1">🔄 Düzenli</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      ${expense.amount.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500">{expense.currency}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{expense.supplier}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{expense.paymentMethod}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(expense.date).toLocaleDateString('tr-TR')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => window.location.href = `/customer/analytics/expenses/view/${expense.id}`}
                        className="text-blue-600 hover:text-blue-900 p-1"
                        title="Görüntüle"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => window.location.href = `/customer/analytics/expenses/edit/${expense.id}`}
                        className="text-indigo-600 hover:text-indigo-900 p-1"
                        title="Düzenle"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(expense.id)}
                        className="text-red-600 hover:text-red-900 p-1"
                        title="Sil"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* No Results */}
        {sortedExpenses.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">Arama kriterlerinize uygun gider bulunamadı.</p>
          </div>
        )}
      </div>
    </div>
  );
}
