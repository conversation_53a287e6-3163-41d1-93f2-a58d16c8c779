'use client';

import React from 'react';
import { XCircleIcon } from '@heroicons/react/24/outline';

interface CancelledOrdersPageProps {
  onNavigate?: (route: string) => void;
}

const CancelledOrdersPage: React.FC<CancelledOrdersPageProps> = ({ onNavigate }) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">İptal Edilen Siparişler</h1>
          <p className="text-gray-600 mt-1">İptal edilen siparişleriniz</p>
        </div>
      </div>

      {/* Empty State */}
      <div className="text-center py-12">
        <XCircleIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          İptal edilen siparişiniz bulunmuyor
        </h3>
        <p className="text-gray-600 mb-6">
          İptal ettiğiniz siparişler burada görünecek.
        </p>
        <button
          onClick={() => onNavigate?.('/customer/orders')}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Tüm Siparişlere Dön
        </button>
      </div>
    </div>
  );
};

export default CancelledOrdersPage;
