import { Request, Response } from 'express'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { PrismaClient } from '@prisma/client'
import { validationResult } from 'express-validator'
import crypto from 'crypto'
import nodemailer from 'nodemailer'

const prisma = new PrismaClient()

// Rate limiting için memory store (production'da Redis kullanılmalı)
const loginAttempts = new Map<string, { count: number; lastAttempt: Date; lockedUntil?: Date }>()

// Email transporter (Gmail için)
const createEmailTransporter = () => {
  return nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.ADMIN_EMAIL || '<EMAIL>',
      pass: process.env.ADMIN_EMAIL_PASSWORD || 'temp_password'
    }
  })
}

// Rate limiting kontrolü
const checkRateLimit = (ip: string): { allowed: boolean; remainingTime?: number } => {
  const attempt = loginAttempts.get(ip)
  const now = new Date()

  if (!attempt) {
    return { allowed: true }
  }

  // Eğer kilitli ise ve süre dolmamışsa
  if (attempt.lockedUntil && attempt.lockedUntil > now) {
    const remainingTime = Math.ceil((attempt.lockedUntil.getTime() - now.getTime()) / 1000 / 60)
    return { allowed: false, remainingTime }
  }

  // Kilit süresi dolmuşsa sıfırla
  if (attempt.lockedUntil && attempt.lockedUntil <= now) {
    loginAttempts.delete(ip)
    return { allowed: true }
  }

  return { allowed: true }
}

// Başarısız giriş denemesini kaydet
const recordFailedAttempt = async (ip: string, email: string, adminId?: string) => {
  const attempt = loginAttempts.get(ip) || { count: 0, lastAttempt: new Date() }
  attempt.count += 1
  attempt.lastAttempt = new Date()

  // 3 başarısız denemeden sonra 15 dakika kilitle
  if (attempt.count >= 3) {
    attempt.lockedUntil = new Date(Date.now() + 15 * 60 * 1000) // 15 dakika
  }

  loginAttempts.set(ip, attempt)

  // Database'e kaydet
  await prisma.loginAttempt.create({
    data: {
      adminId,
      email,
      ip,
      userAgent: '', // Request'ten alınabilir
      success: false
    }
  })
}

// Başarılı girişi kaydet
const recordSuccessfulLogin = async (ip: string, email: string, adminId: string) => {
  // Rate limiting'i sıfırla
  loginAttempts.delete(ip)

  // Database'e kaydet
  await prisma.loginAttempt.create({
    data: {
      adminId,
      email,
      ip,
      userAgent: '',
      success: true
    }
  })

  // Admin'in son giriş bilgilerini güncelle
  await prisma.admin.update({
    where: { id: adminId },
    data: {
      lastLoginAt: new Date(),
      lastLoginIp: ip,
      failedLoginAttempts: 0,
      lockedUntil: null
    }
  })
}

export const adminLogin = async (req: Request, res: Response) => {
  try {
    // Validation kontrolü
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz giriş bilgileri',
        errors: errors.array()
      })
    }

    const { email, password, rememberMe } = req.body
    const clientIp = req.ip || req.connection.remoteAddress || 'unknown'

    // Rate limiting kontrolü
    const rateLimitCheck = checkRateLimit(clientIp)
    if (!rateLimitCheck.allowed) {
      return res.status(429).json({
        success: false,
        message: `Çok fazla başarısız deneme. ${rateLimitCheck.remainingTime} dakika sonra tekrar deneyin.`
      })
    }

    // Admin'i bul
    const admin = await prisma.admin.findUnique({
      where: { email }
    })

    if (!admin) {
      await recordFailedAttempt(clientIp, email)
      return res.status(401).json({
        success: false,
        message: 'Geçersiz email veya şifre'
      })
    }

    // Admin aktif mi kontrol et
    if (!admin.isActive) {
      await recordFailedAttempt(clientIp, email, admin.id)
      return res.status(401).json({
        success: false,
        message: 'Hesabınız devre dışı bırakılmış'
      })
    }

    // Şifre kontrolü
    const isPasswordValid = await bcrypt.compare(password, admin.password)
    if (!isPasswordValid) {
      await recordFailedAttempt(clientIp, email, admin.id)
      return res.status(401).json({
        success: false,
        message: 'Geçersiz email veya şifre'
      })
    }

    // JWT token oluştur
    const tokenExpiry = rememberMe ? '30d' : '8h'
    const token = jwt.sign(
      { 
        adminId: admin.id, 
        email: admin.email, 
        role: admin.role 
      },
      process.env.JWT_SECRET || 'admin_secret_key',
      { expiresIn: tokenExpiry }
    )

    // Başarılı girişi kaydet
    await recordSuccessfulLogin(clientIp, email, admin.id)

    // Cookie ayarla
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: rememberMe ? 30 * 24 * 60 * 60 * 1000 : 8 * 60 * 60 * 1000 // 30 gün veya 8 saat
    }

    res.cookie('admin_token', token, cookieOptions)

    res.json({
      success: true,
      message: 'Giriş başarılı',
      admin: {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: admin.role
      }
    })

  } catch (error) {
    console.error('Admin login error:', error)
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    })
  }
}

export const adminLogout = async (req: Request, res: Response) => {
  try {
    res.clearCookie('admin_token')
    res.json({
      success: true,
      message: 'Çıkış başarılı'
    })
  } catch (error) {
    console.error('Admin logout error:', error)
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    })
  }
}

export const verifyAuth = async (req: Request, res: Response) => {
  try {
    const token = req.cookies.admin_token

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Token bulunamadı'
      })
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'admin_secret_key') as any

    const admin = await prisma.admin.findUnique({
      where: { id: decoded.adminId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true
      }
    })

    if (!admin || !admin.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Geçersiz token veya hesap devre dışı'
      })
    }

    res.json({
      success: true,
      admin
    })

  } catch (error) {
    console.error('Auth verification error:', error)
    res.status(401).json({
      success: false,
      message: 'Geçersiz token'
    })
  }
}

export const forgotPassword = async (req: Request, res: Response) => {
  try {
    const { email } = req.body

    const admin = await prisma.admin.findUnique({
      where: { email }
    })

    // Güvenlik için admin bulunamasa bile başarılı mesaj döndür
    if (!admin) {
      return res.json({
        success: true,
        message: 'Eğer bu email adresi sistemde kayıtlıysa, şifre sıfırlama linki gönderildi.'
      })
    }

    // Eski token'ları temizle
    await prisma.passwordResetToken.deleteMany({
      where: { adminId: admin.id }
    })

    // Yeni token oluştur
    const resetToken = crypto.randomBytes(32).toString('hex')
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000) // 15 dakika

    await prisma.passwordResetToken.create({
      data: {
        token: resetToken,
        adminId: admin.id,
        expiresAt
      }
    })

    // Email gönder
    const transporter = createEmailTransporter()
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3001'}/admin/reset-password?token=${resetToken}`

    await transporter.sendMail({
      from: process.env.ADMIN_EMAIL || '<EMAIL>',
      to: admin.email,
      subject: 'Admin Şifre Sıfırlama',
      html: `
        <h2>Şifre Sıfırlama Talebi</h2>
        <p>Merhaba ${admin.name},</p>
        <p>Admin paneli şifrenizi sıfırlamak için aşağıdaki linke tıklayın:</p>
        <a href="${resetUrl}" style="background: #6b7280; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Şifremi Sıfırla</a>
        <p>Bu link 15 dakika geçerlidir.</p>
        <p>Eğer bu talebi siz yapmadıysanız, bu emaili görmezden gelebilirsiniz.</p>
      `
    })

    res.json({
      success: true,
      message: 'Eğer bu email adresi sistemde kayıtlıysa, şifre sıfırlama linki gönderildi.'
    })

  } catch (error) {
    console.error('Forgot password error:', error)
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    })
  }
}
