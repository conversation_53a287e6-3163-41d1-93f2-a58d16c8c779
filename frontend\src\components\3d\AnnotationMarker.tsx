'use client';

import React, { useRef, useState } from 'react';
import { Html } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';
import { AnnotationMarkerProps } from '../../types/3d';
import { Info, AlertTriangle, Star, Ruler } from 'lucide-react';

export const AnnotationMarker: React.FC<AnnotationMarkerProps> = ({
  annotation,
  onClick,
  visible = true
}) => {
  const markerRef = useRef<THREE.Group>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  // Animation for floating effect
  useFrame((state) => {
    if (markerRef.current && visible) {
      markerRef.current.position.y = annotation.position.y + Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  const getIconComponent = () => {
    switch (annotation.type) {
      case 'info':
        return Info;
      case 'warning':
        return AlertTriangle;
      case 'feature':
        return Star;
      case 'dimension':
        return Ruler;
      default:
        return Info;
    }
  };

  const getIconColor = () => {
    switch (annotation.type) {
      case 'info':
        return 'text-blue-600';
      case 'warning':
        return 'text-yellow-600';
      case 'feature':
        return 'text-green-600';
      case 'dimension':
        return 'text-purple-600';
      default:
        return 'text-blue-600';
    }
  };

  const getBackgroundColor = () => {
    switch (annotation.type) {
      case 'info':
        return 'bg-blue-100 border-blue-300';
      case 'warning':
        return 'bg-yellow-100 border-yellow-300';
      case 'feature':
        return 'bg-green-100 border-green-300';
      case 'dimension':
        return 'bg-purple-100 border-purple-300';
      default:
        return 'bg-blue-100 border-blue-300';
    }
  };

  if (!visible) {
    return null;
  }

  const IconComponent = getIconComponent();

  return (
    <group
      ref={markerRef}
      position={[annotation.position.x, annotation.position.y, annotation.position.z]}
    >
      {/* 3D Marker Sphere */}
      <mesh
        onClick={(e) => {
          e.stopPropagation();
          onClick?.(annotation);
          setShowTooltip(!showTooltip);
        }}
        onPointerOver={(e) => {
          e.stopPropagation();
          setIsHovered(true);
          document.body.style.cursor = 'pointer';
        }}
        onPointerOut={(e) => {
          e.stopPropagation();
          setIsHovered(false);
          document.body.style.cursor = 'auto';
        }}
      >
        <sphereGeometry args={[0.1, 16, 16]} />
        <meshStandardMaterial
          color={annotation.type === 'warning' ? '#f59e0b' : 
                annotation.type === 'feature' ? '#10b981' :
                annotation.type === 'dimension' ? '#8b5cf6' : '#3b82f6'}
          emissive={annotation.type === 'warning' ? '#f59e0b' : 
                   annotation.type === 'feature' ? '#10b981' :
                   annotation.type === 'dimension' ? '#8b5cf6' : '#3b82f6'}
          emissiveIntensity={isHovered ? 0.3 : 0.1}
          transparent
          opacity={isHovered ? 0.9 : 0.7}
        />
      </mesh>

      {/* Pulsing Ring Effect */}
      <mesh rotation={[Math.PI / 2, 0, 0]}>
        <ringGeometry args={[0.15, 0.2, 16]} />
        <meshBasicMaterial
          color={annotation.type === 'warning' ? '#f59e0b' : 
                annotation.type === 'feature' ? '#10b981' :
                annotation.type === 'dimension' ? '#8b5cf6' : '#3b82f6'}
          transparent
          opacity={isHovered ? 0.4 : 0.2}
        />
      </mesh>

      {/* HTML Tooltip */}
      {(showTooltip || isHovered) && (
        <Html
          position={[0, 0.3, 0]}
          center
          distanceFactor={10}
          occlude
          style={{
            pointerEvents: 'none',
            userSelect: 'none'
          }}
        >
          <div className={`
            max-w-xs p-3 rounded-lg shadow-lg border-2 transform -translate-x-1/2 -translate-y-full
            ${getBackgroundColor()}
            animate-in fade-in duration-200
          `}>
            {/* Arrow pointing down */}
            <div className={`
              absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0
              border-l-4 border-r-4 border-t-4 border-transparent
              ${annotation.type === 'warning' ? 'border-t-yellow-300' :
                annotation.type === 'feature' ? 'border-t-green-300' :
                annotation.type === 'dimension' ? 'border-t-purple-300' : 'border-t-blue-300'}
            `} />
            
            {/* Header */}
            <div className="flex items-center mb-2">
              <IconComponent className={`w-4 h-4 mr-2 ${getIconColor()}`} />
              <h3 className="font-semibold text-gray-900 text-sm">
                {annotation.title}
              </h3>
            </div>

            {/* Description */}
            {annotation.description && (
              <p className="text-gray-700 text-xs leading-relaxed">
                {annotation.description}
              </p>
            )}

            {/* Click hint */}
            <div className="mt-2 text-xs text-gray-500">
              Click for details
            </div>
          </div>
        </Html>
      )}

      {/* Connection Line to Surface */}
      <line>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={2}
            array={new Float32Array([
              0, 0, 0,  // Start at annotation position
              0, -annotation.position.y, 0  // End at ground level
            ])}
            itemSize={3}
          />
        </bufferGeometry>
        <lineBasicMaterial
          color={annotation.type === 'warning' ? '#f59e0b' : 
                annotation.type === 'feature' ? '#10b981' :
                annotation.type === 'dimension' ? '#8b5cf6' : '#3b82f6'}
          transparent
          opacity={0.3}
          linewidth={2}
        />
      </line>
    </group>
  );
};

export default AnnotationMarker;
