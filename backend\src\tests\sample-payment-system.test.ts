import request from 'supertest';

const BASE_URL = 'http://localhost:8001';

describe('Sample Payment System Tests', () => {
  
  describe('Shipping Cost Calculation', () => {
    test('should calculate domestic shipping cost correctly', async () => {
      const response = await request(BASE_URL)
        .post('/api/samples/shipping/calculate')
        .send({
          fromAddress: { city: 'İstanbul', country: 'Türkiye' },
          toAddress: { city: 'Ankara', country: 'Türkiye' },
          productCount: 1,
          carrier: 'aras'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.shippingRate).toBeDefined();
      expect(response.body.data.shippingRate.baseRate).toBeGreaterThan(0);
      expect(response.body.data.shippingRate.currency).toBe('TRY');
      expect(response.body.data.availableCarriers).toBeDefined();
    });

    test('should calculate international shipping cost correctly', async () => {
      const response = await request(BASE_URL)
        .post('/api/samples/shipping/calculate')
        .send({
          fromAddress: { city: 'İstanbul', country: 'Türkiye' },
          toAddress: { city: 'Berlin', country: 'Germany' },
          productCount: 2,
          carrier: 'dhl'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.shippingRate.baseRate).toBeGreaterThan(50);
    });

    test('should apply product count multiplier', async () => {
      const singleProductResponse = await request(BASE_URL)
        .post('/api/samples/shipping/calculate')
        .send({
          fromAddress: { city: 'İstanbul', country: 'Türkiye' },
          toAddress: { city: 'Ankara', country: 'Türkiye' },
          productCount: 1,
          carrier: 'aras'
        });

      const multipleProductResponse = await request(BASE_URL)
        .post('/api/samples/shipping/calculate')
        .send({
          fromAddress: { city: 'İstanbul', country: 'Türkiye' },
          toAddress: { city: 'Ankara', country: 'Türkiye' },
          productCount: 3,
          carrier: 'aras'
        });

      expect(multipleProductResponse.body.data.shippingRate.baseRate)
        .toBeGreaterThan(singleProductResponse.body.data.shippingRate.baseRate);
    });
  });

  describe('Producer Approval with Shipping Cost', () => {
    test('should approve sample request with shipping cost', async () => {
      const response = await request(BASE_URL)
        .put('/api/samples/producer/sample-1/approve')
        .send({
          approved: true,
          notes: 'Test onay',
          preparationDays: 5,
          shippingCost: 35,
          carrier: 'aras'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('approved_pending_payment');
      expect(response.body.data.producerResponse.shippingCost).toBe(35);
      expect(response.body.payment).toBeDefined();
    });

    test('should reject sample request', async () => {
      const response = await request(BASE_URL)
        .put('/api/samples/producer/sample-1/approve')
        .send({
          approved: false,
          rejectionReason: 'Test red sebebi'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('rejected');
      expect(response.body.data.producerResponse.rejectionReason).toBe('Test red sebebi');
    });
  });

  describe('Payment Processing', () => {
    test('should process credit card payment successfully', async () => {
      const paymentResponse = await request(BASE_URL)
        .get('/api/samples/payment/sample-1');

      if (paymentResponse.status === 200) {
        const paymentId = paymentResponse.body.data.id;

        const processResponse = await request(BASE_URL)
          .post(`/api/samples/payment/${paymentId}/process`)
          .send({
            paymentMethod: 'credit_card',
            transactionData: {
              cardNumber: '****************',
              expiryDate: '12/25',
              cvv: '123',
              cardholderName: 'Test User'
            }
          });

        expect(processResponse.status).toBe(200);
        expect(processResponse.body.success).toBe(true);
        expect(processResponse.body.data.payment.status).toBe('completed');
      }
    });

    test('should process bank transfer payment', async () => {
      const paymentResponse = await request(BASE_URL)
        .get('/api/samples/payment/sample-2');

      if (paymentResponse.status === 200) {
        const paymentId = paymentResponse.body.data.id;

        const processResponse = await request(BASE_URL)
          .post(`/api/samples/payment/${paymentId}/process`)
          .send({
            paymentMethod: 'bank_transfer',
            transactionData: {
              accountName: 'Test Account',
              iban: 'TR12 3456 7890 1234 5678 9012 34',
              reference: 'Numune sample-2'
            }
          });

        expect(processResponse.status).toBe(200);
        expect(processResponse.body.success).toBe(true);
      }
    });
  });

  describe('Notification System', () => {
    test('should send sample approval notification', async () => {
      const response = await request(BASE_URL)
        .post('/api/notifications/sample-approval')
        .send({
          sampleRequestId: 'sample-test-1',
          customerId: '1',
          approved: true,
          shippingCost: 25
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('notification sent successfully');
    });

    test('should send payment completed notification', async () => {
      const response = await request(BASE_URL)
        .post('/api/notifications/payment-completed')
        .send({
          sampleRequestId: 'sample-test-1',
          customerId: '1',
          producerId: '1',
          amount: 25
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('should get user notifications', async () => {
      const response = await request(BASE_URL)
        .get('/api/notifications/user/1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    test('should get notification statistics', async () => {
      const response = await request(BASE_URL)
        .get('/api/notifications/stats/1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.total).toBeDefined();
      expect(response.body.data.unread).toBeDefined();
      expect(response.body.data.byType).toBeDefined();
    });
  });

  describe('Customer Sample Requests', () => {
    test('should get customer sample requests', async () => {
      const response = await request(BASE_URL)
        .get('/api/samples/customer/1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    test('should get customer payments', async () => {
      const response = await request(BASE_URL)
        .get('/api/samples/payments/customer/1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('Producer Sample Management', () => {
    test('should get producer sample requests', async () => {
      const response = await request(BASE_URL)
        .get('/api/samples/producer/1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    test('should filter producer requests by status', async () => {
      const response = await request(BASE_URL)
        .get('/api/samples/producer/1?status=pending');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      
      response.body.data.forEach((request: any) => {
        expect(request.status).toBe('pending');
      });
    });

    test('should update sample status', async () => {
      const response = await request(BASE_URL)
        .put('/api/samples/producer/sample-2/status')
        .send({
          status: 'shipped',
          notes: 'Numune gönderildi',
          shippingInfo: {
            carrier: 'Aras Kargo',
            trackingNumber: 'AR123456789',
            estimatedDelivery: '2025-07-10'
          }
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('shipped');
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid sample request ID', async () => {
      const response = await request(BASE_URL)
        .put('/api/samples/producer/invalid-id/approve')
        .send({
          approved: true,
          notes: 'Test'
        });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    test('should handle invalid payment ID', async () => {
      const response = await request(BASE_URL)
        .post('/api/samples/payment/invalid-id/process')
        .send({
          paymentMethod: 'credit_card',
          transactionData: {}
        });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });
});

describe('Complete Sample Payment Workflow', () => {
  test('should complete full sample request to payment workflow', async () => {
    // 1. Producer approves with shipping cost
    const approvalResponse = await request(BASE_URL)
      .put('/api/samples/producer/sample-1/approve')
      .send({
        approved: true,
        notes: 'Workflow test',
        preparationDays: 3,
        shippingCost: 30,
        carrier: 'aras'
      });

    expect(approvalResponse.status).toBe(200);
    expect(approvalResponse.body.data.status).toBe('approved_pending_payment');

    // 2. Get payment info
    const paymentInfoResponse = await request(BASE_URL)
      .get('/api/samples/payment/sample-1');

    if (paymentInfoResponse.status === 200) {
      const payment = paymentInfoResponse.body.data;
      expect(payment.amount).toBe(30);
      expect(payment.status).toBe('pending');

      // 3. Process payment
      const paymentResponse = await request(BASE_URL)
        .post(`/api/samples/payment/${payment.id}/process`)
        .send({
          paymentMethod: 'credit_card',
          transactionData: {
            cardNumber: '****************',
            expiryDate: '12/25',
            cvv: '123',
            cardholderName: 'Test User'
          }
        });

      expect(paymentResponse.status).toBe(200);
      expect(paymentResponse.body.data.payment.status).toBe('completed');
      expect(paymentResponse.body.data.sampleRequest.status).toBe('approved');
    }
  });
});

console.log('🧪 Sample Payment System Tests');
console.log('📋 Test Coverage:');
console.log('  ✅ Shipping Cost Calculation');
console.log('  ✅ Producer Approval with Payment');
console.log('  ✅ Payment Processing (Credit Card, Bank Transfer)');
console.log('  ✅ Notification System');
console.log('  ✅ Customer & Producer APIs');
console.log('  ✅ Error Handling');
console.log('  ✅ Complete Workflow Integration');
