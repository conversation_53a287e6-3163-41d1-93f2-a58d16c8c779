'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  HeartIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { QuoteRequestModal } from '@/components/ui/quote-request-modal';
import { Product3DViewerModal } from '@/components/ui/3d-viewer-modal';

interface FavoriteProduct {
  id: string;
  name: string;
  category: string;
  supplier: string;
  image: string;
  addedDate: Date;
  lastPriceUpdate: Date;
  priceRange?: {
    min: number;
    max: number;
    currency: string;
  };
}

interface FavoritesPageProps {
  onNavigate?: (route: string) => void;
}

const FavoritesPage: React.FC<FavoritesPageProps> = ({ onNavigate }) => {
  const [favorites, setFavorites] = useState<FavoriteProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [layout, setLayout] = useState<'grid' | 'list'>('grid');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    category: '',
    supplier: '',
    priceRange: { min: 0, max: 10000 }
  });
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);
  const [is3DViewerOpen, setIs3DViewerOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<FavoriteProduct | null>(null);

  // Gerçek veriler API'den gelecek - şimdilik boş array
  const mockFavorites: FavoriteProduct[] = [];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setFavorites(mockFavorites);
      setLoading(false);
    }, 1000);
  }, []);

  const handleRemoveFavorite = (productId: string) => {
    setFavorites(prev => prev.filter(item => item.id !== productId));
    setSelectedItems(prev => prev.filter(id => id !== productId));
  };

  const handleBulkRemove = () => {
    setFavorites(prev => prev.filter(item => !selectedItems.includes(item.id)));
    setSelectedItems([]);
  };

  const handleSelectItem = (productId: string) => {
    setSelectedItems(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleBulkQuoteRequest = () => {
    const selectedProducts = favorites.filter(item => selectedItems.includes(item.id));
    console.log('Bulk quote request for:', selectedProducts);
    // Navigate to quote request page with selected products
    if (onNavigate) {
      onNavigate('/customer/requests/new?products=' + selectedItems.join(','));
    }
  };

  const handleRequestQuote = (productId: string) => {
    console.log('Request quote for product:', productId);
    const product = favorites.find(p => p.id === productId);
    if (product) {
      setSelectedProduct(product);
      setIsQuoteModalOpen(true);
    }
  };

  const handleView3D = (productId: string) => {
    console.log('View 3D for product:', productId);
    const product = favorites.find(p => p.id === productId);
    if (product) {
      setSelectedProduct(product);
      setIs3DViewerOpen(true);
    }
  };

  const filteredFavorites = favorites.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.supplier.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !filters.category || item.category === filters.category;
    const matchesSupplier = !filters.supplier || item.supplier === filters.supplier;
    
    return matchesSearch && matchesCategory && matchesSupplier;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <HeartSolidIcon className="h-8 w-8 text-red-500 mr-3" />
              Favorilerim
            </h1>
            <p className="text-gray-600 mt-2">
              {favorites.length} favori ürün • {selectedItems.length} seçili
            </p>
          </div>
          
          {/* Layout Toggle */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setLayout('grid')}
              className={`p-2 rounded-lg ${layout === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
            >
              <Squares2X2Icon className="h-5 w-5" />
            </button>
            <button
              onClick={() => setLayout('list')}
              className={`p-2 rounded-lg ${layout === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
            >
              <ListBulletIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Ürün veya üretici ara..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Category Filter */}
          <select
            value={filters.category}
            onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Tüm Kategoriler</option>
            <option value="Mermer">Mermer</option>
            <option value="Granit">Granit</option>
            <option value="Traverten">Traverten</option>
            <option value="Oniks">Oniks</option>
          </select>

          {/* Supplier Filter */}
          <select
            value={filters.supplier}
            onChange={(e) => setFilters(prev => ({ ...prev, supplier: e.target.value }))}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Tüm Üreticiler</option>
            <option value="ABC Mermer Ltd.">ABC Mermer Ltd.</option>
            <option value="XYZ Granit A.Ş.">XYZ Granit A.Ş.</option>
            <option value="DEF Traverten">DEF Traverten</option>
          </select>
        </div>

        {/* Bulk Actions */}
        {selectedItems.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200"
          >
            <div className="flex items-center justify-between">
              <span className="text-blue-800 font-medium">
                {selectedItems.length} ürün seçildi
              </span>
              <div className="flex space-x-2">
                <button
                  onClick={handleBulkQuoteRequest}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <PlusIcon className="h-4 w-4" />
                  <span>Toplu Teklif İste</span>
                </button>
                <button
                  onClick={handleBulkRemove}
                  className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
                >
                  <TrashIcon className="h-4 w-4" />
                  <span>Kaldır</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Products Grid/List */}
      {filteredFavorites.length > 0 ? (
        <div className={layout === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
          : 'space-y-4'
        }>
          {filteredFavorites.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200 ${
                layout === 'list' ? 'flex' : ''
              }`}
            >
              {/* Checkbox */}
              <div className="absolute top-3 left-3 z-10">
                <input
                  type="checkbox"
                  checked={selectedItems.includes(product.id)}
                  onChange={() => handleSelectItem(product.id)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>

              {/* Product Image */}
              <div className={`relative ${layout === 'list' ? 'w-48 h-32' : 'h-48'} bg-gray-200`}>
                <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                  <span className="text-sm">Ürün Resmi</span>
                </div>
                
                {/* Remove Button */}
                <button
                  onClick={() => handleRemoveFavorite(product.id)}
                  className="absolute top-3 right-3 p-1 bg-white rounded-full shadow-md hover:bg-red-50 transition-colors"
                >
                  <HeartSolidIcon className="h-5 w-5 text-red-500" />
                </button>
              </div>

              {/* Product Info */}
              <div className="p-4 flex-1">
                <h3 className="font-semibold text-gray-900 mb-1">{product.name}</h3>
                <p className="text-sm text-gray-600 mb-2">{product.category}</p>
                <p className="text-sm text-blue-600 mb-3">{product.supplier}</p>

                <div className="text-xs text-gray-500 mb-4">
                  <p>Eklenme: {product.addedDate.toLocaleDateString('tr-TR')}</p>
                  <p>Son Güncelleme: {product.lastPriceUpdate.toLocaleDateString('tr-TR')}</p>
                </div>

                {/* Actions */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleRequestQuote(product.id)}
                    className="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-700 transition-colors"
                  >
                    Teklif İste
                  </button>
                  <button
                    onClick={() => handleView3D(product.id)}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50 transition-colors"
                  >
                    🔄 3D
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <HeartIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchQuery || filters.category || filters.supplier 
              ? 'Arama kriterlerinize uygun favori ürün bulunamadı'
              : 'Henüz favori ürününüz yok'
            }
          </h3>
          <p className="text-gray-600 mb-6">
            {searchQuery || filters.category || filters.supplier
              ? 'Farklı filtreler deneyebilir veya arama teriminizi değiştirebilirsiniz.'
              : 'Beğendiğiniz ürünleri favorilere ekleyerek buradan kolayca erişebilirsiniz.'
            }
          </p>
          <button
            onClick={() => onNavigate?.('/products')}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Ürünleri Keşfet
          </button>
        </div>
      )}

      {/* 3D Viewer Modal */}
      <Product3DViewerModal
        isOpen={is3DViewerOpen}
        onClose={() => setIs3DViewerOpen(false)}
        product={selectedProduct ? {
          id: selectedProduct.id,
          name: selectedProduct.name,
          category: selectedProduct.category,
          image: selectedProduct.image
        } : null}
      />

      {/* Quote Request Modal */}
      <QuoteRequestModal
        isOpen={isQuoteModalOpen}
        onClose={() => setIsQuoteModalOpen(false)}
        product={selectedProduct ? {
          id: selectedProduct.id,
          name: selectedProduct.name,
          category: selectedProduct.category,
          image: selectedProduct.image
        } : null}
      />
    </div>
  );
};

export default FavoritesPage;
