'use client'

import * as React from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Download, 
  FileText, 
  BarChart3,
  TrendingUp,
  DollarSign,
  Package,
  Users,
  Calendar,
  CheckCircle
} from 'lucide-react'

interface DetailedReportModalProps {
  isOpen: boolean
  onClose: () => void
  onGenerateReport: (reportConfig: any) => Promise<boolean>
  analyticsData: any
}

export function DetailedReportModal({
  isOpen,
  onClose,
  onGenerateReport,
  analyticsData
}: DetailedReportModalProps) {
  const [reportConfig, setReportConfig] = React.useState({
    format: 'pdf',
    period: 'custom',
    startDate: '',
    endDate: '',
    includeCharts: true,
    includeCustomerData: true,
    includeProductData: true,
    includeFinancialData: true,
    includePredictions: false,
    reportType: 'comprehensive'
  })
  const [isLoading, setIsLoading] = React.useState(false)
  const [step, setStep] = React.useState(1) // 1: Configuration, 2: Preview, 3: Generation

  React.useEffect(() => {
    if (isOpen) {
      // Set default dates
      const endDate = new Date()
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - 6)
      
      setReportConfig(prev => ({
        ...prev,
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0]
      }))
      setStep(1)
    }
  }, [isOpen])

  const handleGenerateReport = async () => {
    setIsLoading(true)
    try {
      const success = await onGenerateReport(reportConfig)
      if (success) {
        setStep(3)
        setTimeout(() => {
          onClose()
        }, 2000)
      }
    } catch (error) {
      console.error('Error generating report:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getReportSections = () => {
    const sections = []
    if (reportConfig.includeFinancialData) sections.push('Mali Veriler')
    if (reportConfig.includeCustomerData) sections.push('Müşteri Analizleri')
    if (reportConfig.includeProductData) sections.push('Ürün Performansı')
    if (reportConfig.includeCharts) sections.push('Grafikler ve Tablolar')
    if (reportConfig.includePredictions) sections.push('Tahmin Modelleri')
    return sections
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Detaylı Analiz Raporu Oluştur
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step 1: Configuration */}
          {step === 1 && (
            <>
              {/* Report Type */}
              <Card>
                <CardHeader>
                  <CardTitle>Rapor Türü</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="reportType">Rapor Kapsamı</Label>
                    <Select 
                      value={reportConfig.reportType} 
                      onValueChange={(value) => setReportConfig(prev => ({ ...prev, reportType: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="comprehensive">Kapsamlı Rapor</SelectItem>
                        <SelectItem value="financial">Mali Rapor</SelectItem>
                        <SelectItem value="customer">Müşteri Analizi</SelectItem>
                        <SelectItem value="product">Ürün Performansı</SelectItem>
                        <SelectItem value="summary">Özet Rapor</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="format">Dosya Formatı</Label>
                      <Select 
                        value={reportConfig.format} 
                        onValueChange={(value) => setReportConfig(prev => ({ ...prev, format: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pdf">PDF</SelectItem>
                          <SelectItem value="excel">Excel</SelectItem>
                          <SelectItem value="word">Word</SelectItem>
                          <SelectItem value="powerpoint">PowerPoint</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="period">Dönem</Label>
                      <Select 
                        value={reportConfig.period} 
                        onValueChange={(value) => setReportConfig(prev => ({ ...prev, period: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1month">Son 1 Ay</SelectItem>
                          <SelectItem value="3months">Son 3 Ay</SelectItem>
                          <SelectItem value="6months">Son 6 Ay</SelectItem>
                          <SelectItem value="1year">Son 1 Yıl</SelectItem>
                          <SelectItem value="custom">Özel Tarih Aralığı</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {reportConfig.period === 'custom' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="startDate">Başlangıç Tarihi</Label>
                        <input
                          id="startDate"
                          type="date"
                          value={reportConfig.startDate}
                          onChange={(e) => setReportConfig(prev => ({ ...prev, startDate: e.target.value }))}
                          className="w-full p-2 border rounded-md"
                        />
                      </div>
                      <div>
                        <Label htmlFor="endDate">Bitiş Tarihi</Label>
                        <input
                          id="endDate"
                          type="date"
                          value={reportConfig.endDate}
                          onChange={(e) => setReportConfig(prev => ({ ...prev, endDate: e.target.value }))}
                          className="w-full p-2 border rounded-md"
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Report Sections */}
              <Card>
                <CardHeader>
                  <CardTitle>Rapor İçeriği</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeFinancialData"
                        checked={reportConfig.includeFinancialData}
                        onCheckedChange={(checked) => 
                          setReportConfig(prev => ({ ...prev, includeFinancialData: checked as boolean }))
                        }
                      />
                      <Label htmlFor="includeFinancialData" className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4" />
                        Mali Veriler
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeCustomerData"
                        checked={reportConfig.includeCustomerData}
                        onCheckedChange={(checked) => 
                          setReportConfig(prev => ({ ...prev, includeCustomerData: checked as boolean }))
                        }
                      />
                      <Label htmlFor="includeCustomerData" className="flex items-center gap-2">
                        <Users className="w-4 h-4" />
                        Müşteri Analizleri
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeProductData"
                        checked={reportConfig.includeProductData}
                        onCheckedChange={(checked) => 
                          setReportConfig(prev => ({ ...prev, includeProductData: checked as boolean }))
                        }
                      />
                      <Label htmlFor="includeProductData" className="flex items-center gap-2">
                        <Package className="w-4 h-4" />
                        Ürün Performansı
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeCharts"
                        checked={reportConfig.includeCharts}
                        onCheckedChange={(checked) => 
                          setReportConfig(prev => ({ ...prev, includeCharts: checked as boolean }))
                        }
                      />
                      <Label htmlFor="includeCharts" className="flex items-center gap-2">
                        <BarChart3 className="w-4 h-4" />
                        Grafikler ve Tablolar
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includePredictions"
                        checked={reportConfig.includePredictions}
                        onCheckedChange={(checked) => 
                          setReportConfig(prev => ({ ...prev, includePredictions: checked as boolean }))
                        }
                      />
                      <Label htmlFor="includePredictions" className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4" />
                        Tahmin Modelleri
                      </Label>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={onClose}>
                  İptal
                </Button>
                <Button onClick={() => setStep(2)}>
                  Önizleme
                </Button>
              </div>
            </>
          )}

          {/* Step 2: Preview */}
          {step === 2 && (
            <>
              <Card className="bg-blue-50 border-blue-200">
                <CardHeader>
                  <CardTitle className="text-blue-800">Rapor Önizleme</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-blue-700">Rapor Türü:</span>
                      <p className="text-blue-900">{reportConfig.reportType}</p>
                    </div>
                    <div>
                      <span className="font-medium text-blue-700">Format:</span>
                      <p className="text-blue-900">{reportConfig.format.toUpperCase()}</p>
                    </div>
                    <div>
                      <span className="font-medium text-blue-700">Dönem:</span>
                      <p className="text-blue-900">
                        {reportConfig.period === 'custom' 
                          ? `${formatDate(reportConfig.startDate)} - ${formatDate(reportConfig.endDate)}`
                          : reportConfig.period
                        }
                      </p>
                    </div>
                    <div>
                      <span className="font-medium text-blue-700">Bölümler:</span>
                      <p className="text-blue-900">{getReportSections().length} bölüm</p>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium text-blue-700">Dahil Edilen Bölümler:</span>
                    <ul className="list-disc list-inside mt-1 text-blue-900">
                      {getReportSections().map((section, index) => (
                        <li key={index}>{section}</li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>
                  Geri Dön
                </Button>
                <Button 
                  onClick={handleGenerateReport}
                  disabled={isLoading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Oluşturuluyor...
                    </>
                  ) : (
                    <>
                      <Download className="w-4 h-4 mr-2" />
                      Raporu Oluştur
                    </>
                  )}
                </Button>
              </div>
            </>
          )}

          {/* Step 3: Success */}
          {step === 3 && (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-green-800 mb-2">
                Rapor Başarıyla Oluşturuldu!
              </h3>
              <p className="text-green-600 mb-4">
                Detaylı analiz raporu {reportConfig.format.toUpperCase()} formatında indirildi.
              </p>
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <p className="text-sm text-green-700">
                  <strong>Dosya:</strong> analiz-raporu-{new Date().toISOString().split('T')[0]}.{reportConfig.format}<br />
                  <strong>Boyut:</strong> ~{Math.floor(Math.random() * 5 + 2)} MB<br />
                  <strong>Bölümler:</strong> {getReportSections().length} bölüm
                </p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
