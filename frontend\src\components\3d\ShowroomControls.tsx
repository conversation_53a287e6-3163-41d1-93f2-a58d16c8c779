'use client';

import React, { useState } from 'react';
import { 
  HomeIcon,
  CubeIcon,
  PlusIcon,
  TrashIcon,
  ArrowsPointingOutIcon,
  Cog6ToothIcon,
  EyeIcon,
  SwatchIcon
} from '@heroicons/react/24/outline';

interface Product {
  id: string;
  name: string;
  texture?: string;
  color?: string;
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
  position: [number, number, number];
  rotation: [number, number, number];
}

interface Room {
  type: 'bathroom' | 'kitchen' | 'living' | 'bedroom' | 'outdoor';
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
}

interface ShowroomControlsProps {
  selectedProducts: Product[];
  selectedRoom: Room;
  availableProducts: Product[];
  onRoomChange: (room: Room) => void;
  onProductAdd: (product: Product) => void;
  onProductRemove: (productId: string) => void;
  onProductUpdate: (productId: string, updates: Partial<Product>) => void;
  onClearAll: () => void;
  onSaveDesign: () => void;
  onLoadDesign: (design: any) => void;
}

const ShowroomControls: React.FC<ShowroomControlsProps> = ({
  selectedProducts,
  selectedRoom,
  availableProducts,
  onRoomChange,
  onProductAdd,
  onProductRemove,
  onProductUpdate,
  onClearAll,
  onSaveDesign,
  onLoadDesign
}) => {
  const [activeTab, setActiveTab] = useState<'room' | 'products' | 'settings'>('room');
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);

  const roomTypes = [
    { type: 'bathroom' as const, name: 'Banyo', icon: '🛁', dimensions: { width: 4, height: 3, depth: 3 } },
    { type: 'kitchen' as const, name: 'Mutfak', icon: '🍳', dimensions: { width: 6, height: 3, depth: 4 } },
    { type: 'living' as const, name: 'Salon', icon: '🛋️', dimensions: { width: 8, height: 3, depth: 6 } },
    { type: 'bedroom' as const, name: 'Yatak Odası', icon: '🛏️', dimensions: { width: 5, height: 3, depth: 4 } },
    { type: 'outdoor' as const, name: 'Dış Mekan', icon: '🌿', dimensions: { width: 10, height: 3, depth: 8 } },
  ];

  const stoneTextures = [
    { name: 'Mermer Beyaz', color: '#f8f8ff', texture: '/textures/marble-white.jpg' },
    { name: 'Granit Siyah', color: '#2c2c2c', texture: '/textures/granite-black.jpg' },
    { name: 'Traverten', color: '#d2b48c', texture: '/textures/travertine.jpg' },
    { name: 'Oniks Yeşil', color: '#355e3b', texture: '/textures/onyx-green.jpg' },
    { name: 'Kuvars Beyaz', color: '#ffffff', texture: '/textures/quartz-white.jpg' },
  ];

  const handleRoomSelect = (roomType: typeof roomTypes[0]) => {
    onRoomChange({
      type: roomType.type,
      dimensions: roomType.dimensions
    });
  };

  const handleProductAdd = (baseProduct: Product) => {
    const newProduct: Product = {
      ...baseProduct,
      id: `${baseProduct.id}-${Date.now()}`,
      position: [0, baseProduct.dimensions.height / 2, 0],
      rotation: [0, 0, 0]
    };
    onProductAdd(newProduct);
  };

  const handleProductUpdate = (productId: string, field: string, value: any) => {
    const updates: Partial<Product> = { [field]: value };
    onProductUpdate(productId, updates);
  };

  const selectedProduct = selectedProducts.find(p => p.id === selectedProductId);

  return (
    <div className="w-80 bg-white border-l border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">3D Showroom</h2>
        <p className="text-sm text-gray-600">Tasarımınızı oluşturun</p>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => setActiveTab('room')}
          className={`flex-1 py-3 px-4 text-sm font-medium ${
            activeTab === 'room'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <HomeIcon className="h-4 w-4 inline mr-1" />
          Oda
        </button>
        <button
          onClick={() => setActiveTab('products')}
          className={`flex-1 py-3 px-4 text-sm font-medium ${
            activeTab === 'products'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <CubeIcon className="h-4 w-4 inline mr-1" />
          Ürünler
        </button>
        <button
          onClick={() => setActiveTab('settings')}
          className={`flex-1 py-3 px-4 text-sm font-medium ${
            activeTab === 'settings'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <Cog6ToothIcon className="h-4 w-4 inline mr-1" />
          Ayarlar
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Room Tab */}
        {activeTab === 'room' && (
          <div className="p-4 space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Oda Tipi Seçin</h3>
              <div className="grid grid-cols-1 gap-2">
                {roomTypes.map((room) => (
                  <button
                    key={room.type}
                    onClick={() => handleRoomSelect(room)}
                    className={`p-3 rounded-lg border text-left transition-colors ${
                      selectedRoom.type === room.type
                        ? 'border-blue-500 bg-blue-50 text-blue-900'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{room.icon}</span>
                      <div>
                        <div className="font-medium">{room.name}</div>
                        <div className="text-xs text-gray-500">
                          {room.dimensions.width}m × {room.dimensions.depth}m
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Oda Boyutları</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Genişlik:</span>
                  <span className="text-sm font-medium">{selectedRoom.dimensions.width}m</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Derinlik:</span>
                  <span className="text-sm font-medium">{selectedRoom.dimensions.depth}m</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Yükseklik:</span>
                  <span className="text-sm font-medium">{selectedRoom.dimensions.height}m</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Products Tab */}
        {activeTab === 'products' && (
          <div className="p-4 space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Ürün Ekle</h3>
              <div className="space-y-2">
                {availableProducts.map((product) => (
                  <div
                    key={product.id}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-gray-300"
                  >
                    <div className="flex items-center space-x-3">
                      <div 
                        className="w-8 h-8 rounded border"
                        style={{ backgroundColor: product.color || '#8B7355' }}
                      />
                      <div>
                        <div className="text-sm font-medium">{product.name}</div>
                        <div className="text-xs text-gray-500">
                          {product.dimensions.width}×{product.dimensions.height}×{product.dimensions.depth}cm
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => handleProductAdd(product)}
                      className="p-1 text-blue-600 hover:text-blue-800"
                    >
                      <PlusIcon className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">
                Sahnedeki Ürünler ({selectedProducts.length})
              </h3>
              {selectedProducts.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">
                  Henüz ürün eklenmedi
                </p>
              ) : (
                <div className="space-y-2">
                  {selectedProducts.map((product) => (
                    <div
                      key={product.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedProductId === product.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedProductId(product.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-6 h-6 rounded border"
                            style={{ backgroundColor: product.color || '#8B7355' }}
                          />
                          <span className="text-sm font-medium">{product.name}</span>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onProductRemove(product.id);
                          }}
                          className="p-1 text-red-600 hover:text-red-800"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Product Editor */}
            {selectedProduct && (
              <div className="border-t pt-4">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Ürün Düzenle</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Renk/Doku
                    </label>
                    <div className="grid grid-cols-3 gap-2">
                      {stoneTextures.map((texture) => (
                        <button
                          key={texture.name}
                          onClick={() => handleProductUpdate(selectedProduct.id, 'color', texture.color)}
                          className={`p-2 rounded border text-xs ${
                            selectedProduct.color === texture.color
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div 
                            className="w-full h-6 rounded mb-1"
                            style={{ backgroundColor: texture.color }}
                          />
                          {texture.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Pozisyon
                    </label>
                    <div className="grid grid-cols-3 gap-2">
                      <div>
                        <label className="block text-xs text-gray-500">X</label>
                        <input
                          type="number"
                          step="0.1"
                          value={selectedProduct.position[0]}
                          onChange={(e) => {
                            const newPosition: [number, number, number] = [
                              parseFloat(e.target.value),
                              selectedProduct.position[1],
                              selectedProduct.position[2]
                            ];
                            handleProductUpdate(selectedProduct.id, 'position', newPosition);
                          }}
                          className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500">Y</label>
                        <input
                          type="number"
                          step="0.1"
                          value={selectedProduct.position[1]}
                          onChange={(e) => {
                            const newPosition: [number, number, number] = [
                              selectedProduct.position[0],
                              parseFloat(e.target.value),
                              selectedProduct.position[2]
                            ];
                            handleProductUpdate(selectedProduct.id, 'position', newPosition);
                          }}
                          className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500">Z</label>
                        <input
                          type="number"
                          step="0.1"
                          value={selectedProduct.position[2]}
                          onChange={(e) => {
                            const newPosition: [number, number, number] = [
                              selectedProduct.position[0],
                              selectedProduct.position[1],
                              parseFloat(e.target.value)
                            ];
                            handleProductUpdate(selectedProduct.id, 'position', newPosition);
                          }}
                          className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <div className="p-4 space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Tasarım İşlemleri</h3>
              <div className="space-y-2">
                <button
                  onClick={onSaveDesign}
                  className="w-full py-2 px-3 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700"
                >
                  Tasarımı Kaydet
                </button>
                <button
                  onClick={onClearAll}
                  className="w-full py-2 px-3 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700"
                >
                  Tümünü Temizle
                </button>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Görünüm Ayarları</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Gölgeler</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Yansımalar</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Çevre Aydınlatması</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">İstatistikler</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>Toplam Ürün:</span>
                  <span>{selectedProducts.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Oda Alanı:</span>
                  <span>{selectedRoom.dimensions.width * selectedRoom.dimensions.depth}m²</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ShowroomControls;
