"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "./button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "./card"
import { Badge } from "./badge"
import { 
  X, 
  CheckCircle,
  Clock,
  Package,
  Truck,
  CreditCard,
  AlertTriangle,
  XCircle,
  Edit,
  Save,
  Calendar,
  DollarSign,
  MessageSquare,
  FileText
} from "lucide-react"
import { Order, OrderStatus } from "@/types/orders"

interface OrderStatusUpdateModalProps {
  isOpen: boolean
  onClose: () => void
  order: Order | null
  onStatusUpdate: (orderId: string, newStatus: OrderStatus, note?: string) => void
}

export function OrderStatusUpdateModal({ 
  isOpen, 
  onClose, 
  order,
  onStatusUpdate
}: OrderStatusUpdateModalProps) {
  const [selectedStatus, setSelectedStatus] = React.useState<OrderStatus>('pending-payment')
  const [updateNote, setUpdateNote] = React.useState('')
  const [estimatedDate, setEstimatedDate] = React.useState('')
  const [trackingNumber, setTrackingNumber] = React.useState('')
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  React.useEffect(() => {
    if (order) {
      setSelectedStatus(order.status)
      setUpdateNote('')
      setEstimatedDate(order.estimatedCompletionDate ? new Date(order.estimatedCompletionDate).toISOString().split('T')[0] : '')
      setTrackingNumber('')
    }
  }, [order])

  if (!isOpen || !order) return null

  const statusOptions = [
    { 
      value: 'pending-payment', 
      label: 'Ön Ödeme Bekliyor', 
      color: 'bg-yellow-100 text-yellow-800',
      icon: Clock,
      description: 'Müşteriden ön ödeme bekleniyor'
    },
    { 
      value: 'payment-confirmed', 
      label: 'Ödeme Onaylandı', 
      color: 'bg-blue-100 text-blue-800',
      icon: CreditCard,
      description: 'Ödeme alındı, üretime hazır'
    },
    { 
      value: 'in-production', 
      label: 'Üretimde', 
      color: 'bg-orange-100 text-orange-800',
      icon: Package,
      description: 'Ürün üretim aşamasında'
    },
    { 
      value: 'ready-for-shipment', 
      label: 'Sevkiyat Hazır', 
      color: 'bg-purple-100 text-purple-800',
      icon: Truck,
      description: 'Ürün hazır, sevkiyat bekliyor'
    },
    { 
      value: 'shipped', 
      label: 'Kargoya Verildi', 
      color: 'bg-indigo-100 text-indigo-800',
      icon: Truck,
      description: 'Ürün kargoya verildi'
    },
    { 
      value: 'delivered', 
      label: 'Teslim Edildi', 
      color: 'bg-green-100 text-green-800',
      icon: CheckCircle,
      description: 'Ürün müşteriye teslim edildi'
    },
    { 
      value: 'completed', 
      label: 'Tamamlandı', 
      color: 'bg-gray-100 text-gray-800',
      icon: CheckCircle,
      description: 'Sipariş tamamen tamamlandı'
    },
    { 
      value: 'cancelled', 
      label: 'İptal Edildi', 
      color: 'bg-red-100 text-red-800',
      icon: XCircle,
      description: 'Sipariş iptal edildi'
    },
    { 
      value: 'refunded', 
      label: 'İade Edildi', 
      color: 'bg-red-100 text-red-800',
      icon: AlertTriangle,
      description: 'Ödeme iade edildi'
    }
  ] as const

  const currentStatusConfig = statusOptions.find(s => s.value === order.status)
  const selectedStatusConfig = statusOptions.find(s => s.value === selectedStatus)

  const handleSubmit = async () => {
    if (!selectedStatusConfig) return

    setIsSubmitting(true)
    try {
      // Simulated API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      onStatusUpdate(order.id, selectedStatus, updateNote)
      onClose()
    } catch (error) {
      console.error('Status update failed:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStatusFlow = () => {
    const statusFlow = [
      'pending-payment',
      'payment-confirmed', 
      'in-production',
      'ready-for-shipment',
      'shipped',
      'delivered',
      'completed'
    ]
    
    const currentIndex = statusFlow.indexOf(order.status)
    const selectedIndex = statusFlow.indexOf(selectedStatus)
    
    return { currentIndex, selectedIndex, statusFlow }
  }

  const { currentIndex, selectedIndex, statusFlow } = getStatusFlow()

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Edit className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Sipariş Durumu Güncelle</h2>
              <p className="text-gray-600">
                {order.orderNumber} - {order.customer.companyName}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {currentStatusConfig && (
              <Badge className={currentStatusConfig.color}>
                Mevcut: {currentStatusConfig.label}
              </Badge>
            )}
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-6">
            {/* Status Flow Visualization */}
            <Card>
              <CardHeader>
                <CardTitle>Sipariş Süreci</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  {statusFlow.map((status, index) => {
                    const config = statusOptions.find(s => s.value === status)
                    if (!config) return null
                    
                    const Icon = config.icon
                    const isCompleted = index <= currentIndex
                    const isSelected = status === selectedStatus
                    const isCurrent = status === order.status
                    
                    return (
                      <div key={status} className="flex flex-col items-center">
                        <div className={`
                          w-12 h-12 rounded-full flex items-center justify-center border-2 mb-2
                          ${isCurrent ? 'border-blue-500 bg-blue-100' : 
                            isSelected ? 'border-green-500 bg-green-100' :
                            isCompleted ? 'border-gray-400 bg-gray-100' : 'border-gray-300 bg-white'}
                        `}>
                          <Icon className={`w-6 h-6 ${
                            isCurrent ? 'text-blue-600' :
                            isSelected ? 'text-green-600' :
                            isCompleted ? 'text-gray-600' : 'text-gray-400'
                          }`} />
                        </div>
                        <span className={`text-xs text-center max-w-20 ${
                          isCurrent ? 'font-bold text-blue-600' :
                          isSelected ? 'font-bold text-green-600' :
                          'text-gray-600'
                        }`}>
                          {config.label}
                        </span>
                        {index < statusFlow.length - 1 && (
                          <div className={`w-8 h-0.5 mt-6 ${
                            index < currentIndex ? 'bg-gray-400' : 'bg-gray-300'
                          }`} />
                        )}
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Status Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Yeni Durum Seçin</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {statusOptions.map((status) => {
                    const Icon = status.icon
                    const isSelected = selectedStatus === status.value
                    const isCurrent = order.status === status.value
                    
                    return (
                      <button
                        key={status.value}
                        onClick={() => setSelectedStatus(status.value)}
                        disabled={isCurrent}
                        className={`
                          p-4 rounded-lg border-2 text-left transition-all
                          ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}
                          ${isCurrent ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                        `}
                      >
                        <div className="flex items-center gap-3 mb-2">
                          <Icon className={`w-5 h-5 ${isSelected ? 'text-blue-600' : 'text-gray-600'}`} />
                          <span className={`font-medium ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>
                            {status.label}
                          </span>
                          {isCurrent && <Badge variant="outline">Mevcut</Badge>}
                        </div>
                        <p className="text-sm text-gray-600">{status.description}</p>
                      </button>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Additional Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Update Note */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="w-5 h-5" />
                    Güncelleme Notu
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <textarea
                    value={updateNote}
                    onChange={(e) => setUpdateNote(e.target.value)}
                    placeholder="Durum değişikliği hakkında not ekleyin..."
                    className="w-full h-24 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </CardContent>
              </Card>

              {/* Additional Fields */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Ek Bilgiler
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {(selectedStatus === 'shipped' || selectedStatus === 'delivered') && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Kargo Takip Numarası
                      </label>
                      <input
                        type="text"
                        value={trackingNumber}
                        onChange={(e) => setTrackingNumber(e.target.value)}
                        placeholder="Takip numarası girin"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  )}
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tahmini Teslim Tarihi
                    </label>
                    <input
                      type="date"
                      value={estimatedDate}
                      onChange={(e) => setEstimatedDate(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-6 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              {selectedStatusConfig && (
                <div className="flex items-center gap-2">
                  <span>Yeni durum:</span>
                  <Badge className={selectedStatusConfig.color}>
                    {selectedStatusConfig.label}
                  </Badge>
                </div>
              )}
            </div>
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
                İptal
              </Button>
              <Button 
                onClick={handleSubmit} 
                disabled={isSubmitting || selectedStatus === order.status}
              >
                {isSubmitting ? (
                  <>
                    <Clock className="w-4 h-4 mr-2 animate-spin" />
                    Güncelleniyor...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Durumu Güncelle
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
