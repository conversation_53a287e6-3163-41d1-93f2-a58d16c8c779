import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { PrismaClient, NotificationType } from '@prisma/client';
import { EmailService } from './EmailService';
import { createEmailConfig } from '../config/email';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

export interface NotificationData {
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  relatedEntityType?: string;
  relatedEntityId?: string;
  data?: any;
  sendEmail?: boolean;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

export interface NotificationPreferences {
  emailEnabled: boolean;
  browserEnabled: boolean;
  escrowNotifications: boolean;
  orderNotifications: boolean;
  quoteNotifications: boolean;
  systemNotifications: boolean;
  marketingEmails: boolean;
}

export interface SocketUser {
  userId: string;
  userType: string;
  socketId: string;
  connectedAt: Date;
}

export class NotificationService {
  private io: SocketIOServer;
  private emailService: EmailService;
  private connectedUsers: Map<string, SocketUser[]> = new Map();
  private userSockets: Map<string, string[]> = new Map();

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    const emailConfig = createEmailConfig();
    this.emailService = new EmailService(emailConfig);

    this.setupSocketHandlers();
  }

  /**
   * Setup Socket.IO event handlers
   */
  private setupSocketHandlers(): void {
    this.io.use(this.authenticateSocket.bind(this));

    this.io.on('connection', (socket) => {
      const user = (socket as any).user;
      console.log(`User ${user.id} connected via socket ${socket.id}`);

      // Add user to connected users
      this.addConnectedUser(user.id, {
        userId: user.id,
        userType: user.userType,
        socketId: socket.id,
        connectedAt: new Date(),
      });

      // Join user to their personal room
      socket.join(`user:${user.id}`);
      socket.join(`userType:${user.userType}`);

      // Send unread notifications count
      this.sendUnreadCount(user.id);

      // Handle notification mark as read
      socket.on('markAsRead', async (notificationId: string) => {
        await this.markAsRead(notificationId, user.id);
        this.sendUnreadCount(user.id);
      });

      // Handle mark all as read
      socket.on('markAllAsRead', async () => {
        await this.markAllAsRead(user.id);
        this.sendUnreadCount(user.id);
      });

      // Handle notification preferences update
      socket.on('updatePreferences', async (preferences: NotificationPreferences) => {
        await this.updateNotificationPreferences(user.id, preferences);
        socket.emit('preferencesUpdated', { success: true });
      });

      // Handle get notifications
      socket.on('getNotifications', async (params: { page?: number; limit?: number }) => {
        const notifications = await this.getUserNotifications(user.id, params.page, params.limit);
        socket.emit('notifications', notifications);
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        console.log(`User ${user.id} disconnected from socket ${socket.id}`);
        this.removeConnectedUser(user.id, socket.id);
      });
    });
  }

  /**
   * Authenticate socket connection
   */
  private async authenticateSocket(socket: any, next: any): Promise<void> {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        return next(new Error('JWT_SECRET environment variable is required'));
      }

      const decoded = jwt.verify(token, jwtSecret) as any;
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: { id: true, email: true, userType: true, status: true }
      });

      if (!user || user.status !== 'ACTIVE') {
        return next(new Error('Invalid or inactive user'));
      }

      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  }

  /**
   * Add connected user
   */
  private addConnectedUser(userId: string, socketUser: SocketUser): void {
    if (!this.connectedUsers.has(userId)) {
      this.connectedUsers.set(userId, []);
    }
    this.connectedUsers.get(userId)!.push(socketUser);

    if (!this.userSockets.has(userId)) {
      this.userSockets.set(userId, []);
    }
    this.userSockets.get(userId)!.push(socketUser.socketId);
  }

  /**
   * Remove connected user
   */
  private removeConnectedUser(userId: string, socketId: string): void {
    const userConnections = this.connectedUsers.get(userId);
    if (userConnections) {
      const filtered = userConnections.filter(conn => conn.socketId !== socketId);
      if (filtered.length > 0) {
        this.connectedUsers.set(userId, filtered);
      } else {
        this.connectedUsers.delete(userId);
      }
    }

    const userSocketIds = this.userSockets.get(userId);
    if (userSocketIds) {
      const filtered = userSocketIds.filter(id => id !== socketId);
      if (filtered.length > 0) {
        this.userSockets.set(userId, filtered);
      } else {
        this.userSockets.delete(userId);
      }
    }
  }

  /**
   * Send notification to user
   */
  async sendNotification(notificationData: NotificationData): Promise<void> {
    try {
      // Create notification in database
      const notification = await prisma.notification.create({
        data: {
          userId: notificationData.userId,
          title: notificationData.title,
          message: notificationData.message,
          notificationType: notificationData.type,
          relatedEntityType: notificationData.relatedEntityType,
          relatedEntityId: notificationData.relatedEntityId,
          data: notificationData.data,
        },
      });

      // Send real-time notification if user is connected
      if (this.userSockets.has(notificationData.userId)) {
        this.io.to(`user:${notificationData.userId}`).emit('notification', {
          id: notification.id,
          title: notification.title,
          message: notification.message,
          type: notification.notificationType,
          priority: notificationData.priority || 'medium',
          createdAt: notification.createdAt,
          data: notification.data,
        });

        // Update unread count
        this.sendUnreadCount(notificationData.userId);
      }

      // Send email if requested and user preferences allow
      if (notificationData.sendEmail) {
        await this.sendEmailNotification(notificationData);
      }
    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(notificationData: NotificationData): Promise<void> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: notificationData.userId },
        include: { profile: true },
      });

      if (!user) return;

      // Check user preferences
      const preferences = await this.getNotificationPreferences(notificationData.userId);
      if (!preferences.emailEnabled) return;

      // Send appropriate email based on notification type
      const emailData = {
        customerName: user.profile?.companyName || user.companyName || 'Değerli Kullanıcı',
        producerName: '',
        orderNumber: '',
        amount: 0,
        currency: 'TRY',
        ...notificationData.data,
      };

      switch (notificationData.type) {
        case NotificationType.PAYMENT_RECEIVED:
          if (notificationData.data?.paymentInstructions) {
            await this.emailService.sendPaymentInstructions(user.email, emailData);
          } else if (notificationData.data?.paymentConfirmed) {
            await this.emailService.sendPaymentConfirmation(user.email, emailData);
          }
          break;
        case NotificationType.ORDER_SHIPPED:
          if (notificationData.data?.approvalRequired) {
            await this.emailService.sendGoodsReadyNotification(user.email, emailData);
          }
          break;
        case NotificationType.ORDER_DELIVERED:
          if (notificationData.data?.paid) {
            await this.emailService.sendPaymentReleaseNotification(user.email, emailData);
          }
          break;
        default:
          // Send generic notification email
          await this.emailService.sendEmail({
            to: user.email,
            subject: notificationData.title,
            html: `
              <h2>${notificationData.title}</h2>
              <p>${notificationData.message}</p>
              <p>Bu bildirim Doğal Taş Pazaryeri sisteminden gönderilmiştir.</p>
            `,
          });
      }
    } catch (error) {
      console.error('Failed to send email notification:', error);
    }
  }

  /**
   * Send unread notifications count
   */
  private async sendUnreadCount(userId: string): Promise<void> {
    try {
      const count = await prisma.notification.count({
        where: {
          userId,
          isRead: false,
        },
      });

      this.io.to(`user:${userId}`).emit('unreadCount', count);
    } catch (error) {
      console.error('Failed to send unread count:', error);
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string, userId: string): Promise<void> {
    await prisma.notification.updateMany({
      where: {
        id: notificationId,
        userId,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(userId: string): Promise<void> {
    await prisma.notification.updateMany({
      where: {
        userId,
        isRead: false,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
  }

  /**
   * Get user notifications
   */
  async getUserNotifications(userId: string, page: number = 1, limit: number = 20): Promise<any> {
    const skip = (page - 1) * limit;

    const [notifications, total] = await Promise.all([
      prisma.notification.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.notification.count({
        where: { userId },
      }),
    ]);

    return {
      notifications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get notification preferences
   */
  async getNotificationPreferences(userId: string): Promise<NotificationPreferences> {
    // This would typically come from a user preferences table
    // For now, return default preferences
    return {
      emailEnabled: true,
      browserEnabled: true,
      escrowNotifications: true,
      orderNotifications: true,
      quoteNotifications: true,
      systemNotifications: true,
      marketingEmails: false,
    };
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(userId: string, preferences: NotificationPreferences): Promise<void> {
    // This would typically update a user preferences table
    // For now, just log the update
    console.log(`Updated notification preferences for user ${userId}:`, preferences);
  }

  /**
   * Broadcast to all users of a specific type
   */
  async broadcastToUserType(userType: string, notificationData: Omit<NotificationData, 'userId'>): Promise<void> {
    const users = await prisma.user.findMany({
      where: { userType: userType as any },
      select: { id: true },
    });

    for (const user of users) {
      await this.sendNotification({
        ...notificationData,
        userId: user.id,
      });
    }
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  /**
   * Get connected users by type
   */
  getConnectedUsersByType(): Record<string, number> {
    const counts: Record<string, number> = {};

    this.connectedUsers.forEach((connections) => {
      connections.forEach((conn) => {
        counts[conn.userType] = (counts[conn.userType] || 0) + 1;
      });
    });

    return counts;
  }

  /**
   * Get Socket.IO instance for external use
   */
  getIO(): SocketIOServer {
    return this.io;
  }
}
