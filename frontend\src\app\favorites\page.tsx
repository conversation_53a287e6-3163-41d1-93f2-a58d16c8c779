"use client"

import * as React from "react"
import { Navigation } from "@/components/ui/navigation"
import { Container } from "@/components/ui/container"
import { Button } from "@/components/ui/button"
import ProductCard from "@/components/ui/product-card"
import { SkipLink } from "@/components/ui/skip-link"
import { VisuallyHidden } from "@/components/ui/visually-hidden"
import { Product3DViewerModal } from "@/components/ui/3d-viewer-modal"
import { QuoteRequestModal } from "@/components/ui/quote-request-modal"
import { FavoritesProvider, useFavorites } from "@/contexts/favorites-context"

function FavoritesContent() {
  const { favorites, removeFromFavorites, isFavorite, clearFavorites } = useFavorites()
  const [selectedProduct, setSelectedProduct] = React.useState<typeof favorites[0] | null>(null)
  const [is3DViewerOpen, setIs3DViewerOpen] = React.useState(false)
  const [isQuoteModalOpen, setIsQuoteModalOpen] = React.useState(false)

  const handleToggleFavorite = (productId: string) => {
    removeFromFavorites(productId)
    console.log(`Removed ${productId} from favorites`)
  }

  const handleView3D = (productId: string) => {
    const product = favorites.find(p => p.id === productId)
    if (product) {
      setSelectedProduct(product)
      setIs3DViewerOpen(true)
    }
  }

  const handleRequestQuote = (productId: string) => {
    // Auth kontrolü ProductCard'da yapılıyor, buraya geldiğinde zaten giriş yapmış
    const product = favorites.find(p => p.id === productId)
    if (product) {
      setSelectedProduct(product)
      setIsQuoteModalOpen(true)
    }
  }

  return (
    <div className="min-h-screen bg-[var(--background)]">
      <SkipLink />
      
      <Navigation />
      
      <main id="main-content">
        <Container size="xl" className="py-8">
          <VisuallyHidden>
            <h1>Favorilerim</h1>
          </VisuallyHidden>

          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-stone-900 mb-2">Favorilerim</h1>
                <p className="text-stone-600">
                  {favorites.length > 0 
                    ? `${favorites.length} favori ürününüz var`
                    : "Henüz favori ürününüz yok"
                  }
                </p>
              </div>
              {favorites.length > 0 && (
                <Button
                  variant="outline"
                  onClick={clearFavorites}
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  🗑️ Tümünü Temizle
                </Button>
              )}
            </div>
          </div>

          {/* Favorites Grid */}
          {favorites.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12">
              {favorites.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  isFavorite={true}
                  onRequestQuote={() => handleRequestQuote(product.id)}
                  onToggleFavorite={() => handleToggleFavorite(product.id)}
                  onView3D={() => handleView3D(product.id)}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">💔</div>
              <h2 className="text-2xl font-semibold text-stone-700 mb-4">
                Henüz favori ürününüz yok
              </h2>
              <p className="text-stone-600 mb-8 max-w-md mx-auto">
                Beğendiğiniz ürünleri favorilere ekleyerek daha sonra kolayca bulabilirsiniz.
              </p>
              <Button
                variant="primary"
                onClick={() => window.location.href = '/products'}
              >
                🛍️ Ürünleri Keşfet
              </Button>
            </div>
          )}

          {/* 3D Viewer Modal */}
          <Product3DViewerModal
            isOpen={is3DViewerOpen}
            onClose={() => setIs3DViewerOpen(false)}
            product={selectedProduct}
          />

          {/* Quote Request Modal */}
          <QuoteRequestModal
            isOpen={isQuoteModalOpen}
            onClose={() => setIsQuoteModalOpen(false)}
            product={selectedProduct}
          />
        </Container>
      </main>
    </div>
  )
}

export default function FavoritesPage() {
  return (
    <FavoritesProvider>
      <FavoritesContent />
    </FavoritesProvider>
  )
}
