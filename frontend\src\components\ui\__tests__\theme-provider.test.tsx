import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ThemeProvider, useTheme } from '../theme-provider'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
})

// Mock matchMedia
const mockMatchMedia = jest.fn()
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: mockMatchMedia,
})

// Test component that uses the theme
const TestComponent = () => {
  const { theme, setTheme, systemTheme, resolvedTheme } = useTheme()
  
  return (
    <div>
      <div data-testid="current-theme">{theme}</div>
      <div data-testid="system-theme">{systemTheme}</div>
      <div data-testid="resolved-theme">{resolvedTheme}</div>
      <button onClick={() => setTheme('light')} data-testid="set-light">
        Set Light
      </button>
      <button onClick={() => setTheme('dark')} data-testid="set-dark">
        Set Dark
      </button>
      <button onClick={() => setTheme('system')} data-testid="set-system">
        Set System
      </button>
    </div>
  )
}

describe('ThemeProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    
    // Mock matchMedia to return light theme by default
    mockMatchMedia.mockImplementation((query) => ({
      matches: query === '(prefers-color-scheme: dark)' ? false : true,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    }))
  })

  // Basic rendering tests
  describe('Rendering', () => {
    it('renders children correctly', () => {
      render(
        <ThemeProvider>
          <div data-testid="child">Child content</div>
        </ThemeProvider>
      )
      
      expect(screen.getByTestId('child')).toBeInTheDocument()
    })

    it('provides default theme context', () => {
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('system')
      expect(screen.getByTestId('system-theme')).toHaveTextContent('light')
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('light')
    })

    it('uses custom default theme', () => {
      render(
        <ThemeProvider defaultTheme="dark">
          <TestComponent />
        </ThemeProvider>
      )
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark')
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('dark')
    })
  })

  // Theme switching tests
  describe('Theme Switching', () => {
    it('switches to light theme', async () => {
      const user = userEvent.setup()
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      await user.click(screen.getByTestId('set-light'))
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('light')
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('light')
    })

    it('switches to dark theme', async () => {
      const user = userEvent.setup()
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      await user.click(screen.getByTestId('set-dark'))
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark')
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('dark')
    })

    it('switches to system theme', async () => {
      const user = userEvent.setup()
      
      render(
        <ThemeProvider defaultTheme="light">
          <TestComponent />
        </ThemeProvider>
      )
      
      await user.click(screen.getByTestId('set-system'))
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('system')
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('light') // Based on mocked system preference
    })
  })

  // System theme detection tests
  describe('System Theme Detection', () => {
    it('detects dark system preference', () => {
      mockMatchMedia.mockImplementation((query) => ({
        matches: query === '(prefers-color-scheme: dark)' ? true : false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }))
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      expect(screen.getByTestId('system-theme')).toHaveTextContent('dark')
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('dark')
    })

    it('responds to system theme changes', async () => {
      let mediaQueryCallback: ((e: MediaQueryListEvent) => void) | null = null
      
      mockMatchMedia.mockImplementation((query) => ({
        matches: query === '(prefers-color-scheme: dark)' ? false : true,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn((event, callback) => {
          if (event === 'change') {
            mediaQueryCallback = callback
          }
        }),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }))
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      expect(screen.getByTestId('system-theme')).toHaveTextContent('light')
      
      // Simulate system theme change to dark
      if (mediaQueryCallback) {
        mediaQueryCallback({ matches: true } as MediaQueryListEvent)
      }
      
      await waitFor(() => {
        expect(screen.getByTestId('system-theme')).toHaveTextContent('dark')
      })
    })
  })

  // LocalStorage persistence tests
  describe('LocalStorage Persistence', () => {
    it('saves theme to localStorage', async () => {
      const user = userEvent.setup()
      
      render(
        <ThemeProvider storageKey="test-theme">
          <TestComponent />
        </ThemeProvider>
      )
      
      await user.click(screen.getByTestId('set-dark'))
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('test-theme', 'dark')
    })

    it('loads theme from localStorage', () => {
      localStorageMock.getItem.mockReturnValue('dark')
      
      render(
        <ThemeProvider storageKey="test-theme">
          <TestComponent />
        </ThemeProvider>
      )
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('test-theme')
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark')
    })

    it('handles localStorage errors gracefully', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage not available')
      })
      
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to load theme from localStorage:',
        expect.any(Error)
      )
      
      consoleSpy.mockRestore()
    })

    it('handles localStorage save errors gracefully', async () => {
      const user = userEvent.setup()
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('localStorage quota exceeded')
      })
      
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      await user.click(screen.getByTestId('set-dark'))
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to save theme to localStorage:',
        expect.any(Error)
      )
      
      consoleSpy.mockRestore()
    })
  })

  // DOM manipulation tests
  describe('DOM Manipulation', () => {
    it('applies theme to document element', async () => {
      const user = userEvent.setup()
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      await user.click(screen.getByTestId('set-dark'))
      
      await waitFor(() => {
        expect(document.documentElement).toHaveAttribute('data-theme', 'dark')
        expect(document.documentElement).toHaveClass('dark')
      })
    })

    it('updates meta theme-color', async () => {
      const user = userEvent.setup()
      
      // Create meta theme-color element
      const metaThemeColor = document.createElement('meta')
      metaThemeColor.name = 'theme-color'
      metaThemeColor.content = '#ffffff'
      document.head.appendChild(metaThemeColor)
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      await user.click(screen.getByTestId('set-dark'))
      
      await waitFor(() => {
        expect(metaThemeColor.content).toBe('#111827')
      })
      
      // Cleanup
      document.head.removeChild(metaThemeColor)
    })

    it('removes old theme classes when switching', async () => {
      const user = userEvent.setup()
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      await user.click(screen.getByTestId('set-light'))
      await waitFor(() => {
        expect(document.documentElement).toHaveClass('light')
      })
      
      await user.click(screen.getByTestId('set-dark'))
      await waitFor(() => {
        expect(document.documentElement).not.toHaveClass('light')
        expect(document.documentElement).toHaveClass('dark')
      })
    })
  })

  // Hook usage tests
  describe('useTheme Hook', () => {
    it('throws error when used outside provider', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      
      expect(() => {
        render(<TestComponent />)
      }).toThrow('useTheme must be used within a ThemeProvider')
      
      consoleSpy.mockRestore()
    })

    it('provides all theme context values', () => {
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      expect(screen.getByTestId('current-theme')).toBeInTheDocument()
      expect(screen.getByTestId('system-theme')).toBeInTheDocument()
      expect(screen.getByTestId('resolved-theme')).toBeInTheDocument()
      expect(screen.getByTestId('set-light')).toBeInTheDocument()
    })
  })

  // Hydration tests
  describe('Hydration', () => {
    it('prevents hydration mismatch by hiding content initially', () => {
      const { container } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      // Initially should be hidden
      expect(container.firstChild).toHaveStyle({ visibility: 'hidden' })
    })

    it('shows content after mounting', async () => {
      const { container } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      )
      
      await waitFor(() => {
        expect(container.firstChild).not.toHaveStyle({ visibility: 'hidden' })
      })
    })
  })

  // Configuration tests
  describe('Configuration', () => {
    it('uses custom storage key', async () => {
      const user = userEvent.setup()
      
      render(
        <ThemeProvider storageKey="custom-theme-key">
          <TestComponent />
        </ThemeProvider>
      )
      
      await user.click(screen.getByTestId('set-dark'))
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('custom-theme-key', 'dark')
    })

    it('disables system theme when enableSystem is false', () => {
      render(
        <ThemeProvider enableSystem={false}>
          <TestComponent />
        </ThemeProvider>
      )
      
      // Should still work, but system detection might be different
      expect(screen.getByTestId('current-theme')).toBeInTheDocument()
    })
  })
})
