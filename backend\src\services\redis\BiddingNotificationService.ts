// RFC-301: Real-time Bidding Notifications Service
import { createClient, RedisClientType } from 'redis';
import { BiddingRedisService, BidCompetitionData } from './BiddingRedisService';

export interface NotificationData {
  type: string;
  bidRequestId?: string;
  title: string;
  category?: string;
  deadline?: Date;
  deliveryCountry?: string;
  timestamp: number;
  totalBids?: number;
  priceRange?: {
    lowest: number;
    highest: number;
    average: number;
  };
  timeRemaining?: number;
}

export interface BidRequest {
  id: string;
  anonymousId: string;
  title: string;
  specifications: {
    category: string;
  };
  bidDeadline: Date;
  deliveryAddress: {
    country: string;
  };
  customerId: string;
}

export interface Bid {
  id: string;
  producerId: string;
  bidRequestId: string;
}

export class BiddingNotificationService {
  private client: RedisClientType;
  private pubSub: RedisClientType;
  private biddingService: BiddingRedisService;

  constructor() {
    this.client = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    this.pubSub = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    this.biddingService = new BiddingRedisService();
    
    this.client.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });
    
    this.pubSub.on('error', (err) => {
      console.error('Redis PubSub Error:', err);
    });
  }

  async connect(): Promise<void> {
    if (!this.client.isOpen) {
      await this.client.connect();
    }
    if (!this.pubSub.isOpen) {
      await this.pubSub.connect();
    }
    await this.biddingService.connect();
  }

  async disconnect(): Promise<void> {
    if (this.client.isOpen) {
      await this.client.disconnect();
    }
    if (this.pubSub.isOpen) {
      await this.pubSub.disconnect();
    }
    await this.biddingService.disconnect();
  }

  // Notify producers of new bid requests
  async notifyProducersOfNewRequest(bidRequest: BidRequest): Promise<void> {
    await this.connect();
    // Find matching producers based on capabilities
    const matchingProducers = await this.findMatchingProducers(bidRequest.specifications);
    
    for (const producerId of matchingProducers) {
      // Add to producer's notification queue
      const notification: NotificationData = {
        type: 'new_bid_request',
        bidRequestId: bidRequest.anonymousId,
        title: bidRequest.title,
        category: bidRequest.specifications.category,
        deadline: bidRequest.bidDeadline,
        deliveryCountry: bidRequest.deliveryAddress.country,
        timestamp: Date.now()
      };

      await this.client.lPush(`notifications:${producerId}`, JSON.stringify(notification));
      
      // Publish real-time notification
      await this.pubSub.publish(`producer:${producerId}`, JSON.stringify({
        event: 'new_bid_request',
        data: notification
      }));
    }
  }

  // Notify customer of new bids (PRD: Sadece toplam teklif tutarları görünür)
  async notifyCustomerOfNewBid(bidRequestId: string, customerId: string): Promise<void> {
    await this.connect();
    // Get updated competition data
    const competition = await this.biddingService.getBidCompetition(bidRequestId);
    
    if (!competition) return;

    const notification: NotificationData = {
      type: 'new_bid_received',
      bidRequestId,
      title: 'New bid received',
      totalBids: competition.totalBids,
      priceRange: {
        lowest: competition.lowestPrice,
        highest: competition.highestPrice,
        average: competition.averagePrice
      },
      timestamp: Date.now()
    };

    await this.client.lPush(`notifications:${customerId}`, JSON.stringify(notification));
    
    // Real-time update
    await this.pubSub.publish(`customer:${customerId}`, JSON.stringify({
      event: 'new_bid_received',
      data: notification
    }));
  }

  // Deadline approaching notifications (PRD: 48-72 saat)
  async notifyDeadlineApproaching(): Promise<void> {
    await this.connect();
    // Get bid requests expiring in next 2 hours
    const expiringRequests = await this.biddingService.getExpiringBidRequests(2);
    
    for (const bidRequestId of expiringRequests) {
      const bidRequest = await this.getBidRequestFromDatabase(bidRequestId);
      
      if (!bidRequest) continue;

      const timeRemaining = Math.floor((bidRequest.bidDeadline.getTime() - Date.now()) / 1000 / 60); // minutes
      
      // Notify customer
      await this.client.lPush(`notifications:${bidRequest.customerId}`, JSON.stringify({
        type: 'bid_deadline_approaching',
        bidRequestId,
        title: 'Bid deadline approaching',
        timeRemaining,
        timestamp: Date.now()
      }));

      // Notify all bidding producers
      const bids = await this.getBidsForRequest(bidRequestId);
      for (const bid of bids) {
        await this.client.lPush(`notifications:${bid.producerId}`, JSON.stringify({
          type: 'bid_deadline_approaching',
          bidRequestId: bidRequest.anonymousId,
          title: 'Bid deadline approaching',
          timeRemaining,
          timestamp: Date.now()
        }));
      }
    }
  }

  // Get user notifications
  async getUserNotifications(userId: string, limit: number = 50): Promise<NotificationData[]> {
    await this.connect();
    const notifications = await this.client.lRange(`notifications:${userId}`, 0, limit - 1);
    return notifications.map(n => JSON.parse(n));
  }

  // Mark notifications as read
  async markNotificationsAsRead(userId: string, count: number): Promise<void> {
    await this.connect();
    // Remove read notifications from the queue
    for (let i = 0; i < count; i++) {
      await this.client.lPop(`notifications:${userId}`);
    }
  }

  // Subscribe to real-time notifications
  async subscribeToUserNotifications(userId: string, callback: (notification: any) => void): Promise<void> {
    await this.connect();
    await this.pubSub.subscribe(`producer:${userId}`, (message) => {
      callback(JSON.parse(message));
    });
    
    await this.pubSub.subscribe(`customer:${userId}`, (message) => {
      callback(JSON.parse(message));
    });
  }

  private async findMatchingProducers(specifications: any): Promise<string[]> {
    // This would integrate with your database to find producers
    // who can fulfill the specifications
    // For now, return mock data
    return ['producer-1', 'producer-2', 'producer-3'];
  }

  private async getBidRequestFromDatabase(bidRequestId: string): Promise<BidRequest | null> {
    // This would integrate with your Prisma client
    // For now, return mock data
    return {
      id: bidRequestId,
      anonymousId: `REQ_${bidRequestId}`,
      title: 'Sample Bid Request',
      specifications: { category: 'marble' },
      bidDeadline: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
      deliveryAddress: { country: 'TR' },
      customerId: 'customer-123'
    };
  }

  private async getBidsForRequest(bidRequestId: string): Promise<Bid[]> {
    // This would integrate with your Prisma client
    // For now, return mock data
    return [
      { id: 'bid-1', producerId: 'producer-1', bidRequestId },
      { id: 'bid-2', producerId: 'producer-2', bidRequestId }
    ];
  }
}
