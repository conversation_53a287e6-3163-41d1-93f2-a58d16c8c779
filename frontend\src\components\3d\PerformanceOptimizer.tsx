'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import * as THREE from 'three';

interface PerformanceOptimizerProps {
  children: React.ReactNode;
  enableLOD?: boolean;
  enableAdaptiveQuality?: boolean;
  targetFPS?: number;
  qualityLevels?: QualityLevel[];
}

interface QualityLevel {
  name: 'low' | 'medium' | 'high' | 'ultra';
  textureSize: number;
  shadowMapSize: number;
  antialias: boolean;
  pixelRatio: number;
  lodBias: number;
}

interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  memoryUsage: number;
  drawCalls: number;
  triangles: number;
  geometries: number;
  textures: number;
}

// Initialize quality levels function to avoid SSR issues
const getDefaultQualityLevels = (): QualityLevel[] => [
  {
    name: 'low',
    textureSize: 512,
    shadowMapSize: 512,
    antialias: false,
    pixelRatio: 1,
    lodBias: 2
  },
  {
    name: 'medium',
    textureSize: 1024,
    shadowMapSize: 1024,
    antialias: true,
    pixelRatio: 1,
    lodBias: 1
  },
  {
    name: 'high',
    textureSize: 2048,
    shadowMapSize: 2048,
    antialias: true,
    pixelRatio: typeof window !== 'undefined' ? Math.min(window.devicePixelRatio, 2) : 2,
    lodBias: 0
  },
  {
    name: 'ultra',
    textureSize: 4096,
    shadowMapSize: 4096,
    antialias: true,
    pixelRatio: typeof window !== 'undefined' ? Math.min(window.devicePixelRatio, 3) : 2,
    lodBias: -1
  }
];

export const PerformanceOptimizer: React.FC<PerformanceOptimizerProps> = ({
  children,
  enableLOD = true,
  enableAdaptiveQuality = true,
  targetFPS = 60,
  qualityLevels
}) => {
  // Initialize quality levels safely
  const defaultQualityLevels = qualityLevels || getDefaultQualityLevels();
  const { gl, scene, camera } = useThree();
  const [currentQuality, setCurrentQuality] = useState<QualityLevel>(defaultQualityLevels[2]); // Start with high
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    frameTime: 16.67,
    memoryUsage: 0,
    drawCalls: 0,
    triangles: 0,
    geometries: 0,
    textures: 0
  });

  const frameTimeHistory = useRef<number[]>([]);
  const lastTime = useRef(performance.now());
  const frameCount = useRef(0);
  const qualityAdjustmentCooldown = useRef(0);

  // Performance monitoring
  const updateMetrics = useCallback(() => {
    const now = performance.now();
    const deltaTime = now - lastTime.current;
    lastTime.current = now;

    // Update frame time history
    frameTimeHistory.current.push(deltaTime);
    if (frameTimeHistory.current.length > 60) {
      frameTimeHistory.current.shift();
    }

    // Calculate average FPS
    const avgFrameTime = frameTimeHistory.current.reduce((a, b) => a + b, 0) / frameTimeHistory.current.length;
    const fps = 1000 / avgFrameTime;

    // Get renderer info
    const info = gl.info;
    
    // Estimate memory usage (rough approximation)
    const memoryUsage = (info.memory?.geometries || 0) * 1000 + (info.memory?.textures || 0) * 2000;

    setMetrics({
      fps,
      frameTime: avgFrameTime,
      memoryUsage,
      drawCalls: info.render?.calls || 0,
      triangles: info.render?.triangles || 0,
      geometries: info.memory?.geometries || 0,
      textures: info.memory?.textures || 0
    });

    frameCount.current++;
  }, [gl]);

  // Adaptive quality adjustment
  const adjustQuality = useCallback(() => {
    if (!enableAdaptiveQuality || qualityAdjustmentCooldown.current > 0) return;

    const { fps } = metrics;
    const currentIndex = defaultQualityLevels.findIndex(q => q.name === currentQuality.name);

    // If FPS is too low, decrease quality
    if (fps < targetFPS * 0.8 && currentIndex > 0) {
      const newQuality = defaultQualityLevels[currentIndex - 1];
      setCurrentQuality(newQuality);
      applyQualitySettings(newQuality);
      qualityAdjustmentCooldown.current = 180; // 3 seconds at 60fps
      console.log(`Quality decreased to ${newQuality.name} (FPS: ${fps.toFixed(1)})`);
    }
    // If FPS is consistently high, increase quality
    else if (fps > targetFPS * 1.2 && currentIndex < defaultQualityLevels.length - 1) {
      const newQuality = defaultQualityLevels[currentIndex + 1];
      setCurrentQuality(newQuality);
      applyQualitySettings(newQuality);
      qualityAdjustmentCooldown.current = 300; // 5 seconds at 60fps
      console.log(`Quality increased to ${newQuality.name} (FPS: ${fps.toFixed(1)})`);
    }
  }, [metrics, currentQuality, qualityLevels, targetFPS, enableAdaptiveQuality]);

  // Apply quality settings to renderer
  const applyQualitySettings = useCallback((quality: QualityLevel) => {
    // Update pixel ratio
    gl.setPixelRatio(quality.pixelRatio);

    // Update shadow map size
    if (gl.shadowMap) {
      gl.shadowMap.setSize(quality.shadowMapSize, quality.shadowMapSize);
    }

    // Update antialias (requires renderer recreation, so we'll skip this for now)
    // This would typically be handled at the Canvas level

    // Update texture LOD bias
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        const materials = Array.isArray(object.material) ? object.material : [object.material];
        materials.forEach((material) => {
          if (material instanceof THREE.MeshStandardMaterial || material instanceof THREE.MeshBasicMaterial) {
            // Apply texture size limits
            [material.map, material.normalMap, material.roughnessMap, material.metalnessMap].forEach((texture) => {
              if (texture) {
                // This is a simplified approach - in practice, you'd have multiple texture variants
                texture.generateMipmaps = quality.textureSize >= 1024;
                texture.minFilter = quality.textureSize >= 1024 ? THREE.LinearMipmapLinearFilter : THREE.LinearFilter;
              }
            });
          }
        });
      }
    });
  }, [gl, scene]);

  // LOD System
  const updateLOD = useCallback(() => {
    if (!enableLOD) return;

    const cameraPosition = camera.position;

    scene.traverse((object) => {
      if (object.userData.isLODObject) {
        const distance = cameraPosition.distanceTo(object.position);
        const lodLevel = getLODLevel(distance, currentQuality.lodBias);
        
        // Apply LOD based on distance
        if (object instanceof THREE.Mesh) {
          const geometry = object.geometry;
          if (geometry.userData.lodLevels) {
            const targetLOD = geometry.userData.lodLevels[lodLevel];
            if (targetLOD && object.geometry !== targetLOD) {
              object.geometry = targetLOD;
            }
          }
        }
      }
    });
  }, [camera, scene, currentQuality.lodBias, enableLOD]);

  // Get LOD level based on distance
  const getLODLevel = (distance: number, bias: number): number => {
    const adjustedDistance = distance + bias;
    
    if (adjustedDistance < 5) return 0; // High detail
    if (adjustedDistance < 15) return 1; // Medium detail
    if (adjustedDistance < 30) return 2; // Low detail
    return 3; // Very low detail
  };

  // Texture streaming and management
  const manageTextures = useCallback(() => {
    const maxTextures = currentQuality.name === 'low' ? 50 : 
                       currentQuality.name === 'medium' ? 100 : 
                       currentQuality.name === 'high' ? 200 : 400;

    // Simple texture management - remove unused textures
    if (metrics.textures > maxTextures) {
      scene.traverse((object) => {
        if (object instanceof THREE.Mesh && object.material) {
          const materials = Array.isArray(object.material) ? object.material : [object.material];
          materials.forEach((material) => {
            // This is a simplified approach - in practice, you'd have a more sophisticated system
            if (material.userData.lastUsed && Date.now() - material.userData.lastUsed > 30000) {
              // Dispose unused textures after 30 seconds
              [material.map, material.normalMap, material.roughnessMap].forEach((texture) => {
                if (texture) {
                  texture.dispose();
                }
              });
            }
          });
        }
      });
    }
  }, [scene, metrics.textures, currentQuality]);

  // Frame update
  useFrame(() => {
    updateMetrics();
    updateLOD();
    
    // Reduce cooldown
    if (qualityAdjustmentCooldown.current > 0) {
      qualityAdjustmentCooldown.current--;
    }

    // Adjust quality every 60 frames (1 second at 60fps)
    if (frameCount.current % 60 === 0) {
      adjustQuality();
      manageTextures();
    }
  });

  // Initialize quality settings
  useEffect(() => {
    applyQualitySettings(currentQuality);
  }, [currentQuality, applyQualitySettings]);

  return (
    <group>
      {children}
      
      {/* Performance Monitor Display */}
      <PerformanceMonitor 
        metrics={metrics} 
        currentQuality={currentQuality}
        visible={process.env.NODE_ENV === 'development'}
      />
    </group>
  );
};

// Performance Monitor Component
interface PerformanceMonitorProps {
  metrics: PerformanceMetrics;
  currentQuality: QualityLevel;
  visible?: boolean;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ 
  metrics, 
  currentQuality, 
  visible = false 
}) => {
  if (!visible) return null;

  return (
    <Html position={[-5, 4, 0]} transform={false}>
      <div className="bg-black bg-opacity-75 text-white p-3 rounded-lg text-xs font-mono">
        <div className="grid grid-cols-2 gap-2">
          <div>FPS: {metrics.fps.toFixed(1)}</div>
          <div>Frame: {metrics.frameTime.toFixed(1)}ms</div>
          <div>Quality: {currentQuality.name}</div>
          <div>Memory: {(metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB</div>
          <div>Draws: {metrics.drawCalls}</div>
          <div>Tris: {metrics.triangles}</div>
          <div>Geoms: {metrics.geometries}</div>
          <div>Texs: {metrics.textures}</div>
        </div>
      </div>
    </Html>
  );
};

// LOD Geometry Generator
export const createLODGeometry = (baseGeometry: THREE.BufferGeometry): THREE.BufferGeometry[] => {
  const lodLevels: THREE.BufferGeometry[] = [];
  
  // High detail (original)
  lodLevels[0] = baseGeometry.clone();
  
  // Medium detail (50% vertices)
  lodLevels[1] = simplifyGeometry(baseGeometry, 0.5);
  
  // Low detail (25% vertices)
  lodLevels[2] = simplifyGeometry(baseGeometry, 0.25);
  
  // Very low detail (10% vertices)
  lodLevels[3] = simplifyGeometry(baseGeometry, 0.1);
  
  return lodLevels;
};

// Simple geometry simplification (placeholder - in practice, use a proper decimation algorithm)
const simplifyGeometry = (geometry: THREE.BufferGeometry, factor: number): THREE.BufferGeometry => {
  const simplified = geometry.clone();
  
  // This is a very basic simplification - in practice, you'd use algorithms like
  // quadric error metrics or edge collapse
  const positionAttribute = simplified.getAttribute('position');
  const originalCount = positionAttribute.count;
  const targetCount = Math.floor(originalCount * factor);
  
  if (targetCount < originalCount) {
    // Simple vertex removal (not ideal, but demonstrates the concept)
    const step = Math.floor(originalCount / targetCount);
    const newPositions: number[] = [];
    
    for (let i = 0; i < originalCount; i += step) {
      newPositions.push(
        positionAttribute.getX(i),
        positionAttribute.getY(i),
        positionAttribute.getZ(i)
      );
    }
    
    simplified.setAttribute('position', new THREE.Float32BufferAttribute(newPositions, 3));
  }
  
  return simplified;
};

// Device capability detection
export const detectDeviceCapabilities = () => {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  
  if (!gl) {
    return { tier: 'low', maxTextureSize: 512, supportsFloatTextures: false };
  }
  
  const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
  const supportsFloatTextures = !!gl.getExtension('OES_texture_float');
  const maxVertexAttributes = gl.getParameter(gl.MAX_VERTEX_ATTRIBS);
  
  // Simple device tier classification
  let tier: 'low' | 'medium' | 'high' = 'medium';
  
  if (maxTextureSize >= 4096 && supportsFloatTextures && maxVertexAttributes >= 16) {
    tier = 'high';
  } else if (maxTextureSize < 1024 || !supportsFloatTextures) {
    tier = 'low';
  }
  
  canvas.remove();
  
  return {
    tier,
    maxTextureSize,
    supportsFloatTextures,
    maxVertexAttributes
  };
};
