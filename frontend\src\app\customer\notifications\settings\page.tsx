'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { BellIcon, EnvelopeIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';

export default function NotificationSettingsPage() {
  const [settings, setSettings] = useState({
    // Bildirim Kanalları
    systemNotifications: true,
    emailNotifications: true,
    
    // Satış Bildirimleri
    salesPaymentReminders: true,
    salesDeliveryReminders: true,
    salesStatusUpdates: true,
    salesOverduePayments: true,
    
    // Gider Bildirimleri
    expenseReminders: true,
    expenseRecurringReminders: true,
    expenseBudgetAlerts: false,
    
    // Çek Bildirimleri
    checkDueReminders: true,
    checkAdvanceReminders: true,
    checkAdvanceDays: 3,
    
    // Teslimat Bildirimleri
    deliveryStatusUpdates: true,
    deliveryDelayAlerts: true,
    deliveryCompletionNotifications: true,
    
    // Genel Ayarlar
    quietHoursEnabled: true,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    weekendNotifications: false,
    
    // Email Ayarları
    emailAddress: '<EMAIL>',
    emailDigest: 'daily' // daily, weekly, never
  });

  const handleToggle = (key: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }));
  };

  const handleInputChange = (key: string, value: string | number) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = () => {
    // API call to save settings
    console.log('Bildirim ayarları kaydedildi:', settings);
    alert('Bildirim ayarları başarıyla kaydedildi!');
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <BellIcon className="h-8 w-8 text-blue-600 mr-3" />
              Bildirim Ayarları
            </h1>
            <p className="text-gray-600 mt-1">Bildirim tercihlerinizi yönetin</p>
          </div>
          <Link
            href="/customer/notifications"
            className="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            ← Bildirimlere Dön
          </Link>
        </div>
      </div>

      <div className="space-y-8">
        
        {/* Bildirim Kanalları */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <DevicePhoneMobileIcon className="h-5 w-5 text-blue-600 mr-2" />
            Bildirim Kanalları
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Sistem Bildirimleri</label>
                <p className="text-sm text-gray-500">Platform üzerinden bildirim alın</p>
              </div>
              <input
                type="checkbox"
                checked={settings.systemNotifications}
                onChange={() => handleToggle('systemNotifications')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Email Bildirimleri</label>
                <p className="text-sm text-gray-500">Email adresinize bildirim gönderilsin</p>
              </div>
              <input
                type="checkbox"
                checked={settings.emailNotifications}
                onChange={() => handleToggle('emailNotifications')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            

          </div>
        </div>

        {/* Satış Bildirimleri */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            💰 Satış Bildirimleri
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Ödeme Hatırlatmaları</label>
                <p className="text-sm text-gray-500">Ödeme vadesi yaklaştığında hatırlatma</p>
              </div>
              <input
                type="checkbox"
                checked={settings.salesPaymentReminders}
                onChange={() => handleToggle('salesPaymentReminders')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Teslimat Hatırlatmaları</label>
                <p className="text-sm text-gray-500">Teslimat tarihi yaklaştığında hatırlatma</p>
              </div>
              <input
                type="checkbox"
                checked={settings.salesDeliveryReminders}
                onChange={() => handleToggle('salesDeliveryReminders')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Durum Güncellemeleri</label>
                <p className="text-sm text-gray-500">Satış durumu değişikliklerinde bildirim</p>
              </div>
              <input
                type="checkbox"
                checked={settings.salesStatusUpdates}
                onChange={() => handleToggle('salesStatusUpdates')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Geciken Ödemeler</label>
                <p className="text-sm text-gray-500">Ödeme vadesi geçen satışlar için uyarı</p>
              </div>
              <input
                type="checkbox"
                checked={settings.salesOverduePayments}
                onChange={() => handleToggle('salesOverduePayments')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
          </div>
        </div>

        {/* Çek Bildirimleri */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            💳 Çek Bildirimleri
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Vade Tarihi Hatırlatmaları</label>
                <p className="text-sm text-gray-500">Çek vadesi geldiğinde hatırlatma</p>
              </div>
              <input
                type="checkbox"
                checked={settings.checkDueReminders}
                onChange={() => handleToggle('checkDueReminders')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Önceden Hatırlatma</label>
                <p className="text-sm text-gray-500">Vade tarihinden önce hatırlatma gönder</p>
              </div>
              <input
                type="checkbox"
                checked={settings.checkAdvanceReminders}
                onChange={() => handleToggle('checkAdvanceReminders')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            
            {settings.checkAdvanceReminders && (
              <div className="ml-4 flex items-center space-x-3">
                <label className="text-sm text-gray-700">Kaç gün önce:</label>
                <select
                  value={settings.checkAdvanceDays}
                  onChange={(e) => handleInputChange('checkAdvanceDays', parseInt(e.target.value))}
                  className="px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={1}>1 gün</option>
                  <option value={2}>2 gün</option>
                  <option value={3}>3 gün</option>
                  <option value={5}>5 gün</option>
                  <option value={7}>1 hafta</option>
                </select>
              </div>
            )}
          </div>
        </div>

        {/* Gider Bildirimleri */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            💸 Gider Bildirimleri
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Gider Hatırlatmaları</label>
                <p className="text-sm text-gray-500">Ödeme vadesi yaklaşan giderler için hatırlatma</p>
              </div>
              <input
                type="checkbox"
                checked={settings.expenseReminders}
                onChange={() => handleToggle('expenseReminders')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Düzenli Gider Hatırlatmaları</label>
                <p className="text-sm text-gray-500">Tekrarlanan giderler için hatırlatma</p>
              </div>
              <input
                type="checkbox"
                checked={settings.expenseRecurringReminders}
                onChange={() => handleToggle('expenseRecurringReminders')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
          </div>
        </div>

        {/* Genel Ayarlar */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            ⚙️ Genel Ayarlar
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Sessiz Saatler</label>
                <p className="text-sm text-gray-500">Belirtilen saatlerde bildirim gönderilmesin</p>
              </div>
              <input
                type="checkbox"
                checked={settings.quietHoursEnabled}
                onChange={() => handleToggle('quietHoursEnabled')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            
            {settings.quietHoursEnabled && (
              <div className="ml-4 grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Başlangıç</label>
                  <input
                    type="time"
                    value={settings.quietHoursStart}
                    onChange={(e) => handleInputChange('quietHoursStart', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Bitiş</label>
                  <input
                    type="time"
                    value={settings.quietHoursEnd}
                    onChange={(e) => handleInputChange('quietHoursEnd', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            )}
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Hafta Sonu Bildirimleri</label>
                <p className="text-sm text-gray-500">Cumartesi ve Pazar günleri bildirim gönderilsin</p>
              </div>
              <input
                type="checkbox"
                checked={settings.weekendNotifications}
                onChange={() => handleToggle('weekendNotifications')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
          </div>
        </div>

        {/* Email Ayarları */}
        {settings.emailNotifications && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <EnvelopeIcon className="h-5 w-5 text-blue-600 mr-2" />
              Email Ayarları
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email Adresi</label>
                <input
                  type="email"
                  value={settings.emailAddress}
                  onChange={(e) => handleInputChange('emailAddress', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email Özeti</label>
                <select
                  value={settings.emailDigest}
                  onChange={(e) => handleInputChange('emailDigest', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="never">Özet gönderme</option>
                  <option value="daily">Günlük özet</option>
                  <option value="weekly">Haftalık özet</option>
                </select>
              </div>
            </div>
          </div>
        )}



        {/* Save Button */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Link
            href="/customer/notifications"
            className="px-6 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            İptal
          </Link>
          <button
            onClick={handleSave}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            Ayarları Kaydet
          </button>
        </div>
      </div>
    </div>
  );
}
