// RFC-501: User Management Component
'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Users, 
  Search, 
  Filter, 
  Eye, 
  UserCheck, 
  UserX, 
  AlertTriangle,
  Calendar,
  Mail,
  Phone,
  Building,
  MapPin
} from 'lucide-react';

interface User {
  id: string;
  email: string;
  userType: 'PRODUCER' | 'CUSTOMER' | 'ADMIN';
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'BANNED';
  createdAt: string;
  lastLoginAt?: string;
  profile?: {
    companyName?: string;
    contactPerson?: string;
    phone?: string;
    countryCode?: string;
  };
}

interface UserSearchResult {
  users: User[];
  total: number;
  page: number;
  totalPages: number;
}

interface UserDetails {
  id: string;
  email: string;
  userType: string;
  status: string;
  createdAt: string;
  lastLoginAt?: string;
  profile?: any;
  activityHistory: any[];
  orderHistory: any[];
  paymentHistory: any[];
  riskScore: number;
}

export default function UserManagement() {
  const [users, setUsers] = useState<UserSearchResult | null>(null);
  const [selectedUser, setSelectedUser] = useState<UserDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [userTypeFilter, setUserTypeFilter] = useState('ALL');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    fetchUsers();
  }, [searchQuery, userTypeFilter, statusFilter, currentPage]);

  const fetchUsers = async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      });

      if (searchQuery) params.append('query', searchQuery);
      if (userTypeFilter && userTypeFilter !== 'ALL') params.append('userType', userTypeFilter);
      if (statusFilter && statusFilter !== 'ALL') params.append('status', statusFilter);

      const response = await fetch(`http://localhost:8000/api/admin/users?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();
      setUsers(data.data);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUserDetails = async (userId: string) => {
    try {
      const response = await fetch(`http://localhost:8000/api/admin/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user details');
      }

      const data = await response.json();
      setSelectedUser(data.data);
    } catch (error) {
      console.error('Error fetching user details:', error);
    }
  };

  const updateUserStatus = async (userId: string, status: string, reason?: string) => {
    try {
      const response = await fetch(`http://localhost:8000/api/admin/users/${userId}/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ status, reason })
      });

      if (!response.ok) {
        throw new Error('Failed to update user status');
      }

      // Refresh users list
      fetchUsers();
      
      // Update selected user if it's the same one
      if (selectedUser?.id === userId) {
        fetchUserDetails(userId);
      }
    } catch (error) {
      console.error('Error updating user status:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="secondary"><AlertTriangle className="w-3 h-3 mr-1" />Beklemede</Badge>;
      case 'ACTIVE':
        return <Badge variant="default"><UserCheck className="w-3 h-3 mr-1" />Aktif</Badge>;
      case 'SUSPENDED':
        return <Badge variant="destructive"><UserX className="w-3 h-3 mr-1" />Askıya Alınmış</Badge>;
      case 'BANNED':
        return <Badge variant="destructive"><UserX className="w-3 h-3 mr-1" />Yasaklanmış</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getUserTypeBadge = (userType: string) => {
    switch (userType) {
      case 'PRODUCER':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700">Üretici</Badge>;
      case 'CUSTOMER':
        return <Badge variant="outline" className="bg-green-50 text-green-700">Müşteri</Badge>;
      case 'ADMIN':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700">Admin</Badge>;
      default:
        return <Badge variant="outline">{userType}</Badge>;
    }
  };

  const getRiskScoreColor = (score: number) => {
    if (score >= 70) return 'text-red-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Kullanıcılar yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Kullanıcı Yönetimi</h1>
          <p className="text-muted-foreground">
            Platform kullanıcılarını yönetin ve izleyin
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-lg px-3 py-1">
            {users?.total || 0} Toplam Kullanıcı
          </Badge>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="E-posta, şirket adı veya iletişim kişisi ile arayın..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={userTypeFilter} onValueChange={setUserTypeFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Kullanıcı Tipi" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Tüm Tipler</SelectItem>
                <SelectItem value="PRODUCER">Üretici</SelectItem>
                <SelectItem value="CUSTOMER">Müşteri</SelectItem>
                <SelectItem value="ADMIN">Admin</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Durum" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Tüm Durumlar</SelectItem>
                <SelectItem value="PENDING">Beklemede</SelectItem>
                <SelectItem value="ACTIVE">Aktif</SelectItem>
                <SelectItem value="SUSPENDED">Askıya Alınmış</SelectItem>
                <SelectItem value="BANNED">Yasaklanmış</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      <div className="grid gap-4">
        {users?.users.map((user) => (
          <Card key={user.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="space-y-2 flex-1">
                  <div className="flex items-center space-x-3">
                    <Users className="w-5 h-5 text-blue-600" />
                    <h3 className="text-lg font-semibold">{user.email}</h3>
                    {getUserTypeBadge(user.userType)}
                    {getStatusBadge(user.status)}
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                    {user.profile?.companyName && (
                      <div className="flex items-center space-x-2">
                        <Building className="w-4 h-4" />
                        <span>{user.profile.companyName}</span>
                      </div>
                    )}
                    {user.profile?.contactPerson && (
                      <div className="flex items-center space-x-2">
                        <Users className="w-4 h-4" />
                        <span>{user.profile.contactPerson}</span>
                      </div>
                    )}
                    {user.profile?.phone && (
                      <div className="flex items-center space-x-2">
                        <Phone className="w-4 h-4" />
                        <span>{user.profile.phone}</span>
                      </div>
                    )}
                    {user.profile?.countryCode && (
                      <div className="flex items-center space-x-2">
                        <MapPin className="w-4 h-4" />
                        <span>{user.profile.countryCode}</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>Katılım: {new Date(user.createdAt).toLocaleDateString('tr-TR')}</span>
                    </div>
                    {user.lastLoginAt && (
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4" />
                        <span>Son giriş: {new Date(user.lastLoginAt).toLocaleDateString('tr-TR')}</span>
                      </div>
                    )}
                  </div>
                </div>

                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      onClick={() => fetchUserDetails(user.id)}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Detayları Görüntüle
                    </Button>
                  </DialogTrigger>
                  
                  <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>Kullanıcı Detayları: {user.email}</DialogTitle>
                    </DialogHeader>
                    
                    {selectedUser && (
                      <UserDetailsModal 
                        user={selectedUser}
                        onUpdateStatus={updateUserStatus}
                      />
                    )}
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      {users && users.totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Önceki
          </Button>
          <span className="text-sm text-muted-foreground">
            Sayfa {currentPage} / {users.totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(users.totalPages, prev + 1))}
            disabled={currentPage === users.totalPages}
          >
            Sonraki
          </Button>
        </div>
      )}

      {users?.users.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Kullanıcı bulunamadı</h3>
            <p className="text-muted-foreground">Arama kriterlerinizi ayarlamayı deneyin.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// User Details Modal Component
function UserDetailsModal({ 
  user, 
  onUpdateStatus 
}: {
  user: UserDetails;
  onUpdateStatus: (userId: string, status: string, reason?: string) => void;
}) {
  const [statusReason, setStatusReason] = useState('');

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>E-posta</Label>
          <p className="font-medium">{user.email}</p>
        </div>
        <div>
          <Label>Kullanıcı Tipi</Label>
          <p className="font-medium">{user.userType === 'PRODUCER' ? 'Üretici' : user.userType === 'CUSTOMER' ? 'Müşteri' : 'Admin'}</p>
        </div>
        <div>
          <Label>Durum</Label>
          <p className="font-medium">{user.status === 'ACTIVE' ? 'Aktif' : user.status === 'PENDING' ? 'Beklemede' : user.status === 'SUSPENDED' ? 'Askıya Alınmış' : 'Yasaklanmış'}</p>
        </div>
        <div>
          <Label>Risk Skoru</Label>
          <p className={`font-medium ${getRiskScoreColor(user.riskScore)}`}>
            {user.riskScore}/100
          </p>
        </div>
      </div>

      {/* Profile Information */}
      {user.profile && (
        <div>
          <h3 className="text-lg font-semibold mb-3">Profil Bilgileri</h3>
          <div className="grid grid-cols-2 gap-4">
            {user.profile.companyName && (
              <div>
                <Label>Şirket Adı</Label>
                <p className="font-medium">{user.profile.companyName}</p>
              </div>
            )}
            {user.profile.contactPerson && (
              <div>
                <Label>İletişim Kişisi</Label>
                <p className="font-medium">{user.profile.contactPerson}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Activity Summary */}
      <div>
        <h3 className="text-lg font-semibold mb-3">Aktivite Özeti</h3>
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 border rounded">
            <p className="text-2xl font-bold">{user.orderHistory.length}</p>
            <p className="text-sm text-muted-foreground">Siparişler</p>
          </div>
          <div className="text-center p-3 border rounded">
            <p className="text-2xl font-bold">{user.paymentHistory.length}</p>
            <p className="text-sm text-muted-foreground">Ödemeler</p>
          </div>
          <div className="text-center p-3 border rounded">
            <p className="text-2xl font-bold">{user.activityHistory.length}</p>
            <p className="text-sm text-muted-foreground">Aktiviteler</p>
          </div>
        </div>
      </div>

      {/* Status Update Actions */}
      <div className="border-t pt-4">
        <h3 className="text-lg font-semibold mb-3">Yönetici İşlemleri</h3>
        <div className="space-y-3">
          <div>
            <Label htmlFor="reason">Durum değişikliği nedeni</Label>
            <Textarea
              id="reason"
              value={statusReason}
              onChange={(e) => setStatusReason(e.target.value)}
              placeholder="Durum değişikliği nedenini girin..."
              rows={3}
            />
          </div>
          <div className="flex space-x-2">
            <Button
              onClick={() => onUpdateStatus(user.id, 'ACTIVE', statusReason)}
              disabled={user.status === 'ACTIVE'}
              className="bg-green-600 hover:bg-green-700"
            >
              <UserCheck className="w-4 h-4 mr-2" />
              Aktifleştir
            </Button>
            <Button
              onClick={() => onUpdateStatus(user.id, 'SUSPENDED', statusReason)}
              disabled={user.status === 'SUSPENDED'}
              variant="destructive"
            >
              <UserX className="w-4 h-4 mr-2" />
              Askıya Al
            </Button>
            <Button
              onClick={() => onUpdateStatus(user.id, 'BANNED', statusReason)}
              disabled={user.status === 'BANNED'}
              variant="destructive"
            >
              <UserX className="w-4 h-4 mr-2" />
              Yasakla
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

function getRiskScoreColor(score: number) {
  if (score >= 70) return 'text-red-600';
  if (score >= 40) return 'text-yellow-600';
  return 'text-green-600';
}
