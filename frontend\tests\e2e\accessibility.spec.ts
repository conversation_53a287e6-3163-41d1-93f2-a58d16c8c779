import { test, expect } from '@playwright/test'
import AxeBuilder from '@axe-core/playwright'

/**
 * End-to-End Accessibility Tests for RFC-004 UI/UX Design System
 * 
 * These tests verify WCAG 2.1 AA compliance in real browser environments
 * across different devices and accessibility settings.
 */

test.describe('Accessibility E2E Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the home page before each test
    await page.goto('/')
    
    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle')
  })

  test('homepage should not have accessibility violations', async ({ page }) => {
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze()

    expect(accessibilityScanResults.violations).toEqual([])
  })

  test('design system demo page should be accessible', async ({ page }) => {
    await page.goto('/design-system')
    await page.waitForLoadState('networkidle')

    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze()

    expect(accessibilityScanResults.violations).toEqual([])
  })

  test('keyboard navigation works correctly', async ({ page }) => {
    // Test tab navigation through interactive elements
    await page.keyboard.press('Tab')
    
    // Should focus on skip link first
    const skipLink = page.getByRole('link', { name: /ana içeriğe geç/i })
    await expect(skipLink).toBeFocused()
    
    // Continue tabbing through navigation
    await page.keyboard.press('Tab')
    const brandLink = page.getByRole('link', { name: /türkiye doğal taş pazarı/i })
    await expect(brandLink).toBeFocused()
    
    // Tab through navigation links
    await page.keyboard.press('Tab')
    const homeLink = page.getByRole('link', { name: /ana sayfa/i })
    await expect(homeLink).toBeFocused()
  })

  test('theme toggle is keyboard accessible', async ({ page }) => {
    // Find and focus theme toggle
    const themeToggle = page.getByRole('button').filter({ hasText: /☀️|🌙|💻/ })
    await themeToggle.focus()
    await expect(themeToggle).toBeFocused()
    
    // Activate with Enter key
    await page.keyboard.press('Enter')
    
    // Theme should change (check for data-theme attribute change)
    await page.waitForTimeout(500) // Wait for theme transition
    const htmlElement = page.locator('html')
    const dataTheme = await htmlElement.getAttribute('data-theme')
    expect(dataTheme).toBeTruthy()
  })

  test('product cards are accessible', async ({ page }) => {
    // Wait for product cards to load
    const productCards = page.getByRole('button').filter({ hasText: /teklif al/i })
    await expect(productCards.first()).toBeVisible()
    
    // Test keyboard navigation to product card
    await productCards.first().focus()
    await expect(productCards.first()).toBeFocused()
    
    // Test activation with Enter
    await page.keyboard.press('Enter')
    // Should trigger some action (in real app would navigate)
  })

  test('form inputs have proper labels and error states', async ({ page }) => {
    // Navigate to a page with forms (search filters)
    const categorySelect = page.getByRole('combobox').first()
    await expect(categorySelect).toBeVisible()
    
    // Check that form controls have accessible names
    const categoryLabel = page.getByText('Kategori')
    await expect(categoryLabel).toBeVisible()
    
    // Test keyboard interaction
    await categorySelect.focus()
    await expect(categorySelect).toBeFocused()
  })

  test('color contrast meets WCAG standards', async ({ page }) => {
    // Test with axe-core color contrast rules
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2aa'])
      .include('body')
      .analyze()

    // Filter for color contrast violations
    const contrastViolations = accessibilityScanResults.violations.filter(
      violation => violation.id === 'color-contrast'
    )
    
    expect(contrastViolations).toEqual([])
  })

  test('images have appropriate alt text', async ({ page }) => {
    const images = page.getByRole('img')
    const imageCount = await images.count()
    
    for (let i = 0; i < imageCount; i++) {
      const image = images.nth(i)
      const altText = await image.getAttribute('alt')
      
      // All images should have alt text (empty alt is acceptable for decorative images)
      expect(altText).toBeDefined()
    }
  })

  test('headings follow proper hierarchy', async ({ page }) => {
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withRules(['heading-order'])
      .analyze()

    expect(accessibilityScanResults.violations).toEqual([])
  })

  test('focus indicators are visible', async ({ page }) => {
    // Test focus visibility on interactive elements
    const buttons = page.getByRole('button')
    const buttonCount = await buttons.count()
    
    if (buttonCount > 0) {
      const firstButton = buttons.first()
      await firstButton.focus()
      
      // Check that focus is visible (this is a basic check)
      await expect(firstButton).toBeFocused()
      
      // In a real test, you might check for specific focus styles
      const focusedElement = page.locator(':focus')
      await expect(focusedElement).toBeVisible()
    }
  })

  test('skip links work correctly', async ({ page }) => {
    // Press Tab to focus skip link
    await page.keyboard.press('Tab')
    
    const skipLink = page.getByRole('link', { name: /ana içeriğe geç/i })
    await expect(skipLink).toBeFocused()
    
    // Activate skip link
    await page.keyboard.press('Enter')
    
    // Should focus main content
    const mainContent = page.getByRole('main')
    await expect(mainContent).toBeVisible()
  })

  test('mobile accessibility', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip('This test only runs on mobile')
    }
    
    // Test mobile-specific accessibility
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze()

    expect(accessibilityScanResults.violations).toEqual([])
    
    // Test touch targets are large enough (44px minimum)
    const buttons = page.getByRole('button')
    const buttonCount = await buttons.count()
    
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = buttons.nth(i)
      const boundingBox = await button.boundingBox()
      
      if (boundingBox) {
        expect(boundingBox.width).toBeGreaterThanOrEqual(44)
        expect(boundingBox.height).toBeGreaterThanOrEqual(44)
      }
    }
  })

  test('dark mode accessibility', async ({ page }) => {
    // Switch to dark mode
    const themeToggle = page.getByRole('button').filter({ hasText: /☀️|🌙|💻/ })
    await themeToggle.click()
    
    // Wait for theme change
    await page.waitForTimeout(500)
    
    // Test accessibility in dark mode
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze()

    expect(accessibilityScanResults.violations).toEqual([])
  })

  test('reduced motion preferences are respected', async ({ page }) => {
    // Set reduced motion preference
    await page.emulateMedia({ reducedMotion: 'reduce' })
    
    // Reload page to apply preference
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // Test that animations are reduced/disabled
    // This would need specific implementation based on your CSS
    const animatedElements = page.locator('[class*="animate-"]')
    const count = await animatedElements.count()
    
    // In a real implementation, you'd check that animations are disabled
    // when prefers-reduced-motion is set
    console.log(`Found ${count} potentially animated elements`)
  })

  test('screen reader landmarks are present', async ({ page }) => {
    // Check for proper landmark roles
    const main = page.getByRole('main')
    await expect(main).toBeVisible()
    
    const navigation = page.getByRole('navigation')
    await expect(navigation).toBeVisible()
    
    const contentinfo = page.getByRole('contentinfo') // footer
    await expect(contentinfo).toBeVisible()
  })

  test('form validation is accessible', async ({ page }) => {
    // This test would be more relevant with actual forms
    // For now, test the search form accessibility
    const searchInputs = page.getByRole('combobox')
    const inputCount = await searchInputs.count()
    
    if (inputCount > 0) {
      const firstInput = searchInputs.first()
      
      // Check that input has accessible name
      const accessibleName = await firstInput.getAttribute('aria-label') || 
                            await firstInput.getAttribute('aria-labelledby')
      
      // Should have some form of accessible name
      expect(accessibleName || await page.locator('label').count()).toBeTruthy()
    }
  })

  test('error messages are announced to screen readers', async ({ page }) => {
    // Test error state accessibility
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withRules(['aria-valid-attr-value', 'aria-required-attr'])
      .analyze()

    expect(accessibilityScanResults.violations).toEqual([])
  })
})
