'use client';

import React from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Container } from '@/components/ui/container';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useSimpleTranslation } from '@/hooks/useSimpleTranslation';
import { usePathname } from 'next/navigation';

export default function TestI18nPage() {
  const { t, locale, isRTL } = useSimpleTranslation();
  const pathname = usePathname();

  return (
    <div className={`min-h-screen bg-background ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <Navigation />

      <Container className="py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-4">
              {t('test.title')}
            </h1>
            <p className="text-muted-foreground">
              {t('test.subtitle')}
            </p>
          </div>



          {/* URL Test */}
          <Card>
            <CardHeader>
              <CardTitle>{t('test.url_test')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>{t('test.current_url')}:</strong> {typeof window !== 'undefined' ? window.location.pathname : pathname}</p>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/test-i18n'}
                  >
                    Türkçe (/)
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/en/test-i18n'}
                  >
                    English (/en/)
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/ar/test-i18n'}
                  >
                    العربية (/ar/)
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/zh/test-i18n'}
                  >
                    中文 (/zh/)
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/ru/test-i18n'}
                  >
                    Русский (/ru/)
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Sample Content */}
          <Card>
            <CardHeader>
              <CardTitle>{t('test.sample_content')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold mb-2">{t('common.current_language')}: {locale.toUpperCase()}</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    {t('test.turkish_content')}
                  </p>
                  <ul className="text-sm space-y-1">
                    <li>• {t('nav.home')}</li>
                    <li>• {t('nav.products')}</li>
                    <li>• {t('nav.3d_showroom')}</li>
                    <li>• {t('nav.news')}</li>
                    <li>• {t('nav.about')}</li>
                    <li>• {t('nav.contact')}</li>
                  </ul>
                </div>

                <div className="p-4 bg-muted rounded-lg">
                  <h5 className="font-semibold mb-2">{t('common.language')}: {locale}</h5>
                  <p className="text-sm">
                    {t('test.english_content')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* RTL Test */}
          <Card>
            <CardHeader>
              <CardTitle>{t('test.rtl_test')} {isRTL ? '(RTL Active)' : '(LTR Active)'}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className={isRTL ? 'text-right' : 'text-left'}>
                  {t('test.turkish_content')}
                </p>
                <div className={`flex gap-2 ${isRTL ? 'justify-start' : 'justify-start'}`}>
                  <Button variant="outline">{t('common.cancel')}</Button>
                  <Button>{t('common.save')}</Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Debug Info */}
          <Card>
            <CardHeader>
              <CardTitle>{t('test.debug_info')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm font-mono">
                <p><strong>User Agent:</strong> {navigator.userAgent}</p>
                <p><strong>Language:</strong> {navigator.language}</p>
                <p><strong>Languages:</strong> {navigator.languages.join(', ')}</p>
                <p><strong>Timezone:</strong> {Intl.DateTimeFormat().resolvedOptions().timeZone}</p>
                <p><strong>Current Time:</strong> {new Date().toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </Container>
    </div>
  );
}
