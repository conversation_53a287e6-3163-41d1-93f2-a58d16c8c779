import { Request, Response, NextFunction } from 'express';

export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new Error(`Route not found: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    success: false,
    error: {
      message: 'Route not found',
      statusCode: 404,
      path: req.originalUrl,
      method: req.method,
      timestamp: new Date().toISOString(),
      availableRoutes: [
        'GET /health',
        'POST /api/auth/login',
        'POST /api/auth/register',
        'GET /api/users/profile',
        'GET /api/products',
        'GET /api-docs'
      ]
    }
  });
};
