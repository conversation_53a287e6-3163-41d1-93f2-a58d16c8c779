'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Search,
  Filter,
  Download,
  Eye,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  User,
  DollarSign,
  TrendingUp,
  Activity,
  Flag,
  FileText,
  Calendar
} from 'lucide-react'
import { FraudAlertModal } from '@/components/admin/FraudAlertModal'

// Mock data - gerçek API'den gelecek
const mockFraudAlerts = [
  {
    id: 'fraud_001',
    paymentId: 'pay_12348',
    userId: 'user_001',
    customerName: 'Suspicious Trading Ltd.',
    type: 'suspicious_amount',
    severity: 'high',
    description: 'Yeni hesap ile anormal yüksek tutarlı ödeme ($50,000)',
    amount: 50000,
    currency: 'USD',
    createdAt: '2025-01-02T14:30:00Z',
    investigated: false,
    riskScore: 85,
    flags: [
      'Yeni hesap (< 30 gün)',
      '<PERSON><PERSON>ks<PERSON> tutar (> $25,000)',
      'İlk işlem',
      'Farklı IP adresi'
    ],
    details: {
      accountAge: 15,
      previousTransactions: 0,
      ipAddress: '*************',
      location: 'İstanbul, Türkiye',
      deviceFingerprint: 'fp_abc123'
    }
  },
  {
    id: 'fraud_002',
    paymentId: 'pay_12349',
    userId: 'user_002',
    customerName: 'Quick Stone Co.',
    type: 'unusual_pattern',
    severity: 'medium',
    description: 'Kısa sürede çoklu ödeme işlemi (5 işlem / 2 saat)',
    amount: 15000,
    currency: 'USD',
    createdAt: '2025-01-02T12:15:00Z',
    investigated: true,
    investigatedAt: '2025-01-02T13:00:00Z',
    investigatedBy: 'admin_001',
    resolution: 'false_positive',
    riskScore: 65,
    flags: [
      'Çoklu işlem',
      'Kısa zaman aralığı',
      'Farklı ürünler'
    ],
    details: {
      accountAge: 120,
      previousTransactions: 25,
      ipAddress: '*********',
      location: 'Ankara, Türkiye',
      deviceFingerprint: 'fp_def456'
    }
  },
  {
    id: 'fraud_003',
    paymentId: 'pay_12350',
    userId: 'user_003',
    customerName: 'Marble Import Inc.',
    type: 'high_risk_user',
    severity: 'critical',
    description: 'Kara listede bulunan kullanıcı ile bağlantılı hesap',
    amount: 30000,
    currency: 'USD',
    createdAt: '2025-01-02T10:45:00Z',
    investigated: false,
    riskScore: 95,
    flags: [
      'Kara liste bağlantısı',
      'Benzer hesap bilgileri',
      'Şüpheli IP aralığı',
      'Proxy kullanımı'
    ],
    details: {
      accountAge: 45,
      previousTransactions: 8,
      ipAddress: '***********',
      location: 'Bilinmeyen',
      deviceFingerprint: 'fp_ghi789'
    }
  }
]

const mockStats = {
  totalAlerts: 15,
  highRisk: 3,
  mediumRisk: 8,
  lowRisk: 4,
  investigated: 12,
  falsePositives: 7,
  confirmedFraud: 2
}

export default function FraudDetectionPage() {
  const [selectedAlert, setSelectedAlert] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [severityFilter, setSeverityFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [fraudAlerts, setFraudAlerts] = useState(mockFraudAlerts)
  const [isLoading, setIsLoading] = useState(false)

  const handleInvestigateAlert = async (alertId: string, resolution: string, notes?: string) => {
    setIsLoading(true)
    try {
      // API çağrısı simülasyonu
      await new Promise(resolve => setTimeout(resolve, 1000))

      setFraudAlerts(prev => prev.map(alert =>
        alert.id === alertId
          ? {
              ...alert,
              investigated: true,
              resolution,
              investigatedAt: new Date().toISOString(),
              investigatedBy: 'admin_001',
              investigationNotes: notes
            }
          : alert
      ))

      // Alert investigated successfully
    } catch (error) {
      // Error handling - could be logged to monitoring service
    } finally {
      setIsLoading(false)
    }
  }

  const handleMarkFalsePositive = async (alertId: string) => {
    await handleInvestigateAlert(alertId, 'false_positive', 'Yanlış alarm olarak işaretlendi')
  }

  const handleConfirmFraud = async (alertId: string) => {
    const notes = prompt('Dolandırıcılık detaylarını belirtin:')
    if (notes) {
      await handleInvestigateAlert(alertId, 'confirmed_fraud', notes)
    }
  }

  const handleMarkUnderReview = async (alertId: string) => {
    const notes = prompt('İnceleme notları:')
    await handleInvestigateAlert(alertId, 'under_review', notes)
  }

  const filteredAlerts = fraudAlerts.filter(alert => {
    const matchesSearch = alert.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         alert.paymentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         alert.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesSeverity = severityFilter === 'all' || alert.severity === severityFilter
    const matchesStatus = statusFilter === 'all' ||
                         (statusFilter === 'investigated' && alert.investigated) ||
                         (statusFilter === 'pending' && !alert.investigated)
    return matchesSearch && matchesSeverity && matchesStatus
  })

  // Dinamik stats hesaplama
  const stats = {
    totalAlerts: fraudAlerts.length,
    highRisk: fraudAlerts.filter(a => a.severity === 'high').length,
    pending: fraudAlerts.filter(a => !a.investigated).length,
    confirmed: fraudAlerts.filter(a => a.resolution === 'confirmed_fraud').length
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge variant="destructive">Kritik</Badge>
      case 'high':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Yüksek</Badge>
      case 'medium':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Orta</Badge>
      case 'low':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Düşük</Badge>
      default:
        return <Badge variant="outline">{severity}</Badge>
    }
  }

  const getStatusBadge = (alert: any) => {
    if (!alert.investigated) {
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">İnceleme Bekliyor</Badge>
    }
    
    switch (alert.resolution) {
      case 'false_positive':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Yanlış Alarm</Badge>
      case 'confirmed_fraud':
        return <Badge variant="destructive">Dolandırıcılık Onaylandı</Badge>
      case 'under_review':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">İnceleme Altında</Badge>
      default:
        return <Badge variant="outline">İncelendi</Badge>
    }
  }

  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return 'text-red-600'
    if (score >= 60) return 'text-yellow-600'
    if (score >= 40) return 'text-blue-600'
    return 'text-green-600'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Fraud Detection</h1>
          <p className="text-gray-600 mt-1">
            Şüpheli işlemleri tespit edin ve güvenlik uyarılarını yönetin
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Rapor İndir
          </Button>
          <Button variant="outline">
            <Activity className="w-4 h-4 mr-2" />
            Risk Analizi
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Uyarı</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalAlerts}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">İnceleme Bekliyor</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <Flag className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Yüksek Risk</p>
              <p className="text-2xl font-bold text-gray-900">{stats.highRisk}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Shield className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Onaylanan Fraud</p>
              <p className="text-2xl font-bold text-gray-900">{stats.confirmed}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Risk Level Distribution */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Seviyesi Dağılımı</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
            <div className="w-3 h-3 bg-red-600 rounded-full"></div>
            <div>
              <p className="text-sm font-medium text-gray-900">Kritik Risk</p>
              <p className="text-lg font-bold text-red-600">{mockStats.highRisk}</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
            <div className="w-3 h-3 bg-yellow-600 rounded-full"></div>
            <div>
              <p className="text-sm font-medium text-gray-900">Orta Risk</p>
              <p className="text-lg font-bold text-yellow-600">{mockStats.mediumRisk}</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
            <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
            <div>
              <p className="text-sm font-medium text-gray-900">Düşük Risk</p>
              <p className="text-lg font-bold text-blue-600">{mockStats.lowRisk}</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-3 h-3 bg-gray-600 rounded-full"></div>
            <div>
              <p className="text-sm font-medium text-gray-900">İncelendi</p>
              <p className="text-lg font-bold text-gray-600">{mockStats.investigated}</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Search and Filters */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Müşteri adı, ödeme ID veya açıklama ile ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={severityFilter}
              onChange={(e) => setSeverityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tüm Risk Seviyeleri</option>
              <option value="critical">Kritik</option>
              <option value="high">Yüksek</option>
              <option value="medium">Orta</option>
              <option value="low">Düşük</option>
            </select>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tüm Durumlar</option>
              <option value="pending">İnceleme Bekliyor</option>
              <option value="investigated">İncelendi</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Fraud Alerts Cards */}
      <div className="space-y-4">
        {filteredAlerts.length > 0 ? (
          filteredAlerts.map((alert) => (
            <Card key={alert.id} className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-start justify-between">
                {/* Left Section - Risk Score & Customer Info */}
                <div className="flex items-start space-x-4">
                  {/* Risk Score */}
                  <div className="flex flex-col items-center">
                    <div className={`text-3xl font-bold ${getRiskScoreColor(alert.riskScore)}`}>
                      {alert.riskScore}
                    </div>
                    <div className="text-xs text-gray-500">Risk Skoru</div>
                    <div className="mt-2">
                      {getSeverityBadge(alert.severity)}
                    </div>
                  </div>

                  {/* Customer & Payment Info */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                        <User className="h-6 w-6 text-red-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{alert.customerName}</h3>
                        <p className="text-sm text-gray-500 font-mono">{alert.paymentId}</p>
                      </div>
                    </div>

                    {/* Alert Description */}
                    <div className="mb-3">
                      <p className="text-gray-700 text-sm leading-relaxed">{alert.description}</p>
                    </div>

                    {/* Risk Flags */}
                    <div className="flex flex-wrap gap-2 mb-3">
                      {alert.flags.slice(0, 3).map((flag: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs bg-red-50 text-red-700 border-red-200">
                          <AlertTriangle className="w-3 h-3 mr-1" />
                          {flag}
                        </Badge>
                      ))}
                      {alert.flags.length > 3 && (
                        <Badge variant="outline" className="text-xs bg-gray-50 text-gray-600">
                          +{alert.flags.length - 3} daha
                        </Badge>
                      )}
                    </div>

                    {/* Amount & Date */}
                    <div className="flex items-center space-x-6 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <DollarSign className="w-4 h-4" />
                        <span className="font-medium">${alert.amount.toLocaleString()} {alert.currency}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(alert.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Section - Status & Actions */}
                <div className="flex flex-col items-end space-y-3">
                  {/* Status */}
                  <div>
                    {getStatusBadge(alert)}
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col space-y-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedAlert(alert)}
                      className="w-full"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Detayları İncele
                    </Button>

                    {!alert.investigated && (
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-green-600 border-green-600 hover:bg-green-50"
                          onClick={() => handleMarkFalsePositive(alert.id)}
                          disabled={isLoading}
                        >
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Yanlış Alarm
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 border-red-600 hover:bg-red-50"
                          onClick={() => handleConfirmFraud(alert.id)}
                          disabled={isLoading}
                        >
                          <Flag className="w-4 h-4 mr-1" />
                          Fraud Onayla
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          ))
        ) : (
          <Card className="p-12 text-center">
            <Shield className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Fraud Uyarısı Bulunamadı</h3>
            <p className="text-gray-500">
              {searchTerm || severityFilter !== 'all' || statusFilter !== 'all'
                ? 'Arama kriterlerinize uygun fraud uyarısı bulunamadı.'
                : 'Şu anda aktif fraud uyarısı bulunmuyor.'}
            </p>
          </Card>
        )}
      </div>

      {/* Fraud Alert Modal */}
      {selectedAlert && (
        <FraudAlertModal
          alert={selectedAlert}
          isOpen={!!selectedAlert}
          onClose={() => setSelectedAlert(null)}
          onInvestigate={async (alertId, resolution, notes) => {
            await handleInvestigateAlert(alertId, resolution, notes)
            setSelectedAlert(null)
          }}
        />
      )}
    </div>
  )
}
