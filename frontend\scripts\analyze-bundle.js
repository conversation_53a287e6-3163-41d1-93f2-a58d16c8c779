#!/usr/bin/env node

/**
 * Bundle Analysis Script for RFC-004 UI/UX Design System
 * 
 * This script analyzes the webpack bundle to identify:
 * - Large dependencies
 * - Duplicate modules
 * - Optimization opportunities
 * - Tree-shaking effectiveness
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🔍 Starting bundle analysis...\n')

// Build with analyzer
console.log('📦 Building with bundle analyzer...')
try {
  execSync('ANALYZE=true npm run build', { 
    stdio: 'inherit',
    env: { ...process.env, ANALYZE: 'true' }
  })
} catch (error) {
  console.error('❌ Build failed:', error.message)
  process.exit(1)
}

// Check if analyze directory exists
const analyzeDir = path.join(__dirname, '../analyze')
if (!fs.existsSync(analyzeDir)) {
  console.error('❌ Analyze directory not found')
  process.exit(1)
}

console.log('\n✅ Bundle analysis complete!')
console.log('📊 Reports generated:')
console.log(`   - Client: ${path.join(analyzeDir, 'client.html')}`)
console.log(`   - Server: ${path.join(analyzeDir, 'server.html')}`)

// Generate bundle size report
console.log('\n📈 Generating bundle size report...')

const buildDir = path.join(__dirname, '../.next')
const staticDir = path.join(buildDir, 'static')

if (fs.existsSync(staticDir)) {
  const chunks = []
  
  // Read chunks directory
  const chunksDir = path.join(staticDir, 'chunks')
  if (fs.existsSync(chunksDir)) {
    const chunkFiles = fs.readdirSync(chunksDir)
      .filter(file => file.endsWith('.js'))
      .map(file => {
        const filePath = path.join(chunksDir, file)
        const stats = fs.statSync(filePath)
        return {
          name: file,
          size: stats.size,
          sizeKB: Math.round(stats.size / 1024 * 100) / 100
        }
      })
      .sort((a, b) => b.size - a.size)
    
    chunks.push(...chunkFiles)
  }
  
  // Read pages directory
  const pagesDir = path.join(staticDir, 'chunks/pages')
  if (fs.existsSync(pagesDir)) {
    const pageFiles = fs.readdirSync(pagesDir)
      .filter(file => file.endsWith('.js'))
      .map(file => {
        const filePath = path.join(pagesDir, file)
        const stats = fs.statSync(filePath)
        return {
          name: `pages/${file}`,
          size: stats.size,
          sizeKB: Math.round(stats.size / 1024 * 100) / 100
        }
      })
      .sort((a, b) => b.size - a.size)
    
    chunks.push(...pageFiles)
  }
  
  // Generate report
  const report = {
    timestamp: new Date().toISOString(),
    totalChunks: chunks.length,
    totalSize: chunks.reduce((sum, chunk) => sum + chunk.size, 0),
    largestChunks: chunks.slice(0, 10),
    recommendations: generateRecommendations(chunks)
  }
  
  // Save report
  const reportPath = path.join(analyzeDir, 'bundle-report.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  
  // Display summary
  console.log('\n📋 Bundle Summary:')
  console.log(`   Total chunks: ${report.totalChunks}`)
  console.log(`   Total size: ${Math.round(report.totalSize / 1024)} KB`)
  console.log(`   Largest chunk: ${report.largestChunks[0]?.name} (${report.largestChunks[0]?.sizeKB} KB)`)
  
  console.log('\n🔝 Top 5 Largest Chunks:')
  report.largestChunks.slice(0, 5).forEach((chunk, index) => {
    console.log(`   ${index + 1}. ${chunk.name} - ${chunk.sizeKB} KB`)
  })
  
  if (report.recommendations.length > 0) {
    console.log('\n💡 Optimization Recommendations:')
    report.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`)
    })
  }
  
  console.log(`\n📄 Detailed report saved: ${reportPath}`)
}

console.log('\n🎉 Analysis complete! Open the HTML reports in your browser to explore.')

function generateRecommendations(chunks) {
  const recommendations = []
  
  // Check for large chunks
  const largeChunks = chunks.filter(chunk => chunk.sizeKB > 500)
  if (largeChunks.length > 0) {
    recommendations.push(`Consider code splitting for large chunks: ${largeChunks.map(c => c.name).join(', ')}`)
  }
  
  // Check for many small chunks
  const smallChunks = chunks.filter(chunk => chunk.sizeKB < 10)
  if (smallChunks.length > 20) {
    recommendations.push('Consider combining small chunks to reduce HTTP requests')
  }
  
  // Check for potential duplicates
  const duplicateNames = chunks
    .map(chunk => chunk.name.replace(/\.[a-f0-9]+\.js$/, '.js'))
    .filter((name, index, arr) => arr.indexOf(name) !== index)
  
  if (duplicateNames.length > 0) {
    recommendations.push('Check for potential duplicate modules in chunks')
  }
  
  // UI components specific recommendations
  const uiChunk = chunks.find(chunk => chunk.name.includes('ui-components'))
  if (uiChunk && uiChunk.sizeKB > 200) {
    recommendations.push('Consider lazy loading UI components or splitting into smaller chunks')
  }
  
  // Three.js specific recommendations
  const threeChunk = chunks.find(chunk => chunk.name.includes('three'))
  if (threeChunk && threeChunk.sizeKB > 1000) {
    recommendations.push('Consider lazy loading Three.js components only when needed')
  }
  
  return recommendations
}
