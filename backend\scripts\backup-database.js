#!/usr/bin/env node

/**
 * Database Backup Script
 * Creates automated backups of the production database
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');

// Load environment variables
require('dotenv').config({ path: '.env.production' });

console.log('💾 Starting Database Backup Process...\n');

// Configuration
const BACKUP_DIR = path.join(process.cwd(), 'backups');
const RETENTION_DAYS = parseInt(process.env.BACKUP_RETENTION_DAYS || '30');
const S3_BUCKET = process.env.BACKUP_S3_BUCKET;

// Ensure backup directory exists
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
  console.log('✅ Backup directory created');
}

// Generate backup filename
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
const backupFile = path.join(BACKUP_DIR, `backup-${timestamp}.sql`);
const compressedFile = `${backupFile}.gz`;

// Parse database URL
function parseDatabaseUrl(url) {
  const dbUrl = new URL(url);
  return {
    host: dbUrl.hostname,
    port: dbUrl.port || 5432,
    username: dbUrl.username,
    password: dbUrl.password,
    database: dbUrl.pathname.slice(1)
  };
}

// Create database backup
async function createBackup() {
  console.log('🗄️ Creating database backup...');
  
  try {
    const dbConfig = parseDatabaseUrl(process.env.DATABASE_URL);
    
    // Create SQL dump
    const pgDumpCommand = [
      'pg_dump',
      `-h ${dbConfig.host}`,
      `-p ${dbConfig.port}`,
      `-U ${dbConfig.username}`,
      `-d ${dbConfig.database}`,
      '--verbose',
      '--clean',
      '--no-owner',
      '--no-privileges',
      `--file="${backupFile}"`
    ].join(' ');

    execSync(pgDumpCommand, {
      stdio: 'inherit',
      env: { ...process.env, PGPASSWORD: dbConfig.password }
    });

    console.log(`✅ SQL dump created: ${backupFile}`);

    // Compress backup
    console.log('🗜️ Compressing backup...');
    execSync(`gzip "${backupFile}"`, { stdio: 'inherit' });
    console.log(`✅ Backup compressed: ${compressedFile}`);

    return compressedFile;
  } catch (error) {
    console.error('❌ Backup creation failed:', error.message);
    throw error;
  }
}

// Verify backup integrity
async function verifyBackup(backupFile) {
  console.log('🔍 Verifying backup integrity...');
  
  try {
    // Check if file exists and has content
    const stats = fs.statSync(backupFile);
    if (stats.size === 0) {
      throw new Error('Backup file is empty');
    }

    // Test gzip integrity
    execSync(`gzip -t "${backupFile}"`, { stdio: 'pipe' });
    
    console.log(`✅ Backup verified (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
    return true;
  } catch (error) {
    console.error('❌ Backup verification failed:', error.message);
    return false;
  }
}

// Upload to S3 (if configured)
async function uploadToS3(backupFile) {
  if (!S3_BUCKET) {
    console.log('⏭️ S3 upload skipped (no bucket configured)');
    return;
  }

  console.log('☁️ Uploading backup to S3...');
  
  try {
    const fileName = path.basename(backupFile);
    const s3Key = `database-backups/${fileName}`;
    
    const awsCommand = [
      'aws s3 cp',
      `"${backupFile}"`,
      `s3://${S3_BUCKET}/${s3Key}`,
      '--storage-class STANDARD_IA'
    ].join(' ');

    execSync(awsCommand, { stdio: 'inherit' });
    console.log(`✅ Backup uploaded to S3: s3://${S3_BUCKET}/${s3Key}`);
  } catch (error) {
    console.error('❌ S3 upload failed:', error.message);
    // Don't fail the entire backup process for S3 issues
  }
}

// Clean old backups
async function cleanOldBackups() {
  console.log('🧹 Cleaning old backups...');
  
  try {
    const files = fs.readdirSync(BACKUP_DIR);
    const backupFiles = files
      .filter(file => file.startsWith('backup-') && file.endsWith('.sql.gz'))
      .map(file => ({
        name: file,
        path: path.join(BACKUP_DIR, file),
        mtime: fs.statSync(path.join(BACKUP_DIR, file)).mtime
      }))
      .sort((a, b) => b.mtime - a.mtime);

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - RETENTION_DAYS);

    let deletedCount = 0;
    for (const file of backupFiles) {
      if (file.mtime < cutoffDate) {
        fs.unlinkSync(file.path);
        deletedCount++;
        console.log(`🗑️ Deleted old backup: ${file.name}`);
      }
    }

    console.log(`✅ Cleanup completed (${deletedCount} files deleted)`);
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
  }
}

// Generate backup report
async function generateReport(backupFile, success) {
  const prisma = new PrismaClient();
  
  try {
    const stats = fs.statSync(backupFile);
    
    await prisma.backupLog.create({
      data: {
        fileName: path.basename(backupFile),
        filePath: backupFile,
        fileSize: stats.size,
        status: success ? 'SUCCESS' : 'FAILED',
        createdAt: new Date(),
        s3Uploaded: !!S3_BUCKET
      }
    });

    console.log('✅ Backup log recorded');
  } catch (error) {
    console.error('⚠️ Failed to record backup log:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// Send notification (if configured)
async function sendNotification(success, backupFile, error = null) {
  // This would integrate with your notification system
  // For now, just log the result
  
  if (success) {
    console.log('📧 Backup completed successfully');
    console.log(`   File: ${path.basename(backupFile)}`);
    console.log(`   Size: ${(fs.statSync(backupFile).size / 1024 / 1024).toFixed(2)} MB`);
  } else {
    console.error('📧 Backup failed');
    if (error) {
      console.error(`   Error: ${error.message}`);
    }
  }
}

// Main backup process
async function main() {
  let backupFile = null;
  let success = false;
  let error = null;

  try {
    // Create backup
    backupFile = await createBackup();
    
    // Verify backup
    const isValid = await verifyBackup(backupFile);
    if (!isValid) {
      throw new Error('Backup verification failed');
    }
    
    // Upload to S3
    await uploadToS3(backupFile);
    
    // Clean old backups
    await cleanOldBackups();
    
    success = true;
    console.log('\n🎉 Database backup completed successfully!');
    
  } catch (err) {
    error = err;
    console.error('\n❌ Database backup failed:', err.message);
  } finally {
    // Generate report
    if (backupFile) {
      await generateReport(backupFile, success);
    }
    
    // Send notification
    await sendNotification(success, backupFile, error);
  }

  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
