# Prometheus Configuration for Natural Stone Marketplace Monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'natural-stone-marketplace'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Backend API
  - job_name: 'backend-api'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Frontend Application
  - job_name: 'frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 30s

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Nginx Reverse Proxy
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: '/nginx_status'
    scrape_interval: 30s

  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # AI Marketing Specific Metrics
  - job_name: 'ai-marketing-learning'
    static_configs:
      - targets: ['ai-marketing-backend:3001']
    metrics_path: '/api/admin/ai-marketing/metrics/learning'
    scrape_interval: 60s
    scrape_timeout: 30s

  - job_name: 'ai-marketing-research'
    static_configs:
      - targets: ['ai-marketing-backend:3001']
    metrics_path: '/api/admin/ai-marketing/metrics/research'
    scrape_interval: 300s  # 5 minutes
    scrape_timeout: 30s

  - job_name: 'ai-marketing-optimization'
    static_configs:
      - targets: ['ai-marketing-backend:3001']
    metrics_path: '/api/admin/ai-marketing/metrics/optimization'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'ai-marketing-apis'
    static_configs:
      - targets: ['ai-marketing-backend:3001']
    metrics_path: '/api/admin/ai-marketing/metrics/apis'
    scrape_interval: 60s
    scrape_timeout: 30s

  # Custom Business Metrics
  - job_name: 'business-metrics'
    static_configs:
      - targets: ['ai-marketing-backend:3001']
    metrics_path: '/api/metrics/business'
    scrape_interval: 300s  # 5 minutes
    params:
      format: ['prometheus']

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "https://your-remote-storage-endpoint/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"

# Remote read configuration
# remote_read:
#   - url: "https://your-remote-storage-endpoint/api/v1/read"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"
