import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

const locales = ['tr', 'en', 'ar', 'zh', 'ru']

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  // Skip static files, API routes, and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.') ||
    pathname.startsWith('/static')
  ) {
    return NextResponse.next()
  }

  // Check if pathname already has a locale
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )

  // If pathname has locale, continue
  if (pathnameHasLocale) {
    return NextResponse.next()
  }

  // Get preferred language from cookie or header
  const preferredLocale = request.cookies.get('preferred-language')?.value ||
    request.headers.get('accept-language')?.split(',')[0]?.split('-')[0] ||
    'tr'

  // For root path without locale, continue (Turkish default)
  if (pathname === '/') {
    return NextResponse.next()
  }

  // For paths that should have locale support, redirect to localized version
  const pathsRequiringLocale = [
    '/customer',
    '/producer',
    '/admin',
    '/products',
    '/about',
    '/contact',
    '/news',
    '/3d-showroom'
  ]

  const shouldRedirect = pathsRequiringLocale.some(path =>
    pathname.startsWith(path)
  )

  if (shouldRedirect && preferredLocale !== 'tr') {
    const url = request.nextUrl.clone()
    url.pathname = `/${preferredLocale}${pathname}`
    return NextResponse.redirect(url)
  }

  // For other paths without locale, continue with current path
  return NextResponse.next()
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
