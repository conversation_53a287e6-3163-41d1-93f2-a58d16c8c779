'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BuildingStorefrontIcon,
  BellIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  HeartIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import { BellIcon as BellSolidIcon } from '@heroicons/react/24/solid';

interface StockNotification {
  id: string;
  supplier: string;
  product: string;
  category: string;
  quantity: number;
  unit: string;
  priceRange: { min: number; max: number };
  addedDate: Date;
  location: string;
  description?: string;
  isNew: boolean;
}

interface StockPageProps {
  onNavigate?: (route: string) => void;
}

const StockPage: React.FC<StockPageProps> = ({ onNavigate }) => {
  const [stockNotifications, setStockNotifications] = useState<StockNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    category: '',
    supplier: '',
    location: '',
    priceRange: { min: 0, max: 1000 },
    dateAdded: { from: '', to: '' }
  });
  const [notificationSettings, setNotificationSettings] = useState({
    emailAlerts: true,
    pushNotifications: true,
    categories: ['Mermer', 'Granit'],
    suppliers: ['ABC Mermer Ltd.']
  });

  // Mock data
  const mockStockNotifications: StockNotification[] = [
    {
      id: '1',
      supplier: 'ABC Mermer Ltd.',
      product: 'Premium Beyaz Mermer',
      category: 'Mermer',
      quantity: 500,
      unit: 'm²',
      priceRange: { min: 45, max: 65 },
      addedDate: new Date('2024-01-25'),
      location: 'Afyon',
      description: 'Yüksek kalite, düşük su emme oranı',
      isNew: true
    },
    {
      id: '2',
      supplier: 'XYZ Granit A.Ş.',
      product: 'Siyah Granit - Premium',
      category: 'Granit',
      quantity: 300,
      unit: 'm²',
      priceRange: { min: 55, max: 75 },
      addedDate: new Date('2024-01-24'),
      location: 'Muğla',
      description: 'Mutfak tezgahı için ideal',
      isNew: true
    },
    {
      id: '3',
      supplier: 'DEF Traverten',
      product: 'Bej Traverten - Doğal',
      category: 'Traverten',
      quantity: 200,
      unit: 'm²',
      priceRange: { min: 35, max: 50 },
      addedDate: new Date('2024-01-23'),
      location: 'Denizli',
      isNew: false
    },
    {
      id: '4',
      supplier: 'GHI Oniks',
      product: 'Yeşil Oniks - Lüks',
      category: 'Oniks',
      quantity: 50,
      unit: 'm²',
      priceRange: { min: 120, max: 180 },
      addedDate: new Date('2024-01-22'),
      location: 'Konya',
      description: 'Özel projeler için',
      isNew: false
    }
  ];

  useEffect(() => {
    setTimeout(() => {
      setStockNotifications(mockStockNotifications);
      setLoading(false);
    }, 1000);
  }, []);

  const handleRequestQuote = (stockId: string) => {
    const stock = stockNotifications.find(s => s.id === stockId);
    if (stock && onNavigate) {
      onNavigate(`/customer/requests/new?product=${stock.product}&supplier=${stock.supplier}`);
    }
  };

  const handleAddToFavorites = (stockId: string) => {
    console.log('Adding to favorites:', stockId);
    // Implement add to favorites functionality
  };

  const handleView3D = (stockId: string) => {
    console.log('View 3D for stock:', stockId);
    // Open 3D viewer modal or navigate to 3D viewer page
    alert(`3D Görüntüleyici açılıyor: ${stockId}`);
  };

  const markAsRead = (stockId: string) => {
    setStockNotifications(prev => 
      prev.map(stock => 
        stock.id === stockId ? { ...stock, isNew: false } : stock
      )
    );
  };

  const filteredNotifications = stockNotifications.filter(stock => {
    const matchesSearch = stock.product.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         stock.supplier.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !filters.category || stock.category === filters.category;
    const matchesSupplier = !filters.supplier || stock.supplier === filters.supplier;
    const matchesLocation = !filters.location || stock.location === filters.location;
    const matchesPrice = stock.priceRange.min >= filters.priceRange.min && 
                        stock.priceRange.max <= filters.priceRange.max;
    
    return matchesSearch && matchesCategory && matchesSupplier && matchesLocation && matchesPrice;
  });

  const newNotificationsCount = stockNotifications.filter(s => s.isNew).length;

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <BuildingStorefrontIcon className="h-8 w-8 text-orange-600 mr-3" />
              Stok Takibi
              {newNotificationsCount > 0 && (
                <span className="ml-3 bg-red-500 text-white text-sm font-medium px-2 py-1 rounded-full">
                  {newNotificationsCount} yeni
                </span>
              )}
            </h1>
            <p className="text-gray-600 mt-2">
              Üretici stok güncellemelerini takip edin ve fırsatları kaçırmayın
            </p>
          </div>
          
          <button className="bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition-colors flex items-center space-x-2">
            <BellSolidIcon className="h-5 w-5" />
            <span>Bildirim Ayarları</span>
          </button>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 mb-6">
          <div className="grid grid-cols-1 lg:grid-cols-6 gap-4">
            {/* Search */}
            <div className="lg:col-span-2 relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Ürün veya üretici ara..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <select
              value={filters.category}
              onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="">Tüm Kategoriler</option>
              <option value="Mermer">Mermer</option>
              <option value="Granit">Granit</option>
              <option value="Traverten">Traverten</option>
              <option value="Oniks">Oniks</option>
            </select>

            {/* Supplier Filter */}
            <select
              value={filters.supplier}
              onChange={(e) => setFilters(prev => ({ ...prev, supplier: e.target.value }))}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="">Tüm Üreticiler</option>
              <option value="ABC Mermer Ltd.">ABC Mermer Ltd.</option>
              <option value="XYZ Granit A.Ş.">XYZ Granit A.Ş.</option>
              <option value="DEF Traverten">DEF Traverten</option>
              <option value="GHI Oniks">GHI Oniks</option>
            </select>

            {/* Location Filter */}
            <select
              value={filters.location}
              onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="">Tüm Lokasyonlar</option>
              <option value="Afyon">Afyon</option>
              <option value="Muğla">Muğla</option>
              <option value="Denizli">Denizli</option>
              <option value="Konya">Konya</option>
            </select>

            {/* Clear Filters */}
            <button
              onClick={() => setFilters({
                category: '',
                supplier: '',
                location: '',
                priceRange: { min: 0, max: 1000 },
                dateAdded: { from: '', to: '' }
              })}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Filtreleri Temizle
            </button>
          </div>
        </div>

        {/* Notification Settings Summary */}
        <div className="bg-orange-50 border border-orange-200 rounded-xl p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <BellSolidIcon className="h-5 w-5 text-orange-600" />
              <div>
                <h4 className="font-medium text-orange-900">Bildirim Ayarları</h4>
                <p className="text-sm text-orange-700">
                  {notificationSettings.categories.length} kategori, {notificationSettings.suppliers.length} üretici takip ediliyor
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2 text-sm text-orange-700">
              {notificationSettings.emailAlerts && <span>📧 Email</span>}
              {notificationSettings.pushNotifications && <span>🔔 Push</span>}
            </div>
          </div>
        </div>
      </div>

      {/* Stock Notifications */}
      {filteredNotifications.length > 0 ? (
        <div className="space-y-4">
          {filteredNotifications.map((stock, index) => (
            <motion.div
              key={stock.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`bg-white rounded-xl shadow-sm border-2 p-6 hover:shadow-md transition-all duration-200 ${
                stock.isNew ? 'border-orange-200 bg-orange-50' : 'border-gray-200'
              }`}
              onClick={() => markAsRead(stock.id)}
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {stock.isNew && (
                    <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                  )}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{stock.product}</h3>
                    <p className="text-sm text-gray-600">{stock.supplier}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                    {stock.category}
                  </span>
                  {stock.isNew && (
                    <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium">
                      YENİ
                    </span>
                  )}
                </div>
              </div>

              {/* Details */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div className="flex items-center space-x-2">
                  <BuildingStorefrontIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">{stock.quantity} {stock.unit}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <MapPinIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">{stock.location}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <CalendarIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {stock.addedDate.toLocaleDateString('tr-TR')}
                  </span>
                </div>
              </div>

              {/* Description */}
              {stock.description && (
                <div className="mb-4">
                  <p className="text-gray-700 text-sm">{stock.description}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRequestQuote(stock.id);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                >
                  <ClipboardDocumentListIcon className="h-4 w-4" />
                  <span>Teklif İste</span>
                </button>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleView3D(stock.id);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <span>🔄 3D</span>
                </button>

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddToFavorites(stock.id);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <HeartIcon className="h-4 w-4" />
                  <span>Favorilere Ekle</span>
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <BuildingStorefrontIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchQuery || filters.category || filters.supplier || filters.location
              ? 'Arama kriterlerinize uygun stok bildirimi bulunamadı'
              : 'Henüz stok bildirimi yok'
            }
          </h3>
          <p className="text-gray-600 mb-6">
            {searchQuery || filters.category || filters.supplier || filters.location
              ? 'Farklı filtreler deneyebilir veya arama teriminizi değiştirebilirsiniz.'
              : 'Üreticiler yeni stok eklediğinde burada bildirimler görünecek.'
            }
          </p>
          <button
            onClick={() => onNavigate?.('/products')}
            className="bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition-colors"
          >
            Ürünleri Keşfet
          </button>
        </div>
      )}
    </div>
  );
};

export default StockPage;
