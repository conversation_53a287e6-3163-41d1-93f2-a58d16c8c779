'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Brain, 
  Search, 
  Target, 
  Zap, 
  Database, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Activity,
  BarChart3,
  Settings,
  RefreshCw
} from 'lucide-react';

interface SystemStats {
  adaptiveLearning: {
    learningCycles: number;
    patternsDiscovered: number;
    successRate: number;
    lastCycle: string;
  };
  continuousResearch: {
    researchCycles: number;
    insightsGenerated: number;
    trendsIdentified: number;
    lastResearch: string;
  };
  dynamicStrategy: {
    strategiesGenerated: number;
    performanceImprovement: number;
    activeStrategies: number;
    lastEvolution: string;
  };
  realTimeOptimizer: {
    optimizationActions: number;
    alertsGenerated: number;
    performanceGains: number;
    lastOptimization: string;
  };
  knowledgeBase: {
    knowledgeEntries: number;
    expertiseAreas: number;
    confidenceScore: number;
    lastUpdate: string;
  };
  systemHealth: {
    overall: boolean;
    tradeMap: boolean;
    linkedIn: boolean;
    googleAds: boolean;
    database: boolean;
  };
}

interface Alert {
  id: string;
  severity: 'critical' | 'warning' | 'info';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

export default function AIMarketingDashboard() {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    fetchSystemStats();
    fetchAlerts();

    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchSystemStats();
        fetchAlerts();
      }, 30000); // 30 saniye

      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const fetchSystemStats = async () => {
    try {
      const response = await fetch('/api/admin/ai-marketing/stats');
      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch system stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAlerts = async () => {
    try {
      const response = await fetch('/api/admin/ai-marketing/alerts');
      const data = await response.json();
      setAlerts(data);
    } catch (error) {
      console.error('Failed to fetch alerts:', error);
    }
  };

  const acknowledgeAlert = async (alertId: string) => {
    try {
      await fetch(`/api/admin/ai-marketing/alerts/${alertId}/acknowledge`, {
        method: 'POST'
      });
      setAlerts(alerts.map(alert => 
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      ));
    } catch (error) {
      console.error('Failed to acknowledge alert:', error);
    }
  };

  const getHealthStatusColor = (status: boolean) => {
    return status ? 'text-green-600' : 'text-red-600';
  };

  const getHealthStatusIcon = (status: boolean) => {
    return status ? <CheckCircle className="h-4 w-4" /> : <AlertTriangle className="h-4 w-4" />;
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'warning': return 'default';
      case 'info': return 'secondary';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-6 w-6 animate-spin" />
          <span>Loading AI Marketing Dashboard...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">🧠 Self-Learning AI Marketing Dashboard</h1>
          <p className="text-muted-foreground">
            Real-time monitoring of adaptive AI marketing system
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Activity className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-pulse' : ''}`} />
            Auto Refresh: {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              fetchSystemStats();
              fetchAlerts();
            }}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Health Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            System Health Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="flex items-center space-x-2">
              <span className={getHealthStatusColor(stats?.systemHealth.overall || false)}>
                {getHealthStatusIcon(stats?.systemHealth.overall || false)}
              </span>
              <span className="text-sm font-medium">Overall</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={getHealthStatusColor(stats?.systemHealth.tradeMap || false)}>
                {getHealthStatusIcon(stats?.systemHealth.tradeMap || false)}
              </span>
              <span className="text-sm">Trade Map API</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={getHealthStatusColor(stats?.systemHealth.linkedIn || false)}>
                {getHealthStatusIcon(stats?.systemHealth.linkedIn || false)}
              </span>
              <span className="text-sm">LinkedIn API</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={getHealthStatusColor(stats?.systemHealth.googleAds || false)}>
                {getHealthStatusIcon(stats?.systemHealth.googleAds || false)}
              </span>
              <span className="text-sm">Google Ads API</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={getHealthStatusColor(stats?.systemHealth.database || false)}>
                {getHealthStatusIcon(stats?.systemHealth.database || false)}
              </span>
              <span className="text-sm">Database</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alerts */}
      {alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Active Alerts ({alerts.filter(a => !a.acknowledged).length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {alerts.filter(a => !a.acknowledged).slice(0, 5).map((alert) => (
                <Alert key={alert.id}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle className="flex items-center justify-between">
                    <span>{alert.severity.toUpperCase()}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => acknowledgeAlert(alert.id)}
                    >
                      Acknowledge
                    </Button>
                  </AlertTitle>
                  <AlertDescription>
                    {alert.message}
                    <div className="text-xs text-muted-foreground mt-1">
                      {new Date(alert.timestamp).toLocaleString()}
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="learning">Learning</TabsTrigger>
          <TabsTrigger value="research">Research</TabsTrigger>
          <TabsTrigger value="strategy">Strategy</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
          <TabsTrigger value="knowledge">Knowledge</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Adaptive Learning */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Adaptive Learning</CardTitle>
                <Brain className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.adaptiveLearning.learningCycles || 0}</div>
                <p className="text-xs text-muted-foreground">Learning cycles completed</p>
                <div className="mt-2">
                  <div className="flex justify-between text-xs">
                    <span>Success Rate</span>
                    <span>{stats?.adaptiveLearning.successRate || 0}%</span>
                  </div>
                  <Progress value={stats?.adaptiveLearning.successRate || 0} className="mt-1" />
                </div>
              </CardContent>
            </Card>

            {/* Continuous Research */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Research Engine</CardTitle>
                <Search className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.continuousResearch.insightsGenerated || 0}</div>
                <p className="text-xs text-muted-foreground">Insights generated</p>
                <div className="mt-2 text-xs text-muted-foreground">
                  {stats?.continuousResearch.trendsIdentified || 0} trends identified
                </div>
              </CardContent>
            </Card>

            {/* Dynamic Strategy */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Strategy Evolution</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.dynamicStrategy.strategiesGenerated || 0}</div>
                <p className="text-xs text-muted-foreground">Strategies generated</p>
                <div className="mt-2">
                  <Badge variant="secondary">
                    +{stats?.dynamicStrategy.performanceImprovement || 0}% improvement
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Real-time Optimizer */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Real-time Optimizer</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.realTimeOptimizer.optimizationActions || 0}</div>
                <p className="text-xs text-muted-foreground">Optimization actions</p>
                <div className="mt-2 text-xs text-muted-foreground">
                  {stats?.realTimeOptimizer.alertsGenerated || 0} alerts generated
                </div>
              </CardContent>
            </Card>

            {/* Knowledge Base */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Knowledge Base</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.knowledgeBase.knowledgeEntries || 0}</div>
                <p className="text-xs text-muted-foreground">Knowledge entries</p>
                <div className="mt-2">
                  <div className="flex justify-between text-xs">
                    <span>Confidence</span>
                    <span>{stats?.knowledgeBase.confidenceScore || 0}%</span>
                  </div>
                  <Progress value={stats?.knowledgeBase.confidenceScore || 0} className="mt-1" />
                </div>
              </CardContent>
            </Card>

            {/* Performance Trends */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Performance Trends</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">↗ +24%</div>
                <p className="text-xs text-muted-foreground">Overall improvement</p>
                <div className="mt-2 text-xs text-muted-foreground">
                  Last 30 days
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Learning Tab */}
        <TabsContent value="learning">
          <Card>
            <CardHeader>
              <CardTitle>Adaptive Learning Engine Details</CardTitle>
              <CardDescription>
                Detailed view of learning patterns and performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Learning details component will be implemented here
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Research Tab */}
        <TabsContent value="research">
          <Card>
            <CardHeader>
              <CardTitle>Continuous Research Module</CardTitle>
              <CardDescription>
                Market research insights and trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Research details component will be implemented here
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Strategy Tab */}
        <TabsContent value="strategy">
          <Card>
            <CardHeader>
              <CardTitle>Dynamic Strategy Generator</CardTitle>
              <CardDescription>
                Strategy evolution and performance tracking
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Strategy details component will be implemented here
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Optimization Tab */}
        <TabsContent value="optimization">
          <Card>
            <CardHeader>
              <CardTitle>Real-time Optimizer</CardTitle>
              <CardDescription>
                Real-time performance optimization and alerts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Optimization details component will be implemented here
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Knowledge Tab */}
        <TabsContent value="knowledge">
          <Card>
            <CardHeader>
              <CardTitle>Knowledge Base Evolution</CardTitle>
              <CardDescription>
                Knowledge entries and expertise areas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Knowledge base details component will be implemented here
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
