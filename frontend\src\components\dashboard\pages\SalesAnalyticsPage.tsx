'use client';

import React, { useState } from 'react';
import { ChartBarIcon, PencilIcon, TrashIcon, EyeIcon } from '@heroicons/react/24/outline';

interface SalesAnalyticsPageProps {
  onNavigate?: (route: string) => void;
}

// Gerçek veriler API'den gelecek - şimdilik boş array
const mockSales: any[] = [];

const SalesAnalyticsPage: React.FC<SalesAnalyticsPageProps> = ({ onNavigate }) => {
  const [sales, setSales] = useState(mockSales);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('date');

  // Filtreleme ve arama
  const filteredSales = sales.filter(sale => {
    const matchesSearch = sale.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.orderNumber.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterStatus === 'all' || sale.paymentStatus === filterStatus;

    return matchesSearch && matchesFilter;
  });

  // Sıralama
  const sortedSales = [...filteredSales].sort((a, b) => {
    switch (sortBy) {
      case 'date':
        return new Date(b.saleDate).getTime() - new Date(a.saleDate).getTime();
      case 'price':
        return b.salePrice - a.salePrice;
      case 'company':
        return a.companyName.localeCompare(b.companyName);
      default:
        return 0;
    }
  });

  const handleDelete = (id: number) => {
    if (confirm('Bu satış kaydını silmek istediğinizden emin misiniz?')) {
      setSales(sales.filter(sale => sale.id !== id));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Ödendi': return 'bg-green-100 text-green-800';
      case 'Kısmi Ödendi': return 'bg-yellow-100 text-yellow-800';
      case 'Bekliyor': return 'bg-gray-100 text-gray-800';
      case 'Gecikmiş': return 'bg-red-100 text-red-800';
      case 'Teslim Edildi': return 'bg-green-100 text-green-800';
      case 'Hazırlanıyor': return 'bg-blue-100 text-blue-800';
      case 'Kargoda': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const totalSales = sales.reduce((sum, sale) => sum + sale.salePrice, 0);
  const completedSales = sales.filter(sale => sale.deliveryStatus === 'Teslim Edildi').length;
  const pendingSales = sales.filter(sale => sale.paymentStatus === 'Bekliyor').length;
  if (sales.length === 0) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Satış Analizi</h1>
            <p className="text-gray-600 mt-1">Satış performansınızı analiz edin ve trendleri takip edin</p>
          </div>
          <button
            onClick={() => onNavigate?.('/customer/analytics/sales/add')}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <span>+</span>
            <span>Satış Ekle</span>
          </button>
        </div>

        {/* Empty State */}
        <div className="text-center py-12">
          <ChartBarIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Satış verisi bulunmuyor
          </h3>
          <p className="text-gray-600 mb-6">
            İlk satışınızı ekleyerek analizlere başlayın.
          </p>
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => onNavigate?.('/customer/analytics/sales/add')}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              İlk Satışı Ekle
            </button>
            <button
              onClick={() => onNavigate?.('/customer/dashboard')}
              className="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Dashboard'a Dön
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Satış Analizi</h1>
          <p className="text-gray-600 mt-1">Satış performansınızı analiz edin ve trendleri takip edin</p>
        </div>
        <button
          onClick={() => onNavigate?.('/customer/analytics/sales/add')}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <span>+</span>
          <span>Satış Ekle</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <span className="text-green-600 text-xl">💰</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Toplam Satış</p>
              <p className="text-2xl font-bold text-gray-900">${totalSales.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <span className="text-blue-600 text-xl">📦</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Toplam Sipariş</p>
              <p className="text-2xl font-bold text-gray-900">{sales.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <span className="text-green-600 text-xl">✅</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Teslim Edildi</p>
              <p className="text-2xl font-bold text-gray-900">{completedSales}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <span className="text-yellow-600 text-xl">⏳</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Bekleyen Ödeme</p>
              <p className="text-2xl font-bold text-gray-900">{pendingSales}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">

          {/* Search */}
          <div className="flex-1">
            <input
              type="text"
              placeholder="Ürün, firma veya sipariş numarası ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Filters */}
          <div className="flex space-x-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">Tüm Ödemeler</option>
              <option value="Ödendi">Ödendi</option>
              <option value="Kısmi Ödendi">Kısmi Ödendi</option>
              <option value="Bekliyor">Bekliyor</option>
              <option value="Gecikmiş">Gecikmiş</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="date">Tarihe Göre</option>
              <option value="price">Fiyata Göre</option>
              <option value="company">Firmaya Göre</option>
            </select>
          </div>
        </div>
      </div>

      {/* Sales Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ürün
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Firma
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ebat & Miktar
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fiyat
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ödeme Durumu
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Teslimat
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tarih
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedSales.map((sale) => (
                <tr key={sale.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{sale.productName}</div>
                      <div className="text-sm text-gray-500">{sale.category} • {sale.surfaceFinish}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{sale.companyName}</div>
                    <div className="text-sm text-gray-500">{sale.orderNumber}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{sale.dimensions}</div>
                    <div className="text-sm text-gray-500">{sale.quantity} m²</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      ${sale.salePrice.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500">{sale.paymentMethod}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(sale.paymentStatus)}`}>
                      {sale.paymentStatus}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(sale.deliveryStatus)}`}>
                      {sale.deliveryStatus}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(sale.saleDate).toLocaleDateString('tr-TR')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => onNavigate?.(`/customer/analytics/sales/view/${sale.id}`)}
                        className="text-blue-600 hover:text-blue-900 p-1"
                        title="Görüntüle"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => onNavigate?.(`/customer/analytics/sales/edit/${sale.id}`)}
                        className="text-indigo-600 hover:text-indigo-900 p-1"
                        title="Düzenle"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(sale.id)}
                        className="text-red-600 hover:text-red-900 p-1"
                        title="Sil"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* No Results */}
        {sortedSales.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">Arama kriterlerinize uygun satış bulunamadı.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SalesAnalyticsPage;
