'use client'

import * as React from 'react'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { useQuote } from '@/contexts/quote-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  FileText,
  DollarSign,
  Calendar,
  User,
  Package,
  MessageSquare,
  Edit
} from 'lucide-react'
import { MessageModal } from '@/components/ui/message-modal'
import { RequestDetailsModal } from '@/components/ui/request-details-modal'



export default function QuotedRequests() {
  const { producer } = useProducerAuth()
  const { quoteRequests, quotes, isLoading } = useQuote()
  const [selectedRequest, setSelectedRequest] = React.useState<any>(null)
  const [isMessageModalOpen, setIsMessageModalOpen] = React.useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = React.useState(false)

  // Get quoted requests for this producer
  const quotedRequests = React.useMemo(() => {
    if (!producer?.id) return []

    // Get all requests where this producer has submitted a quote
    const requestsWithMyQuotes = quoteRequests.filter(request => {
      const hasMyQuote = quotes.some(quote =>
        quote.quoteRequestId === request.id &&
        quote.producerId === producer.id
      )
      return hasMyQuote
    })

    return requestsWithMyQuotes
  }, [quoteRequests, quotes, producer?.id])

  const handleSendMessage = (request: any) => {
    setSelectedRequest(request)
    setIsMessageModalOpen(true)
  }

  const handleViewDetails = (request: any) => {
    setSelectedRequest(request)
    setIsDetailsModalOpen(true)
  }

  const handleUpdateQuote = (request: any) => {
    // Navigate to quote update page or open modal
    console.log('Update quote for request:', request.id)
    alert('Teklif güncelleme özelliği yakında eklenecek!')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Teklif Verildi</h1>
        <p className="text-gray-600">
          Teklif verdiğiniz ve müşteri yanıtı beklenen talepler
        </p>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Quoted Requests List */}
      <div className="space-y-4">
        {quotedRequests.map((request) => {
          // Get the quote for this request from this producer
          const myQuote = quotes.find(quote =>
            quote.quoteRequestId === request.id &&
            quote.producerId === producer?.id
          )

          return (
          <Card key={request.id} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-blue-100">
                      <FileText className="w-4 h-4" />
                      Teklif Verildi
                    </div>
                    <span className="text-sm text-gray-500">#{request.id}</span>
                    <span className="text-xs text-gray-600">
                      Teklif tarihi: {myQuote?.createdAt.toLocaleDateString('tr-TR')}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <User className="w-4 h-4" />
                        Müşteri
                      </div>
                      <div className="font-medium">{request.customerName}</div>
                      <div className="text-sm text-gray-500">{request.customerEmail}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <Package className="w-4 h-4" />
                        Ürün
                      </div>
                      <div className="font-medium">{request.products?.length || 0} ürün</div>
                      <div className="text-sm text-gray-500">
                        {request.products?.reduce((total: number, product: any) =>
                          total + product.specifications.reduce((sum: number, spec: any) => sum + parseFloat(spec.area || 0), 0), 0
                        ).toFixed(2) || 0} m² toplam
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <DollarSign className="w-4 h-4" />
                        Fiyat Karşılaştırması
                      </div>
                      <div className="font-medium">Teklif Tutarı</div>
                      <div className="text-sm text-green-600 font-medium">
                        {myQuote?.totalAmount.toFixed(2)} {myQuote?.currency}
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <Calendar className="w-4 h-4" />
                        Geçerlilik
                      </div>
                      <div className="font-medium">
                        {myQuote?.validUntil.toLocaleDateString('tr-TR')}
                      </div>
                      <div className="text-sm text-gray-500">
                        Durum: {myQuote?.status === 'pending' ? 'Bekliyor' : myQuote?.status}
                      </div>
                    </div>
                  </div>

                  {/* My Quote Details */}
                  <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="font-medium text-blue-800 mb-2">Verdiğim Teklif</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-blue-600">Toplam Tutar:</span>
                        <span className="ml-2 font-medium">{myQuote?.totalAmount.toFixed(2)} {myQuote?.currency}</span>
                      </div>
                      <div>
                        <span className="text-blue-600">Ürün Sayısı:</span>
                        <span className="ml-2 font-medium">{myQuote?.items.length} ürün</span>
                      </div>
                      <div>
                        <span className="text-blue-600">Geçerlilik:</span>
                        <span className="ml-2 font-medium">{myQuote?.validUntil.toLocaleDateString('tr-TR')}</span>
                      </div>
                    </div>
                    {myQuote?.terms && (
                      <div className="mt-2">
                        <span className="text-blue-600 text-sm">Şartlar:</span>
                        <p className="text-sm text-blue-700 mt-1">{myQuote.terms}</p>
                      </div>
                    )}
                  </div>

                  {/* Request Message */}
                  {request.message && (
                    <div className="mb-4">
                      <p className="text-sm text-gray-600 mb-2">Müşteri Mesajı:</p>
                      <p className="text-sm bg-gray-50 p-3 rounded">{request.message}</p>
                    </div>
                  )}

                  {/* Product Details */}
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-gray-900 mb-2">Ürün Detayları</h5>
                    <div className="space-y-2">
                      {request.products.map((product: any) => (
                        <div key={product.id} className="bg-gray-50 rounded p-3">
                          <div className="font-medium text-sm">{product.productName}</div>
                          <div className="text-xs text-gray-600">{product.productCategory}</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {product.specifications?.length || 0} farklı ebat
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col gap-2 ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleUpdateQuote(request)}
                  >
                    <Edit className="w-4 h-4 mr-1" />
                    Teklifi Güncelle
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSendMessage(request)}
                  >
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Mesaj Gönder
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetails(request)}
                  >
                    Detayları Gör
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
          )
        })}
      </div>

      {/* Empty State */}
      {!isLoading && quotedRequests.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Teklif verilmiş talep bulunamadı
            </h3>
            <p className="text-gray-600">
              Henüz teklif verdiğiniz talep bulunmuyor.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Message Modal */}
      {isMessageModalOpen && selectedRequest && (
        <MessageModal
          isOpen={isMessageModalOpen}
          onClose={() => {
            setIsMessageModalOpen(false)
            setSelectedRequest(null)
          }}
          request={selectedRequest}
        />
      )}

      {/* Details Modal */}
      {isDetailsModalOpen && selectedRequest && (
        <RequestDetailsModal
          isOpen={isDetailsModalOpen}
          onClose={() => {
            setIsDetailsModalOpen(false)
            setSelectedRequest(null)
          }}
          request={selectedRequest}
        />
      )}
    </div>
  )
}
