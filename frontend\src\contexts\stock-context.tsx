'use client'

import React, { createContext, useContext, useState } from 'react'

interface StockItem {
  id: string
  productId: string
  productName: string
  producerName: string
  image?: { file?: File; url?: string }
  metraj: number
  thickness: number
  width: number
  length: number
  price: number
  currency: 'USD' | 'EUR' | 'TL'
  status: 'pending' | 'approved' | 'rejected'
  submittedAt: Date
  reviewedAt?: Date
  reviewedBy?: string
  rejectionReason?: string
}

interface StockContextType {
  stockItems: StockItem[]
  addStockItems: (productId: string, productName: string, producerName: string, items: Omit<StockItem, 'id' | 'productId' | 'productName' | 'producerName' | 'status' | 'submittedAt'>[]) => void
  approveStockItem: (id: string, reviewedBy: string) => void
  rejectStockItem: (id: string, reason: string, reviewedBy: string) => void
  getPendingStockItems: () => StockItem[]
  getApprovedStockItems: () => StockItem[]
  getStockItemsByProduct: (productId: string) => StockItem[]
}

const StockContext = createContext<StockContextType | undefined>(undefined)

// Gerçek veriler API'den gelecek - şimdilik boş array
const initialStockItems: StockItem[] = [];

export function StockProvider({ children }: { children: React.ReactNode }) {
  const [stockItems, setStockItems] = useState<StockItem[]>(initialStockItems)

  const addStockItems = (
    productId: string, 
    productName: string, 
    producerName: string, 
    items: Omit<StockItem, 'id' | 'productId' | 'productName' | 'producerName' | 'status' | 'submittedAt'>[]
  ) => {
    const newStockItems = items.map(item => ({
      ...item,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      productId,
      productName,
      producerName,
      status: 'pending' as const,
      submittedAt: new Date()
    }))

    setStockItems(prev => [...prev, ...newStockItems])
  }

  const approveStockItem = (id: string, reviewedBy: string) => {
    setStockItems(prev => prev.map(item => 
      item.id === id 
        ? { 
            ...item, 
            status: 'approved' as const, 
            reviewedAt: new Date(), 
            reviewedBy 
          } 
        : item
    ))
  }

  const rejectStockItem = (id: string, reason: string, reviewedBy: string) => {
    setStockItems(prev => prev.map(item => 
      item.id === id 
        ? { 
            ...item, 
            status: 'rejected' as const, 
            reviewedAt: new Date(), 
            reviewedBy,
            rejectionReason: reason
          } 
        : item
    ))
  }

  const getPendingStockItems = () => {
    return stockItems.filter(item => item.status === 'pending')
  }

  const getApprovedStockItems = () => {
    return stockItems.filter(item => item.status === 'approved')
  }

  const getStockItemsByProduct = (productId: string) => {
    return stockItems.filter(item => item.productId === productId && item.status === 'approved')
  }

  const value: StockContextType = {
    stockItems,
    addStockItems,
    approveStockItem,
    rejectStockItem,
    getPendingStockItems,
    getApprovedStockItems,
    getStockItemsByProduct
  }

  return (
    <StockContext.Provider value={value}>
      {children}
    </StockContext.Provider>
  )
}

export function useStock() {
  const context = useContext(StockContext)
  if (context === undefined) {
    throw new Error('useStock must be used within a StockProvider')
  }
  return context
}
