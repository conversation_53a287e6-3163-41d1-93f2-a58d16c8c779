'use client'

import * as React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Package,
  User,
  Calendar,
  DollarSign,
  MapPin,
  Phone,
  Mail,
  FileText,
  Truck,
  Clock,
  Layers,
  TrendingUp
} from 'lucide-react'
import { MultiDeliveryDashboard } from './multi-delivery-dashboard'
import { mockMultiDeliveryOrder } from '@/data/mock-multi-delivery'

interface OrderDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  order: any
}

export function OrderDetailsModal({
  isOpen,
  onClose,
  order
}: OrderDetailsModalProps) {
  const [activeTab, setActiveTab] = React.useState<'details' | 'multi-delivery'>('details')
  const [multiDeliveryOrder, setMultiDeliveryOrder] = React.useState(mockMultiDeliveryOrder)

  if (!order) return null

  // Check if this order should have multi-delivery (based on quantity or amount)
  const shouldShowMultiDelivery = order.quantity > 500 || order.totalAmount > 25000

  const handleUpdateStage = (packageId: string, stageId: string, status: any) => {
    console.log('Updating stage:', { packageId, stageId, status })
    // Update logic here
  }

  const handlePauseProduction = (packageId: string) => {
    console.log('Pausing production:', packageId)
    // Pause logic here
  }

  const handleViewPackageDetails = (packageId: string) => {
    console.log('Viewing package details:', packageId)
    // View details logic here
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'production':
        return 'bg-blue-100 text-blue-800'
      case 'shipped':
        return 'bg-purple-100 text-purple-800'
      case 'delivered':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor'
      case 'paid':
        return 'Ödendi'
      case 'production':
        return 'Üretimde'
      case 'shipped':
        return 'Kargoda'
      case 'delivered':
        return 'Teslim Edildi'
      default:
        return status
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Sipariş Detayları - #{order.id}
          </DialogTitle>
        </DialogHeader>

        {/* Tab Navigation */}
        {shouldShowMultiDelivery && (
          <div className="flex gap-4 border-b">
            <Button
              variant="ghost"
              className={`pb-2 ${activeTab === 'details' ? 'border-b-2 border-blue-600 text-blue-600' : ''}`}
              onClick={() => setActiveTab('details')}
            >
              <FileText className="w-4 h-4 mr-2" />
              Sipariş Detayları
            </Button>
            <Button
              variant="ghost"
              className={`pb-2 ${activeTab === 'multi-delivery' ? 'border-b-2 border-blue-600 text-blue-600' : ''}`}
              onClick={() => setActiveTab('multi-delivery')}
            >
              <Layers className="w-4 h-4 mr-2" />
              Çoklu Teslimat Yönetimi
            </Button>
          </div>
        )}

        <div className="space-y-6">
          {activeTab === 'details' ? (
            <>
              {/* Order Status */}
              <div className="flex items-center gap-3">
            <Badge className={getStatusColor(order.status)}>
              {getStatusText(order.status)}
            </Badge>
            {order.paymentStatus && (
              <Badge className={order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                {order.paymentStatus === 'paid' ? 'Ödendi' : 'Ödeme Bekliyor'}
              </Badge>
            )}
          </div>

          {/* Customer Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <User className="w-4 h-4" />
              Müşteri Bilgileri
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-500" />
                <span>{order.customerName}</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-500" />
                <span>{order.customerEmail}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-gray-500" />
                <span>{order.customerPhone}</span>
              </div>
              {order.customerAddress && (
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-gray-500" />
                  <span>{order.customerAddress}</span>
                </div>
              )}
            </div>
          </div>

          {/* Product Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <Package className="w-4 h-4" />
              Ürün Bilgileri
            </h3>
            <div className="space-y-3">
              <div>
                <p className="font-medium">{order.productName}</p>
                <p className="text-sm text-gray-600">{order.productDescription}</p>
              </div>
              
              {order.specifications && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                  <div>
                    <span className="text-gray-500">Kalınlık:</span>
                    <p className="font-medium">{order.specifications.thickness}cm</p>
                  </div>
                  <div>
                    <span className="text-gray-500">En:</span>
                    <p className="font-medium">{order.specifications.width}cm</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Boy:</span>
                    <p className="font-medium">{order.specifications.length}cm</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Miktar:</span>
                    <p className="font-medium">{order.quantity} m²</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Order Details */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Sipariş Detayları
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <span>Sipariş Tarihi: {order.orderDate}</span>
              </div>
              <div className="flex items-center gap-2">
                <DollarSign className="w-4 h-4 text-gray-500" />
                <span>Toplam: ${order.totalAmount}</span>
              </div>
              {order.deliveryDate && (
                <div className="flex items-center gap-2">
                  <Truck className="w-4 h-4 text-gray-500" />
                  <span>Teslimat Tarihi: {order.deliveryDate}</span>
                </div>
              )}
              {order.estimatedDelivery && (
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span>Tahmini Teslimat: {order.estimatedDelivery}</span>
                </div>
              )}
            </div>
          </div>

          {/* Special Notes */}
          {order.notes && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Özel Notlar
              </h3>
              <p className="text-sm text-gray-700">{order.notes}</p>
            </div>
          )}

            </>
          ) : (
            /* Multi Delivery Tab */
            <div className="space-y-4">
              {shouldShowMultiDelivery ? (
                <MultiDeliveryDashboard
                  order={multiDeliveryOrder}
                  onUpdateStage={handleUpdateStage}
                  onPauseProduction={handlePauseProduction}
                  onViewPackageDetails={handleViewPackageDetails}
                />
              ) : (
                <div className="text-center py-8">
                  <Layers className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Çoklu Teslimat Mevcut Değil</h3>
                  <p className="text-gray-600">
                    Bu sipariş çoklu teslimat için uygun değil. Büyük metrajlı siparişler için çoklu teslimat sistemi kullanılır.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Kapat
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
