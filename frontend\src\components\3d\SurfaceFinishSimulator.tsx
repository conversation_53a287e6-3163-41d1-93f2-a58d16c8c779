'use client';

import React, { useState, useEffect, useRef } from 'react';
import { 
  Pa<PERSON>, 
  <PERSON>rk<PERSON>, 
  Eye, 
  RotateCw,
  Info,
  Zap,
  Brush,
  Gem
} from 'lucide-react';
import { 
  SurfaceFinishConfiguration, 
  SurfaceFinishType, 
  SurfaceFinishName,
  AdvancedProductConfig 
} from '../../types/3d';

interface SurfaceFinishSimulatorProps {
  productId: string;
  configuration: SurfaceFinishConfiguration;
  currentConfig: AdvancedProductConfig;
  onConfigChange: (config: Partial<AdvancedProductConfig>) => void;
  onMaterialUpdate: (materialProps: MaterialProperties) => void;
  className?: string;
}

interface MaterialProperties {
  roughness: number;
  metallic: number;
  normalIntensity: number;
  displacementScale: number;
  emissive: number;
}

export const SurfaceFinishSimulator: React.FC<SurfaceFinishSimulatorProps> = ({
  productId,
  configuration,
  currentConfig,
  onConfigChange,
  onMaterialUpdate,
  className = ''
}) => {
  const [selectedFinish, setSelectedFinish] = useState<SurfaceFinishName>(currentConfig.surfaceFinish);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [previewMode, setPreviewMode] = useState<'grid' | 'detail'>('grid');
  const [showComparison, setShowComparison] = useState(false);
  const [comparisonFinishes, setComparisonFinishes] = useState<SurfaceFinishName[]>([]);

  // Get current finish configuration
  const getCurrentFinish = () => {
    return configuration.availableFinishes.find(f => f.name === selectedFinish) ||
           configuration.availableFinishes[0];
  };

  // Handle finish selection with animation
  const handleFinishSelect = async (finishName: SurfaceFinishName) => {
    if (finishName === selectedFinish || isTransitioning) return;

    setIsTransitioning(true);
    
    try {
      // Start transition animation
      const finish = configuration.availableFinishes.find(f => f.name === finishName);
      if (!finish) return;

      // Update material properties with transition
      await animateTransition(getCurrentFinish(), finish);
      
      setSelectedFinish(finishName);
      onConfigChange({ surfaceFinish: finishName });
      
    } catch (error) {
      console.error('Surface finish transition failed:', error);
    } finally {
      setIsTransitioning(false);
    }
  };

  // Animate transition between surface finishes
  const animateTransition = async (fromFinish: SurfaceFinishType, toFinish: SurfaceFinishType) => {
    const { duration, steps } = configuration.transitionAnimation;
    const stepDuration = duration / steps;

    for (let i = 0; i <= steps; i++) {
      const progress = i / steps;
      const easedProgress = easeInOutCubic(progress);

      const interpolatedProps: MaterialProperties = {
        roughness: lerp(fromFinish.roughness, toFinish.roughness, easedProgress),
        metallic: lerp(fromFinish.metallic, toFinish.metallic, easedProgress),
        normalIntensity: lerp(fromFinish.normalIntensity, toFinish.normalIntensity, easedProgress),
        displacementScale: lerp(0.1, 0.3, easedProgress),
        emissive: 0
      };

      onMaterialUpdate(interpolatedProps);
      
      if (i < steps) {
        await new Promise(resolve => setTimeout(resolve, stepDuration));
      }
    }
  };

  // Easing function
  const easeInOutCubic = (t: number): number => {
    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
  };

  // Linear interpolation
  const lerp = (start: number, end: number, progress: number): number => {
    return start + (end - start) * progress;
  };

  // Get finish icon
  const getFinishIcon = (finishName: SurfaceFinishName) => {
    const icons = {
      'ham': Brush,
      'honlu': Sparkles,
      'cilali': Gem,
      'fircinlanmis': Brush,
      'yakma': Zap,
      'eskitme': RotateCw,
      'kumlama': Brush,
      'dolgu': Palette
    };
    return icons[finishName] || Brush;
  };

  // Get finish color theme
  const getFinishColorTheme = (finishName: SurfaceFinishName) => {
    const themes = {
      'ham': 'bg-stone-100 border-stone-300 text-stone-700',
      'honlu': 'bg-gray-100 border-gray-300 text-gray-700',
      'cilali': 'bg-blue-100 border-blue-300 text-blue-700',
      'fircinlanmis': 'bg-amber-100 border-amber-300 text-amber-700',
      'yakma': 'bg-red-100 border-red-300 text-red-700',
      'eskitme': 'bg-yellow-100 border-yellow-300 text-yellow-700',
      'kumlama': 'bg-purple-100 border-purple-300 text-purple-700',
      'dolgu': 'bg-green-100 border-green-300 text-green-700'
    };
    return themes[finishName] || 'bg-gray-100 border-gray-300 text-gray-700';
  };

  // Toggle comparison mode
  const toggleComparison = (finishName: SurfaceFinishName) => {
    if (comparisonFinishes.includes(finishName)) {
      setComparisonFinishes(prev => prev.filter(f => f !== finishName));
    } else if (comparisonFinishes.length < 3) {
      setComparisonFinishes(prev => [...prev, finishName]);
    }
    
    setShowComparison(comparisonFinishes.length > 0 || !comparisonFinishes.includes(finishName));
  };

  // Effect to update material on mount
  useEffect(() => {
    const currentFinish = getCurrentFinish();
    onMaterialUpdate({
      roughness: currentFinish.roughness,
      metallic: currentFinish.metallic,
      normalIntensity: currentFinish.normalIntensity,
      displacementScale: 0.2,
      emissive: 0
    });
  }, []);

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Palette className="w-5 h-5 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">Yüzey İşlemi</h3>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Preview Mode Toggle */}
            <button
              onClick={() => setPreviewMode(prev => prev === 'grid' ? 'detail' : 'grid')}
              className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
              title={previewMode === 'grid' ? 'Detay Görünümü' : 'Izgara Görünümü'}
            >
              <Eye className="w-4 h-4 text-gray-600" />
            </button>
            
            {/* Comparison Toggle */}
            <button
              onClick={() => setShowComparison(!showComparison)}
              className={`p-2 rounded-lg transition-colors ${
                showComparison 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
              }`}
              title="Karşılaştırma Modu"
            >
              <RotateCw className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Surface Finish Grid */}
      <div className="p-4">
        {previewMode === 'grid' ? (
          <FinishGrid
            finishes={configuration.availableFinishes}
            selectedFinish={selectedFinish}
            comparisonFinishes={comparisonFinishes}
            showComparison={showComparison}
            isTransitioning={isTransitioning}
            onFinishSelect={handleFinishSelect}
            onToggleComparison={toggleComparison}
            getFinishIcon={getFinishIcon}
            getFinishColorTheme={getFinishColorTheme}
          />
        ) : (
          <FinishDetail
            finish={getCurrentFinish()}
            isTransitioning={isTransitioning}
            onFinishSelect={handleFinishSelect}
            availableFinishes={configuration.availableFinishes}
          />
        )}
      </div>

      {/* Comparison Panel */}
      {showComparison && comparisonFinishes.length > 0 && (
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Karşılaştırma</h4>
          <ComparisonPanel
            finishes={configuration.availableFinishes.filter(f => 
              comparisonFinishes.includes(f.name)
            )}
            selectedFinish={selectedFinish}
            onFinishSelect={handleFinishSelect}
          />
        </div>
      )}

      {/* Current Selection Info */}
      <div className="p-4 bg-gray-50 border-t border-gray-200">
        <CurrentFinishInfo
          finish={getCurrentFinish()}
          isTransitioning={isTransitioning}
        />
      </div>
    </div>
  );
};

// Finish Grid Component
interface FinishGridProps {
  finishes: SurfaceFinishType[];
  selectedFinish: SurfaceFinishName;
  comparisonFinishes: SurfaceFinishName[];
  showComparison: boolean;
  isTransitioning: boolean;
  onFinishSelect: (finish: SurfaceFinishName) => void;
  onToggleComparison: (finish: SurfaceFinishName) => void;
  getFinishIcon: (finish: SurfaceFinishName) => React.ComponentType<any>;
  getFinishColorTheme: (finish: SurfaceFinishName) => string;
}

const FinishGrid: React.FC<FinishGridProps> = ({
  finishes,
  selectedFinish,
  comparisonFinishes,
  showComparison,
  isTransitioning,
  onFinishSelect,
  onToggleComparison,
  getFinishIcon,
  getFinishColorTheme
}) => {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
      {finishes.map((finish) => {
        const Icon = getFinishIcon(finish.name);
        const isSelected = finish.name === selectedFinish;
        const isInComparison = comparisonFinishes.includes(finish.name);
        const colorTheme = getFinishColorTheme(finish.name);

        return (
          <div key={finish.name} className="relative">
            <button
              onClick={() => onFinishSelect(finish.name)}
              disabled={isTransitioning}
              className={`w-full p-4 rounded-lg border-2 transition-all text-center ${
                isSelected
                  ? 'border-purple-500 bg-purple-50 text-purple-900 shadow-md'
                  : `border-gray-200 hover:border-gray-300 ${colorTheme}`
              } ${isTransitioning ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <Icon className="w-6 h-6 mx-auto mb-2" />
              <div className="text-sm font-medium capitalize">
                {finish.name}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {finish.description}
              </div>
              
              {/* Price Multiplier */}
              <div className="text-xs font-medium mt-2 text-green-600">
                {finish.priceMultiplier === 1 ? 'Temel Fiyat' : `+%${Math.round((finish.priceMultiplier - 1) * 100)}`}
              </div>
            </button>

            {/* Comparison Checkbox */}
            {showComparison && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleComparison(finish.name);
                }}
                className={`absolute top-2 right-2 w-5 h-5 rounded border-2 transition-colors ${
                  isInComparison
                    ? 'bg-blue-500 border-blue-500 text-white'
                    : 'border-gray-300 bg-white hover:border-gray-400'
                }`}
              >
                {isInComparison && (
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            )}

            {/* Transition Overlay */}
            {isTransitioning && isSelected && (
              <div className="absolute inset-0 bg-purple-100 bg-opacity-75 rounded-lg flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

// Finish Detail Component
interface FinishDetailProps {
  finish: SurfaceFinishType;
  isTransitioning: boolean;
  onFinishSelect: (finish: SurfaceFinishName) => void;
  availableFinishes: SurfaceFinishType[];
}

const FinishDetail: React.FC<FinishDetailProps> = ({
  finish,
  isTransitioning,
  onFinishSelect,
  availableFinishes
}) => {
  return (
    <div className="space-y-6">
      {/* Current Finish Details */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-lg font-semibold text-gray-900 capitalize mb-2">
          {finish.name}
        </h4>
        <p className="text-gray-600 mb-4">{finish.description}</p>
        
        {/* Technical Properties */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-sm font-medium text-gray-700">Pürüzlülük:</span>
            <div className="mt-1 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${finish.roughness * 100}%` }}
              />
            </div>
            <span className="text-xs text-gray-500">{(finish.roughness * 100).toFixed(0)}%</span>
          </div>
          
          <div>
            <span className="text-sm font-medium text-gray-700">Metalik:</span>
            <div className="mt-1 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${finish.metallic * 100}%` }}
              />
            </div>
            <span className="text-xs text-gray-500">{(finish.metallic * 100).toFixed(0)}%</span>
          </div>
        </div>
      </div>

      {/* Quick Switch */}
      <div>
        <h5 className="text-sm font-medium text-gray-700 mb-3">Hızlı Geçiş:</h5>
        <div className="flex flex-wrap gap-2">
          {availableFinishes.map((f) => (
            <button
              key={f.name}
              onClick={() => onFinishSelect(f.name)}
              disabled={isTransitioning || f.name === finish.name}
              className={`px-3 py-1 rounded-full text-sm transition-colors ${
                f.name === finish.name
                  ? 'bg-purple-100 text-purple-700 cursor-default'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              } ${isTransitioning ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {f.name}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

// Comparison Panel Component
interface ComparisonPanelProps {
  finishes: SurfaceFinishType[];
  selectedFinish: SurfaceFinishName;
  onFinishSelect: (finish: SurfaceFinishName) => void;
}

const ComparisonPanel: React.FC<ComparisonPanelProps> = ({
  finishes,
  selectedFinish,
  onFinishSelect
}) => {
  return (
    <div className="overflow-x-auto">
      <table className="w-full text-sm">
        <thead>
          <tr className="border-b border-gray-200">
            <th className="text-left py-2 px-3">Yüzey İşlemi</th>
            <th className="text-left py-2 px-3">Pürüzlülük</th>
            <th className="text-left py-2 px-3">Fiyat Çarpanı</th>
            <th className="text-left py-2 px-3">Açıklama</th>
          </tr>
        </thead>
        <tbody>
          {finishes.map((finish) => (
            <tr 
              key={finish.name}
              className={`border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                finish.name === selectedFinish ? 'bg-purple-50' : ''
              }`}
              onClick={() => onFinishSelect(finish.name)}
            >
              <td className="py-2 px-3 font-medium capitalize">{finish.name}</td>
              <td className="py-2 px-3">{(finish.roughness * 100).toFixed(0)}%</td>
              <td className="py-2 px-3">×{finish.priceMultiplier}</td>
              <td className="py-2 px-3 text-gray-600">{finish.description}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Current Finish Info Component
interface CurrentFinishInfoProps {
  finish: SurfaceFinishType;
  isTransitioning: boolean;
}

const CurrentFinishInfo: React.FC<CurrentFinishInfoProps> = ({
  finish,
  isTransitioning
}) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-3">
        <div className={`w-3 h-3 rounded-full ${isTransitioning ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'}`} />
        <div>
          <span className="text-sm font-medium text-gray-900 capitalize">
            {finish.name} Yüzey İşlemi
          </span>
          <p className="text-xs text-gray-500">{finish.description}</p>
        </div>
      </div>
      
      <div className="text-right">
        <div className="text-sm font-medium text-gray-900">
          Fiyat Etkisi: {finish.priceMultiplier === 1 ? 'Yok' : `+%${Math.round((finish.priceMultiplier - 1) * 100)}`}
        </div>
        {isTransitioning && (
          <div className="text-xs text-yellow-600">Uygulanıyor...</div>
        )}
      </div>
    </div>
  );
};
