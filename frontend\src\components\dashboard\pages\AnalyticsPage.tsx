'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  ArrowDownTrayIcon,
  PlusIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';
import ChartWidget from '../components/ChartWidget';

interface AnalyticsData {
  purchaseAnalytics: {
    totalSpent: number;
    averageOrderValue: number;
    topSuppliers: Array<{
      name: string;
      totalAmount: number;
      orderCount: number;
      averageDeliveryTime: number;
    }>;
    categoryBreakdown: Array<{
      category: string;
      amount: number;
      percentage: number;
    }>;
  };
  salesAnalytics: {
    totalRevenue: number;
    totalProfit: number;
    profitMargin: number;
    topCustomers: Array<{
      name: string;
      totalPurchases: number;
      profitGenerated: number;
    }>;
    bestSellingProducts: Array<{
      name: string;
      quantitySold: number;
      revenue: number;
      profit: number;
    }>;
  };
  charts: {
    monthlySpending: any;
    supplierComparison: any;
    categoryTrends: any;
    profitLossChart: any;
  };
}

interface AnalyticsPageProps {
  onNavigate?: (route: string) => void;
}

const AnalyticsPage: React.FC<AnalyticsPageProps> = ({ onNavigate }) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'purchases' | 'sales' | 'profit-loss'>('purchases');
  const [dateRange, setDateRange] = useState({
    from: '2024-01-01',
    to: '2024-06-30'
  });

  // Gerçek veriler API'den gelecek - şimdilik boş array
  const mockSalesData: any[] = [];

  // Calculate real sales analytics from mock data
  const calculateSalesAnalytics = () => {
    const deliveredSales = mockSalesData.filter(sale => sale.deliveryStatus === 'Teslim Edildi');
    const totalRevenue = deliveredSales.reduce((sum, sale) => sum + sale.salePrice, 0);

    // Estimate profit (30% margin)
    const totalProfit = totalRevenue * 0.3;
    const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

    // Group by category
    const categoryBreakdown = mockSalesData.reduce((acc, sale) => {
      const existing = acc.find(item => item.category === sale.category);
      if (existing) {
        existing.revenue += sale.salePrice;
        existing.quantity += sale.quantity;
      } else {
        acc.push({
          category: sale.category,
          revenue: sale.salePrice,
          quantity: sale.quantity
        });
      }
      return acc;
    }, [] as any[]);

    return {
      totalRevenue,
      totalProfit,
      profitMargin,
      deliveredSales: deliveredSales.length,
      totalSales: mockSalesData.length,
      categoryBreakdown
    };
  };

  const realSalesData = calculateSalesAnalytics();

  // Gerçek veriler API'den gelecek - şimdilik boş data
  const mockAnalyticsData: AnalyticsData = {
    purchaseAnalytics: {
      totalSpent: 0,
      averageOrderValue: 0,
      topSuppliers: [],
      categoryBreakdown: []
    },
    salesAnalytics: {
      totalRevenue: 0,
      totalProfit: 0,
      profitMargin: 0,
      topCustomers: [],
      bestSellingProducts: []
    },
    charts: {
      monthlySpending: {
        labels: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'],
        datasets: [{
          label: 'Aylık Harcama',
          data: [0, 0, 0, 0, 0, 0],
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: true
        }]
      },
      supplierComparison: {
        labels: [],
        datasets: [{
          label: 'Toplam Alım ($)',
          data: [],
          backgroundColor: []
        }]
      },
      categoryTrends: {
        labels: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'],
        datasets: []
      },
      profitLossChart: {
        labels: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'],
        datasets: [{
          label: 'Net Kar ($)',
          data: [0, 0, 0, 0, 0, 0],
          backgroundColor: (ctx: any) => {
            const value = ctx.parsed?.y || 0;
            return value >= 0 ? '#10B981' : '#EF4444';
          }
        }]
      }
    }
  };

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setAnalyticsData(mockAnalyticsData);
      setLoading(false);
    }, 1000);
  }, [dateRange]);

  const handleExportData = (format: 'pdf' | 'excel' | 'csv') => {
    console.log(`Exporting analytics data as ${format}`);
    // Implement export functionality
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <ChartBarIcon className="h-8 w-8 text-purple-600 mr-3" />
              Analizler
            </h1>
            <p className="text-gray-600 mt-2">
              İş performansınızı analiz edin ve raporlar oluşturun
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Date Range Selector */}
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-5 w-5 text-gray-400" />
              <input
                type="date"
                value={dateRange.from}
                onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value }))}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
              />
              <span className="text-gray-500">-</span>
              <input
                type="date"
                value={dateRange.to}
                onChange={(e) => setDateRange(prev => ({ ...prev, to: e.target.value }))}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
              />
            </div>
            
            {/* Export Button */}
            <div className="relative group">
              <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2">
                <ArrowDownTrayIcon className="h-5 w-5" />
                <span>Dışa Aktar</span>
              </button>
              
              {/* Export Dropdown */}
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                <div className="py-2">
                  <button
                    onClick={() => handleExportData('pdf')}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    PDF Raporu
                  </button>
                  <button
                    onClick={() => handleExportData('excel')}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Excel Dosyası
                  </button>
                  <button
                    onClick={() => handleExportData('csv')}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    CSV Dosyası
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'purchases', label: 'Alım Analizi', icon: CurrencyDollarIcon },
              { id: 'sales', label: 'Satış Analizi', icon: ArrowTrendingUpIcon },
              { id: 'profit-loss', label: 'Kar-Zarar', icon: ArrowTrendingDownIcon }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      {activeTab === 'purchases' && analyticsData && (
        <div className="space-y-8">
          {/* Purchase KPIs */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Toplam Harcama</h3>
              <p className="text-3xl font-bold text-blue-600">
                ${analyticsData.purchaseAnalytics.totalSpent.toLocaleString()}
              </p>
              <p className="text-sm text-gray-600 mt-1">Son 6 ay</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Ortalama Sipariş</h3>
              <p className="text-3xl font-bold text-green-600">
                ${analyticsData.purchaseAnalytics.averageOrderValue.toLocaleString()}
              </p>
              <p className="text-sm text-gray-600 mt-1">Sipariş başına</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Toplam Sipariş</h3>
              <p className="text-3xl font-bold text-purple-600">
                {analyticsData.purchaseAnalytics.topSuppliers.reduce((sum, supplier) => sum + supplier.orderCount, 0)}
              </p>
              <p className="text-sm text-gray-600 mt-1">Adet</p>
            </motion.div>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <ChartWidget
              type="line"
              title="Aylık Harcama Trendi"
              data={analyticsData.charts.monthlySpending}
              height={300}
            />
            
            <ChartWidget
              type="bar"
              title="Üretici Karşılaştırması"
              data={analyticsData.charts.supplierComparison}
              height={300}
            />
          </div>

          {/* Top Suppliers Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">En Çok Alım Yapılan Üreticiler</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Üretici
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Toplam Tutar
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Sipariş Sayısı
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ort. Teslimat
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {analyticsData.purchaseAnalytics.topSuppliers.map((supplier, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{supplier.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">${supplier.totalAmount.toLocaleString()}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{supplier.orderCount} adet</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{supplier.averageDeliveryTime} gün</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </motion.div>
        </div>
      )}

      {activeTab === 'sales' && analyticsData && (
        <div className="space-y-8">
          {/* Sales KPIs */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Toplam Gelir</h3>
              <p className="text-3xl font-bold text-green-600">
                ${analyticsData.salesAnalytics.totalRevenue.toLocaleString()}
              </p>
              <p className="text-sm text-gray-600 mt-1">
                {realSalesData.deliveredSales} teslim edildi / {realSalesData.totalSales} toplam
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Toplam Kar</h3>
              <p className="text-3xl font-bold text-purple-600">
                ${analyticsData.salesAnalytics.totalProfit.toLocaleString()}
              </p>
              <p className="text-sm text-gray-600 mt-1">Net kar</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Kar Marjı</h3>
              <p className="text-3xl font-bold text-blue-600">
                {analyticsData.salesAnalytics.profitMargin.toFixed(1)}%
              </p>
              <p className="text-sm text-gray-600 mt-1">Ortalama</p>
            </motion.div>
          </div>

          {/* Manual Sales Entry */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-blue-50 border border-blue-200 rounded-xl p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Satış Verisi Ekle</h3>
                <p className="text-blue-700">
                  Yaptığınız satışları manuel olarak ekleyerek kar-zarar analizinizi güncel tutun.
                </p>
              </div>
              <button
                onClick={() => onNavigate?.('/customer/analytics/sales/add')}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <PlusIcon className="h-5 w-5" />
                <span>Satış Ekle</span>
              </button>
            </div>
          </motion.div>

          {/* Best Selling Products */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">En Çok Satan Ürünler</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ürün
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Satılan Miktar
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Gelir
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Kar
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {analyticsData.salesAnalytics.bestSellingProducts.map((product, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{product.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{product.quantitySold} m²</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">${product.revenue.toLocaleString()}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-green-600">${product.profit.toLocaleString()}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </motion.div>
        </div>
      )}

      {activeTab === 'profit-loss' && analyticsData && (
        <div className="space-y-8">
          {/* Profit Loss Chart */}
          <ChartWidget
            type="bar"
            title="Aylık Kar-Zarar Analizi"
            data={analyticsData.charts.profitLossChart}
            height={400}
          />

          {/* Category Trends */}
          <ChartWidget
            type="line"
            title="Kategori Bazlı Trend Analizi"
            data={analyticsData.charts.categoryTrends}
            height={350}
          />
        </div>
      )}
    </div>
  );
};

export default AnalyticsPage;
