// User Types
export interface User {
  id: string;
  email: string;
  userType: 'PRODUCER' | 'CUSTOMER' | 'ADMIN';
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'BANNED';
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserProfile {
  id: string;
  userId: string;
  companyName: string;
  companyType?: string;
  contactPerson?: string;
  phone?: string;
  address?: Address;
  countryCode: string;
  businessDescription?: string;
  verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
}

export interface Address {
  street: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

// Product Types
export interface Product {
  id: string;
  producerId: string;
  name: string;
  description?: string;
  categoryId: string;
  specifications: ProductSpecifications;
  dimensions?: ProductDimensions;
  basePrice?: number;
  currency: string;
  minimumOrderQuantity?: number;
  productionTimeDays?: number;
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED';
  isActive: boolean;
  featured: boolean;
  slug: string;
  searchKeywords: string[];
  images: ProductImage[];
  createdAt: string;
  updatedAt: string;
}

export interface ProductSpecifications {
  stoneType: string;
  finish: string;
  hardness?: number;
  density?: number;
  waterAbsorption?: number;
  freezeThawResistance?: string;
  [key: string]: any;
}

export interface ProductDimensions {
  length?: number;
  width?: number;
  thickness?: number;
  unit: 'mm' | 'cm' | 'm';
}

export interface ProductImage {
  id: string;
  url: string;
  altText?: string;
  isPrimary: boolean;
  sortOrder: number;
}

export interface ProductCategory {
  id: string;
  name: string;
  description?: string;
  slug: string;
  parentId?: string;
  children?: ProductCategory[];
}

// Bid Types
export interface BidRequest {
  id: string;
  customerId: string;
  anonymousId: string;
  title: string;
  description: string;
  specifications: ProductSpecifications;
  quantity: number;
  unit: string;
  deliveryAddress: Address;
  deliveryDate?: string;
  deliveryTerms?: string;
  bidDeadline: string;
  status: 'ACTIVE' | 'CLOSED' | 'CANCELLED';
  bids: Bid[];
  createdAt: string;
  updatedAt: string;
}

export interface Bid {
  id: string;
  bidRequestId: string;
  producerId: string;
  anonymousId: string;
  unitPrice: number;
  totalPrice: number;
  currency: string;
  deliveryTimeDays: number;
  paymentTerms?: string;
  validityDays: number;
  notes?: string;
  status: 'SUBMITTED' | 'SELECTED' | 'REJECTED' | 'EXPIRED';
  createdAt: string;
  updatedAt: string;
}

// Order Types
export interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  producerId: string;
  bidId?: string;
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  shippingCost: number;
  totalAmount: number;
  currency: string;
  deliveryAddress: Address;
  status: 'PENDING' | 'CONFIRMED' | 'PRODUCTION' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  specifications?: ProductSpecifications;
}

// Payment Types
export interface Payment {
  id: string;
  orderId: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED';
  escrowStatus: 'PENDING' | 'HELD' | 'RELEASED' | 'REFUNDED';
  createdAt: string;
  updatedAt: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    message: string;
    statusCode: number;
    details?: any;
  };
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  userType: 'PRODUCER' | 'CUSTOMER';
  companyName: string;
  countryCode: string;
  contactPerson?: string;
  phone?: string;
}

// UI Types
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface TableColumn<T = any> {
  key: keyof T;
  title: string;
  sortable?: boolean;
  render?: (value: any, record: T) => React.ReactNode;
}

export interface FilterOption {
  key: string;
  label: string;
  type: 'text' | 'select' | 'range' | 'date';
  options?: SelectOption[];
}

// 3D Types
export interface Stone3DModel {
  id: string;
  productId: string;
  modelUrl: string;
  textures: TextureAsset[];
  dimensions: ProductDimensions;
}

export interface TextureAsset {
  id: string;
  url: string;
  type: 'diffuse' | 'normal' | 'roughness' | 'specular';
  resolution: {
    width: number;
    height: number;
  };
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
}
