import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface CommissionCalculation {
  amount: number;
  method: 'PERCENTAGE' | 'PER_M2';
  rate?: number;
  totalM2?: number;
  commissionPerM2?: number;
  details: {
    baseAmount: number;
    totalM2?: number;
    commissionPerM2?: number;
    calculatedAmount: number;
  };
}

export class CommissionService {
  private commissionPerM2: number;
  private commissionCurrency: string;
  private defaultCommissionRate: number;

  constructor() {
    this.commissionPerM2 = parseFloat(process.env.PLATFORM_COMMISSION_PER_M2 || '1.00'); // $1 per m²
    this.commissionCurrency = process.env.PLATFORM_COMMISSION_CURRENCY || 'USD';
    this.defaultCommissionRate = parseFloat(process.env.PLATFORM_COMMISSION_RATE || '0.05'); // 5% fallback
  }

  /**
   * Calculate commission based on m² (preferred method)
   */
  calculateCommissionByM2(totalM2: number): CommissionCalculation {
    const amount = totalM2 * this.commissionPerM2;
    
    return {
      amount,
      method: 'PER_M2',
      totalM2,
      commissionPerM2: this.commissionPerM2,
      details: {
        baseAmount: 0, // Not applicable for m² based
        totalM2,
        commissionPerM2: this.commissionPerM2,
        calculatedAmount: amount
      }
    };
  }

  /**
   * Calculate commission based on percentage (fallback method)
   */
  calculateCommissionByPercentage(baseAmount: number, rate?: number): CommissionCalculation {
    const commissionRate = rate || this.defaultCommissionRate;
    const amount = baseAmount * commissionRate;
    
    return {
      amount,
      method: 'PERCENTAGE',
      rate: commissionRate,
      details: {
        baseAmount,
        calculatedAmount: amount
      }
    };
  }

  /**
   * Smart commission calculation - prefers m² if available, falls back to percentage
   */
  calculateCommission(baseAmount: number, totalM2?: number, rate?: number): CommissionCalculation {
    if (totalM2 && totalM2 > 0) {
      return this.calculateCommissionByM2(totalM2);
    } else {
      return this.calculateCommissionByPercentage(baseAmount, rate);
    }
  }

  /**
   * Create commission record in database
   */
  async createCommissionRecord(
    paymentId: string,
    orderId: string,
    calculation: CommissionCalculation
  ): Promise<any> {
    return await prisma.commission.create({
      data: {
        paymentId,
        orderId,
        amount: calculation.amount,
        rate: calculation.rate || 0,
        totalM2: calculation.totalM2 || 0,
        commissionPerM2: calculation.commissionPerM2,
        calculationMethod: calculation.method,
        calculatedAt: new Date()
      }
    });
  }

  /**
   * Get commission statistics for admin dashboard
   */
  async getCommissionStats(startDate?: Date, endDate?: Date) {
    const whereClause: any = {};
    
    if (startDate && endDate) {
      whereClause.calculatedAt = {
        gte: startDate,
        lte: endDate
      };
    }

    const commissions = await prisma.commission.findMany({
      where: whereClause,
      include: {
        order: {
          select: {
            totalM2: true,
            currency: true
          }
        }
      }
    });

    const totalCommission = commissions.reduce((sum, comm) => sum + comm.amount, 0);
    const totalM2Processed = commissions.reduce((sum, comm) => sum + (comm.order?.totalM2 ? Number(comm.order.totalM2) : 0), 0);
    const totalTransactions = commissions.length;

    // Calculate monthly breakdown
    const monthlyStats = this.calculateMonthlyStats(commissions);

    return {
      totalCommission,
      totalM2Processed,
      totalTransactions,
      averageCommissionPerM2: totalM2Processed > 0 ? totalCommission / totalM2Processed : 0,
      averageCommissionPerTransaction: totalTransactions > 0 ? totalCommission / totalTransactions : 0,
      monthlyStats,
      commissionPerM2: this.commissionPerM2,
      currency: this.commissionCurrency
    };
  }

  /**
   * Calculate monthly commission statistics
   */
  private calculateMonthlyStats(commissions: any[]) {
    const monthlyData: { [key: string]: { amount: number; m2: number; transactions: number } } = {};

    commissions.forEach(commission => {
      const month = new Date(commission.calculatedAt).toISOString().slice(0, 7); // YYYY-MM
      
      if (!monthlyData[month]) {
        monthlyData[month] = { amount: 0, m2: 0, transactions: 0 };
      }
      
      monthlyData[month].amount += commission.amount;
      monthlyData[month].m2 += commission.totalM2 || 0;
      monthlyData[month].transactions += 1;
    });

    return Object.entries(monthlyData)
      .map(([month, data]) => ({
        month,
        ...data
      }))
      .sort((a, b) => a.month.localeCompare(b.month));
  }

  /**
   * Get commission breakdown by category
   */
  async getCommissionByCategory(startDate?: Date, endDate?: Date) {
    const whereClause: any = {};
    
    if (startDate && endDate) {
      whereClause.calculatedAt = {
        gte: startDate,
        lte: endDate
      };
    }

    const commissions = await prisma.commission.findMany({
      where: whereClause,
      include: {
        order: {
          include: {
            quote: {
              include: {
                product: {
                  select: {
                    category: true
                  }
                }
              }
            }
          }
        }
      }
    });

    const categoryStats: { [key: string]: { amount: number; m2: number; transactions: number } } = {};

    commissions.forEach(commission => {
      const categoryName = commission.order?.quote?.product?.category?.name || 'Diğer';

      if (!categoryStats[categoryName]) {
        categoryStats[categoryName] = { amount: 0, m2: 0, transactions: 0 };
      }

      categoryStats[categoryName].amount += commission.amount;
      categoryStats[categoryName].m2 += commission.order?.totalM2 ? Number(commission.order.totalM2) : 0;
      categoryStats[categoryName].transactions += 1;
    });

    return Object.entries(categoryStats).map(([category, stats]) => ({
      category,
      ...stats,
      percentage: 0 // Will be calculated in the frontend
    }));
  }

  /**
   * Get commission breakdown by producer
   */
  async getCommissionByProducer(startDate?: Date, endDate?: Date) {
    const whereClause: any = {};
    
    if (startDate && endDate) {
      whereClause.calculatedAt = {
        gte: startDate,
        lte: endDate
      };
    }

    const commissions = await prisma.commission.findMany({
      where: whereClause,
      include: {
        order: {
          include: {
            producer: {
              select: {
                id: true,
                companyName: true,
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    });

    const producerStats: { [key: string]: any } = {};

    commissions.forEach(commission => {
      const producer = commission.order?.producer;
      if (!producer) return;

      const producerKey = producer.id;
      const producerName = producer.companyName || `${producer.firstName} ${producer.lastName}`;
      
      if (!producerStats[producerKey]) {
        producerStats[producerKey] = {
          id: producer.id,
          name: producerName,
          amount: 0,
          m2: 0,
          transactions: 0
        };
      }
      
      producerStats[producerKey].amount += commission.amount;
      producerStats[producerKey].m2 += commission.order?.totalM2 ? Number(commission.order.totalM2) : 0;
      producerStats[producerKey].transactions += 1;
    });

    return Object.values(producerStats).sort((a: any, b: any) => b.amount - a.amount);
  }

  /**
   * Calculate net amount for producer (total - commission)
   */
  calculateProducerAmount(totalAmount: number, totalM2?: number): { 
    commission: number; 
    producerAmount: number; 
    details: CommissionCalculation 
  } {
    const commissionCalc = this.calculateCommission(totalAmount, totalM2);
    const producerAmount = totalAmount - commissionCalc.amount;

    return {
      commission: commissionCalc.amount,
      producerAmount,
      details: commissionCalc
    };
  }
}

export default CommissionService;
