'use client'

import * as React from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useProducerAuth } from '@/contexts/producer-auth-context'
import { Button } from '@/components/ui/button'
import { Container } from '@/components/ui/container'
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  FileText,
  BarChart3,
  Settings,
  LogOut,
  Bell,
  User,
  TestTube,
  Building2
} from 'lucide-react'

const sidebarItems = [
  {
    name: 'Dashboard',
    href: '/producer/dashboard',
    icon: LayoutDashboard
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    href: '/producer/products',
    icon: Package
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    href: '/producer/blocks',
    icon: Building2
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    href: '/producer/quote-requests',
    icon: FileText
  },
  {
    name: 'Numune Talepleri',
    href: '/producer/sample-requests',
    icon: TestTube
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    href: '/producer/orders',
    icon: ShoppingCart
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    href: '/producer/analytics',
    icon: BarChart3
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    href: '/producer/settings',
    icon: Settings
  }
]

export default function ProducerLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const router = useRouter()
  const { producer, logout, isAuthenticated, isLoading } = useProducerAuth()
  const [isSidebarOpen, setIsSidebarOpen] = React.useState(false)

  // Redirect to home if not authenticated
  React.useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/?auth_required=true')
    }
  }, [isLoading, isAuthenticated, router])

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Yükleniyor...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !producer) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Giriş Gerekli
          </h2>
          <p className="text-gray-600 mb-4">
            Bu sayfayı görüntülemek için üretici hesabınızla giriş yapmalısınız.
          </p>
          <Button onClick={() => router.push('/')}>
            Ana Sayfaya Dön
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-gray-50 flex overflow-hidden">
      {/* Mobile sidebar backdrop */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
        ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:relative lg:flex lg:flex-shrink-0 lg:h-screen
      `}>
        <div className="flex flex-col h-full w-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200 flex-shrink-0">
            <Link href="/producer/dashboard" className="flex items-center">
              <span className="text-xl font-bold text-amber-600">
                Üretici Paneli
              </span>
            </Link>
            <button
              onClick={() => setIsSidebarOpen(false)}
              className="lg:hidden text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {/* Producer Info */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-amber-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">{producer.name}</p>
                <p className="text-xs text-gray-500">{producer.companyName}</p>
                {!producer.isApproved && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 mt-1">
                    Onay Bekliyor
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {sidebarItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`
                    flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors
                    ${isActive
                      ? 'bg-amber-100 text-amber-700'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                    }
                  `}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {item.name}
                </Link>
              )
            })}
          </nav>

          {/* Logout */}
          <div className="p-4 border-t border-gray-200 flex-shrink-0">
            <Button
              variant="ghost"
              onClick={logout}
              className="w-full justify-start text-gray-600 hover:text-gray-900"
            >
              <LogOut className="w-5 h-5 mr-3" />
              Çıkış Yap
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col min-w-0 h-screen">
        {/* Top bar */}
        <header className="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center justify-between h-16 px-6">
            <button
              onClick={() => setIsSidebarOpen(true)}
              className="lg:hidden text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <Bell className="w-5 h-5" />
              </Button>
              <div className="text-sm text-gray-600">
                Hoş geldiniz, {producer?.name || 'Test Kullanıcısı'}
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 p-4 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  )
}
