'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Unlock,
  Clock,
  Wallet,
  User,
  Building,
  Package,
  Truck,
  CheckCircle,
  AlertTriangle,
  Calendar,
  DollarSign,
  RefreshCw,
  FileText
} from 'lucide-react'

interface EscrowManagementModalProps {
  escrow: any
  isOpen: boolean
  onClose: () => void
  onRelease: (escrowId: string, reason?: string) => void
  onExtend: (escrowId: string, newDate: string) => void
}

export function EscrowManagementModal({
  escrow,
  isOpen,
  onClose,
  onRelease,
  onExtend
}: EscrowManagementModalProps) {
  const [action, setAction] = useState<'release' | 'extend' | null>(null)
  const [reason, setReason] = useState('')
  const [newExpiryDate, setNewExpiryDate] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)

  const handleSubmit = async () => {
    if (!action) return

    setIsProcessing(true)
    try {
      if (action === 'release') {
        await onRelease(escrow.id, reason)
      } else if (action === 'extend') {
        if (!newExpiryDate) {
          alert('Yeni son tarih seçmelisiniz')
          return
        }
        await onExtend(escrow.id, newExpiryDate)
      }
      onClose()
    } catch (error) {
      console.error('Error processing escrow:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getDaysUntilExpiry = (expiryDate: string) => {
    const now = new Date()
    const expiry = new Date(expiryDate)
    const diffTime = expiry.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'held':
        return <Badge className="bg-blue-100 text-blue-800">Beklemede</Badge>
      case 'ready_to_release':
        return <Badge className="bg-green-100 text-green-800">Serbest Bırakılabilir</Badge>
      case 'released':
        return <Badge className="bg-gray-100 text-gray-800">Serbest Bırakıldı</Badge>
      case 'expired':
        return <Badge className="bg-red-100 text-red-800">Süresi Doldu</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getDeliveryStatusBadge = (status: string) => {
    switch (status) {
      case 'processing':
        return <Badge variant="outline" className="text-yellow-600">İşleniyor</Badge>
      case 'shipped':
        return <Badge variant="outline" className="text-blue-600">Gönderildi</Badge>
      case 'delivered':
        return <Badge variant="outline" className="text-green-600">Teslim Edildi</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (!escrow) return null

  const daysUntilExpiry = getDaysUntilExpiry(escrow.expiresAt)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wallet className="w-5 h-5" />
            Escrow Yönetimi - {escrow.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Basic Info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getStatusBadge(escrow.status)}
              <span className="text-sm text-gray-500">
                Oluşturulma: {formatDate(escrow.createdAt)}
              </span>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">
                ${escrow.amount.toLocaleString()} {escrow.currency}
              </div>
              <div className={`text-sm ${daysUntilExpiry <= 7 ? 'text-red-600' : 'text-gray-500'}`}>
                {daysUntilExpiry > 0 ? `${daysUntilExpiry} gün kaldı` : 'Süresi doldu'}
              </div>
            </div>
          </div>

          {/* Order and Parties Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Package className="w-5 h-5" />
                Sipariş Bilgileri
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Sipariş No</label>
                  <p className="text-gray-900 font-mono">{escrow.orderId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Ürün</label>
                  <p className="text-gray-900">{escrow.orderInfo.productName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Miktar</label>
                  <p className="text-gray-900">{escrow.orderInfo.quantity}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Teslimat Durumu</label>
                  <div className="mt-1">
                    {getDeliveryStatusBadge(escrow.orderInfo.deliveryStatus)}
                  </div>
                  {escrow.orderInfo.trackingNumber && (
                    <p className="text-sm text-gray-500 mt-1">
                      Takip No: {escrow.orderInfo.trackingNumber}
                    </p>
                  )}
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <User className="w-5 h-5" />
                Taraflar
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Müşteri</label>
                  <p className="text-gray-900">{escrow.customerName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Üretici</label>
                  <p className="text-gray-900">{escrow.producerName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Ödeme ID</label>
                  <p className="text-gray-900 font-mono text-sm">{escrow.paymentId}</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Timeline */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Clock className="w-5 h-5" />
              İşlem Geçmişi
            </h3>
            <div className="space-y-4">
              {escrow.timeline.map((event: any, index: number) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-1">
                    <div className={`w-3 h-3 rounded-full ${
                      event.status === 'created' ? 'bg-blue-500' :
                      event.status === 'funded' ? 'bg-green-500' :
                      event.status === 'shipped' ? 'bg-yellow-500' :
                      event.status === 'delivered' ? 'bg-purple-500' :
                      event.status === 'confirmed' ? 'bg-green-600' :
                      'bg-gray-400'
                    }`} />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{event.event}</p>
                    <p className="text-xs text-gray-500">{formatDate(event.date)}</p>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Expiry Information */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Son Tarih Bilgileri
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="text-sm font-medium text-gray-500">Mevcut Son Tarih</label>
                <p className="text-gray-900">{formatDate(escrow.expiresAt)}</p>
                <p className={`text-sm mt-1 ${daysUntilExpiry <= 7 ? 'text-red-600' : 'text-gray-500'}`}>
                  {daysUntilExpiry > 0 ? `${daysUntilExpiry} gün kaldı` : 'Süresi doldu'}
                </p>
              </div>
              {daysUntilExpiry <= 7 && (
                <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  <div>
                    <p className="text-sm font-medium text-red-800">Dikkat!</p>
                    <p className="text-xs text-red-600">Escrow süresi yakında dolacak</p>
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* Actions */}
          {escrow.status !== 'released' && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                İşlemler
              </h3>
              
              {!action && (
                <div className="flex gap-4">
                  {escrow.status === 'ready_to_release' && (
                    <Button
                      onClick={() => setAction('release')}
                      className="flex-1 bg-green-600 hover:bg-green-700"
                    >
                      <Unlock className="w-4 h-4 mr-2" />
                      Escrow'u Serbest Bırak
                    </Button>
                  )}
                  <Button
                    onClick={() => setAction('extend')}
                    variant="outline"
                    className="flex-1"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Süreyi Uzat
                  </Button>
                </div>
              )}

              {action && (
                <div className="space-y-4">
                  <div className="flex items-center gap-2 p-3 rounded-lg bg-gray-50">
                    {action === 'release' ? (
                      <Unlock className="w-5 h-5 text-green-600" />
                    ) : (
                      <RefreshCw className="w-5 h-5 text-blue-600" />
                    )}
                    <span className="font-medium">
                      {action === 'release' ? 'Escrow\'u Serbest Bırakıyorsunuz' : 'Escrow Süresini Uzatıyorsunuz'}
                    </span>
                  </div>

                  {action === 'extend' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Yeni Son Tarih
                      </label>
                      <Input
                        type="datetime-local"
                        value={newExpiryDate}
                        onChange={(e) => setNewExpiryDate(e.target.value)}
                        min={new Date().toISOString().slice(0, 16)}
                        className="w-full"
                      />
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {action === 'release' ? 'Serbest Bırakma Notu (İsteğe bağlı)' : 'Uzatma Sebebi (İsteğe bağlı)'}
                    </label>
                    <Textarea
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      placeholder={
                        action === 'release' 
                          ? 'Serbest bırakma ile ilgili notlarınızı yazabilirsiniz...'
                          : 'Süre uzatma sebebini açıklayabilirsiniz...'
                      }
                      rows={3}
                      className="w-full"
                    />
                  </div>

                  <div className="flex gap-3">
                    <Button
                      onClick={handleSubmit}
                      disabled={isProcessing || (action === 'extend' && !newExpiryDate)}
                      className={action === 'release' ? 'bg-green-600 hover:bg-green-700' : ''}
                    >
                      {isProcessing ? 'İşleniyor...' : 
                       action === 'release' ? 'Serbest Bırakı Tamamla' : 'Süre Uzatmayı Tamamla'}
                    </Button>
                    <Button
                      onClick={() => {
                        setAction(null)
                        setReason('')
                        setNewExpiryDate('')
                      }}
                      variant="outline"
                      disabled={isProcessing}
                    >
                      İptal
                    </Button>
                  </div>
                </div>
              )}
            </Card>
          )}

          {/* Commission Information */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Komisyon Bilgileri
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Toplam Tutar</label>
                <p className="text-lg font-semibold text-gray-900">
                  ${escrow.amount.toLocaleString()}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Platform Komisyonu</label>
                <p className="text-lg font-semibold text-blue-600">
                  ${(escrow.amount * 0.02).toLocaleString()} (2%)
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Üreticiye Aktarılacak</label>
                <p className="text-lg font-semibold text-green-600">
                  ${(escrow.amount * 0.98).toLocaleString()}
                </p>
              </div>
            </div>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
