'use client';

import React, { useMemo, useEffect } from 'react';
import { useLoader } from '@react-three/fiber';
import * as THREE from 'three';
import { ProductPlacement } from '../core/ShowroomEngine';
import { PatternGenerator } from '../utils/patterns';
import { GroutManager } from '../utils/grout';

interface RealisticMaterialSystemProps {
  product: ProductPlacement;
  showWireframe?: boolean;
}

// Realistic material configurations for different stone types
const MATERIAL_CONFIGS = {
  'marble-carrara-white': {
    baseColor: '#f8f9fa',
    roughness: 0.1,
    metalness: 0.0,
    normalScale: 0.3,
    bumpScale: 0.1,
    envMapIntensity: 0.8,
    clearcoat: 0.3,
    clearcoatRoughness: 0.1
  },
  'marble-emperador-dark': {
    baseColor: '#8b4513',
    roughness: 0.15,
    metalness: 0.0,
    normalScale: 0.4,
    bumpScale: 0.15,
    envMapIntensity: 0.7,
    clearcoat: 0.4,
    clearcoatRoughness: 0.1
  },
  'granite-absolute-black': {
    baseColor: '#2d3748',
    roughness: 0.2,
    metalness: 0.1,
    normalScale: 0.5,
    bumpScale: 0.2,
    envMapIntensity: 0.9,
    clearcoat: 0.6,
    clearcoatRoughness: 0.05
  },
  'granite-kashmir-white': {
    baseColor: '#e2e8f0',
    roughness: 0.25,
    metalness: 0.05,
    normalScale: 0.4,
    bumpScale: 0.18,
    envMapIntensity: 0.8,
    clearcoat: 0.5,
    clearcoatRoughness: 0.08
  },
  'travertine-classic-beige': {
    baseColor: '#f7fafc',
    roughness: 0.6,
    metalness: 0.0,
    normalScale: 0.8,
    bumpScale: 0.4,
    envMapIntensity: 0.3,
    clearcoat: 0.1,
    clearcoatRoughness: 0.3
  },
  'travertine-noce': {
    baseColor: '#d4a574',
    roughness: 0.65,
    metalness: 0.0,
    normalScale: 0.9,
    bumpScale: 0.45,
    envMapIntensity: 0.25,
    clearcoat: 0.1,
    clearcoatRoughness: 0.35
  },
  'onyx-green-pakistan': {
    baseColor: '#38a169',
    roughness: 0.05,
    metalness: 0.0,
    normalScale: 0.2,
    bumpScale: 0.05,
    envMapIntensity: 1.2,
    clearcoat: 0.8,
    clearcoatRoughness: 0.02,
    transmission: 0.3,
    thickness: 0.5
  },
  'limestone-jerusalem-gold': {
    baseColor: '#f6d55c',
    roughness: 0.4,
    metalness: 0.0,
    normalScale: 0.6,
    bumpScale: 0.3,
    envMapIntensity: 0.4,
    clearcoat: 0.2,
    clearcoatRoughness: 0.2
  }
};

// Generate procedural stone texture
function generateStoneTexture(
  materialType: string,
  size: number = 1024
): { 
  diffuseTexture: THREE.CanvasTexture;
  normalTexture: THREE.CanvasTexture;
  roughnessTexture: THREE.CanvasTexture;
} {
  const canvas = document.createElement('canvas');
  canvas.width = size;
  canvas.height = size;
  const ctx = canvas.getContext('2d')!;

  const config = MATERIAL_CONFIGS[materialType as keyof typeof MATERIAL_CONFIGS] || MATERIAL_CONFIGS['marble-carrara-white'];

  // Generate base texture based on material type
  if (materialType.includes('marble')) {
    generateMarbleTexture(ctx, size, config.baseColor);
  } else if (materialType.includes('granite')) {
    generateGraniteTexture(ctx, size, config.baseColor);
  } else if (materialType.includes('travertine')) {
    generateTravertineTexture(ctx, size, config.baseColor);
  } else if (materialType.includes('onyx')) {
    generateOnyxTexture(ctx, size, config.baseColor);
  } else if (materialType.includes('limestone')) {
    generateLimestoneTexture(ctx, size, config.baseColor);
  } else {
    // Default texture
    ctx.fillStyle = config.baseColor;
    ctx.fillRect(0, 0, size, size);
  }

  const diffuseTexture = new THREE.CanvasTexture(canvas);
  diffuseTexture.wrapS = diffuseTexture.wrapT = THREE.RepeatWrapping;

  // Generate normal map
  const normalCanvas = document.createElement('canvas');
  normalCanvas.width = size;
  normalCanvas.height = size;
  const normalCtx = normalCanvas.getContext('2d')!;
  generateNormalMap(normalCtx, size, materialType);
  const normalTexture = new THREE.CanvasTexture(normalCanvas);
  normalTexture.wrapS = normalTexture.wrapT = THREE.RepeatWrapping;

  // Generate roughness map
  const roughnessCanvas = document.createElement('canvas');
  roughnessCanvas.width = size;
  roughnessCanvas.height = size;
  const roughnessCtx = roughnessCanvas.getContext('2d')!;
  generateRoughnessMap(roughnessCtx, size, materialType);
  const roughnessTexture = new THREE.CanvasTexture(roughnessCanvas);
  roughnessTexture.wrapS = roughnessTexture.wrapT = THREE.RepeatWrapping;

  return { diffuseTexture, normalTexture, roughnessTexture };
}

function generateMarbleTexture(ctx: CanvasRenderingContext2D, size: number, baseColor: string) {
  // Base color
  ctx.fillStyle = baseColor;
  ctx.fillRect(0, 0, size, size);

  // Add marble veins
  const gradient = ctx.createLinearGradient(0, 0, size, size);
  gradient.addColorStop(0, 'rgba(200,200,200,0.3)');
  gradient.addColorStop(0.5, 'rgba(150,150,150,0.1)');
  gradient.addColorStop(1, 'rgba(200,200,200,0.3)');
  
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, size, size);

  // Add random veins
  for (let i = 0; i < 5; i++) {
    ctx.beginPath();
    ctx.strokeStyle = `rgba(180,180,180,${0.2 + Math.random() * 0.3})`;
    ctx.lineWidth = 2 + Math.random() * 4;
    ctx.moveTo(Math.random() * size, Math.random() * size);
    
    for (let j = 0; j < 3; j++) {
      ctx.quadraticCurveTo(
        Math.random() * size, Math.random() * size,
        Math.random() * size, Math.random() * size
      );
    }
    ctx.stroke();
  }
}

function generateGraniteTexture(ctx: CanvasRenderingContext2D, size: number, baseColor: string) {
  // Base color
  ctx.fillStyle = baseColor;
  ctx.fillRect(0, 0, size, size);

  // Add granite speckles
  for (let i = 0; i < 2000; i++) {
    const x = Math.random() * size;
    const y = Math.random() * size;
    const radius = Math.random() * 3;
    
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fillStyle = `rgba(${Math.random() * 100}, ${Math.random() * 100}, ${Math.random() * 100}, ${0.3 + Math.random() * 0.4})`;
    ctx.fill();
  }
}

function generateTravertineTexture(ctx: CanvasRenderingContext2D, size: number, baseColor: string) {
  // Base color
  ctx.fillStyle = baseColor;
  ctx.fillRect(0, 0, size, size);

  // Add travertine holes and patterns
  for (let i = 0; i < 50; i++) {
    const x = Math.random() * size;
    const y = Math.random() * size;
    const radius = 2 + Math.random() * 8;
    
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fillStyle = `rgba(0,0,0,${0.1 + Math.random() * 0.2})`;
    ctx.fill();
  }

  // Add horizontal bands
  for (let i = 0; i < 10; i++) {
    const y = (i / 10) * size + Math.random() * 20;
    ctx.fillStyle = `rgba(200,200,200,${0.1 + Math.random() * 0.1})`;
    ctx.fillRect(0, y, size, 2 + Math.random() * 3);
  }
}

function generateOnyxTexture(ctx: CanvasRenderingContext2D, size: number, baseColor: string) {
  // Base color
  ctx.fillStyle = baseColor;
  ctx.fillRect(0, 0, size, size);

  // Add onyx bands
  for (let i = 0; i < 8; i++) {
    const gradient = ctx.createLinearGradient(0, i * size / 8, 0, (i + 1) * size / 8);
    gradient.addColorStop(0, `rgba(255,255,255,${0.1 + Math.random() * 0.2})`);
    gradient.addColorStop(0.5, `rgba(0,0,0,${0.1 + Math.random() * 0.1})`);
    gradient.addColorStop(1, `rgba(255,255,255,${0.1 + Math.random() * 0.2})`);
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, i * size / 8, size, size / 8);
  }
}

function generateLimestoneTexture(ctx: CanvasRenderingContext2D, size: number, baseColor: string) {
  // Base color
  ctx.fillStyle = baseColor;
  ctx.fillRect(0, 0, size, size);

  // Add limestone texture
  for (let i = 0; i < 1000; i++) {
    const x = Math.random() * size;
    const y = Math.random() * size;
    const radius = Math.random() * 2;
    
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fillStyle = `rgba(${200 + Math.random() * 55}, ${200 + Math.random() * 55}, ${150 + Math.random() * 55}, ${0.2 + Math.random() * 0.3})`;
    ctx.fill();
  }
}

function generateNormalMap(ctx: CanvasRenderingContext2D, size: number, materialType: string) {
  // Generate normal map based on material type
  const imageData = ctx.createImageData(size, size);
  const data = imageData.data;

  for (let i = 0; i < data.length; i += 4) {
    // Default normal pointing up
    data[i] = 128;     // R (X)
    data[i + 1] = 128; // G (Y)
    data[i + 2] = 255; // B (Z)
    data[i + 3] = 255; // A

    // Add some variation based on material
    if (materialType.includes('travertine')) {
      data[i] += (Math.random() - 0.5) * 60;
      data[i + 1] += (Math.random() - 0.5) * 60;
    }
  }

  ctx.putImageData(imageData, 0, 0);
}

function generateRoughnessMap(ctx: CanvasRenderingContext2D, size: number, materialType: string) {
  const config = MATERIAL_CONFIGS[materialType as keyof typeof MATERIAL_CONFIGS] || MATERIAL_CONFIGS['marble-carrara-white'];
  const baseRoughness = Math.floor(config.roughness * 255);
  
  ctx.fillStyle = `rgb(${baseRoughness}, ${baseRoughness}, ${baseRoughness})`;
  ctx.fillRect(0, 0, size, size);

  // Add variation
  for (let i = 0; i < 500; i++) {
    const x = Math.random() * size;
    const y = Math.random() * size;
    const radius = Math.random() * 10;
    const variation = (Math.random() - 0.5) * 100;
    
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fillStyle = `rgba(${baseRoughness + variation}, ${baseRoughness + variation}, ${baseRoughness + variation}, 0.5)`;
    ctx.fill();
  }
}

export const RealisticMaterialSystem: React.FC<RealisticMaterialSystemProps> = ({
  product,
  showWireframe = false
}) => {
  // Generate realistic textures
  const { diffuseTexture, normalTexture, roughnessTexture } = useMemo(() => {
    return generateStoneTexture(product.productId);
  }, [product.productId]);

  // Get material configuration
  const materialConfig = useMemo(() => {
    return MATERIAL_CONFIGS[product.productId as keyof typeof MATERIAL_CONFIGS] || MATERIAL_CONFIGS['marble-carrara-white'];
  }, [product.productId]);

  // Create realistic material
  const material = useMemo(() => {
    const mat = new THREE.MeshPhysicalMaterial({
      color: materialConfig.baseColor,
      roughness: materialConfig.roughness,
      metalness: materialConfig.metalness,
      envMapIntensity: materialConfig.envMapIntensity,
      clearcoat: materialConfig.clearcoat,
      clearcoatRoughness: materialConfig.clearcoatRoughness,
      transparent: product.opacity < 1,
      opacity: product.opacity,
      wireframe: showWireframe
    });

    // Apply textures
    mat.map = diffuseTexture;
    mat.normalMap = normalTexture;
    mat.normalScale = new THREE.Vector2(materialConfig.normalScale, materialConfig.normalScale);
    mat.roughnessMap = roughnessTexture;

    // Special properties for onyx (translucency)
    if (product.productId.includes('onyx')) {
      mat.transmission = materialConfig.transmission || 0;
      mat.thickness = materialConfig.thickness || 0;
    }

    // Apply grout and pattern
    const tileSize = {
      width: product.scale.width,
      height: product.scale.height
    };

    GroutManager.applyGroutToMaterial(
      mat,
      tileSize,
      product.groutSettings,
      materialConfig.baseColor
    );

    return mat;
  }, [
    materialConfig,
    product.opacity,
    product.groutSettings,
    product.scale,
    showWireframe,
    diffuseTexture,
    normalTexture,
    roughnessTexture,
    product.productId
  ]);

  // Set texture repeat based on product scale
  useEffect(() => {
    const repeatX = product.scale.width / 100; // Convert cm to meters for repeat
    const repeatY = product.scale.height / 100;
    
    diffuseTexture.repeat.set(repeatX, repeatY);
    normalTexture.repeat.set(repeatX, repeatY);
    roughnessTexture.repeat.set(repeatX, repeatY);
  }, [product.scale, diffuseTexture, normalTexture, roughnessTexture]);

  return <primitive object={material} attach="material" />;
};

export default RealisticMaterialSystem;
