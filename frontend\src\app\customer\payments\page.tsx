'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  CreditCard, 
  Upload, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Building,
  FileText,
  Download,
  Eye
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import toast from 'react-hot-toast';

interface PaymentRequest {
  id: string;
  orderId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'receipt_uploaded' | 'verified' | 'rejected';
  createdAt: string;
  dueDate: string;
  bankInfo: {
    bankName: string;
    iban: string;
    accountHolder: string;
    referenceCode: string;
  };
  receipt?: {
    url: string;
    uploadedAt: string;
  };
  rejectionReason?: string;
}

export default function CustomerPaymentsPage() {
  const { user } = useAuth();
  const [payments, setPayments] = useState<PaymentRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPayment, setSelectedPayment] = useState<PaymentRequest | null>(null);
  const [uploadingReceipt, setUploadingReceipt] = useState(false);
  const [receiptFile, setReceiptFile] = useState<File | null>(null);
  const [bankReference, setBankReference] = useState('');

  useEffect(() => {
    fetchPayments();
  }, []);

  const fetchPayments = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockPayments: PaymentRequest[] = [
        {
          id: 'pay_001',
          orderId: 'ORD_001',
          amount: 15000,
          currency: 'TRY',
          status: 'pending',
          createdAt: '2024-01-15T10:00:00Z',
          dueDate: '2024-01-18T23:59:59Z',
          bankInfo: {
            bankName: 'Türkiye İş Bankası',
            iban: 'TR33 0006 4000 0011 2345 6789 01',
            accountHolder: 'Doğal Taş Pazaryeri Ltd. Şti.',
            referenceCode: 'REF001'
          }
        },
        {
          id: 'pay_002',
          orderId: 'ORD_002',
          amount: 8500,
          currency: 'TRY',
          status: 'receipt_uploaded',
          createdAt: '2024-01-10T14:30:00Z',
          dueDate: '2024-01-13T23:59:59Z',
          bankInfo: {
            bankName: 'Türkiye İş Bankası',
            iban: 'TR33 0006 4000 0011 2345 6789 01',
            accountHolder: 'Doğal Taş Pazaryeri Ltd. Şti.',
            referenceCode: 'REF002'
          },
          receipt: {
            url: '/uploads/receipts/receipt_002.pdf',
            uploadedAt: '2024-01-11T09:15:00Z'
          }
        }
      ];
      setPayments(mockPayments);
    } catch (error) {
      console.error('Error fetching payments:', error);
      toast.error('Ödemeler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleReceiptUpload = async (paymentId: string) => {
    if (!receiptFile || !bankReference.trim()) {
      toast.error('Lütfen dekont dosyası seçin ve banka referans numarasını girin');
      return;
    }

    try {
      setUploadingReceipt(true);
      
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('receipt', receiptFile);
      formData.append('bankReference', bankReference);
      formData.append('paymentId', paymentId);

      // Upload receipt - replace with actual API call
      const response = await fetch(`/api/payments/${paymentId}/receipt`, {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        toast.success('Dekont başarıyla yüklendi. Admin onayı bekleniyor.');
        await fetchPayments();
        setSelectedPayment(null);
        setReceiptFile(null);
        setBankReference('');
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Error uploading receipt:', error);
      toast.error('Dekont yüklenirken hata oluştu');
    } finally {
      setUploadingReceipt(false);
    }
  };

  const getStatusBadge = (status: PaymentRequest['status']) => {
    switch (status) {
      case 'pending':
        return <Badge variant="destructive"><Clock className="w-3 h-3 mr-1" />Ödeme Bekleniyor</Badge>;
      case 'receipt_uploaded':
        return <Badge variant="secondary"><Upload className="w-3 h-3 mr-1" />Dekont Yüklendi</Badge>;
      case 'verified':
        return <Badge variant="default"><CheckCircle className="w-3 h-3 mr-1" />Onaylandı</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Reddedildi</Badge>;
      default:
        return <Badge variant="outline">Bilinmeyen</Badge>;
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Ödemeler yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Ödemelerim</h1>
          <p className="text-gray-600 mt-1">
            Bekleyen ödemelerinizi görüntüleyin ve dekont yükleyin
          </p>
        </div>
      </div>

      {/* Payments List */}
      <div className="grid gap-6">
        {payments.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Bekleyen ödeme bulunamadı</h3>
              <p className="text-gray-600">Henüz ödeme yapmanız gereken bir sipariş bulunmuyor.</p>
            </CardContent>
          </Card>
        ) : (
          payments.map((payment) => (
            <Card key={payment.id} className="overflow-hidden">
              <CardHeader className="bg-gray-50">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">Sipariş #{payment.orderId}</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">
                      Oluşturulma: {formatDate(payment.createdAt)}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900 mb-2">
                      {formatCurrency(payment.amount, payment.currency)}
                    </div>
                    {getStatusBadge(payment.status)}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                {/* Bank Information */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-blue-900 mb-3 flex items-center">
                    <Building className="w-4 h-4 mr-2" />
                    Banka Bilgileri
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="text-blue-700 font-medium">Banka:</span>
                      <span className="ml-2">{payment.bankInfo.bankName}</span>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">Hesap Sahibi:</span>
                      <span className="ml-2">{payment.bankInfo.accountHolder}</span>
                    </div>
                    <div className="md:col-span-2">
                      <span className="text-blue-700 font-medium">IBAN:</span>
                      <span className="ml-2 font-mono">{payment.bankInfo.iban}</span>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">Referans Kodu:</span>
                      <span className="ml-2 font-mono font-bold">{payment.bankInfo.referenceCode}</span>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">Son Ödeme:</span>
                      <span className="ml-2">{formatDate(payment.dueDate)}</span>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-3">
                  {payment.status === 'pending' && (
                    <Button 
                      onClick={() => setSelectedPayment(payment)}
                      className="flex-1"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Dekont Yükle
                    </Button>
                  )}
                  
                  {payment.receipt && (
                    <Button variant="outline" className="flex-1">
                      <Eye className="w-4 h-4 mr-2" />
                      Dekontu Görüntüle
                    </Button>
                  )}
                  
                  {payment.status === 'rejected' && payment.rejectionReason && (
                    <div className="flex-1 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-start">
                        <AlertTriangle className="w-4 h-4 text-red-600 mt-0.5 mr-2 flex-shrink-0" />
                        <div>
                          <p className="text-sm font-medium text-red-800">Red Nedeni:</p>
                          <p className="text-sm text-red-700">{payment.rejectionReason}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Receipt Upload Modal */}
      {selectedPayment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Dekont Yükle</CardTitle>
              <p className="text-sm text-gray-600">
                Sipariş #{selectedPayment.orderId} için ödeme dekontunu yükleyin
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="receipt-file">Dekont Dosyası</Label>
                <Input
                  id="receipt-file"
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={(e) => setReceiptFile(e.target.files?.[0] || null)}
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">
                  PDF, JPG veya PNG formatında dosya yükleyebilirsiniz
                </p>
              </div>
              
              <div>
                <Label htmlFor="bank-reference">Banka Referans Numarası</Label>
                <Input
                  id="bank-reference"
                  value={bankReference}
                  onChange={(e) => setBankReference(e.target.value)}
                  placeholder="Havale referans numaranızı girin"
                  className="mt-1"
                />
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedPayment(null);
                    setReceiptFile(null);
                    setBankReference('');
                  }}
                  className="flex-1"
                >
                  İptal
                </Button>
                <Button
                  onClick={() => handleReceiptUpload(selectedPayment.id)}
                  disabled={uploadingReceipt || !receiptFile || !bankReference.trim()}
                  className="flex-1"
                >
                  {uploadingReceipt ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Yükleniyor...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4 mr-2" />
                      Yükle
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
