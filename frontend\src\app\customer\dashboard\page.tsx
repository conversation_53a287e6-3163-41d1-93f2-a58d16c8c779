'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import KPICard from '@/components/dashboard/components/KPICard';
import ChartWidget from '@/components/dashboard/components/ChartWidget';
import QuickActions from '@/components/dashboard/components/QuickActions';
import RecentActivity from '@/components/dashboard/components/RecentActivity';
import NotificationsModal from '@/components/notifications/NotificationsModal';
import { NotificationsProvider } from '@/contexts/notifications-context';

interface DashboardData {
  kpis: {
    activeOrders: number;
    pendingQuotes: number;
    completedOrders: number;
    totalSpent: number;
  };
  charts: {
    orderTrend: any;
    categoryDistribution: any;
  };
  recentActivity: any[];
}

// Helper functions
const getOrderTitle = (status: string) => {
  switch (status) {
    case 'PENDING': return 'Yeni sipariş alındı';
    case 'CONFIRMED': return 'Sipariş onaylandı';
    case 'PRODUCTION': return '<PERSON><PERSON>im başladı';
    case 'SHIPPED': return 'Kargo yolda';
    case 'DELIVERED': return 'Sipariş teslim edildi';
    case 'CANCELLED': return 'Sipariş iptal edildi';
    default: return 'Sipariş güncellendi';
  }
};

const mapOrderStatus = (status: string) => {
  switch (status) {
    case 'DELIVERED': return 'completed';
    case 'CANCELLED': return 'cancelled';
    case 'PENDING': return 'pending';
    case 'CONFIRMED':
    case 'PRODUCTION':
    case 'SHIPPED': return 'in-progress';
    default: return 'pending';
  }
};

const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInHours < 1) return 'Az önce';
  if (diffInHours < 24) return `${diffInHours} saat önce`;
  if (diffInDays === 1) return '1 gün önce';
  return `${diffInDays} gün önce`;
};

export default function CustomerDashboardPage() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [isNotificationsModalOpen, setIsNotificationsModalOpen] = useState(false);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      console.log('=== FETCHING DASHBOARD DATA ===');

      // Gerçek API çağrısı
      const response = await fetch('/api/users/dashboard-stats', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('=== DASHBOARD API RESPONSE ===', JSON.stringify(result, null, 2));

      if (result.success && result.data) {
        // Backend'ten gelen veriyi frontend formatına dönüştür
        const apiData = result.data;

        // Recent activity'yi frontend formatına dönüştür
        const formattedActivities = (apiData.recentActivity || []).map((order: any, index: number) => ({
          id: index + 1,
          type: 'order',
          title: getOrderTitle(order.status),
          description: `${order.quote?.product?.name || 'Ürün'} - ${order.totalAmount || 0} ${order.currency || 'USD'}`,
          time: formatTimeAgo(order.createdAt),
          status: mapOrderStatus(order.status)
        }));

        const dashboardData: DashboardData = {
          kpis: {
            activeOrders: apiData.activeOrders || 0,
            pendingQuotes: apiData.pendingQuotes || 0,
            completedOrders: apiData.completedOrders || 0,
            totalSpent: apiData.totalSpent || 0
          },
          charts: {
            orderTrend: {
              labels: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'],
              datasets: [
                {
                  label: 'Siparişler',
                  data: [0, 0, 0, 0, 0, 0], // Gerçek chart verisi için ayrı API gerekebilir
                  borderColor: 'rgb(59, 130, 246)',
                  backgroundColor: 'rgba(59, 130, 246, 0.1)',
                },
                {
                  label: 'Tamamlanan',
                  data: [0, 0, 0, 0, 0, 0],
                  borderColor: 'rgb(16, 185, 129)',
                  backgroundColor: 'rgba(16, 185, 129, 0.1)',
                }
              ]
            },
            categoryDistribution: {}
          },
          recentActivity: formattedActivities
        };

        setDashboardData(dashboardData);
      } else {
        throw new Error(result.error || 'Dashboard data fetch failed');
      }
    } catch (error) {
      console.error('Dashboard data fetch error:', error);
      // Set fallback data even on error
      setDashboardData({
        kpis: {
          activeOrders: 0,
          pendingQuotes: 0,
          completedOrders: 0,
          totalSpent: 0
        },
        charts: {
          orderTrend: { labels: [], datasets: [] },
          categoryDistribution: { labels: [], datasets: [] }
        },
        recentActivity: []
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleNavigate = (route: string) => {
    // Navigation is handled by the layout
    window.location.href = route;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg font-medium">Dashboard yükleniyor...</p>
          <p className="text-gray-500 text-sm mt-2">Veriler hazırlanıyor, lütfen bekleyin.</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <KPICard
            title="Bekleyen Teklifler"
            value={dashboardData?.kpis.pendingQuotes || 0}
            change={{ percentage: 8.3, trend: 'up', period: 'week' }}
            icon="file-text"
            color="blue"
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <KPICard
            title="Tamamlanan Siparişler"
            value={dashboardData?.kpis.completedOrders || 0}
            change={{ percentage: 15.2, trend: 'up', period: 'month' }}
            icon="check-circle"
            color="green"
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <KPICard
            title="Toplam Harcama"
            value={dashboardData?.kpis.totalSpent || 0}
            currency="USD"
            change={{ percentage: 12.1, trend: 'up', period: 'month' }}
            icon="dollar-sign"
            color="purple"
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <KPICard
            title="Aktif Siparişler"
            value={dashboardData?.kpis.activeOrders || 0}
            change={{ percentage: -2.1, trend: 'down', period: 'month' }}
            icon="clock"
            color="orange"
          />
        </motion.div>
      </div>

      {/* Charts and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        {/* Order Trend Chart */}
        <motion.div
          className="lg:col-span-2"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <ChartWidget
            type="line"
            title="Sipariş Trendi"
            data={dashboardData?.charts.orderTrend}
            height={300}
          />
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
        >
          <QuickActions
            onNavigate={handleNavigate}
            onNewQuoteRequest={() => {
              // Teklif modal'ını açmak için parent layout'a sinyal gönder
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('openQuoteModal'));
              }
            }}
            onOpenNotifications={() => setIsNotificationsModalOpen(true)}
          />
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
      >
        <RecentActivity
          activities={dashboardData?.recentActivity || []}
          onNavigate={handleNavigate}
        />
      </motion.div>

      {/* Notifications Modal */}
      <NotificationsProvider>
        <NotificationsModal
          isOpen={isNotificationsModalOpen}
          onClose={() => setIsNotificationsModalOpen(false)}
          onNavigate={handleNavigate}
        />
      </NotificationsProvider>
    </>
  );
}
