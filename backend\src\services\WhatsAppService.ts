import { PrismaClient } from '@prisma/client';
import { EmailService } from './EmailService';
import { createEmailConfig } from '../config/email';

const prisma = new PrismaClient();

export interface WhatsAppMessage {
  to: string;
  message: string;
  type: 'text' | 'template';
  templateData?: any;
}

export interface WhatsAppConfig {
  businessNumber: string;
  enabled: boolean;
  autoReply: boolean;
  businessHours: {
    start: string;
    end: string;
    timezone: string;
  };
}

export class WhatsAppService {
  private emailService: EmailService;
  private config: WhatsAppConfig;

  constructor() {
    const emailConfig = createEmailConfig();
    this.emailService = new EmailService(emailConfig);
    
    this.config = {
      businessNumber: process.env.WHATSAPP_BUSINESS_NUMBER || '+905551234567',
      enabled: process.env.WHATSAPP_ENABLED === 'true',
      autoReply: process.env.WHATSAPP_AUTO_REPLY === 'true',
      businessHours: {
        start: process.env.WHATSAPP_BUSINESS_START || '09:00',
        end: process.env.WHATSAPP_BUSINESS_END || '18:00',
        timezone: process.env.WHATSAPP_TIMEZONE || 'Europe/Istanbul',
      },
    };
  }

  /**
   * Generate WhatsApp Web URL for sending message
   */
  generateWhatsAppURL(phoneNumber: string, message: string): string {
    // Clean phone number (remove spaces, dashes, etc.)
    const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
    
    // Encode message for URL
    const encodedMessage = encodeURIComponent(message);
    
    // Generate WhatsApp Web URL
    return `https://wa.me/${cleanNumber}?text=${encodedMessage}`;
  }

  /**
   * Generate WhatsApp Web URL for business number
   */
  generateBusinessWhatsAppURL(message?: string): string {
    const encodedMessage = message ? encodeURIComponent(message) : '';
    const url = `https://wa.me/${this.config.businessNumber.replace(/[^\d]/g, '')}`;
    
    return message ? `${url}?text=${encodedMessage}` : url;
  }

  /**
   * Send payment instructions via WhatsApp (generates URL)
   */
  async sendPaymentInstructions(customerPhone: string, data: {
    customerName: string;
    orderNumber: string;
    amount: number;
    currency: string;
    referenceCode: string;
    bankInfo: {
      bankName: string;
      iban: string;
      accountHolder: string;
    };
  }): Promise<{ whatsappUrl: string; message: string }> {
    
    const message = this.formatPaymentInstructionsMessage(data);
    const whatsappUrl = this.generateWhatsAppURL(customerPhone, message);

    // Log the WhatsApp message attempt
    await this.logWhatsAppMessage({
      to: customerPhone,
      message,
      type: 'template',
      templateData: data,
    });

    return { whatsappUrl, message };
  }

  /**
   * Send order status update via WhatsApp
   */
  async sendOrderStatusUpdate(customerPhone: string, data: {
    customerName: string;
    orderNumber: string;
    status: string;
    message: string;
  }): Promise<{ whatsappUrl: string; message: string }> {
    
    const message = this.formatOrderStatusMessage(data);
    const whatsappUrl = this.generateWhatsAppURL(customerPhone, message);

    await this.logWhatsAppMessage({
      to: customerPhone,
      message,
      type: 'template',
      templateData: data,
    });

    return { whatsappUrl, message };
  }

  /**
   * Send escrow notification via WhatsApp
   */
  async sendEscrowNotification(customerPhone: string, data: {
    customerName: string;
    orderNumber: string;
    type: 'payment_confirmed' | 'goods_ready' | 'payment_released';
    amount?: number;
    currency?: string;
  }): Promise<{ whatsappUrl: string; message: string }> {
    
    const message = this.formatEscrowNotificationMessage(data);
    const whatsappUrl = this.generateWhatsAppURL(customerPhone, message);

    await this.logWhatsAppMessage({
      to: customerPhone,
      message,
      type: 'template',
      templateData: data,
    });

    return { whatsappUrl, message };
  }

  /**
   * Format payment instructions message
   */
  private formatPaymentInstructionsMessage(data: {
    customerName: string;
    orderNumber: string;
    amount: number;
    currency: string;
    referenceCode: string;
    bankInfo: {
      bankName: string;
      iban: string;
      accountHolder: string;
    };
  }): string {
    return `🏦 *Ödeme Talimatları*

Merhaba ${data.customerName},

Sipariş ${data.orderNumber} için ödeme yapmanız gerekmektedir.

💰 *Ödenecek Tutar:* ${data.amount} ${data.currency}

🏛️ *Banka Bilgileri:*
• Banka: ${data.bankInfo.bankName}
• Hesap Sahibi: ${data.bankInfo.accountHolder}
• IBAN: ${data.bankInfo.iban}

🔢 *Referans Kodu:* ${data.referenceCode}
⚠️ *Önemli:* Havale yaparken mutlaka referans kodunu açıklama kısmına yazın!

📞 Sorularınız için bize WhatsApp'tan ulaşabilirsiniz.

Doğal Taş Pazaryeri`;
  }

  /**
   * Format order status message
   */
  private formatOrderStatusMessage(data: {
    customerName: string;
    orderNumber: string;
    status: string;
    message: string;
  }): string {
    const statusEmoji = this.getStatusEmoji(data.status);
    
    return `${statusEmoji} *Sipariş Durumu Güncellendi*

Merhaba ${data.customerName},

Sipariş ${data.orderNumber} durumu güncellendi:

📋 *Durum:* ${data.status}
💬 *Açıklama:* ${data.message}

📞 Detaylar için bize WhatsApp'tan ulaşabilirsiniz.

Doğal Taş Pazaryeri`;
  }

  /**
   * Format escrow notification message
   */
  private formatEscrowNotificationMessage(data: {
    customerName: string;
    orderNumber: string;
    type: 'payment_confirmed' | 'goods_ready' | 'payment_released';
    amount?: number;
    currency?: string;
  }): string {
    let emoji = '💰';
    let title = '';
    let content = '';

    switch (data.type) {
      case 'payment_confirmed':
        emoji = '✅';
        title = 'Ödeme Onaylandı';
        content = 'Ödemeniz onaylandı ve emanette tutuluyor. Üretici ürününüzü hazırlamaya başlayacak.';
        break;
      case 'goods_ready':
        emoji = '📦';
        title = 'Ürün Hazır';
        content = 'Üretici ürününüzün hazır olduğunu bildirdi. Lütfen onaylayın ve ödeme talimatı verin.';
        break;
      case 'payment_released':
        emoji = '🎉';
        title = 'İşlem Tamamlandı';
        content = `Siparişiniz tamamlandı ve üreticiye ödeme yapıldı${data.amount ? ` (${data.amount} ${data.currency})` : ''}.`;
        break;
    }

    return `${emoji} *${title}*

Merhaba ${data.customerName},

Sipariş ${data.orderNumber} için güncelleme:

${content}

📞 Sorularınız için bize WhatsApp'tan ulaşabilirsiniz.

Doğal Taş Pazaryeri`;
  }

  /**
   * Get status emoji
   */
  private getStatusEmoji(status: string): string {
    const emojiMap: { [key: string]: string } = {
      'pending': '⏳',
      'confirmed': '✅',
      'processing': '🔄',
      'shipped': '🚚',
      'delivered': '📦',
      'cancelled': '❌',
      'completed': '🎉',
    };

    return emojiMap[status.toLowerCase()] || '📋';
  }

  /**
   * Log WhatsApp message to database
   */
  private async logWhatsAppMessage(messageData: WhatsAppMessage): Promise<void> {
    try {
      // This would typically be stored in a WhatsApp messages table
      // For now, we'll just log it
      console.log('WhatsApp message logged:', {
        to: messageData.to,
        type: messageData.type,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to log WhatsApp message:', error);
    }
  }

  /**
   * Check if within business hours
   */
  isWithinBusinessHours(): boolean {
    const now = new Date();
    const currentTime = now.toLocaleTimeString('tr-TR', { 
      hour12: false, 
      timeZone: this.config.businessHours.timezone 
    });
    
    return currentTime >= this.config.businessHours.start && 
           currentTime <= this.config.businessHours.end;
  }

  /**
   * Get auto-reply message
   */
  getAutoReplyMessage(): string {
    if (this.isWithinBusinessHours()) {
      return `Merhaba! 👋

Doğal Taş Pazaryeri'ne hoş geldiniz. Size nasıl yardımcı olabiliriz?

🕒 Çalışma Saatleri: ${this.config.businessHours.start} - ${this.config.businessHours.end}

📞 Hemen size dönüş yapacağız!`;
    } else {
      return `Merhaba! 👋

Doğal Taş Pazaryeri'ne hoş geldiniz. 

🕒 Çalışma Saatleri: ${this.config.businessHours.start} - ${this.config.businessHours.end}

Mesaj saatleri dışında ulaştınız. En kısa sürede size dönüş yapacağız.

🌙 İyi geceler!`;
    }
  }

  /**
   * Generate customer support WhatsApp URL
   */
  generateSupportURL(customerInfo?: {
    name?: string;
    orderNumber?: string;
    issue?: string;
  }): string {
    let message = `Merhaba! 👋\n\nDoğal Taş Pazaryeri müşteri desteği ile iletişime geçmek istiyorum.\n\n`;
    
    if (customerInfo) {
      if (customerInfo.name) {
        message += `👤 Ad: ${customerInfo.name}\n`;
      }
      if (customerInfo.orderNumber) {
        message += `📋 Sipariş No: ${customerInfo.orderNumber}\n`;
      }
      if (customerInfo.issue) {
        message += `❓ Konu: ${customerInfo.issue}\n`;
      }
    }
    
    message += `\nYardımınızı bekliyorum. Teşekkürler! 🙏`;
    
    return this.generateBusinessWhatsAppURL(message);
  }

  /**
   * Get WhatsApp configuration
   */
  getConfig(): WhatsAppConfig {
    return this.config;
  }

  /**
   * Update WhatsApp configuration
   */
  updateConfig(newConfig: Partial<WhatsAppConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}
