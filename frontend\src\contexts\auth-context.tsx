"use client"

import * as React from "react"
import { useRouter } from 'next/navigation'
import { CustomerRegistrationModal } from "@/components/ui/customer-registration-modal"

interface User {
  id: string
  name: string
  email: string
  role: 'customer' | 'producer' | 'admin'
  company?: string
}

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (email: string, password: string) => Promise<boolean>
  register: (name: string, email: string, password: string) => Promise<boolean>
  customerRegister: (data: any) => Promise<boolean>
  logout: () => void
  showLoginModal: () => void
  showRegisterModal: () => void
  showCustomerRegisterModal: () => void
}

const AuthContext = React.createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const [user, setUser] = React.useState<User | null>(null)
  const [isLoginModalOpen, setIsLoginModalOpen] = React.useState(false)
  const [isRegisterModalOpen, setIsRegisterModalOpen] = React.useState(false)
  const [isCustomerRegisterModalOpen, setIsCustomerRegisterModalOpen] = React.useState(false)
  const [isLoading, setIsLoading] = React.useState(true)
  const [mounted, setMounted] = React.useState(false)

  // Load user from localStorage on mount
  React.useEffect(() => {
    setMounted(true)
    setIsLoading(true)

    try {
      const savedUser = localStorage.getItem('user')
      const authToken = localStorage.getItem('authToken')

      console.log('=== LOADING USER FROM LOCALSTORAGE ===');
      console.log('Saved user:', savedUser);
      console.log('Auth token exists:', !!authToken);

      // Both user and token must exist for valid session
      if (savedUser && savedUser !== 'null' && authToken) {
        const parsedUser = JSON.parse(savedUser)
        setUser(parsedUser)
        console.log('=== USER LOADED FROM LOCALSTORAGE ===', parsedUser);
      } else {
        // Clear invalid session data
        localStorage.removeItem('user')
        localStorage.removeItem('authToken')
        // Clear cookie as well
        document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;'
        setUser(null)
        console.log('=== NO VALID USER IN LOCALSTORAGE ===');
      }
    } catch (error) {
      console.error('Error loading user from localStorage:', error)
      // Clear corrupted data
      localStorage.removeItem('user')
      localStorage.removeItem('authToken')
      document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;'
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Save user to localStorage whenever user changes
  React.useEffect(() => {
    if (user) {
      localStorage.setItem('user', JSON.stringify(user))
      localStorage.setItem('authToken', 'mock-token-' + Date.now())
      console.log('=== USER SAVED TO LOCALSTORAGE VIA EFFECT ===', user);
    }
    // NOT: user null olduğunda localStorage'ı temizlemiyoruz
    // Sadece logout fonksiyonunda temizliyoruz
  }, [user])

  const login = React.useCallback(async (email: string, password: string): Promise<boolean> => {
    console.log('=== LOGIN ATTEMPT ===');
    console.log('Email:', email);

    // Basit validation
    if (!email || !password) {
      alert('Email ve şifre gereklidir');
      return false;
    }

    try {
      // Login request gönder (Next.js rewrites ile backend'e yönlendirilir)
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include', // Cookies için
        body: JSON.stringify({ email, password, userType: 'customer' })
      });

      const data = await response.json();
      console.log('=== BACKEND RESPONSE ===', data);

      if (response.ok && data.success) {
        // Kullanıcı tipini kontrol et - backend'de yapılıyor
        console.log('Login successful, user type:', data.data.user.userType);

        // Backend'den gelen user bilgilerini kullan
        const user: User = {
          id: data.data.user.id,
          name: data.data.user.name || `${data.data.user.profile?.contactPerson}` || email.split('@')[0],
          email: data.data.user.email,
          role: data.data.user.role || data.data.user.userType || 'customer',
          company: data.data.user.companyName || data.data.user.profile?.companyName
        };

        console.log('=== USER AUTHENTICATED ===', user);

        // Token'ları kaydet (localStorage + cookie)
        if (data.data.accessToken) {
          localStorage.setItem('authToken', data.data.accessToken);
          // Middleware için cookie'ye de kaydet
          document.cookie = `auth-token=${data.data.accessToken}; path=/; max-age=${data.data.expiresIn || 900}; SameSite=Lax`;
        }
        if (data.data.refreshToken) {
          localStorage.setItem('refreshToken', data.data.refreshToken);
        }

        // User state'ini güncelle
        setUser(user);
        setIsLoginModalOpen(false);

        // Role'e göre yönlendirme
        const redirectUrl = user.role === 'customer' ? '/customer/dashboard' :
                           user.role === 'producer' ? '/producer/dashboard' :
                           user.role === 'admin' ? '/admin/dashboard' : '/';

        console.log('=== REDIRECTING TO ===', redirectUrl);

        // Next.js router ile yönlendirme
        setTimeout(() => {
          console.log('=== ROUTER PUSH CALLED ===', redirectUrl);
          try {
            router.push(redirectUrl);
            console.log('=== ROUTER PUSH SUCCESS ===');
          } catch (error) {
            console.error('=== ROUTER PUSH ERROR ===', error);
            // Fallback to window.location
            window.location.href = redirectUrl;
          }
        }, 500);

        return true;
      } else {
        console.log('=== LOGIN FAILED ===', data.error);
        const errorMessage = data.error || 'Bilinmeyen hata';
        alert('Giriş başarısız: ' + errorMessage);
        return false;
      }
    } catch (error) {
      console.error('=== LOGIN ERROR ===', error);
      return false;
    }
  }, [])

  const register = React.useCallback(async (name: string, email: string, password: string): Promise<boolean> => {
    // Mock register - gerçek uygulamada API çağrısı yapılacak
    if (name && email && password) {
      const mockUser: User = {
        id: Date.now().toString(),
        name: name,
        email: email,
        role: 'customer'
      }
      setUser(mockUser)
      setIsRegisterModalOpen(false)
      return true
    }
    return false
  }, [])

  const customerRegister = React.useCallback(async (data: any): Promise<boolean> => {
    console.log('=== CUSTOMER REGISTER ATTEMPT ===');
    console.log('Registration data:', JSON.stringify(data, null, 2));

    if (!data.firstName || !data.lastName || !data.email || !data.password || !data.companyName) {
      alert('Tüm zorunlu alanları doldurunuz');
      return false;
    }

    try {
      // Önce backend'in çalışıp çalışmadığını test edelim
      console.log('=== TESTING BACKEND CONNECTION ===');
      try {
        const healthResponse = await fetch('/api/health');
        const healthData = await healthResponse.json();
        console.log('=== BACKEND HEALTH ===', JSON.stringify(healthData, null, 2));
      } catch (healthError) {
        console.error('=== BACKEND HEALTH CHECK FAILED ===', healthError);
        alert('Backend sunucusu çalışmıyor. Lütfen backend\'i başlatın.');
        return false;
      }

      const requestData = {
        email: data.email,
        password: data.password,
        userType: 'customer', // Backend küçük harf bekliyor
        companyName: data.companyName,
        countryCode: 'TR', // Şimdilik sabit TR, sonra ülke seçimi eklenebilir
        contactPerson: `${data.firstName} ${data.lastName}`,
        phone: data.phone
      };

      console.log('=== SENDING REGISTER REQUEST ===', JSON.stringify(requestData, null, 2));

      // Backend'e kayıt isteği gönder
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(requestData)
      });

      const result = await response.json();
      console.log('=== REGISTER RESPONSE ===', JSON.stringify(result, null, 2));
      console.log('=== RESPONSE STATUS ===', response.status);
      console.log('=== RESPONSE HEADERS ===', [...response.headers.entries()]);

      if (response.ok && result.success) {
        // Kayıt başarılı
        setIsCustomerRegisterModalOpen(false);

        // Başarılı kayıt mesajı göster
        alert('Kayıt başarılı! Şimdi giriş yapabilirsiniz.');

        // Giriş modalını aç
        setIsLoginModalOpen(true);

        return true;
      } else {
        console.log('=== REGISTER FAILED ===', JSON.stringify(result, null, 2));
        // Detaylı hata mesajını göster
        let errorMessage = 'Kayıt sırasında bir hata oluştu';
        if (result.error) {
          if (typeof result.error === 'string') {
            errorMessage = result.error;
          } else if (result.error.message) {
            errorMessage = result.error.message;
          } else if (result.error.details) {
            // Validation hatalarını göster
            errorMessage = result.error.details.map((detail: any) => detail.message).join(', ');
          }
        }
        alert(errorMessage);
        return false;
      }
    } catch (error) {
      console.error('=== REGISTER ERROR ===', error);
      alert('Kayıt sırasında bir hata oluştu');
      return false;
    }
  }, [])

  const logout = React.useCallback(() => {
    console.log('=== LOGOUT ===');

    // Clear user state
    setUser(null)

    // Clear all auth data
    if (typeof window !== 'undefined') {
      localStorage.removeItem('user')
      localStorage.removeItem('authToken')
      localStorage.removeItem('producer')

      // Clear cookies
      document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;'
      document.cookie = 'producer=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;'

      // Çıkış sonrası ana sayfaya yönlendir
      window.location.href = '/'
    }
  }, [])

  const showLoginModal = React.useCallback(() => {
    setIsLoginModalOpen(true)
    setIsRegisterModalOpen(false)
    setIsCustomerRegisterModalOpen(false)
  }, [])

  const showRegisterModal = React.useCallback(() => {
    setIsRegisterModalOpen(true)
    setIsLoginModalOpen(false)
    setIsCustomerRegisterModalOpen(false)
  }, [])

  const showCustomerRegisterModal = React.useCallback(() => {
    setIsCustomerRegisterModalOpen(true)
    setIsLoginModalOpen(false)
    setIsRegisterModalOpen(false)
  }, [])

  const value = React.useMemo(() => ({
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    customerRegister,
    logout,
    showLoginModal,
    showRegisterModal,
    showCustomerRegisterModal
  }), [user, isLoading, login, register, customerRegister, logout, showLoginModal, showRegisterModal, showCustomerRegisterModal])

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <AuthContext.Provider value={value}>
        <div style={{ visibility: "hidden" }}>{children}</div>
      </AuthContext.Provider>
    )
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
      {/* Login Modal */}
      {isLoginModalOpen && (
        <LoginModal
          isOpen={isLoginModalOpen}
          onClose={() => setIsLoginModalOpen(false)}
          onLogin={login}
          onSwitchToRegister={showRegisterModal}
          onSwitchToCustomerRegister={showCustomerRegisterModal}
        />
      )}

      {/* Register Modal */}
      {isRegisterModalOpen && (
        <RegisterModal
          isOpen={isRegisterModalOpen}
          onClose={() => setIsRegisterModalOpen(false)}
          onRegister={register}
          onSwitchToLogin={showLoginModal}
        />
      )}

      {/* Customer Registration Modal */}
      {isCustomerRegisterModalOpen && (
        <CustomerRegistrationModal
          isOpen={isCustomerRegisterModalOpen}
          onClose={() => setIsCustomerRegisterModalOpen(false)}
          onRegister={customerRegister}
          onSwitchToLogin={showLoginModal}
        />
      )}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = React.useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Login Modal Component
interface LoginModalProps {
  isOpen: boolean
  onClose: () => void
  onLogin: (email: string, password: string) => Promise<boolean>
  onSwitchToRegister: () => void
  onSwitchToCustomerRegister: () => void
}

function LoginModal({ isOpen, onClose, onLogin, onSwitchToRegister, onSwitchToCustomerRegister }: LoginModalProps) {
  const [email, setEmail] = React.useState('')
  const [password, setPassword] = React.useState('')
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState('')



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const success = await onLogin(email, password)
      if (!success) {
        setError('Geçersiz e-posta veya şifre')
      }
    } catch (error) {
      setError('Giriş sırasında bir hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Giriş Yap</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              E-posta
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Şifre
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
              placeholder="••••••••"
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}

          <div className="flex gap-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              İptal
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-stone-600 text-white rounded-md hover:bg-stone-700 disabled:opacity-50"
            >
              {isLoading ? 'Giriş yapılıyor...' : 'Giriş Yap'}
            </button>
          </div>
        </form>

        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600">
            Hesabınız yoksa{' '}
            <button
              onClick={onSwitchToCustomerRegister}
              className="text-stone-600 hover:text-stone-700 font-medium"
            >
              hemen üye olun
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}

// Register Modal Component
interface RegisterModalProps {
  isOpen: boolean
  onClose: () => void
  onRegister: (name: string, email: string, password: string) => Promise<boolean>
  onSwitchToLogin: () => void
}

function RegisterModal({ isOpen, onClose, onRegister, onSwitchToLogin }: RegisterModalProps) {
  const [name, setName] = React.useState('')
  const [email, setEmail] = React.useState('')
  const [password, setPassword] = React.useState('')
  const [confirmPassword, setConfirmPassword] = React.useState('')
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    if (password !== confirmPassword) {
      setError('Şifreler eşleşmiyor')
      setIsLoading(false)
      return
    }

    if (password.length < 6) {
      setError('Şifre en az 6 karakter olmalıdır')
      setIsLoading(false)
      return
    }

    try {
      const success = await onRegister(name, email, password)
      if (!success) {
        setError('Üye olurken bir hata oluştu')
      }
    } catch (error) {
      setError('Üye olurken bir hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />

      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Üye Ol</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Ad Soyad
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
              placeholder="Adınız Soyadınız"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              E-posta
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Şifre
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
              placeholder="••••••••"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Şifre Tekrar
            </label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500"
              placeholder="••••••••"
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}

          <div className="flex gap-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              İptal
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-stone-600 text-white rounded-md hover:bg-stone-700 disabled:opacity-50"
            >
              {isLoading ? 'Üye oluyor...' : 'Üye Ol'}
            </button>
          </div>
        </form>

        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600">
            Zaten hesabınız var mı?{' '}
            <button
              onClick={onSwitchToLogin}
              className="text-stone-600 hover:text-stone-700 font-medium"
            >
              Giriş Yap
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
