'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';

export default function EditSalePage() {
  const router = useRouter();
  const params = useParams();
  const saleId = params.id;

  const [formData, setFormData] = useState({
    productName: '',
    thickness: '',
    width: '',
    length: '',
    surfaceFinish: '',
    customSurfaceFinish: '',
    companyName: '',
    phone: '',
    email: '',
    address: '',
    description: '',
    salePrice: '',
    saleDate: '',
    quantity: '',
    deliveryStatus: 'Bekliyor',
    deliveryReason: '',
    deliveryDate: '',
    deliveryReminder: false,
    deliveryReminderDate: '',
    paymentStatus: 'Bekliyor',
    paymentMethod: '',
    paidAmount: '',
    remainingAmount: '',
    installmentCount: '',
    installmentDates: '',
    checkCount: '',
    checkDetails: [],
    category: '',
    customCategory: '',
    orderNumber: ''
  });

  const [loading, setLoading] = useState(true);

  // Mock data - gerçek uygulamada API'den gelecek
  const mockSaleData = {
    1: {
      productName: 'Beyaz Mermer',
      thickness: '2.0',
      width: '60',
      length: '120',
      surfaceFinish: 'Cilalı',
      customSurfaceFinish: '',
      companyName: 'ABC İnşaat Ltd.',
      phone: '0532 123 45 67',
      email: '<EMAIL>',
      address: 'İstanbul, Türkiye',
      description: 'Yüksek kalite beyaz mermer',
      salePrice: '2500.00',
      saleDate: '2024-01-15',
      quantity: '15.5',
      deliveryStatus: 'Teslim Edildi',
      deliveryReason: '',
      deliveryDate: '2024-01-20',
      deliveryReminder: false,
      deliveryReminderDate: '',
      paymentStatus: 'Ödendi',
      paymentMethod: 'Banka Havalesi',
      paidAmount: '',
      remainingAmount: '',
      installmentCount: '',
      installmentDates: '',
      checkCount: '',
      checkDetails: [],
      category: 'Mermer',
      customCategory: '',
      orderNumber: 'SIP-2024-001'
    }
  };

  useEffect(() => {
    // Mock API call
    setTimeout(() => {
      const saleData = mockSaleData[saleId as keyof typeof mockSaleData];
      if (saleData) {
        setFormData(saleData);
      }
      setLoading(false);
    }, 500);
  }, [saleId]);

  const surfaceFinishes = [
    'Cilalı', 'Mat', 'Antik', 'Fırçalı', 'Alevli', 'Doğal', 'Sandblast', 'Diğer'
  ];

  const categories = [
    'Mermer', 'Granit', 'Traverten', 'Oniks', 'Kuvarsit', 'Diğer'
  ];

  const deliveryStatuses = [
    'Bekliyor', 'Hazırlanıyor', 'Kargoda', 'Teslim Edildi'
  ];

  const paymentStatuses = [
    'Bekliyor', 'Ödendi', 'Kısmi Ödendi', 'Gecikmiş'
  ];

  const paymentMethods = [
    'Nakit', 'Banka Havalesi', 'Kredi Kartı', 'Çek'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // API call will be here
      console.log('Satış güncellendi:', formData);
      
      // Success message and redirect
      alert('Satış kaydı başarıyla güncellendi!');
      router.push('/customer/analytics/sales');
    } catch (error) {
      console.error('Satış güncellenirken hata:', error);
      alert('Satış güncellenirken bir hata oluştu.');
    }
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Satış bilgileri yükleniyor...</span>
        </div>
      </div>
    );
  }

  if (!mockSaleData[saleId as keyof typeof mockSaleData]) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Satış Bulunamadı</h2>
          <p className="text-gray-600 mb-6">Aradığınız satış kaydı bulunamadı.</p>
          <Link
            href="/customer/analytics/sales"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Satış Listesine Dön
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Satış Kaydını Düzenle</h1>
            <p className="text-gray-600 mt-1">Satış bilgilerini güncelleyin</p>
          </div>
          <Link
            href="/customer/analytics/sales"
            className="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            ← Geri Dön
          </Link>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        
        {/* Ürün Bilgileri */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Ürün Bilgileri</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ürün Adı *
              </label>
              <input
                type="text"
                name="productName"
                value={formData.productName}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kategori *
              </label>
              <select
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Kategori Seçin</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Yüzey İşlemi *
              </label>
              <select
                name="surfaceFinish"
                value={formData.surfaceFinish}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Yüzey İşlemi Seçin</option>
                {surfaceFinishes.map(finish => (
                  <option key={finish} value={finish}>{finish}</option>
                ))}
              </select>
            </div>

          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Link
            href="/customer/analytics/sales"
            className="px-6 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            İptal
          </Link>
          <button
            type="submit"
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            Değişiklikleri Kaydet
          </button>
        </div>

      </form>
    </div>
  );
}
