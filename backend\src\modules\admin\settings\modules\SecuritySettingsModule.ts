import { SettingsModule } from '../core/SettingsModule';
import { SettingsCategory } from '../types/settings.types';

export class SecuritySettingsModule extends SettingsModule {
  protected category: SettingsCategory = 'SECURITY';

  protected getDefaultSettings() {
    return {
      passwordMinLength: {
        key: 'password_min_length',
        value: 8,
        type: 'number',
        category: this.category,
        description: 'Minimum password length'
      },
      passwordRequireUppercase: {
        key: 'password_require_uppercase',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Require uppercase letters in password'
      },
      passwordRequireNumbers: {
        key: 'password_require_numbers',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Require numbers in password'
      },
      passwordRequireSpecialChars: {
        key: 'password_require_special_chars',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Require special characters in password'
      },
      sessionTimeout: {
        key: 'session_timeout',
        value: 3600, // 1 hour in seconds
        type: 'number',
        category: this.category,
        description: 'Session timeout in seconds'
      },
      maxLoginAttempts: {
        key: 'max_login_attempts',
        value: 5,
        type: 'number',
        category: this.category,
        description: 'Maximum login attempts before lockout'
      },
      lockoutDuration: {
        key: 'lockout_duration',
        value: 900, // 15 minutes in seconds
        type: 'number',
        category: this.category,
        description: 'Account lockout duration in seconds'
      },
      twoFactorEnabled: {
        key: 'two_factor_enabled',
        value: false,
        type: 'boolean',
        category: this.category,
        description: 'Enable two-factor authentication'
      },
      allowedFileTypes: {
        key: 'allowed_file_types',
        value: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
        type: 'array',
        category: this.category,
        description: 'Allowed file upload types'
      },
      maxFileSize: {
        key: 'max_file_size',
        value: ********, // 10MB in bytes
        type: 'number',
        category: this.category,
        description: 'Maximum file upload size in bytes'
      },
      enableCaptcha: {
        key: 'enable_captcha',
        value: true,
        type: 'boolean',
        category: this.category,
        description: 'Enable CAPTCHA for forms'
      },
      ipWhitelist: {
        key: 'ip_whitelist',
        value: [],
        type: 'array',
        category: this.category,
        description: 'IP addresses allowed to access admin panel'
      }
    };
  }

  async validateSetting(key: string, value: any): Promise<boolean> {
    switch (key) {
      case 'password_min_length':
        return typeof value === 'number' && value >= 6 && value <= 50;
      case 'password_require_uppercase':
      case 'password_require_numbers':
      case 'password_require_special_chars':
      case 'two_factor_enabled':
      case 'enable_captcha':
        return typeof value === 'boolean';
      case 'session_timeout':
        return typeof value === 'number' && value > 0;
      case 'max_login_attempts':
        return typeof value === 'number' && value > 0 && value <= 20;
      case 'lockout_duration':
        return typeof value === 'number' && value > 0;
      case 'allowed_file_types':
        return Array.isArray(value) && value.every(type => typeof type === 'string');
      case 'max_file_size':
        return typeof value === 'number' && value > 0;
      case 'ip_whitelist':
        return Array.isArray(value) && value.every(ip => typeof ip === 'string');
      default:
        return true;
    }
  }
}
