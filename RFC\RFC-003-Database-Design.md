# RFC-003: Veritabanı Tasarımı ve Şema

**Durum**: DRAFT  
**Yazar**: Augment Agent  
**Tarih**: 2025-06-27  
**Bağımlılıklar**: RFC-001, RFC-002  
**İlgili RFC'ler**: RFC-101, RFC-201, RFC-301, RFC-401  

## Özet

Bu RFC, Türkiye Doğal Taş Marketplace platformunun veritabanı tasarımını, şema yapısını ve veri yönetim stratejilerini tanımlar. PostgreSQL ana veritabanı, Redis cache ve Elasticsearch arama motoru kullanılarak optimize edilmiş bir veri mimarisi önerilmektedir.

## Motivasyon

Platformun karmaşık iş mantığı, yüksek performans gereksinimleri ve veri bütünlüğü ihtiyaçları için iyi tasarlanmış bir veritabanı şeması kritik öneme sahiptir. Özellikle anonim tekli<PERSON> siste<PERSON>, escrow ödemeler ve çok dilli destek için özel veri yapıları gereklidir.

## Detaylı Tasarım

### 1. Veritabanı Mimarisi

#### 1.1 Multi-Database Strategy
```
┌─────────────────┬─────────────────┬─────────────────┐
│   PostgreSQL    │      Redis      │  Elasticsearch  │
│   (Primary)     │     (Cache)     │    (Search)     │
├─────────────────┼─────────────────┼─────────────────┤
│ • Transactional │ • Sessions      │ • Product Search│
│ • User Data     │ • Cache         │ • Logs          │
│ • Orders        │ • Rate Limiting │ • Analytics     │
│ • Payments      │ • Pub/Sub       │ • Full-text     │
│ • Bids          │ • Temp Data     │ • Aggregations  │
└─────────────────┴─────────────────┴─────────────────┘
```

### 2. PostgreSQL Şema Tasarımı

#### 2.1 Core Tables

##### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_type user_type_enum NOT NULL, -- 'producer', 'customer', 'admin'
    status user_status_enum DEFAULT 'pending', -- 'pending', 'active', 'suspended', 'banned'
    email_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_type_status ON users(user_type, status);
```

##### User Profiles Table
```sql
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- Company Information
    company_name VARCHAR(255) NOT NULL,
    company_type company_type_enum, -- 'producer', 'importer', 'distributor', 'warehouse', 'applicator', 'architect', 'construction'
    tax_number VARCHAR(50),
    trade_registry_number VARCHAR(50),

    -- Contact Information
    contact_person VARCHAR(255),
    phone VARCHAR(20),
    address JSONB, -- Flexible address structure
    country_code CHAR(2) NOT NULL,

    -- Business Information
    business_description TEXT,
    production_capacity INTEGER, -- For producers
    service_countries TEXT[], -- Array of country codes

    -- Producer Specific Fields (PRD requirements)
    production_capacity_report JSONB, -- Detailed capacity information
    certificates JSONB, -- ISO, CE, etc. certificates
    bank_information JSONB, -- Bank details for payments

    -- Custom Manufacturing (Fason Üretim)
    offers_custom_manufacturing BOOLEAN DEFAULT FALSE,
    custom_manufacturing_details TEXT,

    -- Company Introduction
    company_introduction TEXT, -- Firma tanıtım metni

    -- Verification
    verification_status verification_status_enum DEFAULT 'pending',
    verification_documents JSONB, -- Document references
    verified_at TIMESTAMP WITH TIME ZONE,
    verified_by UUID REFERENCES users(id),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_country ON user_profiles(country_code);
CREATE INDEX idx_user_profiles_custom_manufacturing ON user_profiles(offers_custom_manufacturing);
```

##### Producer Locations Table (Ocak ve Fabrika Lokasyonları)
```sql
CREATE TABLE producer_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    producer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- Location Information
    location_type location_type_enum NOT NULL, -- 'quarry', 'factory'
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    google_maps_link TEXT,
    coordinates POINT, -- PostGIS point for lat/lng

    -- Additional Details
    description TEXT,
    capacity INTEGER, -- Production capacity for this location
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_producer_locations_producer ON producer_locations(producer_id);
CREATE INDEX idx_producer_locations_type ON producer_locations(location_type);
CREATE INDEX idx_producer_locations_coordinates ON producer_locations USING GIST(coordinates);
```

##### Stone Analysis Reports Table (Taş Analiz Raporları)
```sql
CREATE TABLE stone_analysis_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    producer_id UUID NOT NULL REFERENCES users(id),
    product_category_id UUID REFERENCES product_categories(id),

    -- Report Information
    report_name VARCHAR(255) NOT NULL,
    report_date DATE NOT NULL,
    laboratory_name VARCHAR(255),

    -- Technical Properties (PRD requirements)
    density DECIMAL(5,2), -- Yoğunluk (kg/m³)
    hardness DECIMAL(5,2), -- Sertlik
    water_absorption DECIMAL(5,2), -- Su emme oranı (%)
    freeze_thaw_resistance DECIMAL(5,2), -- Donma-çözülme direnci
    compressive_strength DECIMAL(8,2), -- Basınç dayanımı (MPa)
    flexural_strength DECIMAL(8,2), -- Eğilme dayanımı (MPa)

    -- Additional Properties
    additional_properties JSONB, -- Other technical properties

    -- Document Reference
    report_document_url TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_stone_analysis_producer ON stone_analysis_reports(producer_id);
CREATE INDEX idx_stone_analysis_category ON stone_analysis_reports(product_category_id);
```

##### Products Table
```sql
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    producer_id UUID NOT NULL REFERENCES users(id),

    -- Basic Information
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID NOT NULL REFERENCES product_categories(id),

    -- Technical Specifications
    specifications JSONB NOT NULL, -- Flexible spec structure
    dimensions JSONB, -- Length, width, height, thickness (cm)
    surface_finish surface_finish_enum DEFAULT 'raw', -- ham, honlu, cilalı, fırçalı, etc.

    -- Analysis Report Reference
    analysis_report_id UUID REFERENCES stone_analysis_reports(id),

    -- Pricing and Availability
    base_price DECIMAL(10,2),
    currency CHAR(3) DEFAULT 'USD',
    minimum_order_quantity INTEGER,
    production_time_days INTEGER,

    -- Location References
    quarry_location_id UUID REFERENCES producer_locations(id),
    factory_location_id UUID REFERENCES producer_locations(id),

    -- Status and Visibility
    status product_status_enum DEFAULT 'draft',
    is_active BOOLEAN DEFAULT TRUE,
    featured BOOLEAN DEFAULT FALSE,

    -- SEO and Search
    slug VARCHAR(255) UNIQUE,
    search_keywords TEXT[],

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_products_producer ON products(producer_id);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_status ON products(status, is_active);
CREATE INDEX idx_products_search ON products USING GIN(search_keywords);
CREATE INDEX idx_products_analysis_report ON products(analysis_report_id);
```

##### Product Price Lists Table (Dinamik Fiyat Listeleri - PRD requirement)
```sql
CREATE TABLE product_price_lists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,

    -- Price List Type
    list_type price_list_type_enum NOT NULL, -- 'dimensional', 'surface_finish', 'slab'

    -- Dimensional Products (Ebatlı Ürün)
    thickness_cm DECIMAL(5,2), -- Kalınlık
    width_cm DECIMAL(8,2), -- En
    length_cm DECIMAL(8,2), -- Boy
    surface_finish surface_finish_enum, -- Yüzey işlemi
    packaging packaging_enum DEFAULT 'pallet', -- Ambalaj (paletüstü sabit)
    delivery_type delivery_type_enum DEFAULT 'factory', -- Teslimat şekli (fabrika teslim sabit)

    -- Pricing
    price_per_m2 DECIMAL(10,2),
    currency CHAR(3) DEFAULT 'USD',

    -- Surface Finish Pricing (Yüzey İşlemi Fiyat Listesi)
    can_produce BOOLEAN DEFAULT TRUE, -- Yaparım/Yapamam

    -- Slab Products (Plaka)
    slab_specifications JSONB, -- Plaka özel özellikleri

    -- Status
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_price_lists_product ON product_price_lists(product_id);
CREATE INDEX idx_price_lists_type ON product_price_lists(list_type);
CREATE INDEX idx_price_lists_dimensions ON product_price_lists(thickness_cm, width_cm, length_cm);
```

##### Block Products Table (Blok Ürünleri - PRD requirement)
```sql
CREATE TABLE block_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,

    -- Block Dimensions (cm)
    width_cm DECIMAL(8,2) NOT NULL,
    length_cm DECIMAL(8,2) NOT NULL,
    height_cm DECIMAL(8,2) NOT NULL,

    -- Weight and Pricing
    weight_tons DECIMAL(8,3), -- Calculated or measured weight
    price_per_ton DECIMAL(10,2) NOT NULL,
    currency CHAR(3) DEFAULT 'USD',

    -- Availability
    stock_quantity INTEGER DEFAULT 0,
    is_available BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_block_products_product ON block_products(product_id);
CREATE INDEX idx_block_products_dimensions ON block_products(width_cm, length_cm, height_cm);
```

##### Bid Requests Table (Anonim Teklif Sistemi)
```sql
CREATE TABLE bid_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES users(id),

    -- Request Details
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    specifications JSONB NOT NULL,
    quantity INTEGER NOT NULL,
    unit unit_enum NOT NULL, -- 'm2', 'ton', 'piece'

    -- Delivery Information
    delivery_address JSONB NOT NULL,
    delivery_date DATE,
    delivery_terms delivery_terms_enum, -- 'FOB', 'CIF', 'DDP'

    -- Request Settings
    bid_deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    auto_close BOOLEAN DEFAULT TRUE,
    max_suppliers INTEGER DEFAULT 10,

    -- Status
    status bid_request_status_enum DEFAULT 'active',

    -- Privacy (Anonim sistem için)
    anonymous_id VARCHAR(50) UNIQUE NOT NULL, -- Public identifier

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_bid_requests_customer ON bid_requests(customer_id);
CREATE INDEX idx_bid_requests_status ON bid_requests(status);
CREATE INDEX idx_bid_requests_deadline ON bid_requests(bid_deadline);
CREATE INDEX idx_bid_requests_anonymous ON bid_requests(anonymous_id);
```

##### Bids Table
```sql
CREATE TABLE bids (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bid_request_id UUID NOT NULL REFERENCES bid_requests(id),
    producer_id UUID NOT NULL REFERENCES users(id),
    
    -- Bid Details
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    currency CHAR(3) DEFAULT 'USD',
    
    -- Terms
    delivery_time_days INTEGER NOT NULL,
    payment_terms TEXT,
    validity_days INTEGER DEFAULT 30,
    
    -- Additional Information
    notes TEXT,
    attachments JSONB, -- File references
    
    -- Status
    status bid_status_enum DEFAULT 'submitted',
    
    -- Privacy (Anonim sistem için)
    anonymous_id VARCHAR(50) UNIQUE NOT NULL,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(bid_request_id, producer_id)
);

CREATE INDEX idx_bids_request ON bids(bid_request_id);
CREATE INDEX idx_bids_producer ON bids(producer_id);
CREATE INDEX idx_bids_status ON bids(status);
CREATE INDEX idx_bids_price ON bids(total_price);
```

##### Orders Table
```sql
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number VARCHAR(50) UNIQUE NOT NULL,

    -- Parties
    customer_id UUID NOT NULL REFERENCES users(id),
    producer_id UUID NOT NULL REFERENCES users(id),
    bid_id UUID REFERENCES bids(id), -- Null for direct orders

    -- Order Details
    items JSONB NOT NULL, -- Order line items
    subtotal DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_cost DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL,
    currency CHAR(3) DEFAULT 'USD',

    -- Platform Commission (PRD requirement)
    total_m2 DECIMAL(10,2), -- Total m2 for commission calculation
    total_tons DECIMAL(10,3), -- Total tons for block commission calculation
    platform_commission DECIMAL(10,2), -- $1 per m2, $10 per ton

    -- Delivery
    delivery_address JSONB NOT NULL,
    delivery_terms delivery_terms_enum,
    estimated_delivery_date DATE,

    -- Status
    status order_status_enum DEFAULT 'pending',

    -- Timestamps
    confirmed_at TIMESTAMP WITH TIME ZONE,
    shipped_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_orders_customer ON orders(customer_id);
CREATE INDEX idx_orders_producer ON orders(producer_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_number ON orders(order_number);
```

##### Payments Table (Escrow System)
```sql
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id),

    -- Payment Details
    amount DECIMAL(12,2) NOT NULL,
    currency CHAR(3) DEFAULT 'USD',
    payment_method payment_method_enum NOT NULL,

    -- Bank Transfer Support (PRD requirement)
    bank_transfer_receipt_url TEXT, -- Dekont upload
    bank_transfer_verified BOOLEAN DEFAULT FALSE,
    verified_by UUID REFERENCES users(id), -- Admin who verified
    verified_at TIMESTAMP WITH TIME ZONE,

    -- Escrow Information
    escrow_status escrow_status_enum DEFAULT 'pending',
    escrow_account_id VARCHAR(255), -- External escrow reference

    -- Payment Gateway
    gateway_provider VARCHAR(50), -- 'stripe', 'paypal', etc.
    gateway_transaction_id VARCHAR(255),
    gateway_response JSONB,

    -- Status and Timing
    status payment_status_enum DEFAULT 'pending',
    paid_at TIMESTAMP WITH TIME ZONE,
    released_at TIMESTAMP WITH TIME ZONE,
    refunded_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_payments_order ON payments(order_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_escrow ON payments(escrow_status);
CREATE INDEX idx_payments_bank_transfer ON payments(bank_transfer_verified);
```

##### Product Categories Table (Ürün Kategorileri - PRD requirement)
```sql
CREATE TABLE product_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    parent_id UUID REFERENCES product_categories(id), -- For subcategories

    -- Category Details
    description TEXT,
    image_url TEXT,
    sort_order INTEGER DEFAULT 0,

    -- Status
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_product_categories_parent ON product_categories(parent_id);
CREATE INDEX idx_product_categories_slug ON product_categories(slug);

-- Insert main categories from PRD
INSERT INTO product_categories (name, slug, description) VALUES
('Mermer', 'mermer', 'Beyaz, renkli, desenli mermer çeşitleri'),
('Traverten', 'traverten', 'Klasik, noce, silver traverten'),
('Granit', 'granit', 'İç mekan, dış mekan granit'),
('Oniks', 'oniks', 'Dekoratif, aydınlatma oniks'),
('Kireçtaşı', 'kirec-tasi', 'Yapı, dekorasyon kireçtaşı'),
('Bazalt', 'bazalt', 'Döşeme, cephe bazalt'),
('Andezit', 'andezit', 'Yapı, yol andezit');
```

#### 2.2 Enum Types
```sql
-- User related enums
CREATE TYPE user_type_enum AS ENUM ('producer', 'customer', 'admin');
CREATE TYPE user_status_enum AS ENUM ('pending', 'active', 'suspended', 'banned');
CREATE TYPE verification_status_enum AS ENUM ('pending', 'verified', 'rejected');
CREATE TYPE company_type_enum AS ENUM ('producer', 'importer', 'distributor', 'warehouse', 'applicator', 'architect', 'construction');

-- Location related enums
CREATE TYPE location_type_enum AS ENUM ('quarry', 'factory');

-- Product related enums
CREATE TYPE product_status_enum AS ENUM ('draft', 'pending', 'approved', 'rejected');
CREATE TYPE unit_enum AS ENUM ('m2', 'ton', 'piece', 'm3');
CREATE TYPE surface_finish_enum AS ENUM ('raw', 'honed', 'polished', 'brushed', 'sandblasted', 'flamed', 'antique', 'filled');

-- Price list related enums
CREATE TYPE price_list_type_enum AS ENUM ('dimensional', 'surface_finish', 'slab');
CREATE TYPE packaging_enum AS ENUM ('pallet', 'crate', 'bulk');
CREATE TYPE delivery_type_enum AS ENUM ('factory', 'port', 'destination');

-- Order related enums
CREATE TYPE delivery_terms_enum AS ENUM ('FOB', 'CIF', 'DDP', 'EXW');
CREATE TYPE order_status_enum AS ENUM ('pending', 'confirmed', 'production', 'shipped', 'delivered', 'cancelled');

-- Bid related enums
CREATE TYPE bid_request_status_enum AS ENUM ('active', 'closed', 'cancelled');
CREATE TYPE bid_status_enum AS ENUM ('submitted', 'selected', 'rejected', 'expired');

-- Payment related enums
CREATE TYPE payment_method_enum AS ENUM ('credit_card', 'bank_transfer', 'paypal', 'letter_of_credit');
CREATE TYPE payment_status_enum AS ENUM ('pending', 'completed', 'failed', 'refunded');
CREATE TYPE escrow_status_enum AS ENUM ('pending', 'held', 'released', 'refunded');
```

##### AI News and Analytics Tables (PRD AI Features)
```sql
CREATE TABLE ai_news_articles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Article Information
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    source_url TEXT,
    source_name VARCHAR(255),

    -- Classification
    category ai_news_category_enum NOT NULL,
    language CHAR(2) DEFAULT 'tr',
    sentiment sentiment_enum, -- 'positive', 'negative', 'neutral'

    -- AI Processing
    keywords TEXT[],
    entities JSONB, -- Extracted entities
    relevance_score DECIMAL(3,2), -- 0.00 to 1.00

    -- Publication
    published_at TIMESTAMP WITH TIME ZONE,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_ai_news_category ON ai_news_articles(category);
CREATE INDEX idx_ai_news_published ON ai_news_articles(published_at);
CREATE INDEX idx_ai_news_relevance ON ai_news_articles(relevance_score);
CREATE INDEX idx_ai_news_keywords ON ai_news_articles USING GIN(keywords);
```

##### Email Marketing Tables (PRD Email Marketing)
```sql
CREATE TABLE email_campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Campaign Details
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    template_id UUID,

    -- Targeting
    target_countries TEXT[], -- Country-based segmentation
    target_user_types user_type_enum[],
    target_segments JSONB, -- Custom segmentation rules

    -- AI Settings
    ai_optimized BOOLEAN DEFAULT FALSE,
    optimal_send_time TIMESTAMP WITH TIME ZONE,
    personalization_enabled BOOLEAN DEFAULT FALSE,

    -- Status
    status email_campaign_status_enum DEFAULT 'draft',
    scheduled_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,

    -- Metrics
    total_recipients INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_email_campaigns_status ON email_campaigns(status);
CREATE INDEX idx_email_campaigns_scheduled ON email_campaigns(scheduled_at);
```

### 3. Redis Veri Yapıları

#### 3.1 Session Management
```redis
# User sessions
SET session:{session_id} "{user_id, expires_at, ...}" EX 86400

# User active sessions
SADD user_sessions:{user_id} {session_id}
```

#### 3.2 Rate Limiting
```redis
# API rate limiting
INCR rate_limit:{user_id}:{endpoint} EX 3600
```

#### 3.3 Real-time Data
```redis
# Active bid requests
ZADD active_bids {timestamp} {bid_request_id}

# Notification queues
LPUSH notifications:{user_id} "{notification_data}"

# AI News Cache
SET ai_news:latest "{news_data}" EX 3600
ZADD ai_news:trending {score} {article_id}

# Email Campaign Analytics
HINCRBY email_stats:{campaign_id} opened 1
HINCRBY email_stats:{campaign_id} clicked 1
```

##### Product Media Table (Görsel Materyaller - PRD requirement)
```sql
CREATE TABLE product_media (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,

    -- Media Information
    media_type media_type_enum NOT NULL, -- 'image', 'video', '360_view', '3d_model'
    file_url TEXT NOT NULL,
    file_size INTEGER, -- File size in bytes
    mime_type VARCHAR(100),

    -- Image/Video Properties
    width INTEGER,
    height INTEGER,
    duration INTEGER, -- For videos, in seconds

    -- Display Properties
    title VARCHAR(255),
    alt_text VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE, -- Main product image

    -- Status
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_product_media_product ON product_media(product_id);
CREATE INDEX idx_product_media_type ON product_media(media_type);
CREATE INDEX idx_product_media_primary ON product_media(is_primary);
```

##### Notifications Table (Bildirim Sistemi - PRD requirement)
```sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- Notification Details
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    notification_type notification_type_enum NOT NULL,

    -- Related Entity
    related_entity_type VARCHAR(50), -- 'bid_request', 'order', 'payment', etc.
    related_entity_id UUID,

    -- Delivery Channels
    email_sent BOOLEAN DEFAULT FALSE,
    push_sent BOOLEAN DEFAULT FALSE,
    whatsapp_sent BOOLEAN DEFAULT FALSE,
    telegram_sent BOOLEAN DEFAULT FALSE,

    -- Status
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(notification_type);
CREATE INDEX idx_notifications_unread ON notifications(user_id, is_read);
```

##### Order Tracking Table (Sipariş Takibi - PRD requirement)
```sql
CREATE TABLE order_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,

    -- Tracking Information
    status order_tracking_status_enum NOT NULL,
    description TEXT,
    location VARCHAR(255),

    -- Timestamps
    estimated_date TIMESTAMP WITH TIME ZONE,
    actual_date TIMESTAMP WITH TIME ZONE,

    -- Additional Information
    notes TEXT,
    attachments JSONB, -- Photos, documents

    -- Logistics
    carrier_name VARCHAR(255),
    tracking_number VARCHAR(255),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

CREATE INDEX idx_order_tracking_order ON order_tracking(order_id);
CREATE INDEX idx_order_tracking_status ON order_tracking(status);
```

##### Quality Reviews Table (Kalite Değerlendirmeleri - PRD requirement)
```sql
CREATE TABLE quality_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id),
    customer_id UUID NOT NULL REFERENCES users(id),
    producer_id UUID NOT NULL REFERENCES users(id),

    -- Review Details
    overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 5),
    quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
    delivery_rating INTEGER CHECK (delivery_rating >= 1 AND delivery_rating <= 5),
    service_rating INTEGER CHECK (service_rating >= 1 AND service_rating <= 5),

    -- Review Content
    title VARCHAR(255),
    comment TEXT,
    pros TEXT,
    cons TEXT,

    -- Media
    review_images JSONB, -- Array of image URLs

    -- Status
    is_verified BOOLEAN DEFAULT FALSE,
    is_published BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_quality_reviews_order ON quality_reviews(order_id);
CREATE INDEX idx_quality_reviews_producer ON quality_reviews(producer_id);
CREATE INDEX idx_quality_reviews_rating ON quality_reviews(overall_rating);
```

##### Additional Enum Types (PRD Requirements)
```sql
-- Media related enums
CREATE TYPE media_type_enum AS ENUM ('image', 'video', '360_view', '3d_model');

-- Notification related enums
CREATE TYPE notification_type_enum AS ENUM ('bid_received', 'bid_selected', 'payment_received', 'order_confirmed', 'order_shipped', 'order_delivered', 'system_announcement');

-- Order tracking related enums
CREATE TYPE order_tracking_status_enum AS ENUM ('order_confirmed', 'production_started', 'quality_control', 'packaging', 'ready_for_shipment', 'shipped', 'in_transit', 'customs_clearance', 'delivered');

-- AI and News related enums
CREATE TYPE ai_news_category_enum AS ENUM ('market_prices', 'technology', 'regulations', 'events', 'competition');
CREATE TYPE sentiment_enum AS ENUM ('positive', 'negative', 'neutral');
CREATE TYPE email_campaign_status_enum AS ENUM ('draft', 'scheduled', 'sending', 'sent', 'cancelled');
```

### 4. Elasticsearch Mapping

#### 4.1 Product Search Index
```json
{
  "mappings": {
    "properties": {
      "id": {"type": "keyword"},
      "name": {
        "type": "text",
        "analyzer": "standard",
        "fields": {
          "keyword": {"type": "keyword"}
        }
      },
      "description": {"type": "text"},
      "category": {"type": "keyword"},
      "specifications": {"type": "object"},
      "price": {"type": "double"},
      "location": {"type": "geo_point"},
      "surface_finish": {"type": "keyword"},
      "dimensions": {
        "properties": {
          "width": {"type": "double"},
          "length": {"type": "double"},
          "thickness": {"type": "double"}
        }
      },
      "analysis_report": {
        "properties": {
          "density": {"type": "double"},
          "hardness": {"type": "double"},
          "water_absorption": {"type": "double"}
        }
      },
      "producer_info": {
        "properties": {
          "company_name": {"type": "text"},
          "country": {"type": "keyword"},
          "verified": {"type": "boolean"}
        }
      },
      "created_at": {"type": "date"}
    }
  }
}
```

#### 4.2 AI News Search Index
```json
{
  "mappings": {
    "properties": {
      "id": {"type": "keyword"},
      "title": {
        "type": "text",
        "analyzer": "turkish"
      },
      "content": {
        "type": "text",
        "analyzer": "turkish"
      },
      "category": {"type": "keyword"},
      "keywords": {"type": "keyword"},
      "sentiment": {"type": "keyword"},
      "relevance_score": {"type": "double"},
      "published_at": {"type": "date"},
      "source_name": {"type": "keyword"}
    }
  }
}
```

### 5. Veri Güvenliği ve Privacy

#### 5.1 Encryption at Rest
```sql
-- Sensitive data encryption
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Encrypted columns
ALTER TABLE user_profiles 
ADD COLUMN encrypted_tax_number BYTEA;
```

#### 5.2 Row Level Security
```sql
-- Enable RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Policy for users to see only their own data
CREATE POLICY user_profile_policy ON user_profiles
    FOR ALL TO authenticated_user
    USING (user_id = current_user_id());
```

## Implementasyon

### Faz 1: Core Schema (Hafta 1-2)
1. PostgreSQL kurulumu ve konfigürasyonu
2. Temel tabloların oluşturulması (users, user_profiles, products)
3. Enum types tanımlaması
4. Temel indexlerin oluşturulması
5. Product categories tablosu ve veri girişi

### Faz 2: Producer-Specific Features (Hafta 3-4)
1. Producer locations tablosu (ocak/fabrika lokasyonları)
2. Stone analysis reports tablosu (analiz raporları)
3. Product price lists tablosu (dinamik fiyat listeleri)
4. Block products tablosu (blok ürünleri)
5. JSONB kolonları ve indexleri

### Faz 3: Bidding and Order System (Hafta 5-6)
1. Bid requests ve bids tabloları (anonim teklif sistemi)
2. Orders tablosu (komisyon hesaplama ile)
3. Payments tablosu (escrow ve banka havalesi desteği)
4. Full-text search setup
5. Geo-spatial data support (PostGIS)

### Faz 4: AI and Marketing Features (Hafta 7-8)
1. AI news articles tablosu
2. Email campaigns tablosu
3. Redis integration (session, cache, real-time)
4. Elasticsearch setup ve mappings
5. Data synchronization scripts

### Faz 5: Advanced Features (Hafta 9-10)
1. Row Level Security (RLS) implementation
2. Audit trail ve logging
3. Data encryption setup
4. Performance optimization
5. Backup ve disaster recovery

## Güvenlik Değerlendirmesi

### Database Security
- **Connection Security**: SSL/TLS encryption
- **Authentication**: Strong password policies
- **Authorization**: Role-based access control
- **Audit Logging**: All database operations logged

### Data Privacy
- **PII Encryption**: Personal data encrypted at rest
- **Anonymization**: Bid system privacy protection
- **GDPR Compliance**: Right to be forgotten implementation
- **Data Retention**: Automated cleanup policies

## Performans Etkisi

### Query Optimization
- **Indexing Strategy**: Composite indexes for common queries
- **Partitioning**: Large tables partitioned by date
- **Connection Pooling**: PgBouncer for connection management
- **Query Analysis**: Regular EXPLAIN ANALYZE reviews

### Caching Strategy
- **Redis Caching**: Frequently accessed data cached
- **Application-level Caching**: ORM query caching
- **CDN**: Static assets cached globally

## Alternatifler

### Database Alternatives
- **MongoDB**: Document-based approach
- **CockroachDB**: Distributed SQL database
- **Amazon Aurora**: Managed PostgreSQL

### Search Alternatives
- **Apache Solr**: Alternative search engine
- **Algolia**: Managed search service
- **PostgreSQL Full-text**: Built-in search capabilities

## PRD Gereksinimlerinin Karşılanması

### ✅ Üretici Kayıt Sistemi
- **Firma bilgileri**: user_profiles tablosunda company_name, tax_number, trade_registry_number
- **Ocak lokasyonları**: producer_locations tablosu (Google Maps link ile)
- **Fabrika lokasyonları**: producer_locations tablosu (birden fazla lokasyon desteği)
- **Üretim kapasitesi**: production_capacity_report JSONB kolonu
- **Analiz raporları**: stone_analysis_reports tablosu (yoğunluk, sertlik, su emme vb.)
- **Sertifikalar**: certificates JSONB kolonu
- **Banka bilgileri**: bank_information JSONB kolonu
- **Fason üretim**: offers_custom_manufacturing boolean kolonu

### ✅ Ürün Yönetimi
- **Kategoriler**: product_categories tablosu (Mermer, Traverten, Granit vb.)
- **Dinamik fiyat listeleri**: product_price_lists tablosu (Excel benzeri)
- **Blok ürünleri**: block_products tablosu (ton bazında satış)
- **Yüzey işlemleri**: surface_finish_enum (ham, honlu, cilalı vb.)
- **Boyutlar**: dimensions JSONB (cm cinsinden)

### ✅ Anonim Teklif Sistemi
- **Gizlilik**: anonymous_id kolonları ile kimlik koruması
- **Rekabetçi ortam**: Sadece toplam tutarlar görünür
- **Escrow ödemeler**: payments tablosu ile güvenli ödeme

### ✅ Komisyon Sistemi
- **m² bazında**: total_m2 kolonu ile $1 komisyon hesabı
- **Ton bazında**: total_tons kolonu ile $10 komisyon hesabı
- **Platform komisyonu**: platform_commission kolonu

### ✅ AI Özellikleri
- **Haber toplama**: ai_news_articles tablosu
- **Email marketing**: email_campaigns tablosu (ülke bazlı segmentasyon)
- **Elasticsearch**: Gelişmiş arama ve analitik

### ✅ Takip ve İzleme Sistemi
- **Sipariş takibi**: order_tracking tablosu (üretim aşamalarından teslimat)
- **Bildirim sistemi**: notifications tablosu (email, push, WhatsApp, Telegram)
- **Lojistik takibi**: tracking_number ve carrier_name kolonları

### ✅ Görsel Materyaller
- **Ürün medyası**: product_media tablosu (fotoğraf, video, 360°, 3D)
- **Yüksek çözünürlük**: file_size ve dimensions desteği
- **Sıralama**: sort_order ile görsel düzenleme

### ✅ Kalite Kontrol
- **Müşteri değerlendirmeleri**: quality_reviews tablosu
- **Çoklu rating**: kalite, teslimat, hizmet puanları
- **Doğrulama**: is_verified ile admin onayı

## Veri Migrasyonu ve Seed Data

### Initial Data Setup
```sql
-- Default admin user
INSERT INTO users (email, password_hash, user_type, status, email_verified)
VALUES ('<EMAIL>', '$2b$12$...', 'admin', 'active', TRUE);

-- Sample surface finishes
INSERT INTO surface_finish_enum VALUES
('raw'), ('honed'), ('polished'), ('brushed'), ('sandblasted'), ('flamed'), ('antique'), ('filled');

-- Sample notification types
INSERT INTO notification_type_enum VALUES
('bid_received'), ('bid_selected'), ('payment_received'), ('order_confirmed'),
('order_shipped'), ('order_delivered'), ('system_announcement');
```

### Data Migration Scripts
```sql
-- Migration for existing data (if any)
CREATE OR REPLACE FUNCTION migrate_legacy_data()
RETURNS VOID AS $$
BEGIN
    -- Migration logic here
    RAISE NOTICE 'Data migration completed';
END;
$$ LANGUAGE plpgsql;
```

## Backup ve Disaster Recovery

### Backup Strategy
```sql
-- Daily full backup
pg_dump -h localhost -U postgres -d naturalstone_marketplace > backup_$(date +%Y%m%d).sql

-- Point-in-time recovery setup
archive_mode = on
archive_command = 'cp %p /backup/archive/%f'
wal_level = replica
```

### Monitoring Queries
```sql
-- Database size monitoring
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Active connections
SELECT count(*) as active_connections
FROM pg_stat_activity
WHERE state = 'active';

-- Slow queries
SELECT query, mean_time, calls
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
```

## Gelecek Çalışmalar

1. **Sharding Strategy**: Horizontal scaling plan
2. **Read Replicas**: Read performance optimization
3. **Data Warehouse**: Analytics database separation
4. **Time-series Data**: IoT ve tracking data optimization
5. **3D Model Storage**: 3D ürün modellerinin depolanması
6. **Blockchain Integration**: Şeffaflık için blockchain entegrasyonu
7. **Multi-language Support**: Çoklu dil desteği için i18n tabloları
8. **GraphQL Schema**: API için GraphQL şema entegrasyonu
9. **Event Sourcing**: Kritik işlemler için event sourcing
10. **CQRS Pattern**: Command Query Responsibility Segregation

## Özet ve Sonuç

Bu RFC, Türkiye Doğal Taş Marketplace platformu için kapsamlı bir veritabanı tasarımı sunmaktadır. Tasarım şu ana prensiplere dayanmaktadır:

### 🎯 Temel Prensipler
1. **Gizlilik Odaklı**: Anonim teklif sistemi için özel veri yapıları
2. **Ölçeklenebilir**: Multi-database stratejisi ile yüksek performans
3. **Güvenli**: Row-level security ve encryption desteği
4. **Esnek**: JSONB kolonları ile dinamik veri yapıları
5. **Analitik**: Elasticsearch entegrasyonu ile gelişmiş arama

### 📊 Veritabanı İstatistikleri
- **Ana Tablolar**: 15+ core tablo
- **Enum Types**: 20+ özel veri tipi
- **Indexler**: 50+ performans indexi
- **Desteklenen Diller**: Türkçe, İngilizce + 7 ek dil
- **Veri Tipleri**: PostgreSQL, Redis, Elasticsearch

### 🚀 Performans Hedefleri
- **Query Response**: < 100ms (basit sorgular)
- **Search Response**: < 200ms (Elasticsearch)
- **Concurrent Users**: 10,000+ eşzamanlı kullanıcı
- **Data Growth**: 1TB+ veri kapasitesi

### ✅ PRD Uyumluluğu
Bu tasarım PRD.md dosyasındaki tüm gereksinimleri karşılamaktadır:
- ✅ Anonim teklif sistemi
- ✅ Dinamik fiyat listeleri
- ✅ Ocak/fabrika lokasyonları
- ✅ Analiz raporları
- ✅ Komisyon sistemi
- ✅ AI özellikleri
- ✅ Email marketing
- ✅ Sipariş takibi

---

**Doküman Bilgileri**:
- **RFC Numarası**: RFC-003
- **Versiyon**: 2.0
- **Son Güncelleme**: 2025-06-28
- **Durum**: DRAFT → REVIEW
- **Onay Bekleyen**: Tech Lead, Database Admin

**Bağlantılı RFC'ler**:
- RFC-001: Veritabanının sistem mimarisindeki yeri
- RFC-002: Kullanılan veritabanı teknolojileri
- RFC-101: Kullanıcı yönetimi veri yapıları
- RFC-201: Ürün katalog veri yapıları
- RFC-301: Teklif sistemi veri yapıları (anonim sistem)
- RFC-401: Ödeme sistemi veri yapıları (escrow)
- RFC-501: Admin dashboard veri gereksinimleri
- RFC-601: AI chatbot veri yapıları
- RFC-701: 3D ürün görüntüleme veri gereksinimleri

**İletişim**:
- **Yazar**: Augment Agent
- **Email**: <EMAIL>
- **Slack**: #database-design
