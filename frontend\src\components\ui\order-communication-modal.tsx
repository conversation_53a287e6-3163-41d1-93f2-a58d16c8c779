"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "./button"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, Card<PERSON>itle } from "./card"
import { Badge } from "./badge"
import { 
  X, 
  MessageSquare,
  Send,
  Phone,
  Mail,
  User,
  Calendar,
  Building2,
  Clock,
  CheckCircle,
  AlertTriangle
} from "lucide-react"
import { Order } from "@/types/orders"

interface OrderCommunicationModalProps {
  isOpen: boolean
  onClose: () => void
  order: Order | null
  onSendMessage: (recipient: 'customer' | 'producer', subject: string, message: string) => void
}

export function OrderCommunicationModal({ 
  isOpen, 
  onClose, 
  order,
  onSendMessage
}: OrderCommunicationModalProps) {
  const [selectedRecipient, setSelectedRecipient] = React.useState<'customer' | 'producer'>('customer')
  const [subject, setSubject] = React.useState('')
  const [message, setMessage] = React.useState('')
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  React.useEffect(() => {
    if (order) {
      setSubject(`Sipariş ${order.orderNumber} Hakkında`)
      setMessage('')
    }
  }, [order])

  if (!isOpen || !order) return null

  const handleSubmit = async () => {
    if (!subject.trim() || !message.trim()) {
      alert('Konu ve mesaj alanları zorunludur.')
      return
    }

    setIsSubmitting(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulated API call
      onSendMessage(selectedRecipient, subject, message)
      onClose()
    } catch (error) {
      console.error('Message send failed:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'call-customer':
        window.open(`tel:${order.customer.phone}`)
        break
      case 'email-customer':
        window.open(`mailto:${order.customer.email}?subject=${encodeURIComponent(subject)}`)
        break
      case 'call-producer':
        window.open(`tel:${order.producer.phone}`)
        break
      case 'email-producer':
        window.open(`mailto:${order.producer.email}?subject=${encodeURIComponent(subject)}`)
        break
    }
  }

  const messageTemplates = [
    {
      title: 'Durum Güncelleme',
      content: `Merhaba,\n\n${order.orderNumber} numaralı siparişinizin durumu güncellenmiştir.\n\nDetaylar için lütfen sisteme giriş yapınız.\n\nTeşekkürler.`
    },
    {
      title: 'Ödeme Hatırlatması',
      content: `Merhaba,\n\n${order.orderNumber} numaralı siparişiniz için bekleyen ödemenizi hatırlatmak isteriz.\n\nÖdeme tutarı: ${order.payment.totalDue} ${order.pricing.currency}\n\nLütfen en kısa sürede ödemenizi gerçekleştiriniz.\n\nTeşekkürler.`
    },
    {
      title: 'Teslimat Bilgisi',
      content: `Merhaba,\n\n${order.orderNumber} numaralı siparişiniz teslimat için hazırlanmıştır.\n\nTahmini teslimat tarihi: ${order.estimatedCompletionDate ? new Date(order.estimatedCompletionDate).toLocaleDateString('tr-TR') : 'Belirtilmemiş'}\n\nHerhangi bir sorunuz varsa lütfen bizimle iletişime geçin.\n\nTeşekkürler.`
    },
    {
      title: 'Genel Bilgilendirme',
      content: `Merhaba,\n\n${order.orderNumber} numaralı siparişiniz hakkında bilgilendirme yapmak isteriz.\n\n[Buraya özel mesajınızı yazın]\n\nTeşekkürler.`
    }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MessageSquare className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">İletişim</h2>
              <p className="text-gray-600">
                {order.orderNumber} - {order.customer.companyName}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Hızlı İletişim</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Customer Quick Actions */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                    <User className="w-4 h-4" />
                    Müşteri: {order.customer.companyName}
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{order.customer.contactPerson}</p>
                        <p className="text-sm text-gray-600">{order.customer.email}</p>
                        <p className="text-sm text-gray-600">{order.customer.phone}</p>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickAction('call-customer')}
                        >
                          <Phone className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickAction('email-customer')}
                        >
                          <Mail className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Producer Quick Actions */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                    <Building2 className="w-4 h-4" />
                    Üretici: {order.producer.companyName}
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{order.producer.contactPerson}</p>
                        <p className="text-sm text-gray-600">{order.producer.email}</p>
                        <p className="text-sm text-gray-600">{order.producer.phone}</p>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickAction('call-producer')}
                        >
                          <Phone className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickAction('email-producer')}
                        >
                          <Mail className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Message Form */}
            <Card>
              <CardHeader>
                <CardTitle>Mesaj Gönder</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Recipient Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Alıcı
                  </label>
                  <div className="flex gap-2">
                    <Button
                      variant={selectedRecipient === 'customer' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedRecipient('customer')}
                    >
                      <User className="w-4 h-4 mr-2" />
                      Müşteri
                    </Button>
                    <Button
                      variant={selectedRecipient === 'producer' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedRecipient('producer')}
                    >
                      <Building2 className="w-4 h-4 mr-2" />
                      Üretici
                    </Button>
                  </div>
                </div>

                {/* Subject */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Konu
                  </label>
                  <input
                    type="text"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Mesaj konusu"
                  />
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mesaj
                  </label>
                  <textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Mesajınızı buraya yazın..."
                  />
                </div>

                {/* Message Templates */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hızlı Şablonlar
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    {messageTemplates.map((template, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => setMessage(template.content)}
                        className="text-left justify-start"
                      >
                        {template.title}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-6 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              {selectedRecipient === 'customer' ? (
                <span>Mesaj müşteriye gönderilecek: {order.customer.companyName}</span>
              ) : (
                <span>Mesaj üreticiye gönderilecek: {order.producer.companyName}</span>
              )}
            </div>
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
                İptal
              </Button>
              <Button onClick={handleSubmit} disabled={isSubmitting || !subject.trim() || !message.trim()}>
                {isSubmitting ? (
                  <>
                    <Clock className="w-4 h-4 mr-2 animate-spin" />
                    Gönderiliyor...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Mesaj Gönder
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
