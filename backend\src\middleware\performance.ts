import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { logInfo, logWarn } from '../utils/logger';

const prisma = new PrismaClient();

// Performance monitoring middleware
export const performanceMonitor = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();

  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any): any {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    const endMemory = process.memoryUsage();
    
    // Log performance metrics
    const performanceData = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime,
      memoryUsage: {
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        external: endMemory.external - startMemory.external
      },
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      contentLength: res.get('Content-Length')
    };

    // Log slow requests
    if (responseTime > 1000) {
      logWarn('Slow request detected', performanceData);
    }

    // Store performance data in database (async, don't block response)
    if (process.env.NODE_ENV === 'production') {
      setImmediate(async () => {
        try {
          await prisma.performanceLog.create({
            data: {
              endpoint: req.url,
              method: req.method,
              responseTime,
              statusCode: res.statusCode,
              userId: (req as any).user?.id || null,
              ipAddress: req.ip
            }
          });
        } catch (error) {
          // Don't log database errors to avoid infinite loops
          console.error('Failed to store performance log:', error);
        }
      });
    }

    // Call original end method
    return originalEnd.call(this, chunk, encoding);
  };

  next();
};

// Memory usage monitoring
export const memoryMonitor = () => {
  const memoryUsage = process.memoryUsage();
  const memoryInMB = {
    rss: Math.round(memoryUsage.rss / 1024 / 1024),
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    external: Math.round(memoryUsage.external / 1024 / 1024)
  };

  // Warn if memory usage is high
  if (memoryInMB.heapUsed > 512) { // 512MB threshold
    logWarn('High memory usage detected', memoryInMB);
  }

  return memoryInMB;
};

// Database connection monitoring
export const dbConnectionMonitor = async () => {
  try {
    const startTime = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - startTime;

    if (responseTime > 100) { // 100ms threshold
      logWarn('Slow database query detected', { responseTime });
    }

    return { status: 'connected', responseTime };
  } catch (error) {
    logWarn('Database connection error', { error: (error as Error).message });
    return { status: 'disconnected', error: (error as Error).message };
  }
};

// CPU usage monitoring (approximate)
export const cpuMonitor = () => {
  const startUsage = process.cpuUsage();
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const endUsage = process.cpuUsage(startUsage);
      const cpuPercent = {
        user: Math.round((endUsage.user / 1000000) * 100), // Convert to percentage
        system: Math.round((endUsage.system / 1000000) * 100)
      };
      
      resolve(cpuPercent);
    }, 100); // Sample for 100ms
  });
};

// Request size monitoring
export const requestSizeMonitor = (req: Request, res: Response, next: NextFunction) => {
  const contentLength = parseInt(req.get('content-length') || '0');
  
  if (contentLength > 5 * 1024 * 1024) { // 5MB threshold
    logWarn('Large request detected', {
      url: req.url,
      method: req.method,
      contentLength,
      ip: req.ip
    });
  }

  next();
};

// Response size monitoring
export const responseSizeMonitor = (req: Request, res: Response, next: NextFunction) => {
  const originalSend = res.send;
  
  res.send = function(body: any) {
    const responseSize = Buffer.byteLength(JSON.stringify(body), 'utf8');
    
    if (responseSize > 1024 * 1024) { // 1MB threshold
      logWarn('Large response detected', {
        url: req.url,
        method: req.method,
        responseSize,
        statusCode: res.statusCode
      });
    }

    return originalSend.call(this, body);
  };

  next();
};

// API endpoint performance metrics
export const apiMetrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  averageResponseTime: 0,
  responseTimes: [] as number[],

  recordRequest(responseTime: number, success: boolean) {
    this.totalRequests++;
    
    if (success) {
      this.successfulRequests++;
    } else {
      this.failedRequests++;
    }

    this.responseTimes.push(responseTime);
    
    // Keep only last 1000 response times for average calculation
    if (this.responseTimes.length > 1000) {
      this.responseTimes.shift();
    }

    this.averageResponseTime = this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length;
  },

  getMetrics() {
    return {
      totalRequests: this.totalRequests,
      successfulRequests: this.successfulRequests,
      failedRequests: this.failedRequests,
      successRate: this.totalRequests > 0 ? (this.successfulRequests / this.totalRequests) * 100 : 0,
      averageResponseTime: Math.round(this.averageResponseTime),
      p95ResponseTime: this.getPercentile(95),
      p99ResponseTime: this.getPercentile(99)
    };
  },

  getPercentile(percentile: number): number {
    if (this.responseTimes.length === 0) return 0;
    
    const sorted = [...this.responseTimes].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return Math.round(sorted[index] || 0);
  }
};

// Metrics collection middleware
export const metricsCollector = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();

  res.on('finish', () => {
    const responseTime = Date.now() - startTime;
    const success = res.statusCode < 400;
    
    apiMetrics.recordRequest(responseTime, success);
  });

  next();
};

// Health check with performance metrics
export const healthCheckWithMetrics = async () => {
  const memory = memoryMonitor();
  const cpu = await cpuMonitor();
  const db = await dbConnectionMonitor();
  const metrics = apiMetrics.getMetrics();

  return {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory,
    cpu,
    database: db,
    api: metrics,
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0'
  };
};

// Performance optimization suggestions
export const getOptimizationSuggestions = () => {
  const memory = memoryMonitor();
  const metrics = apiMetrics.getMetrics();
  const suggestions: string[] = [];

  if (memory.heapUsed > 512) {
    suggestions.push('Consider implementing memory optimization or increasing server memory');
  }

  if (metrics.averageResponseTime > 500) {
    suggestions.push('API response times are high, consider database query optimization');
  }

  if (metrics.successRate < 95) {
    suggestions.push('Error rate is high, investigate failed requests');
  }

  if (metrics.p95ResponseTime > 1000) {
    suggestions.push('95th percentile response time is high, optimize slow endpoints');
  }

  return suggestions;
};
