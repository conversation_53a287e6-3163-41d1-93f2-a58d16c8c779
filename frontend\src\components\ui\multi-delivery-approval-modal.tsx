'use client'

import * as React from 'react'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { 
  Package, 
  Calendar, 
  DollarSign,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Truck,
  MessageSquare,
  FileText
} from 'lucide-react'

interface MultiDeliveryApprovalModalProps {
  isOpen: boolean
  onClose: () => void
  quote: any
  onApprove: (approvalData: ApprovalData) => void
  onReject: (rejectionReason: string) => void
  onRequestChanges: (changeRequests: string) => void
}

interface ApprovalData {
  approvedPackages: string[]
  customerNotes: string
  agreesToTerms: boolean
}

export function MultiDeliveryA<PERSON>rovalModal({
  isOpen,
  onClose,
  quote,
  onApprove,
  onReject,
  onRequestChanges
}: MultiDeliveryApprovalModalProps) {
  const [selectedPackages, setSelectedPackages] = React.useState<string[]>([])
  const [customerNotes, setCustomerNotes] = React.useState('')
  const [agreesToTerms, setAgreesToTerms] = React.useState(false)
  const [rejectionReason, setRejectionReason] = React.useState('')
  const [changeRequests, setChangeRequests] = React.useState('')
  const [actionType, setActionType] = React.useState<'approve' | 'reject' | 'changes' | null>(null)
  const [isLoading, setIsLoading] = React.useState(false)

  React.useEffect(() => {
    if (isOpen && quote) {
      // Reset form
      setSelectedPackages(quote.deliveryPackages?.map((pkg: any) => pkg.packageNumber.toString()) || [])
      setCustomerNotes('')
      setAgreesToTerms(false)
      setRejectionReason('')
      setChangeRequests('')
      setActionType(null)
    }
  }, [isOpen, quote])

  const handlePackageToggle = (packageNumber: string) => {
    setSelectedPackages(prev => 
      prev.includes(packageNumber)
        ? prev.filter(p => p !== packageNumber)
        : [...prev, packageNumber]
    )
  }

  const handleApprove = async () => {
    if (selectedPackages.length === 0) {
      alert('En az bir paket seçmelisiniz!')
      return
    }

    if (!agreesToTerms) {
      alert('Şartları kabul etmelisiniz!')
      return
    }

    setIsLoading(true)
    try {
      await onApprove({
        approvedPackages: selectedPackages,
        customerNotes,
        agreesToTerms
      })
      onClose()
    } catch (error) {
      console.error('Error approving quote:', error)
      alert('Onay işlemi sırasında hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      alert('Lütfen red sebebini belirtin!')
      return
    }

    setIsLoading(true)
    try {
      await onReject(rejectionReason)
      onClose()
    } catch (error) {
      console.error('Error rejecting quote:', error)
      alert('Red işlemi sırasında hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRequestChanges = async () => {
    if (!changeRequests.trim()) {
      alert('Lütfen değişiklik taleplerini belirtin!')
      return
    }

    setIsLoading(true)
    try {
      await onRequestChanges(changeRequests)
      onClose()
    } catch (error) {
      console.error('Error requesting changes:', error)
      alert('Değişiklik talebi sırasında hata oluştu.')
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR')
  }

  const formatCurrency = (amount: number, currency: string) => {
    const symbol = currency === 'USD' ? '$' : currency === 'EUR' ? '€' : '₺'
    return `${symbol}${amount.toLocaleString()}`
  }

  const getTotalAmount = () => {
    if (quote?.quoteType === 'single') {
      return quote.totalAmount
    }
    return quote?.deliveryPackages?.reduce((sum: number, pkg: any) => sum + pkg.amount, 0) || 0
  }

  const getSelectedAmount = () => {
    if (quote?.quoteType === 'single') {
      return quote.totalAmount
    }
    return quote?.deliveryPackages
      ?.filter((pkg: any) => selectedPackages.includes(pkg.packageNumber.toString()))
      ?.reduce((sum: number, pkg: any) => sum + pkg.amount, 0) || 0
  }

  if (!quote) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            {quote.quoteType === 'multi' ? 'Çoklu Teslimat Teklifi Onayı' : 'Teklif Onayı'}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Quote Summary */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-blue-800">Teklif Özeti</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-blue-700">Üretici:</span>
                <p className="text-blue-900">{quote.producerName}</p>
              </div>
              <div>
                <span className="font-medium text-blue-700">Ürün:</span>
                <p className="text-blue-900">{quote.productName}</p>
              </div>
              <div>
                <span className="font-medium text-blue-700">Toplam Miktar:</span>
                <p className="text-blue-900">{quote.quantity} m²</p>
              </div>
              <div>
                <span className="font-medium text-blue-700">Toplam Tutar:</span>
                <p className="text-blue-900">{formatCurrency(getTotalAmount(), quote.currency)}</p>
              </div>
            </CardContent>
          </Card>

          {/* Action Selection */}
          {!actionType && (
            <Card>
              <CardHeader>
                <CardTitle>Bu teklif için ne yapmak istiyorsunuz?</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  onClick={() => setActionType('approve')}
                  className="h-20 bg-green-600 hover:bg-green-700 flex flex-col items-center justify-center"
                >
                  <CheckCircle className="w-6 h-6 mb-2" />
                  <span>Onayla</span>
                </Button>
                
                <Button
                  onClick={() => setActionType('changes')}
                  className="h-20 bg-orange-600 hover:bg-orange-700 flex flex-col items-center justify-center"
                >
                  <MessageSquare className="w-6 h-6 mb-2" />
                  <span>Değişiklik İste</span>
                </Button>
                
                <Button
                  onClick={() => setActionType('reject')}
                  className="h-20 bg-red-600 hover:bg-red-700 flex flex-col items-center justify-center"
                >
                  <XCircle className="w-6 h-6 mb-2" />
                  <span>Reddet</span>
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Approval Section */}
          {actionType === 'approve' && (
            <>
              {/* Package Selection for Multi-Delivery */}
              {quote.quoteType === 'multi' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Package className="w-5 h-5" />
                      Teslimat Paketleri ({quote.deliveryPackages?.length || 0})
                    </CardTitle>
                    <p className="text-sm text-gray-600">
                      Onaylamak istediğiniz paketleri seçin. Seçilen paketler için sipariş oluşturulacaktır.
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {quote.deliveryPackages?.map((pkg: any, index: number) => (
                      <div
                        key={index}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          selectedPackages.includes(pkg.packageNumber.toString())
                            ? 'border-green-500 bg-green-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => handlePackageToggle(pkg.packageNumber.toString())}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={selectedPackages.includes(pkg.packageNumber.toString())}
                              onChange={() => handlePackageToggle(pkg.packageNumber.toString())}
                              className="text-green-600"
                            />
                            <span className="font-medium">Paket #{pkg.packageNumber}</span>
                          </div>
                          <Badge className="bg-blue-100 text-blue-800">
                            {formatCurrency(pkg.amount, quote.currency)}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">Miktar:</span>
                            <p>{pkg.quantity} m²</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Üretim Süresi:</span>
                            <p>{pkg.productionDuration} gün</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Teslimat Tarihi:</span>
                            <p>{formatDate(pkg.deliveryDate)}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Teslimat:</span>
                            <p>
                              {pkg.deliveryMethod === 'factory_pickup' && 'Fabrika Teslim'}
                              {pkg.deliveryMethod === 'delivery' && 'Teslimat'}
                              {pkg.deliveryMethod === 'partial_delivery' && 'Kısmi Teslimat'}
                            </p>
                          </div>
                        </div>
                        
                        <div className="mt-3 text-sm">
                          <span className="font-medium text-gray-700">Ödeme Koşulları:</span>
                          <p className="text-gray-600">
                            %{pkg.paymentTerms.advancePercentage} Avans, 
                            %{pkg.paymentTerms.deliveryPercentage} Teslimat
                            {pkg.paymentTerms.completionPercentage > 0 && `, %${pkg.paymentTerms.completionPercentage} Tamamlama`}
                          </p>
                        </div>
                        
                        {pkg.notes && (
                          <div className="mt-3 text-sm">
                            <span className="font-medium text-gray-700">Notlar:</span>
                            <p className="text-gray-600">{pkg.notes}</p>
                          </div>
                        )}
                      </div>
                    ))}
                    
                    {/* Selected Summary */}
                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                      <h4 className="font-medium text-green-800 mb-2">Seçilen Paketler Özeti</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-green-700">Seçilen Paket Sayısı:</span>
                          <p className="text-green-900">{selectedPackages.length} / {quote.deliveryPackages?.length || 0}</p>
                        </div>
                        <div>
                          <span className="font-medium text-green-700">Toplam Tutar:</span>
                          <p className="text-green-900">{formatCurrency(getSelectedAmount(), quote.currency)}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* General Terms */}
              {quote.generalTerms && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      Genel Şartlar
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 whitespace-pre-wrap">{quote.generalTerms}</p>
                  </CardContent>
                </Card>
              )}

              {/* Customer Notes */}
              <Card>
                <CardHeader>
                  <CardTitle>Müşteri Notları</CardTitle>
                </CardHeader>
                <CardContent>
                  <Label htmlFor="customerNotes">Ek notlarınız (isteğe bağlı)</Label>
                  <Textarea
                    id="customerNotes"
                    value={customerNotes}
                    onChange={(e) => setCustomerNotes(e.target.value)}
                    rows={3}
                    placeholder="Sipariş ile ilgili özel istekleriniz, notlarınız..."
                    className="mt-1"
                  />
                </CardContent>
              </Card>

              {/* Terms Agreement */}
              <Card className="bg-yellow-50 border-yellow-200">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <label className="flex items-start gap-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={agreesToTerms}
                          onChange={(e) => setAgreesToTerms(e.target.checked)}
                          className="mt-1 rounded border-yellow-300 text-yellow-600 focus:ring-yellow-500"
                        />
                        <div className="text-sm">
                          <p className="font-medium text-yellow-800 mb-1">Şartları Kabul Ediyorum</p>
                          <p className="text-yellow-700">
                            Bu teklifi onaylayarak, belirtilen şartları, teslimat tarihlerini ve ödeme koşullarını 
                            kabul ettiğimi beyan ederim. Sipariş oluşturulduktan sonra değişiklik yapılması 
                            ek ücrete tabi olabilir.
                          </p>
                        </div>
                      </label>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}

          {/* Rejection Section */}
          {actionType === 'reject' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-800">
                  <XCircle className="w-5 h-5" />
                  Teklifi Reddet
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Label htmlFor="rejectionReason">Red Sebebi *</Label>
                <Textarea
                  id="rejectionReason"
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  rows={4}
                  placeholder="Teklifi neden reddediyorsunuz? (Fiyat, teslimat tarihi, şartlar vb.)"
                  className="mt-1"
                />
              </CardContent>
            </Card>
          )}

          {/* Change Requests Section */}
          {actionType === 'changes' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-orange-800">
                  <MessageSquare className="w-5 h-5" />
                  Değişiklik Talepleri
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Label htmlFor="changeRequests">Değişiklik Talepleri *</Label>
                <Textarea
                  id="changeRequests"
                  value={changeRequests}
                  onChange={(e) => setChangeRequests(e.target.value)}
                  rows={4}
                  placeholder="Hangi konularda değişiklik istiyorsunuz? (Fiyat, teslimat tarihi, paket sayısı, ödeme koşulları vb.)"
                  className="mt-1"
                />
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          {actionType && (
            <div className="flex items-center justify-between pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setActionType(null)}
                disabled={isLoading}
              >
                Geri Dön
              </Button>
              
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  İptal
                </Button>
                
                {actionType === 'approve' && (
                  <Button
                    onClick={handleApprove}
                    disabled={selectedPackages.length === 0 || !agreesToTerms || isLoading}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Onaylanıyor...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Teklifi Onayla
                      </>
                    )}
                  </Button>
                )}
                
                {actionType === 'reject' && (
                  <Button
                    onClick={handleReject}
                    disabled={!rejectionReason.trim() || isLoading}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Reddediliyor...
                      </>
                    ) : (
                      <>
                        <XCircle className="w-4 h-4 mr-2" />
                        Teklifi Reddet
                      </>
                    )}
                  </Button>
                )}
                
                {actionType === 'changes' && (
                  <Button
                    onClick={handleRequestChanges}
                    disabled={!changeRequests.trim() || isLoading}
                    className="bg-orange-600 hover:bg-orange-700"
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Gönderiliyor...
                      </>
                    ) : (
                      <>
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Değişiklik İste
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
