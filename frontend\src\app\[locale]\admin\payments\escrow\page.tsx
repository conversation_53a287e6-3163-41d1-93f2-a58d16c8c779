'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Search,
  Filter,
  Download,
  Eye,
  Unlock,
  RefreshCw,
  Wallet,
  Clock,
  CheckCircle,
  AlertTriangle,
  User,
  Calendar,
  DollarSign,
  Package
} from 'lucide-react'
import { EscrowManagementModal } from '@/components/admin/EscrowManagementModal'

// Mock data - gerçek API'den gelecek
const mockEscrowAccounts = [
  {
    id: 'esc_001',
    orderId: 'ORD_001',
    paymentId: 'pay_12345',
    customerName: 'Marble Export Ltd.',
    producerName: 'Afyon Mermer A.Ş.',
    amount: 15000,
    currency: 'USD',
    status: 'held',
    createdAt: '2025-01-02T10:30:00Z',
    expiresAt: '2025-02-01T10:30:00Z',
    orderInfo: {
      productName: 'Beyaz Mermer - Afyon',
      quantity: '150 m²',
      deliveryStatus: 'shipped',
      trackingNumber: 'TRK123456789'
    },
    timeline: [
      { date: '2025-01-02T10:30:00Z', event: 'Escrow oluşturuldu', status: 'created' },
      { date: '2025-01-02T11:00:00Z', event: 'Ödeme onaylandı', status: 'funded' },
      { date: '2025-01-05T14:20:00Z', event: 'Sipariş gönderildi', status: 'shipped' }
    ]
  },
  {
    id: 'esc_002',
    orderId: 'ORD_002',
    paymentId: 'pay_12346',
    customerName: 'Stone Trading Co.',
    producerName: 'Denizli Traverten Ltd.',
    amount: 8500,
    currency: 'USD',
    status: 'held',
    createdAt: '2025-01-02T09:15:00Z',
    expiresAt: '2025-02-01T09:15:00Z',
    orderInfo: {
      productName: 'Traverten - Denizli',
      quantity: '85 m²',
      deliveryStatus: 'processing',
      trackingNumber: null
    },
    timeline: [
      { date: '2025-01-02T09:15:00Z', event: 'Escrow oluşturuldu', status: 'created' },
      { date: '2025-01-02T09:45:00Z', event: 'Ödeme onaylandı', status: 'funded' }
    ]
  },
  {
    id: 'esc_003',
    orderId: 'ORD_003',
    paymentId: 'pay_12347',
    customerName: 'Natural Stone Inc.',
    producerName: 'Çanakkale Granit San.',
    amount: 22000,
    currency: 'USD',
    status: 'ready_to_release',
    createdAt: '2025-01-01T16:45:00Z',
    expiresAt: '2025-01-31T16:45:00Z',
    orderInfo: {
      productName: 'Granit - Çanakkale',
      quantity: '220 m²',
      deliveryStatus: 'delivered',
      trackingNumber: 'TRK987654321'
    },
    timeline: [
      { date: '2025-01-01T16:45:00Z', event: 'Escrow oluşturuldu', status: 'created' },
      { date: '2025-01-01T17:00:00Z', event: 'Ödeme onaylandı', status: 'funded' },
      { date: '2025-01-03T10:30:00Z', event: 'Sipariş gönderildi', status: 'shipped' },
      { date: '2025-01-07T15:20:00Z', event: 'Teslimat tamamlandı', status: 'delivered' },
      { date: '2025-01-07T16:00:00Z', event: 'Müşteri onayı alındı', status: 'confirmed' }
    ]
  }
]

const mockStats = {
  totalEscrow: 125000,
  activeAccounts: 28,
  readyToRelease: 5,
  expiringSoon: 3
}

export default function EscrowPage() {
  const [selectedEscrow, setSelectedEscrow] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [escrowAccounts, setEscrowAccounts] = useState(mockEscrowAccounts)
  const [isLoading, setIsLoading] = useState(false)

  const handleReleaseEscrow = async (escrowId: string, reason?: string) => {
    setIsLoading(true)
    try {
      // API çağrısı simülasyonu
      await new Promise(resolve => setTimeout(resolve, 1500))

      setEscrowAccounts(prev => prev.map(escrow =>
        escrow.id === escrowId
          ? {
              ...escrow,
              status: 'released',
              releasedAt: new Date().toISOString(),
              releasedBy: 'admin_001',
              releaseReason: reason,
              timeline: [
                ...escrow.timeline,
                { date: new Date().toISOString(), event: 'Escrow serbest bırakıldı', status: 'released' }
              ]
            }
          : escrow
      ))

      // Escrow released successfully
    } catch (error) {
      // Error handling - could be logged to monitoring service
    } finally {
      setIsLoading(false)
    }
  }

  const handleRefundEscrow = async (escrowId: string, reason: string) => {
    setIsLoading(true)
    try {
      // API çağrısı simülasyonu
      await new Promise(resolve => setTimeout(resolve, 1500))

      setEscrowAccounts(prev => prev.map(escrow =>
        escrow.id === escrowId
          ? {
              ...escrow,
              status: 'refunded',
              refundedAt: new Date().toISOString(),
              refundedBy: 'admin_001',
              refundReason: reason,
              timeline: [
                ...escrow.timeline,
                { date: new Date().toISOString(), event: 'Escrow iade edildi', status: 'refunded' }
              ]
            }
          : escrow
      ))

      // Escrow refunded successfully
    } catch (error) {
      // Error handling - could be logged to monitoring service
    } finally {
      setIsLoading(false)
    }
  }

  const getDaysUntilExpiry = (expiryDate: string) => {
    const now = new Date()
    const expiry = new Date(expiryDate)
    const diffTime = expiry.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const filteredEscrows = escrowAccounts.filter(escrow => {
    const matchesSearch = escrow.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         escrow.producerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         escrow.orderId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || escrow.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Dinamik stats hesaplama
  const stats = {
    totalEscrow: escrowAccounts
      .filter(e => ['held', 'ready_to_release'].includes(e.status))
      .reduce((sum, e) => sum + e.amount, 0),
    activeAccounts: escrowAccounts.filter(e => ['held', 'ready_to_release'].includes(e.status)).length,
    readyToRelease: escrowAccounts.filter(e => e.status === 'ready_to_release').length,
    expiringSoon: escrowAccounts.filter(e => {
      const daysUntilExpiry = getDaysUntilExpiry(e.expiresAt)
      return daysUntilExpiry <= 7 && daysUntilExpiry > 0 && ['held', 'ready_to_release'].includes(e.status)
    }).length
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'held':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Beklemede</Badge>
      case 'ready_to_release':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Serbest Bırakılabilir</Badge>
      case 'released':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Serbest Bırakıldı</Badge>
      case 'refunded':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800">İade Edildi</Badge>
      case 'expired':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Süresi Doldu</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getDeliveryStatusBadge = (status: string) => {
    switch (status) {
      case 'processing':
        return <Badge variant="outline" className="text-yellow-600">İşleniyor</Badge>
      case 'shipped':
        return <Badge variant="outline" className="text-blue-600">Gönderildi</Badge>
      case 'delivered':
        return <Badge variant="outline" className="text-green-600">Teslim Edildi</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Escrow Yönetimi</h1>
          <p className="text-gray-600 mt-1">
            Emanet hesaplarını yönetin ve ödemeleri serbest bırakın
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Rapor İndir
          </Button>
          <Button variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Yenile
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Wallet className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Toplam Escrow</p>
              <p className="text-2xl font-bold text-gray-900">${stats.totalEscrow.toLocaleString()}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Aktif Hesaplar</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeAccounts}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Unlock className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Serbest Bırakılabilir</p>
              <p className="text-2xl font-bold text-gray-900">{stats.readyToRelease}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Süresi Yaklaşan</p>
              <p className="text-2xl font-bold text-gray-900">{stats.expiringSoon}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Müşteri, üretici veya sipariş numarası ile ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tüm Durumlar</option>
              <option value="held">Beklemede</option>
              <option value="ready_to_release">Serbest Bırakılabilir</option>
              <option value="released">Serbest Bırakıldı</option>
              <option value="expired">Süresi Doldu</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Escrow Accounts Table */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sipariş Bilgileri
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Taraflar
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tutar
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Teslimat Durumu
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Escrow Durumu
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Son Tarih
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredEscrows.map((escrow) => {
                const daysUntilExpiry = getDaysUntilExpiry(escrow.expiresAt)
                return (
                  <tr key={escrow.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <Package className="h-5 w-5 text-blue-600" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {escrow.orderId}
                          </div>
                          <div className="text-sm text-gray-500">
                            {escrow.orderInfo.productName}
                          </div>
                          <div className="text-xs text-gray-400">
                            {escrow.orderInfo.quantity}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div className="font-medium">Müşteri:</div>
                        <div className="text-gray-600">{escrow.customerName}</div>
                        <div className="font-medium mt-1">Üretici:</div>
                        <div className="text-gray-600">{escrow.producerName}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        ${escrow.amount.toLocaleString()} {escrow.currency}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        {getDeliveryStatusBadge(escrow.orderInfo.deliveryStatus)}
                        {escrow.orderInfo.trackingNumber && (
                          <div className="text-xs text-gray-500">
                            {escrow.orderInfo.trackingNumber}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(escrow.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        <div>
                          <div>{formatDate(escrow.expiresAt)}</div>
                          <div className={`text-xs ${daysUntilExpiry <= 7 ? 'text-red-600' : 'text-gray-400'}`}>
                            {daysUntilExpiry > 0 ? `${daysUntilExpiry} gün kaldı` : 'Süresi doldu'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedEscrow(escrow)}
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          Detay
                        </Button>
                        {escrow.status === 'ready_to_release' && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-green-600 border-green-600 hover:bg-green-50"
                          >
                            <Unlock className="w-4 h-4 mr-1" />
                            Serbest Bırak
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Escrow Management Modal */}
      {selectedEscrow && (
        <EscrowManagementModal
          escrow={selectedEscrow}
          isOpen={!!selectedEscrow}
          onClose={() => setSelectedEscrow(null)}
          onRelease={async (escrowId, reason) => {
            await handleReleaseEscrow(escrowId, reason)
            setSelectedEscrow(null)
          }}
          onRefund={async (escrowId, reason) => {
            await handleRefundEscrow(escrowId, reason)
            setSelectedEscrow(null)
          }}
        />
      )}
    </div>
  )
}
