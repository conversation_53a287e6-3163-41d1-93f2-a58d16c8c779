'use client'

import * as React from 'react'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  BarChart3, 
  TrendingUp,
  TrendingDown,
  Calendar,
  DollarSign,
  Package,
  Users,
  CheckCircle,
  ArrowRight,
  Target,
  Award
} from 'lucide-react'

interface ComparisonAnalysisModalProps {
  isOpen: boolean
  onClose: () => void
  onGenerateComparison: (comparisonConfig: any) => Promise<boolean>
  availablePeriods: string[]
}

export function ComparisonAnalysisModal({
  isOpen,
  onClose,
  onGenerateComparison,
  availablePeriods
}: ComparisonAnalysisModalProps) {
  const [comparisonConfig, setComparisonConfig] = React.useState({
    primaryPeriod: '6months',
    comparisonPeriod: '6months_ago',
    metrics: ['revenue', 'orders', 'customers'],
    analysisType: 'detailed',
    includeGrowthRate: true,
    includeMarketShare: false,
    includeSeasonalAdjustment: true
  })
  const [isLoading, setIsLoading] = React.useState(false)
  const [step, setStep] = React.useState(1) // 1: Configuration, 2: Results

  const handleGenerateComparison = async () => {
    setIsLoading(true)
    try {
      const success = await onGenerateComparison(comparisonConfig)
      if (success) {
        setStep(2)
      }
    } catch (error) {
      console.error('Error generating comparison:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const generateMockComparison = () => {
    return {
      revenue: {
        current: 125000,
        previous: 108000,
        change: 15.7,
        trend: 'up'
      },
      orders: {
        current: 45,
        previous: 38,
        change: 18.4,
        trend: 'up'
      },
      customers: {
        current: 28,
        previous: 24,
        change: 16.7,
        trend: 'up'
      },
      avgOrderValue: {
        current: 2778,
        previous: 2842,
        change: -2.3,
        trend: 'down'
      }
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(value)
  }

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  const getTrendIcon = (trend: string) => {
    return trend === 'up' ? TrendingUp : TrendingDown
  }

  const getTrendColor = (trend: string) => {
    return trend === 'up' ? 'text-green-600' : 'text-red-600'
  }

  const getPeriodLabel = (period: string) => {
    const labels: { [key: string]: string } = {
      '1month': 'Son 1 Ay',
      '3months': 'Son 3 Ay',
      '6months': 'Son 6 Ay',
      '1year': 'Son 1 Yıl',
      '6months_ago': '6 Ay Önceki Dönem',
      '1year_ago': 'Geçen Yıl Aynı Dönem',
      'previous_quarter': 'Önceki Çeyrek'
    }
    return labels[period] || period
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Dönemsel Karşılaştırma Analizi
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step 1: Configuration */}
          {step === 1 && (
            <>
              {/* Period Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Karşılaştırma Dönemleri
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="primaryPeriod">Ana Dönem</Label>
                      <Select 
                        value={comparisonConfig.primaryPeriod} 
                        onValueChange={(value) => setComparisonConfig(prev => ({ ...prev, primaryPeriod: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1month">Son 1 Ay</SelectItem>
                          <SelectItem value="3months">Son 3 Ay</SelectItem>
                          <SelectItem value="6months">Son 6 Ay</SelectItem>
                          <SelectItem value="1year">Son 1 Yıl</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="comparisonPeriod">Karşılaştırma Dönemi</Label>
                      <Select 
                        value={comparisonConfig.comparisonPeriod} 
                        onValueChange={(value) => setComparisonConfig(prev => ({ ...prev, comparisonPeriod: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="6months_ago">6 Ay Önceki Dönem</SelectItem>
                          <SelectItem value="1year_ago">Geçen Yıl Aynı Dönem</SelectItem>
                          <SelectItem value="previous_quarter">Önceki Çeyrek</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div className="flex items-center justify-center gap-4">
                      <div className="text-center">
                        <div className="font-medium text-blue-800">
                          {getPeriodLabel(comparisonConfig.primaryPeriod)}
                        </div>
                        <div className="text-sm text-blue-600">Ana Dönem</div>
                      </div>
                      <ArrowRight className="w-5 h-5 text-blue-600" />
                      <div className="text-center">
                        <div className="font-medium text-blue-800">
                          {getPeriodLabel(comparisonConfig.comparisonPeriod)}
                        </div>
                        <div className="text-sm text-blue-600">Karşılaştırma</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Metrics Selection */}
              <Card>
                <CardHeader>
                  <CardTitle>Karşılaştırılacak Metrikler</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {[
                      { id: 'revenue', label: 'Gelir', icon: DollarSign },
                      { id: 'orders', label: 'Siparişler', icon: Package },
                      { id: 'customers', label: 'Müşteriler', icon: Users },
                      { id: 'growth', label: 'Büyüme', icon: TrendingUp }
                    ].map((metric) => (
                      <div key={metric.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={metric.id}
                          checked={comparisonConfig.metrics.includes(metric.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setComparisonConfig(prev => ({
                                ...prev,
                                metrics: [...prev.metrics, metric.id]
                              }))
                            } else {
                              setComparisonConfig(prev => ({
                                ...prev,
                                metrics: prev.metrics.filter(m => m !== metric.id)
                              }))
                            }
                          }}
                          className="text-blue-600"
                        />
                        <Label htmlFor={metric.id} className="flex items-center gap-2">
                          <metric.icon className="w-4 h-4" />
                          {metric.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Analysis Options */}
              <Card>
                <CardHeader>
                  <CardTitle>Analiz Seçenekleri</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="analysisType">Analiz Detayı</Label>
                    <Select 
                      value={comparisonConfig.analysisType} 
                      onValueChange={(value) => setComparisonConfig(prev => ({ ...prev, analysisType: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="summary">Özet Analiz</SelectItem>
                        <SelectItem value="detailed">Detaylı Analiz</SelectItem>
                        <SelectItem value="comprehensive">Kapsamlı Analiz</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="includeGrowthRate"
                        checked={comparisonConfig.includeGrowthRate}
                        onChange={(e) => setComparisonConfig(prev => ({ ...prev, includeGrowthRate: e.target.checked }))}
                        className="text-blue-600"
                      />
                      <Label htmlFor="includeGrowthRate">Büyüme oranı hesapla</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="includeMarketShare"
                        checked={comparisonConfig.includeMarketShare}
                        onChange={(e) => setComparisonConfig(prev => ({ ...prev, includeMarketShare: e.target.checked }))}
                        className="text-blue-600"
                      />
                      <Label htmlFor="includeMarketShare">Pazar payı analizi</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="includeSeasonalAdjustment"
                        checked={comparisonConfig.includeSeasonalAdjustment}
                        onChange={(e) => setComparisonConfig(prev => ({ ...prev, includeSeasonalAdjustment: e.target.checked }))}
                        className="text-blue-600"
                      />
                      <Label htmlFor="includeSeasonalAdjustment">Mevsimsel düzeltme uygula</Label>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={onClose}>
                  İptal
                </Button>
                <Button 
                  onClick={handleGenerateComparison}
                  disabled={isLoading || comparisonConfig.metrics.length === 0}
                  className="bg-orange-600 hover:bg-orange-700"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Analiz Ediliyor...
                    </>
                  ) : (
                    <>
                      <BarChart3 className="w-4 h-4 mr-2" />
                      Karşılaştırma Yap
                    </>
                  )}
                </Button>
              </div>
            </>
          )}

          {/* Step 2: Results */}
          {step === 2 && (
            <>
              <Card className="bg-orange-50 border-orange-200">
                <CardHeader>
                  <CardTitle className="text-orange-800 flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    Karşılaştırma Sonuçları
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {Object.entries(generateMockComparison()).map(([key, data]) => {
                      const TrendIcon = getTrendIcon(data.trend)
                      return (
                        <div key={key} className="bg-white p-4 rounded-lg border">
                          <div className="text-center">
                            <h4 className="font-medium text-gray-800 mb-2">
                              {key === 'revenue' ? 'Gelir' :
                               key === 'orders' ? 'Siparişler' :
                               key === 'customers' ? 'Müşteriler' : 'Ort. Sipariş'}
                            </h4>
                            <div className="space-y-2">
                              <div>
                                <p className="text-2xl font-bold">
                                  {key === 'revenue' || key === 'avgOrderValue' 
                                    ? formatCurrency(data.current)
                                    : data.current.toLocaleString()
                                  }
                                </p>
                                <p className="text-sm text-gray-500">Mevcut</p>
                              </div>
                              <div className="flex items-center justify-center gap-1">
                                <TrendIcon className={`w-4 h-4 ${getTrendColor(data.trend)}`} />
                                <span className={`font-medium ${getTrendColor(data.trend)}`}>
                                  {formatPercentage(data.change)}
                                </span>
                              </div>
                              <p className="text-xs text-gray-500">
                                Önceki: {key === 'revenue' || key === 'avgOrderValue' 
                                  ? formatCurrency(data.previous)
                                  : data.previous.toLocaleString()
                                }
                              </p>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Key Insights */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Önemli Bulgular
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-green-800">Güçlü Performans</h4>
                        <p className="text-green-700 text-sm">
                          Gelir ve sipariş sayısında %15+ artış gözlemlendi. Bu trend devam ederse yıl sonu hedefleri aşılabilir.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <TrendingUp className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-blue-800">Müşteri Büyümesi</h4>
                        <p className="text-blue-700 text-sm">
                          Yeni müşteri kazanımında %16.7 artış var. Müşteri sadakat programları etkili olmuş.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                      <TrendingDown className="w-5 h-5 text-yellow-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-yellow-800">Dikkat Edilmesi Gereken</h4>
                        <p className="text-yellow-700 text-sm">
                          Ortalama sipariş değeri %2.3 azalmış. Upselling stratejileri gözden geçirilmeli.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Recommendations */}
              <Card>
                <CardHeader>
                  <CardTitle>Öneriler</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full mt-2"></span>
                      <span>Mevcut büyüme trendini sürdürmek için pazarlama bütçesini %20 artırın</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <span>Yeni müşteri segmentlerine odaklanarak büyümeyi hızlandırın</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-orange-500 rounded-full mt-2"></span>
                      <span>Ortalama sipariş değerini artırmak için paket teklifleri geliştirin</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-purple-500 rounded-full mt-2"></span>
                      <span>Mevsimsel trendleri değerlendirerek stok planlaması yapın</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              <div className="text-center">
                <Button onClick={onClose} className="bg-orange-600 hover:bg-orange-700">
                  Analizi Tamamla
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
